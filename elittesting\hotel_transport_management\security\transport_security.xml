<?xml version="1.0" encoding="utf-8"?>
<openerp>
<data noupdate="1">
	<record id="group_transport_manager" model="res.groups">
        <field name="name">Hotel Management/ Transport Manager</field>
    </record>
    <record id="group_transport_user" model="res.groups">
        <field name="name">Hotel Management / Transport User</field>
    </record>
	
	 <!-- Multi - Company Rules -->
	
	<!--<record model="ir.rule" id="hotel_room_comp_rule">
        <field name="name">Sale Order multi-company</field>
        <field name="model_id" ref="model_hotel_room"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="hotel_reservation_comp_rule">
        <field name="name">Sale Order Line multi-company</field>
        <field name="model_id" ref="model_hotel_reservation"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('shop_id.company_id.id','=',False),('shop_id.company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="hotel_reservation_line_comp_rule">
        <field name="name">Sale Shop multi-company</field>
        <field name="model_id" ref="model_hotel_reservation"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>-->
	
</data>
</openerp>
