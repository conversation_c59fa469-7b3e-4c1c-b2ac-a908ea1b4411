# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign_itsme
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2022
# <AUTHOR> <EMAIL>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 10:53+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid ""
"<small>Name: The signatory has provided this identity through itsme®</small>"
msgstr ""
"<small>Имя: Лицо, подписавшее документ, предоставило его через "
"itsme®</small>"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid "Confirm your identity"
msgstr ""

#. module: sign_itsme
#: model:sign.item.role,name:sign_itsme.sign_item_role_itsme_customer
msgid "Customer (identified with itsme®)"
msgstr "Клиент (идентифицированный с помощью itsme®)"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "Дополнительный шаг аутентификации"

#. module: sign_itsme
#: model:ir.model.fields,help:sign_itsme.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""
"Заставить подписавшего идентифицироваться с помощью второго метода "
"аутентификации"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Go Back"
msgstr "Назад"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid "Identification refused"
msgstr "Отказ в идентификации"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Identify with itsme"
msgstr "Идентифицировать себя с ней"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid "Name"
msgstr "Имя"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Please confirm your identity to finalize your signature."
msgstr "Пожалуйста, подтвердите свою личность, чтобы завершить подпись."

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/models/sign_request_item.py:0
#, python-format
msgid "Sign request item is not validated yet."
msgstr "Элемент запроса знака еще не подтвержден."

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_item_role
msgid "Signature Item Party"
msgstr "Группа элемента подписи"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request_item
msgid "Signature Request Item"
msgstr "Элемент запроса на подпись"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid ""
"The itsme® identification data could not be forwarded to Odoo, the signature"
" could not be saved."
msgstr ""
"Идентификационные данные itsme® не удалось передать в Odoo, подпись не "
"удалось сохранить."

#. module: sign_itsme
#: model:ir.model.fields.selection,name:sign_itsme.selection__sign_item_role__auth_method__itsme
msgid "Via itsme®"
msgstr "Via itsme®"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid ""
"You have rejected the identification request or took too long to process it."
" You can try again to finalize your signature."
msgstr ""
"Вы отклонили запрос на идентификацию или его обработка заняла слишком много "
"времени. Вы можете повторить попытку, чтобы завершить подпись."

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/controllers/main.py:0
#, python-format
msgid "itsme® IAP service could not be found."
msgstr "не удалось найти сервис itsme® IAP."

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_birthdate
msgid "itsme® Signer's Birthdate"
msgstr "itsme® Дата рождения подписанта"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_name
msgid "itsme® Signer's Name"
msgstr "itsme® Имя подписанта"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_validation_hash
msgid "itsme® Validation Token"
msgstr "валидационный токен itsme®"
