# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * industry_fsm
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2019-03-20 14:36+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid ""
"<i class=\"fa fa-lg fa-pencil-square-o mr-2\" aria-label=\"View quotations\""
" title=\"View quotations\" role=\"img\"/>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<i class=\"fa fa-lg fa-pencil-square-o mr-2\" aria-label=\"View quotations\""
" title=\"View quotations\"/>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid ""
"<i class=\"fa fa-lg fa-plus-circle mr-2\" aria-label=\"Create new quotation\" title=\"Create new quotation\"/>\n"
"                                New Quotation"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<i class=\"fa fa-lg fa-plus-circle mr-2\" aria-label=\"Create new quotation\" title=\"Create new quotation\"/>\n"
"                        New Quotation"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:123
#, python-format
msgid "<p class='o_view_nocontent_smiling_face'>No more Tasks</p>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid ""
"<span attrs=\"{'invisible': [('planned_date_end', '=', False)]}\"> to "
"</span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Add materials"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_billable
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__allow_billable
msgid "Allow to bill Tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__product_template_ids
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__product_template_ids
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_create_sale_order__product_template_ids
msgid "Allowed Products"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_create_sale_order
msgid "Create SO from task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__currency_id
msgid "Currency"
msgstr "Νόμισμα"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid "Date"
msgstr "Ημερομηνία"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_email
msgid "Email "
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__planned_date_end
msgid "End date"
msgstr "Ημερ. λήξης"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.menu_fsm_root
msgid "Field Service"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_product_task_map_user
msgid "Fsm User"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search
msgid "Future"
msgstr "Μελλοντικά"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__id
msgid "ID"
msgstr "Κωδικός"

#. module: industry_fsm
#: model:project.task,legend_normal:industry_fsm.planning_task_0
#: model:project.task,legend_normal:industry_fsm.planning_task_1
#: model:project.task,legend_normal:industry_fsm.planning_task_2
#: model:project.task,legend_normal:industry_fsm.planning_task_3
#: model:project.task,legend_normal:industry_fsm.planning_task_4
#: model:project.task,legend_normal:industry_fsm.planning_task_5
#: model:project.task,legend_normal:industry_fsm.planning_task_6
#: model:project.task,legend_normal:industry_fsm.planning_task_7
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
msgid "In Progress"
msgstr "Σε εξέλιξη"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__material_line_ids
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_create_sale_order__material_line_ids
msgid "Material Line"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_mobile
msgid "Mobile"
msgstr "Κινητό"

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:110
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search
#, python-format
msgid "My Tasks"
msgstr "Εργασίες μου"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_0
msgid "New"
msgstr "Νέα"

#. module: industry_fsm
#: model:project.task,legend_blocked:industry_fsm.planning_task_0
#: model:project.task,legend_blocked:industry_fsm.planning_task_1
#: model:project.task,legend_blocked:industry_fsm.planning_task_2
#: model:project.task,legend_blocked:industry_fsm.planning_task_3
#: model:project.task,legend_blocked:industry_fsm.planning_task_4
#: model:project.task,legend_blocked:industry_fsm.planning_task_5
#: model:project.task,legend_blocked:industry_fsm.planning_task_6
#: model:project.task,legend_blocked:industry_fsm.planning_task_7
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
msgid "Not validated"
msgstr "Μη Επικυρωμένο"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_phone
msgid "Phone"
msgstr "Τηλέφωνο"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search
msgid "Planned for Today"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__planning_overlap
msgid "Planning Overlap"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_product_product_kanban_material
msgid "Price:"
msgstr "Τιμή:"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_product_product
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__product_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_product_product_kanban_material
msgid "Product"
msgstr "Είδος"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_product_task_map
msgid "Product Task Map"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__timesheet_product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:169
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Products"
msgstr "Είδη"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__product_template_ids
#: model:ir.model.fields,help:industry_fsm.field_project_task__product_template_ids
#: model:ir.model.fields,help:industry_fsm.field_project_task_create_sale_order__product_template_ids
msgid "Products allowed to be added on this Task's Material"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
msgid "Project"
msgstr "Έργο"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__quantity
msgid "Quantity"
msgstr "Ποσότητα"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__quotation_count
msgid "Quotation Count"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Quotation(s)"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Quotations created from this task"
msgstr ""

#. module: industry_fsm
#: model:project.task,legend_done:industry_fsm.planning_task_0
#: model:project.task,legend_done:industry_fsm.planning_task_1
#: model:project.task,legend_done:industry_fsm.planning_task_2
#: model:project.task,legend_done:industry_fsm.planning_task_3
#: model:project.task,legend_done:industry_fsm.planning_task_4
#: model:project.task,legend_done:industry_fsm.planning_task_5
#: model:project.task,legend_done:industry_fsm.planning_task_6
#: model:project.task,legend_done:industry_fsm.planning_task_7
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
msgid "Ready for Next Stage"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_sale_order
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search
msgid "Search planning"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid "Start"
msgstr "Έναρξη"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__planned_date_begin
msgid "Start date"
msgstr "Ημερομηνία έναρξης"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid "Stop"
msgstr "Διακοπή"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_product_task_map__task_id
#: model:ir.model.fields,field_description:industry_fsm.field_sale_order__task_id
msgid "Task"
msgstr "Εργασία"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_is_done
msgid "Task Done"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_kanban
msgid "Task name"
msgstr ""

#. module: industry_fsm
#: sql_constraint:product.task.map:0
msgid "The task and product must be unique on materials ordered!"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:89
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
#: model:product.product,name:industry_fsm.fsm_time_product
#: model:product.template,name:industry_fsm.fsm_time_product_product_template
#, python-format
msgid "Time"
msgstr "Χρόνος"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "Χρόνος που Αναλώθηκε"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm
msgid "Time recorded on this task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "Φύλλο Χρόνου Εργασίας"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__timesheet_product_id
msgid "Timesheet Product"
msgstr ""

#. module: industry_fsm
#: model:product.product,uom_name:industry_fsm.fsm_time_product
#: model:product.template,uom_name:industry_fsm.fsm_time_product_product_template
msgid "Unit(s)"
msgstr "Μονάδες"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form_sale
msgid "Use Intervention Reports"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:165
#, python-format
msgid "You haven't started this task yet!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "other task(s) for this employee at the same time."
msgstr ""

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_action_worker_tasks
msgid "project.task.action.worker.tasks"
msgstr ""
