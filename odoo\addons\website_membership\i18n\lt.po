# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Anatolij, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Anatolij, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Visi"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Visos šalys"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Asociacijos"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "Uždaryti"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Raskite verslo partnerį"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "Free Members"
msgstr "Nemokami nariai"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Location"
msgstr "Vieta"

#. module: website_membership
#: code:addons/website_membership/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_membership.index
#, python-format
msgid "Members"
msgstr "Nariai"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Members Page"
msgstr ""

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Narystės eilutė"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "Nerasta rezultatų."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Mūsų narių aplankas"

#. module: website_membership
#: model:ir.model,name:website_membership.model_website
msgid "Website"
msgstr "Svetainė"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "World Map"
msgstr "Pasaulio žemėlapis"
