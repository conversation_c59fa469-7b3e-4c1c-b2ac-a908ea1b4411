# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_planning
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        as\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        como\n"
"                    </span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Planned</span>"
msgstr "<span class=\"o_stat_text\">Planeado</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">To plan</span>"
msgstr "<span class=\"o_stat_text\">Por planear</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span>Hours</span>"
msgstr "<span>Horas</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
msgid "<strong>Sales Order Item  — </strong>"
msgstr "<strong>Artículo de la orden de venta — </strong>"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__allocated_hours
msgid "Allocated Time"
msgstr "Tiempo asignado"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Billable"
msgstr "Facturable"

#. module: sale_planning
#: model:ir.ui.menu,name:sale_planning.sale_planning_menu_schedule_by_sale_order
msgid "By Sales Order"
msgstr "Por orden de venta"

#. module: sale_planning
#: model:product.template,name:sale_planning.developer_product_product_template
msgid "Developer (Plan services)"
msgstr "Desarrollador (plan de servicios)"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__end_datetime
msgid "End Date"
msgstr "Fecha de finalización"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_tester
msgid "Functional Tester"
msgstr "Probador funcional"

#. module: sale_planning
#: model:product.template,name:sale_planning.technical_maintainance_product_product_template
msgid "IT Technical Maintenance (Plan services)"
msgstr "Mantenimiento técnico de TI (planear servicios)"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_technician
msgid "IT Technician"
msgstr "Técnico de TI"

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,help:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_planning.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order. Only employees with this role will "
"automatically be assigned shifts for Sales Orders containing this service."
msgstr ""
"Si está activado, se generará un turno de forma automática para la función "
"seleccionada al confirmar la orden de venta. Solo los empleados con esta "
"función recibirán un turno en automático para las órdenes de venta que "
"incluyan este servicio."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "My Sales Orders"
msgstr "Mis órdenes de venta"

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "No shifts found. Let's create one!"
msgstr "No se encontraron turnos. ¡Creemos uno!"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Non Billable"
msgstr "No facturable"

#. module: sale_planning
#: model:ir.model.constraint,message:sale_planning.constraint_planning_slot_check_datetimes_set_or_plannable_slot
msgid ""
"Only slots linked to a sale order with a plannable service can be "
"unscheduled."
msgstr ""
"Solo se puede anular la programación de los espacios vinculados a una orden "
"de venta con servicios planeables. "

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.planning_action_orders_planned
msgid "Orders Planned"
msgstr "Órdenes planeadas"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.developer_product_product_template
msgid "Our developer will help you to add new features to your Software."
msgstr ""
"Nuestro desarrollador le ayudará a agregar nuevas funciones a su software."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Plan Orders"
msgstr "Planear órdenes"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_enabled
msgid "Plan Services"
msgstr "Planear servicios"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid ""
"Plannable services should be a service product, product\n"
"%s."
msgstr ""
"Los servicios planeables deben ser productos o productos de servicio\n"
"%s."

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid "Plannable services should use an UoM within the %s category."
msgstr ""
"Los servicios planeables deben utilizar una UdM dentro de la categoría %s."

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Reporte de análisis de planeación"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_first_sale_line_id
msgid "Planning First Sale Line"
msgstr "Primera línea de venta de planeación"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_planned
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_planned
msgid "Planning Hours Planned"
msgstr "Horas de planeación planeadas"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_to_plan
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_to_plan
msgid "Planning Hours To Plan"
msgstr "Horas de planeación por planear"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_initial_date
msgid "Planning Initial Date"
msgstr "Fecha inicial de planeación"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_role
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_role_id
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_role_id
msgid "Planning Role"
msgstr "Función de planeación"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_slot
msgid "Planning Shift"
msgstr "Planear turno"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_slot_ids
msgid "Planning Slot"
msgstr "Espacio de planeación"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_product_template
msgid "Product"
msgstr "Producto"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__role_product_ids
msgid "Role Product"
msgstr "Función del producto"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_order_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Sales Order"
msgstr "Orden de venta"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_line_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_planning.period_report_template
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.resource_sale_planning
msgid "Sales Order Item"
msgstr "Artículo en la orden de venta"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de la orden de venta"

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""
"Artículo de orden de venta para el que se realizará este turno. Cuando las "
"órdenes de venta se planean de forma automática, se consideran las horas "
"restantes del artículo de la orden de venta, así como la función que se "
"define en el servicio."

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "Schedule by Sales Order"
msgstr "Programar por orden de venta"

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Programe sus recursos humanos y materiales entre funciones, proyectos y "
"órdenes de venta."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Service"
msgstr "Servicio"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_role__product_ids
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__role_product_ids
msgid "Services"
msgstr "Servicios"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/controllers/main.py:0
#, python-format
msgid "Shift"
msgstr "Turno"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__start_datetime
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.technical_maintainance_product_product_template
msgid "Take a rest. We'll do our best."
msgstr "Descanse. Haremos nuestro mejor esfuerzo."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "The sales orders have successfully been assigned."
msgstr "Las órdenes de venta se asignaron con éxito."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "There are no sales orders to assign or no employees are available."
msgstr "No hay órdenes de venta para asignar o empleados disponibles."

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
#, python-format
msgid ""
"This Sale Order Item doesn't have a target value of planned hours. Planned "
"hours :"
msgstr ""
"Este artículo de orden de venta no tiene un valor objetivo de horas "
"planeadas. Horas planeadas:"

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/planning_gantt_controller.js:0
#, python-format
msgid ""
"This resource is not available for this shift during the selected period."
msgstr ""
"Este recurso no está disponible para este turno durante el periodo "
"seleccionado."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_view_form_in_gantt_inherit_sale_planning
msgid "Unschedule"
msgstr "Anular programación"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
#, python-format
msgid "View Planning"
msgstr "Ver planeación"

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "View Shifts"
msgstr "Ver turnos"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_planner
msgid "Work Planner"
msgstr "Planeador de trabajo"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "e.g. Consultant"
msgstr "Por ejemplo, consultor"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order_line.py:0
#, python-format
msgid "remaining"
msgstr "restante"
