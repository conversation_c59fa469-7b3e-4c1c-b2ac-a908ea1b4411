# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# Андрей <PERSON>ев <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <viola<PERSON><PERSON><EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>erina <<EMAIL>>, 2022
# Collex100, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# Evgen<PERSON>a <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# alenafairy, 2023
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Сергей Шебанин <<EMAIL>>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Счета"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "%s (max %s chars)"
msgstr "%s (не более %s символов)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ", you must first link a social media."
msgstr "сначала укажите ссылку на социальную сеть."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Произошла ошибка при попытке привязать аккаунт</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe\" title=\"See the post\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<small class=\"pe-1\">Clicks:</small>"
msgstr "<small class=\"pe-1\">Клики:</small>"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Поздравляем! Зайдите через несколько минут, чтобы проверить свою "
"статистику.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Активно"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Статус действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Иконка типа действия"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add"
msgstr "Добавить"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Добавить пост"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add a Stream"
msgstr "Добавить источник"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Добавить источник"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add an image"
msgstr "добавьте изображение"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "All Companies"
msgstr "Все компании"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Все относящиеся посты в соц сетях"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Разрешенные аккаунты"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Архивировано"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Аккаунты доступны?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Прикрепить изображения"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
#, python-format
msgid "Audience"
msgstr "Публика"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Тренд публики"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Ссылка на автора"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Имя автора"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid ""
"Author link to the external social.media (ex: link to the Twitter Account)."
msgstr ""
"Авторская ссылка на внешнюю социальную сеть (например, ссылка на аккаунт в "
"Twitter)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr "Перед размещением ссылки будут преобразованы для отслеживания."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "По источнику"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF Токен"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Календарная дата"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Кампания"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Кампании"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Кампании используются для централизации маркетинговых усилий и отслеживания "
"их результатов."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts ?"
msgstr "Может связывать аккаунты?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Cancel"
msgstr "Отмена"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Выберите, какой <b>аккаунт</b> вы хотите связать первым."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Click to refresh."
msgstr "Нажать, чтобы обновить."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Переходы"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Comment Image"
msgstr "Комментировать изображение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Компания"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Connecting Problem"
msgstr "Проблема с подключением"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' Twitter account"
msgstr ""
"Содержит ручку социальной сети человека, создавшего эту учетную запись. "
"Например: '@odoo.official' для аккаунта Odoo в Twitter"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Содержание сообщения социального поста, которое подвергается постобработке "
"(ссылки сокращаются, UTM, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr ""
"Контролирует, будут ли обрабатываться социальные потоки в этой социальной "
"сети."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Контролирует, можем ли мы связать аккаунты или нет."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Создать Кампанию"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Создать пост"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
#, python-format
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Создайте другие учетные записи для %(media_names)s для этой компании или "
"попросите %(company_names)s поделиться своими учетными записями"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Создал"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Определяет, есть ли у этого аккаунта статистика Аудиторий/Сообщений/Историй.\n"
"        Аккаунты со статистикой отображаются на приборной панели."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Определяет, есть ли у этого аккаунта статистика тенденций или нет."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Delete Comment"
msgstr "Удалить комментарий"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Демо-режим"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Описание"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Учетные записи разработчиков"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "Do you really want to delete %s"
msgstr "Вы действительно хотите удалить %s"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Черновик"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Из-за ограничений по длине следующие сообщения не могут быть опубликованы:\n"
" %s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Edit Comment"
msgstr "Редактировать комментарий"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Включить демо-режим"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module.<br/>\n"
"                                        This must never be used on a production database!"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Engagement"
msgstr "Обязательство"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Тренд вовлеченности"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Каждый раз, когда создается учетная запись, создается и utm.medium, который "
"привязывается к ней"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Неудачно"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Причина сбоя"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Лента"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Feed Posts"
msgstr "Посты из ленты"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Количество постов в ленте"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Иконка со шрифтом Font awesome, например. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Отформатированная опубликованная дата"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Общая аудитория социального аккаунта (лайки страницы, фолловеры аккаунта, "
"...)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "Go back to Odoo"
msgstr "Назад к Odoo"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Перейти к"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Группировка"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Ручка / Краткое название"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Happy with the result? Let's post it!"
msgstr "Вам нравится результат? Опубликуем его!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Имеет статистику по счетам"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "Есть тренды?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "Идентификатор"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий действие исключения."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Изображение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "URL изображения"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Image Url"
msgstr "Ссылка на изображение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "Ссылки на изображения"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Изображения, которыми поделились с этим постом."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Insights"
msgstr "Инсайты"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Похоже, возникла проблема со ссылкой на социальную сеть, нажмите здесь, "
"чтобы снова связать аккаунт"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "It will appear in the Feed once it has posts to display."
msgstr "Он появится в ленте, как только в ней появятся сообщения."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account____last_update
#: model:ir.model.fields,field_description:social.field_social_live_post____last_update
#: model:ir.model.fields,field_description:social.field_social_media____last_update
#: model:ir.model.fields,field_description:social.field_social_post____last_update
#: model:ir.model.fields,field_description:social.field_social_post_template____last_update
#: model:ir.model.fields,field_description:social.field_social_stream____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post_image____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_type____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's <b>connect</b> to Facebook, LinkedIn or Twitter."
msgstr "Давайте <b>подключимся</b> к Facebook, LinkedIn или Twitter."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's create your own <b>social media</b> dashboard."
msgstr ""
"Давайте создадим вашу собственную панель управления <b>социальными "
"сетями</b>."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's start posting."
msgstr "Давайте начнем публиковать."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Like"
msgstr "Лайк"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Лайки"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Описание ссылки"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Link Image"
msgstr "Изображение ссылки"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL Изображения ссылки"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Название ссылки"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "URL ссылки"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Link a new account"
msgstr "Привязать новую учетную запись"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Счет по ссылке"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Привязать учетную запись"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Привяжите учетную запись к компании, чтобы ограничить ее использование, или "
"оставьте ее пустой, чтобы ею могли пользоваться все компании."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Ссылка на прямой пост в целевом СМИ."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Свяжите социальные аккаунты"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Ссылка на внешнюю статистику социального аккаунта"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Связь с внешними социальными сетями нарушена"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Список счетов, которые могут быть выбраны для этой должности."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Прямые сообщения в социальных сетях"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Load more comments..."
msgstr "Загрузить другие комментарии..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_main_attachment_id
#: model:ir.model.fields,field_description:social.field_social_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Main actions"
msgstr "Основные действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Максимальная длина столба"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Медиа"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Тип носителя"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Сообщение"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__message_length
#: model:ir.model.fields,field_description:social.field_social_post_template__message_length
msgid "Message Length"
msgstr "Длина сообщения"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Message posted"
msgstr "Сообщение опубликовано"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Message posted partially. These are the ones that couldn't be posted: <br>%s"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"Большинство постов social.live.posts напрямую переходят из состояния Ready в Posted/Failed, поскольку они являются результатом одного вызова стороннего API.\n"
"        Состояние \"Публикация\" также доступно для тех, которые отправляются пакетным способом (например, push-уведомления)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моего действия"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Мои посты"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Мои источники"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Имя"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New Post"
msgstr "Новый пост"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New content available"
msgstr "Доступен новый контент"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Next"
msgstr "Далее"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Встреча в календаре для следующего действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Крайний срок следующего действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Краткое описание следующего действия"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующего действия"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Социального аккаунта пока нет!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Социальных потоков пока нет!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Поток еще не добавлен!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "No comments yet."
msgstr "Пока ни одного комментария"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "No social accounts configured, please contact your administrator."
msgstr "Социальные аккаунты не настроены, обратитесь к администратору."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Number of Followers of your channel"
msgstr "Количество подписчиков канала"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Кол-во переходов"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Количество взаимодействий (лайков, акций, комментариев...) с социальными "
"постами"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих действия"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "Количество вовлеченных в пост людей (лайки, комментарии...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Количество людей, вовлеченных в ваши посты (лайки, комментарии, ...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Re-tweets, ...)."
msgstr ""
"Количество историй, созданных на основе ваших сообщений (Shares, Re-tweets, "
"...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr "Количество просмотров ваших постов (лайки, комментарии, акции...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Retweets...)"
msgstr ""
"Количество случаев, когда люди, взаимодействовавшие с вашим каналом, "
"создавали истории в ленте своих друзей или последователей (Shares, "
"Retweets...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Or add"
msgstr "Или добавьте"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr "Процент увеличения/уменьшения аудитории за определенный период."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Процентное соотношение увеличения/уменьшения вовлеченности за определенный "
"период."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr "Процент увеличения/уменьшения историй за определенный период."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Укажите хотя бы одну учетную запись, в которую нужно отправить сообщение "
"(для ID сообщения %s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Post"
msgstr "Провести"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Post Image"
msgstr "Опубликовать изображение"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Post Images"
msgstr "Опубликовать изображения"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Опубликовать ссылку"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Почтовое сообщение"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Опубликовать сейчас"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Ссылка на внешнюю социальную сеть (например, ссылка на фактический пост в "
"Facebook)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Сообщение на"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Проведено"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "В процессе"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Посты"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Сообщения по учетной записи"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Posts By Accounts"
msgstr "Posts By Accounts"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Нажмите Enter, чтобы отправить сообщение. Нажмите Shift+Enter, чтобы "
"вставить разрыв строки."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Предварительный просмотр вашего сообщения"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Previous"
msgstr "Назад"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr ""
"Опубликуйте сообщение сразу или запланируйте его на более позднее время."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Опубликовано"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Дата публикации"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Дата публикации"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Можно производить"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Связанные социальные сети"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, Twitter, ...)."
msgstr "Соответствующие социальные сети (Facebook, Twitter, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Связанный социальный аккаунт"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Reply"
msgstr "Ответить"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Повторить"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Расписание"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Запланировать позже"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Запланировано"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Плановая дата"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Selected accounts (%s) do not match the selected company (%s)"
msgstr "Выбранные счета (%s) не соответствуют выбранной компании (%s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Отправить сейчас"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Последовательность, используемая для упорядочивания потоков (в основном для "
"представления канбан \"Подача\")"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Установите максимальное количество символов, которое может быть размещено в "
"сообщении. 0 - без ограничений."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Настройки"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Учетная запись соц. сети"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Социальные аккаунты"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Пост в социальных сетях"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Социальный менеджер"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
msgid "Social Marketing"
msgstr "Маркетинг в соцсетях"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Social Media"
msgstr "Социальные сети"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Сообщения в социальных сетях"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Пост в социальной сети"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Шаблон социальных постов"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Шаблоны социальных постов"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Социальные посты"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Источник"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Пост источника соц. сети"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Прикрепление изображения к сообщению в социальном потоке"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Социальные потоки"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Социальный пользователь"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
#: model:ir.cron,cron_name:social.ir_cron_post_scheduled
msgid "Social: Publish Scheduled Posts"
msgstr "Социальные: Публикуйте сообщения по расписанию"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
#, python-format
msgid "Something went wrong while posting the comment."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
#, python-format
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Извините, вам не разрешено перепривязывать эту учетную запись, обратитесь к "
"администратору."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Источник"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Специальное техническое поле, которое содержит дикту, содержащую имена живых"
" постов по идентификаторам медиа (используется для просмотра канбан)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Ссылка на статистику"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Статус"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус основан на плановых действиях\n"
"Просрочено: срок исполнения истек\n"
"Сегодня: выполнить сегодня\n"
"Запланировано: срок в будущем."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
#, python-format
msgid "Stories"
msgstr "Истории"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Истории Тренд"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "Stream Added (%s)"
msgstr "Поток добавлен (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Stream Post"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Изображения Stream Post"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "URL-адреса изображений потоковых сообщений"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Типы ручьев"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Название типа потока (техническое)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Потоки включены"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr ""
"Подпосты, которые будут опубликованы на каждом из выбранных социальных "
"аккаунтов."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Synchronize"
msgstr "Синхронизировать"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "Поле \"сообщение\" обязательно для идентификатора поста %s"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Счета, на которых будет опубликована эта заметка."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"Имя автора поста, основанное на информации от третьих лиц (например, 'John "
"Doe')."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Пост считается \"Опубликованным\", если все его подпосты (по одному на "
"каждый социальный аккаунт) либо \"Не удалось\", либо \"Опубликован\""

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "Опубликованная дата основана на информации третьих лиц."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"Причина, по которой пост не был успешно размещен в социальной сети "
"(например, ошибка подключения, дублирование поста, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "Соответствующее изображение в социальных сетях"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Социальные сети, связанные с выбранными социальными аккаунтами."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "В подпосте есть ошибки при отправке"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Этот токен можно использовать для проверки того, что входящий запрос от "
"социального провайдера не был подделан."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Заголовок"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "To add a stream"
msgstr "Чтобы добавить поток"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Раздел"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип активности исключения в записи."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
msgid "UTM Campaign"
msgstr "UTM Кампания"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM тип трафика"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "источник UTM"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Unknown error"
msgstr "Неизвестная ошибка"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "Uploaded file does not seem to be a valid image."
msgstr "Загруженный файл, похоже, не является действительным изображением."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app.\n"
"                            Those credentials are provided in the developer section of your professional social media account."
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Используется для сравнения, когда нам нужно ограничить некоторые функции для"
" определенных медиа носителей ('facebook', 'twitter', ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "View"
msgstr "Осмотр"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Когда"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Когда был опубликован глобальный пост. Фактические даты публикации подпостов"
" могут отличаться в зависимости от СМИ."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Прикрепляйте изображения к своим постам (если социальная сеть это "
"поддерживает)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a comment..."
msgstr "Напишите комментарий..."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Write a message to get a preview of your post."
msgstr ""
"Напишите сообщение, чтобы получить предварительный просмотр вашего "
"сообщения."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a reply..."
msgstr "Оставить отзыв…"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Напишите заманчивый пост, добавьте изображения и запланируйте его "
"последующую публикацию сразу на нескольких платформах."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "You can only move posts that are scheduled."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Вы не можете удалить эти UTM Mediums, поскольку они связаны со следующими социальными аккаунтами в Social:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Вы не можете удалить эти UTM-источники, поскольку они связаны с социальными постами в Social:\n"
"%(utm_sources)s"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Ваш пост"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "a Stream from an existing account"
msgstr "поток с существующей учетной записи"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "перед публикацией."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "comment/reply"
msgstr "комментарий/ответ "

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "панель управления"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "for"
msgstr "для"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "replies..."
msgstr "ответы..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr "следить за своими постами и отслеживать всю социальную активность."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "чтобы связать свои аккаунты и начать публиковать сообщения."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "чтобы начать отправлять сообщения из Odoo."
