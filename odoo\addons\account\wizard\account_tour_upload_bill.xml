<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_tour_upload_bill" model="ir.ui.view">
            <field name="name">account.tour.upload.bill</field>
            <field name="model">account.tour.upload.bill</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <h2>With Odoo, you won't have to record bills manually</h2>
                        <p>We process bills automatically so that you only have to validate them. Choose how you want to test our artificial intelligence engine:</p>
                        <group col="6">
                            <group colspan="2">
                                <field name="selection" widget="radio" nolabel="1" colspan="2"/>
                            </group>
                            <group colspan="4" attrs="{'invisible': [('selection', '!=', 'sample')]}">
                                <field name="preview_invoice" widget="html" nolabel="1" colspan="2"/>
                            </group>
                            <group colspan="4" attrs="{'invisible': [('selection', '!=', 'upload')]}">
                                <field name="attachment_ids" widget="many2many_binary" string="Attach a file" nolabel="1" colspan="2"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button string="Continue" type="object" name="apply" class="btn-primary" data-hotkey="q"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
               </form>
            </field>
        </record>

        <record id="account_tour_upload_bill_email_confirm" model="ir.ui.view">
            <field name="name">account.tour.upload.bill.email.confirm</field>
            <field name="model">account.tour.upload.bill.email.confirm</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <p>Send your email to <field name="email_alias" class="oe_inline"/> with a pdf of an invoice as attachment.</p>
                        <p>Once done, press continue.</p>
                    </sheet>
                    <footer>
                        <button string="Continue" type="object" name="apply" class="btn-primary" data-hotkey="q"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
               </form>
            </field>
        </record>
    </data>
</odoo>
