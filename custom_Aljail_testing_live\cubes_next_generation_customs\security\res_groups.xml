<?xml version="1.0" encoding="utf-8"?>
<odoo>

     <record id="group_detailed_discount_sales" model="res.groups">
        <field name="name">Detailed Discount Sales</field>
    </record>
    <record id="sale_margin_group" model="res.groups">
        <field name="name">Sale Margin</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="product_cost_group" model="res.groups">
        <field name="name">product Cost</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

</odoo>
