<odoo>
    <data>
        <!-- Inherit Form View to Modify it -->
<!--        <record id="product_category_form_inherit" model="ir.ui.view">-->
<!--            <field name="name">product.category.form</field>-->
<!--            <field name="model">product.category</field>-->
<!--            <field name="inherit_id" ref="product.product_category_form_view"/>-->
<!--            <field name="arch" type="xml">-->

<!--                <xpath expr="//field[@name='parent_id']" position="after">-->
<!--                    <field name="short_code"/>-->
<!--                </xpath>-->

<!--            </field>-->
<!--        </record>-->

        <record id="product_product_tree_inherit" model="ir.ui.view">
            <field name="name">product.product.tree.inherit</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_product_tree_view"/>
            <field name="arch" type="xml">

                 <xpath expr="//field[@name='standard_price']" position="attributes">
                    <attribute name="groups">cubes_next_generation_customs.product_cost_group</attribute>

                </xpath>

            </field>
        </record>

         <record id="product_template_tree_inherit" model="ir.ui.view">
            <field name="name">product.template.tree.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">

                 <xpath expr="//field[@name='standard_price']" position="attributes">
                    <attribute name="groups">cubes_next_generation_customs.product_cost_group</attribute>

                </xpath>

            </field>
        </record>


          <record id="product_only_form_view_access_inherit" model="ir.ui.view">
            <field name="name">product.only.form.view.global.discount.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">

                 <xpath expr="//label[@for='standard_price']" position="attributes">
                    <attribute name="groups">cubes_next_generation_customs.product_cost_group</attribute>

                </xpath>

                 <xpath expr="//div[@name='standard_price_uom']" position="attributes">
                    <attribute name="groups">cubes_next_generation_customs.product_cost_group</attribute>

                </xpath>

            </field>
        </record>


    </data>
</odoo>