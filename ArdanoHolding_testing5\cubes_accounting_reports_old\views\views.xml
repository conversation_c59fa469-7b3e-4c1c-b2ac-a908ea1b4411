<odoo>
  <data>
    <record id="cubes_account_analytic_account_view_list_inherit" model="ir.ui.view">
            <field name="name">cubes.account.analytic.account.list.inherit</field>
            <field name="model">account.analytic.line</field>
            <field name="inherit_id" ref="analytic.view_account_analytic_line_tree"/>
            <field name="arch" type="xml">
                <field name="amount" position="before">
                  <field name="goods_balance"/>
                    <field name="cleaning_balance"/>
                    <field name="other_balance"/>
                </field>
            </field>
        </record>



      <record id="action_cubes_accounting_action" model="ir.actions.act_window">
            <field name="name">Analytic Accounts Detail</field>
            <field name="res_model">account.analytic.line</field>
            <field name="view_mode">tree</field>
          <field name="context">{ 'group_by': 'account_id'}</field>
          <!--search_default_group_date': 1,-->
      </record>

      <menuitem
            parent="account.menu_finance_reports"
            id="cubes_accounting_reports"
            action="action_cubes_accounting_action"/>

    <record id="cubes_account_move_view_form_inherit" model="ir.ui.view">
            <field name="name">cubes.account.move.form.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <field name="ref" position="after">
                    <field name="indirect_cost"/>
                    <field name="analytic_account_custom"  attrs="{'invisible': [('indirect_cost', '!=', 'garbage_account')]}"/>
                    <field name="partner_id_custom"  attrs="{'invisible': ['|',('indirect_cost', '=', False),('indirect_cost', '=', 'garbage_account')]}"/>
                </field>
            </field>
    </record>

  </data>
</odoo>