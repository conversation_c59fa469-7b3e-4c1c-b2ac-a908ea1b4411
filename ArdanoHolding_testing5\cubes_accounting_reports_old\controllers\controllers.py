# -*- coding: utf-8 -*-
# from odoo import http


# class CubesAccountingReports(http.Controller):
#     @http.route('/cubes_accounting_reports/cubes_accounting_reports', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/cubes_accounting_reports/cubes_accounting_reports/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('cubes_accounting_reports.listing', {
#             'root': '/cubes_accounting_reports/cubes_accounting_reports',
#             'objects': http.request.env['cubes_accounting_reports.cubes_accounting_reports'].search([]),
#         })

#     @http.route('/cubes_accounting_reports/cubes_accounting_reports/objects/<model("cubes_accounting_reports.cubes_accounting_reports"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('cubes_accounting_reports.object', {
#             'object': obj
#         })
