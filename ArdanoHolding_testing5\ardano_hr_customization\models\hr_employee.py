from odoo import api, models, fields
from odoo.exceptions import ValidationError


class ResPartnerBank(models.Model):
    _inherit = 'res.partner.bank'

    partner_id = fields.Many2one('res.partner', tracking=True, required=False)
    employee_id = fields.Many2one('hr.employee', string='Employee')


class HrEmployee(models.Model):
    _inherit = "hr.employee"

    id_number = fields.Char(string='ID Number')
    certificate_id = fields.Many2one('hr.employee.certificate', string='Certificate Level')
    bank_ids = fields.One2many('res.partner.bank', 'employee_id', readonly=False)
    date_of_certification = fields.Date(string='Certification Date')
    bonus_id = fields.Many2one('hr.bounce', string="Job position")
    ardano_department_id = fields.Many2one('hr.ardano_department', string='Department')
    ardano_team_id = fields.Many2one('hr.team', string='Team')
    ardano_unit_id = fields.Many2one('hr.unit', string='Unit')


    # @api.constrains('bank_ids')
    # def set_bank_ids_validation(self):
    #     for rec in self:
    #         if not rec.address_home_id:
    #             raise ValidationError('you must create address first.')



class HrJob(models.Model):
    _inherit = 'hr.job'

    hourly_wage = fields.Float(string ="Hourly Wage")


class JobCategory(models.Model):
    _name = 'job.category'
    _rec_name = 'job_category'

    job_category = fields.Char("Job Category", required=True, Index=True)

class ContractClassification(models.Model):
    _name = 'contract.classification'
    _rec_name = 'contract_name'

    contract_name = fields.Char("Contract Name", required=True, Index=True)
    percentage = fields.Float("Percentage")

