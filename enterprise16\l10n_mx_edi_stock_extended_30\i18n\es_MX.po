# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx_edi_stock_extended_30
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 08:50+0000\n"
"PO-Revision-Date: 2024-02-06 08:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx_edi_stock_extended_30
#: model_terms:ir.actions.act_window,help:l10n_mx_edi_stock_extended_30.l10n_mx_edi_customs_document_type_action
msgid "Add a new customs document type for MX delivery guide."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model_terms:ir.actions.act_window,help:l10n_mx_edi_stock_extended_30.l10n_mx_edi_customs_regime_action
msgid "Add a new customs regime for MX delivery guide."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: code:addons/l10n_mx_edi_stock_extended_30/models/stock_picking.py:0
#, python-format
msgid "At least one product is missing a material type."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__code
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__code
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_document_type_code
msgid "Code"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_product__country_code
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_template__country_code
msgid "Country Code"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__create_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__create_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__create_date
msgid "Created on"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.ui.menu,name:l10n_mx_edi_stock_extended_30.menu_stock_mx_customs_document_type
msgid "Custom Document Type"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_doc_identification
msgid "Customs Document Identification"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_document_type_id
msgid "Customs Document Type"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.actions.act_window,name:l10n_mx_edi_stock_extended_30.l10n_mx_edi_customs_regime_action
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_regime_id
#: model:ir.ui.menu,name:l10n_mx_edi_stock_extended_30.menu_stock_mx_customs_regime
msgid "Customs Regime"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_product__l10n_mx_edi_material_description
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_template__l10n_mx_edi_material_description
msgid ""
"Description of the state of the material or product when performing a "
"foreign trade operation."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__display_name
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.actions.act_window,name:l10n_mx_edi_stock_extended_30.l10n_mx_edi_customs_document_type_action
msgid "Documento Aduanero"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_document_type__goods_direction__export
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_regime__goods_direction__export
msgid "Export"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_doc_identification
msgid "Folio of the customs document associated with the import of the goods."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__id
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__id
msgid "ID"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_document_type__goods_direction__import
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_regime__goods_direction__import
msgid "Import"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_document_type__goods_direction__both
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__l10n_mx_edi_customs_regime__goods_direction__both
msgid "Import, Export"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_importer_id
msgid "Importer"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_importer_id
msgid "Importer registered in the customs documentation."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type____last_update
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__write_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__write_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__product_template__l10n_mx_edi_material_type__04
msgid "Materia para la industria manufacturera"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__product_template__l10n_mx_edi_material_type__01
msgid "Materia prima"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__product_template__l10n_mx_edi_material_type__02
msgid "Materia procesada"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__product_template__l10n_mx_edi_material_type__03
msgid "Materia terminada(producto terminado)"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_product__l10n_mx_edi_material_description
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_template__l10n_mx_edi_material_description
msgid "Material Description"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_product__l10n_mx_edi_material_type
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_product_template__l10n_mx_edi_material_type
msgid "Material Type"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model,name:l10n_mx_edi_stock_extended_30.model_l10n_mx_edi_customs_document_type
msgid "Mexican Customs Document Type"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model,name:l10n_mx_edi_stock_extended_30.model_l10n_mx_edi_customs_regime
msgid "Mexican Customs Regime"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__name
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__name
msgid "Name"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock_extended_30.selection__product_template__l10n_mx_edi_material_type__05
msgid "Otra"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_pedimento_number
msgid "Pedimento Number"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_pedimento_number
msgid "Pedimento number associated with the import of the goods."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: code:addons/l10n_mx_edi_stock_extended_30/models/stock_picking.py:0
#, python-format
msgid "Please define a VAT number for the importer."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: code:addons/l10n_mx_edi_stock_extended_30/models/stock_picking.py:0
#, python-format
msgid "Please define a customs document identification."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: code:addons/l10n_mx_edi_stock_extended_30/models/stock_picking.py:0
#, python-format
msgid "Please define a customs regime."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model,name:l10n_mx_edi_stock_extended_30.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_regime_id
msgid "Regime associated to the good's transfer (import or export)."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_product__l10n_mx_edi_material_type
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_template__l10n_mx_edi_material_type
msgid ""
"State of the material or product when performing a foreign trade operation."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_product__country_code
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_product_template__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.constraint,message:l10n_mx_edi_stock_extended_30.constraint_l10n_mx_edi_customs_document_type_uniq_code
#: model:ir.model.constraint,message:l10n_mx_edi_stock_extended_30.constraint_l10n_mx_edi_customs_regime_uniq_code
msgid "This code is already used."
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model,name:l10n_mx_edi_stock_extended_30.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_document_type__goods_direction
#: model:ir.model.fields,field_description:l10n_mx_edi_stock_extended_30.field_l10n_mx_edi_customs_regime__goods_direction
msgid "Type"
msgstr ""

#. module: l10n_mx_edi_stock_extended_30
#: model:ir.model.fields,help:l10n_mx_edi_stock_extended_30.field_stock_picking__l10n_mx_edi_customs_document_type_id
msgid "Type of customs document associated with the transport of the goods."
msgstr ""
