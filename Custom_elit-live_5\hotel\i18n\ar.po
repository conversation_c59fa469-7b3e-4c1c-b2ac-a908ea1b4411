# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-01 00:00+0000\n"
"PO-Revision-Date: 2023-01-01 00:00+0000\n"
"Last-Translator: \n"
"Language-Team: Arabic\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Generator: Odoo 16.0\n"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_count
msgid "# Product Variants"
msgstr "# أنواع المنتجات"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__product_count
msgid "# Products"
msgstr "# المنتجات"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_service_type_form
msgid "Account Properties"
msgstr "خصائص الحساب"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree_view
msgid "All Folio"
msgstr "جميع الفوليو"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_view_form
msgid "Amenities"
msgstr "المرافق"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__rcateg_id
msgid "Amenity Catagory"
msgstr "فئة المرافق"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_type_view_form
msgid "Amenity Category"
msgstr "فئة المرافق"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_parent_amenity
msgid "Amenity Defination"
msgstr "تعريف المرفق"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__amenity_rate
msgid "Amenity Rate"
msgstr "سعر المرفق"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_list
msgid "Amenity rate"
msgstr "سعر المرفق"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_undiscounted
msgid "Amount Before Discount"
msgstr "المبلغ قبل الخصم"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Automatic Declaration"
msgstr "الإعلان التلقائي"

#. module: hotel
#: model:ir.model.fields.selection,name:hotel.selection__product_product__state__draft
msgid "Available"
msgstr "متاح" 