# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <manelf<PERSON>@outlook.com>, 2022
# <PERSON>rnau <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# Ó<PERSON><PERSON> Fonseca <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:52+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Óscar Fonseca <<EMAIL>>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_booth_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Un producte emmagatzemable és un producte pel qual gestioneu estoc. Cal instal·lar l'aplicació Inventari.\n"
"Un consumible és un producte pel qual no es gestiona estoc.\n"
"Un servei és un producte no material que subministreu. "

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_ids
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__event_booth_id
msgid "Booth"
msgstr "Booth"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_id
msgid "Booth Category"
msgstr "Categoria d'estand"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr ""
"Categoria d'estands per a la qual encara hi ha estands disponibles. Es fa "
"servir al frontend"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_count
msgid "Booth Count"
msgstr "Comptador  d'estand"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_category_view_form
msgid "Booth Details"
msgstr "Detalls d'estand"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_tree
msgid "Booth Registration"
msgstr "Registre d'estand "

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_ids
#: model_terms:ir.ui.view,arch_db:event_booth_sale.sale_order_view_form
msgid "Booths"
msgstr "Estands"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_category_id
msgid "Booths Category"
msgstr "Categoria d'estands"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Cancel"
msgstr "Cancel·lar"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_ids
msgid "Confirmed Booths"
msgstr "Estands confirmats"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_registration_ids
msgid "Confirmed Registration"
msgstr "Registre confirmat"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Contact"
msgstr "Contacte"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_email
msgid "Contact Email"
msgstr "Correu electrònic del contacte"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_mobile
msgid "Contact Mobile"
msgstr "Contacte mòbil"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_name
msgid "Contact Name"
msgstr "Nom del contacte"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_phone
msgid "Contact Phone"
msgstr "Telèfon del contacte"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_date
msgid "Created on"
msgstr "Creat el"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__partner_id
msgid "Customer"
msgstr "Client"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Details"
msgstr "Detalls"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__display_name
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_id
msgid "Event"
msgstr "Esdeveniment"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth
#: model:ir.model.fields.selection,name:event_booth_sale.selection__product_template__detailed_type__event_booth
msgid "Event Booth"
msgstr "Cabina d'esdeveniments"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_category
msgid "Event Booth Category"
msgstr "Categoria de l'estand de l'esdeveniment"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "Categoria disponible del contenidor d'esdeveniments"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_configurator
msgid "Event Booth Configurator"
msgstr "Configurador dels estands d'Esdeveniment"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_registration
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__event_booth_registration_ids
msgid "Event Booth Registration"
msgstr "Esdeveniment registre a la cabina"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_type_booth
msgid "Event Booth Template"
msgstr "Plantilla d'estand d'esdeveniment"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_id
msgid "Final Sale Order Line"
msgstr "Línia final de venda"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__id
msgid "ID"
msgstr "ID"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__image_1920
msgid "Image"
msgstr "Imatge"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__is_event_booth
msgid "Is Event Booth"
msgstr "És un estand d'esdeveniments"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__is_paid
msgid "Is Paid"
msgstr "És pagat"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator____last_update
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Ok"
msgstr "Ok"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_id
msgid "Order Reference"
msgstr "Referència de comanda"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Paid"
msgstr "Pagat"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Pending Booths"
msgstr "Estands pendents"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__price
msgid "Price"
msgstr "Preu"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce
msgid "Price Reduce"
msgstr "Preu reduït "

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Preu reduït sense IVA"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_template
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_template_option__product_id
msgid "Product"
msgstr "Producte"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_booth_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Tipus de producte"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_product
msgid "Product Variant"
msgstr "Variants de producte"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Línia de plantilla de pressupostos"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Opció de plantilla de pressupost"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Registrations"
msgstr "Registres"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid "Registrations from the same Order Line must belong to a single event."
msgstr ""
"Les inscripcions de la mateixa línia de comandes han de pertànyer a un sol "
"esdeveniment."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_registration_ids
msgid "SO Lines with reservations"
msgstr "Per tant, línies amb reserves"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Sale Order"
msgstr "Comanda de venda"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__sale_order_line_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__sale_order_line_id
msgid "Sale Order Line"
msgstr "Línia comanda de venda"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order
msgid "Sales Order"
msgstr "Comanda de venda"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: event_booth_sale
#: model:ir.actions.act_window,name:event_booth_sale.event_booth_configurator_action
msgid "Select an event booth"
msgstr "Seleccioneu un estand d'esdeveniments"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid ""
"The following booths are unavailable, please remove them to continue : "
"%(booth_names)s"
msgstr ""
"Els estands següents no estan disponibles. Si us plau, treieu-los per a "
"continuar : %(booth_names)s"

#. module: event_booth_sale
#: model:ir.model.constraint,message:event_booth_sale.constraint_event_booth_registration_unique_registration
msgid "There can be only one registration for a booth by sale order line"
msgstr ""
"Només pot haver-hi un registre per a un estand per línia de comandes de "
"venda"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_tree_from_event
msgid "Total"
msgstr "Total"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Used to create registration when providing the desired event booth."
msgstr ""
"S'utilitza per crear el registre quan es proporciona l'estand "
"d'esdeveniments desitjada."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth.py:0
#, python-format
msgid ""
"You can't delete the following booths as they are linked to sales orders: "
"%(booths)s"
msgstr ""
"No podeu eliminar aquests stands perquè estan enllaçats a comandes de venda:"
" %(booths)s"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/wizard/event_booth_configurator.py:0
#, python-format
msgid "You have to select at least one booth."
msgstr "Heu de seleccionar almenys un estand."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth_registration.py:0
#, python-format
msgid ""
"Your order has been cancelled because the following booths have been "
"reserved"
msgstr ""
"S'ha cancel·lat la comanda, perquè els següents estands han estat reservats"
