<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="sign.SignViews.buttons" owl="1">
        <t t-if="!env.isSmall">
            <input type="file" multiple="true" t-ref="uploadFileInput" accept="application/pdf, application/x-pdf, application/vnd.cups-pdf" class="o_input_file o_hidden" t-on-change.stop="onFileInputChange"/>
            <t t-if="props.resModel === 'sign.template'" t-call="sign.SignViews.sign_template.buttons"/>
            <t t-if="props.resModel === 'sign.request'" t-call="sign.SignViews.sign_request.buttons"/>
        </t>
    </t>

    <t t-name="sign.SignViews.sign_template.buttons" owl="1">
        <button class="btn btn-primary" title="Upload a pdf that you want to sign directly" t-on-click="() => this.requestFile(true, 'sign_send_request')">
            UPLOAD A PDF TO SIGN
        </button>
        <button t-if="isSignUser" class="btn btn-link" title="Upload a PDF file to use as a template." t-on-click="() => this.requestFile(false, 'sign_template_edit')">
            UPLOAD A PDF TEMPLATE
        </button>
    </t>

    <t t-name="sign.SignViews.sign_request.buttons" owl="1">
        <button class="btn btn-primary" title="Upload a pdf that you want to sign directly" t-on-click="() => this.requestFile(true, 'sign_send_request')">
            UPLOAD A PDF TO SIGN
        </button>
    </t>
</templates>
