# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# jabiri7, 2022
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr "<b>Publicació LinkedIn</b>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr "ID actual de LinkedIn de la publicació"

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages data: %r."
msgstr ""

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages: %r."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr "ID de l'aplicació"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr "App Secreta"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Imatge de l'autor"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr ""
"Marqueu aquesta opció si voleu utilitzar el vostre compte de desenvolupador "
"de LinkedIn personal en lloc del compte proporcionat."

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr "Mostrar la vista prèvia de LinkedIn"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#, python-format
msgid "Likes"
msgstr "M'agrada"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr "ID del compte de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr "URL del compte de LinkedIn"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
#, python-format
msgid "LinkedIn Comments"
msgstr "Comentaris de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr "Agradats de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr "Vista prèvia de Linkedin"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr "Nom de la vanitat LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "Token d'accés LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr "ID de l'autor de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr "URL de l'autor de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr "URL de la imatge de l'autor LinkedIn"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "LinkedIn did not provide a valid access token."
msgstr "LinkedIn no ha proporcionat un testimoni d'accés vàlid."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr "URN de la publicació de LinkedIn"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr "Compte de desenvolupador de Linkedin"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr "Tipus de contingut multimèdia"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr "Publica la imatge"

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "Missatges"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr "Compte social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr "Publicació social en directe"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "Mitjans de comunicació"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "Correu social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicació social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "Flux social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr "Publicació de la transmissió en viu social"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr "El token d'accés s'utilitza per realitzar la sol·licitud a l'API REST"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"L'URL sol·licitat per aquest servei ha retornat un error. Contacteu amb "
"l'autor de l'aplicació."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr "No hi ha cap pàgina enllaçada a aquest compte"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "Hi ha hagut un problema d'autenticació durant la vostra sol·licitud."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "No autoritzat. Si us plau, contacteu amb l'administrador."

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "Desconegut"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr "Utilitza el teu propi compte LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Utilitzat per fer comparacions quan necessitem restringir algunes "
"característiques a un contingut multimèdia específic ('facebook', 'twitter',"
" ...)."

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr "Nom Vanity, utilitzat per generar un enllaç a l'autor"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr ""
"No hem pogut pujar la imatge, intentar reduir la seva mida i publicar-la de "
"nou (error: Ha fallat durant el registre de pujada)."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr ""
"No hem pogut pujar la imatge, intentar reduir la seva mida i tornar-la a "
"publicar."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#, python-format
msgid "Wrong stream type for \"%s\""
msgstr "Tipus de transmissió en viu incorrecte per a «%s\""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "No tens una subscripció activa. Si us plau, compri un aquí:%s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid "unknown"
msgstr "desconegut"
