<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="advanced_web_domain_widget._DomainSelectorBits" owl="1">
        <t t-set="tree" t-value="buildTree()"/>
        <t t-if="tree">
            <DomainSelectorRootNodeBits
                value="props.value"
                node="tree"
                readonly="props.readonly"
                resModel="props.resModel"
                isDebugMode="props.isDebugMode"
                debugValue="props.debugValue"
                className="props.className"
            />
        </t>
        <t t-else="">
            <div t-att-class="props.className">This domain is not supported.</div>
        </t>
    </t>

</templates>
