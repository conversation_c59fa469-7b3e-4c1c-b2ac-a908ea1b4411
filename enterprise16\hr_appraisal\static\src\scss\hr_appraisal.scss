.o_hr_employee_kanban {
    .o_kanban_card_appraisal {
        height: 12.75rem;
    }
    .oe_kanban_card {
        .o_goal_progression {
            margin-left: 4px;
            margin-right: 4px;
            padding: 0.5rem 0.5rem;
            &.active {
                background-color: $o-enterprise-primary-color;
                color: white;
            }
        }
        .goal_description {
            height: 40px;
        }
        .o_kanban_record_top {
            .o_kanban_record_top_left, .o_kanban_record_top_right {
                overflow-wrap: break-word;
                word-wrap: break-word;
                display: flex;
                font-size: 15px;
            }
            .o_kanban_record_top_left {
                flex: 1 1 auto;
            }
            .o_kanban_record_top_right {
                flex: 0 1 auto;
                margin-right: 1em;
            }
        }
        .o_field_image {
            margin-right: 8px;
        }
    }
    .ribbon {
        &::before, &::after {
            display: none;
        }

        span {
            padding: 5px;
            font-size: small;
            z-index: unset;
        }
    }
    .ribbon-top-right {
        margin-top: -9px;

        span {
            left: 12px;
            right: 30px;
            height: 25px;
            top: 18px;
        }
    }
}

.o_appraisal_form {
    .o_form_sheet {
        padding-bottom: 0 !important;
    }

    div.o_field_boolean_toggle_confirm, div.o_field_boolean_toggle {
        display: inline-flex;
    }
    div.o_boolean_toggle {
        &.o_readonly_modifier {
            cursor: not-allowed;
        }
    }
    .o_appraisal_overlay {
        position: absolute;
        top: 40px;
        width: 100%;
        font-size: 1.5rem;
        text-align: center;
        display: flex;
        justify-content: center;
        flex-direction: column;
        min-height: 6rem;

        .fa {
            display: block;
            font-size: 4rem;
        }
    }
    .o_appraisal_blur {
        filter: blur(0.2rem);
        line-height: 1;
        margin-top: 2rem;
        opacity: .3;
        user-select: none;
        pointer-events: none;
    }
    .o_appraisal_both_feedback {
        margin: -16px var(--notebook-margin-x) !important;
        &>.o_appraisal_feedback {
            max-width: 50%;
        }
    }
    .o_appraisal_feedback {
        display: block;
        box-sizing: border-box;
        flex: 1;
        padding: 32px 16px;
        max-width: 100%;
        * {
            box-sizing: border-box;
        }
        .o_field_html {
            margin-bottom: 0;
        }
        .row {
            max-width: 100%;
            margin: 0;
        }
        .o_appraisal_input {
            padding: 0 16px;
        }
        .o_appraisal_input,.o_appraisal_input * {
            max-width: 100%;
        }
    }
    .o_appraisal_manager_feedback {
        background-color: map-get($grays, '200');
        .note-editable {
            background-color: map-get($grays, '200');
        }
    }
    @include media-breakpoint-down(lg) {
        .o_appraisal_feedback_title {
            flex-direction: column;
        }
    }
    @include media-breakpoint-down(md) {
        .o_appraisal_feedback_title {
            flex-direction: row;
        }
        .o_appraisal_both_feedback>.o_appraisal_feedback {
            max-width: 100%!important;
            margin: 0;
        }
    }
    @include media-breakpoint-down(sm) {
        .o_appraisal_feedback_title {
            flex-direction: column;
        }
    }
}

.o_goal_list {
    .o_badge_cell{
        text-align: center;
    }
}

.o_appraisal_goal_form {
    .o_selection_badge.active {
        background: $o-brand-odoo !important;
        color: $o-white !important;
        border-color: $o-brand-odoo !important;
    }
}

.feedback-template .note-editable {
    border: none;
}
