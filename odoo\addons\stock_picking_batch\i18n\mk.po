# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_picking_wave
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-05-31 13:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/"
"mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: stock_picking_wave
#: model:ir.model,name:stock_picking_wave.model_stock_picking_to_wave
msgid "Add pickings to a picking wave"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Add pickings to wave"
msgstr ""

#. module: stock_picking_wave
#: model:ir.actions.act_window,name:stock_picking_wave.action_picking_to_wave
#: model:ir.actions.act_window,name:stock_picking_wave.picking_to_wave_act
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Add to Wave"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Cancel"
msgstr "Откажи"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Cancel picking"
msgstr "Откажи избор"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Cancelled"
msgstr "Откажано"

#. module: stock_picking_wave
#: model_terms:ir.actions.act_window,help:stock_picking_wave.action_picking_wave
msgid "Click to create a Picking Wave."
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Confirm"
msgstr "Потврди"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Confirm picking"
msgstr "Потврди избор"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_create_uid
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_create_date
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_create_date
msgid "Created on"
msgstr "Креирано на"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_display_name
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
#: selection:stock.picking.wave,state:0
msgid "Done"
msgstr "Завршено"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Draft"
msgstr "Нацрт"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Force availability"
msgstr "Присили достапност"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Group By"
msgstr "Групирај по"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_id
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_id
msgid "ID"
msgstr "ID"

#. module: stock_picking_wave
#: model:product.product,name:stock_picking_wave.product_product_ice_cream_choco
#: model:product.template,name:stock_picking_wave.product_product_ice_cream_choco_product_template
msgid "Ice Cream Chocolate"
msgstr "Чоколаден сладолед"

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_ice_cream_choco
#: model:product.template,description_sale:stock_picking_wave.product_product_ice_cream_choco_product_template
#, fuzzy
msgid "Ice Cream Chocolate with sticks"
msgstr "Сладолед од чоколадо"

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_ice_cream_vani
#: model:product.product,name:stock_picking_wave.product_product_ice_cream_vani
#: model:product.template,description_sale:stock_picking_wave.product_product_ice_cream_vani_product_template
#: model:product.template,name:stock_picking_wave.product_product_ice_cream_vani_product_template
msgid "Ice Cream Vanilla"
msgstr "Сладолед од ванила"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "In Progress"
msgstr "Во тек"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave___last_update
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave___last_update
msgid "Last Modified on"
msgstr "Последна промена на"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_write_uid
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_write_date
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_picking_ids
msgid "List of picking associated to this wave"
msgstr ""

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_name
msgid "Name of the picking wave"
msgstr ""

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:42
#, python-format
msgid "Nothing to print."
msgstr "Нема ништо за печатење."

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_user_id
msgid "Person responsible for this wave"
msgstr ""

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:60
#: model:ir.model,name:stock_picking_wave.model_stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_wave_id
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_id_9535
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
#, python-format
msgid "Picking Wave"
msgstr ""

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_name
msgid "Picking Wave Name"
msgstr ""

#. module: stock_picking_wave
#: model:ir.actions.act_window,name:stock_picking_wave.action_picking_wave
#: model:ir.ui.menu,name:stock_picking_wave.menu_action_picking_wave
msgid "Picking Waves"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Picking Waves not finished"
msgstr ""

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_id_9535
msgid "Picking wave associated to this picking"
msgstr ""

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Pickings"
msgstr "Требувања"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Print all pickings"
msgstr ""

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_user_id
msgid "Responsible"
msgstr "Одговорен"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Running"
msgstr "Стартување"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Search Picking Waves"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Select a wave"
msgstr ""

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:59
#, python-format
msgid ""
"Some pickings are still waiting for goods. Please check or force their "
"availability before setting this wave to done."
msgstr ""

#. module: stock_picking_wave
#: model:product.product,name:stock_picking_wave.product_product_dry_specu
#: model:product.template,name:stock_picking_wave.product_product_dry_specu_product_template
msgid "Speculoos"
msgstr ""

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_dry_specu
#: model:product.template,description_sale:stock_picking_wave.product_product_dry_specu_product_template
msgid "Speculoos - A belgian speciality"
msgstr ""

#. module: stock_picking_wave
#: model:mail.message.subtype,description:stock_picking_wave.mt_wave_state
#: model:mail.message.subtype,name:stock_picking_wave.mt_wave_state
msgid "Stage Changed"
msgstr "Етапата е променета"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_state
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "State"
msgstr "Држава"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_tree
msgid "Stock Picking Waves"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.actions.act_window,help:stock_picking_wave.action_picking_wave
msgid ""
"The goal of the picking waves is to group operations that may\n"
"                (needs to) be done together in order to increase their "
"efficiency.\n"
"                It may also be useful to assign jobs (one person = one wave) "
"or\n"
"                help the timing management of operations (tasks to be done "
"at 1pm)."
msgstr ""

#. module: stock_picking_wave
#: model:ir.model,name:stock_picking_wave.model_stock_picking
msgid "Transfer"
msgstr "Трансфер"

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:60
#, python-format
msgid "Transferred by"
msgstr ""

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "User"
msgstr "Корисник"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_stock_picking_wave_search_inherit
msgid "Wave"
msgstr "Бран"

#~ msgid "Action Needed"
#~ msgstr "Потребна е акција"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Датум на испраќање на последната порака"

#~ msgid "Followers"
#~ msgstr "Пратители"

#~ msgid "Followers (Channels)"
#~ msgstr "Пратители (Канали)"

#~ msgid "Followers (Partners)"
#~ msgstr "Пратители (Партнери)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето внимание."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето вниманите."

#~ msgid "Is Follower"
#~ msgstr "е следач"

#~ msgid "Last Message Date"
#~ msgstr "Датум на последна порака"

#~ msgid "Messages"
#~ msgstr "Пораки"

#~ msgid "Messages and communication history"
#~ msgstr "Пораки и историја на комуникација"

#~ msgid "Number of Actions"
#~ msgstr "Број на акции"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Број на пораки за кои ѝм е потребна акција"

#~ msgid "Number of unread messages"
#~ msgstr "Број на непрочитани пораки"

#~ msgid "Unread Messages"
#~ msgstr "Непрочитани Пораки"

#~ msgid "Unread Messages Counter"
#~ msgstr "Тезга на непрочитаните пораки"

#~ msgid "Website Messages"
#~ msgstr "Пораки на веб сајт"

#~ msgid "Website communication history"
#~ msgstr "Историја на веб комуникација"
