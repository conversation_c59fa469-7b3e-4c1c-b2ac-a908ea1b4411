<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_hotel_management_config_settings" model="ir.ui.view">
            <field name="name">hotel management settings</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <form string="Configure Hotel Management" version="7.0" class="oe_form_configuration">
                    <header>
                        <button string="Apply" type="object" name="execute" class="oe_highlight"/>
                        or
                        <button string="Cancel" type="object" name="cancel" class="oe_link"/>
                    </header>
                    <separator string="Hotel Management"/>
                    <group>
<!--                         <label for="id" string="Invoice"/> -->
                        <div >
                        	<field name="test" class="oe_inline" on_change="onchange_test(test)" />
                                <label for="test"/>
                        </div>
                    </group>
                </form>
            </field>
        </record>

        <record id="action_hotel_management_config_settings" model="ir.actions.act_window">
            <field name="name">hotel Project</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
        </record>

<!--        <menuitem id="config_root_menu_id" name="Settings" action="action_hotel_management_config_settings" parent="hotel.hotel_configuration_menu" sequence="1"/>-->


        <record id="hotel_setting_id" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.base.setup</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="0"/>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@id='invite_users']" position="after">
                    <div id="invite_users">
                            <h2>Check In &amp; Check Out</h2>
                            <div class="row mt16 o_settings_container" name="users_setting_container">
                                <div class="col-12 col-lg-6 o_setting_box" id="invite_users_setting">
                                    <div class="o_setting_right_pane">
                                        <span><strong>Check In Time:  </strong>   <field name='checkin'/></span>
                                    </div>
                                </div>
                                <div class="col-12 col-lg-6 o_setting_box" id="active_user_setting">
                                    <div class="o_setting_right_pane">
                                        <span>
                                            <strong>Check Out Time:  </strong> <field name="checkout"/>
                                        </span>
                                        <br/>
                                        <span>
                                            <strong>Real Check Out Time:  </strong> <field name="real_check_out"/>
                                        </span>

                                    </div>
                                </div>
                            </div>
                        </div>
                </xpath>
            </field>
        </record>



    </data>
</odoo>
