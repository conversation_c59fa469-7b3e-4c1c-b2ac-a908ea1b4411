<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="lunch.LunchCurrency" owl="1">
        <span>
            <t t-if="props.currency.position == 'before'" t-esc="props.currency.symbol"/>
            <t t-esc="amount"/>
            <t t-if="props.currency.position == 'after'" t-esc="props.currency.symbol"/>
        </span>
    </t>

    <t t-name="lunch.LunchOrderLine" owl="1">
        <div class="d-flex align-items-center pe-3">
            <div class="btn-group">
                <button class="btn btn-sm btn-icon btn-link fa fa-minus-circle p-0" t-if="canEdit" t-on-click="() => this.updateQuantity(-1)"/>
                <span t-esc="line.quantity" class="px-2 py-1"/>
                <button class="btn btn-sm btn-icon btn-link fa fa-plus-circle p-0" t-if="canEdit" t-on-click="() => this.updateQuantity(1)"/>
            </div>
            <span class="flex-grow-1 ps-2 text-700">
                <a t-on-click="() => props.openOrderLine(line.product[0], line.id)" t-if="canEdit" role="button" title="Edit order">
                    <t t-esc="line.product[1]"/>
                </a>
                <t t-else="" t-esc="line.product[1]"/>
                <span t-esc="line.state" t-attf-class="badge ms-2 rounded-pill text-bg-#{badgeClass} border-#{badgeClass} "/>
            </span>
            <LunchCurrency currency="props.currency" amount="line.product[2]"/>
        </div>
        <ul t-if="hasToppings" class="list-unstyled ps-4">
            <li t-foreach="line.toppings" t-as="topping" t-key="topping" class="d-flex pe-3">
                <span class="flex-grow-1 lunch_topping" t-esc="topping[0]"/>
                <LunchCurrency currency="props.currency" amount="topping[1]"/>
            </li>
        </ul>
        <div t-if="line.note" t-esc="line.note" class="text-muted ps-4"/>
    </t>

    <t t-name="lunch.LunchAlerts" owl="1">
        <div class="alert alert-warning mb-0" t-if="props.alerts.length !== 0" role="alert">
            <t t-foreach="props.alerts" t-as="alert" t-key="alert.id">
                <LunchAlert message="alert.message" />
            </t>
        </div>
    </t>

    <t t-name="lunch.LunchUser" owl="1">
        <div class="lunch_user pb-1">
            <span t-if="!props.isManager" t-esc="props.username"/>
            <Many2XAutocomplete
                t-else=""
                value="props.username"
                resModel="'res.users'"
                getDomain="getDomain"
                activeActions="{}"
                update.bind="props.onUpdateUser"
            />
        </div>
    </t>

    <t t-name="lunch.LunchLocation" owl="1">
        <div class="lunch_location pb-1">
            <t t-if="props.location">
                <Many2XAutocomplete
                    value="props.location"
                    resModel="'lunch.location'"
                    getDomain="getDomain"
                    activeActions="{}"
                    update.bind="props.onUpdateLunchLocation"
                />
            </t>
            <t t-else="">
                <p>No lunch location available.</p>
            </t>
        </div>
    </t>

    <t t-name="lunch.LunchDashboardOrder" owl="1">
        <LunchAlerts alerts="state.infos.alerts"/>

        <div class="o_lunch_banner container-fluid p-4 border-bottom bg-view">
            <div class="row h-100">
                <div class="col-12 col-md-4">
                    <div class="row h-100 align-content-center">
                        <div class="col-3">
                            <img class="o_image_64_cover rounded-circle" t-att-src="state.infos.userimage"/>
                        </div>
                        <div class="col-9">
                            <LunchUser
                                isManager="state.infos.is_manager"
                                username="state.infos.username"
                                onUpdateUser.bind="onUpdateUser"/>

                            <LunchLocation
                                location="location"
                                onUpdateLunchLocation.bind="onUpdateLunchLocation"/>

                            <div class="d-flex pb-1">
                                <span class="flex-grow-1">Your Account</span>
                                <LunchCurrency currency="currency" amount="state.infos.wallet"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-5" t-if="hasLines">
                    <h4 class="mb-0">
                        Your Order
                        <button t-if="state.infos.raw_state != 'confirmed'" class="btn btn-sm btn-icon btn-link fa fa-trash" t-on-click.prevent="emptyCart"/>
                    </h4>
                    <ul class="o_lunch_widget_lines overflow-auto list-unstyled">
                        <li t-foreach="state.infos.lines" t-as="line" t-key="line.id">
                            <LunchOrderLine line="line" currency="currency" onUpdateQuantity.bind="onUpdateQuantity" openOrderLine.bind="props.openOrderLine"/>
                        </li>
                    </ul>
                </div>
                <div class="col-12 col-md-3 d-flex flex-column justify-content-between" t-if="hasLines">
                    <h4 class="d-flex flex-row mt-1">
                        <span class="flex-grow-1">
                            Total
                        </span>
                        <LunchCurrency currency="currency" amount="state.infos.total"/>
                    </h4>
                    <button class="btn btn-primary" t-if="canOrder" t-on-click="orderNow">Order Now</button>
                </div>
            </div>
        </div>
    </t>

    <t t-name="lunch.LunchDashboard" owl="1">
        <t t-set="currency" t-value="state.infos.currency"/>
        <t t-if="!env.isSmall">
            <t t-call="lunch.LunchDashboardOrder"/>
        </t>
        <t t-else="">
            <details class="fixed-bottom bg-view p-2" t-att-open="state.mobileOpen">
                <summary class="btn btn-primary w-100" t-on-click="() => state.mobileOpen = !state.mobileOpen">
                    <i class="fa fa-fw fa-shopping-cart"/>
                    Your Cart (<LunchCurrency currency="currency" amount="state.infos.total || 0"/>)
                </summary>

                <t t-call="lunch.LunchDashboardOrder"/>
            </details>
        </t>
    </t>
</templates>
