<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data noupdate="1">
        <record id="cron_pull_today_attendance" model="ir.cron">
            <field name="name">Biotime: Pull Today Attendance</field>
            <field name="model_id" ref="model_biotime_config"/>
            <field name="state">code</field>
            <field name="code">model.action_get_today_attendance()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active" eval="True"/>
            <field name="doall" eval="False"/>
        </record>

        <!-- Scheduled action to process unknown punches -->
        <record id="ir_cron_biotime_process_unknown_punches" model="ir.cron">
            <field name="name">BioTime: Process Unknown Punches</field>
            <field name="model_id" ref="model_biotime_punch" />
            <field name="user_id" ref="base.user_root" />
            <field name="active">True</field>
            <field name="state">code</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False" />
            <field name="code">
model.action_auto_process_unknown_punches()
            </field>
        </record>
        
        <!-- Scheduled action to notify about problematic punches -->
        <record id="ir_cron_biotime_notify_issues" model="ir.cron">
            <field name="name">BioTime: Notify About Attendance Issues</field>
            <field name="model_id" ref="model_biotime_punch" />
            <field name="user_id" ref="base.user_root" />
            <field name="active">True</field>
            <field name="state">code</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(datetime.now().replace(hour=8, minute=0, second=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="numbercall">-1</field>
            <field name="doall" eval="False" />
            <field name="code">
model.action_notify_administrators()
            </field>
        </record>
    </data>
</odoo>