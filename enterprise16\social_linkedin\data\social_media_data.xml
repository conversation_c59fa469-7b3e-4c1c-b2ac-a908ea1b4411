<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="social_media_linkedin" model="social.media">
            <field name="name">LinkedIn</field>
            <field name="media_type">linkedin</field>
            <field name="media_description">Manage your LinkedIn account and schedule posts</field>
            <field name="max_post_length">3000</field>
            <field name="image" type="base64" file="social_linkedin/static/src/img/linkedin.svg"/>
        </record>

        <record id="stream_type_linkedin_company_post" model="social.stream.type">
            <field name="name">Posts</field>
            <field name="stream_type">linkedin_company_post</field>
            <field name="media_id" ref="social_media_linkedin"></field>
        </record>
    </data>
</odoo>
