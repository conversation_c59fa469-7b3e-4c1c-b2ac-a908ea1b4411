# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* hotel_restaurant_pos
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-21 05:17+0000\n"
"PO-Revision-Date: 2020-05-21 05:17+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_details_credit
msgid "Cancel"
msgstr "Отменить"

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Cancelled"
msgstr "Отменено"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__create_date
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__create_date
msgid "Created on"
msgstr "Создан"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_pos_credit_details
msgid "Credit Details"
msgstr "Детали Кредитки"

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Credit Sale"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.ui.menu,name:hotel_restaurant_pos.credit_sale_configuration_wiz
msgid "Credit Sales"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__date_end
msgid "Date End"
msgstr "Дата Окончания"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__date_start
msgid "Date Start"
msgstr "Дата Начала"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_details_credit
msgid "Dates"
msgstr "Даты"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__display_name
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__display_name
msgid "Display Name"
msgstr "Отображаемое Имя"

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Draft"
msgstr "Черновик"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_hotel_folio
msgid "Hotel Folio Inherit Adding POS ORDER TABS"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_hotel_menucard
msgid "Hotel Menucard Inherit Adding Point_of_sale category"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__id
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__id
msgid "ID"
msgstr "Номер"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_hotel_reservation_order
msgid "Includes Hotel Reservation Order"
msgstr "Включает В Себя Бронирования Отеля"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_form_inherit1
msgid "Invoice"
msgstr "Счёт"

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Invoiced"
msgstr "Счёт выставлен"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos____last_update
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_restaurant_pos__write_date
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__folio_ids
msgid "Link to Folio"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__folio_line_id
msgid "Link to Room"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.actions.act_window,name:hotel_restaurant_pos.action_report_pos_details_credit
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_details_credit
msgid "POS Credit Details"
msgstr ""

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_folio_pos_form_id
msgid "POS ORDERS"
msgstr ""

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_folio_pos_form_id
msgid "POS Order Entries"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_folio__pos_order_ids
msgid "POS Orders"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_reservation_order__pos_ref
msgid "POS Ref."
msgstr ""

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Paid"
msgstr "Оплачено"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_form_inherit1
msgid "Payment"
msgstr "Платеж"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_hotel_menucard__pos_category
msgid "Point of Sale Category"
msgstr "Категории для точки продаж"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Конфигурация точки продаж"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr "Заказы точки продажи"

#. module: hotel_restaurant_pos
#: selection:pos.order,state:0
msgid "Posted"
msgstr "Проведено"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_details_credit
msgid "Print Report"
msgstr "Распечать отчет"

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_hotel_restaurant_pos
msgid "Restaurant POS related Back end"
msgstr ""

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_form_inherit1
msgid "Return Products"
msgstr "Возврат продуктов"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_credit_details__user_ids
msgid "Salespeople"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_config__shop_id
msgid "Shop"
msgstr "Магазин"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_config_form_inherit
msgid "Shop Name"
msgstr "Название Магазина"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_config_form_inherit
msgid "Shop id"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__state
msgid "Status"
msgstr "Статус"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__table_ids
msgid "Table number"
msgstr "№ Столика"

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_hotel_form_inherit1
msgid "Tables"
msgstr "Таблицы"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,help:hotel_restaurant_pos.field_hotel_menucard__pos_category
msgid "The Point of Sale Category this products belongs to. Those categories are used to group similar products and are specific to the Point of Sale."
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__waiter_name
msgid "Waiter Name"
msgstr "Имя Официанта"

#. module: hotel_restaurant_pos
#: model:ir.model.fields,field_description:hotel_restaurant_pos.field_pos_order__credit_sales
msgid "creditsales"
msgstr ""

#. module: hotel_restaurant_pos
#: model:ir.model,name:hotel_restaurant_pos.model_pos_session
msgid "inherited pos.session class"
msgstr ""

#. module: hotel_restaurant_pos
#: model_terms:ir.ui.view,arch_db:hotel_restaurant_pos.view_pos_details_credit
msgid "or"
msgstr "или"

