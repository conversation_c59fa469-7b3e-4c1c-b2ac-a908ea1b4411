# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-09 09:25+0000\n"
"PO-Revision-Date: 2022-11-09 09:25+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx_reports
#: model:ir.model.fields.selection,name:l10n_mx_reports.selection__res_partner__l10n_mx_type_of_operation__03
msgid " 03 - Provision of Professional Services"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields.selection,name:l10n_mx_reports.selection__res_partner__l10n_mx_type_of_operation__06
msgid " 06 - Renting of buildings"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields.selection,name:l10n_mx_reports.selection__res_partner__l10n_mx_type_of_operation__85
msgid " 85 - Others"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Accounts with too much tags"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Accounts without tag"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,help:l10n_mx_reports.field_res_country__demonym
msgid "Adjective for relationship between a person and a country."
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model,name:l10n_mx_reports.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "COA SAT (XML)"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model,name:l10n_mx_reports.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_country
#: model:ir.model,name:l10n_mx_reports.model_res_country
msgid "Country"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#: model:account.report,name:l10n_mx_reports.diot_report
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_counter
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_country_code
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_exempt
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_importation_16
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_operation_type_code
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_paid_0
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_paid_16
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_paid_16_non_cred
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_paid_8
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_paid_8_non_cred
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_partner_nationality
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_partner_vat_number
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_refunds
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_third_party_code
#: model:account.report.expression,report_line_name:l10n_mx_reports.diot_report_line_withheld
#: model:account.report.line,name:l10n_mx_reports.diot_report_line
#: model:ir.actions.client,name:l10n_mx_reports.action_account_report_diot
#, python-format
msgid "DIOT"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "DIOT (txt)"
msgstr ""

#. module: l10n_mx_reports
#: model_terms:ir.ui.view,arch_db:l10n_mx_reports.res_partner_account_diot_form_oml
msgid "DIOT Information"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "DPIVA"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "DPIVA (txt)"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_country__demonym
msgid "Demonym"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_importation_16
msgid "Importation 16%"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,help:l10n_mx_reports.field_res_partner__l10n_mx_type_of_operation
#: model:ir.model.fields,help:l10n_mx_reports.field_res_users__l10n_mx_type_of_operation
msgid ""
"Indicate the operations type that makes this supplier. Is the second column "
"in DIOT report"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,help:l10n_mx_reports.field_res_partner__l10n_mx_type_of_third
#: model:ir.model.fields,help:l10n_mx_reports.field_res_users__l10n_mx_type_of_third
msgid ""
"Indicate the type of third that is the supplier. Is the first column in DIOT"
" report."
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Invalid SAT code: %s"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_partner__l10n_mx_nationality
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_users__l10n_mx_nationality
msgid "L10N Mx Nationality"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_partner__l10n_mx_type_of_operation
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_users__l10n_mx_type_of_operation
msgid "L10N Mx Type Of Operation"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_partner__l10n_mx_type_of_third
#: model:ir.model.fields,field_description:l10n_mx_reports.field_res_users__l10n_mx_type_of_third
msgid "L10N Mx Type Of Third"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.actions.server,name:l10n_mx_reports.ir_cron_load_xsd_file_ir_actions_server
#: model:ir.cron,cron_name:l10n_mx_reports.ir_cron_load_xsd_file
#: model:ir.cron,name:l10n_mx_reports.ir_cron_load_xsd_file
msgid "Load XSD File (Mexican reports)"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model,name:l10n_mx_reports.model_l10n_mx_report_handler
msgid "Mexican Account Report Custom Handler"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.ui.menu,name:l10n_mx_reports.account_reports_legal_mexican_statements_menu
msgid "Mexico"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Missing Debit or Credit balance account tag in database."
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_partner_nationality
msgid "Nationality"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model.fields,help:l10n_mx_reports.field_res_partner__l10n_mx_nationality
#: model:ir.model.fields,help:l10n_mx_reports.field_res_users__l10n_mx_nationality
msgid ""
"Nationality based in the supplier country. Is the seventh column in DIOT "
"report"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Only Mexican company can generate SAT report."
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_paid_0
msgid "Paid 0%"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_paid_16
msgid "Paid 16%"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_paid_16_non_cred
msgid "Paid 16% - Non-Creditable"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_paid_8
msgid "Paid 8 %"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_paid_8_non_cred
msgid "Paid 8 % - Non-Creditable"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "Partner missing informations"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_refunds
msgid "Refunds"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "SAT (XML)"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "See the list of partners"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid "Show list"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid ""
"Some account prefixes used in your trial balance use both Debit and Credit "
"balance account tags. This is not allowed."
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/trial_balance.py:0
#, python-format
msgid ""
"Some accounts present in your trial balance don't have a Debit or a Credit "
"balance account tag."
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid ""
"The report cannot be generated because some partners are missing a valid RFC"
" or type of operation"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.ui.menu,name:l10n_mx_reports.menu_action_account_report_diot
msgid "Transactions with third parties [ DIOT ]"
msgstr ""

#. module: l10n_mx_reports
#: model:ir.model,name:l10n_mx_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_operation_type
msgid "Type of Operation"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_third_party
msgid "Type of Third"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_partner_vat_number
msgid "VAT"
msgstr ""

#. module: l10n_mx_reports
#: model:account.report.column,name:l10n_mx_reports.diot_report_withheld
msgid "Withheld"
msgstr ""

#. module: l10n_mx_reports
#: code:addons/l10n_mx_reports/models/account_diot.py:0
#, python-format
msgid "You can only export one period at a time with this file format!"
msgstr ""
