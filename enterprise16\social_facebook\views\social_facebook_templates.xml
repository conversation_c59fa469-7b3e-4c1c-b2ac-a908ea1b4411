<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <template id="facebook_preview" t-name="Facebook Preview">
        <div class="o_social_preview o_social_facebook_preview bg-white px-3 border rounded overflow-hidden">
            <div class="o_social_preview_header d-flex my-3">
                <span class="o_social_preview_icon_wrapper d-inline-block rounded-circle overflow-hidden me-2">
                    <i class="fa fa-facebook text-white fa-2x"/>
                </span>

                <div class="o_social_preview_author">
                    <b class="d-block mb-2">Facebook Page</b>
                    <div class="text-muted o_social_preview_description">
                        Published by Facebook Page •
                        <a t-if="live_post_link" t-attf-href="#{live_post_link}" target="blank"><time t-esc="published_date"/></a>
                        <t t-else=""><time t-esc="published_date"/></t> • <i class="fa fa-globe"/>
                    </div>
                </div>
            </div>
            <div class="o_social_preview_message pb-3">
                <t t-esc="message" />
            </div>
            <div class="o_social_stream_post_image d-flex mx-n3 overflow-hidden">
                <t t-foreach="images" t-as="image">
                    <a t-if="image_index == 1 and len(images) > 2" class="o_social_stream_post_image_more position-relative d-flex ms-1">
                        <img t-attf-src="data:image/png;base64, #{image}" alt="Post Image" />
                        <div class="o_social_stream_post_image_more_overlay d-flex align-items-center h-100 w-100 text-white justify-content-center position-absolute h1 m-0" style="user-select: none;">
                            +<t t-esc="len(images) - 2"/>
                        </div>
                    </a>
                    <img t-elif="image_index &lt; 2" t-attf-src="data:image/png;base64, #{image}" alt="Post Image"  />
                </t>
            </div>
        </div>
    </template>
</data>
</odoo>
