        # STEP 1: Find company-wide indirect costs (FIXED - Sum all entries)
        company_indirect_costs = {
            'equity_saving': 0,
            'warranty': 0,
            'rental': 0,
            'salary': 0
        }
        
        # Find equity saving cost (sum ALL entries, not just one)
        equity_saving_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'equity_saving_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in equity_saving_entries:
            equity_saving_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in equity_saving_lines:
                company_indirect_costs['equity_saving'] += line.debit
        
        # Find warranty cost (sum ALL entries, not just one)
        warranty_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'warranty_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in warranty_entries:
            warranty_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in warranty_lines:
                company_indirect_costs['warranty'] += line.debit
        
        # Find rental cost (sum ALL entries, not just one)
        rental_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'rental_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in rental_entries:
            rental_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in rental_lines:
                company_indirect_costs['rental'] += line.debit
        
        # Find salary cost (sum ALL entries, not just one)
        salary_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'salary_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in salary_entries:
            salary_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in salary_lines:
                company_indirect_costs['salary'] += line.debit 