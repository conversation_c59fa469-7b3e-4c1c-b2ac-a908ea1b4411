# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_xlsx
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-01 20:11+0000\n"
"PO-Revision-Date: 2021-03-16 12:45+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (https://www.transifex.com/oca/teams/23907/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: report_xlsx
#: code:addons/report_xlsx/models/ir_report.py:0
#, python-format
msgid "%s model was not found"
msgstr "%s modelo no fue encontrado"

#. module: report_xlsx
#. openerp-web
#: code:addons/report_xlsx/static/src/js/report/action_manager_report.js:0
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""
"Una ventana emergente con su informe fue bloqueada. Puede que necesite "
"cambiar las preferencias de su navegador para que permita ventanas "
"emergentes en esta página."

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_abstract
#, fuzzy
msgid "Abstract XLSX Report"
msgstr "Resumen Informe XLSX"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_abstract__display_name
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_partner_xlsx__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_abstract__id
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_partner_xlsx__id
msgid "ID"
msgstr "ID"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_abstract____last_update
#: model:ir.model.fields,field_description:report_xlsx.field_report_report_xlsx_partner_xlsx____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_partner_xlsx
msgid "Partner XLSX Report"
msgstr "Informe empresa XLSX"

#. module: report_xlsx
#: model:ir.actions.report,name:report_xlsx.partner_xlsx
msgid "Print to XLSX"
msgstr "Imprimir en XLSX"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_ir_actions_report
#, fuzzy
msgid "Report Action"
msgstr "Acción informe"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_ir_actions_report__report_type
msgid "Report Type"
msgstr "Tipo informe"

#. module: report_xlsx
#: model:ir.model.fields,help:report_xlsx.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""
"El tipo de informe que se representará, cada uno con su propio método de "
"representación. HTML significa que el informe se abrirá directamente en el "
"PDF de su navegador significa que el informe se representará usando "
"Wkhtmltopdf y el usuario lo descargará."

#. module: report_xlsx
#. openerp-web
#: code:addons/report_xlsx/static/src/js/report/action_manager_report.js:0
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: report_xlsx
#: model:ir.model.fields.selection,name:report_xlsx.selection__ir_actions_report__report_type__xlsx
msgid "XLSX"
msgstr "XLSX"

#, fuzzy
#~ msgid "ir.actions.report"
#~ msgstr "ir.actions.report.xml"
