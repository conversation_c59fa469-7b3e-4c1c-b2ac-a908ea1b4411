# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_enhancement
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-01 00:00+0000\n"
"PO-Revision-Date: 2023-01-01 00:00+0000\n"
"Last-Translator: \n"
"Language-Team: Arabic\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Generator: Odoo 16.0\n"

#. module: sale_enhancement
#: model:ir.model,name:sale_enhancement.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_enhancement
#: model:ir.model,name:sale_enhancement.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر البيع"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__total_discount
msgid "Total Discount"
msgstr "إجمالي الخصم"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__discount_type
msgid "Discount Type"
msgstr "نوع الخصم"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__discount_rate
msgid "Discount Rate"
msgstr "نسبة الخصم"

#. module: sale_enhancement
#: model:ir.model.fields.selection,name:sale_enhancement.selection__sale_order__discount_type__percentage
msgid "Percentage"
msgstr "نسبة مئوية"

#. module: sale_enhancement
#: model:ir.model.fields.selection,name:sale_enhancement.selection__sale_order__discount_type__amount
msgid "Amount"
msgstr "المبلغ"

#. module: sale_enhancement
#: model_terms:ir.ui.view,arch_db:sale_enhancement.view_order_form_inherit
msgid "Apply Discount"
msgstr "تطبيق الخصم"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "المبلغ غير الخاضع للضريبة"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__amount_tax
msgid "Taxes"
msgstr "الضرائب"

#. module: sale_enhancement
#: model:ir.model.fields,field_description:sale_enhancement.field_sale_order__amount_total
msgid "Total"
msgstr "الإجمالي" 