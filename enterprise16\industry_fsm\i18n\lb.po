# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 13:19+0000\n"
"PO-Revision-Date: 2019-08-26 09:36+0000\n"
"Last-Translator: <PERSON> ALT <<EMAIL>>, 2019\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "# Days to Deadline"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
msgid "# Working Days to Assign"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
msgid "# Working Days to Close"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid ""
"<span attrs=\"{'invisible': [('planned_date_end', '=', False)]}\"> to "
"</span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid ""
"<span attrs=\"{'invisible': [('timesheet_timer_last_stop', '=', False)]}\"> "
"to </span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "<span class=\"align-middle\">for this employee at the same time.</span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Add materials"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_invoice_fsm
msgid "All tasks were invoiced"
msgstr ""

#. module: industry_fsm
#: model:ir.model.constraint,message:industry_fsm.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Archived"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignation Date"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_id
msgid "Assigned To"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__user_id
msgid "Assigned to"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid "Choose Products"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Click Create to start a new task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Company"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "Konfiguratioun"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Create Invoice"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid "Create a new task"
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm.js:0
#, python-format
msgid "Create and plan your first Field Service Task"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_server_action_batch_invoice_fsm
msgid "Create invoice"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__currency_id
msgid "Currency"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
msgid "Customer"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid "Date"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Done"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_effective
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid "Effective Hours"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#: model:product.product,name:industry_fsm.field_service_product
#: model:product.template,name:industry_fsm.field_service_product_product_template
msgid "Field Service"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Group By"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
msgid "In Progress"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid "Invoice"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Invoices"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "Kanban State"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm____last_update
msgid "Last Modified on"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Launch the Timer"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "My Tasks"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_0
msgid "New"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm_quotation
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "New Quotation"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "No future Tasks planned"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_invoice_fsm
msgid "No task to Invoice"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "No task to schedule"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
msgid "Not validated"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_open
msgid "Number of Working Days to Open the task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_close
msgid "Number of Working Days to close the task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__invoice_count
msgid "Number of invoices"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid "Overlapping Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid "Pause"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_planned
msgid "Planned Hours"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Planned for Today"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__planning_overlap
msgid "Planning Overlap"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_product_product_kanban_material
msgid "Price:"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_product_product_kanban_material
msgid "Product"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Products"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__allow_material
msgid "Products on Tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Project"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Provide worksheet reports to be signed off by customers"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__quotation_count
msgid "Quotation Count"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_fsm_quotation
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form_quotation
msgid "Quotations"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
msgid "Ready for Next Stage"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
msgid "Remaining Hours"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Responsible"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
msgid "Resume"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Search planning"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__timesheet_product_id
msgid ""
"Select a Service product with which you would like to bill your time spent "
"on tasks."
msgstr ""

#. module: industry_fsm
#: model:product.product,name:industry_fsm.fsm_time_product
#: model:product.template,name:industry_fsm.fsm_time_product_product_template
msgid "Service on Timesheet"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Stage"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Start"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Start Date"
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm.js:0
#, python-format
msgid "Start to manage your Onsite Intervention with the Field Service app."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Stop"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Stop the Timer"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_sale_order__task_id
msgid "Task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
msgid "Task Done"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm
msgid "Task Title"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Tasks Analysis"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid "The FSM task must have a customer set to be sold."
msgstr ""

#. module: industry_fsm
#: model:ir.model.constraint,message:industry_fsm.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""

#. module: industry_fsm
#: model:ir.model.constraint,message:industry_fsm.constraint_project_project_timesheet_product_required_if_billable_and_timesheets
msgid ""
"The timesheet product is required when the task can be billed and timesheets"
" are allowed."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid "There are currently no tasks, click on create to create one"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#, python-format
msgid "This action is only allowed on FSM project."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid ""
"This report allows you to analyse the performance of your projects and "
"users. You can analyse the quantities of tasks, the hours spent compared to "
"the planned hours, the average number of days to open or close a task, etc."
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__timesheet_product_id
msgid "Timesheet Product"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Do"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Invoice"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
msgid "To Schedule"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_to_invoice
msgid "To invoice"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To schedule"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr ""

#. module: industry_fsm
#: model:product.product,uom_name:industry_fsm.field_service_product
#: model:product.product,uom_name:industry_fsm.fsm_time_product
#: model:product.template,uom_name:industry_fsm.field_service_product_product_template
#: model:product.template,uom_name:industry_fsm.fsm_time_product_product_template
msgid "Units"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Worksheet Templates"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "You can create one for yourself by clicking on Create."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "other tasks"
msgstr ""
