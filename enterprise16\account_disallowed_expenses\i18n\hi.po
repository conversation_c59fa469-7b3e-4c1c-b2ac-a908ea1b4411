# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.disallowed_expenses_main_template
msgid ""
"<span>There are multiple disallowed expenses rates in this period</span>"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_account
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__account_ids
msgid "Account"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Active"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.actions.act_window,help:account_disallowed_expenses.action_account_disallowed_expenses_category_list
msgid "Add a Disallowed Expenses Category"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__category_id
msgid "Category"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Category Name"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__code
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Code"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__company_id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__company_id
msgid "Company"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__current_rate
msgid "Current Rate"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__rate
msgid "Disallowed %"
msgstr ""

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_disallowed_amount
msgid "Disallowed Amount"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_report_de
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_form
msgid "Disallowed Expenses"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.actions.act_window,name:account_disallowed_expenses.action_account_disallowed_expenses_category_list
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_disallowed_expenses_category_list
msgid "Disallowed Expenses Categories"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_category
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_account__disallowed_expenses_category_id
msgid "Disallowed Expenses Category"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_report_handler
msgid "Disallowed Expenses Custom Handler"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_rate
msgid "Disallowed Expenses Rate"
msgstr ""

#. module: account_disallowed_expenses
#: model:account.report,name:account_disallowed_expenses.disallowed_expenses_report
#: model:ir.actions.client,name:account_disallowed_expenses.action_account_report_de
msgid "Disallowed Expenses Report"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.constraint,message:account_disallowed_expenses.constraint_account_disallowed_expenses_category_unique_code_in_country
msgid "Disallowed expenses category code should be unique in each company."
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__display_name
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr ""

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "General Ledger"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__id
msgid "ID"
msgstr ""

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Journal Items"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category____last_update
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__name
msgid "Name"
msgstr ""

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#, python-format
msgid "No Rate"
msgstr ""

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_rate
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__rate_ids
msgid "Rate"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Rates"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Related Account(s)"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Set Rates"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,help:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Set active to false to hide the category without removing it."
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr ""

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Total"
msgstr ""

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_total_amount
msgid "Total Amount"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. 1201"
msgstr ""

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. Non-Deductible Tax"
msgstr ""
