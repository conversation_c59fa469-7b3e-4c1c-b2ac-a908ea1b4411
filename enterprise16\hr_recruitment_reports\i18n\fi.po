# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_reports
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <ka<PERSON>.<PERSON><PERSON>@emsystems.fi>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__count
msgid "# Applicant"
msgstr "# Hakija"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hired
msgid "# Hired"
msgstr "# Palkattu"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hiring_ratio
msgid "# Hired Ratio"
msgstr "# Palkattujen suhde"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__meetings_amount
msgid "# Meetings"
msgstr "Tapaamisten lkm"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refused
msgid "# Refused"
msgstr "Hylätty"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Analysis"
msgstr "Analyysi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__applicant_id
msgid "Applicant"
msgstr "Hakija"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Applicant Name"
msgstr "Hakijan nimi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__company_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Company"
msgstr "Yritys"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_uid
msgid "Creator"
msgstr "Kirjoittaja"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__days_in_stage
msgid "Days In Stage"
msgstr "Päivät tilassa"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__display_name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__date_closed
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_end
msgid "End Date"
msgstr "Päättymispäivä"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__is_hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__is_hired
msgid "Hired"
msgstr "Palkattu"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Hiring ratio"
msgstr "Palkkaussuhde"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__in_progress
msgid "In Progress"
msgstr "Käynnissä"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__job_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__job_id
msgid "Job"
msgstr "Tehtävä"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Job Position"
msgstr "Tehtävänimike"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Last 365 Days Applicant"
msgstr "Viimeiset 365 päivää Hakija"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report____last_update
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__medium_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Medium"
msgstr "Media"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__process_duration
msgid "Process Duration"
msgstr "Prosessin kesto"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__user_id
msgid "Recruiter"
msgstr "Rekrytoija"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Recruitment Analysis"
msgstr "Rekrytoinnin analyysi"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "Rekrytoinnin analyysiraportti"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_stage_report
msgid "Recruitment Stage Analysis"
msgstr "Rekrytointivaiheen analyysi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refuse_reason_id
msgid "Refuse Reason"
msgstr "Kieltäytyminen syy"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__refused
msgid "Refused"
msgstr "Hylätty"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__source_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Source"
msgstr "Lähde"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_source_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_team_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_source_pivot
msgid "Source Analysis"
msgstr "Lähdeanalyysi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__stage_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Stage"
msgstr "Vaihe"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_date
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_begin
msgid "Start Date"
msgstr "Alkupäivä"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__state
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__state
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "State"
msgstr "Tila"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_team_action
msgid "Team Performance"
msgstr "Joukkueen suorituskyky"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
msgid "This report performs analysis on your recruitment source."
msgstr "Tämä raportti suorittaa analyysin rekrytointilähteestäsi."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid "This report performs analysis on your recruitment."
msgstr "Tämä raportti analysoi rekrytointiasi."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
msgid "This report performs analysis on your teams' performance."
msgstr "Tämä raportti analysoi tiimiesi suorituskykyä."

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Time By Stages"
msgstr "Aika vaiheiden mukaan"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Time In Stage Analysis"
msgstr "Aika vaiheessa Analyysi"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total Hired"
msgstr "Palkatut yhteensä"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total Meetings"
msgstr "Kokoukset yhteensä"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total applicants"
msgstr "Hakijat yhteensä"
