# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_ebay
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" If you want to set quantity to 0, the Out Of Stock option should be enabled"
" and the listing duration should set to Good 'Til Canceled"
msgstr ""
" 如果你设置数量为0,需启用缺货选项，清单时间应该被设为货物直到取消\n"
"如果要将数量设置为0，则应启用缺货选项，并且设置列表持续时间直到取消"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" You need to have at least 2 variations selected for a multi-variations listing.\n"
" Or if you try to delete a variation, you cannot do it by unselecting it. Setting the quantity to 0 is the safest method to make a variation unavailable."
msgstr ""
" 你必须至少选择2个产品变体。对于多产品变体清单.否则如果你尝试删除一个产品变体，你将不能选择它。\n"
"设置数量为0，是使一个产品变体无效的安全方法。"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_10
msgid "10 Days"
msgstr "10 天"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_3
msgid "3 Days"
msgstr "3 天"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_30
msgid "30 Days (only for fixed price)"
msgstr "30天 (仅固定价格)"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_5
msgid "5 Days"
msgstr "5天"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_7
msgid "7 Days"
msgstr "7天"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr "<span class=\"o_form_label\">销售团队</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Storage</span>"
msgstr "<span class=\"o_form_label\">存储</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Synchronization</span>"
msgstr "<span class=\"o_form_label\">同步</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Account</span>"
msgstr "<span class=\"o_form_label\">eBay 账号</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">eBay Marketplace Account Deletion/Closure "
"Notifications</span>"
msgstr "<span class=\"o_form_label\">eBay市场账户的删除/关闭通知</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Options</span>"
msgstr "<span class=\"o_form_label\">eBay 选项</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "启用其他货币"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Add other countries"
msgstr "添加其他国家"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"All the quantities must be greater than 0 or you need to enable the Out Of "
"Stock option."
msgstr "所有的数量必须大于0或激活缺货选项。"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_best_offer
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_best_offer
msgid "Allow Best Offer"
msgstr "允许议价"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__chinese
msgid "Auction"
msgstr "拍卖"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_buy_it_now_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_buy_it_now_price
msgid "Buy It Now Price"
msgstr "现买价"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Cancel"
msgstr "取消"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_id
msgid "Category"
msgstr "类别"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_2_id
msgid "Category 2 (Optional)"
msgstr "类别2(可选)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_id
msgid "Category ID"
msgstr "类别 ID"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_parent_id
msgid "Category Parent ID"
msgstr "上级分类 ID"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_type
msgid "Category Type"
msgstr "类别类型"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__code
msgid "Code"
msgstr "代号"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Configure The eBay Integrator Now"
msgstr "现在配置eBay集成"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_country_all_form
msgid "Countries"
msgstr "国家"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Countries & Currencies"
msgstr "国家和货币"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_country
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_country
msgid "Country"
msgstr "国家"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_uid
msgid "Created by"
msgstr "创建人"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_date
msgid "Created on"
msgstr "创建时间"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_currency
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Currency"
msgstr "币种"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__store
msgid "Custom Store Category"
msgstr "自定义店铺类别"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Describe the product characteristics..."
msgstr "说明产品特性..."

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_template_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_template_id
msgid "Description Template"
msgstr "说明模板"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_dev_id
msgid "Developer Key"
msgstr "开发者密钥"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Documentation"
msgstr "文档"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_duration
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_duration
msgid "Duration"
msgstr "时长"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay Synchronisation could not confirm because of the following error:\n"
"%s"
msgstr ""
"由于以下错误，Ebay同步无法确认：\n"
"%s"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay could not synchronize order:\n"
"%s"
msgstr ""
"易趣无法同步订单：\n"
"%s"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_orders_sync
msgid "Ebay: get new orders"
msgstr "Ebay: 获得新订单"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_recovery
msgid "Ebay: orders recovery"
msgstr "Ebay: 订单恢复"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_stock_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_stock_sync
msgid "Ebay: synchronise stock"
msgstr "Ebay：同步库存"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_categories_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_categories
msgid "Ebay: update categories"
msgstr "易趣网：更新类别"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "End Item's Listing"
msgstr "终止物品的上市"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
" No Variant Set To Be Listed On eBay."
msgstr ""
"遇到问题.\n"
" eBay 上的产品没有设置产品变体."

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
"'%s'"
msgstr ""
"遇到问题.\n"
"'%s'"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Fixed Price"
msgstr "固定价格"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__fixedpriceitem
msgid "Fixed price"
msgstr "固定价格"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__full_name
msgid "Full Name"
msgstr "完整名称"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_gallery_plus
msgid "Gallery Plus"
msgstr "画廊加"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Generate Token"
msgstr "生成令牌"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__gtc
msgid "Good 'Til Cancelled (only for fixed price)"
msgstr "货物直到取消(仅对固定价)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__id
msgid "ID"
msgstr "ID"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,help:sale_ebay.field_res_currency__ebay_available
msgid "If activated, can be used for eBay."
msgstr "如果激活，可用于eBay。"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Import eBay config data and sync transactions"
msgstr "导入易趣网配置数据和同步事务"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Impossible to revise a listing into a multi-variations listing.\n"
" Create a new listing."
msgstr ""
"不能把一个产品修改成多产品变体产品.\n"
" 创建一个新产品."

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_item_condition_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_item_condition_id
msgid "Item Condition"
msgstr "物品条件"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_last_sync
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_last_sync
msgid "Last update"
msgstr "最近更新"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__leaf_category
msgid "Leaf Category"
msgstr "叶类"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link Existing Listing"
msgstr "链接已发布产品"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Link With Existing eBay Listing"
msgstr "链接现有的易趣网上列表"

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_ebay_link_listing
msgid "Link with Existing eBay Listing"
msgstr "链接现有的易趣网上列表"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link with eBay Listing"
msgstr "与易趣网上列表链接"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "List Item on eBay"
msgstr "易趣网上的列表项目"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_type
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_type
msgid "Listing Type"
msgstr "上市类型"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_location
msgid "Location"
msgstr "位置"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_account_deletion_endpoint
msgid "Marketplace account deletion notification endpoint"
msgstr "市场账户删除通知端点"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode"
msgstr "模式"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode and credentials"
msgstr "模式和证明"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__name
msgid "Name"
msgstr "名称"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid "No Business Policies"
msgstr "没有商业政策"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__ebay
msgid "Official eBay Category"
msgstr "官方eBay类别"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "One parameter is missing."
msgstr "缺少一个参数。"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Or the condition is not compatible with the category."
msgstr "或者条件与类别不相符。"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_out_of_stock
msgid "Out Of Stock"
msgstr "缺货"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_payment_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_payment_policy_id
msgid "Payment Policy"
msgstr "支付政策"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"Please provide your ebay production keys before enabling the account "
"deletion notifications."
msgstr "在启用账户删除通知之前，请提供您的Ebay生产环境密钥。"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Policies"
msgstr "政策"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_id
msgid "Policy ID"
msgstr "政策ID"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_private_listing
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_private_listing
msgid "Private Listing"
msgstr "私有上市"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_template
msgid "Product"
msgstr "产品"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Product Categories"
msgstr "产品类别"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "Product created from eBay transaction %s"
msgstr "从eBay交易创建的产品%s"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__prod
msgid "Production"
msgstr "生产"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_app_id
msgid "Production App Key"
msgstr "正式应用密钥"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_cert_id
msgid "Production Cert Key"
msgstr "正式证书密钥"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_token
msgid "Production Token"
msgstr "生产令牌"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_use
msgid "Publish On eBay"
msgstr "在eBay发布"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity
msgid "Quantity On eBay"
msgstr "在 eBay上的数量"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity_sold
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity_sold
msgid "Quantity Sold"
msgstr "卖出数量"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Relist Item"
msgstr "重新上市物品"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_return_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_return_policy_id
msgid "Return Policy"
msgstr "退货政策"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Revise Item"
msgstr "修订物品"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team"
msgstr "销售团队"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team to manage eBay sales"
msgstr "销售团队管理eBay销售"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__sand
msgid "Sandbox"
msgstr "沙箱"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_app_id
msgid "Sandbox App Key"
msgstr "沙箱应用密钥"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_cert_id
msgid "Sandbox Cert Key"
msgstr "沙箱证书密钥"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_token
msgid "Sandbox Token"
msgstr "沙箱令牌"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Sell on eBay"
msgstr "在eBay上销售"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_shipping_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_shipping_policy_id
msgid "Shipping Policy"
msgstr "送货政策"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_start_date
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_start_date
msgid "Start Date"
msgstr "开始日期"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_price
msgid "Starting Price for Auction"
msgstr "拍卖起始价"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Storage location of your products"
msgstr "您产品的存储位置"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_id
msgid "Store Category (Optional)"
msgstr "店铺类别(可选)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_2_id
msgid "Store Category 2 (Optional)"
msgstr "店铺类别2(可选)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_subtitle
msgid "Subtitle"
msgstr "小标题"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__short_summary
msgid "Summary"
msgstr "摘要"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sync now"
msgstr "现在同步"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Chose The Following Delivery Method :\n"
msgstr "购买者选择了如下送货方式 :\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Posted :\n"
msgstr "买方发布 :\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"The python 'cryptography' module is not installed on your server.\n"
"It is necessary to support eBay account deletion notifications, please contact your system administrator to install it."
msgstr ""
"你的服务器上没有安装python的 “cryptography \"模块。\n"
"它是支持eBay账户删除通知的必要条件，请联系你的系统管理员安装它。"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_subtitle
msgid ""
"The subtitle is restricted to 55 characters. Fees can be claimed by eBay for"
" this feature"
msgstr "子标题限制55个字符.对于此功能eBAY 要收费"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_title
msgid "The title is restricted to 80 characters"
msgstr "标题支持80个字符"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_category_2_id
msgid ""
"The use of a secondary category is not allowed on every eBay sites. Fees can"
" be claimed by eBay for this feature"
msgstr "每个eBay网站都不允许使用二级分类。eBay可以为这项功能收取费用"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"There is no last synchronization date in your System Parameters. Create a "
"System Parameter record with the key \"ebay_last_sync\" and the value set to"
" the date of the oldest order you wish to synchronize in the format \"YYYY-"
"MM-DD\"."
msgstr ""
"你的系统参数中没有上次同步日期。使用键“ebay_last_sync”创建一个系统参数记录，并将值设置为你希望以“YYYY-MM-"
"DD”格式同步的最早订单的日期。"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"This function should not be called with a range of more than 30 days, as "
"eBay does not handle longer timespans. Instead use synchronize_orders which "
"split in as many calls as needed."
msgstr ""
"此函数应在30天内调用，因为eBay无法处理更长的时间跨度。或者，可以使用synchronize_orders，根据需要拆分为任意多个调用。"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_partner.py:0
#, python-format
msgid ""
"This is an automated notification as a deletion request has been received from eBay concerning the account \"%s (%s)\". The account has been anonymised already and his portal access revoked (if they had any).\n"
"\n"
"However, personal information might remain in linked documents, please review them according to laws that apply."
msgstr ""
"这是一个自动通知，因为收到了eBay关于\"%s（%s）\"账户的删除请求。该账户已被匿名化，其门户访问权也被撤销（如果他们有的话）。\n"
"\n"
"然而，个人信息可能会保留在链接文件中，请根据应用的法律审查这些文件。"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_title
msgid "Title"
msgstr "称谓"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_stock_picking
msgid "Transfer"
msgstr "调拨"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_type
msgid "Type"
msgstr "类型"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_sync_stock
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_sync_stock
msgid "Use Stock Quantity"
msgstr "使用库存数量"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_use
msgid "Use eBay"
msgstr "使用eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,field_description:sale_ebay.field_res_currency__ebay_available
msgid "Use on eBay"
msgstr "在eBay上使用"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Variants"
msgstr "变体"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_verification_token
msgid "Verification Token"
msgstr "验证令牌"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_zip_code
msgid "Zip"
msgstr "邮政编码"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay"
msgstr "eBay"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_category
msgid "eBay Category"
msgstr "eBay 类别"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_description
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_description
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "eBay Description"
msgstr "易趣网描述"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_domain
msgid "eBay Environment"
msgstr "eBay 环境"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_fixed_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_fixed_price
msgid "eBay Fixed Price"
msgstr "eBay 固定价格"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_id
msgid "eBay ID"
msgstr "eBay ID"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_item_condition
msgid "eBay Item Condition"
msgstr "eBay 项目条件"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_link_listing
msgid "eBay Link Listing"
msgstr "eBay 链接列表"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__ebay_id
msgid "eBay Listing ID"
msgstr "eBay上市 ID"

#. module: sale_ebay
#: model:mail.activity.type,summary:sale_ebay.ebay_GDPR_notification
msgid "eBay Odoo connector notification - account deletion"
msgstr "eBay Odoo连接器通知--账户删除"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_policy
msgid "eBay Policy"
msgstr "eBay 政策"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_site
msgid "eBay Site"
msgstr "eBay网站"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_status
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_status
msgid "eBay Status"
msgstr "eBay 状态"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_partner__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_res_users__ebay_id
msgid "eBay User ID"
msgstr "eBay 用户 ID"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_variant_url
msgid "eBay Variant URL"
msgstr "eBay 产品变体网址"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_site
msgid "eBay Website"
msgstr "易趣网的网站"

#. module: sale_ebay
#: model:mail.activity.type,name:sale_ebay.ebay_GDPR_notification
msgid "eBay connector: account deletion notification"
msgstr "eBay连接器：账户删除通知"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay documentation"
msgstr "eBay文件"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid ""
"eBay error: Impossible to synchronize the categories. \n"
"'%s'"
msgstr ""
"eBay 错误:不能同步种类\n"
"'%s'"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "eBay is unreachable. Please try again later."
msgstr "eBay 无法访问. 请再试."

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay parameters"
msgstr "eBay 参数"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"eBay requires supporting customer account deletion/closure notifications.\n"
"                                Please follow the"
msgstr ""
"eBay要求支持客户账户的删除/关闭通知。\n"
"                                请按照"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_url
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_url
msgid "eBay url"
msgstr "eBay 网址"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_end_items_listings
msgid "eBay: End product listings"
msgstr "eBay: 最终产品列表"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_list_items
msgid "eBay: List products"
msgstr "eBay: 产品上市"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_relist_items
msgid "eBay: Relist products"
msgstr "eBay: 修改产品"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_revise_items
msgid "eBay: Revise products"
msgstr "eBay: 修改产品"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_unlink_items_listings
msgid "eBay: Unlink product listings"
msgstr "eBay: 断开产品列表"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_ebay_update_carrier
msgid "eBay: Update carrier information"
msgstr "eBay: 更新承运商信息"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_currency
msgid "ebay Currency"
msgstr "ebay 货币"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sales_team
msgid "ebay Sales Team"
msgstr "ebay 销售团队"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "to setup this mechanism."
msgstr "到设置这个机制。"

#. module: sale_ebay
#: code:addons/sale_ebay/tools/ebaysdk.py:0
#, python-format
msgid ""
"An unexpected error occured from eBay.\n"
"Please check your credentials and try again later."
msgstr ""
"eBay 出现意外错误。\n"
"请检查您的证书，并稍后重试。"
