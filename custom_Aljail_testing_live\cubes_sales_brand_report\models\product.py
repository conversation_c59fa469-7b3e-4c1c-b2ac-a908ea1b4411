# -*- coding: utf-8 -*-
import datetime
from dateutil.relativedelta import relativedelta

from odoo import api, models, fields
from odoo.tools.misc import formatLang


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    brand_id = fields.Many2one('product.tag', string="Brand")
    months_qty_delivered = fields.Text(string="Months Delivered Qty", compute="_compute_months_qty_delivered")

    def _compute_months_qty_delivered(self):
        for rec in self:
            rec.months_qty_delivered = ''
            obj_months = []
            obj = []
            if self.env.context.get('months_array'):
                index = 0
                for month in self.env.context.get('months_array'):
                    obj_months.append(month.strftime("%b %Y"))
                    # from_date = month
                    from_date = month.replace(day=1)
                    to_date = month + relativedelta(months=1) - relativedelta(days=month.day)
                    if index > 0:
                        # from_date = from_date - relativedelta(days=from_date.day - 1)
                        from_date = from_date.replace(day=1)
                    if index == len(self.env.context.get('months_array')) - 1:
                        # calculate last day
                        to_date = (month + relativedelta(months=1)).replace(day=1) - relativedelta(days=1)
                    domain = [
                        ('product_id', '=', rec.product_variant_id.id),
                        ('order_id.date_order', '>=', from_date),
                        ('order_id.date_order', '<=', to_date.replace(hour=23, minute=59, second=59))
                    ]
                    if self.env.context.get('branch_id'):
                        domain.append(('order_id.branch_id', '=', int(self.env.context.get('branch_id'))))
                    order_lines = self.env['sale.order.line'].sudo().search(domain)
                    obj.append(str(sum(order_lines.mapped('qty_delivered'))))
                    index += 1
            if obj_months and obj:
                rec.months_qty_delivered = ','.join(obj) + '-' + ','.join(obj_months)


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    brand_id = fields.Many2one(comodel_name="product.tag", related='product_id.brand_id', store=True)
