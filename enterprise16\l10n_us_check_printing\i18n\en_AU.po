# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_us_check_printing
#
# Translators:
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-01-29 15:26+0000\n"
"PO-Revision-Date: 2021-04-22 13:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_us_check_printing
#: model:ir.actions.report,print_report_name:l10n_us_check_printing.action_print_check_bottom
msgid "'Check Bottom - %s' % (object.partner_id.name or '',)"
msgstr "'Cheque Bottom - %s' % (object.partner_id.name or '',)"

#. module: l10n_us_check_printing
#: model:ir.actions.report,print_report_name:l10n_us_check_printing.action_print_check_middle
msgid "'Check Middle - %s' % (object.partner_id.name or '',)"
msgstr "'Cheque Middle - %s' % (object.partner_id.name or '',)"

#. module: l10n_us_check_printing
#: model:ir.actions.report,print_report_name:l10n_us_check_printing.action_print_check_top
msgid "'Check Top - %s' % (object.partner_id.name or '',)"
msgstr "'Cheque Top - %s' % (object.partner_id.name or '',)"

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Balance Due"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Check Amount:"
msgstr "Cheque Amount:"

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Description"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Due Date"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Invoice Amount"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Payment"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model,name:l10n_us_check_printing.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_bottom
msgid "Print Check (Bottom)"
msgstr "Print Cheque (Bottom)"

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_middle
msgid "Print Check (Middle)"
msgstr "Print Cheque (Middle)"

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_top
msgid "Print Check (Top)"
msgstr "Print Cheque (Top)"

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_check
msgid "VOID"
msgstr ""
