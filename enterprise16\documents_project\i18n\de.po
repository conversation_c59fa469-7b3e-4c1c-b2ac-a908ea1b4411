# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:41+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid "%(project_name)s's Documents"
msgstr "Dokumente von %(project_name)s"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Eine Reihe an Bedingungen und Aktionen die für alle Anhänge verfügbar sind, "
"die den Bedingungen entprechen"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_ask_for_validation
msgid "Ask for Validation"
msgstr "Validierung anfragen"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Dateianhang"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Kategorisieren und teilen Sie Ihre Dokumente mit Ihren Kunden."

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_facet
msgid "Category"
msgstr "Kategorie"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Erstellen"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Eine Aufgabe erstellen"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Standard-Stichwörter"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_deprecate
msgid "Deprecate"
msgstr "Nicht mehr verwenden"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_deprecated
msgid "Deprecated"
msgstr "Nicht mehr verwendet"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
#: model:ir.model.fields,field_description:documents_project.field_ir_attachment__document_ids
msgid "Document"
msgstr "Dokument"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
#, python-format
msgid "Documents"
msgstr "Dokumente"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Dokumente &amp; Analysen"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_folder
msgid "Documents Workspace"
msgstr "Arbeitsbereich „Dokumente“"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_draft
msgid "Draft"
msgstr "Entwurf"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__is_shared
msgid "Is Shared"
msgstr "Ist geteilt"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_mark_as_draft
msgid "Mark As Draft"
msgstr "Als Entwurf markieren"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "Merged Workspace"
msgstr "Zusammengeführter Arbeitsbereich"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "New task from Documents"
msgstr "Neue Aufgabe aus Dokumenten"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__document_count
msgid "Number of documents in Project"
msgstr "Anzahl Dokumente in Projekt"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_count
msgid "Number of documents in Task"
msgstr "Anzahl Dokumente in Aufgabe"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_documents_folder__project_ids
msgid "Project"
msgstr "Projekt"

#. module: documents_project
#: model:documents.folder,name:documents_project.documents_project_folder
msgid "Projects"
msgstr "Projekte"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_ids
#: model:ir.model.fields,field_description:documents_project.field_project_task__shared_document_ids
msgid "Shared Documents"
msgstr "Geteilte Dokumente"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_count
msgid "Shared Documents Count"
msgstr "Anzahl geteilter Dokumente"

#. module: documents_project
#: model:documents.facet,name:documents_project.documents_project_status
msgid "Status"
msgstr "Status"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Aufgabe"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "Task created from document"
msgstr "Aufgabe aus Dokument erstellt"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr ""
"Der Arbeitsbereich „%s“ ist für die Projekte-App erforderlich und kann nicht"
" gelöscht werden."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"The \"%s\" workspace should either be in the \"%s\" company like this "
"project or be open to all companies."
msgstr ""
"Der Arbeitsbereich „%s“ sollte sich entweder im Unternehmen „%s“ befinden, "
"wie dieses Projekt, oder für alle Unternehmen zugänglich sein."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the \"%s\" project to "
"which it is linked. Please update the company of the \"%s\" project, or "
"leave the company of this workspace empty."
msgstr ""
"Dieser Arbeitsbereich sollte im selben Unternehmen bleiben wie das Projekt "
"„%s“, mit dem er verknüpft ist. Bitte aktualisieren Sie das Unternehmen des "
"Projekts „%s“ oder lassen Sie das Unternehmen dieses Arbeitsbereichs leer."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Dieser Arbeitsbereich sollte im selben Unternehmen bleiben wie das folgende Projekt, mit dem er verknüpft ist:\n"
"%s\n"
"\n"
"Bitte aktualisieren Sie das Unternehmen der Projekte oder lassen Sie das Unternehmen dieses Arbeitsbereichs leer."

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_to_validate
msgid "To Validate"
msgstr "Zu validieren"

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"<span class=\"fw-normal\">Eine Datei</span> hochladen oder <span class=\"fw-"
"normal\">hier</span> ablegen"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__project_use_documents
msgid "Use Documents"
msgstr "Dokumente verwenden"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_validate
msgid "Validate"
msgstr "Validieren"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_validated
msgid "Validated"
msgstr "Validiert"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,field_description:documents_project.field_project_task__documents_folder_id
msgid "Workspace"
msgstr "Arbeitsbereich"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Workspace in which all of the documents of this project will be categorized."
" All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Arbeitsbereich, in dem alle Dokumente dieses Projekts kategorisiert werden. "
"Alle Anhänge Ihrer Aufgaben werden auch automatisch als Dokumente in diesen "
"Arbeitsbereich eingefügt."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%s\" company:\n"
"%s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%s\" workspace blank."
msgstr ""
"Sie können das Unternehmen dieses Projekts nicht ändern, weil der Arbeitsbereich mit den anderen folgenden Projekten verknüpft ist, die sich noch im Unternehmen „%s“ befinden:\n"
"%s\n"
"\n"
"Bitte aktualisieren Sie das Unternehmen aller Projekte, sodass sie im selben Unternehmen wie ihr Arbeitsbereich bleiben, oder lassen Sie das Unternehmen des Arbeitsbereichs „%s“ leer."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "You cannot set a company on the Projects workspace."
msgstr "Sie können kein Unternehmen im Projekte-Arbeitsbereich einstellen."

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.share_page
msgid "documents shared"
msgstr "geteilte Dokumente"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "new %s from %s"
msgstr "neue %s aus %s"
