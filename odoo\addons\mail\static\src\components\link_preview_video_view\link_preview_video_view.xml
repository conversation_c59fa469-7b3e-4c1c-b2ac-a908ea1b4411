<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.LinkPreviewVideoView" owl="1">
        <div class="o_LinkPreview o_LinkPreviewVideoView card position-relative w-100 mb-2 rounded bg-view shadow-sm overflow-hidden" t-att-class="{ 'me-2': !linkPreviewVideoView.linkPreviewListViewOwner.messageViewOwner.isInChatWindow }" t-attf-class="{{ className }}" t-on-mouseenter="linkPreviewVideoView.onMouseEnter" t-on-mouseleave="linkPreviewVideoView.onMouseLeave" t-ref="root">
            <div class="row g-0 flex-row-reverse h-100">
                <div class="col min-w-0" t-att-class="{ 'd-flex align-items-center': !linkPreviewVideoView.linkPreview.og_description }">
                    <div class="o_LinkPreviewVideoView_content p-3 bg-view">
                        <h6 class="o_LinkPreviewVideoView_title card-title mb-0 me-2" t-attf-class="{{ linkPreviewVideoView.linkPreview.og_description ? 'text-truncate' : 'o_LinkPreviewVideoView_hasMultineLines overflow-hidden' }}">
                            <a t-att-href="linkPreviewVideoView.linkPreview.source_url" target="_blank" t-esc="linkPreviewVideoView.linkPreview.og_title"/>
                        </h6>
                        <p t-if="linkPreviewVideoView.linkPreview.og_description" class="o_LinkPreviewVideoView_description o_LinkPreviewVideoView_hasMultineLines card-text mb-0 mt-2 text-muted small overflow-hidden" t-out="linkPreviewVideoView.linkPreview.og_description"></p>
                    </div>
                </div>
                <div class="o_LinkPreviewVideoView_imageLinkWrap col-4 d-block">
                    <a t-att-href="linkPreviewVideoView.linkPreview.source_url" class="o_LinkPreviewVideoView_imageLink" target="_blank">
                        <div t-if="linkPreviewVideoView.linkPreview.og_image" class="o_linkPreviewVideo_overlay position-relative h-100 opacity-trigger-hover">
                            <img class="o_LinkPreviewVideoView_image img-fluid h-100 rounded-bottom o_object_fit_cover" t-att-src="linkPreviewVideoView.linkPreview.og_image" t-att-alt="linkPreviewVideoView.linkPreview.og_title"/>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <div class="o_LinkPreviewVideoView_playBtn btn btn-lg rounded opacity-75 opacity-100-hover transition-base">
                                    <i class="fa fa-play"/>
                                </div>
                            </div>
                        </div>
                        <span t-else="" class="o_LinkPreviewVideoView_imageEmpty d-flex align-items-center justify-content-center w-100 h-100 bg-100 text-300">
                            <i class="fa fa-camera fa-2x"/>
                        </span>
                    </a>
                </div>
            </div>
            <div t-if="linkPreviewVideoView.linkPreviewAsideView" class="o_LinkPreviewVideoView_aside position-absolute top-0 end-0 small">
                <LinkPreviewAsideView className="'fa fa-stack p-0 opacity-75 opacity-100-hover'" record="linkPreviewVideoView.linkPreviewAsideView"/>
            </div>
        </div>
    </t>
</templates>
