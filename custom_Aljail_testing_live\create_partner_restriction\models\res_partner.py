from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ResPartner(models.Model):
    _inherit = "res.partner"

    @api.model
    def create(self, vals):
        if self.env.user.has_group('create_partner_restriction.restrict_partner_creation'):
            raise ValidationError(_("You don't have access to create partner."))
        return super(<PERSON>s<PERSON><PERSON><PERSON>, self).create(vals)
