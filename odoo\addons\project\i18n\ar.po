# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON><PERSON> <o<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <must<PERSON><PERSON>@cubexco.com>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                     لم يتم العثور على أي مؤشرات للتقدم!\n"
"                </p><p>\n"
"                    قم بتتبع نقاط التقدم الأساسية التي يجب الوصول إليها لتحقيق النجاح.\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_calendar/project_calendar_controller.js:0
#, python-format
msgid " - Tasks by Deadline"
msgstr "- المهام حسب الموعد النهائي "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "عدد المتعاونين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "عدد التقييمات "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "عدد المهام"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
#, python-format
msgid "# of Tasks"
msgstr "عدد المهام"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "#{record.commercial_partner_id.value}"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} مؤشرات التقدم التي قد تم الوصول "
"إليها من # {record.milestone_count.value}"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "#{record.partner_id.value}"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Burndown Chart"
msgstr "مخطط توقُّف %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Milestones"
msgstr "مؤشرات تقدم %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Rating"
msgstr "تقييم %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Tasks Analysis"
msgstr "تحليل مهام %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Updates"
msgstr "تحديثات %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ %(child_count)s tasks)"
msgstr "(+ مهام %(child_count)s) "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ 1 task)"
msgstr "(+ 1 مهمة) "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(تاريخ الاستحقاق "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(آخر تحديث للمشروع)، "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- وصل في "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
".\n"
"      Attach all documents or links to the task directly, to have all research information centralized."
msgstr ""
".\n"
"      قم بربط كافة المستندات أو الروابط إلى المهام مباشرة، حتى تكون كافة معلومات البحث في مكان واحد. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__10
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__10
msgid "10"
msgstr "10"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__11
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__11
msgid "11"
msgstr "11"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__12
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__12
msgid "12"
msgstr "12"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__13
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__13
msgid "13"
msgstr "13"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__14
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__14
msgid "14"
msgstr "14"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__15
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__15
msgid "15"
msgstr "15"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__16
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__16
msgid "16"
msgstr "16"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__17
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__17
msgid "17"
msgstr "17"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__18
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__18
msgid "18"
msgstr "18"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__19
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__19
msgid "19"
msgstr "19"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__20
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__20
msgid "20"
msgstr "20"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__21
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__21
msgid "21"
msgstr "21"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__22
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__22
msgid "22"
msgstr "22"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__23
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__23
msgid "23"
msgstr "23"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__24
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__24
msgid "24"
msgstr "24"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__25
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__25
msgid "25"
msgstr "25"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__26
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__26
msgid "26"
msgstr "26"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__27
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__27
msgid "27"
msgstr "27"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__28
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__28
msgid "28"
msgstr "28"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__29
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__29
msgid "29"
msgstr "29"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__30
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__30
msgid "30"
msgstr "30"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__31
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__31
msgid "31"
msgstr "31"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>قم بسحب وإفلات</b> البطاقة لتغيير مرحلة مهمتك. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this task won't be notified \n"
"    of the note you are logging unless you specifically tag them)</i>. Use @ <b>mentions</b> to ping a colleague \n"
"    or # <b>mentions</b> to reach an entire team."
msgstr ""
"<b>قم بتسجيل الملاحظات</b> للتواصل الدااخلي <i>(لن يتم إخطار الأفراد المتابعين لهذه المهمة \n"
"    بالملاحظة التي تقوم بتسجيلها إلا إذا قمت بربطهم بشكل خاص)</i>. استخدم تذكيرات <b>@</b> لتنبيه زملاء العمل \n"
"    أو أو تذكيرات <b>#</b> لتصل إلى الفريق بأكمله. "

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br><br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the task \"<strong t-out=\"object.name or ''\">Planning and budget</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In progress</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">Weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                مرحباً <t t-out=\"partner.name or ''\">براندن فريمان</t>،<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                مرحباً،<br><br>\n"
"            </t>\n"
"            هلّا تفضلت ببضع دقائق من وقتك لتقييم خدماتنا المتعلقة بالمهمة \"<strong t-out=\"object.name or ''\">التخطيط والميزانية</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                المسندة إلى <strong t-out=\"object._rating_get_operator().name or ''\">ميتشل آدمن</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>أخبرنا عن رأيك عن خدمتنا</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(اضغط على إحدى تلك الوجوه المبتسمة)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            نحن نقدّر ملاحظاتك، حيث إنها تساعدنا على التحسن باستمرار.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">لقد تم إرسال استطلاع العميل لأنه قد تم نقل مهمتك إلى مرحلة <b t-out=\"object.stage_id.name or ''\">قيد التنفيذ</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">يتم إرسال استطلاع العميل <b t-out=\"object.project_id.rating_status_period or ''\">أسبوعياً</b> طالما أن المهمة في مرحلة <b t-out=\"object.stage_id.name or ''\">قيد التنفيذ</b>.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"    عزيزنا <t t-out=\"object.partner_id.name or 'customer'\">براندن فريمان</t>،<br>\n"
"    يسرنا إخبارك بأننا قد أتممنا المشروع \"<strong t-out=\"object.name or ''\">الترميمات</strong>\" بنجاح.\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">لقد وصلتك هذه الرسالة لأنه قد تم نقل مشروعك إلى مرحلة <b t-out=\"object.stage_id.name or ''\">تم الانتهاء</b></span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Thank you for your enquiry.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    عزيزنا <t t-out=\"object.partner_id.name or 'customer'\">براندن فريمان</t>،<br>\n"
"    شكراً لاستفسارك.<br>\n"
"    يرجى إعلامنا إذا كانت لديك أي أسئلة.\n"
"    <br><br>\n"
"    شكراً لك،\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"إدارة \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_avg', '&lt;', 3.66)]}\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_avg', '&lt;', 2.33), ('rating_avg', '&gt;=', 3.66)]}\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_avg', '&gt;=', 2.33)]}\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_avg', '<', 3.66)]}\" title=\"راضٍ \"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_avg', '<', 2.33), ('rating_avg', '>=', 3.66)]}\" title=\"لا بأس به \"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_avg', '>=', 2.33)]}\" title=\"غير راضٍ \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> خاص "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"سهم \"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"سهم \" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"تقييمات العملاء</b> معطلة في المشروع (المشاريع) التالية : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "<p><em>Number of tasks: %(tasks_count)s</em></p>"
msgstr "<p><em>عدد المهام: %(tasks_count)s</em></p>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">المرحلة:</small> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">of</span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">من</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"التواريخ \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"ألقاب النطاق \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا خاصة "
"بالشركة فقط. \" groups=\"base.group_multi_company\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"الشريك \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    المتعاونين\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Blocking</span>"
msgstr "<span class=\"o_stat_text\">الحجب</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">آخر تقييم</span> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text\">المهام الفرعية</span> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">المهام</span> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">in Recurrence</span>"
msgstr "<span class=\"o_stat_text\">في التكرار</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>إعداد التقارير</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>عرض</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">المرفقات</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_planned_hours_template
msgid "<strong>Allocated Hours:</strong>"
msgstr "<strong>الساعات المخصصة:</strong> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assignees</strong>"
msgstr "<strong>المسند إليهم</strong> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Customer</strong>"
msgstr "<strong>العميل</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>الموعد النهائي:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Description</strong>"
msgstr "<strong>الوصف:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>سجل الرسائل والتواصل</strong> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>مؤشر التقدم:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>المشروع:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>مؤشرات التقدم</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"قاموس بايثون الذي سيتم تقييمه لتوفير قيم افتراضية عند إنشاء سجلات جديدة لهذا"
" اللقب. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"لا يمكن تحديد المتعاون أكثر من مرة في وصول مشاركة المشروع. يرجى إزالة النسخة"
" (النسخ) ثم حاول مجدداً. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "A new task will be created on the following dates:"
msgstr "سيتم إنشاء مهمة جديدة في التواريخ التالية: "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"لا يمكن أن تقوم بربط مرحلة مستخدم بالمشروع لأنها تكون مرئية فقط للمستخدم. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "توجد علامة تصنيف بنفس الاسم. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "يمكن أن يكون للمهمة مرحلة شخصية واحدة فقط لكل مستخدم. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "قبول رسائل البريد من"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_instruction_message
msgid "Access Instruction Message"
msgstr "رسالة إرشادات الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "وضع الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "تحذير من خطأ بالوصول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "نشط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "الأنشطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "أنواع الأنشطة "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Add Milestone"
msgstr "إضافة مؤشر تقدم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "إضافة وصف..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "إضافة ملاحظة"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"قم بإضافة أعمدة لتنظيم مهامك إلى <b>مراحل</b> <i>مثال: جديد - قيد التنفيذ - "
"منتهي</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "إضافة جهات اتصال لمشاركة المشروع... "

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "إضافة محتوى إضافي لعرضه في البريد الإكتروني"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "إضافة مهمتك عندما تكون جاهزة. "

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "المدير "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "منهجية تطوير البرمجيات (Agile Scrum) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "لقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "أمان ألقاب جهات الاتصال "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr "نطاق اللقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_value
msgid "Alias email"
msgstr "لقب البريد الإلكتروني "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "النموذج الملقب "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "الكل"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "جميع المستخدمين الداخليين"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__all
msgid "All tasks"
msgstr "كافة المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__planned_hours
msgid "Allocated Hours"
msgstr "الساعات المخصصة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_rating
msgid "Allow Customer Ratings"
msgstr "السماح بتقييمات العملاء "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allow_subtasks
msgid "Allow Sub-tasks"
msgstr "السماح بالمهام الفرعية "

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
#: model:ir.model.fields,help:project.field_project_task__project_analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""
"الحساب التحليلي الذي يرتبط به هذا المشروع ومهامه وجداوله الزمنية. \n"
"تتبع كافة تكاليف وإيرادات مشاريعك عن طريق إعداد هذا الحساب التحليلي في مستنداتك ذات الصلة (مثال: أوامر البيع، فواتير العملاء، أوامر الشراء، فواتير الموردين، النفقات، إلخ...). \n"
"يمكن تغيير هذا الحساب التحليلي في كل مهمة على حدة، إذا لزم الأمر. \n"
"الحساب التحليلي مطلوب حتى تتمكن من استخدام الجداول الزمنية. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task and its timesheets are linked.\n"
"Track the costs and revenues of your task by setting its analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"By default, the analytic account of the project is set. However, it can be changed on each task individually if necessary."
msgstr ""
"الحساب التحليلي التي ترتبط به هذه المهمة وجداولها الزمنية. \n"
"تتبع كافة تكاليف وإيرادات مهامك عن طريق إعداد هذا الحساب التحليلي في مستنداتك ذات الصلة (مثال: أوامر البيع، فواتير العملاء، أوامر الشراء، فواتير الموردين، النفقات، إلخ...). \n"
"يكون الحساب التحليلي للمشروع معداً بشكل افتراضي، ولكن يمكن تغيير هذا الحساب التحليلي في كل مهمة على حدة، إذا لزم الأمر. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "التحليلات"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"قم بتحليل مدى سرعة أداء فريقك لمهام مشروعك وتحقق مما إذا كان كل شيء يسير حسب"
" الخطة. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "قم بتحليل تقدم مشاريعك وأداء موظفيك. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__ancestor_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__ancestor_id
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Ancestor Task"
msgstr "المهمة الأصلية "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__april
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__april
msgid "April"
msgstr "أبريل"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_controller.js:0
#, python-format
msgid "Archive"
msgstr "الأرشيف "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "أرشفه المراحل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "مؤرشف"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Archived tasks cannot be recurring. Please unarchive the task first."
msgstr "لا يمكن أن تكون المهام المؤرشفة متكررة. يرجى إلغاء أرشفتها أولاً. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "هل أنت متاكد أنك ترغب في الاستمرار؟ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete those stages ?"
msgstr "هل أنت متاكد أنك ترغب في حذف تلك المراحل؟ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "سهم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "أيقونة السهم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "التركيب "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Assign a responsible to your task"
msgstr "تعيين مسؤول لمهمتك "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Assign each new project to this plan"
msgstr "إسناد كل مشروع جديد لهذه الخطة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr "إسنادها لي"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "تم إسنادها "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Assigned On: %s"
msgstr "تم التعيين في: %s "

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "المهام المسندة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "تم إسنادها إلى "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Assignees"
msgstr "المسند إليهم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "تاريخ الإسناد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "تاريخ الإسناد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "في خطر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage, employees can block tasks or mark them as ready for the next step.\n"
"                                    You can customize here the labels for each state."
msgstr ""
"في كل مرحلة، بوسع الموظفين حجب المهام أو تعيينها كجاهزة للخطوة التالية.\n"
"                                    بإمكانك تخصيص بطاقات عناوين هنا لكل حالة."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "المرفقات التي لا تأتي من رسالة. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__august
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__august
msgid "August"
msgstr "أغسطس"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "الكاتب "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "إنشاء المهام تلقائياً للأنشطة العادية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic Kanban Status"
msgstr "حالة كانبان التلقائية "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""
"قم بتعديل حالة كانبان تلقائياً عندما يجيب العميل على ملاحظات هذه المرحلة.\n"
" * سوف تقوم الملاحظات الإيجابية بتحديث حالة كانبان إلى 'جاهز للمرحلة الجديدة' (العلامة الخضراء).\n"
" * ستعين الملاحظات المحايدة أو السلبية حالة كانبان إلى 'محجوب' (العلامة الحمراء).\n"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Avatar"
msgstr "الصورة الرمزية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating"
msgstr "متوسط التقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "متوسط التقييم (%) "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "متوسط التقييم: غير راضٍ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "متوسط التقييم: جيد "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "متوسط التقييم: راضٍ "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "المتأخرات"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "الرصيد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Billed"
msgstr "مفوتر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
msgid "Block"
msgstr "كتلة برمجية إنشائية "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_6
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_6
#: model:project.task.type,legend_blocked:project.project_stage_0
#: model:project.task.type,legend_blocked:project.project_stage_2
#: model:project.task.type,legend_blocked:project.project_stage_3
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Blocked"
msgstr "محجوب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "محجوب من قِبَل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Blocking"
msgstr "الحجب "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "عصف ذهني "

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "خلل "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "مخطط التوقف "

#. module: project
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "تنبيه أو تعين كمهمة منتهية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__can_be_marked_as_done
msgid "Can Be Marked As Done"
msgstr "يمكن تعيينه كتم الانتهاء منه "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Canceled"
msgstr "تم الإلغاء "

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Cannot aggregate field %r."
msgstr "لا يمكن تجميع الحقل %r. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__partner_is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "تحقق مما إذا كانت جهة الاتصال عبارة عن شركة، وإلا فستعتبر فرداً "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__child_text
msgid "Child Text"
msgstr "النص التابع "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the name of a customer,\n"
"     of a product, of a team, of a construction site, etc.</i>"
msgstr ""
"اختر <b>اسماً</b> لمشروعك. <i>يمكنك اختيار ما تريد: اسم العميل،\n"
"     اسم المنتج، اسم الفريق، اسم موقع البناء، وما إلى ذلك.</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"أعطِ مهمتك <b>اسماً</b> <i>(مثال: تصميم الموقع الإلكتروني، شراء "
"البضائع...)</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr "المدينة"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "مراجعة العميل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Closed"
msgstr "مغلق"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Last 30 Days"
msgstr "المغلقة خلال الـ 30 يوماً الأخيرة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Last 7 Days"
msgstr "المغلقة خلال آخر 7 أيام "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Tasks"
msgstr "المهام المغلقة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closing Stage"
msgstr "مرحلة الإغلاق "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid ""
"Collaborate efficiently with key stakeholders by sharing with them the "
"Kanban view of your tasks. Collaborators will be able to edit parts of tasks"
" and send messages."
msgstr ""
"تعاون بكفاءة مع الجهات المعنية عن طريق مشاركة نافذة عرض كانبان لمهامك معهم. "
"سيكون بمقدور المتعاونين تحرير أجزاء من المهام وإرسال الرسائل. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "المتعاون"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
#, python-format
msgid "Collaborators"
msgstr "المتعاونون"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "المتعاونون في المشروع المُشارَك "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"اجمع الملاحظات من عملائك عن طريق إرسال طلبات تقييم إليهم عندما تصل المهمة إلى مرحلة محددة. للقيام بذلك، قم بتحديد قالب بريد إلكتروني للتقييم في المراحل المعنية. \n"
"التقييم عند تغير المرحلة: سيتم إرسال بريد إلكتروني تلقائياً عندما تصل المهمة إلى المرحلة التي قد تم تعيين قالب البريد الإلكتروني للتقييم فيها. \n"
"التقييم الدوري: سيتم إرسال بريد إلكتروني في فترات منتظمة طالما أن المهمة لا تزال في المرحلة التي قد تم تعيين قالب البريد الإلكتروني للتقييم فيها. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "اللون"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "معرف اللون"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__commercial_partner_id
#: model:ir.model.fields,field_description:project.field_project_task__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجاري"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt. Organize priorities amongst orders using the"
msgstr ""
"تواصل مع العملاء حول المهمة باستخدام بوابة العملاء. قم بإرفاق تصاميم الشعار بالمهمة حتى تنتقل المعلومات من\n"
"      المصممين إلى العمال طابعي القمصان. قم بتنظيم الأولويات من بين الأوامر باستخدام "

#. module: project
#: model:ir.model,name:project.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
msgid "Company"
msgstr "الشركة "

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "التهيئة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "تهيئة المراحل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "تأكيد"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_task_type_delete.py:0
#, python-format
msgid "Confirmation"
msgstr "تأكيد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Congratulations, you are now a master of project management."
msgstr "تهانينا، أصبحت الآن محترفاً في إدارة المشاريع. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "استشارة"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "جهة الاتصال"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_stop_recurrence_confirmation_dialog/project_stop_recurrence_confirmation_dialog.xml:0
#, python-format
msgid "Continue Recurrence"
msgstr "الاستمرار في التكرار "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "الكتابة الإعلامية "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Costs"
msgstr "التكاليف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "صورة الغلاف"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr ""
"قم بإنشاء <b>الأنشطة</b> لإنشاء قوائم للمهام المراد تنفيذها أو لجدولة "
"الاجتماعات. "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "تاريخ الإنشاء"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "إنشاء مشروع"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "إنشاء مرحلة جديدة في مخطط سير عمل المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "إنشاء مشروع "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr "أنشئ مشاريعاً لتنظيم مهامك وتحديد سير عمل مختلف لكل مشروع. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr "أنشئ مشاريعاً لتنظيم مهامك. حدد سير عمل مختلف لكل مشروع. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "قم بإنشاء مهام عن طريق إرسال بريد إلكتروني إلى "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr ""
"أنشئ المهام عن طريق إرسال رسالة إلى عنوان البريد الإلكتروني في مشروعك. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "أنشئ في"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "العملة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "المشروع الحالي للمهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current stage of the task"
msgstr "المرحلة الحالية للمهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "المرحلة الحالية لهذه المهمة"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"متاح حالياً لجميع من يقومون بعرض هذا المستند. اضغط لتقييد الوصول ليُسمَح "
"للموظفين الداخليين فقط. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"وحدهم الموظفون الداخليون يُسمَح لهم بالوصول. اضغط لجعله متاحاً لجميع من "
"يقومون بعرض هذا المستند. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "الرسالة المرتدة المخصصة"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Customer"
msgstr "العميل"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr "بريد العميل الإلكتروني "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "ملاحظات العميل"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "رابط بوابة العميل"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "تقييمات العميل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "حالة تقييمات العملاء "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly. Your managers decide which feedback is accepted"
msgstr ""
"يقدم العملاء ملاحظاتهم عبر البريد الإلكتروني؛ ويقوم أودو بإنشاء المهام تلقائياً، ويمكنك\n"
"      التواصل بشأن المهمة مباشرةً. يحدد مديرك أي الملاحظات مقبولة "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customers will be added to the followers of their project and tasks."
msgstr "ستتم إضافة العملاء إلى قائمة متابعي مشاريعهم. "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Customize how tasks are named according to the project and create tailor "
"made status messages for each step of the workflow. It helps to document "
"your workflow: what should be done at which step."
msgstr ""
"قم بتخصيص كيفية تسمية المهام وفقاً للسجل ثم قم بإنشاء رسائل حالات معدّة "
"خصيصاً لكل خطوة من سير العمل. يساعدك على توثيق سير عملك: ما يجب فعله في أي "
"خطوة. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "يوميًا"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "Date and Stage"
msgstr "التاريخ والمرحلة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__date
msgid "Date of the Month"
msgstr "اليوم من الشهر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__date
msgid "Date of the Year"
msgstr "اليوم من السنة "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the stage of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage to another."
msgstr ""
"تاريخ آخر تعديل للمرحة التي بها مهمتك. \n"
"بناءً على هذه المعلومة، يمكنك تمييز المهام المتعطلة والحصول على الإحصاءات حول الوقت الذي تستغرقه في العادة للانتقال من مرحلة إلى أخرى. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"التاريخ الذي ينتهي فيه المشروع. يتم أخذ الإطار الزمني المحدد في المشروع بعين"
" الاعتبار عند عرض المخططات. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"تاريخ آخر مرة تم إسناد (أو إلغاء إسناد) هذه المهمة. بناءً على ذلك، تمكن من "
"الحصول على الإحصاءات حول الوقت الذي تستغرقه عملية إسناد المهام عادةً. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_weekday
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_weekday
msgid "Day Of The Week"
msgstr "اليوم من الأسبوع "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__day
msgid "Day of the Month"
msgstr "اليوم من الشهر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__day
msgid "Day of the Year"
msgstr "اليوم من السنة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "أيام "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "الأيام حتى الموعد النهائي "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Deadline"
msgstr "الموعد النهائي"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Deadline: %s"
msgstr "الموعد النهائي: %s "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "عزيزي"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__december
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__december
msgid "December"
msgstr "ديسمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_company__analytic_plan_id
#: model:ir.model.fields,field_description:project.field_res_config_settings__analytic_plan_id
msgid "Default Plan"
msgstr "الخطة الافتراضية "

#. module: project
#: model:ir.model.fields,help:project.field_res_company__analytic_plan_id
#: model:ir.model.fields,help:project.field_res_config_settings__analytic_plan_id
msgid "Default Plan for a new analytic account for projects"
msgstr "الخطة الافتراضية للحساب التحليلي الجديد للمشاريع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"قم بتحديد الخطوات التي سيتم استخدامها في المشروع بدءاً من\n"
"                إنشاء المهمة، حتى إغلاق المهمة أو المشكلة.\n"
"                سوف تستخدم هذه المراحل في تتبع مستوى التقدم\n"
"                في حل مهمة أو مشكلة."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr "يقوم بتحديد الخطوات التي يخطوها مشروعك منذ الإنشاء وحتى الإكمال. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr "يقوم بتحديد الخطوات التي تخطوها مهامك منذ الإنشاء وحتى الإكمال. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "حذف مؤشر التقدم "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
#, python-format
msgid "Delete Stage"
msgstr "حذف المرحلة "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"قم بإيصال مهامك تلقائياً عند الوصول إلى مؤشر تقدم جديد، عن طريق ربطه بعنصر "
"أمر مبيعات. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "تم التوصيل "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "المهام المعتمدة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "الوصف"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "وصف لتقديم المزيد من المعلومات والسياق حول هذا المشروع "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "تصميم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "قم بتحديد الترتيب الذي ستؤدي به مهامك "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "التّطوير"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "الملخص"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "التسويق الرقمي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "تحذيرات التقييم المعطلة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "إهمال "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "عرض وضع الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_parent_task_button
msgid "Display Parent Task Button"
msgstr "عرض زر المهمة الأصلية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_project_id
msgid "Display Project"
msgstr "عرض المشروع "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__ko
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Dissatisfied"
msgstr "غير راضٍ "

#. module: project
#. odoo-python
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد الملخص للمستخدم. "

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "تم الانتهاء "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "مسودة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr "القائمة المنسدلة"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit"
msgstr "تحرير"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_renderer.js:0
#, python-format
msgid "Edit Personal Stage"
msgstr "تحرير المرحلة الشخصية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Edit recurring task"
msgstr "تحرير المهمة المتكررة "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "جاري التحرير "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_email
#: model:ir.model.fields,field_description:project.field_project_project__partner_email
#: model:ir.model.fields,field_description:project.field_project_task__partner_email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email From"
msgstr "بريد إلكتروني من "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""
"عناوين البريد الإلكتروني التي كانت في قائمة CC في رسائل البريد الواردة من "
"هذه المهمة والتي هي غير مرتبطة حالياً بمستخدم موجود بالفعل. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "نسخة البريد الإلكتروني "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "رسائل البريد الإلكترونية مرسلة إلى "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Employees Only"
msgstr "الموظفين فقط "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "تاريخ الانتهاء"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "خطأ! لا يمكنك إنشاء تدرج هرمي متداخل للمهام. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr "يمكن لأي شخص أن يقوم باقتراح أفكار، وسيقوم المحرر بتحديد أفضلها كـ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Expected"
msgstr "المتوقع "

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "تجربة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "تاريخ الانتهاء "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "عوامل التصفية التفصيلية "

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "خارجي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "معلومات إضافية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "المفضلة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__february
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__february
msgid "February"
msgstr "فبراير"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid ""
"Field %s is not a stored field, only stored fields (regular or many2many) "
"are valid for the 'groupby' parameter"
msgstr ""
"الحقل %s ليس حقلاً مخزناً. وحدها الحقول المخزنة (العادية أو متعدد إلى "
"ومتعدد) هي الصالحة لمعيار 'groupby' "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "المستند النهائي"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__first
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__first
msgid "First"
msgstr "الأول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "مطوي في عرض كانبان"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__is_closed
#: model:ir.model.fields,help:project.field_report_project_task_user__is_closed
msgid "Folded in Kanban stages are closing stages."
msgstr "الخطوات المطوية في عرض كانبان هي الخطوات الختامية. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr "تابع هذا المشروع لتتبع الفعاليات المرتبطة بمهامه ومشكلاته تلقائياً. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr "تمت متابعته "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Followed Tasks"
msgstr "المهام المتابعة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "التحديثات المتابعة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال fa-tasks "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "للأبد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__fri
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__fri
msgid "Fri"
msgstr "الجمعة"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__fri
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__fri
msgid "Friday"
msgstr "الجمعة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "إنجاز الأعمال"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr "احصل على لقطة لحالة مشروعك وقم بمشاركة تقدمه مع الأطراف المعنية. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr "احصل على ملاحظات العملاء "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
msgstr ""
"امنح الموظفين صلاحية الوصول إلى مشروعك أو مهامك عن طريق إضافتهم كمتابعين. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Grant portal users access to your project or tasks by adding them as "
"followers."
msgstr ""
"امنح مستخدمي البوابة صلاحية الوصول إلى مشروعك أو مهامك عن طريق إضافتهم "
"كمتابعين. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr "بطاقة عنوان كانبان خضراء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr "بطاقة عنوان كانبان رمادية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "التجميع حسب "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the"
msgstr ""
"تمكن من تجميع أفكارك ضمن مهام مشروعك الجديد وقم بمناقشتها في دردشة المهام. "
"استخدم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "التسليم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "وجه سعيد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__has_late_and_unreached_milestone
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__has_late_and_unreached_milestone
msgid "Has Late And Unreached Milestone"
msgstr "يحتوي على مؤشر تقدم متأخر ولم يتم الوصول إليه "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "مرتفع"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "High Priority"
msgstr "أولوية عالية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "السجل"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "الساعات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "ما هي أحوال مشروعك؟ "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "المُعرف"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"معرف السجل الأصل الذي يحتوي على اللقب (مثال: المشروع الذي يحتوي على اللقب "
"لإنشاء المهمة) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "أفكار"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"إذا كان مفعلاً، سيتم عرض هذه المرحلة كمطوية في عرض كانبان لمشاريعك. تعد "
"المشاريع في المرحلة المطوية مغلقة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your tasks. Tasks in a folded stage are considered as closed (not applicable"
" to personal stages)."
msgstr ""
"إذا كان مفعلاً، سيتم عرض هذه المرحلة كمطوية في عرض كانبان لمشاريعك. تعد "
"المهام في المرحلة المطوية مغلقة. (لا ينطبق على المراحل الشخصية). "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"إذا كان معيناً، سيتم إرسال طلب تقييم تلقائياً عن طريق البريد الإلكتروني إلى العميل عندما تصل المهمة إلى هذه المهمة. \n"
"وإلا، فسيتم إرساله في فترات منتظمة طالما أن المهمة لا تزال في تلك المرحلة، بناءً على تهيئة مشروعك. \n"
"لاستخدام هذه الخاصية، تأكد من أن خيار 'تقييمات العملاء' مفعل في مشروعك. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة بريد الكتروني إلى العميل تلقائياً عند وصول "
"المشروع إلى هذه المرحلة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة بريد الكتروني إلى العميل تلقائياً عند وصول "
"المهمة إلى هذه المرحلة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"إذا كان محدداً، سوف يتم إرسال هذا المحتوى تلقائياً إلى المستخدمين غير المصرح"
" لهم عوضاً عن الرسالة الافتراضية. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"إذا تم تعيين قيمة الحقل النشط إلى خطأ، سوف يكون باستطاعتك إخفاء المشروع دون "
"إزالته. "

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_0
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_2
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_3
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_4
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_5
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_6
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_0
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_1
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_2
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_3
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_4
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_5
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_6
#: model:project.task.type,legend_normal:project.project_stage_0
#: model:project.task.type,legend_normal:project.project_stage_1
#: model:project.task.type,legend_normal:project.project_stage_2
#: model:project.task.type,legend_normal:project.project_stage_3
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "قيد التطوير "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "صندوق الوارد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Initially Planned Hours"
msgstr "ساعات العمل المبدئية المخطط لها"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "داخلي"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal Note"
msgstr "ملاحظة داخلية"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"البريد الإلكتروني الداخلي المرتبط بهذا المشروع. تتم مزامنة رسائل البريد "
"الإلكتروني الواردة تلقائياً مع المهام (أو يقوم بالإصدار اختيارياً إذا كان "
"تطبيق تثبيت تتبع المشاكل مثبتاً). "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "يتم عرض الملاحظات الداخلية فقط للمستخدمين الداخليين. "

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid aggregation function %r."
msgstr "وظيفة التجميع غير صالحة %r. "

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid field %r on model %r"
msgstr "حقل غير صالح %r في النموذج %r "

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid field specification %r."
msgstr "مواصفات الحقل غير صالحة %r. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr "المشغّل غير صالح: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "القيمة غير صالحة: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "دعوة الأفراد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users"
msgstr "المستخدمون الداخليون المدعوون "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users"
msgstr "مستخدمو البوابة المدعوون وكافة المستخدمين الداخليين "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Invoiced"
msgstr "مفوتر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_analytic_account_id_changed
msgid "Is Analytic Account Manually Changed"
msgstr "تم تغيير الحساب التحليلي يدوياً "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_blocked
msgid "Is Blocked"
msgstr "محجوب "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "تم تجاوز الموعد النهائي "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "الموعد النهائي في المستقبل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_milestone_exceeded
msgid "Is Milestone Exceeded"
msgstr "تم تخطي مؤشر التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_reached
msgid "Is Milestone Reached"
msgstr "تم الوصول إلى مؤشر التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_private
msgid "Is Private"
msgstr "خاص "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_is_company
msgid "Is a Company"
msgstr "شركة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "إصدار المشكلة"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid "It seems that some tasks are part of a recurrence."
msgstr "يبدو أن بعض مهامك هي جزء من تكرار. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid ""
"It seems that some tasks are part of a recurrence. At least one of them must"
" be kept as a model to create the next occurences."
msgstr ""
"يبدو أن بعض المهام هي جزء من تكرار. يجب أن تكون إحداها على الأقل نموذجاً "
"لإنشاء المرات التالية. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid "It seems that this task is part of a recurrence."
msgstr "يبدو أن هذه المهمة هي جزء من تكرار. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid ""
"It seems that this task is recurrent. Would you like to stop its recurrence?"
msgstr "يبدو أن هذه المهمة متكررة. هل ترغب في إيقاف تكرارها؟ "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__january
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__january
msgid "January"
msgstr "يناير"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__july
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__july
msgid "July"
msgstr "يوليو"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__june
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__june
msgid "June"
msgstr "يونيو"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "تفسير حالة محجوب في واجهة كانبان "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "تفسير حالة \"جاري\" في واجهة كانبان "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Kanban State"
msgstr "حالة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr "بطاقة عنوان حالة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr "تفسير حالة \"صالح\" في واجهة كانبان "

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"تمكن من متابعة تقدم مهامك منذ إنشائها وحتى اكتمالها.<br>\n"
"                    تعاون بكفاءة عن طريق الدردشة في الوقت الفعلي أو عبر البريد الإلكتروني."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"تمكن من متابعة تقدم مهامك منذ إنشائها وحتى اكتمالها.<br>\n"
"                تعاون بكفاءة عن طريق الدردشة في الوقت الفعلي أو عبر البريد الإلكتروني."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "قيمة المؤشر الرئيسي المفتوحة لمهمة المشروع"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__last
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__last
msgid "Last"
msgstr "آخِر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator____last_update
#: model:ir.model.fields,field_description:project.field_project_milestone____last_update
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_project_stage____last_update
#: model:ir.model.fields,field_description:project.field_project_share_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_recurrence____last_update
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_update____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "الشهر الماضي"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "آخر تحديث للمرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "آخر تحديث"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "لون آخر تحديث "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "حالة آخر تحديث "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "آخر تحديث في"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Milestones"
msgstr "مؤشرات التقدم المتأخرة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Tasks"
msgstr "المهام المتأخرة "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "لاحقاً"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Leave a comment"
msgstr "اترك تعليقًا"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "فلنقم بإنشاء <b>مشروعك</b> الأول. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>stage</b>."
msgstr "فلنقم بإنشاء <b>مرحلتك</b> الأولى. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "فلنقم بإنشاء <b>مهمتك</b> الأولى. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your second <b>stage</b>."
msgstr "فلنقم بإنشاء <b>مرحلتك</b> الثانية. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"فلنعد إلى <b>طريقة عرض كانبان</b> للحصول على نظرة عامة على مهامك التالية. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "فلنبدأ بالعمل على مهامك "

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "فلننتظر حتى يقوم عملاؤك بإظهار أنفسهم. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Link"
msgstr "الرابط"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "مباشر "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "تصميم الشعار"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "منخفض"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Low Priority"
msgstr "أولوية منخفضة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_update__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "المُرفقات الرئيسية"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"قم بإدارة دورة حياة مشروعك في طريقة عرض كانبان. أضف المشاريع التي تم الحصول عليها حديثاً،\n"
"      ثم عينها واستخدم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "التصنيع"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__march
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__march
msgid "March"
msgstr "مارس"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Margin"
msgstr "الهامش"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "مصادر المواد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__may
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__may
msgid "May"
msgstr "مايو"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""
"قس مدى رضا عملائك عن طريق إرسال طلبات تقييم عندما تصل مهامك إلى مرحلة معينة."
" "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "الأعضاء"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "القائمة"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "الرسالة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Milestone"
msgstr "مؤشر التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "عدد مؤشرات التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count_reached
msgid "Milestone Count Reached"
msgstr "تم الوصول إلى عدد مؤشرات التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_deadline
msgid "Milestone Deadline"
msgstr "الموعد النهائي لمؤشر التقدم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_project_task__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Milestones"
msgstr "مؤشرات التقدم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "خلط "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__mon
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__mon
msgid "Mon"
msgstr "الإثنين "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__mon
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__mon
msgid "Monday"
msgstr "الاثنين"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "شهور"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Favorite Projects"
msgstr "مشاريعي المفضلة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "مفضلاتي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Private Tasks"
msgstr "مهامي الخاصة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Projects"
msgstr "مشاريعي "

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr "مهامي "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "تحديثاتي "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "الاسم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "الاسم المختصر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks"
msgstr "أسماء المهام "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__name
msgid ""
"Name of your project. It can be anything you want e.g. the name of a "
"customer or a service."
msgstr ""
"اسم مشروعك. يمكن أن يكون أي شيء ترغب به. مثال: اسم العميل أو اسم خدمة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""
"الاسم المستخدم للإشارة إلى مهام مشروعك. مثال: المهام، التذاكر، مهلة التنفيذ،"
" إلخ... "

#. module: project
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "تحتاج إلى مساعدة فنية أو تقنية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "وجه محايد"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#, python-format
msgid "New"
msgstr "جديد"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "خاصية جديدة "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#, python-format
msgid "New Milestone"
msgstr "مؤشر تقدم جديد "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "الطلبات الجديدة "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "المشاريع الجديدة "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "طلب جديد"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "الأحدث"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Next"
msgstr "التالي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr "الأنشطة التالية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Next Activity"
msgstr "النشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__next_recurrence_date
msgid "Next Recurrence Date"
msgstr "تاريخ التكرار القادم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_message
msgid "Next Recurrencies"
msgstr "التكرارات التالية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "لا يوجد عميل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "مؤشر تقدم جديد "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "No Rating"
msgstr "لا يوجد تقييم "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__none
msgid "No Rating yet"
msgstr "لا يوجد تقييم بعد"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr "بلا موضوع"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "لم يتم العثور على أنواع أنشطة. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid "No collaborators found"
msgstr "لم يتم العثور على أي متعاونين "

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "لا توجد أي تقييمات عملاء بعد "

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "لم يتم العثور على أي مشروع. فلننشئ واحداً! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "لم يتم العثور على أي مراحل. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "لم يتم العثور على أي علامات تصنيف. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "لم يتم العثور على أي مهام. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "لم يتم العثور على أي تحديثات. فلنقم بإنشائها! "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "لا شيء"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Not Blocked"
msgstr "غير محجوب "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Not Blocking"
msgstr "ليس هناك حجب "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Not Implemented."
msgstr "غير مطبق. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "ملاحظة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__november
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__november
msgid "November"
msgstr "نوفمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__after
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__after
msgid "Number of Repetitions"
msgstr "عدد مرات التكرار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "عدد المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__recurrence_left
msgid "Number of Tasks Left to Create"
msgstr "عدد المهام المتبقية لإنشائها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "عدد المستندات المرفقة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__october
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__october
msgid "October"
msgstr "أكتوبر"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "خارج المسار "

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "تصميم المكتب "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__ok
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Okay"
msgstr "حسناً "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "Sprint قديم مكتمل "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "قيد الانتظار "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "في المسار "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "مرة في الشهر"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "عفوًا! حدث خطأ ما. حاول إعادة تحميل الصفحة وتسجيل الدخول."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Open"
msgstr "فتح"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Open Tasks"
msgstr "المهام المفتوحة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Open tasks"
msgstr "المهام المفتوحة "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Operation should be = or != (not %s)"
msgstr "يجب أن تكون العملية = أو != (ليس %s) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"معرف اختياري لمناقشة (سجل) سيتم إرفاق كافة رسائل البريد الإلكتروني الواردة "
"فيه، حتى لو لم يتم الرد عليها. إذا تم تعيين قيمة له، سيعطل هذا إنشاء السجلات"
" الجديدة بالكامل. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"قم بتنظيم مهامك عن طريق توزيعها في مخطط سير العمل.<br>\n"
"                    تعاون بكفاءة عن طريق الدردشة في الوقت الفعلي أو عبر البريد الإلكتروني."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "آخرون"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Output name %r is used twice."
msgstr "اسم المخرج %r مُستخدم مرتين. "

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "المهام التي تم تجاوزها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr "المالك"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "أفكار الصفحة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "النموذج الأصلي "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "معرف مناقشة السجل الرئيسي "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr "المهمة الرئيسية "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"النموذج الرئيسي الذي يحتفظ بلقب البريد الإلكتروني. ليس بالضرورة أن يكون "
"النموذج الذي يحتفظ بمرجع لقب البريد الإلكتروني هو النموذج المحدد في الحقل "
"alias_model_id (مثال: المشروع (parent_model) والمهمة (model))"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"الأشخاص الذين سيكون هذا المشروع ومهامه مرئياً بالنسبة لهم.\n"
"\n"
"- المستخدمين الداخليين المدعوين: عند متابعة مشروع ما، سيحصل المستخدمون الداخليون على صلاحية الوصول إلى كافة مهامه دون استثناء. وإلا، فسيحصلون فقط على صلاحية الوصول إلى المهام المحددة التي يتابعونها.\n"
"بإمكان المستخدم ذو صلاحية الوصول المشروع > المدير الوصول إلى هذا المشروع ومهامه، حتى وإن لم يكن أحد المتابعين.\n"
"\n"
"- كافة المستخدمين الداخليين: بوسع كافة المستخدمين الداخليين الوصول إلى المشروع وكافة مهامه دون استثناء.\n"
"\n"
"- مستخدمي البوابة المدعوين وكافة المستخدمين الداخليين: بوسع كافة المستخدمين الداخليين الوصول إلى المشروع وكافة مهامه دون استثناء.\n"
"عند متابعة مشروع ما، سيحصل مستخدمو البوابة على صلاحية الوصول إلى كافة مهامه دون استثناء. وإلا، فسيحصلون فقط على صلاحية الوصول إلى المهام المحددة التي يتابعونها.\n"
"\n"
"عندما تتم مشاركة مشروع للقراءة فقط، تتم إعادة توجيه مستخدم البوابة إلى بوابته. بإمكانه عرض المهام ولكن لن يكون بمقدوره تحريرها.\n"
"عندما تتم مشاركة مشروع للتحرير، تتم إعادة توجيه مستخدم البوابة إلى نافذتي عرض كانبان والقائمة للمهام. بإمكانه تعديل أي عدد من الحقول في المهتم.\n"
"\n"
"بأي حال، لا يزال بوسع مستخدم داخلي بلا صلاحيات وصول إلى المشاريع الوصول إلى المهام، إذا تم منحه رابط URL المناسب (ويجب أن يكون أحد المتابعين إذا كان المشروع خاصاً). "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "نسبة التقييمات السعيدة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodic rating"
msgstr "التقييم الدوري "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Personal Stage"
msgstr "مرحلة شخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "حالة المرحلة الشخصية "

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "مرحلة المهمة الشخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
msgid "Personal User Stage"
msgstr "مرحلة المستخدم الشخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_phone
#: model:ir.model.fields,field_description:project.field_project_task__partner_phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr ""
"قم بتخطيط تخصيص الموارد عبر كافة المشاريع وتقدير المواعيد النهائية بدقة أكبر"
" "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Planned Date"
msgstr "التاريخ المخطط "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Planning"
msgstr "التخطيط"

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"يرجى إزالة المهام الموجودة في المشروع المرتبط بالحسابات التي ترغب في حذفها. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "الإذاعة وإنتاج مقاطع الفيديو "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"سياسة لنشر رسالة في المستند باستخدام بوابة البريد الإلكتروني.\n"
"- الجميع: يمكن للجميع النشر\n"
"- الشركاء: الشركاء المعتمدون فقط\n"
"- المتابعون: فقط متابعو المستند ذي الصلة أو أعضاء القنوات التالية.\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "رابط الوصول لبوابة العملاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "أسماء مستخدمي البوابة "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "ستتم إزالة مستخدمي البوابة من من قائمة متابعي المشروع ومهامه. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Previous"
msgstr "السابق"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Prioritize Tasks by using the"
msgstr "أعطِ الأولوية للمهام باستخدام "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#, python-format
msgid "Priority"
msgstr "الأولوية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "الأولوية: {{'Important' if task.priority == '1' else 'Normal'}}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr "تحذير الأمان للظهور "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_private_task_many2one_field/project_private_task_many2one_field.xml:0
#: code:addons/project/static/src/components/project_private_task_many2one_field/project_private_task_many2one_field.xml:0
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Private"
msgstr "خاص"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Profitability"
msgstr "الربحية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "مدى التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "نسبة التقدم "

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
#, python-format
msgid "Project"
msgstr "المشروع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_analytic_account_id
msgid "Project Analytic Account"
msgstr "حساب المشروع التحليلي "

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "المتعاونين في المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_color
msgid "Project Color"
msgstr "لون المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "عدد المشاريع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "مدير المشروع"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "مؤشر تقدم المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "اسم المشروع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "حالة تقييم المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "مشروع مُتَشارَك "

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "مشاركة المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "مشاركة المشروع: المهمة "

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "مرحلة المشروع "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "تغيرت مرحلة المشروع "

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "معالج حذف مرحلة المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "مراحل المشروع "

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "علامات تصنيف المشاريع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "مهام المشروع"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "تحديث المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "تحديثات المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "قابلية ظهور المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "وصف المشروع..."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "حالة المشروع - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "مهام المشروع"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_recurring_tasks_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_recurring_tasks
msgid "Project: Create Recurring Tasks"
msgstr "المشروع: إنشاء مهام متكررة "

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "المشروع: تم إنجاز المشروع "

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "المشروع: الاعتراف بالطلب "

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr "المشروع: إرسال تقييم"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "المشروع: طلب تقييم المهمة "

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_tags__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#, python-format
msgid "Projects"
msgstr "المشاريع "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr "تحتوي المشاريع على مهام لها نفس الموضوع، ولكل منها لوحة بيانات خاصة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"المشاريع التي توجد فيها هذه المرحلة. إذا قمت بمتابعة سير عمل مماثل في عدة "
"مشاريع، يمكنك مشاركة هذه المرحلة بينها والحصول على معلومات مجمعة بهذه "
"الطريقة. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
msgid "Properties"
msgstr "الخصائص "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "تم النشر "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.js:0
#, python-format
msgid "Published on %s"
msgstr "تم النشر في %s "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "جاري النشر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Rating"
msgstr "التقييم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "التقييم (/5) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "متوسط نص التقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "قالب البريد الإلكتروني للتقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "تواتر التقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "آخر ملاحظات التقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "آخر صورة للتقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_text
msgid "Rating Last Text"
msgstr "تقييم آخر نص "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "آخر قيمة للتقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "الموعد النهائي لطلب التقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "رضا التقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "نص التقييم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#, python-format
msgid "Rating Value (/5)"
msgstr "قيمة التقييم (/5) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "عدد التقييمات"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr "التقييم عند تغيير المرحلة"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "تم الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "تاريخ الوصول "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "للقراءة فقط"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
#: model:project.task.type,legend_done:project.project_personal_stage_admin_0
#: model:project.task.type,legend_done:project.project_personal_stage_admin_1
#: model:project.task.type,legend_done:project.project_personal_stage_admin_2
#: model:project.task.type,legend_done:project.project_personal_stage_admin_3
#: model:project.task.type,legend_done:project.project_personal_stage_admin_4
#: model:project.task.type,legend_done:project.project_personal_stage_admin_5
#: model:project.task.type,legend_done:project.project_personal_stage_admin_6
#: model:project.task.type,legend_done:project.project_personal_stage_demo_0
#: model:project.task.type,legend_done:project.project_personal_stage_demo_1
#: model:project.task.type,legend_done:project.project_personal_stage_demo_2
#: model:project.task.type,legend_done:project.project_personal_stage_demo_3
#: model:project.task.type,legend_done:project.project_personal_stage_demo_4
#: model:project.task.type,legend_done:project.project_personal_stage_demo_5
#: model:project.task.type,legend_done:project.project_personal_stage_demo_6
#: model:project.task.type,legend_done:project.project_stage_0
#: model:project.task.type,legend_done:project.project_stage_2
#, python-format
msgid "Ready"
msgstr "جاهز"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for Next Stage"
msgstr "جاهز للمرحلة التالية "

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "جاهز لإعادة الفتح"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "استقبال {{ object.name }} "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "المستلمين"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "معرف مناقشة السجل"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "تسجيل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_update
msgid "Recurrence Update"
msgstr "تحديث التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurrent"
msgstr "متكرر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_project_task__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
msgid "Recurring Tasks"
msgstr "المهام المتكررة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr "بطاقة عنوان كانبان حمراء"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "تم الرفض "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "المستند ذو الصلة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "معرف المستند ذي الصلة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "نموذج المستند ذي الصلة "

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "الترميم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_day
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_day
msgid "Repeat Day"
msgstr "تكرار اليوم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "التكرار كل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_month
msgid "Repeat Month"
msgstr "تكرار الشهر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Repeat On"
msgstr "التكرار في "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_month
msgid "Repeat On Month"
msgstr "التكرار في الشهر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_year
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_year
msgid "Repeat On Year"
msgstr "التكرار في السنة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_day
msgid "Repeat Show Day"
msgstr "تكرار إظهار اليوم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_dow
msgid "Repeat Show Dow"
msgstr "تكرار اليوم من الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_month
msgid "Repeat Show Month"
msgstr "تكرار إظهار الشهر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_week
msgid "Repeat Show Week"
msgstr "تكرار إظهار الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "تكرار الوحدة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_week
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_week
msgid "Repeat Week"
msgstr "تكرار الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_number
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_number
msgid "Repetitions"
msgstr "التكرار "

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "البحث"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "البحث والتطوير "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "مشروع البحث"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "البحث"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "تخصيص الموارد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Revenues"
msgstr "الإيرادات "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "وجه حزين"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sat
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sat
msgid "Sat"
msgstr "السبت"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Satisfaction"
msgstr "الرضا "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__top
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Satisfied"
msgstr "راضي "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sat
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sat
msgid "Saturday"
msgstr "السبت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Schedule your activity once it is ready."
msgstr "قم بجدولة نشاطك عندما يصبح جاهزاً. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "نص برمجي"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "البحث <span class=\"nolabel\"> (في المحتوى)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "البحث عن مشروع"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "تحديث البحث "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "البحث في الكل"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "البحث في الأفراد المسند إليهم "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "البحث في الرسائل"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Milestone"
msgstr "البحث في مؤشرات التقدم "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "البحث في الأولوية "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "البحث في المشاريع "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "البحث في المراجع "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "البحث في المراحل"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "البحث في الحالات "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__second
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__second
msgid "Second"
msgstr "الثاني "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#, python-format
msgid "Send"
msgstr "إرسال"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "إرسال بريد إلكتروني"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__september
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__september
msgid "September"
msgstr "سبتمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "تعيين صورة غلاف"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
#, python-format
msgid "Set Status"
msgstr "قم بتحديد الحالة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "تعيين قالب تقييم بريد إلكتروني للمراحل "

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""
"التعيين في مراحل المشروع لإخطار العملاء عند وصول المشروع إلى تلك المرحلة "

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""
"قم بإعداد هذا القالب في إحدى مراحل المشروع لطلب الملاحظات من عملائك. قم "
"بتفعيل خاصية \"تقييمات العملاء\" في المشروع "

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"تعيين هذا القالب في مرحلة المشروع لأتمتة رسائل البريد الإلكتروني عندما تصل "
"المهام إلى مراحل "

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "الإعدادات"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "مشاركة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "مشاركة القابل للتحرير "

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "مشاركة المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Readonly"
msgstr "المشاركة للقراءة فقط "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "Should repeat at least once"
msgstr "يجب التكرار مرة واحدة على الأقل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "إظهار المشروع في لوحة المعلومات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "عرض كافة السجلات المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "منذ "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "تطوير البرمجيات"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "عذراً، لا يمكنك تعيين مهمة كمهمة رئيسية لنفسها. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "المواصفات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr "قم بتقسيم مهمامك لتنظيم عملك بوضع مؤشرات تقدم فرعية "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "سبرينت متأخر "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "سبرينت مكتمل "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "سبرينت قيد التنفيذ "

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr "المرحلة"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "تم تغيير المرحلة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "وصف وتلميحات المرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "مالك المرحلة "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "تم تغيير المرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "المراحل النشطة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "المراحل لحذفها "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Stalling for 30 Days+"
msgstr "التعطيل لأكثر من 30 يوم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "الحالة"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Status Update - "
msgstr "تحديث الحالة - "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_stop_recurrence_confirmation_dialog/project_stop_recurrence_confirmation_dialog.xml:0
#, python-format
msgid "Stop Recurrence"
msgstr "إيقاف التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "عدد المهام الفرعية "

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_project__allow_subtasks
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "المهام الفرعية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Sub-tasks Planned Hours"
msgstr "الساعات المخطط لها المهام الفرعية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "تم الإرسال في "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"مجموع الساعات المخصصة لكافة المهام الفرعية (ومهامها الفرعية) المرتبطة بهذه "
"المهمة. عادةً ما يكون عادةً أقل من عدد الساعات المخصصة لهذه المهمة أو "
"مساوياً له. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "الملخص"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sun
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sun
msgid "Sun"
msgstr "الأحد"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sun
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sun
msgid "Sunday"
msgstr "الأحد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "طباعة قميص"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
msgid "Tags"
msgstr "علامات التصنيف "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Task"
msgstr "المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "أنشطة المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "تم حجب المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr "عدد المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count_with_subtasks
msgid "Task Count With Subtasks"
msgstr "عدد المهام مع المهام الفرعية "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "تم إنشاء المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "تبعيات المهام "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_dependency_change
#: model:mail.message.subtype,name:project.mt_task_dependency_change
msgid "Task Dependency Changes"
msgstr "تغييرات تبعيات المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "سجلات المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "خصائص المهمة "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "تقييم المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "المهمة جاهزة"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تكرار المهمة "

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "مرحلة المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "تغيرت مرحلة المهمة"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "مراحل المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "عنوان المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "عنوان المهمة..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "تم حجب المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task description..."
msgstr "وصف المهمة... "

#. module: project
#: model:mail.message.subtype,name:project.mt_task_progress
msgid "Task in Progress"
msgstr "المهام قيد التنفيذ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr "المهمة قيد التنفيذ. اضغط لحظرها أو تعيينها كمنتهية. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr "المهمة محجوبة. اضغط لإلغاء حظرها أو تعيينها كمكتملة. "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "المهمة جاهزة للمرحلة المقبلة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "المهمة: "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_milestone__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_project_tags__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
#, python-format
msgid "Tasks"
msgstr "المهام"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.report_project_task_user_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Tasks Due Today"
msgstr "المهام التي يجب إنجازها اليوم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "إدارة المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "مراحل المهام"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
#, python-format
msgid "Tasks in Recurrence"
msgstr "المهام في التكرار "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "الاختبارات"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "The Burndown Chart must be grouped by"
msgstr "يجب تجميع مخطط التوقف حسب "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "المرحلة الشخصية للمستخدم الحالي. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "مرحلة المهمة الشخصية للمستخدم الحالي. "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid ""
"The end date should be after the day of the month or the last day of the "
"month"
msgstr "يجب أن يكون تاريخ الانتهاء بعد اليوم من الشهر أو آخر يوم من الشهر "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The end date should be in the future"
msgstr "يجب أن يكون تاريخ النهاية في المستقبل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "تمت إضافة مؤشر التقدم التالي: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "تمت إضافة مؤشرات التقدم التالية: "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The interval should be greater than 0"
msgstr "يجب أن تكون الفترة أكبر من 0 "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"النموذج (مستندات أودو) الذي يقترن به هذا اللقب. أي رسالة واردة لا ترد على "
"سجل موجود ستقوم بإنشاء سجل جديد من نفس نوع هذا النموذج (مثلًا: مهمة مشروع) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"اسم لقب البريد الإلكتروني، مثلًا: 'وظائف' إذا كنت ترغب في جمع الرسائل "
"المرسلة لـ<<EMAIL>> "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"مالك السجلات المنشأة عند استلام رسائل بريد إلكتروني على هذا اللقب. إذا لم "
"يتم تعيين قيمة لهذا الحقل، سيحاول النظام معرفة المالك الصحيح حسب عنوان "
"البريد الإلكتروني للمرسل، أو سيستخدم حساب المدير إذا لم يجد حساباً مرتبطاً "
"بعنوان البريد الإلكتروني. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"لا يمكن مشاركة المشروع مع المستلم (المستلمين) لأن خصوصية المشروع مقيدة "
"للغاية. قم بتغيير مستوى الخصوصية لـ 'مرئي للعملاء المتابعين' لتتيح للمستلم "
"(المستلمين) الوصول إليه. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "يجب أن يكون تاريخ بدء المشروع قبل تاريخ انتهائه. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "The search does not support the %s operator or %s value."
msgstr "لا يدعم البحث مشغل %s أو قيمة %s. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"لا يمكن مشاركة المهمة مع المستلم (المستلمين) لأن خصوصية المهمة مقيدة للغاية."
" قم بتغيير مستوى الخصوصية لـ 'مرئي للعملاء المتابعين' لتتيح للمستلم "
"(المستلمين) الوصول إليه. "

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "The view must be grouped by date and by stage_id"
msgstr "يجب أن تكون نافذة العرض مجمعة حسب التاريخ وحسب  stage_id "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "لا توجد تعليقات حالياً. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "There are no more occurrences."
msgstr "ليس هناك المزيد من الوقائع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "لا توجد مشاريع. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "ليس هناك تقييمات لهذا المشروع في الوقت الحالي "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "لا توجد مهام. "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "There is nothing to report."
msgstr "لا يوجد شيء لكتابة تقرير عنه. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr "سوف يستلم هؤلاء الأفراد بريداً إلكترونياً. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__third
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__third
msgid "Third"
msgstr "ثالثًا"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "هذا الشهر"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "هذا الأسبوع"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__subsequent
msgid "This and following tasks"
msgstr "هذه المهمة والمهام التالية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr "هذه الخطوة مكتملة. اضغط لحجبها أو تعيينها كقيد التنفيذ. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__this
msgid "This task"
msgstr "هذه المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"سيقوم ذلك بأرشفة المراحل وكافة المهام التي تحتويها من المشاريع التالية: "

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"يمثلون الفئات المختلفة للأشياء التي عليك القيام بها (مثال: \"اتصال\" أو "
"\"إرسال بريد إلكتروني\"). "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__thu
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__thu
msgid "Thu"
msgstr "الخميس"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__thu
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__thu
msgid "Thursday"
msgstr "الخميس"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "إدارة الوقت"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "نصيحة: أنشئ المهام من رسائل البريد الواردة "

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Customize tasks and stages according to the project"
msgstr "نصيحة: قم بتخصيص المهام والمراحل وفقاً للمشروع "

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#, python-format
msgid "Title"
msgstr "العنوان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Bill"
msgstr "بانتظار الفوترة "

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "المهام المراد تنفيذها"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Invoice"
msgstr "بانتظار الفوترة"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "للطباعة"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"لإنجاز المهام، استخدم الأنشطة والحالات في المهام.<br>\n"
"                دردش في الوقت الفعلي أو تواصل عبر البريد الإلكتروني للتواصل بشكل أكثر فعالية. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "اليوم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Total"
msgstr "الإجمالي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "تتبع نسبة رضا العميل على المهام"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr "تتبع نقاط التقدم الرئيسية التي يجب إنجازها لتحقيق النجاح. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr "تتبع نقاط التقدم الرئيسية التي يجب إنجازها لتحقيق النجاح. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your projects. Any project, its tasks and "
"timesheets are linked to an analytic account and any analytic account "
"belongs to a plan."
msgstr ""
"تتبع مدى ربحية مشاريعك. أي مشروع تكون مهامه وجداوله الزمنية مرتبطة بحساب "
"تحليلي، وأي حساب تحليلي ينتمي إلى خطة. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "تتبع تقدم مشاريعك "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "تتبع الوقت المقضي على المشاريع والمهام "

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""
"علامات التصنيف الشفافة غير مرئية في نافذة عرض كانبان لمشاريعك ومهامك. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__tue
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__tue
msgid "Tue"
msgstr "الثلاثاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__tue
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__tue
msgid "Tuesday"
msgstr "الثلاثاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "مرتان شهريًا"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Two tasks cannot depend on each other."
msgstr "لا يمكن أن تعتمد مهمتان على بعضهما. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_list/project_task_list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "إلغاء الأرشفة "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unarchive Tasks"
msgstr "إلغاء أرشفة المهام "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Unassign Me"
msgstr "إلغاء إسنادي  "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "غير مسند "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "حساب تحليلي مجهول"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Unknown field %r in 'groupby'"
msgstr "حقل غير معروف %r في 'groupby' "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
msgid "Until"
msgstr "حتى"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "تحديث"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "تم إنشاء التحديث "

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "قابلية الاستخدام "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use"
msgstr "استخدم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_enabled
msgid "Use Email Alias"
msgstr "استخدام لقب البريد الإلكتروني "

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "استخدم مؤشرات التقدم "

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "استخدام التقييم في المشروع "

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "استخدام المهام المتكررة "

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "استخدام المراحل في المشروع "

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr "استخدام المهام الفرعية"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "استخدام تبعيات المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "استخدام المهام كـ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "الاستخدام في مشروعي "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "استخدم علامات التصنيف لوضع مهامك في فئات. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use the"
msgstr "استخدم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the main changes about this task."
msgstr ""
"استخدم الدردشة <b>لإرسال رسائل البريد الإلكتروني</b> والتواصل بكفاءة مع عملائك.\n"
"    أضف المزيد من الأشخاص إلى قائمة المتابعين لإبقائهم على علم بالتغييرات الأساسية المتعلقة بهذه المهمة. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "المستخدم"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Value should be True or False (not %s)"
msgstr "يجب أن تكون القيمة صحيحة أو خاطئة (ليس %s) "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "عرض "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "View Task"
msgstr "عرض المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
msgid "View Tasks"
msgstr "عرض المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "الظهور"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Visible"
msgstr "مرئي "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr "أتريد طريقة أفضل لـ<b>إدارة مشاريعك</b>؟ <i>ابدأ من هنا.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "إعادة تصميم الموقع الإلكتروني "

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__wed
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__wed
msgid "Wed"
msgstr "الأربعاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__wed
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__wed
msgid "Wednesday"
msgstr "الأربعاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "أسابيع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "أيام العمل لتعيينها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "أيام العمل لإغلاقها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "ساعات العمل لتعيينها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "ساعات العمل لإغلاقها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "وقت العمل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "وقت العمل المتبقي حتى الإسناد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "وقت العمل المتبقي حتى الإغلاق"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr "هل ترغب في إلغاء أرشفة كافة المهام الموجودة في تلك المراحل أيضاً؟ "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "الكتابة"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "سنويًا"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "سنوات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"بوسعك أيضاً إضافة وصف لمساعدة زملائك على فهم معنى المرحلة والغرض منها. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__tag_ids
msgid ""
"You can only see tags that are already present in your project. If you try "
"creating a tag that is already existing in other projects, it won't generate"
" any duplicates."
msgstr ""
"يمكنك فقط رؤية علامات التصنيف الموجودة بالفعل في مشروعك. إذا حاولت إنشاء "
"علامة تصنيف موجودة بالفعل في مشاريع أخرى، لن تقوم بإنشاء أي نسخ مطابقة. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You can only set a personal stage on a private task."
msgstr "يمكنك فقط تعيين مرحلة شخصية في مهمة خاصة. "

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"You cannot change the company of an analytic account if it is related to a "
"project."
msgstr "لا يمكنك تغيير شركة حساب تحليلي إذا كان متعلقاً بمشروع. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete recurring tasks. Please disable the recurrence first."
msgstr "لا يمكنك حذف المهام المتكررة. يرجى تعطيل التكرار أولاً. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"لا يمكنك حذف مراحل تحتوي على مهام. بإمكانك إما أرشفتها أولاً أو حذف كافة "
"مهامها. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr "لا يمكنك حذف مراحل تحتوي على مهام. عليك أولاً حذف كافة مهامها. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "لا يمكنك قراءة %s حقول في المهمة. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "لا يمكنك الكتابة في %s حقول في المهمة. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "تم تعيينك لـ %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "تم تعيينك لـ"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "لا تملك حق الكتابة في حقل %s. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""
"لديك تغييرات غير محفوظة - لا داعي للقلق! سيقوم أودو بحفظها تلقائياً بينما "
"تتنقل. <br/> بإمكانك إهمال كافة التغييرات من هنا أو حفظ مهمتك يدوياً. <br/> "
"فلنقم بحفظها يدوياً. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "You must be"
msgstr "لابد أنك "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You should at least have one personal stage. Create a new stage to which the"
" tasks can be transferred after this one is deleted."
msgstr ""
"يجب أن تكون لديك مرحة شخصية واحدة على الأقل. قم بإنشاء مرحلة جديدة يمكن أن "
"يتم تحويل المهام إليها بعد أن يتم حذف هذه. "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "You should select a least one day"
msgstr "عليك تحديد يوم واحد على الأقل "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "and"
msgstr "و"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"وأي الملاحظات يتم\n"
"      نقله إلى عمود \"مرفوض\". "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignee"
msgstr "المسند إليه "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "المسند إليهم "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "avatar"
msgstr "الصورة الرمزية"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "bullets to indicate the status of a task."
msgstr "نقاط للإشارة إلى حالة المهمة. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"button to inform your colleagues that a task is ready for the next stage."
msgstr "لإخطار زملائك في العمل بأن المهمة جاهزة للمرحلة التالية. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "comments"
msgstr "تعليقات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "مثال: التقييم الشهري "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr "مثال: حفلة مكتبية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "مثال: إطلاق المنتج "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "مثال: إرسال الدعوات "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "مثال: حفلة مكتبية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "مثال: إطلاق المنتج "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon to organize your daily activities."
msgstr "لتنظيم أنشطتك اليومية "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon."
msgstr ". "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "logged in"
msgstr "تم تسجيل الدخول"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "المشروع. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "جاهزة ليتم تحديدها كتم الوصول إليها "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr "المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "تم تحديث الموعد النهائي لمؤشر التقدم التالي: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "تم تحديث الموعد النهائي لمؤشر التقدم التالي: "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"لتحديد ما إذا كان المشروع\n"
"      جاهزاً للخطوة التالية. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to indicate a problem or a need for discussion on a task."
msgstr "للإشارة إلى مشكلة أو الحاجة إلى المناقشة حول مهمة ما. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "to post a comment."
msgstr "لكتابة تعليق."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to signalize what is the current status of your Idea."
msgstr "للإشارة إلى الحالة الحالية لفكرتك. "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "سيقوم بإنشاء مهام في "

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "{{ object.project_id.company_id.name }}: Satisfaction Survey"
msgstr "{{ object.project_id.company_id.name }}: استطلاع الرضا "
