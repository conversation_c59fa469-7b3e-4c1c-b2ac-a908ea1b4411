# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* test_mail_full
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-09 10:49+0000\n"
"PO-Revision-Date: 2019-09-09 10:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_needaction
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_needaction
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_needaction
msgid "Action Needed"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_attachment_count
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_attachment_count
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: test_mail_full
#: model:ir.model,name:test_mail_full.model_mail_test_sms
#: model:ir.model,name:test_mail_full.model_mail_test_sms_bl
msgid "Chatter Model for SMS Gateway"
msgstr ""

#. module: test_mail_full
#: model:ir.model,name:test_mail_full.model_mail_test_sms_partner
msgid "Chatter Model for SMS Gateway (Partner only)"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__create_uid
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__create_uid
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__create_uid
msgid "Created by"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__create_date
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__create_date
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__create_date
msgid "Created on"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__customer_id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__customer_id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__partner_id
msgid "Customer"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__display_name
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__display_name
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__display_name
msgid "Display Name"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__email_from
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__email_from
msgid "Email From"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_follower_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_follower_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_follower_ids
msgid "Followers"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_channel_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_channel_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_partner_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_partner_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__id
msgid "ID"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_needaction
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_unread
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_needaction
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_unread
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_needaction
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_has_error
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_has_sms_error
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_has_error
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_has_sms_error
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_has_error
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__phone_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_is_follower
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_is_follower
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms____last_update
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl____last_update
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner____last_update
msgid "Last Modified on"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__write_uid
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__write_uid
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__write_uid
msgid "Last Updated by"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__write_date
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__write_date
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__write_date
msgid "Last Updated on"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_main_attachment_id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_main_attachment_id
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_has_error
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_has_error
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_ids
msgid "Messages"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__mobile_nbr
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__mobile_nbr
msgid "Mobile Nbr"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__name
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__name
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__name
msgid "Name"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_needaction_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_needaction_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_has_error_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_has_error_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_needaction_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_needaction_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_has_error_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_has_error_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__message_unread_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__message_unread_counter
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__phone_blacklisted
msgid "Phone Blacklisted"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__phone_nbr
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__phone_nbr
msgid "Phone Nbr"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_has_sms_error
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_has_sms_error
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__subject
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__subject
msgid "Subject"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_unread
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_unread
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_unread
msgid "Unread Messages"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__message_unread_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__message_unread_counter
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms__website_message_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_bl__website_message_ids
#: model:ir.model.fields,field_description:test_mail_full.field_mail_test_sms_partner__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: test_mail_full
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms__website_message_ids
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_bl__website_message_ids
#: model:ir.model.fields,help:test_mail_full.field_mail_test_sms_partner__website_message_ids
msgid "Website communication history"
msgstr ""
