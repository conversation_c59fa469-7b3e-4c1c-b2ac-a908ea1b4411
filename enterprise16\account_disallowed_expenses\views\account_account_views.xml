<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="view_account_form" model="ir.ui.view">
        <field name="name">account.account.form</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='tax_ids']" position="after">
                <field name="disallowed_expenses_category_id" string="Disallowed Expenses"
                attrs="{'invisible': [('internal_group', '!=', 'expense'), ('internal_group', '!=', 'income')]}"/>
            </xpath>
        </field>
    </record>

    <record id="view_account_search" model="ir.ui.view">
        <field name="name">account.account.search</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_search"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="disallowed_expenses_category_id"/>
            </field>
        </field>
    </record>
</odoo>
