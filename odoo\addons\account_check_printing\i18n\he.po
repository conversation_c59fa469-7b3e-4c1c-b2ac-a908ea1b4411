# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# NoaFarkash, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>lam <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>yal arkush, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON> arkush, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid " : Check Number Sequence"
msgstr "הרצף המספרי של שיקים"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr "הגדירו שוליים לצ'ק שיופס כדי שיתאים להגדרות המדפסת."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "סכום במילים"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "בטל"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "בדיקת פריסה"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "שוליים שמאליים של השיק"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "מספר המחאה"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "הדפסת שיק"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "שוליים ימניים של שיק"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "רצף מספרי של שיקים"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "שוליים עליונים של שיק"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Check numbers can only consist of digits"
msgstr "מספרי צ'ק חייבים להיות בספרות בלבד"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "סמן אפשרות זו אם ההמחאות שהודפסו מראש אינן ממוספרות."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "שיק להדפסה"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "שיקים"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "הרצף המספרי של שיקים"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
#, python-format
msgid "Checks to Print"
msgstr "שיקים להדפסה"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "שיקים להדפסה"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "לך לפאנל ההגדרות"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "מזהה"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr "כדי להדפיס שיקים מרובים בבת אחת, עליהם להיות שייכים לאותו יומן בנק."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "יומן"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "מספור ידני"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"ידני: שלם או קבל תשלום בכל שיטה מחוץ ל-Odoo.\n"
"ספקי תשלומים: לכל ספק תשלומים יש אמצעי תשלום משלו. בקש עסקה בכרטיס/לכרטיס הודות לאסימון תשלום שנשמר על ידי השותף בקנייה או בהרשמה מקוונת.\n"
"צ'ק: שלם חשבונות בצ'ק והדפיס אותו מ-Odoo.\n"
"הפקדת אצווה: אסוף מספר צ'קים של לקוחות בו-זמנית, תוך הפקה והגשה של הפקדה אצווה לבנק שלך. מודול account_batch_payment הכרחי.\n"
"העברת אשראי SEPA: שלם באזור SEPA על ידי שליחת קובץ העברת אשראי SEPA לבנק שלך. מודול account_sepa הכרחי.\n"
"הוראת קבע SEPA: קבל תשלום באזור SEPA הודות למנדט שהשותף שלך יעניק לך. מודול account_sepa הכרחי.\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "פנקס צ'קים עם ספח."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "מספר השיק הבא"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
#, python-format
msgid "Next Check Number should only contains numbers."
msgstr "מספר השיק הבא צריך להכיל מספרים בלבד."

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "אף אחד"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_users__property_payment_method_id
msgid "Payment Method"
msgstr "אמצעי תשלום"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "אמצעי תשלום"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "תשלומים"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr "כדי להדפיס צ'קים צריך לסמן 'צ'ק' כאמצעי תשלום בהוצאה שטרם עברה התאמה."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "בבקשה להקליד את מספר השיק המודפס שאתה רוצה להדפיס עלוי"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_bank_statement_line__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_move__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__preferred_payment_method_id
msgid "Preferred Payment Method"
msgstr "שיטת תשלום"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,help:account_check_printing.field_res_users__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""
"אמצעי תשלום מועדף כשמשלמים לספק זה. נשתמש בזה כדי לסנן חשבוניות ספקים ולרשום"
" הוצאות מרובות אוטומטית. שימושים אפשריים: יצירת קבצים להעברות מרובות, הדפסת "
"צ'קים."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "הדפס"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "הדפס שיק"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "הדפס שיקים"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "הדפס תווית תאריך"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "הדפס שיקים ממוספרים מראש"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_register
msgid "Register Payment"
msgstr "רשום תשלום"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "שוליים ימניים"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"בחירת פורמט שמתאים לנייר עליו יודפסו הצ'קים .\n"
"כדי להשבית את אפשרות ההדפסה בחרו 'ללא'."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "נשלח"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "מספר רץ של השיק המודפס הבא"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "הראה מספר שיק"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr ""
"משהו השתבש בפריסת הצ'ק, נא להשתמש בפריסה אחרת תחת חשבוניות/הגדרות חשבונאיות "
"ונסו שוב."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"המספרים האלה כבר בשימוש:\n"
"%s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"מספר השיק האחרון היה %s. על מנת למנוע את דחיית השיק על ידי הבנק, עליך לבחור "
"מספר גבוה יותר."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"יומן זה הותאם כך שידפיס מספרי שיקים. אם קיימים כבר מספרים על נייר ההדפסה או "
"אם המספור כעת אינו נכון, אפשר לשנות זאת בהגדרות היומן."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""
"האפשרות הזו מאפשרת להדפיס פרטי צ'ק (ספח) במספר דפים, אם הפרטים לא נכנסים "
"בעמוד אחד."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"אפשרות זו מאפשרת להדפיס את תווית התאריך על השיק לפי עלות לרכישה.\n"
"השבת זאת אם ההמחאה המודפסת מראש שלך כוללת את תווית התאריך."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "מאפשר לרשום את מספר השיק בשורת התשלום."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "הורד סימון כנשלח"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "צ'ק מבוטל"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
"יש לבחור בפריסת צ'ק. עושים את זה תחת חשבוניות / הגדרות חשבונאיות, חפשו "
"'פריסת צ'ק' והגדירו תצורה."
