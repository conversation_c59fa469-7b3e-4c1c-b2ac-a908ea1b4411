<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.CallActionList" owl="1">
        <t t-if="callActionListView">
            <div class="o_CallActionList d-flex justify-content-between" t-attf-class="{{ className }}" t-ref="root">
                <div class="o_CallActionList_buttons d-flex align-items-center flex-wrap">
                    <t t-if="callActionListView.thread.rtc and messaging.rtc.currentRtcSession">
                        <button class="o_CallActionList_button btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                            t-att-class="{ 'o-isActive': !messaging.rtc.currentRtcSession.isMute, 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                            t-att-aria-label="callActionListView.microphoneButtonTitle"
                            t-att-title="callActionListView.microphoneButtonTitle"
                            t-on-click="callActionListView.onClickMicrophone">
                            <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                <i class="fa fa-stack-1x" t-att-class="{
                                    'fa-lg': !callActionListView.isSmall,
                                    'fa-microphone': !messaging.rtc.currentRtcSession.isMute,
                                    'fa-microphone-slash': messaging.rtc.currentRtcSession.isMute,
                                    'text-danger': messaging.rtc.currentRtcSession.isMute,
                                }"/>
                            </div>
                        </button>
                        <button class="o_CallActionList_button btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                            t-att-class="{ 'o-isActive': !messaging.rtc.currentRtcSession.isDeaf, 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                            t-att-aria-label="callActionListView.headphoneButtonTitle"
                            t-att-title="callActionListView.headphoneButtonTitle"
                            t-on-click="callActionListView.onClickDeafen">
                            <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                <i class="fa fa-stack-1x" t-att-class="{
                                    'fa-lg': !callActionListView.isSmall,
                                    'fa-headphones': !messaging.rtc.currentRtcSession.isDeaf,
                                    'fa-deaf': messaging.rtc.currentRtcSession.isDeaf,
                                    'text-danger': messaging.rtc.currentRtcSession.isDeaf,
                                }"/>
                            </div>
                        </button>
                        <button class="o_CallActionList_button o_CallActionList_videoButton btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                            t-att-class="{
                                'o-isActive': messaging.rtc.sendUserVideo,
                                'o-isSmall p-2': callActionListView.isSmall,
                                'p-3': !callActionListView.isSmall,
                            }"
                            t-att-aria-label="callActionListView.cameraButtonTitle"
                            t-att-title="callActionListView.cameraButtonTitle"
                            t-on-click="callActionListView.onClickCamera">
                            <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                <i class="fa fa-video-camera fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall, 'text-success': messaging.rtc.sendUserVideo }"/>
                            </div>
                        </button>
                        <t t-if="!messaging.device.isMobileDevice">
                            <button class="o_CallActionList_button o_CallActionList_videoButton btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                                t-att-class="{
                                    'o-isActive': messaging.rtc.sendDisplay,
                                    'o-isSmall p-2': callActionListView.isSmall,
                                    'p-3': !callActionListView.isSmall,
                                }"
                                t-att-aria-label="callActionListView.screenSharingButtonTitle"
                                t-att-title="callActionListView.screenSharingButtonTitle"
                                t-on-click="callActionListView.onClickScreen">
                                <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                    <i class="fa fa-desktop fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall, 'text-success': messaging.rtc.sendDisplay }"/>
                                </div>
                            </button>
                        </t>
                        <t t-if="!callActionListView.callView.isFullScreen">
                            <button class="o_CallActionList_button btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                                aria-label="Activate Full Screen"
                                title="Activate Full Screen"
                                t-att-class="{ 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                                t-on-click="callActionListView.callView.activateFullScreen"
                            >
                                <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                    <i class="fa fa-arrows-alt fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                                </div>
                            </button>
                        </t>
                        <t t-if="callActionListView.callView.isFullScreen">
                            <button class="o_CallActionList_button btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                                aria-label="Deactivate Full Screen"
                                title="Deactivate Full Screen"
                                t-att-class="{ 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                                t-on-click="callActionListView.callView.deactivateFullScreen"
                            >
                                <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                    <i class="fa fa-compress fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                                </div>
                            </button>
                        </t>
                        <t t-if="messaging.modelManager.isDebug">
                            <button class="o_CallActionList_button btn d-flex m-1 border-0 rounded-circle shadow-none opacity-100 opacity-75-hover"
                                    aria-label="More"
                                    title="More"
                                    t-att-class="{ 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                                    t-on-click="callActionListView.onClickMore"
                                    t-ref="moreButton"
                            >
                                <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                    <i class="fa fa-ellipsis-h fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                                </div>
                            </button>
                        </t>
                    </t>
                    <t t-if="!callActionListView.thread">
                        <button class="o_CallActionList_button o_CallActionList_callToggle btn btn-success d-flex m-1 border-0 rounded-circle shadow-none"
                            t-att-class="{ 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                            aria-label="Join Video Call"
                            title="Join Video Call"
                            t-att-disabled="callActionListView.thread.hasPendingRtcRequest"
                            t-on-click="callActionListView.onClickToggleVideoCall">
                            <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                <i class="fa fa-video-camera fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                            </div>
                        </button>
                    </t>
                    <t t-if="callActionListView.thread">
                        <t t-if="callActionListView.thread.rtcInvitingSession and !callActionListView.thread.rtc">
                            <button class="o_CallActionList_button o_CallActionList_callToggle o-isActive btn btn-danger d-flex m-1 border-0 rounded-circle shadow-none"
                                t-att-class="{ 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall }"
                                aria-label="Reject"
                                title="Reject"
                                t-att-disabled="callActionListView.thread.hasPendingRtcRequest"
                                t-on-click="callActionListView.onClickRejectCall">
                                <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                    <i class="fa fa-phone fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                                </div>
                            </button>
                        </t>
                        <button class="o_CallActionList_button o_CallActionList_callToggle btn d-flex m-1 border-0 rounded-circle shadow-none"
                            t-att-aria-label="callActionListView.callButtonTitle"
                            t-att-class="{ 'o-isActive btn-danger': !!callActionListView.thread.rtc, 'o-isSmall p-2': callActionListView.isSmall, 'p-3': !callActionListView.isSmall, 'btn-success': !callActionListView.thread.rtc }"
                            t-att-disabled="callActionListView.thread.hasPendingRtcRequest"
                            t-att-title="callActionListView.callButtonTitle"
                            t-on-click="callActionListView.onClickToggleAudioCall">
                            <div class="o_CallActionList_buttonIconWrapper fa-stack" t-att-class="{ 'o-isSmall': callActionListView.isSmall }">
                                <i class="fa fa-phone fa-stack-1x" t-att-class="{ 'fa-lg': !callActionListView.isSmall }"/>
                            </div>
                        </button>
                    </t>
                </div>
            </div>
        </t>
    </t>

</templates>
