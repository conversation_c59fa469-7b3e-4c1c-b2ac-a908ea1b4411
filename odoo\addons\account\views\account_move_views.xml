<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- VIEWS -->

        <!-- account.move.line (Journal items) -->
        <record id="view_move_line_form" model="ir.ui.view">
            <field name="name">account.move.line.form</field>
            <field name="model">account.move.line</field>
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <form string="Journal Item" create="false">
                    <sheet>
                        <field name="company_id" invisible="1"/>
                        <field name="parent_state" invisible="1"/>
                        <group>
                            <field name="name"/>
                            <field name="partner_id"
                                domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                readonly="1"/>
                        </group>
                        <notebook colspan="4">
                            <page string="Information" name="information">
                                <group>
                                    <group string="Amount">
                                        <field name="account_id" options="{'no_create': True}" domain="[('company_id', '=', company_id)]" readonly="1"/>
                                        <field name="debit" readonly="1"/>
                                        <field name="credit" readonly="1"/>
                                        <field name="balance" readonly="1"/>
                                        <field name="quantity" readonly="1"/>
                                    </group>
                                    <group string="Accounting Documents" colspan="2">
                                        <field name="move_id" readonly="1"/>
                                        <field name="statement_line_id" readonly="True" attrs="{'invisible': [('statement_line_id','=',False)]}"/>
                                    </group>
                                    <group string="Dates">
                                        <field name="date" groups="account.group_account_readonly"/>
                                        <field name="date_maturity"/>
                                    </group>

                                    <group string="Taxes" attrs="{'invisible': [('tax_line_id','=',False), ('tax_ids','=',[])]}">
                                        <field name="tax_line_id" readonly="1" attrs="{'invisible': [('tax_line_id','=',False)]}"/>
                                        <field name="tax_ids" widget="many2many_tags" readonly="1" attrs="{'invisible': [('tax_ids','=',[])]}"/>
                                        <field name="tax_tag_invert" readonly="1" groups="base.group_no_one"/>
                                        <field name="tax_audit"/>
                                    </group>
                                    <group string="Matching" attrs="{'invisible':[('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                        <label for="full_reconcile_id"/>
                                        <div>
                                            <field name="full_reconcile_id" attrs="{'invisible':[('full_reconcile_id','=',False)]}"/>
                                            <field name="matched_debit_ids" invisible="1"/>
                                            <field name="matched_credit_ids" invisible="1"/>
                                            <button name="open_reconcile_view"
                                                class="oe_link"
                                                type="object"
                                                string="-> View partially reconciled entries"
                                                attrs="{'invisible': ['|', ('full_reconcile_id', '!=', False), '&amp;', ('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                            </button>
                                        </div>
                                    </group>
                                    <group string="Currency" groups="base.group_multi_currency">
                                        <field name="currency_id" invisible="1"/>
                                        <field name="amount_currency"/>
                                    </group>
                                    <group string="Product" attrs="{'invisible': [('product_id', '=', False)]}">
                                        <field name="product_id" readonly="1"/>
                                    </group>
                                    <group string="States">
                                        <field name="blocked"/>
                                    </group>
                                    <group string="Analytic" groups="analytic.group_analytic_accounting">
                                        <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"
                                            readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Analytic Lines" name="analytic_lines" groups="analytic.group_analytic_accounting">
                                <field name="date" invisible="1"/>
                                <field name="analytic_line_ids" context="{'tree_view_ref':'analytic.view_account_analytic_line_tree', 'default_general_account_id':account_id, 'default_name': name, 'default_date':date, 'amount': (debit or 0.0)-(credit or 0.0)}"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="account_move_line_view_kanban" model="ir.ui.view">
            <field name="name">account.move.line.kanban</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" create="false" group_create="false">
                    <field name="company_currency_id"/>
                    <field name="partner_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-8">
                                        <field name="account_id"/>
                                    </div>
                                    <strong t-if="record.date_maturity.raw_value" class="col-4 ps-0 text-end">
                                        <i class="fa fa-clock-o" aria-label="Date" role="img" title="Date"/> <field name="date_maturity"/>
                                    </strong>
                                </div>
                                <div class="row mb4" style="min-height: 60px;">
                                    <em class="col-10">
                                        <field name="name"/>
                                    </em>
                                    <div class="col-2 text-end">
                                        <img t-att-src="kanban_image('res.partner', 'avatar_128', record.partner_id.raw_value)" t-att-title="record.partner_id.value" t-att-alt="record.partner_id.value" class="oe_kanban_avatar o_image_24_cover"/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <field name="tax_ids" widget="many2many_tags"/>
                                    </div>
                                    <div class="col-6 text-end">
                                        <t t-if="record.debit.raw_value > 0">
                                            <field name="debit"/><span> (DR)</span>
                                        </t>
                                        <t t-if="record.credit.raw_value > 0">
                                            <field name="credit"/><span> (CR)</span>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="account_move_line_view_kanban_mobile" model="ir.ui.view">
            <field name="inherit_id" ref="account_move_line_view_kanban"/>
            <field name="mode">primary</field>
            <field name="model">account.move.line</field>
            <field name="name">account.move.line.kanban.mobile</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban[hasclass('o_kanban_mobile')]" position="attributes">
                    <attribute name="create">true</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_move_line_pivot" model="ir.ui.view">
            <field name="name">account.move.line.pivot</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <pivot string="Journal Items" sample="1">
                    <field name="journal_id" type="row"/>
                    <field name="date" type="col"/>
                    <field name="balance" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="view_move_line_tree" model="ir.ui.view">
            <field name="name">account.move.line.tree</field>
            <field name="model">account.move.line</field>
            <field eval="100" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Journal Items" create="false" edit="true" expand="context.get('expand', False)" multi_edit="1" sample="1">
                    <field name="move_id" invisible="1"/>
                    <field name="date" readonly="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hide"/>
                    <field name="journal_id" readonly="1" options='{"no_open":True}' optional="hide"/>
                    <field name="move_name" string="Journal Entry" widget="open_move_widget"/>
                    <field name="account_id" options="{'no_open': True, 'no_create': True}" domain="[('company_id', '=', company_id)]" groups="account.group_account_readonly"/>
                    <field name="partner_id" optional="show" attrs="{'readonly':[('move_type', '!=', 'entry')]}"/>
                    <field name="ref" optional="hide" readonly="False"/>
                    <field name="product_id" readonly="1" optional="hide"/>
                    <field name="name" optional="show"/>
                    <field name="tax_ids" widget="many2many_tags" width="0.5" optional="hide" readonly="1"/>
                    <field name="amount_currency" groups="base.group_multi_currency" optional="show" readonly="1" attrs="{'invisible':[('is_same_currency', '=', True)]}"/>
                    <field name="currency_id" readonly="1" groups="base.group_multi_currency" optional="hide" string="Currency" attrs="{'invisible':[('is_same_currency', '=', True)]}"/>
                    <field name="debit" sum="Total Debit" readonly="1"/>
                    <field name="credit" sum="Total Credit" readonly="1"/>
                    <field name="tax_tag_ids" string="Tax Grids" domain="[('applicability', '=', 'taxes')]" widget="many2many_tags" width="0.5" optional="hide"/>
                    <field name="discount_date" string="Discount Date" optional="hide" />
                    <field name="discount_amount_currency" string="Discount Amount" optional="hide" />
                    <field name="tax_line_id" string="Originator Tax" optional="hide" readonly="1"/>
                    <field name="date_maturity" readonly="1" optional="hide"/>
                    <field name="balance" sum="Total Balance" optional="hide" readonly="1"/>
                    <field name="matching_number" readonly="1" optional="show"/>
                    <field name="amount_residual" sum="Total Residual" string="Residual" readonly="1" optional="hide" attrs="{'invisible':[('is_account_reconcile', '=', False)]}"/>
                    <field name="amount_residual_currency" sum="Total Residual in Currency" string="Residual in Currency" readonly="1" optional="hide" attrs="{'invisible':['|', ('is_same_currency', '=', True), ('is_account_reconcile', '=', False)]}"/>
                    <field name="analytic_distribution" widget="analytic_distribution"
                           groups="analytic.group_analytic_accounting"
                           optional="show"
                           options="{'product_field': 'product_id', 'account_field': 'account_id', 'force_applicability': 'optional'}"
                           />

                    <field name="move_type" invisible="1"/>
                    <field name="parent_state" invisible="1"/>
                    <field name="account_type" invisible="1"/>
                    <field name="statement_line_id" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="is_same_currency" invisible="1"/>
                    <field name="is_account_reconcile" invisible="1"/>
                    <field name="sequence" invisible="1"/>
                    <groupby name="partner_id">
                        <button name="edit" type="edit" icon="fa-edit" title="Edit"/>
                    </groupby>
                </tree>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_sales_purchases" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.sales.purchase</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="tax_tag_ids" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_bank_cash" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.bank.cash</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_misc" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_general" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="account_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="balance" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>


        <record id="view_move_line_tree_grouped_partner" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.partner</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="date_maturity" position="attributes"><attribute name="optional">show</attribute></field>
                <field name="balance" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tax_audit_tree" model="ir.ui.view">
            <field name="name">account.move.line.tax.audit.tree</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="matching_number" position="replace">
                    <field name="tax_line_id" string="Tax"/>
                    <field name="tax_base_amount" sum="Total Base Amount"/>
                    <field name="tax_audit"/>
                </field>
                <field name="analytic_distribution" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="debit" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
                <field name="credit" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
                <field name="journal_id" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
            </field>
        </record>

        <record id="account_move_line_graph_date" model="ir.ui.view">
            <field name="name">account.move.line.graph</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" sample="1">
                    <field name="date"/>
                    <field name="balance" operator="+" type='measure'/>
                </graph>
            </field>
        </record>

        <record id="view_account_move_line_filter" model="ir.ui.view">
            <field name="name">account.move.line.search</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <search string="Search Journal Items">
                    <field name="name" string="Journal Item" filter_domain="[
                        '|', '|', '|',
                        ('name', 'ilike', self), ('ref', 'ilike', self), ('account_id', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="date"/>
                    <field name="account_id"/>
                    <field name="account_type"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="move_id" string="Journal Entry" filter_domain="[
                        '|', '|', ('move_id.name', 'ilike', self), ('move_id.ref', 'ilike', self), ('move_id.partner_id', 'ilike', self)]"/>
                    <field name="tax_ids" />
                    <field name="tax_line_id" string="Originator Tax"/>
                    <field name="reconcile_model_id"/>
                    <separator/>
                    <filter string="Unposted" name="unposted" domain="[('parent_state', '=', 'draft')]" help="Unposted Journal Items"/>
                    <filter string="Posted" name="posted" domain="[('parent_state', '=', 'posted')]" help="Posted Journal Items"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('move_id.to_check', '=', True)]"/>
                    <separator/>
                    <filter string="Unreconciled" domain="[('amount_residual', '!=', 0), ('account_id.reconcile', '=', True)]" help="Journal items where matching number isn't set" name="unreconciled"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bank" domain="[('journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cash" domain="[('journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Miscellaneous" domain="[('journal_id.type', '=', 'general')]" name="misc_filter" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Payable" domain="[('account_id.account_type', '=', 'liability_payable'), ('account_id.non_trade', '=', False)]" help="From Trade Payable accounts" name="trade_payable"/>
                    <filter string="Receivable" domain="[('account_id.account_type', '=', 'asset_receivable'), ('account_id.non_trade', '=', False)]" help="From Trade Receivable accounts" name="trade_receivable"/>
                    <filter string="Non Trade Payable" domain="[('account_id.account_type', '=', 'liability_payable'), ('account_id.non_trade', '=', True)]" help="From Non Trade Receivable accounts" name="non_trade_payable" invisible="1"/>
                    <filter string="Non Trade Receivable" domain="[('account_id.account_type', '=', 'asset_receivable'), ('account_id.non_trade', '=', True)]" help="From Non Trade Receivable accounts" name="non_trade_receivable" invisible="1"/>
                    <filter string="P&amp;L Accounts" domain="[('account_id.internal_group', 'in', ('income', 'expense'))]" help="From P&amp;L accounts" name="pl_accounts"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <separator/>
                    <filter string="Report Dates" name="date_between" domain="[('date', '&gt;=', context.get('date_from')), ('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <filter string="Report Dates" name="date_before" domain="[('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <separator/>
                    <filter string="Analytic Accounts" name="analytic_accounts" domain="[('analytic_distribution', 'in', context.get('analytic_ids'))]" invisible="1"/>
                    <group expand="0" string="Group By">
                        <filter string="Journal Entry" name="group_by_move" domain="[]" context="{'group_by': 'move_name'}"/>
                        <filter string="Account" name="group_by_account" domain="[]" context="{'group_by': 'account_id'}"/>
                        <filter string="Partner" name="group_by_partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Date" name="groupby_date" domain="[]" context="{'group_by': 'date'}"/>
                        <filter string="Taxes" name="group_by_taxes" domain="[]" context="{'group_by': 'tax_ids'}"/>
                        <filter string="Tax Grid" name="group_by_tax_tags" domain="[]" context="{'group_by': 'tax_tag_ids'}"/>
                        <filter string="Matching #" name="group_by_matching" domain="[]" context="{'group_by': 'full_reconcile_id'}"/>
                    </group>
                    <searchpanel class="account_root">
                        <field name="account_root_id" icon="fa-filter" groupby="account_id" limit="0"/>
                    </searchpanel>
                </search>
            </field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="view_move_tree" model="ir.ui.view">
            <field name="name">account.move.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree string="Journal Entries" sample="1" decoration-info="state == 'draft'" expand="context.get('expand', False)">
                    <field name="company_currency_id" invisible="1"/>
                    <field name="made_sequence_hole" invisible="1"/>
                    <field name="date"/>
                    <field name="name" decoration-danger="made_sequence_hole"/>
                    <field name="partner_id" optional="show"/>
                    <field name="ref" optional="show"/>
                    <field name="journal_id"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show"/>
                    <field name="amount_total_signed" sum="Total Amount" string="Total" decoration-bf="1"/>
                    <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-success="state == 'posted'"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="to_check" optional="hide" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>

        <record id="view_invoice_tree" model="ir.ui.view">
            <field name="name">account.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree string="Invoices"
                      js_class="account_tree"
                      decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'"
                      expand="context.get('expand', False)"
                      sample="1">
                    <header>
                        <button name="action_register_payment" type="object" string="Register Payment"
                            groups="account.group_account_user"
                            invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt', 'in_invoice', 'in_refund','in_receipt')"/>
                    </header>
                    <field name="made_sequence_hole" invisible="1"/>
                    <field name="name" decoration-bf="1" decoration-danger="made_sequence_hole"/>
                    <field name="invoice_partner_display_name" invisible="context.get('default_move_type') not in ('in_invoice', 'in_refund','in_receipt')" groups="base.group_user" string="Vendor" />
                    <field name="invoice_partner_display_name" invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund','out_receipt')" groups="base.group_user" string="Customer" />
                    <field name="invoice_date" optional="show" invisible="context.get('default_move_type') not in ('in_invoice', 'in_refund','in_receipt')" string="Bill Date"/>
                    <field name="invoice_date" optional="show" invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund','out_receipt')" string="Invoice Date"/>
                    <field name="date" optional="hide" string="Accounting Date"/>
                    <field name="invoice_date_due" widget="remaining_days" optional="show" attrs="{'invisible': [['payment_state', 'in', ('paid', 'in_payment', 'reversed')]]}"/>
                    <field name="invoice_origin" optional="hide" string="Source Document"/>
                    <field name="payment_reference" optional="hide" invisible="context.get('default_move_type') in ('out_invoice', 'out_refund','out_receipt')"/>
                    <field name="ref" optional="hide"/>
                    <field name="invoice_user_id" optional="hide" invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund','out_receipt')" string="Salesperson" widget="many2one_avatar_user"/>
                    <field name="activity_ids" widget="list_activity" optional="show"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="hide"/>
                    <field name="company_id" groups="!base.group_multi_company" invisible="1"/>
                    <field name="amount_untaxed_signed" string="Tax Excluded" sum="Total" optional="show"/>
                    <field name="amount_tax_signed" string="Tax" sum="Total" optional="hide"/>
                    <field name="amount_total_signed" string="Total" sum="Total" decoration-bf="1" optional="show"/>
                    <field name="amount_total_in_currency_signed" string="Total in Currency" groups="base.group_multi_currency"  optional="show"/>
                    <field name="amount_residual_signed" string="Amount Due" sum="Amount Due" optional="hide"/>
                    <field name="currency_id" groups="base.group_multi_currency" optional="hide"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="to_check" optional="hide" widget="boolean_toggle"/>
                    <field name="payment_state"
                           widget="badge"
                           decoration-danger="payment_state == 'not_paid'"
                           decoration-warning="payment_state in ('partial', 'in_payment')"
                           decoration-success="payment_state in ('paid', 'reversed')"
                           attrs="{'invisible': [('payment_state', 'in', ('invoicing_legacy'))]}"
                           optional="show"/>
                    <field name="state" widget="badge" decoration-success="state == 'posted'" decoration-info="state == 'draft'" optional="show"/>
                    <field name="move_type" invisible="context.get('default_move_type', True)"/>
                  </tree>
            </field>
        </record>

        <record id="view_out_invoice_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="banner_route">/account/account_invoice_onboarding</attribute>
                </xpath>
                <field name="currency_id" position="attributes">
                    <attribute name="string">Invoice Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_out_credit_note_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Credit Note Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_in_invoice_bill_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Bill Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_refund_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Refund Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_receipt_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Receipt Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_account_move_kanban" model="ir.ui.view">
            <field name="name">account.move.kanban</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1" js_class="account_documents_kanban">
                    <field name="journal_id"/>
                    <field name="partner_id"/>
                    <field name="ref"/>
                    <field name="date"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-6 o_kanban_record_headings">
                                        <strong>
                                            <span>
                                                <field name="partner_id" attrs="{'invisible': [('partner_id', '=', False)]}" />
                                                <field name="journal_id" attrs="{'invisible': [('partner_id', '!=', False)]}" />
                                            </span>
                                        </strong>
                                    </div>
                                    <div class="col-6 text-end">
                                        <strong><i class="fa fa-clock-o" aria-label="Date" role="img" title="Date"/> <t t-esc="record.date.value"/></strong>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <span><field name="ref"/></span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <span><field name="amount_total_in_currency_signed" widget='monetary'/></span>
                                        <span><field name="currency_id" invisible="1"/></span>
                                    </div>
                                    <div class="col-6">
                                        <span class="float-end">
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'posted': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry" js_class="account_move_form">
                    <header>
                        <!-- Post -->
                        <button name="action_post" string="Post" class="oe_highlight"
                                type="object" groups="account.group_account_invoice" data-hotkey="v"
                                context="{'validate_analytic': True}"
                                attrs="{'invisible': ['|', ('hide_post_button', '=', True), ('move_type', '!=', 'entry')]}"/>
                        <button name="action_post" string="Confirm" class="oe_highlight"
                                type="object" groups="account.group_account_invoice" data-hotkey="v"
                                context="{'validate_analytic': True}"
                                attrs="{'invisible': ['|', '|', ('hide_post_button', '=', True), ('move_type', '=', 'entry'), ('display_inactive_currency_warning','=',True)]}"/>
                        <!-- Send (only invoices) -->
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send &amp; Print"
                                attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), ('is_move_sent', '=', True), ('move_type', 'not in', ('out_invoice', 'out_refund'))]}"
                                class="oe_highlight"
                                data-hotkey="y"/>
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send &amp; Print"
                                attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), ('is_move_sent', '=', False), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}"
                                data-hotkey="y"/>
                        <!-- Register Payment (only invoices / receipts) -->
                        <button name="action_register_payment" id="account_invoice_payment_btn"
                                type="object" class="oe_highlight"
                                attrs="{'invisible': ['|', '|', ('state', '!=', 'posted'), ('payment_state', 'not in', ('not_paid', 'partial')), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"
                                context="{'dont_redirect_to_payments': True}"
                                string="Register Payment" data-hotkey="g"
                                groups="account.group_account_invoice"/>
                        <!-- Preview (only customer invoices) -->
                        <button name="preview_invoice" type="object" string="Preview" data-hotkey="o"
                                title="Preview invoice"
                                attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'out_refund')), ('state', '==', 'cancel')]}"/>
                        <!-- Reverse -->
                        <button name="%(action_view_account_move_reversal)d" string="Reverse Entry"
                                type="action" groups="account.group_account_invoice" data-hotkey="z"
                                attrs="{'invisible': ['|', ('move_type', '!=', 'entry'), '|', ('state', '!=', 'posted'), ('payment_state', '=', 'reversed')]}"/>
                        <button name="action_reverse" string='Add Credit Note'
                                type='object' groups="account.group_account_invoice"
                                attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'in_invoice')), ('state', '!=', 'posted')]}"/>
                        <!-- Cancel -->
                        <button name="button_cancel" string="Cancel Entry" type="object" groups="account.group_account_invoice" data-hotkey="w"
                                attrs="{'invisible' : ['|', '|', ('id', '=', False), ('state', '!=', 'draft'),('move_type', '!=', 'entry')]}"/>
                        <button name="button_cancel" string="Cancel" type="object" groups="account.group_account_invoice" data-hotkey="w"
                                attrs="{'invisible' : ['|', '|', ('id', '=', False), ('state', '!=', 'draft'),('move_type', '==', 'entry')]}"/>
                        <button name="button_draft" string="Reset to Draft" type="object" groups="account.group_account_invoice"
                                attrs="{'invisible' : [('show_reset_to_draft_button', '=', False)]}" data-hotkey="q" />
                        <!-- Set as Checked -->
                        <button name="button_set_checked" string="Set as Checked" type="object" groups="account.group_account_invoice"
                                attrs="{'invisible' : [('to_check', '=', False)]}" data-hotkey="k" />
                        <field name="state" widget="statusbar" statusbar_visible="draft,posted"/>
                    </header>
                    <div class="alert alert-warning mb-0" role="alert"
                         attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('duplicated_ref_ids', '=', [])]}">
                        Warning: this bill might be a duplicate of
                        <button name="open_duplicated_ref_bill_view"
                                type="object"
                                string="one of those bills"
                                class="btn btn-link p-0"
                        />
                    </div>
                    <!-- Invoice outstanding credits -->
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-warning mb-0" role="alert"
                         attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('tax_lock_date_message', '=', False)]}">
                        <field name="tax_lock_date_message" nolabel="1"/>
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', '|', ('move_type', 'not in', ('out_invoice', 'out_receipt')), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding credits</a></bold> for this customer. You can allocate them to mark this invoice as paid.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', '|', ('move_type', 'not in', ('in_invoice', 'in_receipt')), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> for this vendor. You can allocate them to mark this bill as paid.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', '|', ('move_type', '!=', 'out_refund'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> for this customer. You can allocate them to mark this credit note as paid.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', '|', ('move_type', '!=', 'in_refund'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding credits</a></bold> for this vendor. You can allocate them to mark this credit note as paid.
                    </div>
                    <div class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('auto_post', '!=', 'at_date')]}">
                        This move is configured to be posted automatically at the accounting date: <field name="date" readonly="1"/>.
                    </div>
                    <div class="alert alert-info mb-0" role="alert"
                         attrs="{'invisible': ['|', '|', ('state', '!=', 'draft'), ('auto_post', '=', 'no'), ('auto_post', '=', 'at_date')]}">
                         <field name="auto_post" readonly="1"/> auto-posting enabled. Next accounting date: <field name="date" readonly="1"/>.<span attrs="{'invisible': [('auto_post_until', '=', False)]}"> The recurrence will end on <field name="auto_post_until" readonly="1"/> (included).</span>
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-warning mb-0" role="alert"
                         attrs="{'invisible': [('partner_credit_warning', '=', '')]}">
                        <field name="partner_credit_warning"/>
                    </div>
                    <!-- Currency consistency -->
                    <div class="alert alert-warning mb-0" role="alert"
                         attrs="{'invisible': ['|', ('display_inactive_currency_warning', '=', False), ('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}">
                        In order to validate this bill, you must <button class="oe_link" type="object" name="action_activate_currency" style="padding: 0; vertical-align: baseline;">activate the currency of the bill</button>. The journal entries need to be computed by Odoo before being posted in your company's currency.
                    </div>
                    <div class="alert alert-warning mb-0" role="alert"
                         attrs="{'invisible': ['|', ('display_inactive_currency_warning', '=', False), ('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}">
                        In order to validate this invoice, you must <button class="oe_link" type="object" name="action_activate_currency" style="padding: 0; vertical-align: baseline;">activate the currency of the invoice</button>. The journal entries need to be computed by Odoo before being posted in your company's currency.
                    </div>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button name="action_open_business_doc"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    attrs="{'invisible': ['|', '|', ('move_type', '!=', 'entry'), ('id', '=', False), ('payment_id', '=', False)]}"
                                    string="1 Payment">
                            </button>
                            <button name="open_reconcile_view"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    attrs="{'invisible': ['|', '|', ('move_type', '!=', 'entry'), ('id', '=', False), ('has_reconciled_entries', '=', False)]}"
                                    string="Reconciled Items">
                            </button>
                            <button name="open_created_caba_entries"
                                    class="oe_stat_button"
                                    icon="fa-usd"
                                    type="object"
                                    attrs="{'invisible': [('tax_cash_basis_created_move_ids', '=', [])]}"
                                    string="Cash Basis Entries">
                            </button>
                        </div>

                        <!-- Payment status for invoices / receipts -->
                        <widget name="web_ribbon" title="Paid"
                                attrs="{'invisible': ['|', ('payment_state', '!=', 'paid'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                        <widget name="web_ribbon" title="In Payment"
                                attrs="{'invisible': ['|', ('payment_state', '!=', 'in_payment'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                        <widget name="web_ribbon" title="Partial"
                                attrs="{'invisible': ['|', ('payment_state', '!=', 'partial'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                        <widget name="web_ribbon" title="Reversed"
                                bg_color="bg-danger"
                                attrs="{'invisible': [('payment_state', '!=', 'reversed')]}"/>
                         <widget name="web_ribbon" text="Invoicing App Legacy"
                                bg_color="bg-info"
                                attrs="{'invisible': [('payment_state', '!=', 'invoicing_legacy')]}"
                                tooltip="This entry has been generated through the Invoicing app, before installing Accounting. It has been disabled by the 'Invoicing Switch Threshold Date' setting so that it does not impact your accounting."/>

                        <!-- Invisible fields -->
                        <field name="id" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="journal_id" invisible="1"/>
                        <field name="show_name_warning" invisible="1"/>
                        <field name="posted_before" invisible="1"/>
                        <field name="move_type" invisible="1"/>
                        <field name="payment_state" invisible="1" force_save="1"/>
                        <field name="invoice_filter_type_domain" invisible="1"/>
                        <field name="suitable_journal_ids" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                        <field name="company_currency_id" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="bank_partner_id" invisible="1"/>
                        <field name="display_qr_code" invisible="1"/>
                        <field name="show_reset_to_draft_button" invisible="1"/>

                        <field name="invoice_has_outstanding" invisible="1"/>
                        <field name="is_move_sent" invisible="1"/>
                        <field name="has_reconciled_entries" invisible="1"/>
                        <field name="restrict_mode_hash_table" invisible="1"/>
                        <field name="country_code" invisible="1"/>
                        <field name="display_inactive_currency_warning" invisible="1"/>
                        <field name="statement_line_id" invisible="1"/>
                        <field name="payment_id" invisible="1"/>
                        <field name="tax_country_id" invisible="1"/>
                        <field name="tax_cash_basis_created_move_ids" invisible="1"/>
                        <field name="quick_edit_mode" invisible="1"/>
                        <field name="hide_post_button" invisible="1"/>
                        <field name="duplicated_ref_ids" invisible="1"/>
                        <field name="quick_encoding_vals" invisible="1"/>

                        <div class="oe_title">
                            <span class="o_form_label"><field name="move_type" attrs="{'invisible': [('move_type', '=', 'entry')]}" readonly="1" nolabel="1"/></span>

                            <div class="text-warning" attrs="{'invisible': [('show_name_warning', '=', False)]}">The current highest number is <field class="oe_inline" name="highest_name"/>. You might want to put a higher number here.</div>

                            <h1>
                                <field name="name" attrs="{'invisible':[('name', '=', '/'), ('posted_before', '=', False), ('quick_edit_mode', '=', False)],
                                'readonly': [('state', '!=', 'draft')]}" placeholder="Draft"/>

                                <span attrs="{'invisible': ['|', '|', '|', ('state', '!=', 'draft'), ('name', '!=', '/'), ('posted_before', '=', True), ('quick_edit_mode', '=', True)]}">Draft</span>
                            </h1>
                        </div>
                        <group>
                            <group id="header_left_group">

                                <label for="partner_id" string="Customer" style="font-weight:bold;"
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                                <label for="partner_id" string="Vendor" style="font-weight:bold;"
                                       attrs="{'invisible': [('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}"/>
                                <field name="partner_id" widget="res_partner_many2one" nolabel="1"
                                       context="{
                                            'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('out_invoice', 'out_refund', 'out_receipt') and 'customer') or (context.get('default_move_type', 'entry') in ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False,
                                            'show_address': 1, 'default_is_company': True, 'show_vat': True}"
                                       domain="[('type', '!=', 'private'), ('company_id', 'in', (False, company_id))]"
                                       options='{"always_reload": True, "no_quick_create": True}'
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>

                                <field name="partner_shipping_id"
                                       groups="account.group_delivery_invoice_address"
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="quick_edit_total_amount" class="w-50"
                                       attrs="{'invisible': ['|', ('move_type', '=', 'entry'), ('quick_edit_mode', '=', False)], 'readonly': [('state', '!=', 'draft')]}"/>
                                <label for="ref" string="Bill Reference"
                                       attrs="{'invisible':[('move_type', 'not in', ('in_invoice', 'in_receipt', 'in_refund'))]}" />
                                <field name="ref" nolabel="1" attrs="{'invisible':[('move_type', 'not in', ('in_invoice', 'in_receipt', 'in_refund'))]}" />
                                <field name="ref" attrs="{'invisible':[('move_type', 'in', ('in_invoice', 'in_receipt', 'in_refund', 'out_invoice', 'out_refund'))]}"/>
                                <field name="tax_cash_basis_origin_move_id" attrs="{'invisible': [('tax_cash_basis_origin_move_id', '=', False)]}"/>
                                <label name="invoice_vendor_bill_id_label" for="invoice_vendor_bill_id" string="Auto-Complete" class="oe_edit_only"
                                       attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('move_type', '!=', 'in_invoice')]}"/>
                                <field name="invoice_vendor_bill_id" nolabel="1" class="oe_edit_only"
                                       attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('move_type', '!=', 'in_invoice')]}"
                                       domain="[('company_id', '=', company_id), ('partner_id','child_of', [partner_id]), ('move_type','=','in_invoice')]"
                                       placeholder="Select an old vendor bill"
                                       options="{'no_create': True}" context="{'show_total_amount': True}"/>
                            </group>
                            <group id="header_right_group">

                                <!-- Invoice date (only invoices / receipts) -->
                                <label for="invoice_date" string="Invoice Date" style="font-weight:bold;"
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                                <label for="invoice_date" string="Bill Date" style="font-weight:bold;"
                                       attrs="{'invisible': [('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}"/>
                                <field name="invoice_date" nolabel="1" options="{'datepicker': {'warn_future': true}}"
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>

                                <field name="date" string="Accounting Date"
                                       attrs="{
                                            'invisible': [('move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt')), ('quick_edit_mode', '=', False)],
                                            'readonly': [('state', '!=', 'draft')],
                                       }"/>
                                <field name="payment_reference"
                                       attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                                <field name="partner_bank_id"
                                       context="{'default_partner_id': bank_partner_id}"
                                       domain="[('partner_id', '=', bank_partner_id)]"
                                       attrs="{'invisible': [('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))],
                                            'readonly': [('state', '!=', 'draft')]}"/>

                                <!-- Invoice payment terms (only invoices) + due date (only invoices / receipts) -->
                                <div class="o_td_label" attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}">
                                    <label for="invoice_date_due" string="Due Date"
                                           attrs="{'invisible': [('invoice_payment_term_id', '!=', False)]}"/>
                                    <label for="invoice_payment_term_id" string="Payment terms"
                                           attrs="{'invisible': [('invoice_payment_term_id', '=', False)]}"/>
                                </div>
                                <div class="d-flex" attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}">
                                    <field name="invoice_date_due" force_save="1"
                                           placeholder="Date"
                                           attrs="{'invisible': [('invoice_payment_term_id', '!=', False)]}"/>
                                    <span class="o_form_label mx-3 oe_edit_only"
                                          attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('invoice_payment_term_id', '!=', False)]}"> or </span>
                                    <field name="invoice_payment_term_id"
                                           context="{'example_date': invoice_date, 'example_amount': tax_totals['amount_total']}"
                                           placeholder="Terms"/>
                                </div>

                                <label for="journal_id"
                                       groups="account.group_account_readonly"
                                       invisible="context.get('default_journal_id') and context.get('move_type', 'entry') != 'entry'"/>
                                <div name="journal_div"
                                     class="d-flex"
                                     groups="account.group_account_readonly"
                                     invisible="context.get('default_journal_id') and context.get('move_type', 'entry') != 'entry'">
                                    <field name="journal_id"
                                        options="{'no_create': True, 'no_open': True}"
                                        attrs="{'readonly': [('posted_before', '=', True), ('name', 'not in', (False, '', '/'))]}"/>
                                    <span class="oe_inline o_form_label mx-3"
                                          groups="base.group_multi_currency"
                                          attrs="{'invisible': [('move_type', '=', 'entry')]}"> in </span>
                                    <field name="currency_id"
                                           groups="base.group_multi_currency"
                                           attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('move_type', '=', 'entry')]}"/>
                                </div>

                                <field name="currency_id"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"
                                       groups="!account.group_account_readonly,base.group_multi_currency"/>

                            </group>
                        </group>
                        <notebook>
                            <page id="invoice_tab"
                                  name="invoice_tab"
                                  string="Invoice Lines"
                                  attrs="{'invisible': [('move_type', '=', 'entry')]}">
                                <field name="invoice_line_ids"
                                       widget="section_and_note_one2many"
                                       mode="tree,kanban"
                                       context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'default_display_type': 'product',
                                           'quick_encoding_vals': quick_encoding_vals,
                                       }">
                                    <tree editable="bottom" string="Journal Items" default_order="sequence, id">
                                        <control>
                                            <create name="add_line_control" string="Add a line"/>
                                            <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                            <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                                        </control>

                                        <!-- Displayed fields -->
                                        <field name="sequence" widget="handle"/>
                                        <field name="product_id"
                                               optional="show"
                                               widget="many2one_barcode"
                                               domain="
                                                    context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')
                                                    and [('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                                    or [('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                               "/>
                                        <field name="name" widget="section_and_note_text" optional="show"/>
                                        <field name="account_id"
                                               context="{'partner_id': partner_id, 'move_type': parent.move_type}"
                                               groups="account.group_account_readonly"
                                               options="{'no_create': True}"
                                               domain="[('deprecated', '=', False), ('account_type', 'not in', ('asset_receivable', 'liability_payable')), ('company_id', '=', parent.company_id), ('is_off_balance', '=', False)]"
                                               attrs="{'required': [('display_type', 'not in', ('line_note', 'line_section'))]}"/>
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                               groups="analytic.group_analytic_accounting"
                                               optional="show"
                                               options="{'product_field': 'product_id', 'account_field': 'account_id'}"
                                               business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'"/>
                                        <field name="quantity" optional="show"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom_id" string="UoM" groups="uom.group_uom" optional="show"/>
                                        <field name="price_unit" string="Price"/>
                                        <field name="discount" string="Disc.%" optional="hide"/>
                                        <field name="tax_ids" widget="many2many_tags"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', '=', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                               options="{'no_create': True}"
                                               optional="show"/>
                                        <field name="price_subtotal"
                                               string="Subtotal"
                                               groups="account.group_show_line_subtotals_tax_excluded"/>
                                        <field name="price_total"
                                               string="Total"
                                               groups="account.group_show_line_subtotals_tax_included"/>

                                        <!-- Others fields -->
                                        <field name="partner_id" invisible="1"/>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                        <!-- /l10n_in_edi.test_edi_json -->
                                        <!-- required for @api.onchange('product_id') -->
                                        <field name="product_uom_id" invisible="1"/>
                                    </tree>
                                    <kanban class="o_kanban_mobile">
                                        <!-- Displayed fields -->
                                        <field name="name"/>
                                        <field name="product_id"/>
                                        <field name="price_subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                                        <field name="price_total" groups="account.group_show_line_subtotals_tax_included"/>
                                        <field name="quantity"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom_id" groups="uom.group_uom"/>
                                        <field name="price_unit"/>
                                        <templates>
                                            <t t-name="kanban-box">
                                                <div t-attf-class="oe_kanban_card oe_kanban_global_click ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                                    <t t-if="!['line_note', 'line_section'].includes(record.display_type.raw_value)">
                                                        <div class="row g-0">
                                                            <div class="col-2 pe-3">
                                                                <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)" t-att-title="record.product_id.value" t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                            </div>
                                                            <div class="col-10">
                                                                <div class="row">
                                                                    <div class="col">
                                                                        <strong t-esc="record.product_id.value"/>
                                                                    </div>
                                                                    <div class="col-auto">
                                                                        <strong class="float-end text-end">
                                                                            <t t-esc="record.price_subtotal.value" groups="account.group_show_line_subtotals_tax_excluded"/>
                                                                            <t t-esc="record.price_total.value" groups="account.group_show_line_subtotals_tax_included"/>
                                                                        </strong>
                                                                    </div>
                                                                </div>
                                                                <div class="text-muted">
                                                                    Quantity:
                                                                    <t t-esc="record.quantity.value"/>
                                                                    <t t-esc="record.product_uom_id.value" groups="uom.group_uom"/>
                                                                </div>
                                                                <div class="text-muted">
                                                                    Unit Price:
                                                                    <t t-esc="record.price_unit.value"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </t>
                                                    <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <t t-esc="record.name.value"/>
                                                            </div>
                                                        </div>
                                                    </t>
                                                </div>
                                            </t>
                                        </templates>

                                        <!-- Others fields -->
                                        <field name="currency_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                    </kanban>

                                    <!-- Form view to cover mobile use -->
                                    <form>
                                        <sheet>
                                            <field name="display_type" invisible="1"/>
                                            <field name="company_id" invisible="1"/>
                                            <field name="partner_id" invisible="1"/>
                                            <group>
                                                <field name="product_id" widget="many2one_barcode"/>
                                                <field name="quantity"/>
                                                <field name="product_uom_category_id" invisible="1"/>
                                                <field name="product_uom_id" groups="uom.group_uom"/>
                                                <field name="price_unit"/>
                                                <field name="discount" string="Disc.%"/>
                                            </group>
                                            <group>
                                                <field name="account_id" options="{'no_create': True}" domain="[('company_id', '=', company_id)]" context="{'partner_id': partner_id, 'move_type': parent.move_type}"/>
                                                <field name="tax_ids" widget="many2many_tags"/>
                                                <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"/>
                                            </group>
                                            <label for="name" string="Description" attrs="{'invisible': [('display_type', 'in', ('line_note', 'line_section'))]}"/>
                                            <label for="name" string="Section" attrs="{'invisible': [('display_type', '!=', 'line_section')]}"/>
                                            <label for="name" string="Note" attrs="{'invisible': [('display_type', '!=', 'line_note')]}"/>
                                            <field name="name" widget="text"/>
                                            <group>
                                                <field name="price_subtotal" string="Subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                                                <field name="price_total" string="Total" groups="account.group_show_line_subtotals_tax_included"/>
                                            </group>
                                        </sheet>
                                    </form>
                                </field>
                                <group col="12" class="oe_invoice_lines_tab">
                                    <group colspan="8">
                                        <field name="narration" placeholder="Terms and Conditions" colspan="2" nolabel="1"/>
                                    </group>
                                    <!-- Totals (only invoices / receipts) -->
                                    <group colspan="4">
                                        <group class="oe_subtotal_footer oe_right"
                                            attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')),
                                                                       ('payment_state' ,'=', 'invoicing_legacy')]}">

                                            <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2"
                                                   attrs="{'readonly': ['|', ('state', '!=', 'draft'), '&amp;', ('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt')), ('quick_edit_mode', '=', False)]}"/>

                                            <field name="invoice_payments_widget" colspan="2" nolabel="1" widget="payment"/>
                                            <field name="amount_residual" class="oe_subtotal_footer_separator" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                        </group>
                                        <field name="invoice_outstanding_credits_debits_widget"
                                            class="oe_invoice_outstanding_credits_debits"
                                            colspan="2" nolabel="1" widget="payment"
                                            attrs="{'invisible': [('state', '!=', 'posted')]}"/>
                                    </group>
                                </group>
                            </page>
                            <page id="aml_tab" string="Journal Items" groups="account.group_account_readonly" name="aml_tab">
                                <field name="line_ids"
                                       mode="tree,kanban"
                                       context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'line_ids': line_ids,
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'kanban_view_ref': 'account.account_move_line_view_kanban_mobile',
                                       }"
                                       attrs="{'invisible': [('payment_state', '=', 'invoicing_legacy'), ('move_type', '!=', 'entry')]}">
                                    <tree editable="bottom" string="Journal Items" decoration-muted="display_type in ('line_section', 'line_note')" default_order="sequence, id">
                                        <!-- Displayed fields -->
                                        <field name="account_id"
                                               attrs="{
                                                    'required': [('display_type', 'not in', ('line_section', 'line_note'))],
                                                    'invisible': [('display_type', 'in', ('line_section', 'line_note'))],
                                               }"
                                               domain="[('deprecated', '=', False), ('company_id', '=', parent.company_id)]" />
                                        <field name="partner_id"
                                               optional="show"
                                               domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                               attrs="{'column_invisible': [('parent.move_type', '!=', 'entry')]}"/>
                                        <field name="name" widget="section_and_note_text" optional="show"/>
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                               groups="analytic.group_analytic_accounting"
                                               optional="show"
                                               options="{'account_field': 'account_id'}"
                                               business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'"/>
                                        <field name="date_maturity"
                                               optional="hide"
                                               invisible="context.get('view_no_maturity')"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="amount_currency"
                                               groups="base.group_multi_currency"
                                               optional="hide"/>
                                        <field name="currency_id" options="{'no_create': True}"
                                               optional="hide" groups="base.group_multi_currency"
                                               attrs="{'column_invisible': [('parent.move_type', '!=', 'entry')]}"/>
                                        <field name="tax_ids" widget="autosave_many2many_tags"
                                               optional="hide"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                               options="{'no_create': True}"
                                               force_save="1"
                                               attrs="{'readonly': [
                                                    '|', '|',
                                                    ('display_type', 'in', ('line_section', 'line_note')),
                                                    ('tax_line_id', '!=', False),
                                                    '&amp;',
                                                    ('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')),
                                                    ('account_type', 'in', ('asset_receivable', 'liability_payable')),
                                                ]}"/>
                                        <field name="debit"
                                               sum="Total Debit"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))], 'readonly': [('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('display_type', 'in', ('line_section', 'line_note', 'product'))]}"/>
                                        <field name="credit"
                                               sum="Total Credit"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))], 'readonly': [('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('display_type', 'in', ('line_section', 'line_note', 'product'))]}"/>
                                        <field name="balance" invisible="1"/>
                                        <field name="discount_date"
                                               string="Discount Date"
                                               optional="hide"
                                        />
                                        <field name="discount_amount_currency"
                                               string="Discount Amount"
                                               optional="hide"
                                        />

                                        <field name="tax_tag_ids"
                                               widget="many2many_tags"
                                               string="Tax Grids"
                                               optional="show"
                                               options="{'no_create': True}"
                                               domain="[
                                                    ('applicability', '=', 'taxes'),
                                                    '|', ('country_id', '=', parent.tax_country_id),
                                                    ('country_id', '=', False),
                                                ]"/>

                                        <field name="tax_tag_invert" readonly="1" optional="hide" groups="base.group_no_one"/>

                                        <!-- Buttons -->
                                        <button name="action_automatic_entry"
                                                type="object"
                                                icon="fa-calendar"
                                                string="Cut-Off"
                                                aria-label="Change Period"
                                                class="float-end"
                                                attrs="{'invisible': [('account_internal_group', 'not in', ('income', 'expense'))], 'column_invisible': ['|', ('parent.move_type', '=', 'entry'), ('parent.state', '!=', 'posted')]}"
                                                context="{'hide_automatic_options': 1, 'default_action': 'change_period'}"/>

                                        <!-- Others fields -->
                                        <field name="tax_line_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="sequence" invisible="1"/>
                                        <field name="id" invisible="1"/>
                                        <field name="account_internal_group" invisible="1"/>
                                        <field name="account_type" invisible="1"/>
                                    </tree>
                                    <!-- Form view to cover mobile use -->
                                    <form>
                                      <group>
                                        <field name="account_id" domain="[('company_id', '=', parent.company_id), ('deprecated', '=', False)]"/>
                                        <field name="partner_id" domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"/>
                                        <field name="name"/>
                                        <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"/>
                                        <field name="amount_currency" groups="base.group_multi_currency"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                        <field name="debit" sum="Total Debit"/>
                                        <field name="credit" sum="Total Credit"/>
                                        <field name="balance" invisible="1"/>
                                        <field name="tax_ids" string="Taxes Applied" widget="autosave_many2many_tags" options="{'no_create': True}"/>
                                        <field name="date_maturity" required="0" invisible="context.get('view_no_maturity', False)"/>
                                      </group>
                                    </form>
                                </field>
                                <div class="alert alert-info text-center mb-0" role="alert" attrs="{'invisible': ['|', ('payment_state', '!=', 'invoicing_legacy'), ('move_type', '=', 'entry')]}">
                                    <span>This entry has been generated through the Invoicing app, before installing Accounting. Its balance has been imported separately.</span>
                                </div>
                            </page>
                            <page id="other_tab" string="Other Info" name="other_info"
                                  attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                <group id="other_tab_group">
                                    <group string="Invoice"
                                           name="sale_info_group"
                                           attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund'))]}">
                                        <label for="ref" string="Customer Reference" />
                                        <field name="ref" nolabel="1"/>
                                        <field name="user_id" invisible="1" force_save="1"/>
                                        <field name="invoice_user_id" domain="[('share', '=', False)]" widget="many2one_avatar_user"/>
                                        <field name="invoice_origin" string="Source Document" force_save="1" invisible="1"/>
                                        <field name="partner_bank_id"
                                               context="{'default_partner_id': bank_partner_id}"
                                               domain="[('partner_id', '=', bank_partner_id)]"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="qr_code_method"
                                               attrs="{'invisible': [('display_qr_code', '=', False)]}"/>
                                    </group>
                                    <group string="Accounting"
                                           name="accounting_info_group"
                                           attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                        <field name="company_id" groups="base.group_multi_company" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="invoice_incoterm_id"/>
                                        <field name="fiscal_position_id"/>
                                        <field name="invoice_cash_rounding_id" groups="account.group_cash_rounding"/>
                                        <field name="invoice_source_email"
                                               widget="email"
                                               attrs="{'invisible': ['|', ('move_type', 'not in', ('in_invoice', 'in_refund')), ('invoice_source_email', '=', False)]}"/>
                                        <field name="auto_post"
                                               attrs="{'readonly': [('state','!=','draft')]}"/>
                                        <field name="auto_post_until"
                                               attrs="{'invisible': [('auto_post', 'in', ('no', 'at_date'))],
                                                       'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="to_check"/>
                                    </group>
                                </group>
                            </page>
                            <page id="other_tab_entry" string="Other Info" name="other_info"
                                  attrs="{'invisible': [('move_type', '!=', 'entry')]}">
                                <group id="other_tab_entry_group">
                                    <group name="misc_group">
                                        <field name="auto_post"
                                               attrs="{'invisible': [('move_type', '!=', 'entry')], 'readonly': [('state','!=','draft')]}"/>
                                        <field name="reversed_entry_id"
                                               attrs="{'invisible': [('move_type', '!=', 'entry')]}"/>
                                        <field name="auto_post_until"
                                               attrs="{'invisible': [('auto_post', 'in', ('no', 'at_date'))],
                                                       'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="to_check"
                                               attrs="{'invisible': [('move_type', '!=', 'entry')]}" />
                                    </group>
                                    <group>
                                        <field name="fiscal_position_id"/>
                                        <field name="company_id" groups="base.group_multi_company" required="1"/>
                                    </group>
                                </group>
                                <!-- Internal note -->
                                <field name="narration" placeholder="Add an internal note..." nolabel="1" height="50"/>
                            </page>
                        </notebook>
                    </sheet>
                    <!-- Attachment preview -->
                    <div class="o_attachment_preview"
                         attrs="{'invisible': ['|',
                                ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')),
                                ('state', '!=', 'draft')]}" />
                    <!-- Chatter -->
                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_account_move_filter" model="ir.ui.view">
            <field name="name">account.move.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Move">
                    <field name="name" string="Journal Entry" filter_domain="['|', '|', ('name', 'ilike', self), ('ref', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="date"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <filter string="Unposted" name="unposted" domain="[('state', '=', 'draft')]" help="Unposted Journal Entries"/>
                    <filter string="Posted" name="posted" domain="[('state', '=', 'posted')]" help="Posted Journal Entries"/>
                    <separator/>
                    <filter string="Reversed" name="reversed" domain="[('payment_state', '=', 'reversed')]"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('to_check', '=', True)]"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bankoperations" domain="[('journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cashoperations" domain="[('journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Miscellaneous" name="misc_filter" domain="[('journal_id.type', '=', 'general')]" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" name="partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Date" name="by_date" domain="[]" context="{'group_by': 'date'}" help="Journal Entries by Date"/>
                        <filter string="Company" name="by_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_account_invoice_filter" model="ir.ui.view">
            <field name="name">account.invoice.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Invoice">
                    <field name="name" string="Invoice"
                           filter_domain="[
                                '|', '|' , '|', '|',
                                ('name', 'ilike', self), ('invoice_origin', 'ilike', self),
                                ('ref', 'ilike', self), ('payment_reference', 'ilike', self),
                                ('partner_id', 'child_of', self)]"/>
                    <field name="journal_id"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="invoice_user_id" string="Salesperson" domain="[('share', '=', False)]"/>
                    <field name="date" string="Period"/>
                    <field name="line_ids" string="Invoice Line"/>
                    <filter domain="[('invoice_user_id', '=', uid)]" name="myinvoices" help="My Invoices"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]"/>
                    <filter name="posted" string="Posted" domain="[('state', '=', 'posted')]"/>
                    <filter name="cancel" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('to_check', '=', True)]"/>
                    <separator/>
                    <!-- not_paid & partial -->
                    <filter name="open" string="Unpaid" domain="[('state', '=', 'posted'), ('payment_state', 'in', ('not_paid', 'partial'))]"/>
                    <!-- in_payment & paid -->
                    <filter name="closed" string="Paid" domain="[('state', '=', 'posted'), ('payment_state', 'in', ('in_payment', 'paid'))]"/>
                    <!-- reversed -->
                    <filter name="reversed" string="Reversed" domain="[('state', '=', 'posted'), ('payment_state', '=', 'reversed')]"/>
                    <!-- overdue -->
                    <filter name="late" string="Overdue" domain="[
                        ('invoice_date_due', '&lt;', time.strftime('%Y-%m-%d')),
                        ('state', '=', 'posted'),
                        ('payment_state', 'in', ('not_paid', 'partial'))
                    ]" help="Overdue invoices, maturity date passed"/>
                    <separator/>
                    <filter name="invoice_date" string="Invoice Date" date="invoice_date"/>
                    <filter name="date" invisible="context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')" string="Accounting Date" date="date"/>
                    <filter name="due_date" string="Due Date" date="invoice_date_due"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'invoice_user_id'}"/>
                        <filter string="Partner" name="groupy_by_partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Status" name="status" context="{'group_by':'state'}"/>
                        <filter string="Journal" name="groupy_by_journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <separator/>
                        <filter string="Invoice Date" name="invoicedate" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Due Date" name="duedate" context="{'group_by': 'invoice_date_due'}"/>
                        <filter string="Date" name="group_by_date" context="{'group_by': 'date'}"/>
                        <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Sequence Prefix" name="group_by_sequence_prefix" context="{'group_by': 'sequence_prefix'}" invisible="1"/>
                    </group>
               </search>
            </field>
        </record>

        <!-- ACTIONS -->

        <!-- account.move.line (Journal Items) -->

        <record id="action_move_line_select" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="context">{'search_default_account_id': [active_id], 'search_default_posted': 1}</field>
        </record>

        <record id="action_automatic_entry" model="ir.actions.server">
            <field name="name">Automatic Entries</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="binding_model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">action = records.action_automatic_entry()</field>
        </record>

        <record id="action_account_moves_all_a" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'create':0}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_all_grouped_matching" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_posted':1, 'expand':'1'}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_sales" model="ir.actions.act_window">
            <field name="context">{'journal_type':'sales', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_sales':1, 'expand': 1}</field>
            <field name="name">Sales</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_purchase" model="ir.actions.act_window">
            <field name="context">{'journal_type':'purchase', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_purchases':1, 'expand': 1}</field>
            <field name="name">Purchases</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_bank_cash" model="ir.actions.act_window">
            <field name="context">{'journal_type':'bank', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_bank':1, 'search_default_cash':1, 'expand': 1}</field>
            <field name="name">Bank and Cash</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_bank_cash"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_misc" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_misc_filter':1, 'expand': 1}</field>
            <field name="name">Miscellaneous</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_misc"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_ledger_partner" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_partner': 1, 'search_default_posted':1, 'search_default_trade_payable':1, 'search_default_trade_receivable':1, 'search_default_unreconciled':1}</field>
            <field name="name">Partner Ledger</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_partner"/>
            <field name="search_view_id" ref="view_account_move_line_filter"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_all_tree" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="context">{'search_default_partner_id': [active_id], 'default_partner_id': active_id, 'search_default_posted':1}</field>
            <field name="view_id" ref="view_move_line_tree"/>
        </record>

        <record id="action_account_moves_all" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_posted':1}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note')), ('parent_state', '!=', 'cancel')]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">tree,pivot,graph,kanban</field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="action_move_journal_line" model="ir.actions.act_window">
            <field name="name">Journal Entries</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
            <field name="context">{'default_move_type': 'entry', 'search_default_posted':1, 'view_no_maturity': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a journal entry
              </p><p>
                A journal entry consists of several journal items, each of
                which is either a debit or a credit transaction.
              </p>
            </field>
        </record>

        <record id="action_move_out_invoice_type" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_out_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'out_invoice')]</field>
            <field name="context">{'default_move_type': 'out_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a customer invoice
              </p><p>
                Create invoices, register payments and keep track of the discussions with your customers.
              </p>
            </field>
        </record>

        <record id="action_move_out_refund_type" model="ir.actions.act_window">
            <field name="name">Credit Notes</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_out_credit_note_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'out_refund')]</field>
            <field name="context">{'default_move_type': 'out_refund'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a credit note
              </p><p>
                Note that the easiest way to create a credit note is to do it directly
                from the customer invoice.
              </p>
            </field>
        </record>

        <record id="action_move_in_invoice_type" model="ir.actions.act_window">
            <field name="name">Bills</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_in_invoice_bill_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'in_invoice')]</field>
            <field name="context">{'default_move_type': 'in_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a vendor bill
              </p><p>
                Create invoices, register payments and keep track of the discussions with your vendors.
              </p>
            </field>
        </record>

        <record id="action_move_in_refund_type" model="ir.actions.act_window">
            <field name="name">Refunds</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_in_invoice_refund_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'in_refund')]</field>
            <field name="context">{'default_move_type': 'in_refund'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a vendor credit note
              </p><p>
                Note that the easiest way to create a vendor credit note is to do it directly from the vendor bill.
              </p>
            </field>
        </record>

        <record id="action_move_out_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'out_receipt')]</field>
            <field name="context">{'default_move_type': 'out_receipt'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new sales receipt
              </p><p>
                When the sale receipt is confirmed, you can record the customer
                payment related to this sales receipt.
              </p>
            </field>
        </record>

        <record id="action_move_in_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_in_invoice_receipt_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'in_receipt')]</field>
            <field name="context">{'default_move_type': 'in_receipt'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Register a new purchase receipt
              </p><p>
                When the purchase receipt is confirmed, you can record the
                vendor payment related to this purchase receipt.
              </p>
            </field>
        </record>

        <record id="action_move_line_form" model="ir.actions.act_window">
            <field name="name">Entries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.move</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
        </record>

        <record model="ir.actions.server" id="action_move_switch_invoice_to_credit_note">
            <field name="name">Switch into invoice/credit note</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="state">code</field>
            <field name="binding_view_types">form</field>
            <field name="code">
if records:
    action = records.action_switch_invoice_into_refund_credit_note()
            </field>
        </record>

        <record model="ir.actions.server" id="action_check_hash_integrity">
            <field name="name">Data Inalterability Check</field>
            <field name="model_id" ref="account.model_res_company"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">
                action = env.company._action_check_hash_integrity()
            </field>
        </record>

    </data>
</odoo>
