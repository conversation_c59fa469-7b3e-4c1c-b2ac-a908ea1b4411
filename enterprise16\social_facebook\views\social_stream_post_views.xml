<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="social_stream_post_view_kanban" model="ir.ui.view">
        <field name="name">social.stream.post.view.kanban.inherit.facebook</field>
        <field name="model">social.stream.post</field>
        <field name="inherit_id" ref="social.social_stream_post_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='stream_id']" position="after">
                <field name="facebook_author_id"/>
                <field name="facebook_likes_count"/>
                <field name="facebook_user_likes"/>
                <field name="facebook_comments_count"/>
                <field name="facebook_shares_count"/>
                <field name="facebook_reach"/>
                <field name="facebook_is_event_post"/>
            </xpath>
            <xpath expr="//span[hasclass('o_social_stream_post_author_image')]" position="inside">
                <img t-if="record.facebook_author_id.raw_value" t-attf-src="https://graph.facebook.com/v17.0/#{record.facebook_author_id.raw_value}/picture" alt="Author Image"/>
            </xpath>
            <xpath expr="//div[hasclass('o_social_author_information')]" position="inside">
                <span t-if="record.facebook_is_event_post.raw_value" class="text-muted ms-2">added an event</span>
            </xpath>

            <xpath expr="//div[hasclass('o_social_stream_post_message')]" position="inside">
                <div class="o_social_stream_post_facebook_stats px-2 d-flex justify-content-around"
                    t-if="record.media_type.raw_value === 'facebook'">
                    <div t-attf-class="o_social_facebook_likes o_social_subtle_btn ps-2 pe-3 #{record.facebook_user_likes.raw_value ? 'o_social_facebook_user_likes' : ''}"
                        t-on-click.stop="_onFacebookPostLike">
                        <i class="fa fa-thumbs-up me-1" title="Likes"/>
                        <field name="facebook_likes_count" t-if="record.facebook_likes_count.value !== '0'" class="fw-bold"/>
                    </div>
                    <div class="o_social_facebook_comments o_social_comments o_social_subtle_btn px-3"
                         t-on-click.stop="_onFacebookCommentsClick">
                        <i class="fa fa-comments me-1" title="Comments"/>
                        <b t-esc="record.facebook_comments_count.value !== '0' ? record.facebook_comments_count.value : ''"/>
                    </div>
                    <div class="flex-grow-1 d-flex text-muted justify-content-end">
                        <div>
                            <t t-esc="record.facebook_shares_count.value"/>
                            Shares
                        </div>
                        <div class="ms-3">
                            <t t-esc="record.facebook_reach.value"/>
                            Views
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</data>
</odoo>
