<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="sale_order_view_form" model="ir.ui.view">
        <field name="name">sale.order.view.form.inherit.event.booth.sale</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_attendee_list']" position="after">
                <field name="event_booth_count" invisible="1"/>
                <button name="action_view_booth_list" type="object" class="oe_stat_button" icon="fa-university"
                        attrs="{'invisible': [('event_booth_count', '=', 0)]}">
                    <field name="event_booth_count" widget="statinfo" string="Booths"/>
                </button>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='event_ticket_id']" position="after">
                <field name="is_event_booth" optional="hide"/>
                <field name="event_booth_category_id" optional="hide"/>
                <field name="event_booth_pending_ids" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='product_uom_qty']" position="attributes">
                <attribute name="attrs">{'readonly': [('is_event_booth', '=', True)]}</attribute>
            </xpath>
        </field>
    </record>

</data></odoo>
