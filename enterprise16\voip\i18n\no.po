# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Norwegian (https://app.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "%(number of missed calls)s missed calls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "1 missed call"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "2 missed calls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"A hardware error occured while trying to access audio recording device. "
"Please make sure your drivers are up to date and try again."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Accept"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Add to Call Queue"
msgstr ""

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occured involving the audio recording device "
"(%(errorName)s):</br>%(errorMessage)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occurred during the instantiation of the User Agent:</br></br> "
"%(error message)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Are you sure that you want to close this website? There's a call ongoing."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr ""

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call duration: %(min)smin %(sec)ssec"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Call rejected (reason: \"%(reasonPhrase)s\")"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/voip_service.js:0
#, python-format
msgid "Calling %s"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Cancel"
msgstr "Kanseller"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please grant it and try again. Otherwise, make sure this website"
" runs over HTTPS and that your browser is not set to deny access to media "
"devices."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone must be used\n"
"        "
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Connecting..."
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Contacts"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
msgid "Created on"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__direction
msgid "Direction"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
msgid "Display Name"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Edit"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "End Call"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Enter number or name"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Failed to start the user agent. The URL of the websocket server may be "
"wrong. Please have an administrator verify the websocket server URL in the "
"General Settings."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
msgid "ID"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__external_device_number
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__incoming
msgid "Incoming"
msgstr ""

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Incoming call from..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
msgid "Last Modified on"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Make a call using:"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mobile:"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Next activities"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__outgoing
msgid "Outgoing"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
#, python-format
msgid "Phone"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Phone:"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Recent"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Remember ?"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from your database"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Next"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone"
" number set: %(record_names)s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Take call"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP address of your PBX Server"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The connection cannot be made.</br> Please check your configuration."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.</br> (Reason "
"received: %(reasonPhrase)s)"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_secret
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The person you try to contact is currently unavailable."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_username
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
#, python-format
msgid "VoIP"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Voip"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid ""
"You must allow the access to the microphone on your device. Otherwise, the "
"VoIP call receiver will not hear you."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr ""
