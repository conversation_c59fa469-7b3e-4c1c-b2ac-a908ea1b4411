# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_reports
# 
# Translators:
# Qaidjo<PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__count
msgid "# Applicant"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hired
msgid "# Hired"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hiring_ratio
msgid "# Hired Ratio"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__meetings_amount
msgid "# Meetings"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refused
msgid "# Refused"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__applicant_id
msgid "Applicant"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Applicant Name"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__company_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Company"
msgstr "Company"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_uid
msgid "Creator"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__days_in_stage
msgid "Days In Stage"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__display_name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__display_name
msgid "Display Name"
msgstr "Display Name"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__date_closed
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_end
msgid "End Date"
msgstr "End Date"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Group By"
msgstr "Group By"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__is_hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__is_hired
msgid "Hired"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Hiring ratio"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__in_progress
msgid "In Progress"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__job_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__job_id
msgid "Job"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Job Position"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Last 365 Days Applicant"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report____last_update
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report____last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__medium_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Medium"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__process_duration
msgid "Process Duration"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__user_id
msgid "Recruiter"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Recruitment Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_stage_report
msgid "Recruitment Stage Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refuse_reason_id
msgid "Refuse Reason"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__refused
msgid "Refused"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__source_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Source"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_source_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_team_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_source_pivot
msgid "Source Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__stage_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Stage"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_date
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_begin
msgid "Start Date"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__state
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__state
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "State"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_team_action
msgid "Team Performance"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
msgid "This report performs analysis on your recruitment source."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid "This report performs analysis on your recruitment."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
msgid "This report performs analysis on your teams' performance."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Time By Stages"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Time In Stage Analysis"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total Hired"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total Meetings"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#, python-format
msgid "Total applicants"
msgstr ""
