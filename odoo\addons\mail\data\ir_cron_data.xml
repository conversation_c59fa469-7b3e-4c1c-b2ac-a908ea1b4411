<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="ir_cron_mail_scheduler_action" model="ir.cron">
            <field name="name">Mail: Email Queue Manager</field>
            <field name="model_id" ref="model_mail_mail"/>
            <field name="state">code</field>
            <field name="code">model.process_email_queue()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field eval="False" name="doall"/>
        </record>

        <record id="ir_cron_module_update_notification" model="ir.cron">
            <field name="name">Publisher: Update Notification</field>
            <field name="model_id" ref="model_publisher_warranty_contract"/>
            <field name="state">code</field>
            <field name="code">model.update_notification(None)</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">weeks</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field eval="False" name="doall" />
            <field name="priority">1000</field>
        </record>

        <record id="base.ir_cron_act" model="ir.actions.act_window">
            <field name="domain" eval="[('id','!=', ref('mail.ir_cron_module_update_notification'))]"/>
        </record>

        <record id="ir_cron_delete_notification" model="ir.cron">
            <field name="name">Notification: Delete Notifications older than 6 Month</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="model_id" ref="model_mail_notification"/>
            <field name="code">model._gc_notifications(max_age_days=180)</field>
            <field name="state">code</field>
        </record>

        <record id="ir_cron_mail_gateway_action" model="ir.cron">
            <field name="name">Mail: Fetchmail Service</field>
            <field name="model_id" ref="model_fetchmail_server"/>
            <field name="state">code</field>
            <field name="code">model._fetch_mails()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <!-- Active flag is set on fetchmail_server.create/write -->
            <field name="active" eval="False"/>
        </record>

        <record id="ir_cron_send_scheduled_message" model="ir.cron">
            <field name="name">Notification: Send scheduled message notifications</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="model_id" ref="model_mail_message_schedule"/>
            <field name="code">model._send_notifications_cron()</field>
            <field name="state">code</field>
        </record>
    </data>
</odoo>
