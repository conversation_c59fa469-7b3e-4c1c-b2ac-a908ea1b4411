<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageReactionGroup" owl="1">
        <t t-if="messageReactionGroup">
            <button class="o_MessageReactionGroup btn d-flex p-0 border rounded-1" t-att-class="{'o-hasUserReacted border-primary': messageReactionGroup.hasUserReacted, 'bg-view': !messageReactionGroup.hasUserReacted}" t-attf-class="{{ className }}" t-att-title="messageReactionGroup.summary" t-on-click="messageReactionGroup.onClick" t-ref="root">
                <span class="o_MessageReactionGroup_content mx-1">
                    <t t-esc="messageReactionGroup.content"/>
                </span>
                <span class="o_MessageReactionGroup_count mx-1" t-att-class="{'o-hasUserReacted text-primary fw-bolder': messageReactionGroup.hasUserReacted}">
                    <t t-esc="messageReactionGroup.count"/>
                </span>
            </button>
        </t>
    </t>

</templates>
