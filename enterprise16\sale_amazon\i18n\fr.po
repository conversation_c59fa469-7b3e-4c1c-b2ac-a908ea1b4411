# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_amazon
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Manon <PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_amazon
#: model:mail.template,body_html:sale_amazon.order_sync_failure
msgid ""
"<div>\n"
"            <p>The synchronization of the Amazon order with reference <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> encountered an error and was not completed.</p>\n"
"            <p>Unless the order is canceled in SellerCentral, no other synchronization will be attempted.</p>\n"
"            <p>To schedule a new synchronization attempt, proceed as follows:\n"
"                <ol>\n"
"                    <li>Enter the Developer Tools.</li>\n"
"                    <li>Open the form of the Amazon Account on which the order was placed.</li>\n"
"                    <li>Navigate to the Order Follow-up tab.</li>\n"
"                    <li>Set a date for <em>Last Orders Sync</em> that is anterior to the last status update of the order.</li>\n"
"                    <li>Save the changes and click on the <em>SYNC ORDERS</em> button.</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>If the problem persists, contact <a href=\"https://www.odoo.com/help/\">Odoo support</a>.</p>\n"
"        </div>\n"
"        "
msgstr ""
"<div>\n"
"            <p>La synchronisation de la commande Amazon avec la référence <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> a rencontré une erreur et n'a pas pu être complétée.</p>\n"
"            <p>À moins que la commande soit annulée dans SellerCentral, aucune autre synchronisation ne peut être tentée.</p>\n"
"            <p>Pour planifier une nouvelle tentative de synchronisation, veuillez procéder comme suit :\n"
"                <ol>\n"
"                    <li>Allez aux Outils développeurs.</li>\n"
"                    <li>Ouvrez le formulaire du compte Amazon sur lequel la commande a été passée.</li>\n"
"                    <li>Allez à l'onglet Suivi de commande.</li>\n"
"                    <li>Fixez une date pour la <em>Synchronisation des dernières commandes</em> qui est antérieure à la dernière mise à jour du statut de la commande.</li>\n"
"                    <li>Enregistrez les modifications et cliquez sur le bouton <em>SYNCHRONISER LES COMMANDES</em> .</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>Si le problème persiste, veuillez contacter <a href=\"https://www.odoo.com/help/\">Odoo Assistance</a>.</p>\n"
"        </div>\n"
"        "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__api_ref
msgid "API Identifier"
msgstr "Identifiant API"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_access_key
msgid "AWS Access Key"
msgstr "Clé d'accès AWS"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_secret_key
msgid "AWS Secret Key"
msgstr "Clé secrète AWS"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_session_token
msgid "AWS Session Token"
msgstr "Jeton de session AWS"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__account_id
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Account"
msgstr "Compte"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Account Name"
msgstr "Nom du compte"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Active"
msgstr "Actif"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"After validation of the credentials, the marketplaces\n"
"                                                to which this account has access will be\n"
"                                                synchronized and automatically made available."
msgstr ""
"Après validation des identifiants, les marketplaces\n"
"                                                auxquelles ce compte a accès seront\n"
"                                                synchronisées et automatiquement mises à disposition."

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_account
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Amazon Account"
msgstr "Compte Amazon"

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_account_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Accounts"
msgstr "Comptes Amazon"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,field_description:sale_amazon.field_res_users__amazon_email
msgid "Amazon Email"
msgstr "L'e-mail amazon"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "Amazon Item Ref"
msgstr "Réf de l'article Amazon"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_location__amazon_location
msgid "Amazon Location"
msgstr "Localisation d'amazon"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_marketplace
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_form
msgid "Amazon Marketplace"
msgstr "Marketplace Amazon"

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_marketplace_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Marketplaces"
msgstr "Marketplaces Amazon"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_offer
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_offer_id
msgid "Amazon Offer"
msgstr "L'offre d'Amazon"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "Amazon Offers"
msgstr "Les offres d'amazon"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_order_ref
msgid "Amazon Order Ref"
msgstr "Référence de la commande Amazon"

#. module: sale_amazon
#: model:product.template,name:sale_amazon.default_product_product_template
msgid "Amazon Sale"
msgstr "Vente Amazon"

#. module: sale_amazon
#: model:product.template,name:sale_amazon.shipping_product_product_template
msgid "Amazon Shipping"
msgstr "Expédition Amazon"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Amazon Sync Pending"
msgstr "Synchronisation d'Amazon en attente"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_crm_team__amazon_team
msgid "Amazon Team"
msgstr "L'équipe d'amazon"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Amazon accounts correspond to Amazon Seller Central accounts."
msgstr "Les comptes Amazon correspondent aux comptes Amazon \"Seller Central\"."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_marketplace.py:0
#, python-format
msgid "Amazon marketplaces cannot be deleted."
msgstr "Les marketplaces Amazon ne peuvent pas être supprimées."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Amazon move : %s"
msgstr "Envoi Amazon : %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"Since the current carrier doesn't automatically provide a tracking "
"reference, you need to set one manually."
msgstr ""
"Amazon exige qu'une référence de suivi soit fournie avec chaque livraison. "
"Étant donné que le transporteur actuel ne fournit pas automatiquement de "
"référence de suivi, vous devez en définir une manuellement."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"You need to assign a carrier to this delivery."
msgstr ""
"Amazon exige qu'une référence de suivi soit fournie avec chaque livraison. "
"Vous devez affecter un transporteur à cette livraison."

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AE
msgid "Amazon.ae"
msgstr "Amazon.ae"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_CA
msgid "Amazon.ca"
msgstr "Amazon.ca"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_JP
msgid "Amazon.co.jp"
msgstr "Amazon.co.jp"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_UK
msgid "Amazon.co.uk"
msgstr "Amazon.co.uk"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_US
msgid "Amazon.com"
msgstr "Amazon.com"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AU
msgid "Amazon.com.au"
msgstr "Amazon.com.au"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BE
msgid "Amazon.com.be"
msgstr "Amazon.com.be"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BR
msgid "Amazon.com.br"
msgstr "Amazon.com.br"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_MX
msgid "Amazon.com.mx"
msgstr "Amazon.com.mx"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_TR
msgid "Amazon.com.tr"
msgstr "Amazon.com.tr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_DE
msgid "Amazon.de"
msgstr "Amazon.de"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_EG
msgid "Amazon.eg"
msgstr "Amazon.eg"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_ES
msgid "Amazon.es"
msgstr "Amazon.es"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_FR
msgid "Amazon.fr"
msgstr "Amazon.fr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IN
msgid "Amazon.in"
msgstr "Amazon.in"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IT
msgid "Amazon.it"
msgstr "Amazon.it"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_NL
msgid "Amazon.nl"
msgstr "Amazon.nl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_PL
msgid "Amazon.pl"
msgstr "Amazon.pl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SA
msgid "Amazon.sa"
msgstr "Amazon.sa"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SE
msgid "Amazon.se"
msgstr "Amazon.se"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SG
msgid "Amazon.sg"
msgstr "Amazon.sg"

#. module: sale_amazon
#: model:mail.template,name:sale_amazon.order_sync_failure
msgid "Amazon: Order Synchronization Failure"
msgstr "Amazon : Échec de la synchronisation des commandes"

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_orders_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_orders
msgid "Amazon: sync orders"
msgstr "Amazon : Synchronisation des commandes"

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_pickings_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_pickings
msgid "Amazon: sync pickings"
msgstr "Amazon : Synchronisation des transferts"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred"
msgstr "Une erreur s'est produite"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred while linking your account with Amazon."
msgstr ""
"Une erreur s'est produite lors de l'association de votre compte à Amazon."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "Available Marketplaces"
msgstr "Les marketplaces disponibles"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "Back to my account"
msgstr "Retour à mon compte"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__company_id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__company_id
msgid "Company"
msgstr "Société"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Impossible d'établir la connexion à l'API."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the feed URL."
msgstr "Impossible d'établir la connexion à l'URL du flux."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the proxy."
msgstr "Impossible d'établir la connexion au proxy."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/controllers/onboarding.py:0
#, python-format
msgid "Could not find Amazon account with id %s"
msgstr "Impossible de trouver le compte Amazon avec l'identifiant %s"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_date
msgid "Created on"
msgstr "Créé le"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Credentials"
msgstr "Identifiants"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/res_config_settings.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
#, python-format
msgid "Default Products"
msgstr "Produits par défaut"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Error code: %s; description: %s"
msgstr "Code d'erreur : %s ; la description : %s"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__eu-west-1
msgid "Europe"
msgstr "Europe"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-west-2
msgid "Far East"
msgstr "Extrême-Orient"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Amazon"
msgstr "Expédié par Amazon"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Merchant"
msgstr "Expédié par le vendeur"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_channel
msgid "Fulfillment Channel"
msgstr "Canal d'expédition"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fba
msgid "Fulfillment by Amazon"
msgstr "Expédition par Amazon"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fbm
msgid "Fulfillment by Merchant"
msgstr "Expédition par le vendeur"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"Gift message:\n"
"%s"
msgstr ""
"Message cadeau :\n"
"%s"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__id
msgid "ID"
msgstr "ID"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active
msgid ""
"If made inactive, this account will no longer be synchronized with Amazon."
msgstr ""
"S'il est rendu inactif, ce compte ne sera plus synchronisé avec Amazon."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"If the date is set in the past, orders placed on this Amazon Account before the first synchronization of the module might be synchronized with Odoo.\n"
"If the date is set in the future, orders placed on this Amazon Account between the previous and the new date will not be synchronized with Odoo."
msgstr ""
"Si la date est fixée dans le passé, les commandes passées sur ce compte Amazon avant la première synchronisation du module peuvent être synchronisées avec Odoo.\n"
"Si la date est fixée à l'avenir, les commandes passées sur ce compte Amazon entre la précédente et la nouvelle date ne seront pas synchronisées avec Odoo."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"If this account gained access to new marketplaces,"
"                                         synchronize and add them to the "
"current sync marketplaces"
msgstr ""
"Si ce compte a obtenu l'accès à de nouvelles marketplaces, synchronisez-les "
"et ajoutez-les aux synchronisations des marketplaces actuelles."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Inactive"
msgstr "Inactif"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_location
msgid "Inventory Locations"
msgstr "Emplacements de l'inventaire"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__is_follow_up_displayed
msgid "Is Follow Up Displayed"
msgstr "Le suivi est affiché"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token
msgid "LWA Access Token"
msgstr "Jeton d'accès LWA"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__refresh_token
msgid "LWA Refresh Token"
msgstr "Jeton d'actualisation LWA"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__last_orders_sync
msgid "Last Orders Sync"
msgstr "Sync de la dernière commande"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Link with Amazon"
msgstr "Associer à Amazon"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Link your Amazon account with Odoo to start synchronizing your\n"
"                                Amazon orders."
msgstr ""
"Associez votre compte Amazon à Odoo pour commencer à synchroniser vos \n"
"commandes Amazon."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__marketplace_id
msgid "Marketplace"
msgstr "Marketplace"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Marketplaces"
msgstr "Marketplaces"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__name
msgid "Name"
msgstr "Nom"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-east-1
msgid "North America"
msgstr "Amérique du Nord"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_product__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_template__offer_count
msgid "Offer Count"
msgstr "Nombre d'offres"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: code:addons/sale_amazon/models/product_product.py:0
#: code:addons/sale_amazon/models/product_template.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_product_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_template_view_form
#, python-format
msgid "Offers"
msgstr "Offres"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Only available marketplaces can be selected"
msgstr "Seuls les marketplaces disponibles peuvent être sélectionnées"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__order_count
msgid "Order Count"
msgstr "Nombre de commandes"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Order Follow-up"
msgstr "Suivi de commande"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#, python-format
msgid "Orders"
msgstr "Commandes"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_template
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_id
msgid "Product"
msgstr "Produit"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_template_id
msgid "Product Template"
msgstr "Modèle de produit"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Products delivered to Amazon customers must have their respective parts in "
"the same package. Operations related to the product %s were not all "
"confirmed at once."
msgstr ""
"Les produits livrés aux clients Amazon doivent avoir leurs pièces "
"respectives dans le même colis. Les opérations liées au produit %s n'ont pas"
" toutes été confirmées d'un coup."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__region
msgid "Region"
msgstr "Région"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register your Amazon account"
msgstr "Enregistrez votre compte Amazon"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register yours to start synchronizing your orders into Odoo."
msgstr ""
"Enregistrez la vôtre pour commencer à synchroniser vos commandes dans Odoo."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token
msgid "Restricted Data Token"
msgstr "Jeton de données restreint"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__sku
msgid "SKU"
msgstr "SKU"

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_offer_unique_sku
msgid "SKU must be unique for a given account and marketplace."
msgstr "Le SKU doit être unique pour un compte et une marketplace donnés."

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order
msgid "Sales Order"
msgstr "Bon de commande"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_crm_team
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__team_id
msgid "Sales Team"
msgstr "Équipe commerciale"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__user_id
msgid "Salesperson"
msgstr "Vendeur"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Select the marketplace on which your seller account\n"
"                                                was originally created."
msgstr ""
"Sélectionnez la marketplace sur laquelle votre compte vendeur\n"
"                                             a été créé à l'origine."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__seller_central_url
msgid "Seller Central URL"
msgstr "URL Seller Central"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__seller_key
msgid "Seller Key"
msgstr "Clé vendeur"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__base_marketplace_id
msgid "Sign-up Marketplace"
msgstr "Marketplace d'inscription"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__location_id
msgid "Stock Location"
msgstr "Emplacement de stock"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Successfully updated the marketplaces available to this account!"
msgstr "Mise à jour réussie des marketplaces disponibles pour ce compte !"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "Sync Marketplaces"
msgstr "Synchroniser les marketplaces"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Orders"
msgstr "Synchroniser les commandes"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Pickings"
msgstr "Synchroniser les transferts"

#. module: sale_amazon
#: model:mail.template,subject:sale_amazon.order_sync_failure
msgid ""
"Synchronization of Amazon order {{ ctx.get('amazon_order_ref') }} has failed"
msgstr ""
"La synchronisation de la commande Amazon {{ ctx.get('amazon_order_ref') }} a"
" échoué"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__tax_included
msgid "Tax Included"
msgstr "Toutes taxes comprises"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__region
msgid ""
"The Amazon region of the marketplace. Please refer to the Selling Partner "
"API documentation to find the correct region."
msgstr ""
"La région Amazon de la marketplace. Veuillez consulter la documentation de "
"l'API Selling Partner pour trouver la bonne région."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "The Amazon-defined item reference."
msgstr "La référence de l'article définie par Amazon"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__api_ref
msgid "The Amazon-defined marketplace reference."
msgstr "La référence de marketplace définie par Amazon."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order__amazon_order_ref
msgid "The Amazon-defined order reference."
msgstr "La référence de commande définie par Amazon."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__team_id
msgid "The Sales Team assigned to Amazon orders for reporting"
msgstr ""
"L'équipe commerciale affectée aux commandes Amazon pour la génération de "
"rapports"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__sku
msgid "The Stock Keeping Unit."
msgstr "L'unité de gestion des stocks."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "La communication avec l'API a échoué."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Error code: %s; description: %s"
msgstr ""
"La communication avec l'API a échoué.\n"
"Code d'erreur : %s; la description : %s"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,help:sale_amazon.field_res_users__amazon_email
msgid "The encrypted email of the customer. Does not forward mails."
msgstr "L'e-mail chiffré du client. Ne transfère pas les mails."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__last_orders_sync
msgid ""
"The last synchronization date for orders placed on this account. Orders "
"whose status has not changed since this date will not be created nor updated"
" in Odoo."
msgstr ""
"Date de la dernière synchronisation des commandes passées sur ce compte. Les"
" commandes dont le statut n'a pas changé depuis cette date ne seront ni "
"créées ni mises à jour dans Odoo."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__location_id
msgid ""
"The location of the stock managed by Amazon under the Amazon Fulfillment "
"program."
msgstr ""
"L'emplacement du stock géré par Amazon dans le cadre du programme Amazon "
"Fulfillment."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__refresh_token
msgid "The long-lived token that can be exchanged for a new access token."
msgstr ""
"Le jeton de longue durée qui peut être échangé contre un nouveau jeton "
"d'accès."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__marketplace_id
msgid "The marketplace of this offer."
msgstr "La marketplace de cette offre."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "The marketplaces this account has access to."
msgstr "Les marketplaces auxquelles ce compte a accès."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "The marketplaces this account sells on."
msgstr "Les marketplaces sur lesquelles ce compte vend."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_credentials_expiry
msgid "The moment at which the AWS credentials become invalid."
msgstr ""
"Moment auquel les informations d'identification AWS deviennent invalides."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token_expiry
msgid "The moment at which the Restricted Data Token becomes invalid."
msgstr "Le moment auquel le Restricted Data Token devient non-valide."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token_expiry
msgid "The moment at which the token becomes invalid."
msgstr "Moment auquel le jeton devient invalide."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/sale_order.py:0
#, python-format
msgid ""
"The order has been canceled by the Amazon customer while someproducts have "
"already been delivered. Please create a return for this order to adjust the "
"stock."
msgstr ""
"La commande a été annulée par le client Amazon alors que certains produits "
"ont déjà été livrés. Veuillez créer un retour pour cette commande afin "
"d'ajuster le stock."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__base_marketplace_id
msgid ""
"The original sign-up marketplace of this account. Used for authentication "
"only."
msgstr ""
"Marketplace d'inscription d'origine de ce compte. Utilisée uniquement pour "
"l'authentification."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__account_id
msgid "The seller account used to manage this product."
msgstr "Le compte vendeur utilisé pour gérer ce produit."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_access_key
msgid "The short-lived key used to identify the assumed ARN role on AWS."
msgstr ""
"La clé de courte durée utilisée pour identifier le rôle ARN assumé sur AWS."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_secret_key
msgid ""
"The short-lived key used to verify the access to the assumed ARN role on "
"AWS."
msgstr ""
"La clé de courte durée utilisée pour vérifier l'accès au rôle ARN supposé "
"sur AWS."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__restricted_data_token
msgid ""
"The short-lived token used instead of the LWA Access Token to access "
"restricted data"
msgstr ""
"Le jeton de courte durée utilisé à la place du jeton d'accès LWA pour "
"accéder aux données restreintes"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__access_token
msgid "The short-lived token used to query Amazon API on behalf of a seller."
msgstr ""
"Le jeton de courte durée utilisé pour interroger l'API Amazon au nom d'un "
"vendeur."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_session_token
msgid ""
"The short-lived token used to query the SP-API with the assumed ARN role on "
"AWS."
msgstr ""
"Jeton de courte durée utilisé pour interroger la SP-API avec le rôle ARN "
"supposé sur AWS."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__name
msgid "The user-defined name of the account."
msgstr "Nom du compte défini par l'utilisateur."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_marketplace_unique_api_ref
msgid "There can only exist one marketplace for a given API Identifier."
msgstr ""
"Il ne peut exister qu'une seul marketplace pour un identificateur d'API "
"donné."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_unique_amazon_order_ref
msgid ""
"There can only exist one sale order for a given Amazon Order Reference."
msgstr ""
"Il ne peut exister qu'un seul bon de commande pour une référence de commande"
" Amazon donnée."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_line_unique_amazon_item_ref
msgid ""
"There can only exist one sale order line for a given Amazon Item Reference."
msgstr ""
"Il ne peut exister qu'une seule ligne de commande pour une référence "
"d'article Amazon donnée."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"This action will disconnect your account with Amazon and"
"                                          cannot be undone. Are you sure you"
" want to proceed?"
msgstr ""
"Cette action déconnectera votre compte avec Amazon et ne pourra pas être "
"annulée. Êtes-vous sur de vouloir continuer ?"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_picking
msgid "Transfer"
msgstr "Transfert"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Unlink account"
msgstr "Dissocier le compte"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Update Available Marketplaces"
msgstr "Mettre à jour les marketplaces disponibles"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "View on Seller Central"
msgstr "Afficher sur Seller Central"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Warning"
msgstr "Avertissement"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Whether the picking must be notified to Amazon or not."
msgstr "Si le transfert doit être notifié à Amazon ou non."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__tax_included
msgid "Whether the price includes the tax amount or not."
msgstr "Si le prix inclut le montant de la taxe ou non."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_location__amazon_location
msgid ""
"Whether this location represents the stock of a seller managed by Amazon "
"under the Amazon Fulfillment program or not."
msgstr ""
"Si cet emplacement représente ou non le stock d'un vendeur géré par Amazon "
"dans le cadre du programme Amazon Fulfillment."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_crm_team__amazon_team
msgid "Whether this sales team is associated with Amazon orders or not."
msgstr ""
"Que cette équipe comemrciale soit associée ou non aux commandes Amazon."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "You first need to authorize the Amazon account %s."
msgstr "Vous devez d'abord autoriser le compte Amazon %s."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"You first need to set the marketplaces to synchronize for the Amazon account"
" %s."
msgstr ""
"Vous devez d'abord définir les marketplaces à synchroniser pour le compte "
"Amazon %s."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"You reached the maximum number of requests for this operation; please try "
"again later."
msgstr ""
"Vous avez atteint le nombre maximum de requêtes pour cette opération ; "
"Veuillez réessayer plus tard."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Your Amazon account is linked with Odoo."
msgstr "Votre compte Amazon est lié à Odoo."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"[%s] %s\n"
"Condition: %s - %s"
msgstr ""
"[%s] %s\n"
"Condition : %s - %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Delivery Charges for %s"
msgstr "[%s] Frais de livraison pour %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Gift Wrapping Charges for %s"
msgstr "[%s] Frais d'emballage cadeau pour %s"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "e.g. American Market"
msgstr "par ex. Marché américain"
