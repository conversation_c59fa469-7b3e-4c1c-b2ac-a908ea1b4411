<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- AlWasead Main Divisions -->
        <record id="division_engineering_affairs" model="hr.division">
            <field name="name">Engineering Affairs Division</field>
            <field name="code">ENG</field>
            <field name="sequence">10</field>
            <field name="description">Responsible for all engineering projects, technical design, construction supervision, and engineering consultancy services. Includes civil, mechanical, electrical, and specialized engineering departments.</field>
            <field name="active">True</field>
        </record>

        <record id="division_administrative_affairs" model="hr.division">
            <field name="name">Administrative Affairs Division</field>
            <field name="code">ADM</field>
            <field name="sequence">20</field>
            <field name="description">Manages human resources, legal affairs, office administration, document management, and general administrative support services across the organization.</field>
            <field name="active">True</field>
        </record>

        <record id="division_finance" model="hr.division">
            <field name="name">Finance Division</field>
            <field name="code">FIN</field>
            <field name="sequence">30</field>
            <field name="description">Oversees financial planning, accounting, budgeting, financial reporting, cost control, and financial analysis for all company operations and projects.</field>
            <field name="active">True</field>
        </record>

        <record id="division_supply" model="hr.division">
            <field name="name">Supply Division</field>
            <field name="code">SUP</field>
            <field name="sequence">40</field>
            <field name="description">Manages procurement, logistics, inventory management, vendor relations, and supply chain operations to support all company divisions and projects.</field>
            <field name="active">True</field>
        </record>



    </data>
</odoo>
