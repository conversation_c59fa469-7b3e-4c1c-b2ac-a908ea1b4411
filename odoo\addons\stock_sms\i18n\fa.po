# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_sms
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: stock_sms
#: model:sms.template,body:stock_sms.sms_template_data_stock_delivery
msgid ""
"\n"
"                {{ (object.company_id.name + ': We are glad to inform you that your order n° ' + object.origin + ' has been shipped.' if object.origin else object.company_id.name + ': We are glad to inform you that your order has been shipped.') + (' Your tracking reference is ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}\n"
"            "
msgstr ""
"```html\n"
"{{ (object.company_id.name + ': خوشحالیم به اطلاعتان برسانیم که سفارش شماره ' + object.origin + ' شما ارسال شده است.' if object.origin else object.company_id.name + ': خوشحالیم به اطلاعتان برسانیم که سفارش شما ارسال شده است.') + (' شماره پیگیری شما ' + (object.carrier_tracking_ref) + ' است.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}\n"
"```"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Cancel"
msgstr "لغو"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_company
msgid "Companies"
msgstr "شرکت"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Confirm"
msgstr "تایید کردن"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_confirm_stock_sms
msgid "Confirm Stock SMS"
msgstr "تأیید پیام کوتاه موجودی"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: stock_sms
#: model:sms.template,name:stock_sms.sms_template_data_stock_delivery
msgid "Delivery: Send by SMS Text Message"
msgstr "ارسال: ارسال از طریق پیامک"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Disable SMS"
msgstr "غیر فعال کردن پیامک"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__has_received_warning_stock_sms
msgid "Has Received Warning Stock Sms"
msgstr "پیامک هشدار موجودی دریافت کرده است"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__id
msgid "ID"
msgstr "شناسه"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms____last_update
msgid "Last Modified on"
msgstr "آخرین اصلاح در"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__pick_ids
msgid "Pick"
msgstr "برداشت"

#. module: stock_sms
#: code:addons/stock_sms/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
#, python-format
msgid "SMS"
msgstr "پیامک"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_move_sms_validation
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Confirmation"
msgstr "پیامک تایید"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Template"
msgstr "قالب پیامک"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_move_sms_validation
msgid "SMS Validation with stock move"
msgstr "تایید پیامکی با انتقال موجودی"

#. module: stock_sms
#: model:ir.model.fields,help:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,help:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
msgid "SMS sent to the customer once the order is delivered."
msgstr "پیامک به مشتری ارسال شد وقتی که سفارش تحویل داده شد."

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_stock_picking
msgid "Transfer"
msgstr "انتقال"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid ""
"You are about to confirm this Delivery Order by SMS Text Message.<br/>\n"
"                This feature can easily be disabled from the Settings of Inventory or by clicking on \"Disable SMS\".<br/>"
msgstr ""
"شما در شرف تایید این سفارش تحویل از طریق پیامک هستید.<br/>\n"
"                 این ویژگی را می توان به راحتی از تنظیمات موجودی یا با کلیک بر روی \"غیرفعال کردن پیامک\" غیرفعال کرد.<br/>"
