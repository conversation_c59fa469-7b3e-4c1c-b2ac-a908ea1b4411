<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Signature Request Template Views -->
    <record id="sign_template_view_kanban" model="ir.ui.view">
        <field name="name">sign.template.kanban</field>
        <field name="model">sign.template</field>
        <field name="arch" type="xml">
            <kanban quick_create="false" class="o_sign_template_kanban" banner_route="/sign/check_iap_credits" default_order="create_date desc" sample="1" js_class="sign_kanban" action="go_to_custom_template" type="object">
                <field name="active"/>
                <field name="attachment_id"/>
                <field name="color"/>
                <field name="create_date"/>
                <field name="favorited_ids"/>
                <field name="responsible_count"/>
                <field name="create_uid"/>
                <field name="signed_count"/>
                <field name="in_progress_count"/>
                <field name="is_sharing"/>
                <templates>
                    <div t-name="kanban-box" t-attf-class="o_sign_sticky_bottom oe_kanban_global_click {{kanban_color(record.color.raw_value)}}">
                        <div class="oe_kanban_main">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <div class="o_kanban_record_title">
                                        <t t-if="record.favorited_ids.raw_value.indexOf(user_context.uid) &lt; 0">
                                            <a type="object" name="toggle_favorited" aria-label="Not in favorites, set it" title="Not in favorites, add it"
                                                class="fa fa-lg fa-star-o favorite_sign_button"/>
                                        </t>
                                        <t t-else="">
                                            <a type="object" name="toggle_favorited" aria-label="In favorites, remove it" title="In favorites, remove it"
                                                class="fa fa-lg fa-star favorite_sign_button_enabled favorite_sign_button"/>
                                        </t>
                                        <span class="ps-4"><field name="display_name"/></span>
                                    </div>
                                </div>
                                <div class="o_dropdown_kanban dropdown">
                                    <a role="button" class="dropdown-toggle o_kanban_manage_toggle_button o_left o-no-caret btn" data-bs-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                    <div class="o_kanban_card_manage_pane dropdown-menu" role="menu">
                                        <a role="menuitem" type="object" name="go_to_custom_template" class="d-none d-md-block dropdown-item" context="{'sign_edit_call': 'sign_template_edit'}">Modify Template</a>
                                        <a role="menuitem" type="object" name="open_requests" class="dropdown-item">Signed Documents</a>
                                        <a role="menuitem" type="object" name="stop_sharing" class="dropdown-item" attrs="{'invisible': [('is_sharing', '=', False)]}">Stop Sharing</a>
                                        <a role="menuitem" type="action" name="%(sign.action_sign_duplicate_template_with_pdf)d" class="dropdown-item">Use Layout</a>
                                        <a role="menuitem" type="object" name="toggle_active" class="dropdown-item">
                                            <t t-if="!record.active.raw_value">Restore</t>
                                            <t t-if="record.active.raw_value">Archive</t>
                                        </a>
                                        <a role="menuitem" type="delete" class="dropdown-item">Delete</a>

                                        <ul role="menu" class="oe_kanban_colorpicker menu-item" data-field="color"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <em><field name="create_date" widget="date"/></em>
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left w-50 mobile-xs-hide">
                                    <button name="%(sign.action_sign_send_request)d" type="action" class="btn btn-primary btn-sm mt8" context="{'sign_directly_without_mail': 0}">Send</button>
                                    <button name="%(sign.action_sign_send_request)d" type="action" class="btn btn-primary btn-sm mt8" context="{'sign_directly_without_mail': 1}">Sign Now</button>
                                    <button name="open_shared_sign_request" type="object" class="btn btn-secondary btn-sm mt8" attrs="{'invisible': ['|', ('active', '=', False), ('responsible_count', '&gt;', 1)]}">Share</button>
                                </div>
                                <div class="oe_kanban_bottom_left w-50 mobile-xs-show">
                                    <button name="dropdown" class="btn btn-primary btn-sm mt8 dropdown-toggle kanban_ignore_dropdown" type="button" data-bs-toggle="dropdown">Action</button>
                                    <div class="dropdown-menu kanban_ignore_dropdown" role="menu">
                                        <button name="%(sign.action_sign_send_request)d" type="action" class="btn btn-primary btn-sm dropdown-item" context="{'sign_directly_without_mail': 0}">Send</button>
                                        <button name="%(sign.action_sign_send_request)d" type="action" class="btn btn-primary btn-sm dropdown-item" context="{'sign_directly_without_mail': 1}">Sign Now</button>
                                        <button name="open_shared_sign_request" type="object" class="btn btn-secondary btn-sm dropdown-item" attrs="{'invisible': ['|', ('active', '=', False), ('responsible_count', '&gt;', 1)]}">Share</button>
                                    </div>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <div class="float-end mt8">
                                        <span class="me-2" title="Number of documents in progress for this template.">
                                            <span class="fa fa-hourglass-half"/>
                                            <field name="in_progress_count"/>
                                        </span>
                                        <span class="me-2" title="Number of documents signed for this template.">
                                            <span class="fa fa-check ms-1"/>
                                            <field name="signed_count"/>
                                        </span>
                                    </div>
                                     <field name="user_id" widget="many2one_avatar_user"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="sign_template_view_tree" model="ir.ui.view">
        <field name="name">sign.template.tree</field>
        <field name="model">sign.template</field>
        <field name="arch" type="xml">
            <tree sample="1" js_class="sign_list">
                <field name="attachment_id"/>
                <field name="create_date"/>
                <field name="sign_item_ids"/>
                <field name="active" invisible="1"/>
                <field name="responsible_count" invisible="1"/>
                <field name="is_sharing"/>
                <button name="%(sign.action_sign_send_request)d" string="Send" type="action" context="{'sign_directly_without_mail': 0}"/>
                <button name="%(sign.action_sign_send_request)d" string="Sign Now" type="action" context="{'sign_directly_without_mail': 1}"/>
                <button name="open_shared_sign_request" string="Share" type="object" attrs="{'invisible': ['|', ('active', '=', False), ('responsible_count', '&gt;', 1)]}"/>
            </tree>
        </field>
    </record>

    <record id="sign_template_view_form" model="ir.ui.view">
        <field name="name">sign.template.form</field>
        <field name="model">sign.template</field>
        <field name="arch" type="xml">
            <form create="false">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="open_requests" class="oe_stat_button" icon="fa-pencil-square-o" >
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="signed_count"/>
                                </span>
                                <span class="o_stat_text">Signed Document</span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1>
                            <div class="o_row">
                                <field name="name"  placeholder="Name of the file" nolabel="1"/>
                            </div>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="attachment_id" invisible="1"/>
                            <field name="responsible_count" invisible="1"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}" placeholder="Tags"/>
                        </group>
                        <group>
                            <field name="redirect_url" widget="url"/>
                            <field name="redirect_url_text"  attrs="{'invisible':[('redirect_url','=','')]}"/>
                            <field name="authorized_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <field name="group_ids" groups="sign.manage_template_access" widget="many2many_tags" />
                            <field name="user_id" domain="[('share', '=', False)]" groups="sign.manage_template_access" options="{'no_open': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="document" string="Document">
                            <field name="datas" widget="pdf_viewer"/>
                        </page>
                        <page string="Fields" name="signatures">
                            <field name="sign_item_ids"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="sign_template_view_search" model="ir.ui.view">
        <field name="name">sign.template.search</field>
        <field name="model">sign.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="attachment_id" string="Document Name"/>
                <field name="tag_ids"/>
                <filter name="my_templates" string="My Templates" domain="[('user_id', '=', uid)]"/>
                <filter name="favorite" string="My Favorites" domain="[('favorited_ids', 'in', uid)]"/>
                <searchpanel>
                    <field name="tag_ids" select="multi" icon="fa-tag" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="sign_template_tour_trigger_action" model="ir.actions.server">
        <field name="name">Template Sample Contract.pdf trigger</field>
        <field name="model_id" ref="sign.model_sign_template"/>
        <field name="state">code</field>
        <field name="code">action = model.trigger_template_tour()</field>
    </record>

    <record id="sign_template_action" model="ir.actions.act_window">
        <field name="name">Templates</field>
        <field name="res_model">sign.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="sign_template_view_search"/>
        <field name="context" eval="{'search_default_favorite': 1}"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No template yet
            </p>
            <p>
                You're one click away from automating your signature process!
            </p>
            <p>Upload a PDF file to create your first template</p>
            <p>- or -</p>
            <a type="action" name="%(sign.sign_template_tour_trigger_action)d" class="btn btn-primary text-white o_sign_sample">Try our sample document</a>
        </field>
    </record>

    <!-- Signature Item Views -->
    <record id="sign_item_view_tree" model="ir.ui.view">
        <field name="name">sign.item.tree</field>
        <field name="model">sign.item</field>
        <field name="arch" type="xml">
            <tree default_order="page,posY,posX,id" editable="bottom">
                <field name="type_id"/>
                <field name="required"/>
                <field name="responsible_id"/>
                <field name="page"/>
                <field name="posX"/>
                <field name="posY"/>
                <field name="width"/>
                <field name="height"/>
            </tree>
        </field>
    </record>

    <record id="sign_item_view_form" model="ir.ui.view">
        <field name="name">sign.item.form</field>
        <field name="model">sign.item</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group string="Information">
                            <field name="type_id"/>
                            <field name="required"/>
                            <field name="responsible_id"/>
                        </group>

                        <group string="Display">
                            <field name="page"/>
                            <field name="posX"/>
                            <field name="posY"/>
                            <field name="width"/>
                            <field name="height"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Signature Item Type Views -->
    <record id="sign_item_type_view_tree" model="ir.ui.view">
        <field name="name">sign.item.type.tree</field>
        <field name="model">sign.item.type</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="item_type"/>
                <field name="auto_field" groups="base.group_system"/>
            </tree>
        </field>
    </record>

    <record id="sign_item_type_view_form" model="ir.ui.view">
        <field name="name">sign.item.type.form</field>
        <field name="model">sign.item.type</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <field name="item_type"/>
                        <field name="auto_field" groups="base.group_system"/>
                    </group>
                    <group>
                        <group>
                            <label for="default_width"/>
                            <div class="o_row">
                                <field name="default_width"/>
                                <span>(1.0 = full page size)</span>
                            </div>

                            <label for="default_height"/>
                            <div class="o_row">
                                <field name="default_height"/>
                                <span>(1.0 = full page size)</span>
                            </div>
                        </group>
                        <group>
                            <field name="tip"/>
                            <field name="placeholder"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="sign_item_type_action" model="ir.actions.act_window">
        <field name="name">Signature Item Type</field>
        <field name="res_model">sign.item.type</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Signature Item Party Views -->
    <record id="sign_item_role_view_tree" model="ir.ui.view">
        <field name="name">sign.item.role.tree</field>
        <field name="model">sign.item.role</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="name" string="Role Name"/>
                <field name="auth_method"/>
                <field name="change_authorized"/>
                <field name="color" widget="color_picker"/>
            </tree>
        </field>
    </record>

    <record id="sign_item_role_view_form" model="ir.ui.view">
        <field name="name">sign.item.role.form</field>
        <field name="model">sign.item.role</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name" placeholder="e.g. Employee"/></h1>
                    </div>
                    <group>
                        <field name="auth_method"/>
                        <field name="change_authorized"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="sign_item_role_action" model="ir.actions.act_window">
        <field name="name">Signature Item Role</field>
        <field name="res_model">sign.item.role</field>
        <field name="view_mode">tree,form</field>
    </record>

     <record id="sign_settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'sign', 'bin_size': False}</field>
    </record>

    <record id="sign_report_green_savings_action" model="ir.actions.report">
        <field name="name">Ecological Savings by using Electronical Signatures</field>
        <field name="model">sign.template</field>
        <field name="report_type">qweb-html</field>
        <field name="report_name">sign.green_savings_report</field>
        <field name="report_file">sign.green_savings_report</field>
        <field name="multi" eval="True"/>
        <field name="paperformat_id" ref="base.paperformat_batch_deposit"/>
        <field name="binding_model_id" ref="model_sign_template"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Menus -->
    <menuitem id="menu_document" name="Sign" web_icon="sign,static/description/icon.svg" sequence="180"/>
    <menuitem id="sign.sign_template_menu" name="Dashboard" parent="sign.menu_document" action="sign_template_action" sequence="10"/>
    <menuitem id="sign_request_menu" name="Documents" parent="menu_document" sequence="20"/>
    <menuitem id="sign_reports" name="Reports" parent="menu_document" sequence="99"/>
    <menuitem id="menu_sign_configuration" sequence="100" name="Configuration" parent="menu_document"/>
    <!-- Menus -->
    <menuitem id="sign.sign_item_settings_menu" name="Settings" parent="sign.menu_sign_configuration" action="sign_settings_action" groups="sign.group_sign_manager"/>
    <menuitem id="sign.sign_item_type_menu" name="Field Types" parent="sign.menu_sign_configuration" action="sign_item_type_action" groups="base.group_no_one"/>
    <menuitem id="sign.sign_item_role_menu" name="Roles" parent="sign.menu_sign_configuration" action="sign_item_role_action" groups="sign.group_sign_manager"/>
    <menuitem id="sign_report_green_savings" name="Green Savings" parent="sign_reports" action="sign.sign_report_green_savings_action"/>
    <menuitem id="sign_request_documents" name="All documents" parent="sign_request_menu" action="sign_request_action"/>
    <menuitem id="sign_request_waiting_for_me" name="Waiting for me" parent="sign_request_menu" action="sign_request_waiting_for_me_action"/>
    <menuitem id="sign_request_my_requests" name="My requests" parent="sign_request_menu" action="sign_request_my_requests_action"/>
</odoo>
