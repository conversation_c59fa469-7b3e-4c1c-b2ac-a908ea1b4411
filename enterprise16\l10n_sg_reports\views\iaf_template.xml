<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="iras_audit_file_company_infos">
            <CompanyInfo>
                <CompanyName><t t-esc="Company['CompanyName']"/></CompanyName>
                <CompanyUEN><t t-esc="Company['CompanyUEN']"/></CompanyUEN>
                <GSTNo><t t-esc="Company['GSTNo']"/></GSTNo>
                <PeriodStart><t t-esc="Company['PeriodStart']"/></PeriodStart>
                <PeriodEnd><t t-esc="Company['PeriodEnd']"/></PeriodEnd>
                <IAFCreationDate><t t-esc="Company['IAFCreationDate']"/></IAFCreationDate>
                <ProductVersion><t t-esc="Company['ProductVersion']"/></ProductVersion>
                <IAFVersion><t t-esc="Company['IAFVersion']"/></IAFVersion>
            </CompanyInfo>
        </template>

        <template id="iras_audit_file_purchases_infos">
            <Purchase
                t-att-PurchaseTotalSGD="Purchases['PurchaseTotalSGD']"
                t-att-GSTTotalSGD="Purchases['GSTTotalSGD']"
                t-att-TransactionCountTotal="Purchases['TransactionCountTotal']">
                <t t-foreach="Purchases['lines']" t-as="line">
                    <PurchaseLines>
                        <SupplierName><t t-esc="line['SupplierName']"/></SupplierName>
                        <SupplierUEN><t t-esc="line['SupplierUEN']"/></SupplierUEN>
                        <InvoiceDate><t t-esc="line['InvoiceDate']"/></InvoiceDate>
                        <InvoiceNo><t t-esc="line['InvoiceNo']"/></InvoiceNo>
                        <t t-if="line['PermitNo']">
                            <PermitNo><t t-esc="line['PermitNo']"/></PermitNo>
                        </t>
                        <LineNo><t t-esc="line['LineNo']"/></LineNo>
                        <ProductDescription><t t-esc="line['ProductDescription']"/></ProductDescription>
                        <PurchaseValueSGD><t t-esc="line['PurchaseValueSGD']"/></PurchaseValueSGD>
                        <GSTValueSGD><t t-esc="line['GSTValueSGD']"/></GSTValueSGD>
                        <TaxCode><t t-esc="line['TaxCode']"/></TaxCode>
                        <FCYCode><t t-esc="line['FCYCode']"/></FCYCode>
                        <PurchaseFCY><t t-esc="line['PurchaseFCY']"/></PurchaseFCY>
                        <GSTFCY><t t-esc="line['GSTFCY']"/></GSTFCY>
                    </PurchaseLines>
                </t>
            </Purchase>
        </template>

        <template id="iras_audit_file_sales_infos">
            <Supply
                t-att-SupplyTotalSGD="Sales['SupplyTotalSGD']"
                t-att-GSTTotalSGD="Sales['GSTTotalSGD']"
                t-att-TransactionCountTotal="Sales['TransactionCountTotal']">
                <t t-foreach="Sales['lines']" t-as="line">
                    <SupplyLines>
                        <CustomerName><t t-esc="line['CustomerName']"/></CustomerName>
                        <CustomerUEN><t t-esc="line['CustomerUEN']"/></CustomerUEN>
                        <InvoiceDate><t t-esc="line['InvoiceDate']"/></InvoiceDate>
                        <InvoiceNo><t t-esc="line['InvoiceNo']"/></InvoiceNo>
                        <LineNo><t t-esc="line['LineNo']"/></LineNo>
                        <ProductDescription><t t-esc="line['ProductDescription']"/></ProductDescription>
                        <SupplyValueSGD><t t-esc="line['SupplyValueSGD']"/></SupplyValueSGD>
                        <GSTValueSGD><t t-esc="line['GSTValueSGD']"/></GSTValueSGD>
                        <TaxCode><t t-esc="line['TaxCode']"/></TaxCode>
                        <t t-if="line['Country']">
                            <Country><t t-esc="line['Country']"/></Country>
                        </t>
                        <FCYCode><t t-esc="line['FCYCode']"/></FCYCode>
                        <SupplyFCY><t t-esc="line['SupplyFCY']"/></SupplyFCY>
                        <GSTFCY><t t-esc="line['GSTFCY']"/></GSTFCY>
                    </SupplyLines>
                </t>
            </Supply>
        </template>

        <template id="iras_audit_file_gldata_infos">
            <GLData
                t-att-TotalDebit="GlData['TotalDebit']"
                t-att-TotalCredit="GlData['TotalCredit']"
                t-att-TransactionCountTotal="GlData['TransactionCountTotal']"
                t-att-GLTCurrency="GlData['GLTCurrency']">
                <t t-foreach="GlData['lines']" t-as="line">
                    <GLDataLines>
                        <TransactionDate><t t-esc="line['TransactionDate']"/></TransactionDate>
                        <AccountID><t t-esc="line['AccountID']"/></AccountID>
                        <AccountName><t t-esc="line['AccountName']"/></AccountName>
                        <TransactionDescription><t t-esc="line['TransactionDescription']"/></TransactionDescription>
                        <t t-if="line['Name']">
                            <Name><t t-esc="line['Name']"/></Name>
                        </t>
                        <t t-if="line['TransactionID']">
                            <TransactionID><t t-esc="line['TransactionID']"/></TransactionID>
                        </t>
                        <t t-if="line['SourceDocumentID']">
                            <SourceDocumentID><t t-esc="line['SourceDocumentID']"/></SourceDocumentID>
                        </t>
                        <t t-if="line['SourceType']">
                            <SourceType><t t-esc="line['SourceType']"/></SourceType>
                        </t>
                        <Debit><t t-esc="line['Debit']"/></Debit>
                        <Credit><t t-esc="line['Credit']"/></Credit>
                        <Balance><t t-esc="line['Balance']"/></Balance>
                    </GLDataLines>
                </t>
            </GLData>
        </template>

        <template id="iras_audit_file_xml">
            <Company>
                <t t-call="l10n_sg_reports.iras_audit_file_company_infos"></t>
                <t t-call="l10n_sg_reports.iras_audit_file_purchases_infos"></t>
                <t t-call="l10n_sg_reports.iras_audit_file_sales_infos"></t>
                <t t-call="l10n_sg_reports.iras_audit_file_gldata_infos"></t>
            </Company>
        </template>
    </data>
</odoo>

