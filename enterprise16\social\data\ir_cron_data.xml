<?xml version='1.0' encoding='utf-8'?>
<odoo noupdate="1">
    <record id="ir_cron_post_scheduled" model="ir.cron">
        <field name="name">Social: Publish Scheduled Posts</field>
        <field name="model_id" ref="model_social_post"/>
        <field name="state">code</field>
        <field name="numbercall">-1</field>
        <field name="code">model._cron_publish_scheduled()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>
</odoo>
