<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="documents_project.public_page_layout" inherit_id="documents.public_page_layout" primary="True">
        <xpath expr="//t[@t-set='share_redirect_URL']" position="replace"/>
    </template>
    <template id="documents_project.share_page" inherit_id="documents.share_page" primary="True">
        <xpath expr="//t[@t-call='documents.public_page_layout']" position="attributes">
            <attribute name="t-call">documents_project.public_page_layout</attribute>
        </xpath>
        <xpath expr="//t[@t-set='zip_URL']" position="attributes">
            <attribute name="t-value">base_url + 'download?' + (('access_token=' + access_token) if access_token else '')</attribute>
        </xpath>
        <xpath expr="//t[@t-set='download_URL']" position="attributes">
            <attribute name="t-value">base_url + str(document.id) + '/download?' + (('access_token=' + access_token) if access_token else '')</attribute>
        </xpath>
        <xpath expr="//section[hasclass('o_docs_share_multible_bar')]/div[hasclass('mb16')]" position="replace">
            <div class="col-sm-6 mb16">
                <b class="text-white h2"><t t-esc="len(document_ids)"></t></b> documents shared
            </div>
        </xpath>
        <xpath expr="//div[hasclass('col-lg-3')]/article/figure" position="replace">
            <figure class="o_card_left d-flex position-relative flex-column flex-nowrap justify-content-between m-0">
                <div class="o_image" t-att-data-mimetype="document.mimetype" t-attf-style="{{ document.mimetype and 'image/' in document.mimetype and 'background-image:None;' '' }}"/>
                <div t-if="document.mimetype and'image/' in document.mimetype" class="o_image_preview" t-attf-style="background-image:url({{ base_url }}{{ document.id }}/thumbnail?{{ keep_query() }})"/>
            </figure>
        </xpath>
        <xpath expr="//article/div[hasclass('o_card_right')]/img" position="attributes">
            <attribute name="t-attf-src">{{ base_url }}{{ document.id }}/avatar?{{ keep_query() }}</attribute>
        </xpath>
        <xpath expr="//form" position="attributes">
            <attribute name="t-attf-action">{{ base_url }}upload?{{ keep_query() }}</attribute>
        </xpath>
    </template>
    <template id="documents_project.portal_my_task" inherit_id="project.portal_my_task">
        <xpath expr="//li[@id='task-nav']/ul" position="inside">
            <li class="nav-item" id="nav-chat" t-if="task.project_use_documents and task.shared_document_ids">
                <a class="nav-link ps-3" target="_blank" t-attf-href="/{{ 'my/projects/' + str(project_id) + '/task' if project_id else 'my/tasks' }}/{{ task.id }}/documents?{{ keep_query() }}">
                    Documents
                </a>
            </li>
        </xpath>
    </template>
    <template id="documents_project.portal_tasks_list" inherit_id="project.portal_tasks_list">
        <xpath expr="//div[@name='project_name']/span" position="attributes">
            <attribute name="class" separator=" " add="text-dark"/>
        </xpath>
        <xpath expr="//div[@name='project_name']" position="inside">
            <span t-if="tasks[0].project_id.use_documents and tasks[0].project_id.shared_document_count" class="ms-auto">
                <a class="btn btn-secondary px-1 py-0" target="_blank" t-attf-href="/my/projects/{{ tasks[0].project_id.id }}/documents?{{ keep_query() }}">
                    <t t-out="tasks[0].project_id.shared_document_count"/> Documents
                </a>
            </span>
        </xpath>
    </template>
</odoo>
