# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# de comptes"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "%s (max %s chars)"
msgstr "%s (max %s caractères)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ", you must first link a social media."
msgstr ", vous devez d'abord lier un réseau social."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Une erreur est survenue lors de la liaison à votre compte</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"Voir le post\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<small class=\"pe-1\">Clicks:</small>"
msgstr "<small class=\"pe-1\">Clics :</small>"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Félicitations ! Revenez dans quelques minutes pour vérifier vos "
"statistiques.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Actif"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add"
msgstr "Ajouter"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Ajouter un post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add a Stream"
msgstr "Ajouter un flux"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Ajouter un flux"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add an image"
msgstr "Ajouter une image"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "All Companies"
msgstr "Toutes les sociétés"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Tous les posts liés aux réseaux sociaux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Comptes autorisés"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Archivé"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Des comptes sont-ils disponibles ?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Joindre des images"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
#, python-format
msgid "Audience"
msgstr "Public"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Tendance du public"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Auteur du lien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Nom de l'auteur"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid ""
"Author link to the external social.media (ex: link to the Twitter Account)."
msgstr ""
"Lien de l'auteur vers le social.media externe (ex: lien vers le compte "
"Twitter)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr "Avant la publication, les liens seront convertis pour être traçables."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Par flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "Jeton CSRF"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Date calendrier"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Campagne"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Campagnes"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Les campagnes sont utilisées pour centraliser vos efforts marketing et "
"suivre leurs résultats."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts ?"
msgstr "Peut lier des comptes ?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Cancel"
msgstr "Annuler"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Choisissez quel <b>compte</b> vous souhaitez lier en premier."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Click to refresh."
msgstr "Cliquer pour rafraîchir. "

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Clics"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Comment Image"
msgstr "Commenter l'image"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Société"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Connecting Problem"
msgstr "Problème de connexion "

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' Twitter account"
msgstr ""
"Contient le pseudonyme de réseau social de la personne qui a créé ce compte."
" Ex : '@odoo.official' pour le compte Twitter 'Odoo'"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Contenu du poste social qui est post-traité (liens raccourcis, UTM, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr "Contrôle si les flux sont supportés sur ce réseau social."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Contrôle si nous pouvons lier les comptes ou non."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Créer une campagne"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Créer un post"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
#, python-format
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Créez d'autres comptes pour %(media_names)s pour cette société ou demandez à"
" %(company_names)s de partager ses comptes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Créé le"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Définit si ce compte a les statistiques sur le public/les engagements/les stories.\n"
"        Les comptes avec des statistiques sont affichés sur le tableau de bord."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Définit si ce compte a des tendances statistiques ou pas."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Delete Comment"
msgstr "Supprimer le commentaire"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Mode démo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Description"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Comptes de développeur"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "Do you really want to delete %s"
msgstr "Êtes-vous sûr de vouloir supprimer %s "

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"En raison de restrictions de longueur, les posts suivants ne peuvent pas être publiés :\n"
"%s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Edit Comment"
msgstr "Modifier le commentaire"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Activer le mode démo"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module.<br/>\n"
"                                        This must never be used on a production database!"
msgstr ""
"Activer cette option et charger les données de démonstration pour tester le module social.<br/>\n"
"Cela ne doit jamais être utilisé sur une base de données de production !"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Engagement"
msgstr "Engagement"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Tendance d'engagement"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Chaque fois qu'un compte est créé, un utm.medium est aussi créé et lié à ce "
"compte"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Échec"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Raison de l'échec"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Feed Posts"
msgstr "Posts du feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Nombre de posts du feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Date de publication formatée"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr "Public général du compte (Pages aimées, abonnés, ...)"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "Go back to Odoo"
msgstr "Revenir à Odoo"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Allez au"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Pseudonyme / Nom court"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Happy with the result? Let's post it!"
msgstr "Heureux du résultat ? Publiez-le !"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "A des statistiques de compte"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "A un message"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "A des tendances ?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Image"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "URL de l'image"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Image Url"
msgstr "URL de l'image"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "URL des images"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Images qui ont été partagées sur ce post."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Insights"
msgstr "Statistiques"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Il semblerait qu'il y ait un problème avec le lien des réseaux sociaux, "
"cliquez ici pour lier le compte à nouveau"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "It will appear in the Feed once it has posts to display."
msgstr "Il apparaîtra dans le feed une fois qu'il aura des posts à afficher."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account____last_update
#: model:ir.model.fields,field_description:social.field_social_live_post____last_update
#: model:ir.model.fields,field_description:social.field_social_media____last_update
#: model:ir.model.fields,field_description:social.field_social_post____last_update
#: model:ir.model.fields,field_description:social.field_social_post_template____last_update
#: model:ir.model.fields,field_description:social.field_social_stream____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post_image____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_type____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's <b>connect</b> to Facebook, LinkedIn or Twitter."
msgstr "<b>Connectez-vous</b> à Facebook, LinkedIn ou Twitter."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Créons votre propre tableau de bord des <b>réseaux sociaux</b>."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's start posting."
msgstr "Commençons à publier."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Like"
msgstr "J'aime"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Likes"
msgstr "J'aime"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Description du lien"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Link Image"
msgstr "Image du lien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL de l'image du lien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Titre du lien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "URL du lien"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Link a new account"
msgstr "Lier un nouveau compte"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Lier un compte"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Lier un compte"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Liez un compte à une entreprise pour limiter son utilisation ou laissez-le "
"vide pour permettre à toutes les entreprises de l'utiliser."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Lien du post en direct sur le réseau cible."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Lier des comptes sociaux"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Lien vers les statistiques externes du compte social"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Le lien avec les réseaux sociaux externes est rompu"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Liste des comptes pouvant être sélectionnés pour ce post."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Posts en direct par les réseaux sociaux"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Load more comments..."
msgstr "Charger plus de commentaires..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_main_attachment_id
#: model:ir.model.fields,field_description:social.field_social_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Main actions"
msgstr "Actions principales"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Longueur maximale du post"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Réseau"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Type de réseau"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Message"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__message_length
#: model:ir.model.fields,field_description:social.field_social_post_template__message_length
msgid "Message Length"
msgstr "Longueur du message"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Message posted"
msgstr "Message publié"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Message posted partially. These are the ones that couldn't be posted: <br>%s"
msgstr ""
"Message publié partiellement. Voici ceux qui n'ont pas pu être publiés : "
"<br>%s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Messages"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"La plupart des social.live.posts passent directement de l'état Prêt à Publié/Echoué puisqu'ils sont le résultat d'un appel unique à l'API.\n"
"        L'état \"En cours\" est aussi disponible pour les publications envoyées par lot (comme les notifications push)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Mes posts"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Mes flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Nom"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New Post"
msgstr "Nouveau post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New content available"
msgstr "Nouveau contenu disponible"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Next"
msgstr "Suivant"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivante"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Pas encore de compte social !"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Pas encore de flux sociaux !"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Aucun flux ajouté pour le moment !"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "No comments yet."
msgstr "Pas encore de commentaires."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "No social accounts configured, please contact your administrator."
msgstr ""
"Aucun compte social configuré, veuillez contacter votre administrateur."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Number of Followers of your channel"
msgstr "Nombre d'abonnés de votre chaîne"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Nombre de clics"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Nombre d'interactions (j'aime, partages, commentaires,...) avec les posts"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "Nombre de contacts avec le post (j'aime, commentaires ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Nombre de contacts avec vos posts (j'aime, commentaires, ...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Re-tweets, ...)."
msgstr ""
"Nombre de stories créées à partir de vos posts (partages, retweets, ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Nombre de fois que des personnes ont participé à vos posts (j'aime, "
"commentaires, partages,...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Retweets...)"
msgstr ""
"Nombre de fois que des personnes ayant participé à votre chaîne ont créé du "
"contenu sur le fil de leurs amis ou abonnés (Partages, retweets,..)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Or add"
msgstr "Ou ajouter"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Le pourcentage d'augmentation/diminution d'audience sur une période définie."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Le pourcentage d'augmentation/diminution de l'engagement sur une période "
"définie."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Le pourcentage d'augmentation/diminution des stories sur une période "
"définie."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Veuillez préciser au moins un compte sur lequel publier (pour les "
"identifiants du post %s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Post"
msgstr "Publier"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Post Image"
msgstr "Publier une image"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Post Images"
msgstr "Publier des images"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Publier un lien"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Publier un message"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Publier maintenant"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Publier un lien vers le social.media externe (ex: lien vers le post Facebook"
" réel)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Publier sur"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Publié"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "En cours"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Posts"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Posts par compte"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Posts By Accounts"
msgstr "Posts par comptes"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Appuyez sur Entrer pour publier. Appuyez sur Maj+Entrer pour insérer un saut"
" de ligne."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Prévisualiser votre post "

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Previous"
msgstr "Précedent"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr ""
"Publiez immédiatement votre post ou planifier la publication pour plus tard."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Publié par"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Date de publication"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Date de publication"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Prêt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Réseaux sociaux associés"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, Twitter, ...)."
msgstr "Réseaux sociaux associés (Facebook, Twitter, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Compte social associé"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Reply"
msgstr "Répondre"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Réessayer"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Planifier"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Planifier ultérieurement"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Planifié"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Date prévue"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Selected accounts (%s) do not match the selected company (%s)"
msgstr ""
"Les comptes sélectionnés (%s) ne correspondent pas à l'entreprise "
"sélectionnée (%s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Envoyer maintenant"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Séquence utilisée pour organiser les flux (pour la vue Kanban 'Feed' "
"principalement)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Définissez un nombre maximum de caractères pouvant être publiés dans la "
"publication. 0 pour aucune limite."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Paramètres"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Compte social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Comptes sociaux"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Post en direct"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Manager social"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
msgid "Social Marketing"
msgstr "Marketing social"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Social Media"
msgstr "Réseaux sociaux"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Posts des réseaux sociaux"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Post"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Modèle de post social"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Modèles de posts sociaux"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Posts sociaux"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Flux social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Post du flux social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Pièce jointe au flux social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Flux sociaux"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Utilisateur social"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
#: model:ir.cron,cron_name:social.ir_cron_post_scheduled
msgid "Social: Publish Scheduled Posts"
msgstr "Social : publier des posts programmés"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
#, python-format
msgid "Something went wrong while posting the comment."
msgstr "Une erreur s'est produite lors de la publication du commentaire."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
#, python-format
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Vous n'êtes pas autorisé à reconnecter ce compte, veuillez contacter votre "
"administrateur."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Source"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Champ technique spécial qui contient un dict contenant les noms des posts en"
" direct par identifiant de média (utilisé pour la vue Kanban)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Statistiques du lien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Statut"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
#, python-format
msgid "Stories"
msgstr "Stories"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Tendance des stories"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "Stream Added (%s)"
msgstr "Flux ajouté (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Post du flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Diffuser des images de post"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "URL des images de flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Types de flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Nom du type de flux (technique)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Flux activés"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr "Sous-posts qui seront publiés sur chaque compte social sélectionné."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Synchronize"
msgstr "Synchroniser"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "Le champ 'message' est obligatoire pour l'identifiant du post %s"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Les comptes sur lesquels ce post sera publié."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"Le nom de l'auteur du post basé sur des informations tierces (par ex. 'John "
"Doe')."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Le post est considérée comme 'Publié' quand tous les sous-posts (une par "
"compte) sont soit 'Echoué' ou 'Publié'"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "La date de publication, basée sur les informations d'un tiers."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"La raison pour laquelle un post n'a pas pu être publié sur le réseau social "
"(par ex. erreur de connexion, publications doubles,...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "L'image des réseaux sociaux associée"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Les réseaux sociaux liés aux comptes sociaux sélectionnés."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Il y a des erreurs de publication sur les sous-posts."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Ce jeton peut être utilisé pour vérifier qu'une demande entrante d'un "
"fournisseur social n'a pas été falsifiée."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Titre"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "To add a stream"
msgstr "Pour ajouter un flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Type"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
msgid "UTM Campaign"
msgstr "Campagne UTM"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "Medium UTM"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "Source UTM"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Unknown error"
msgstr "Erreur inconnue"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "Uploaded file does not seem to be a valid image."
msgstr "Le fichier téléchargé ne semble pas être une image valide."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app.\n"
"                            Those credentials are provided in the developer section of your professional social media account."
msgstr ""
"Utiliser vos propres comptes de développeur sur notre application sociale.\n"
"Ces identifiants sont fournis dans la section développeur de votre compte de réseau social professionnel."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Utilisé pour faire des comparaisons lorsque nous devons restreindre "
"certaines fonctionnalités à un réseau spécifique ('facebook', 'twitter', "
"...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "View"
msgstr "Vue"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Quand"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Quand le post global est publié. Les dates de sous-posts peuvent être "
"différentes selon le média."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Ajoutera des images à vos posts (si le réseau social le prend en charge)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a comment..."
msgstr "Écrire un commentaire..."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Write a message to get a preview of your post."
msgstr "Écrivez un message pour avoir un aperçu de votre post."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a reply..."
msgstr "Écrire une réponse..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Rédigez un post attrayant, ajoutez des images et programmez-le pour qu'il "
"soit publié plus tard sur plusieurs plateformes en même temps."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "You can only move posts that are scheduled."
msgstr "Vous ne pouvez déplacer que les publications planifiées."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Vous ne pouvez pas supprimer ces mediums UTM car ils sont liés aux comptes sociaux suivants dans Social : \n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Vous ne pouvez pas supprimer ces sources UTM car elles sont liées à des posts sociaux dans Social :\n"
"%(utm_sources)s"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Votre post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "a Stream from an existing account"
msgstr "un Flux d'un compte existant"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "avant publication."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "comment/reply"
msgstr "commentaire/réponse"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "tableau de bord"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "for"
msgstr "pour"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "replies..."
msgstr "réponses..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"pour garder un œil sur vos propres publications et surveiller toutes les "
"activités sociales."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "pour lier vos comptes et commencer à publier."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "pour commencer à publier depuis Odoo."
