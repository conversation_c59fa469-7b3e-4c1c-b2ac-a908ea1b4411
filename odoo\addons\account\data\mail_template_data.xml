<?xml version="1.0" ?>
<odoo>

    <!-- Mail template are declared in a NOUPDATE block
         so users can freely customize/delete them -->
    <data noupdate="1">
        <!--Email template -->
        <record id="email_template_edi_invoice" model="mail.template">
            <field name="name">Invoice: Sending</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="email_from">{{ (object.invoice_user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="subject">{{ object.company_id.name }} Invoice (Ref {{ object.name or 'n/a' }})</field>
            <field name="description">Sent to customers with their invoices in attachment</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear
        <t t-if="object.partner_id.parent_id">
            <t t-out="object.partner_id.name or ''"><PERSON></t> (<t t-out="object.partner_id.parent_id.name or ''">Azure Interior</t>),
        </t>
        <t t-else="">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t>,
        </t>
        <br /><br />
        Here is your
        <t t-if="object.name">
            invoice <span style="font-weight:bold;" t-out="object.name or ''">INV/2021/05/0005</span>
        </t>
        <t t-else="">
            invoice
        </t>
        <t t-if="object.invoice_origin">
            (with reference: <t t-out="object.invoice_origin or ''">SUB003</t>)
        </t>
        amounting in <span style="font-weight:bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 143,750.00</span>
        from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <t t-if="object.payment_state in ('paid', 'in_payment')">
            This invoice is already paid.
        </t>
        <t t-else="">
            Please remit payment at your earliest convenience.
            <t t-if="object.payment_reference">
                <br /><br />
                Please use the following communication for your payment: <span style="font-weight:bold;" t-out="object.payment_reference or ''">INV/2021/05/0005</span>.
            </t>
        </t>
        <br /><br />
        Do not hesitate to contact us if you have any questions.
        <t t-if="not is_html_empty(object.invoice_user_id.signature)">
            <br /><br />
            <t t-out="object.invoice_user_id.signature or ''">--<br/>Mitchell Admin</t>
        </t>
    </p>
</div>
            </field>
            <field name="report_template" ref="account_invoices"/>
            <field name="report_name">Invoice_{{ (object.name or '').replace('/','_') }}{{ object.state == 'draft' and '_draft' or '' }}</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_template_data_payment_receipt" model="mail.template">
            <field name="name">Payment: Payment Receipt</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="subject">{{ object.company_id.name }} Payment Receipt (Ref {{ object.name or 'n/a' }})</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Sent manually to customer when clicking on 'Send receipt by email' in payment action</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear <t t-out="object.partner_id.name or ''">Azure Interior</t><br/><br/>
        Thank you for your payment.
        Here is your payment receipt <span style="font-weight:bold;" t-out="(object.name or '').replace('/','-') or ''">BNK1-2021-05-0002</span> amounting
        to <span style="font-weight:bold;" t-out="format_amount(object.amount, object.currency_id) or ''">$ 10.00</span> from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <br/><br/>
        Do not hesitate to contact us if you have any questions.
        <br/><br/>
        Best regards,
        <t t-if="not is_html_empty(user.signature)">
            <br/><br/>
            <t t-out="user.signature or ''">--<br/>Mitchell Admin</t>
        </t>
    </p>
</div>
</field>
            <field name="report_template" ref="account.action_report_payment_receipt"/>
            <field name="report_name">{{ (object.name or '').replace('/','-') }}</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
        <!-- Credit note template -->
        <record id="email_template_edi_credit_note" model="mail.template">
            <field name="name">Credit Note: Sending</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="email_from">{{ (object.invoice_user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="subject">{{ object.company_id.name }} Credit Note (Ref {{ object.name or 'n/a' }})</field>
            <field name="description">Sent to customers with the credit note in attachment</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear
        <t t-if="object.partner_id.parent_id">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t> (<t t-out="object.partner_id.parent_id.name or ''">Azure Interior</t>),
        </t>
        <t t-else="">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t>,
        </t>
        <br /><br />
        Here is your
        <t t-if="object.name">
            credit note <span style="font-weight:bold;" t-out="object.name or ''">RINV/2021/05/0001</span>
        </t>
        <t t-else="">
            credit note
        </t>
        <t t-if="object.invoice_origin">
            (with reference: <t t-out="object.invoice_origin or ''">SUB003</t>)
        </t>
        amounting in <span style="font-weight:bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 143,750.00</span>
        from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <br /><br />
        Do not hesitate to contact us if you have any questions.
        <t t-if="not is_html_empty(object.invoice_user_id.signature)">
            <br /><br />
            <t t-out="object.invoice_user_id.signature or ''">--<br/>Mitchell Admin</t>
        </t>
    </p>
</div>
            </field>
            <field name="report_template" ref="account_invoices"/>
            <field name="report_name">Credit_note_{{ (object.name or '').replace('/','_') }}{{ object.state == 'draft' and '_draft' or '' }}</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
