# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_referral
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid ""
" You've gained %(gained)s points with this progress.<br/>It makes you a new "
"total of %(total)s points. Visit <a href=\"%(url)s\">this link</a> to pick a"
" gift!"
msgstr ""
" Bạn đã giành được %(gained)s điểm nhờ tiến trình này.<br/>Bạn có tổng điểm "
"mới là %(total)s điểm. Hãy bấm vào <a href=\"%(url)s\">liên kết này</a> để "
"chọn một món quà!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__referral_hired
msgid "# Hired by Referral"
msgstr "# Đã tuyển theo giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<b>Responsible: </b>"
msgstr "<b>Phụ trách: </b>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-check me-2\" title=\"Hired\"/>"
msgstr "<i class=\"fa fa-check me-2\" title=\"Đã tuyển\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-square-o\" title=\"Open\"/>"
msgstr "<i class=\"fa fa-square-o\" title=\"Mở\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-danger fa fa-times\" title=\"Closed\"/>"
msgstr "<i class=\"text-danger fa fa-times\" title=\"Đã đóng\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-success fa fa-check\" title=\"Done\"/>"
msgstr "<i class=\"text-success fa fa-check\" title=\"Hoàn tất\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "<span class=\"ml8\">Points</span>"
msgstr "<span class=\"ml8\">Điểm</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Awarded\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Trao thưởng\n"
"                                </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"<span title=\"Buy\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Buy\"/>Buy</span>"
msgstr ""
"<span title=\"Mua\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Mua\"/>Mua</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "<span title=\"More info\">More info</span>"
msgstr "<span title=\"Thông tin thêm\">Thông tin thêm</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"My Link to Share\"><i class=\"fa fa-lg fa-link text-"
"secondary\" role=\"img\" aria-label=\"My Link to Share\"/></span>"
msgstr ""
"<span title=\"Liên kết của tôi để chia sẻ\"><i class=\"fa fa-lg fa-link "
"text-secondary\" role=\"img\" aria-label=\"My Link to Share\"/></span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "<span title=\"Send by Mail\">Refer Friend</span>"
msgstr "<span title=\"Gửi bằng Mail\">Giới thiệu bạn bè</span>"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
#, python-format
msgid ""
"A new alert has been added to the Referrals app! Check your <a "
"href=%(url)s>dashboard</a> now!"
msgstr ""
"Một cảnh báo mới đã được thêm vào ứng dụng Giới thiệu! Kiểm tra <a "
"href=%(url)s>trang tổng quan</a> của bạn ngay!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__active
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_ids
msgid "Activities"
msgstr "Các hoạt động"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hành động ngoại lệ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng kiểu hoạt động"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__name
msgid "Alert"
msgstr "Cảnh báo"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert
msgid "Alert in Referral App"
msgstr "Cảnh báo trong ứng dụng giới thiệu"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_alert_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_alert_configuration
msgid "Alerts"
msgstr "Cảnh báo"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__points
msgid ""
"Amount of points that the referent will receive when the applicant will "
"reach this stage"
msgstr ""
"Số điểm mà người được giới thiệu sẽ nhận được khi người đăng ký đạt được "
"giai đoạn này"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_applicant
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__applicant_id
msgid "Applicant"
msgstr "Ứng viên"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Applicant must have a company."
msgstr "Ứng viên phải có một công ty."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Archived"
msgstr "Đã lưu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tập tin đính kèm"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "Avatar"
msgstr "Ảnh đại diện"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__awarded_employees
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#, python-format
msgid "Awarded Employees"
msgstr "Nhân viên được trao thưởng"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__back
msgid "Back"
msgstr "Quay lại"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background"
msgstr "Nền"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background Image"
msgstr "Hình Nền"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__body_html
msgid "Body"
msgstr "Thân"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__utm_campaign_id
msgid "Campaign"
msgstr "Chiến dịch"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__channel
msgid "Channel"
msgstr "Kênh"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Choose an avatar for your new friend!"
msgstr "Chọn một hình đại diện cho người bạn mới của bạn!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Click to level up!"
msgstr "Bấm để lên cấp!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Close"
msgstr "Đóng"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__company_id
msgid "Company"
msgstr "Công ty"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_config_settings
msgid "Config Settings"
msgstr "Cấu hình"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__cost
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Cost"
msgstr "Giá vốn"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_friend_configuration
msgid "Create a new friend"
msgstr "Tạo một người bạn mới"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_level_configuration
msgid "Create a new level"
msgstr "Tạo một cấp độ mới"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid "Create new alerts"
msgstr "Tạo cảnh báo mới"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_points
msgid "Create new reward points"
msgstr "Tạo điểm phần thưởng mới"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: model:ir.actions.client,name:hr_referral.action_hr_referral_welcome_screen
#: model:ir.ui.menu,name:hr_referral.menu_hr_applicant_employee_referral_dashboard
#, python-format
msgid "Dashboard"
msgstr "Trang tổng quan"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image
msgid "Dashboard Image"
msgstr "Hình ảnh trang tổng quan"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Date"
msgstr "Ngày"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_from
msgid "Date From"
msgstr "Từ ngày"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_to
msgid "Date To"
msgstr "Đến ngày"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__position
msgid ""
"Define the position of the friend. If it's a small friend like a dog, you "
"must select Front, it will be placed in the front of the dashboard, above "
"superhero."
msgstr ""
"Xác định vị trí của người bạn. Nếu là bạn nhỏ như chó thì phải chọn Front, "
"nó sẽ được đặt ở phía trước bảng điều khiển, phía trên siêu anh hùng."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Department"
msgstr "Phòng/Ban"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__description
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Description"
msgstr "Mô tả"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__direct_clicks
msgid "Direct Clicks"
msgstr "Nhấp chuột trực tiếp"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/report/hr_referral_report.py:0
#, python-format
msgid "Direct Referral"
msgstr "Giới thiệu trực tiếp"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__dismissed_user_ids
msgid "Dismissed User"
msgstr "Người dùng bị loại"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid "Do not have access"
msgstr "Không có quyền truy cập"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"Do you want to confirm this reward? After confirmation an HR will contact "
"you."
msgstr ""
"Bạn có muốn xác nhận phần thưởng này không? Sau khi xác nhận, nhân sự sẽ "
"liên hệ với bạn."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__earned_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__earned_points
msgid "Earned Points"
msgstr "Điểm kiếm được"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__email_to
msgid "Email"
msgstr "Email"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Email a friend"
msgstr "Gửi email cho một người bạn"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Employee"
msgstr "Nhân viên"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_hired
msgid "Employee Referral Hired"
msgstr "Nhân viên giới thiệu được tuyển"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_refused
msgid "Employee Referral Refused"
msgstr "Giới thiệu nhân viên bị từ chối"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_report
msgid "Employee Referral Report"
msgstr "Báo cáo giới thiệu nhân viên"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Employees"
msgstr "Nhân viên"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Employees Analysis"
msgstr "Đánh giá nhân viên"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.employee_referral_report_action
msgid "Employees Referral Analysis"
msgstr "Phân tích giới thiệu nhân viên"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_alert__url
msgid ""
"External links must start with 'http://www.'. For an internal url, you don't"
" need to put domain name, you can just insert the path."
msgstr ""
"Các liên kết bên ngoài phải bắt đầu bằng 'http: // www.'. Đối với url nội "
"bộ, bạn không cần đặt tên miền mà chỉ cần chèn đường dẫn."

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__facebook
msgid "Facebook"
msgstr "Facebook"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__facebook_clicks
msgid "Facebook Clicks"
msgstr "Số lần nhấp trên Facebook"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng ví dụ: fa-tasks"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__friend_id
msgid "Friend"
msgstr "Bạn bè"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__name
msgid "Friend Name"
msgstr "Tên bạn bè"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_friend_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_friend_configuration
msgid "Friends"
msgstr "Bạn bè"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_friend
msgid "Friends for Referrals"
msgstr "Bạn bè để giới thiệu"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__front
msgid "Front"
msgstr "Trước mặt"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Gather your team"
msgstr "Tập hợp nhóm của bạn"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Gather your team!"
msgstr "Tập hợp nhóm của bạn!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "Gift Responsible"
msgstr "Phụ trách quà tặng"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Gifts"
msgstr "Quà tặng"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__all_jobs
msgid "Go to All Jobs"
msgstr "Chuyển đến Tất cả Công việc"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Group By"
msgstr "Nhóm theo"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
msgid "HR Referral Alert Search"
msgstr "Tìm kiếm thông báo giới thiệu nhân sự"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__has_referrer
msgid "Has Referrer"
msgstr "Có liên kết giới thiệu"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid ""
"Hello,<br><br>There are some amazing job offers in my company! Have a look, "
"they  can be interesting for you<br><a href=\"%s\">See Job Offers</a>"
msgstr ""
"Xin chào,<br><br>Có một số lời mời làm việc tuyệt vời trong công ty của tôi!"
" Hãy xem, chúng  có thể làm cho bạn cảm thấy hứng thú<br><a href=\"%s\">Xem "
"tin tuyển dụng</a>"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid ""
"Hello,<br><br>There is an amazing job offer for %s in my company! It will be"
" a fit for you<br><a href=\"%s\">See Job Offer</a>"
msgstr ""
"Xin chào,<br><br>Có một lời mời làm việc tuyệt vời cho %s ở công ty của tôi!"
" Nó sẽ phù hợp với bạn<br><a href=\"%s\">Xem lời mời làm việc</a>"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__hired
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__hired
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Hired"
msgstr "Đã tuyển"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_level_id
msgid "Hr Referral Level"
msgstr "Cấp độ giới thiệu của Hr"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_onboarding_page
msgid "Hr Referral Onboarding Page"
msgstr "Trang giới thiệu Hr"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__hr_referral_reward_id
msgid "Hr Referral Reward"
msgstr "Phần thưởng giới thiệu Hr"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__id
msgid "ID"
msgstr "ID"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, các tin nhắn mới yêu cầu sự có mặt của bạn."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu đánh dấu thì một số thông điệp có lỗi."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image_head
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__image
msgid "Image"
msgstr "Hình ảnh"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__progress
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_is_follower
msgid "Is Follower"
msgstr "Trở thành người theo dõi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__job_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Job"
msgstr "Chức vụ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_job
msgid "Job Position"
msgstr "Chức vụ"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_job_employee_referral
msgid "Job Positions"
msgstr "Vị trí công việc"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Job Referral Program"
msgstr "Chương trình giới thiệu việc làm"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__job_open_date
msgid "Job Start Recruitment Date"
msgstr "Ngày tuyển dụng bắt đầu công việc"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__write_date
msgid "Last Update Date"
msgstr "Cập nhật gần nhất"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__last_valuable_stage_id
msgid "Last Valuable Stage"
msgstr "Giai đoạn cuối cùng có giá trị"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward_configuration
msgid "Let's create Super Rewards to thank your employees."
msgstr "Hãy tạo Siêu phần thưởng để cảm ơn nhân viên của bạn."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward
msgid "Let's create super Rewards to thank<br>your employees."
msgstr "Hãy tạo Siêu phần thưởng để cảm ơn<br>nhân viên của bạn."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
msgid "Let's share a job position."
msgstr "Hãy chia sẻ một vị trí công việc."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Level"
msgstr "Mức độ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__name
msgid "Level Name"
msgstr "Tên cấp độ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_level
msgid "Level for referrals"
msgstr "Mức độ giới thiệu"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_level_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_level_configuration
msgid "Levels"
msgstr "Cấp độ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__direct
msgid "Link"
msgstr "Liên kết"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_link_to_share_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Link to Share"
msgstr "Link để chia sẻ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__linkedin
msgid "Linkedin"
msgstr "Linkedin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__linkedin_clicks
msgid "Linkedin Clicks"
msgstr "Số lần nhấp vào Linkedin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__max_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__max_points
msgid "Max Points"
msgstr "Điểm tối đa"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__medium_id
msgid "Medium"
msgstr "Kênh trung gian"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error
msgid "Message Delivery error"
msgstr "Thông báo gửi đi gặp lỗi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_ids
msgid "Messages"
msgstr "Thông báo"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_applicant_employee_referral
msgid "My Referral"
msgstr "Giới thiệu của tôi"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
#, python-format
msgid "New Alert In Referrals App"
msgstr "Cảnh báo mới trong ứng dụng Giới thiệu"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#, python-format
msgid "New gift awarded for %s"
msgstr "Món quà mới được trao cho %s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Next"
msgstr "Kế tiếp"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động tiếp theo"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hành động kế tiếp"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_id
msgid "Next Activity Type"
msgstr "Kiểu hoạt động kế tiếp"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_job_employee_referral
msgid "No job positions are available to share."
msgstr "Không có vị trí công việc nào có sẵn để chia sẻ."

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__no
msgid "Not Clickable"
msgstr "Không thể nhấp"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__closed
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__closed
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Not Hired"
msgstr "Không được tuyển"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số lượng tin gửi đi bị lỗi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__onclick
msgid "On Click"
msgstr "Khi bấm"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_onboarding_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_onboarding_configuration
msgid "Onboarding"
msgstr "Giới thiệu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Ongoing"
msgstr "Đang tiến hành"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open"
msgstr "Mở"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Point icon"
msgstr "Biểu tượng điểm"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__points
#: model:ir.ui.menu,name:hr_referral.menu_hr_points_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points"
msgstr "Điểm"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__points_not_hired
msgid "Points Given For Not Hired"
msgstr "Điểm được cho vì không tuyển được"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__points_missing
msgid "Points Missing"
msgstr "Thiếu điểm"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Points icon"
msgstr "Biểu tượng điểm"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_points
msgid "Points line for referrals"
msgstr "Dòng điểm cho giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points to buy this"
msgstr "Điểm để mua cái này"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__position
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Position"
msgstr "Vị trí"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Positions"
msgstr "Vị trí"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Product"
msgstr "Sản phẩm"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__name
msgid "Product Name"
msgstr "Tên sản phẩm"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
msgid "Ready to receive points?"
msgstr "Sẵn sàng nhận điểm?"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "Báo cáo phân tích tuyển dụng"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Các giai đoạn tuyển dụng"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Referer"
msgstr "Người giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Referral"
msgstr "Giới thiệu"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert_mail_wizard
msgid "Referral Alert Mail Wizard"
msgstr "Tính năng gửi email cảnh báo giới thiệu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_company__hr_referral_background
#: model:ir.model.fields,field_description:hr_referral.field_res_config_settings__hr_referral_background
msgid "Referral Background"
msgstr "Hình nền giới thiệu"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_link_to_share
msgid "Referral Link To Share"
msgstr "Liên kết giới thiệu để chia sẻ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__referral_point_ids
msgid "Referral Point"
msgstr "Điểm giới thiệu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_points_ids
msgid "Referral Points"
msgstr "Điểm giới thiệu"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_referral_reward_responsible_user
msgid "Referral Reward Responsible User"
msgstr "Người phụ trách phần thưởng giới thiệu"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_mail
msgid "Referral Send Mail"
msgstr "Gửi thư giới thiệu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_state
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__referral_state
msgid "Referral State"
msgstr "Trạng thái giới thiệu"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Referral: %s"
msgstr "Giới thiệu: %s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Referral: %s (%s)"
msgstr "Giới thiệu: %s (%s)"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_root
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
#, python-format
msgid "Referrals"
msgstr "Giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_applicant_view_search_bis_inherit_referral
msgid "Referred By"
msgstr "Tham chiếu bởi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__ref_user_id
msgid "Referred By User"
msgstr "Giới thiệu theo người dùng"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_report_employee_referral_all
msgid "Reporting"
msgstr "Báo cáo"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
msgid "Requirements"
msgstr "Yêu cầu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Restart Onboarding"
msgstr "Khởi động lại giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Restore Default"
msgstr "Khôi phục cài đặt mặc định"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Reward"
msgstr "Thưởng"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_reward
msgid "Reward for Referrals"
msgstr "Phần thưởng Giới thiệu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reward_configuration
#, python-format
msgid "Rewards"
msgstr "Phần thưởng"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Search Points / Gifts"
msgstr "Tìm kiếm Điểm / Quà tặng"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Search Referral"
msgstr "Tìm kiếm giới thiệu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
msgid "Send"
msgstr "Gửi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_mail_action
msgid "Send Job Offer by Mail"
msgstr "Gửi thư mời làm việc"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Send Job by Mail"
msgstr "Gửi công việc qua thư"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_alert_mail_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Send Mail"
msgstr "Gửi mail"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__sequence
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__sequence_stage
msgid "Sequence of stage"
msgstr "Thứ tự của giai đoạn"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_configuration
#: model:ir.ui.menu,name:hr_referral.hr_referral_menu_configuration
msgid "Settings"
msgstr "Thiết lập"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Share"
msgstr "Chia sẻ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share Now"
msgstr "Chia sẻ ngay"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share on {{ source.name }}"
msgstr "Chia sẻ {{ source.name }}"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share on {{source.name}}"
msgstr "Chia sẻ {{source.name}}"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__shared_item_infos
msgid "Shared Item Infos"
msgstr "Infos mục được chia sẻ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid "Show in Referrals"
msgstr "Hiển thị trong Giới thiệu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Skip"
msgstr "Bỏ qua"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Skip and Start"
msgstr "Bỏ qua và Bắt đầu"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Sorry, your referral %s has been refused in the recruitment process."
msgstr ""
"Rất tiếc! Lượt giới thiệu %s của bạn đã bị từ chối trong quá trình tuyển "
"dụng."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__utm_source_id
msgid "Source"
msgstr "Nguồn"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__url
msgid "Specify URL"
msgstr "Chỉ định URL"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__stage_id
msgid "Stage"
msgstr "Giai đoạn"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Start"
msgstr "Khởi động"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Start Now"
msgstr "Bắt đầu ngay"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Ngày đến hạn phải được chuyển\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Các hoạt động trong tương lai."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__subject
msgid "Subject"
msgstr "Chủ đề"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Successful"
msgstr "Thành công"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__text
msgid "Text"
msgstr "Văn bản"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image
msgid ""
"This field holds the image used as image for the friend on the dashboard, "
"limited to 1024x1024px."
msgstr ""
"Trường này chứa hình ảnh được sử dụng làm hình ảnh cho bạn bè trên trang "
"tổng quan, giới hạn ở 1024x1024px."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image_head
msgid ""
"This field holds the image used as image for the head's friend when the user"
" must choose a new friend, limited to 1024x1024px."
msgstr ""
"Trường này lưu giữ hình ảnh được sử dụng làm hình ảnh cho người bạn của head"
" khi người dùng phải chọn một người bạn mới, giới hạn ở 1024x1024px."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"Trường này chứa hình ảnh được sử dụng làm hình cho sản phẩm, kích thước bị "
"giới hạn đến 1024x1024px."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid ""
"This link contains a tracker so that people clicking on it will account to a"
" referral for you, even if they apply on a position after a few days."
msgstr ""
"Liên kết này chứa một trình theo dõi để những người nhấp vào nó sẽ tính đến "
"giới thiệu cho bạn, ngay cả khi họ nộp đơn vào một vị trí sau một vài ngày."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid ""
"This option is used in app 'Referrals'. If checked, the stage is displayed "
"in 'Referrals Dashboard' and points are given to the employee."
msgstr ""
"Tùy chọn này được sử dụng trong ứng dụng 'Giới thiệu'. Nếu chọn, trạng thái "
"sẽ được hiển thị trong 'Trang tổng quan giới thiệu' và điểm sẽ được trao cho"
" nhân viên."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "This report performs analysis on your employee referral."
msgstr "Báo cáo này thực hiện phân tích về giới thiệu nhân viên của bạn."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "To Spend"
msgstr "Để chi tiêu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Total"
msgstr "Tổng"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__twitter
msgid "Twitter"
msgstr "Twitter"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__twitter_clicks
msgid "Twitter Clicks"
msgstr "Twitter Bấm"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trên hồ sơ."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__url
msgid "URL"
msgstr "URL"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_campaign
msgid "UTM Campaign"
msgstr "Chiến dịch UTM"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_source
msgid "UTM Source"
msgstr "UTM Source"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__url
msgid "Url"
msgstr "Url"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_users
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__user_ids
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__ref_user_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__ref_user_id
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "User"
msgstr "Người dùng"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_recruitment_referral_user
msgid "User : Referral only"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "User responsible of this gift."
msgstr "Người dùng chịu trách nhiệm về món quà này."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "View Jobs"
msgstr "Xem công việc"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
#, python-format
msgid "Visit Webpage"
msgstr "Visit Webpage"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website Messages"
msgstr "Thông báo Website"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử thông tin liên lạc website"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_onboarding
msgid "Welcome Onboarding in Referral App"
msgstr "Chào mừng bạn gia nhập ứng dụng giới thiệu"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "You are not allowed to access applicant records."
msgstr "Bạn không được phép truy cập hồ sơ ứng viên."

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_campaign.py:0
#, python-format
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following jobs in Referral:\n"
"%(job_names)s"
msgstr ""
"Bạn không thể xóa các Chiến dịch UTM này vì chúng được liên kết với các công việc sau trong Giới thiệu:\n"
"%(job_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following users in Referral:\n"
"%(employee_names)s"
msgstr ""
"Bạn không thể xóa các Nguồn UTM này vì chúng được liên kết với những người dùng sau trong Giới thiệu:\n"
"%(employee_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#, python-format
msgid ""
"You do not have enough points in this company to buy this product. In this "
"company, you have %s points."
msgstr ""
"Bạn không có đủ điểm trong công ty này để mua sản phẩm này. Trong công ty "
"này, bạn có %s điểm."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "You earned"
msgstr "Bạn đã kiếm được"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "You need another"
msgstr "Bạn cần cái khác"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Your referrer got a step further!"
msgstr "Người giới thiệu của bạn đã tiến thêm một bước nữa!"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Your referrer is hired!"
msgstr "Người giới thiệu của bạn được thuê!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "click(s)"
msgstr "click(s)"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "has been hired!"
msgstr "đã được tuyển!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "https://www.google.com"
msgstr "https://www.google.com"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "reward"
msgstr "phần thưởng"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "share"
msgstr "chia sẻ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__url
msgid "url"
msgstr "url"
