# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mail
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:921
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr ""
"Αυτό το κανάλι είναι ιδιωτικό. Οι άνθρωποι πρέπει να προσκληθούν να "
"συμμετάσχουν."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:945
#, python-format
msgid "%d Messages"
msgstr "%d Μηνύματα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:89
#, python-format
msgid "%d days overdue"
msgstr "%d ημέρες καθυστέρησης"

#. module: mail
#: code:addons/mail/models/mail_template.py:249
#, python-format
msgid "%s (copy)"
msgstr "%s (αντίγραφο)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:133
#, python-format
msgid "%s and %s are typing..."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:278
#, python-format
msgid "%s created"
msgstr "%s δημιουργήθηκε"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:131
#, python-format
msgid "%s is typing..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:137
#, python-format
msgid "%s, %s and more are typing..."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_activity.py:341
#, python-format
msgid "%s: %s assigned to you"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:285
#, python-format
msgid "&nbsp;("
msgstr "&nbsp;("

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:326
#, python-format
msgid "(from"
msgstr "(από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:56
#, python-format
msgid ", due on"
msgstr ", έως τις"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:547
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Εμφάνιση παλαιότερων μηνυμάτων --------"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:99
#, python-format
msgid "0 Future"
msgstr "0 Μελλοντικά"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:95
#, python-format
msgid "0 Late"
msgstr "0 Καθυστερημένα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:97
#, python-format
msgid "0 Today"
msgstr "0 Σημερινά"

#. module: mail
#: code:addons/mail/models/mail_channel.py:926
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b>.to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>\n"
"            Type <b>:shortcut</b> to insert canned responses in your message.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:721
#, python-format
msgid ""
"<div class=\"o_mail_notification\">%(author)s invited %(new_partner)s to <a "
"href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:828
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">δημιουργήθηκε <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:728
#: code:addons/mail/models/mail_channel.py:802
#, python-format
msgid ""
"<div class=\"o_mail_notification\">joined <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">προσχώρησε <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:294
#, python-format
msgid ""
"<div class=\"o_mail_notification\">left <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">αποχώρησε <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/wizard/invite.py:23
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow %s document: %s.</p></div>"
msgstr ""
"<div><p>Γεια σας,</p><p>ο/η %s σας προσκάλεσε να ακολουθήσετε το %s έγγραφο:"
" %s.<p></div>"

#. module: mail
#: code:addons/mail/wizard/invite.py:26
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow a new document.</p></div>"
msgstr ""
"<div><p>Γεια σας,</p><p>ο/η %s σας προσκάλεσε να ακολουθήσετε ένα νέο "
"έγγραφο.</p></div>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:28
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p>Κάντε <b>Chat με τους συνεργάτες σας</b> σε πραγματικό χρόνο "
"χρησιμοποιώντας τα άμεσα μηνύματα.</p><p><i>Θα χρειαστεί να προσκαλέσετε "
"πρώτα τους χρήστες από την εφαρμογή Ρυθμίσεις.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:15
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Γράψτε ένα μήνυμα</b> εδώ στα μέλη του καναλιού.</p> <p>Μπορείτε να "
"ειδοποιήσετε κάποιον με <i>'@'</i> ή να συνδέσετε άλλο κανάλι με <i>'#'</i>."
" Ξεκινήστε το μήνυμά σας με <i>'/'</i> για να δείτε τη λίστα των πιθανών "
"εντολών.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:11
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Τα κανάλια διευκολύνουν την οργάνωση πληροφοριών μεταξύ διαφορετικών "
"θεμάτων και ομάδων.</p><p>Προσπαθήστε να <b>δημιουργήσετε το πρώτο "
"κανάλι</b> (π.χ. πωλήσεις. μάρκετινγκ, προϊόν ΑΒΓ, μετά από συνάντηση "
"εργασίας, κ.λ.π).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/create_mode_document_thread.js:65
#, python-format
msgid "<p>Creating a new record...</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"Αν θέλετε να το στείλετε σε όλες τις εγγραφές που ταιριάζουν με το κριτήριο αναζήτησης, επιλέξτε αυτό το πλαίσιο :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"Αν θέλετε να χρησιμοποιήσετε μόνο επιλεγμένες εγγραφές, αποεπιλέξτε αυτό το πλαίσιο επιλογής:\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Προσθήκη</span>\n"
"<span class=\"o_stat_text\">Πλαίσιο ενέργειας</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Αφαίρεση</span>\n"
"<span class=\"o_stat_text\">Πλαίσιο ενέργειας</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"Όλες οι εγγραφές που αντιστοιχούν στο τρέχον φίλτρο αναζήτησης θα ταχυδρομηθούν,\n"
"όχι μόνο οι εγγραφές που επιλέχθηκαν στην προβολή λίστας.\n"
"</strong><br/>\n"
"Το μήνυμα ηλεκτρονικού ταχυδρομείου θα αποσταλεί σε όλες τις εγγραφές που έχουν επιλεγεί στη λίστα.<br/>\n"
"Η επιβεβαίωση αυτού του οδηγού πιθανόν να χρειαστεί μερικά λεπτά και θα κλειδώσει το πρόγραμμα περιήγησής σας."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong> Feedback</strong>"
msgstr "<strong> Σχόλιο</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Θα χρησιμοποιηθούν μόνο εγγραφές που έχουν επιλεγεί στην προβολή λίστας.</strong><br/>\n"
"Το μήνυμα ηλεκτρονικού ταχυδρομείου θα αποσταλεί για όλες τις εγγραφές που έχουν επιλεγεί στη λίστα."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Προτεινόμενες Δραστηριότητες</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Ένα Python λεξικό που θα αξιολογηθεί για να παρέχει προεπιλεγμένες τιμές "
"όταν δημιουργείτε νέες εγγραφές για αυτό το ψευδώνυμο."

#. module: mail
#: code:addons/mail/models/ir_actions.py:69
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Ένας σύντομος κωδικός είναι μια συντόμευση πληκτρολογίου. Για παράδειγμα, "
"πληκτρολογείτε #gm και θα μετατραπεί σε \"Good Morning\"."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:185
#: code:addons/mail/static/src/xml/thread.xml:412
#, python-format
msgid "Accept"
msgstr "Αποδοχή"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:185
#, python-format
msgid "Accept selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:412
#, python-format
msgid "Accept |"
msgstr ""

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Accepted"
msgstr "Αποδεκτή"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Ομάδες Πρόσβασης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
msgid "Action To Do"
msgstr "Ενέργεια Να Κάνω"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Ενεργοποιείται από προεπιλογή κατά την εγγραφή."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Σε Ισχύ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Ενεργός τομέας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:108
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model:mail.message.subtype,name:mail.mt_activities
#, python-format
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:15
#: code:addons/mail/static/src/xml/chatter.xml:93
#: code:addons/mail/static/src/xml/systray.xml:74
#: selection:ir.actions.act_window.view,view_mode:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#: selection:ir.ui.view,type:0
#, python-format
msgid "Activity"
msgstr "Δραστηριότητα"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Μίξη Δραστηριοτήτων"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Τύπος Δραστηριότητας"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Τύποι Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
msgid "Activity User Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:37
#, python-format
msgid "Activity type"
msgstr "Τύπος Δραστηριότητας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:111
#, python-format
msgid "Add"
msgstr "Προσθήκη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:63
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__channel_ids
#, python-format
msgid "Add Channels"
msgstr "Προσθήκη Καναλιών"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:62
#: selection:ir.actions.server,state:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Πρόσθεση Ακολούθων"

#. module: mail
#: code:addons/mail/models/ir_actions.py:63
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "Η προσθήκη ακολούθων μπορεί να γίνει μόνο σε ένα μοντέλο \"Νήμα Email\""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,field_description:mail.field_mail_template__user_signature
msgid "Add Signature"
msgstr "Προσθήκη υπογραφής"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:52
#: code:addons/mail/static/src/xml/discuss.xml:242
#, python-format
msgid "Add a channel"
msgstr "Προσθήκη Καναλιού"

#. module: mail
#: code:addons/mail/models/mail_thread.py:383
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:71
#, python-format
msgid "Add a private channel"
msgstr "Προσθήκη ενός ιδιωτικού καναλιού"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address in the blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:15
#, python-format
msgid "Add attachment"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add channels to notify..."
msgstr "Προσθήκη καναλιών για ενημέρωση..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Προσθήκη επαφών για ενημέρωση..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:418
#, python-format
msgid "Add this email address to white list of people"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Πρόσθετες επαφές"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Για προχωρημένους"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Ρυθμίσεις για προχωρημένους"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
msgid "After"
msgstr "Μετά"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:26
#, python-format
msgid "Alarm menu"
msgstr ""

#. module: mail
#: selection:mail.activity.type,decoration_type:0
msgid "Alert"
msgstr "Προσοχή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model:ir.model.fields,field_description:mail.field_res_users__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Ψευδώνυμο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,field_description:mail.field_res_users__alias_contact
msgid "Alias Contact Security"
msgstr "Ψευδώνυμο Ασφάλειας Επαφής"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Ψευδώνυμο Τομέα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Ψευδώνυμο ονόματος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Ψευδώνυμο τομέα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Μοντέλο Ψευδωνύμου"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Ψευδώνυμο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:20
#: code:addons/mail/static/src/xml/systray.xml:31
#, python-format
msgid "All"
msgstr "Όλα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:46
#, python-format
msgid "All pages:&nbsp;"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Allowed Emails"
msgstr ""

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "Always"
msgstr "Πάντα"

#. module: mail
#: selection:mail.moderation,status:0
msgid "Always Allow"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:418
#, python-format
msgid "Always Allow |"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/mail_failure.js:80
#, python-format
msgid "An error occured when sending an email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:925
#: code:addons/mail/static/src/js/models/messages/message.js:148
#, python-format
msgid "Anonymous"
msgstr "Ανώνυμος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_message__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Οι απαντήσεις δεν πηγαίνουν στο αρχικό νήμα συζήτησης. Αυτό έχει ως επίπτωση"
" την δημιουργία νέου message-id."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Εφαρμογή σε"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:357
#, python-format
msgid "Apply"
msgstr "Εφαρμογή"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:122
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures. You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:52
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Ανατέθηκε σε"

#. module: mail
#: code:addons/mail/models/mail_activity.py:258
#: code:addons/mail/models/mail_activity.py:265
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Επισύναψη αρχείου"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
msgid "Attachment"
msgstr "Συνημμένο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:11
#: model:ir.model.fields,field_description:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Συνημμένα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Τα συνημμένα συνδέονται με ένα έγγραφο μέσω του model / res_id και στο "
"μήνυμα μέσα από αυτό το πεδίο."

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Employees"
msgstr "Επικυρωμένοι Υπάλληλοι"

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Partners"
msgstr "Επικυρωμένοι Συνεργάτες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Συντάκτης"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Author Signature (mass mail only)"
msgstr "Υπογραφή Συντάκτη (μόνο μέσω μαζικής αλληλογραφίας)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Συντάκτης του μηνύματος. Εάν δεν ρυθμιστεί, email από αυτές τις email "
"διευθύνσεις που δεν ταιριάζουν σε κανένα συνεργάτη."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Άβαταρ Συντάκτη"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Εξουσιοδοτημένη Ομάδα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Αυτόματη Διαγραφή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__force_next
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__force_next
msgid "Auto Schedule Next Activity"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Αυτόματη Συνδρομή"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Αυτόματη συνδρομή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify
msgid "Automatic notification"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:6
#: code:addons/mail/static/src/xml/followers.xml:44
#, python-format
msgid "Avatar"
msgstr "Άβαταρ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:420
#, python-format
msgid "Ban"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Ban List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:420
#, python-format
msgid "Ban this email address"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Banned Emails"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:90
#, python-format
msgid "Be careful with channels following internal notifications"
msgstr ""
"Να είστε προσεκτικοί με τα κανάλια που ακολουθούν τις εσωτερικές "
"ειδοποιήσεις"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Body"
msgstr "Κυρίως θέμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
msgid "Bounce"
msgstr "Προώθηση"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:398
#: selection:mail.notification,email_status:0
#, python-format
msgid "Bounced"
msgstr "Προωθήθηκε"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Calendar"
msgstr "Ημερολόγιο"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Τηλεφώνημα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:365
#: code:addons/mail/static/src/xml/activity.xml:86
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Ακύρωση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Ακύρωση Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:401
#: selection:mail.notification,email_status:0
#, python-format
msgid "Canceled"
msgstr "Ακυρώθηκε"

#. module: mail
#: selection:mail.mail,state:0
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Τυποποιημένη Απόκριση / Σύντομος κωδικός"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Πιστό αντίγραφο των παραληπτών του μηνύματος"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr ""
"Πιστό αντίγραφο παραληπτών (μπορούν να χρησιμοποιηθούν σύμβολα κράτησης "
"θέσης)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall
msgid "Catchall Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid "Categories may trigger specific behavior like opening calendar view"
msgstr ""
"Οι κατηγορίες ενδέχεται να ενεργοποιήσουν συγκεκριμένη συμπεριφορά όπως την "
"προβολή ημερολογίου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Category"
msgstr "Κατηγορία"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
msgid "Cc"
msgstr "Κοινοποίηση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Change"
msgstr "Ρέστα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:443
#, python-format
msgid "Changed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Changed Field"
msgstr "Αλλαγμένο Πεδίο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:108
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__channel_id
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#: selection:mail.channel,channel_type:0
#, python-format
msgid "Channel"
msgstr "Κανάλι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_message_ids
msgid "Channel Message"
msgstr "Μήνυμα Καναλιού"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_moderation_menu
msgid "Channel Moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Τύπος Καναλιού"

#. module: mail
#: model:ir.model,name:mail.model_mail_moderation
msgid "Channel black/white list"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:144
#, python-format
msgid "Channel settings"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:47
#: code:addons/mail/static/src/xml/discuss.xml:224
#: code:addons/mail/static/src/xml/systray.xml:22
#: code:addons/mail/static/src/xml/systray.xml:39
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Κανάλια"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Κανάλια/Συνεργάτης"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:220
#: code:addons/mail/static/src/xml/systray.xml:21
#: code:addons/mail/static/src/xml/systray.xml:35
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Chat Discussion"
msgstr "Συζήτηση Chat"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Σύντομος κωδικός Chat "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Θυγατρικά μηνύματα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose an example"
msgstr "Επιλέξτε ένα παράδειγμα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:50
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:67
#, python-format
msgid "Close chat window"
msgstr ""

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Closed"
msgstr "Κλειστό"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Πιστό αντίγραφο διευθύνσεων παραληπτών διαχωρισμένες με κόμμα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Κωδικοί των συνεργατών ως αποδέκτες διαχωρισμένοι με κόμμα"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Κωδικοί των συνεργατών ως αποδέκτες διαχωρισμένοι με κόμμα (μπορεί να "
"χρησιμοποιηθεί η κράτηση θέσης)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Διευθύνσεις παραληπτών διαχωρισμένες με κόμμα"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_to
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Διευθύνσεις παραληπτών διαχωρισμένες με κόμμα (μπορεί να χρησιμοποιηθεί η "
"κράτηση θέσης)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "Comment"
msgstr "Σχόλιο"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:378
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Σύνταξη Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Λειτουργία σύνθεσης"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Διαμορφώστε τους τύπους δραστηριότητάς σας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:130
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Συγχαρητήρια, ο φάκελος εισερχομένων είναι άδειος"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:944
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Συγχαρητήρια, ο φάκελος εισερχομένων είναι άδειος!"

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Περιεχόμενα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Περιεχόμενα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Κατάσταση Αναδίπλωσης Συνομιλίας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Η Συνομιλία είναι ελαχιστοποιημένη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:9
#, python-format
msgid "Conversations"
msgstr "Συζητήσεις"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Μετρητής του αριθμού προωθήσεων emails για αυτή την επαφή"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:565
#, python-format
msgid "Create %s"
msgstr "Δημιουργία %s"

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Create Next Activity"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:400
#, python-format
msgid "Create a new %(document)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:392
#, python-format
msgid "Create a new %(document)s by sending an email to %(email_link)s"
msgstr ""

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Create a new Record"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Δημιουργήθηκε από"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:41
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#, python-format
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_user_id
msgid "Creator"
msgstr "Δημιουργός"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__starred
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Ο τρέχων χρήστης έχει μια ειδοποίηση με αστέρι συνδεδεμένη με αυτό το μήνυμα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__is_moderator
msgid "Current user is a moderator of the channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__date
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Ημερομηνία"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:93
#, python-format
msgid "Dates"
msgstr "Ημερομηνίες"

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Days"
msgstr "Ημέρες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:46
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Προθεσμία"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Αγαπητέ/η"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Προεπιλογή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_next_type_id
msgid "Default Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Προεπιλεγμένη Τιμή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Προεπιλεγμένες τιμές"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Προεπιλεγμένοι αποδέκτες"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Προεπιλεγμένοι αποδέκτες της εγγραφής: \n"
"- συνεργάτης (χρησιμοποιώντας το πεδίο κωδικός από έναν συνεργάτη ή το πεδίο partner_id) ή\n"
"- email (χρησιμοποιώντας το πεδίο email_from ή το πεδίο email)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:87
#: code:addons/mail/static/src/xml/thread.xml:532
#, python-format
msgid "Delete"
msgstr "Διαγραφή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Διαγραφή Emails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Διαγραφή Αντίγραφου Μηνύματος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr "Διαγραφή απεσταλμένων emails (μόνο από μαζική αποστολή)"

#. module: mail
#: selection:mail.mail,state:0
msgid "Delivery Failed"
msgstr "Αποτυχία παράδοσης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Περιγραφή"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Περιγραφή που θα προστεθεί στο δημοσιευμένο μήνυμα για αυτόν τον υποτύπο. "
"Εάν είναι κενό, θα προστεθεί το όνομα αντί αυτού."

#. module: mail
#: selection:ir.ui.view,type:0
msgid "Diagram"
msgstr "Διάγραμμα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:57
#, python-format
msgid "Direct Messages"
msgstr "Άμεσα Μηνύματα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:348
#: code:addons/mail/static/src/xml/activity.xml:103
#: code:addons/mail/static/src/xml/discuss.xml:187
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Discard"
msgstr "Απόρριψη"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:187
#, python-format
msgid "Discard selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:416
#, python-format
msgid "Discard |"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Συζήτηση"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion channel"
msgstr "Κανάλι Συζήτησης"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Συζητήσεις"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_moderation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Εμφανίζει κάποια επιλογή για τα σχετικά έγγραφα για να ανοίξει ο οδηγός "
"σύνθεσης με αυτό το πρότυπο"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "Εμφάνιση ονόματος του σχετικού εγγράφου."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Να μην κρατηθεί αντίγραφο του email στο ιστορικό επικοινωνίας εγγράφων (μόνο"
" για μαζική αποστολή)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Έγγραφο"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Ακόλουθοι του εγγράφου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Μοντέλου Εγγράφου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Όνομα Εγγράφου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:101
#, python-format
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:107
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:99
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Ολοκλήρωση & Προγραμματισμός Επόμενου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:43
#: code:addons/mail/static/src/xml/thread.xml:77
#, python-format
msgid "Download"
msgstr "Λήψη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:11
#, python-format
msgid "Dropdown menu - Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Ημερ. Λήξης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:95
#, python-format
msgid "Due in %d days"
msgstr "Λήξη σε %d ημέρα/ες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Δυναμική Γεννήτρια Κράτησης Θέσης"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:83
#, python-format
msgid "Edit"
msgstr "Επεξεργασία"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:354
#, python-format
msgid "Edit Subscription of "
msgstr "Επεξεργασία συνδρομής από "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:50
#, python-format
msgid "Edit subscription"
msgstr "Επεξεργασία συνδρομής"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_moderation__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
msgid "Email Address"
msgstr "Email Address"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "Ψευδώνυμο Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Ψευδώνυμα Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Ρυθμίσεις Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Email Preview"
msgstr "Προεπισκόπηση email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Αναζήτηση Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__email_status
msgid "Email Status"
msgstr "Κατάσταση Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
msgid "Email Template"
msgstr "Πρότυπο email"

#. module: mail
#: model:ir.model,name:mail.model_email_template_preview
msgid "Email Template Preview"
msgstr "Προεπισκόπηση πρότυπου email"

#. module: mail
#: model:ir.model,name:mail.model_mail_template
msgid "Email Templates"
msgstr "Πρότυπα Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Νήμα Email"

#. module: mail
#: sql_constraint:mail.blacklist:0
msgid "Email address already exists!"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__alias_id
msgid ""
"Email address internally associated with this user. Incoming emails will "
"appear in the user's notifications."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου που συνδέεται εσωτερικά με αυτόν τον "
"χρήστη. Τα εισερχόμενα emails θα εμφανιστούν στις ειδοποιήσεις του χρήστη."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου του αποστολέα. Αυτό το πεδίο είναι "
"ορίζεται όταν δεν ταυτοποιηθεί ο συνεργάτης και έχει αντικαθιστά το πεδίο "
"author_id στην συζήτηση."

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Email address rejected by destination"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to redirect replies..."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου για ανακατεύθυνση  απαντήσεων..."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted means that the recipient won't receive "
"any mass mailing anymore."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Οδηγός Σύνταξης Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Μήνυμα Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:14
#, python-format
msgid "Emojis"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:399
#: selection:mail.activity.type,decoration_type:0
#, python-format
msgid "Error"
msgstr "Σφάλμα"

#. module: mail
#: code:addons/mail/models/update.py:98
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Σφάλμα κατά την επικοινωνία με το διακομιστή εξουσιοδότησης εκδότη."

#. module: mail
#: code:addons/mail/models/mail_mail.py:317
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Σφάλμα χωρίς εξαίρεση. Πιθανώς λόγω αποστολής ενός e-mail χωρίς να "
"υπολογίζονται οι παραλήπτες."

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a channel cannot follow twice the same object."
msgstr ""
"Σφάλμα, ένα κανάλι δεν μπορεί να ακολουθήσει δύο φορές το ίδιο αντικείμενο."

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a partner cannot follow twice the same object."
msgstr ""
"Σφάλμα, ένας συνεργάτης δεν μπορεί να ακολουθήσει δύο φορές το ίδιο "
"αντικείμενο."

#. module: mail
#: sql_constraint:mail.followers:0
msgid ""
"Error: A follower must be either a partner or a channel (but not both)."
msgstr ""
"Σφάλμα: Ένας ακόλουθος πρέπει να είναι είτε συνεργάτης ή κανάλι (αλλά όχι "
"και τα δύο)."

#. module: mail
#: selection:mail.alias,alias_contact:0 selection:mail.channel,public:0
msgid "Everyone"
msgstr "Όλοι"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_warning
#: selection:mail.notification,email_status:0
msgid "Exception"
msgstr "Εξαίρεση"

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Execute Python Code"
msgstr ""

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Execute several actions"
msgstr "Εκτέλεση διαφορετικών ενεργειών"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Εκτεταμένα Φίλτρα..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:45
#, python-format
msgid "Extract pages:&nbsp;"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Αποτυχία Mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Αποτυχία"

#. module: mail
#: code:addons/mail/models/mail_template.py:338
#, python-format
msgid "Failed to render template %r using values %r"
msgstr "Αποτυχία απόδοσης του προτύπου %r χρησιμοποιώντας τις τιμές %r"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Αιτιολογία Αποτυχίας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Αιτιολογία Αποτυχίας. Αυτή είναι συνήθως η εξαίρεση εμφανίζεται από τον "
"email server, αποθηκεύονται για την διευκόλυνση της επισκευής των λαθών σε "
"ζητήματα αλληλογραφίας."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Αγαπημένο από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:316
#: model:ir.model.fields,field_description:mail.field_mail_activity__feedback
#, python-format
msgid "Feedback"
msgstr "Σχόλια"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
msgid "Field"
msgstr "Πεδίο"

#. module: mail
#: code:addons/mail/models/ir_model.py:30
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Το πεδίο \"Νήμα Email\" δεν μπορεί να αλλαχθεί σε \"Ψευδές\"."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Περιγραφή πεδίου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Τύπος Πεδίου"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Το πεδίο χρησιμοποιείται για να συνδέσει το σχετικό μοντέλο στο μοντέλο "
"υποτύπου όταν χρησιμοποιείται αυτόματη συνδρομή σε ένα σχετικό έγγραφο. Το "
"πεδίο αυτό χρησιμοποιείται για τον υπολογισμό getattr "
"(related_document.relation_field)."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Πεδία"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Τελικό έκφραση τοποθέτησης, για να αντιγραφεί στο επιθυμητό πεδίο του "
"προτύπου."

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Folded"
msgstr "Διπλωμένα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:20
#, python-format
msgid "Follow"
msgstr "Ακολουθήστε"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Ακόλουθοι από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:40
#, python-format
msgid "Followers of"
msgstr "Ακόλουθοι του"

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Followers only"
msgstr "Μόνο ακόλουθοι"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:24
#, python-format
msgid "Following"
msgstr "Ακολουθώ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Γραμματοσειρά awesome  π.χ. fa-tasks"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Form"
msgstr "Φόρμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_from
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
msgid "From"
msgstr "Από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:73
#, python-format
msgid "Full composer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:98
#, python-format
msgid "Future"
msgstr "Μελλοντικά"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Gantt"
msgstr "Gantt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Gateway"
msgstr "Δικτυακή πύλη (Gateway)"

#. module: mail
#: selection:ir.actions.server,activity_user_type:0
msgid "Generic User From Record"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:88
#, python-format
msgid "Go to the configuration panel"
msgstr "Μετάβαση στον πίνακα ελέγχου"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Graph"
msgstr "Γράφημα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Ομαδοποίηση κατά..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Ομάδες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines_msg
msgid "Guidelines"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:448
#, python-format
msgid "Guidelines of channel %s"
msgstr ""

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle by Emails"
msgstr "Χειρισμός από Emails"

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle in Odoo"
msgstr "Χειρισμός από Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Έχει Αναφορές"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has attachments"
msgstr "Έχει συνημμένα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__has_error
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_compose_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Κεφαλίδες"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Hello"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Κρυφό"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Απόκρυψη του υποτύπου στις επιλογές του ακόλουθου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_thread__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__id
msgid "ID"
msgstr "Κωδικός"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Αναγνωριστικό της μητρικής εγγραφής που κρατά το ψευδώνυμο (παράδειγμα: έργο"
" που κατέχει το ψευδώνυμο της δημιουργίας εργασίας)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
msgid "Icon"
msgstr "Εικονίδιο"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Αναγνωριστικό του ακολουθούμενου πόρου."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:157
#: code:addons/mail/static/src/xml/discuss.xml:277
#, python-format
msgid "Idle"
msgstr "Αδράνεια"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Αν επιλεγεί, οι συνεργάτες θα λάβουν ένα μήνυμα προειδοποίησης ότι έχουν "
"προστεθεί ως ακόλουθοι του εγγράφου."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,help:mail.field_mail_template__user_signature
msgid ""
"If checked, the user's signature will be appended to the text version of the"
" message"
msgstr ""
"Εάν ενεργοποιηθεί, στην έκδοση κειμένου του μηνύματος, η υπογραφή του χρήστη"
" θα προσαρτηθεί στο μήνυμα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible."
msgstr ""
"Αν οριστεί, ο διαχειριστής ουράς θα στείλει το email μετά την ημερομηνία. "
"Εάν δεν έχει οριστεί, το email  θα αποσταλεί το συντομότερο δυνατό."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Jinja2 placeholders may be used."
msgstr ""
"Αν οριστεί, ο διαχειριστής ουράς θα στείλει το email μετά την ημερομηνία. "
"Εάν δεν έχει οριστεί, το email  θα αποσταλεί το συντομότερο δυνατό. Μπορεί "
"να χρησιμοποιηθούν τα σύμβολα κράτησης Jinja2."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist_mixin__is_blacklisted
#: model:ir.model.fields,help:mail.field_mail_channel_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Εάν έχετε ρυθμίσει στον τομέα (domain) ένα 'catch-all' email, να "
"ανακατευθύνεται σε διακομιστή Odoo , πληκτρολογήστε το όνομα τομέα εδώ."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:39
#, python-format
msgid "Image"
msgstr "Εικόνα"

#. module: mail
#: code:addons/mail/models/mail_alias.py:130
#, python-format
msgid "Inactive Alias"
msgstr "Ανενεργό Ψευδώνυμο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1232
#: code:addons/mail/static/src/xml/discuss.xml:27
#: code:addons/mail/static/src/xml/discuss.xml:205
#: code:addons/mail/static/src/xml/discuss.xml:216
#, python-format
msgid "Inbox"
msgstr "Εισερχόμενα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:33
#, python-format
msgid "Info"
msgstr "Πληροφορίες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model_id
msgid "Initial model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Αρχικό μήνυμα"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Ενσωματώσεις"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Μόνο για εσωτερική χρήση"

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Invalid email address"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:35
#, python-format
msgid "Invalid email address %r"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:88
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Μη έγκυρη έκφραση, πρέπει να είναι μια σωστή έκφραση python όπως ορίζεται "
"στο λεξικό ορισμών της python π.χ. \"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:126
#: code:addons/mail/models/mail_blacklist.py:129
#, python-format
msgid "Invalid primary email field on model %s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1968
#: code:addons/mail/models/mail_thread.py:2199
#, python-format
msgid ""
"Invalid record set: should be called as model (without records) or on "
"single-record recordset"
msgstr ""
"Μη έγκυρο σετ εγγραφών: θα πρέπει να κληθεί ως μοντέλο (χωρίς εγγραφές) ή "
"ένα σετ εγγραφών με μία εγγραφή."

#. module: mail
#: code:addons/mail/controllers/main.py:40
#, python-format
msgid "Invalid token in route %s"
msgstr "Μη έγκυρο διακριτικό στη διαδρομή %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:197
#, python-format
msgid "Invitation"
msgstr "Πρόσκληση"

#. module: mail
#: code:addons/mail/wizard/invite.py:53
#, python-format
msgid "Invitation to follow %s: %s"
msgstr "Πρόσκληση για να ακολουθήσετε: %s: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:45
#: code:addons/mail/static/src/xml/discuss.xml:178
#, python-format
msgid "Invite"
msgstr "Προσκαλέστε"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:171
#, python-format
msgid "Invite Follower"
msgstr "Πρόσκληση Ακόλουθου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:30
#: code:addons/mail/static/src/xml/discuss.xml:178
#, python-format
msgid "Invite people"
msgstr "Πρόσκληση ατόμων"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:1188
#, python-format
msgid "Invite people to #%s"
msgstr "Πρόσκληση ατόμων στο #%s"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Οδηγός Προσκλήσεων"

#. module: mail
#: selection:mail.channel,public:0
msgid "Invited people only"
msgstr "Πρόσκληση ατόμων μόνο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Allowed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Banned"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification
msgid "Is Notification"
msgstr "Είναι Ειδοποίηση"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Είναι Αναγνωσμένο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_subscribed
msgid "Is Subscribed"
msgstr "Είναι Εγγεγραμμένο "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is a member"
msgstr "Είναι μέλος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__is_moderator
msgid "Is moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Είναι καρφιτσωμένο στη διασύνδεση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Συμμετοχή"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Συμμετοχή σε μια ομάδα"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Kanban"
msgstr "Kanban"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Γλώσσα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_moderation____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Εμφανίστηκαν τελευταία"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:94
#, python-format
msgid "Late"
msgstr "Καθυστερημένα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__layout
#: model:ir.model.fields,field_description:mail.field_mail_message__layout
msgid "Layout"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Leave"
msgstr "Αποχώρηση"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:935
#: code:addons/mail/static/src/xml/discuss.xml:147
#, python-format
msgid "Leave this channel"
msgstr "Αποχώρηση από αυτό το Κανάλι"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__channel_ids
msgid ""
"List of channels that will be added as listeners of the current document."
msgstr ""
"Λίστα των καναλιών που θα προστεθούν ως ακροατές του τρέχοντος εγγράφου."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""
"Λίστα των συνεργατών που θα προστεθούν ως ακόλουθοι του τρέχοντος εγγράφου."

#. module: mail
#: code:addons/mail/models/mail_channel.py:946
#, python-format
msgid "List users in the current channel"
msgstr "Λίστα χρηστών στο τρέχον κανάλι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__channel_id
msgid "Listener"
msgstr "Ακροατής"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
msgid "Listeners"
msgstr "Ακροατές"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Ακροατές ενός Καναλιού"

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Livechat Conversation"
msgstr "Ζωντανή Συνομιλία"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:55
#, python-format
msgid "Loading"
msgstr "Γίνεται φόρτωση"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:544
#, python-format
msgid "Loading older messages..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:15
#, python-format
msgid "Loading..."
msgstr "Φόρτωση..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:34
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:41
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Καταγραφή"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:89
#, python-format
msgid "Log a note. Followers will not be notified."
msgstr "Καταγράψτε μια σημείωση. Οι ακόλουθοι δεν θα ειδοποιηθούν."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Καταγραφή ενός Σημειώματος..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Καταγραφή μιας Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Καταγραφή ενός Εσωτερικού Σημειώματος"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:89
#, python-format
msgid "Log note"
msgstr "Σημείωμα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:92
#, python-format
msgid "Log or schedule an activity"
msgstr "Καταγραφή ή προγραμματισμός μιας δραστηριότητας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:68
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:51
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_id
#, python-format
msgid "Mail"
msgstr "Αλληλογραφία"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Τύπος Δραστηριότητας email"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
msgid "Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_mixin
msgid "Mail Blacklist mixin"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:198
#, python-format
msgid "Mail Body"
msgstr "Κυρίως κείμενο Μηνύματος"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Φόρμα Καναλιού email"

#. module: mail
#: code:addons/mail/models/mail_mail.py:385
#, python-format
msgid "Mail Delivery Failed"
msgstr "Παράδοση mail απέτυχε"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Νήμα Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Τιμή Ανίχνευσης Mail"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Έχει δημιουργηθεί email για να ενημερώσει τους ανθρώπους της υφιστάμενης "
"mail.message"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "Mail: Διαχειριστής Ουράς Email"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_notify_channel_moderators_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_notify_channel_moderators
#: model:ir.cron,name:mail.ir_cron_mail_notify_channel_moderators
msgid "Mail: Notify channel moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:939
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Το γραμματοκιβώτιο δεν είναι διαθέσιμο - %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Mails templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:22
#: code:addons/mail/static/src/xml/thread.xml:9
#, python-format
msgid "Manage Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:80
#, python-format
msgid "Mark Done"
msgstr "Σήμανση ως Ολοκληρωμένο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:179
#, python-format
msgid "Mark all as read"
msgstr "Σήμανση όλων ως αναγνωσμένα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:179
#, python-format
msgid "Mark all read"
msgstr "Σήμανση όλων ως αναγνωσμένα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Σήμανση ως Ολοκληρωμένη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:298
#: code:addons/mail/static/src/xml/thread.xml:346
#, python-format
msgid "Mark as Read"
msgstr "Σήμανση ως Αναγνωσμένο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:299
#: code:addons/mail/static/src/xml/thread.xml:340
#, python-format
msgid "Mark as Todo"
msgstr "Σήμανση ως Εκκρεμότητα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:71
#, python-format
msgid "Mark as done"
msgstr "Σήμανση ως Ολοκληρωμένη"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
msgid "Mass Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_medium
msgid "Medium-sized photo"
msgstr "Μεσαίου μεγέθους φωτογραφία"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image_medium
msgid ""
"Medium-sized photo of the group. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""
"Μεσαίου μεγέθους φωτογραφία της ομάδας. Αλλάζει αυτόματα ως εικόνα 128x128 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιήστε αυτό "
"το πεδίο για τις προβολές φόρμας ή μερικές όψεις Kanban."

#. module: mail
#: selection:mail.activity.type,category:0
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Συνάντηση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Μέλη"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Τα μέλη αυτών των ομάδων θα προστίθεται αυτόματα ως ακόλουθοι. Σημειώστε ότι"
" θα είναι σε θέση να διαχειρίζονται τη συνδρομή τους χειροκίνητα εάν είναι "
"απαραίτητο."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/base_partner_merge.py:13
#, python-format
msgid "Merged with the following partners:"
msgstr "Συγχωνεύεται με τους παρακάτω συνεργάτες:"

#. module: mail
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Message"
msgstr "Μήνυμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Κωδικός Μηνύματος"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Ειδοποιήσεις Μηνυμάτων"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Περιγραφή εγγραφής Μηνύματος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Τύπος μηνύματος"

#. module: mail
#: code:addons/mail/models/mail_message.py:1277
#, python-format
msgid "Message are pending moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Παραλήπτες μηνυμάτων (emails)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Μήνυμα αναφορών, όπως αναγνωριστικά προηγούμενων μηνυμάτων"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:165
#, python-format
msgid "Message sent in \""
msgstr "Μήνυμα που στάλθηκε σε \""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Μήνυμα υποτύπου που δίνει ένα πιο ακριβή τύπο στο μήνυμα, ειδικά για τις "
"ειδοποιήσεις του συστήματος. Για παράδειγμα, μπορεί να είναι μία ειδοποίηση "
"που σχετίζετε με μια νέα εγγραφή (Νέο), ή σε μία αλλαγή κατάστασης σε "
"Διαδικασία (Αλλαγή Κατάστασης). Το μήνυμα υποτύπων σας επιτρέπει να "
"καθορίσετε με ακρίβεια τις ειδοποίησες που ο χρήστης επιθυμεί να λαμβάνει "
"στον τοίχο του."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Υποτύπος μηνύματος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Υπότυποι μηνυμάτων που ακολουθούνται, δηλαδή οι υποτύποι που θα εμφανιστούν "
"στον τοίχο του χρήστη."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Τύπος Μηνύματος: email για email μηνύματα, ειδοποίηση για μήνυμα συστήματος,"
" σχόλιο για άλλα μηνύματα όπως είναι οι απαντήσεις χρηστών"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_id
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Μοναδικός ταυτοποιητής μηνύματος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Κωδικός Μηνύματος"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "Μηνύματα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Αναζήτηση μηνυμάτων"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:20
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Τα μηνύματα μπορούν να επισημανθούν <b>με αστέρι</b> για να σας υπενθυμίσουν"
" να τα ελέγξετε αργότερα."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Μηνύματα με εσωτερικούς υποτύπους θα είναι ορατά μόνο από τους εργαζόμενους,"
" γνωστά και ως μέλη της ομάδας base_user"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mailbox.js:202
#, python-format
msgid "Missing domain for mailbox with ID '%s'"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Μοντέλο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Μοντέλο από τους πόρους ακολούθων"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Μοντέλο εφαρμογής υποτύπου. Εάν Λάθος, τότε αυτός ο υποτύπος εφαρμόζεται σε "
"όλα τα μοντέλα."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Μοντέλα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1244
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#, python-format
msgid "Moderate Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation
msgid "Moderate this channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_message__moderator_id
msgid "Moderated By"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_ids
msgid "Moderated Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_channel_ids
msgid "Moderated channels"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_count
msgid "Moderated emails count"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_moderation_action
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Moderation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_tree
msgid "Moderation Lists"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:39
#, python-format
msgid "Moderation Queue"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_message__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_counter
msgid "Moderation count"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_moderator
msgid "Moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderator_ids
msgid "Moderators"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr ""

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Months"
msgstr "Μήνες"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "My Activities"
msgstr "Οι Δραστηριότητες μου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Name"
msgstr "Περιγραφή"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Όνομα που πήρε από το σχετικό έγγραφο"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__report_name
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Όνομα που θα χρησιμοποιηθεί για το παραγόμενο αρχείο αναφοράς (μπορεί να περιέχει σύμβολα κράτησης θέσης)\n"
"Η επέκταση μπορεί να παραλειφθεί και στη συνέχεια θα συμπληρωθεί από τον τύπο έκθεσης."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__needaction
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_compose_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Απαιτεί Ενέργεια"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_mail__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_message__need_moderation
msgid "Need moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
msgid "Needaction Recipient"
msgstr "Χρειάζεται δράση παραλήπτη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:182
#, python-format
msgid "New Channel"
msgstr "Νέο Κανάλι"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:181
#, python-format
msgid "New Message"
msgstr "Νέο Μήνυμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Νέα Τιμή Χαρακτήρα (Char)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Νέα Τιμή Ημερομηνίας-Ώρας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Νέα Τιμή Δεκαδικού Αριθμού"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Νέα Τιμή Ακεραίου Αριθμού"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Νέα Τιμή Νομισματική"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Νέα Τιμή Κειμένου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:935
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:112
#: code:addons/mail/static/src/xml/systray.xml:15
#: code:addons/mail/static/src/xml/systray.xml:24
#, python-format
msgid "New message"
msgstr "Νέο μήνυμα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:429
#, python-format
msgid "New messages"
msgstr "Νέα μηνύματα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:131
#, python-format
msgid "New messages appear here."
msgstr "Τα νέα μηνύματα θα εμφανίζονται εδώ."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:104
#, python-format
msgid "New people"
msgstr "Νέος Συναλλασσόμενος "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_guidelines
msgid ""
"Newcomers on this moderated channel will automatically receive the "
"guidelines."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:95
#, python-format
msgid "Next"
msgstr "Επόμενο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Επόμενες Δραστηριότητες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Επόμενες διαθέσιμες δραστηριότητες"

#. module: mail
#: code:addons/mail/models/mail_notification.py:50
#, python-format
msgid "No Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:68
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:20
#, python-format
msgid "No activities planned."
msgstr "Δεν έχουν προγραμματιστεί δραστηριότητες."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:54
#, python-format
msgid "No conversation yet..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:21
#, python-format
msgid "No data to display"
msgstr "Δεν υπάρχουν δεδομένα προς εμφάνιση"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:186
#, python-format
msgid "No follower"
msgstr "Χωρίς ακόλουθο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:27
#, python-format
msgid "No matches found"
msgstr "Δεν βρέθηκαν αντιστοιχίες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:17
#, python-format
msgid "No message available"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:28
#, python-format
msgid "No message matches your search. Try to change your search filters."
msgstr ""
"Κανένα μήνυμα δεν αντιστοιχεί στην αναζήτησή σας. Προσπαθήστε να αλλάξετε τα"
" φίλτρα αναζήτησης."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:134
#, python-format
msgid "No starred message"
msgstr "Δεν υπάρχουν μηνύματα με αστέρι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_message__no_auto_thread
msgid "No threading for answers"
msgstr "Χωρίς νήμα για απαντήσεις"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Σημείωση"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:304
#, python-format
msgid "Note by"
msgstr "Σημείωση από"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Ειδοποίηση"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
msgid "Notification Management"
msgstr "Διαχείριση Ειδοποιήσεων"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify_msg
msgid "Notification message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
msgid "Notifications"
msgstr "Ειδοποιήσεις"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Ειδοποίηση ακολούθων"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Ειδοποίηση για τους ακολούθους του εγγράφου (μόνο μαζικές αποστολές)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:158
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Παλιά Τιμή Χαρακτήρα (Char)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Παλιά Τιμή Ημερομηνίας-Ώρας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Παλιά Τιμή Δεκαδικού Αριθμού"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Παλιά Τιμή Ακεραίου Αριθμού"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Παλιά Τιμή Νομισματική"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Παλιά Τιμή Κειμένου"

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "On Change"
msgstr "Σε Αλλαγή"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:24
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Μόλις ένα μήνυμα σημανθεί με αστέρι, μπορείτε να επιστρέψετε και να το "
"αναθεωρήσετε ανά πάσα στιγμή εδώ."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:188
#, python-format
msgid "One follower"
msgstr "Ένας ακόλουθος"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:156
#: code:addons/mail/static/src/xml/discuss.xml:276
#, python-format
msgid "Online"
msgstr "Online"

#. module: mail
#: code:addons/mail/models/ir_model.py:28
#, python-format
msgid "Only custom models can be modified."
msgstr "Μόνο τα προσαρμοσμένα μοντέλα μπορούν να τροποποιηθούν."

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Open"
msgstr "Ανοιχτό"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Άνοιγμα Εγγράφου"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Άνοιγμα Γονικού Εγγράφου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:243
#, python-format
msgid "Open chat"
msgstr "Άνοιγμα Chat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:190
#, python-format
msgid "Open document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:192
#, python-format
msgid "Open in Discuss"
msgstr "Άνοιγμα στην Συζήτηση"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Προαιρετική ταυτότητα ενός νήματος (εγγραφής), στην οποία όλα τα εισερχόμενα"
" μηνύματα θα πρέπει να επισυνάπτονται, ακόμη και αν δεν απαντήσει σ 'αυτό. "
"Εάν οριστεί, αυτό θα απενεργοποιήσει εντελώς τη δημιουργία νέων αρχείων."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Προαιρετικός προτιμώμενος διακομιστής για εξερχόμενα μηνύματα. Αν δεν "
"καθοριστεί, θα χρησιμοποιηθεί ο διακομιστής με την μέγιστη προτεραιότητα."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_template
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Προαιρετική έκθεση για να εκτύπωση και σύνδεση"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. "
"${object.partner_id.lang}."
msgstr ""
"Προαιρετική μετάφραση γλώσσας (κωδικός ISO), για επιλογή κατά την αποστολή "
"email. Αν δεν οριστεί, θα χρησιμοποιηθεί η αγγλική έκδοση. Αυτό θα πρέπει "
"συνήθως να είναι μια έκφραση σύμβολο κράτησης θέσης που να παρέχει την "
"κατάλληλη γλώσσα, π.χ. ${object.partner_id.lang}."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Προαιρετική τιμή να θα χρησιμοποιηθεί εάν το πεδίο στόχος είναι κενό"

#. module: mail
#: selection:mail.activity.type,category:0
msgid "Other"
msgstr "Άλλο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0
msgid "Outgoing"
msgstr "Εξερχόμενα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Διακομιστής Εξερχόμενης Αλληλογραφίας"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Εξερχόμενα μηνύματα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Διακομιστής εξερχόμενης αλληλογραφίας"

#. module: mail
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
msgid "Overdue"
msgstr "Εκπρόθεσμο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Παράκαμψη email συντάκτη"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Ιδιοκτήτης"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:40
#, python-format
msgid "PDF file"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Μητρικός"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Γονικό μήνυμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Μοντέλο Γονέα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Κωδικός γονικής εγγραφής νήματος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Γονικό μοντέλο κρατώντας το ψευδώνυμο. Το μοντέλο που κρατά το ψευδώνυμο "
"αναφοράς δεν είναι απαραίτητο το μοντέλο να δίνεται από alias_model_id "
"(παράδειγμα: Έργο (parent_model) και Εργασία (μοντέλο))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Γονικός υποτύπος, που χρησιμοποιείται για την αυτόματη συνδρομή. Αυτό το "
"πεδίο δεν έχει σωστά ονομαστεί. Για παράδειγμα, για ένα Έργο, το parent_id "
"των υποτύπων του έργου αναφέρεται σε Εργασίες σχετικές με τους υποτύπους."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: mail
#: code:addons/mail/models/res_partner.py:29
#, python-format
msgid "Partner Profile"
msgstr "Προφίλ Συνεργάτη"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additionnal information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction_partner_ids
msgid "Partners with Need Action"
msgstr "Συνεργάτες με 'Απαιτεί Ενέργεια'"

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Pending Moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:424
#, python-format
msgid "Pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:139
#, python-format
msgid "Pending moderation messages appear here."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""

#. module: mail
#: selection:mail.moderation,status:0
msgid "Permanent Ban"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid "Permanently delete this email after sending it, to save space"
msgstr ""
"Οριστική διαγραφή αυτού του email μετά την αποστολή του, για να "
"εξοικονομήσετε χώρο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image
msgid "Photo"
msgstr "Φωτογραφία"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Pivot"
msgstr "Συγκεντρωτικό"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Έκφραση κράτησης θέσης"

#. module: mail
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
msgid "Planned"
msgstr "Προγραμματισμένη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:6
#, python-format
msgid "Planned activities"
msgstr "Προγραμματισμένες δραστηριότητες"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:123
#, python-format
msgid "Please complete customer's informations"
msgstr "Παρακαλώ συμπληρώστε τις πληροφορίες του πελάτη"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Please find below the guidelines of the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:544
#, python-format
msgid "Please wait"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:242
#, python-format
msgid "Please, wait while the file is uploading."
msgstr "Παρακαλώ περιμένετε όσο το αρχείο αποστέλεται"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,help:mail.field_res_users__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Πολιτική για τη αποστολή μηνύματος στο έγγραφο χρησιμοποιώντας το mailgateway.\n"
"- όλοι: ο καθένας μπορεί να αποστείλει\n"
"- συνεργάτες: μόνο οι επικυρωμένοι συνεργάτες \n"
"- ακόλουθοι: μόνο οι ακόλουθοι του σχετικού εγγράφου ή μέλη των παρακάτω καναλιών\n"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Δημιουργήθηκε με "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Προηγούμενες Δραστηριότητες"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preferred reply address"
msgstr "Προτιμητέες διευθύνσεις απαντήσεων"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid "Preferred response address (placeholders may be used here)"
msgstr ""
"Προτιμητέα διεύθυνση απάντησης (μπορούν να χρησιμοποιηθούν τα σύμβολα "
"κράτησης θέσης)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:70
#: code:addons/mail/static/src/xml/discuss.xml:275
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:54
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Προεπισκόπηση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Preview of"
msgstr "Προεπισκόπηση του"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:92
#, python-format
msgid "Previous"
msgstr "Προηγούμενο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Προηγούμενος Τύπος Δραστηριότητας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:76
#, python-format
msgid "Print"
msgstr "Εκτύπωση"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Μυστικότητα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:67
#: code:addons/mail/static/src/xml/discuss.xml:228
#, python-format
msgid "Private Channels"
msgstr "Ιδιωτικά κανάλια"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:1348
#, python-format
msgid "Public Channels"
msgstr "Δημόσια Κανάλια"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "Εκδότης: Ειδοποίηση Ενημέρωσης"

#. module: mail
#: selection:ir.ui.view,type:0
msgid "QWeb"
msgstr "QWeb"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:179
#, python-format
msgid "Re:"
msgstr "Re:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:400
#, python-format
msgid "Ready"
msgstr "Έτοιμη"

#. module: mail
#: selection:mail.notification,email_status:0
msgid "Ready to Send"
msgstr "Έτοιμο για Αποστολή"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0
msgid "Received"
msgstr "Παραλήφθηκε"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Παραλήπτης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Recipients"
msgstr "Αποδέκτες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Συνιστώμενος Τύπος Δράσης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__next_type_ids
msgid "Recommended Next Activities"
msgstr "Προτεινόμενες Επόμενες Δραστηριότητες"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Ταυτότητα εγγραφής νήματος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Παραπομπές"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:186
#, python-format
msgid "Reject"
msgstr "Απόρριψη"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:186
#, python-format
msgid "Reject selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:414
#, python-format
msgid "Reject |"
msgstr ""

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Rejected"
msgstr "Απορίφθηκε"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Κωδικός σχετικού εγγράφου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Μοντέλο Σχετικού Εγγράφου"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Όνομα Μοντέλου Σχετικού Εγγράφου"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Σχετικό Μήνυμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Σχετικός Συνεργάτης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Συσχετιζόμενο πεδίο"

#. module: mail
#: selection:mail.activity.type,category:0
msgid "Reminder"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:414
#, python-format
msgid "Remove message with explanation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:416
#, python-format
msgid "Remove message without explanation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Αφαιρέστε την συναφή ενέργεια για να χρησιμοποιήσετε αυτό το πρότυπο για τα "
"τα σχετικά έγγραφα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:53
#, python-format
msgid "Remove this follower"
msgstr "Αφαίρεση ακολούθου"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:343
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Απάντηση"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Διεύθυνση απάντησης ηλεκτρονικού ταχυδρομείου. Ρύθμιση της reply_to "
"παρακάμπτει την αυτόματη δημιουργία νήματος."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply-To"
msgstr "Απάντηση στο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_name
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Αρχείο Αναφοράς"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:69
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
msgid "Responsible"
msgstr "Υπεύθυνοι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Επανάληψη"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Περιεχόμενα εμπλουτισμένου κειμένου"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Rich-text/HTML μήνυμα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:73
#, python-format
msgid "Rotate"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "Διακομιστής SMTP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__res_id
msgid "Sample Document"
msgstr "Δείγμα εγγράφου"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Αποθήκευση ως ένα νέο πρότυπο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Αποθήκευση ως νέο πρότυπο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:63
#, python-format
msgid "Say something"
msgstr "Πείτε κάτι"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Προγραμματισμός"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:93
#, python-format
msgid "Schedule activity"
msgstr "Δραστηριότητα"

#. module: mail
#: code:addons/mail/models/mail_activity.py:395
#, python-format
msgid "Schedule an Activity"
msgstr "Προγραμματισμός μιας Δραστηριότητας"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:82
#, python-format
msgid "Schedule an activity"
msgstr "Προγραμματισμός μιας Δραστηριότητας"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
msgid "Scheduled Date"
msgstr "Προγραμματισμένη Ημερομηνία"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Προγραμματισμένη Ημερομηνία Αποστολής"

#. module: mail
#: selection:ir.ui.view,type:0
msgid "Search"
msgstr "Αναζήτηση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Αναζήτηση Ψευδώνυμου"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Αναζήτηση ομάδων"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Search Moderation List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:183
#, python-format
msgid "Select All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:183
#, python-format
msgid "Select all messages to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Επιλέξτε το πεδίο του στόχου από το σχετικό μοντέλο του  εγγράφου.\n"
"Αν πρόκειται για ένα πεδίο σχέσης θα είστε σε θέση να επιλέξετε ένα πεδίο-στόχο στον προορισμό της σχέσης."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""

#. module: mail
#: selection:mail.channel,public:0
msgid "Selected group of users"
msgstr "Επιλεγμένη ομάδα χρηστών"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:49
#: code:addons/mail/static/src/js/discuss.js:148
#: code:addons/mail/static/src/xml/composer.xml:16
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Αποστολή"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
#: selection:ir.actions.server,state:0
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
msgid "Send Email"
msgstr "Αποστολή Email"

#. module: mail
#: code:addons/mail/models/mail_template.py:265
#, python-format
msgid "Send Mail (%s)"
msgstr "Αποστολή Mail (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:72
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:56
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Αποστολή τώρα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:86
#, python-format
msgid "Send a message"
msgstr "Αποστολή ενός μηνύματος"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:145
#, python-format
msgid "Send explanation to author"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Send guidelines"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:86
#, python-format
msgid "Send message"
msgstr "Μήνυμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__email_send
msgid "Send messages by email"
msgstr "Αποστολή μηνυμάτων με email"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_from
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Διεύθυνση του αποστολέα (μπορούν να χρησιμοποιηθούν τα σύμβολα κράτησης "
"θέσης). Αν δεν έχει οριστεί, η προεπιλεγμένη τιμή θα είναι το ψευδώνυμο "
"email του συντάκτη αν ρυθμιστεί, ή η διεύθυνση email."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:91
#, python-format
msgid "Sending Error"
msgstr "Σφάλμα Αποστολής"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:141
#, python-format
msgid "Sends messages by email"
msgstr "Αποστολή μηνυμάτων με email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:397
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0 selection:mail.notification,email_status:0
#, python-format
msgid "Sent"
msgstr "Εστάλη"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Sent by"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_email
msgid "Sent by Email"
msgstr "Αποστολή με Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Server Action"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Σύντομοι κωδικοί"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Συντόμευση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: mail
#: code:addons/mail/models/mail_channel.py:914
#, python-format
msgid "Show an helper message"
msgstr "Εμφάνιση βοηθητικού μηνύματος"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Ενέργεια Πλευρικής Εργαλειοθήκης"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Δράση της πλευρικής εργαλειοθήκης για να κάνει αυτό το πρότυπο διαθέσιμο "
"στις εγγραφές του εγγράφου που σχετίζονται με το μοντέλο"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_avatar
#: model:ir.model.fields,help:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,help:mail.field_mail_message__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Μικρού μεγέθους εικόνα για αυτή την επαφή. Αλλάζει αυτόματα ως εικόνα 64x64 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιείστε αυτό "
"το πεδίο οπουδήποτε απαιτείται μια μικρή εικόνα."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_small
msgid "Small-sized photo"
msgstr "Μικρού μεγέθους φωτογραφία"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image_small
msgid ""
"Small-sized photo of the group. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Μικρού μεγέθους φωτογραφία της ομάδας. Αλλάζει αυτόματα ως εικόνα 64x64 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιήστε αυτό "
"το πεδίο οπουδήποτε απαιτείται μια μικρή εικόνα."

#. module: mail
#: selection:ir.actions.server,activity_user_type:0
msgid "Specific User"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_id
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Καθορίστε ένα μοντέλο εάν η δραστηριότητα πρέπει να είναι συγκεκριμένη για "
"ένα μοντέλο και δεν είναι διαθέσιμη κατά τη διαχείριση δραστηριοτήτων για "
"άλλα μοντέλα."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:47
#, python-format
msgid "Split"
msgstr "Διαχωρισμός"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1237
#: code:addons/mail/static/src/xml/discuss.xml:33
#: code:addons/mail/static/src/xml/discuss.xml:208
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__starred
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Με αστέρι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_moderation__status
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Κατάσταση"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Υπο-πεδίο"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Υπο-μοντέλο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:8
#: code:addons/mail/static/src/xml/discuss.xml:197
#: model:ir.model.fields,field_description:mail.field_email_template_preview__subject
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#, python-format
msgid "Subject"
msgstr "Θέμα"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__subject
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Θέμα (μπορεί να χρησιμοποιηθεί η κράτηση θέσης)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Θέμα..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:365
#, python-format
msgid "Subject:"
msgstr "Θέμα:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Υποκατάσταση"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Υποτύπος"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Υποτύποι"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Summary"
msgstr "Περίληψη"

#. module: mail
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "System notification"
msgstr "Ειδοποιήσεις Συστήματος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "Τεχνικό πεδίο για προορισμό UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model_id
msgid ""
"Technical field to keep trace of the model at the beginning of the edition "
"for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.wizard_email_template_preview
msgid "Template Preview"
msgstr "Προεπισκόπηση προτύπου"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Πρότυπα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "Thank you!"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "Το"

#. module: mail
#: code:addons/mail/models/ir_actions.py:51
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr ""

#. module: mail
#: sql_constraint:mail.moderation:0
msgid "The email address must be unique per channel !"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "Ο κώδικας HTML που αντικαθιστά τη συντόμευση"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Το μοντέλο (Odoo Document Kind) στο οποίο αντιστοιχεί αυτό το ψευδώνυμο. "
"Κάθε εισερχόμενο email που δεν έχει απαντηθεί σε υπάρχουσα εγγραφή, θα "
"προκαλέσει τη δημιουργία μίας νέας εγγραφής αυτού του μοντέλου (π.χ. Εργασία"
" Έργου)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Το όνομα του ψευδώνυμου email , π.χ. 'jobs', αν θέλετε να πάρετε emails για "
"<<EMAIL>>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Ο ιδιοκτήτης των εγγραφών που δημιουργούνται κατά τη λήψη μηνυμάτων ηλεκτρονικού ταχυδρομείου σε αυτό το ψευδώνυμο. Αν αυτό το πεδίο δεν είναι ρυθμισμένο το σύστημα θα προσπαθήσει να βρει το σωστό ιδιοκτήτη με βάση τον αποστολέα (Από) τη διεύθυνση.\n"
"Διαφορετικά, θα χρησιμοποιήσει το λογαριασμό διαχειριστή, αν ο χρήστης δεν έχει βρεθεί στο σύστημα για τη συγκεκριμένη διεύθυνση."

#. module: mail
#: code:addons/mail/models/mail_activity.py:243
#: code:addons/mail/models/mail_message.py:704
#: code:addons/mail/models/mail_message.py:874
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Η λειτουργία που ζητήθηκε δεν μπορεί να ολοκληρωθεί λόγω των περιορισμών ασφαλείας. Παρακαλούμε επικοινωνήστε με τον διαχειριστή του συστήματός σας.\n"
"\n"
"(Τύπος εγγράφου: %s, Λειτουργία: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "Η συντόμευση που πρέπει να αντικατασταθεί στα Μηνύματα Συνομιλίας"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_id
#: model:ir.model.fields,help:mail.field_mail_template__model_id
msgid "The type of document this template can be used with"
msgstr "Ο τύπος εγγράφου που μπορεί να χρησιμοποιηθεί με αυτό το πρότυπο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Αυτό"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:10
#, python-format
msgid "This action will send an email."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image
msgid ""
"This field holds the image used as photo for the group, limited to "
"1024x1024px."
msgstr ""
"Το πεδίο αυτό αποθηκεύει την εικόνα που χρησιμοποιείται ως φωτογραφία για "
"την ομάδα, με μέγιστο μέγεθος τα 1024x1024 εικονοστοιχεία."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Αυτή η ομάδα είναι ορατή από τα μη μέλη. Μπορείτε να προσθέσετε μέλη σε "
"αόρατη ομάδα μέσω του κουμπιού 'Πρόσκληση'."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Νήμα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
msgid "To"
msgstr "Σε"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Προς (Emails)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Προς (Συνεργάτες)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Προς υλοποίηση"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:39
#: code:addons/mail/static/src/xml/thread_window.xml:10
#, python-format
msgid "To:"
msgstr "Προς:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:83
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:107
#: code:addons/mail/static/src/xml/systray.xml:96
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
#, python-format
msgid "Today"
msgstr "Σήμερα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:93
#, python-format
msgid "Tomorrow"
msgstr "Αύριο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Τα θέματα που συζητήθηκαν σε αυτή την ομάδα..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Οι τιμές παρακολούθησης αποθηκεύονται σε ξεχωριστό μοντέλο. Αυτό το πεδίο "
"επιτρέπει την αναδόμηση της παρακολούθησης και τη δημιουργία στατιστικών "
"στοιχείων για το μοντέλο."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__track_visibility
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Tracking"
msgstr "Παρακολούθηση"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Τιμή Παρακολούθησης"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Τιμές Παρακολούθησης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__track_sequence
msgid "Tracking field sequence"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Τιμές παρακολούθησης"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Tree"
msgstr "Δέντρο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:23
#, python-format
msgid ""
"Try to add some activity on records, or make sure that\n"
"                there is no active filter in the search bar."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Τύπος"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create or Copy a new Record': create a new record with new values, or copy an existing record in your database\n"
"- 'Write on a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Add Followers': add followers to a record (available in Discuss)\n"
"- 'Send Email': automatically send an email (available in email_template)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:241
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Δεν είναι δυνατή η σύνδεση με το διακομιστή SMTP"

#. module: mail
#: code:addons/mail/models/mail_thread.py:2207
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:2172
#, python-format
msgid "Unable to notify message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:34
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:163
#, python-format
msgid "Undefined"
msgstr "Μη ορισμένο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:22
#, python-format
msgid "Unfollow"
msgstr "Άρση ακολούθησης"

#. module: mail
#: sql_constraint:mail.alias:0
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""
"Δυστυχώς αυτό το ψευδώνυμο email χρησιμοποιείται ήδη, παρακαλώ επιλέξτε "
"κάποιο άλλο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Unit"
msgstr "Μονάδα"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:52
#: selection:mail.notification,failure_type:0
#, python-format
msgid "Unknown error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:35
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
#, python-format
msgid "Unread messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:184
#, python-format
msgid "Unselect All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:184
#, python-format
msgid "Unselect all messages to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:180
#, python-format
msgid "Unstar all"
msgstr "Αφαίρεση αστεριού από όλα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:180
#, python-format
msgid "Unstar all messages"
msgstr "Αφαίρεση αστεριού από όλα τα μηνύματα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:341
#, python-format
msgid "Unsubscribe"
msgstr "Κατάργηση εγγραφής"

#. module: mail
#: code:addons/mail/models/mail_template.py:473
#, python-format
msgid "Unsupported report type %s found."
msgstr "Βρέθηκε μη υποστηριζόμενος τύπος αναφοράς %s."

#. module: mail
#: code:addons/mail/models/mail_message.py:188
#, python-format
msgid "Unsupported search filter on moderation status"
msgstr ""

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Update the Record"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:531
#, python-format
msgid "Uploaded"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:523
#, python-format
msgid "Uploading"
msgstr "Αποστέλεται"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:242
#, python-format
msgid "Uploading error"
msgstr "Σφάλμα κατά την μεταφόρτωση"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Χρήση ενεργού τομέα"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Χρήση προτύπου"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use your own email servers"
msgstr "Χρησιμοποιήστε τους δικούς σας διακομιστές ηλεκτρονικού ταχυδρομείου"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Χρησιμοποιείται για να ταξινομήσει υποτύπους."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:61
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "User"
msgstr "Χρήστης"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
msgid "User field name"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:62
#: code:addons/mail/static/src/xml/thread_window.xml:11
#, python-format
msgid "User name"
msgstr "Όνομα χρήστη"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Χρήστες"

#. module: mail
#: code:addons/mail/models/mail_channel.py:959
#, python-format
msgid "Users in this channel: %s %s and you."
msgstr "Χρήστες αυτού του καναλιού: %s %s και εσείς."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Η χρήση του δικού σας διακομιστή ηλεκτρονικού ταχυδρομείου απαιτείται για "
"την αποστολή / λήψη μηνυμάτων ηλεκτρονικού ταχυδρομείου στις εκδόσεις "
"Community και Enterprise. Οι χρήστες του προϊόντος Odoo Online ήδη "
"επωφελούνται από έναν έτοιμο προς χρήση διακομιστή ηλεκτρονικού ταχυδρομείου"
" (@mycompany.odoo.com)."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:41
#, python-format
msgid "Video"
msgstr "Βίντεο"

#. module: mail
#: code:addons/mail/models/mail_thread.py:747
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Προβολή"

#. module: mail
#: code:addons/mail/models/mail_thread.py:745
#, python-format
msgid "View %s"
msgstr "Προβολή %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Τύπος Προβολής"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:99
#, python-format
msgid "View all the attachments of the current record"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:57
#, python-format
msgid "Viewer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:85
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:243
#, python-format
msgid ""
"Warning! \n"
" If you remove a follower, he won't be notified of any email or discussion on this document.\n"
" Do you really want to remove this follower ?"
msgstr ""
"Προειδοποίηση!\n"
"Αν αφαιρέσετε έναν ακόλουθο, δεν θα θα ενημερώνεται πλέον με κάποιο email ή συζήτηση για αυτό το έγγραφο. \n"
"Θέλετε πραγματικά να καταργήσετε αυτόν τον ακόλουθο;"

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Weeks"
msgstr "Εβδομάδες"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Όταν ένα πεδίο σχέσης επιλέγεται ως πρώτο πεδίο, αυτό το πεδίο σας επιτρέπει"
" να επιλέξετε το πεδίο-στόχο μέσα στο μοντέλο του εγγράφου προορισμού (υπο-"
"μοντέλο)."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Όταν ένα πεδίο σχέσης επιλέγεται ως πρώτο πεδίο, αυτό το πεδίο δείχνει το "
"μοντέλο του εγγράφου, η σχέση πηγαίνει."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__track_visibility
msgid ""
"When set, every modification to this field will be tracked in the chatter."
msgstr ""
"Όταν έχει οριστεί, κάθε τροποποίηση αυτού του πεδίου θα παρακολουθείται στην"
" Συζήτηση."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""
"Κατά πόσο το μήνυμα είναι ένα εσωτερικό σημείωμα (μόνο σε κατάσταση σχολίου)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "Κατά πόσο αυτό το μοντέλο υποστηρίζει μηνύματα και ειδοποιήσεις."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Ποιος μπορεί να ακολουθήσει τις δραστηριότητες της ομάδας;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:96
#, python-format
msgid "Write Feedback"
msgstr "Γράψτε ένα σχόλιο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:12
#, python-format
msgid "Write something..."
msgstr "Γράψτε κάτι..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:87
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:109
#, python-format
msgid "Yesterday"
msgstr "Χθες"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:101
#, python-format
msgid "You added <b>%s</b> to the conversation."
msgstr "Έχετε προστεθεί <b>%s</b> στη συνομιλία."

#. module: mail
#: code:addons/mail/models/mail_channel.py:956
#, python-format
msgid "You are alone in this channel."
msgstr "Είστε μόνος/η σε αυτό το κανάλι."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:367
#, python-format
msgid "You are going to ban: %s. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:390
#, python-format
msgid "You are going to discard %s messages. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:394
#, python-format
msgid "You are going to discard 1 message. Do you confirm the action?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid ""
"You are going to send the guidelines to all the subscribers. Do you confirm "
"the action?"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:925
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "Είστε σε ιδιωτική συνομιλία με <b>@%s</b>."

#. module: mail
#: code:addons/mail/models/mail_channel.py:919
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Είστε στο κανάλι <b>#%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:338
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to "
"unsubscribe?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:135
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:87
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Δεν μπορείτε να δημιουργήσετε ένα νέο χρήστη από εδώ.\n"
"Για να δημιουργήσετε νέο χρήστη παρακαλώ πηγαίνετε στο πίνακα διαμόρφωσης."

#. module: mail
#: code:addons/mail/models/mail_channel.py:241
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Δεν μπορείτε να διαγράψετε αυτές τις ομάδες, όπως η ομάδα \"Όλη η Εταιρεία\""
" απαιτείται από άλλες μονάδες."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Έχετε ανατεθεί στο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:198
#, python-format
msgid "You have been invited to: "
msgstr "Έχετε προσκληθεί να:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:138
#, python-format
msgid "You have no message to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Μπορείτε να επισυνάψετε αρχεία σε αυτό το πρότυπο, που θα προστεθούν σε όλα "
"τα μηνύματα ηλεκτρονικού ταχυδρομείου που δημιουργούνται από αυτό το πρότυπο"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:444
#, python-format
msgid "You unpinned your conversation with <b>%s</b>."
msgstr "Ξεκαρφιτσώσατε την συνομιλία σας με <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:439
#, python-format
msgid "You unsubscribed from <b>%s</b>."
msgstr "Έχετε καταργήσει την εγγραφή σας από <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:291
#, python-format
msgid "You:"
msgstr "Εσείς:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:91
#, python-format
msgid "Your message has not been sent."
msgstr "Το μήνυμά σας δεν έχει αποσταλεί."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:424
#, python-format
msgid "Your message is pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:198
#, python-format
msgid "Your message was rejected by moderator."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_actions.py:57
#, python-format
msgid "Your template should define email_from"
msgstr "Το πρότυπό σας θα πρέπει να καθορίσει το email_from"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:68
#, python-format
msgid "Zoom In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:70
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_from:0
msgid "after previous activity deadline"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_from:0
msgid "after validation date"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "alias %s: %s"
msgstr "ψευδώνυμο %s: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "πίνακας-συναντήσεων"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:43
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "από"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "channel."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "created"
msgstr "δημιουργήθηκε"

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "days"
msgstr "ημέρες"

#. module: mail
#: code:addons/mail/models/mail_thread.py:357
#, python-format
msgid "document"
msgstr "εγγραφή"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid ""
"done\n"
"        by"
msgstr ""
"ολοκληρώθηκε\n"
"από"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:45
#, python-format
msgid "e.g. 1-5, 7, 8-9"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "π.χ. Συζήτηση για προσφορά"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:190
#, python-format
msgid "followers"
msgstr "ακόλουθοι"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:30
#, python-format
msgid "for"
msgstr "για"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "from:"
msgstr "από:"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "γενικά"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been"
msgstr "ήταν"

#. module: mail
#: code:addons/mail/models/mail_alias.py:272
#, python-format
msgid "incorrectly configured alias"
msgstr "λανθασμένη ρύθμιση ψευδώνυμου"

#. module: mail
#: code:addons/mail/models/mail_alias.py:268
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "λάνθασμένη διαμόρφωση ψευδώνυμου (άγνωστη αναφορά εγγραφής)"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1051
#, python-format
msgid "model %s does not accept document creation"
msgstr "το μοντέλο %s δεν δέχεται την δημιουργία εγγράφων"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1046
#, python-format
msgid "model %s does not accept document update"
msgstr "το μοντέλο %s δεν δέχεται την ενημέρωση εγγράφων"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1039
#, python-format
msgid ""
"model %s does not accept document update, fall back on document creation"
msgstr ""
"το μοντέλο %s δεν δέχεται την ενημέρωση εγγράφων, επιστροφή στην δημιουργία "
"εγγράφων"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "modified"
msgstr "τροποποιήθηκε"

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "months"
msgstr "μήνες"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "mycompany.odoo.com"
msgstr "mycompany.odoo.com"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:108
#, python-format
msgid "now"
msgstr "τώρα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:323
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "στις"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:71
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:55
#, python-format
msgid "or"
msgstr "ή"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1021
#, python-format
msgid ""
"posting a message without model should be with a null res_id (private "
"message), received %s"
msgstr ""
"η δημοσίευση μηνύματος χωρίς μοντέλο, θα πρέπει να έχει τιμή Κενό (null) το "
"πεδίο res_id (ιδιωτικό μήνυμα), λήφθηκε %s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1025
#, python-format
msgid ""
"posting a message without model should be with a parent_id (private message)"
msgstr ""
"η δημοσίευση μηνύματος χωρίς μοντέλο, θα πρέπει να έχει τιμή πεδίο parent_id"
" (ιδιωτικό μήνυμα)"

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "rd"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:20
#, python-format
msgid "read less"
msgstr "διαβάστε λιγότερα"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:19
#, python-format
msgid "read more"
msgstr "διαβάστε περισσότερα"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "record:"
msgstr "εγγραφή:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1043
#, python-format
msgid "reply to missing document (%s,%s)"
msgstr "απάντηση στο έγγραφο που λείπει (%s,%s)"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1036
#, python-format
msgid "reply to missing document (%s,%s), fall back on new document creation"
msgstr ""
"απάντηση στο έγγραφο που λείπει (%s,%s), επιστροφή στην δημιουργία νέου "
"εγγράφου"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1021
#, python-format
msgid "resetting thread_id"
msgstr "επαναφορά thread_id"

#. module: mail
#: code:addons/mail/models/mail_channel.py:407
#, python-format
msgid "restricted to channel members"
msgstr "περιορίζεται στα μέλη του καναλιού"

#. module: mail
#: code:addons/mail/models/mail_alias.py:277
#, python-format
msgid "restricted to followers"
msgstr "περιορίζεται στους ακόλουθους"

#. module: mail
#: code:addons/mail/models/mail_alias.py:281
#, python-format
msgid "restricted to known authors"
msgstr "περιορίζεται στους γνωστούς συγγραφείς"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "πωλήσεις"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1025
#: code:addons/mail/models/mail_thread.py:1043
#: code:addons/mail/models/mail_thread.py:1046
#: code:addons/mail/models/mail_thread.py:1051
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "skipping"
msgstr "παρακάμπτοντας"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:45
#, python-format
msgid "this document"
msgstr "τρέχον έγγραφο"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "unknown error"
msgstr "άγνωστο σφάλμα"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1014
#, python-format
msgid "unknown target model %s"
msgstr "άγνωστο μοντέλο στόχου %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "weeks"
msgstr ""
