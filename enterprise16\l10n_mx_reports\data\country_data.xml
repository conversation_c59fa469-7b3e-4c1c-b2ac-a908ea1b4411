<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.ad" model="res.country">
            <field name="demonym">Andorran</field>
        </record>
        <record id="base.ae" model="res.country">
            <field name="demonym">Emirati</field>
        </record>
        <record id="base.af" model="res.country">
            <field name="demonym">Afghan</field>
        </record>
        <record id="base.ag" model="res.country">
            <field name="demonym">Antiguan, Barbudan</field>
        </record>
        <record id="base.ai" model="res.country">
            <field name="demonym">Anguillian</field>
        </record>
        <record id="base.al" model="res.country">
            <field name="demonym">Albanian</field>
        </record>
        <record id="base.am" model="res.country">
            <field name="demonym">Armenian</field>
        </record>
        <record id="base.ao" model="res.country">
            <field name="demonym">Angolan</field>
        </record>
        <record id="base.aq" model="res.country">
            </record>
        <record id="base.ar" model="res.country">
            <field name="demonym">Argentinean</field>
        </record>
        <record id="base.as" model="res.country">
            <field name="demonym">American Samoan</field>
        </record>
        <record id="base.at" model="res.country">
            <field name="demonym">Austrian</field>
        </record>
        <record id="base.au" model="res.country">
            <field name="demonym">Australian</field>
        </record>
        <record id="base.aw" model="res.country">
            <field name="demonym">Aruban</field>
        </record>
        <record id="base.ax" model="res.country">
            <field name="demonym">Ålandish</field>
        </record>
        <record id="base.az" model="res.country">
            <field name="demonym">Azerbaijani</field>
        </record>
        <record id="base.ba" model="res.country">
            <field name="demonym">Bosnian, Herzegovinian</field>
        </record>
        <record id="base.bb" model="res.country">
            <field name="demonym">Barbadian</field>
        </record>
        <record id="base.bd" model="res.country">
            <field name="demonym">Bangladeshi</field>
        </record>
        <record id="base.be" model="res.country">
            <field name="demonym">Belgian</field>
        </record>
        <record id="base.bf" model="res.country">
            <field name="demonym">Burkinabe</field>
        </record>
        <record id="base.bg" model="res.country">
            <field name="demonym">Bulgarian</field>
        </record>
        <record id="base.bh" model="res.country">
            <field name="demonym">Bahraini</field>
        </record>
        <record id="base.bi" model="res.country">
            <field name="demonym">Burundian</field>
        </record>
        <record id="base.bj" model="res.country">
            <field name="demonym">Beninese</field>
        </record>
        <record id="base.bl" model="res.country">
            <field name="demonym">Saint Barthélemy Islander</field>
        </record>
        <record id="base.bm" model="res.country">
            <field name="demonym">Bermudian</field>
        </record>
        <record id="base.bn" model="res.country">
            <field name="demonym">Bruneian</field>
        </record>
        <record id="base.bo" model="res.country">
            <field name="demonym">Bolivian</field>
        </record>
        <record id="base.bq" model="res.country">
            <field name="demonym">Dutch</field>
        </record>
        <record id="base.br" model="res.country">
            <field name="demonym">Brazilian</field>
        </record>
        <record id="base.bs" model="res.country">
            <field name="demonym">Bahamian</field>
        </record>
        <record id="base.bt" model="res.country">
            <field name="demonym">Bhutanese</field>
        </record>
        <record id="base.bv" model="res.country">
            </record>
        <record id="base.bw" model="res.country">
            <field name="demonym">Motswana</field>
        </record>
        <record id="base.by" model="res.country">
            <field name="demonym">Belarusian</field>
        </record>
        <record id="base.bz" model="res.country">
            <field name="demonym">Belizean</field>
        </record>
        <record id="base.ca" model="res.country">
            <field name="demonym">Canadian</field>
        </record>
        <record id="base.cc" model="res.country">
            <field name="demonym">Cocos Islander</field>
        </record>
        <record id="base.cf" model="res.country">
            <field name="demonym">Central African</field>
        </record>
        <record id="base.cd" model="res.country">
            <field name="demonym">Congolese</field>
        </record>
        <record id="base.cg" model="res.country">
            <field name="demonym">Congolese</field>
        </record>
        <record id="base.ch" model="res.country">
            <field name="demonym">Swiss</field>
        </record>
        <record id="base.ci" model="res.country">
            <field name="demonym">Ivorian</field>
        </record>
        <record id="base.ck" model="res.country">
            <field name="demonym">Cook Islander</field>
        </record>
        <record id="base.cl" model="res.country">
            <field name="demonym">Chilean</field>
        </record>
        <record id="base.cm" model="res.country">
            <field name="demonym">Cameroonian</field>
        </record>
        <record id="base.cn" model="res.country">
            <field name="demonym">Chinese</field>
        </record>
        <record id="base.co" model="res.country">
            <field name="demonym">Colombian</field>
        </record>
        <record id="base.cr" model="res.country">
            <field name="demonym">Costa Rican</field>
        </record>
        <record id="base.cu" model="res.country">
            <field name="demonym">Cuban</field>
        </record>
        <record id="base.cv" model="res.country">
            <field name="demonym">Cape Verdian</field>
        </record>
        <record id="base.cw" model="res.country">
            <field name="demonym">Dutch</field>
        </record>
        <record id="base.cx" model="res.country">
            <field name="demonym">Christmas Island</field>
        </record>
        <record id="base.cy" model="res.country">
            <field name="demonym">Cypriot</field>
        </record>
        <record id="base.cz" model="res.country">
            <field name="demonym">Czech</field>
        </record>
        <record id="base.de" model="res.country">
            <field name="demonym">German</field>
        </record>
        <record id="base.dj" model="res.country">
            <field name="demonym">Djibouti</field>
        </record>
        <record id="base.dk" model="res.country">
            <field name="demonym">Danish</field>
        </record>
        <record id="base.dm" model="res.country">
            <field name="demonym">Dominican</field>
        </record>
        <record id="base.do" model="res.country">
            <field name="demonym">Dominican</field>
        </record>
        <record id="base.dz" model="res.country">
            <field name="demonym">Algerian</field>
        </record>
        <record id="base.ec" model="res.country">
            <field name="demonym">Ecuadorean</field>
        </record>
        <record id="base.ee" model="res.country">
            <field name="demonym">Estonian</field>
        </record>
        <record id="base.eg" model="res.country">
            <field name="demonym">Egyptian</field>
        </record>
        <record id="base.eh" model="res.country">
            <field name="demonym">Sahrawi</field>
        </record>
        <record id="base.er" model="res.country">
            <field name="demonym">Eritrean</field>
        </record>
        <record id="base.es" model="res.country">
            <field name="demonym">Spanish</field>
        </record>
        <record id="base.et" model="res.country">
            <field name="demonym">Ethiopian</field>
        </record>
        <record id="base.fi" model="res.country">
            <field name="demonym">Finnish</field>
        </record>
        <record id="base.fj" model="res.country">
            <field name="demonym">Fijian</field>
        </record>
        <record id="base.fk" model="res.country">
            <field name="demonym">Falkland Islander</field>
        </record>
        <record id="base.fm" model="res.country">
            <field name="demonym">Micronesian</field>
        </record>
        <record id="base.fo" model="res.country">
            <field name="demonym">Faroese</field>
        </record>
        <record id="base.fr" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.ga" model="res.country">
            <field name="demonym">Gabonese</field>
        </record>
        <record id="base.gd" model="res.country">
            <field name="demonym">Grenadian</field>
        </record>
        <record id="base.ge" model="res.country">
            <field name="demonym">Georgian</field>
        </record>
        <record id="base.gf" model="res.country">
            </record>
        <record id="base.gh" model="res.country">
            <field name="demonym">Ghanaian</field>
        </record>
        <record id="base.gi" model="res.country">
            <field name="demonym">Gibraltar</field>
        </record>
        <record id="base.gg" model="res.country">
            <field name="demonym">Channel Islander</field>
        </record>
        <record id="base.gl" model="res.country">
            <field name="demonym">Greenlandic</field>
        </record>
        <record id="base.gm" model="res.country">
            <field name="demonym">Gambian</field>
        </record>
        <record id="base.gn" model="res.country">
            <field name="demonym">Guinean</field>
        </record>
        <record id="base.gp" model="res.country">
            <field name="demonym">Guadeloupian</field>
        </record>
        <record id="base.gq" model="res.country">
            <field name="demonym">Equatorial Guinean</field>
        </record>
        <record id="base.gr" model="res.country">
            <field name="demonym">Greek</field>
        </record>
        <record id="base.gs" model="res.country">
            <field name="demonym">South Georgia and the South Sandwich Islander</field>
        </record>
        <record id="base.gt" model="res.country">
            <field name="demonym">Guatemalan</field>
        </record>
        <record id="base.gu" model="res.country">
            <field name="demonym">Guamanian</field>
        </record>
        <record id="base.gw" model="res.country">
            <field name="demonym">Guinea-Bissauan</field>
        </record>
        <record id="base.gy" model="res.country">
            <field name="demonym">Guyanese</field>
        </record>
        <record id="base.hk" model="res.country">
            <field name="demonym">Chinese</field>
        </record>
        <record id="base.hm" model="res.country">
            <field name="demonym">Heard and McDonald Islander</field>
        </record>
        <record id="base.hn" model="res.country">
            <field name="demonym">Honduran</field>
        </record>
        <record id="base.hr" model="res.country">
            <field name="demonym">Croatian</field>
        </record>
        <record id="base.ht" model="res.country">
            <field name="demonym">Haitian</field>
        </record>
        <record id="base.hu" model="res.country">
            <field name="demonym">Hungarian</field>
        </record>
        <record id="base.id" model="res.country">
            <field name="demonym">Indonesian</field>
        </record>
        <record id="base.ie" model="res.country">
            <field name="demonym">Irish</field>
        </record>
        <record id="base.il" model="res.country">
            <field name="demonym">Israeli</field>
        </record>
        <record id="base.im" model="res.country">
            <field name="demonym">Manx</field>
        </record>
        <record id="base.in" model="res.country">
            <field name="demonym">Indian</field>
        </record>
        <record id="base.io" model="res.country">
            <field name="demonym">Indian</field>
        </record>
        <record id="base.iq" model="res.country">
            <field name="demonym">Iraqi</field>
        </record>
        <record id="base.ir" model="res.country">
            <field name="demonym">Iranian</field>
        </record>
        <record id="base.is" model="res.country">
            <field name="demonym">Icelander</field>
        </record>
        <record id="base.it" model="res.country">
            <field name="demonym">Italian</field>
        </record>
        <record id="base.je" model="res.country">
            <field name="demonym">Channel Islander</field>
        </record>
        <record id="base.jm" model="res.country">
            <field name="demonym">Jamaican</field>
        </record>
        <record id="base.jo" model="res.country">
            <field name="demonym">Jordanian</field>
        </record>
        <record id="base.jp" model="res.country">
            <field name="demonym">Japanese</field>
        </record>
        <record id="base.ke" model="res.country">
            <field name="demonym">Kenyan</field>
        </record>
        <record id="base.kg" model="res.country">
            <field name="demonym">Kirghiz</field>
        </record>
        <record id="base.kh" model="res.country">
            <field name="demonym">Cambodian</field>
        </record>
        <record id="base.ki" model="res.country">
            <field name="demonym">I-Kiribati</field>
        </record>
        <record id="base.km" model="res.country">
            <field name="demonym">Comoran</field>
        </record>
        <record id="base.kn" model="res.country">
            <field name="demonym">Kittian and Nevisian</field>
        </record>
        <record id="base.kp" model="res.country">
            <field name="demonym">North Korean</field>
        </record>
        <record id="base.kr" model="res.country">
            <field name="demonym">South Korean</field>
        </record>
        <record id="base.kw" model="res.country">
            <field name="demonym">Kuwaiti</field>
        </record>
        <record id="base.ky" model="res.country">
            <field name="demonym">Caymanian</field>
        </record>
        <record id="base.kz" model="res.country">
            <field name="demonym">Kazakhstani</field>
        </record>
        <record id="base.la" model="res.country">
            <field name="demonym">Laotian</field>
        </record>
        <record id="base.lb" model="res.country">
            <field name="demonym">Lebanese</field>
        </record>
        <record id="base.lc" model="res.country">
            <field name="demonym">Saint Lucian</field>
        </record>
        <record id="base.li" model="res.country">
            <field name="demonym">Liechtensteiner</field>
        </record>
        <record id="base.lk" model="res.country">
            <field name="demonym">Sri Lankan</field>
        </record>
        <record id="base.lr" model="res.country">
            <field name="demonym">Liberian</field>
        </record>
        <record id="base.ls" model="res.country">
            <field name="demonym">Mosotho</field>
        </record>
        <record id="base.lt" model="res.country">
            <field name="demonym">Lithuanian</field>
        </record>
        <record id="base.lu" model="res.country">
            <field name="demonym">Luxembourger</field>
        </record>
        <record id="base.lv" model="res.country">
            <field name="demonym">Latvian</field>
        </record>
        <record id="base.ly" model="res.country">
            <field name="demonym">Libyan</field>
        </record>
        <record id="base.ma" model="res.country">
            <field name="demonym">Moroccan</field>
        </record>
        <record id="base.mc" model="res.country">
            <field name="demonym">Monegasque</field>
        </record>
        <record id="base.md" model="res.country">
            <field name="demonym">Moldovan</field>
        </record>
        <record id="base.me" model="res.country">
            <field name="demonym">Montenegrin</field>
        </record>
        <record id="base.mf" model="res.country">
            <field name="demonym">Saint Martin Islander</field>
        </record>
        <record id="base.mg" model="res.country">
            <field name="demonym">Malagasy</field>
        </record>
        <record id="base.mh" model="res.country">
            <field name="demonym">Marshallese</field>
        </record>
        <record id="base.mk" model="res.country">
            <field name="demonym">Macedonian</field>
        </record>
        <record id="base.ml" model="res.country">
            <field name="demonym">Malian</field>
        </record>
        <record id="base.mm" model="res.country">
            <field name="demonym">Burmese</field>
        </record>
        <record id="base.mn" model="res.country">
            <field name="demonym">Mongolian</field>
        </record>
        <record id="base.mo" model="res.country">
            <field name="demonym">Chinese</field>
        </record>
        <record id="base.mp" model="res.country">
            <field name="demonym">American</field>
        </record>
        <record id="base.mq" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.mr" model="res.country">
            <field name="demonym">Mauritanian</field>
        </record>
        <record id="base.ms" model="res.country">
            <field name="demonym">Montserratian</field>
        </record>
        <record id="base.mt" model="res.country">
            <field name="demonym">Maltese</field>
        </record>
        <record id="base.mu" model="res.country">
            <field name="demonym">Mauritian</field>
        </record>
        <record id="base.mv" model="res.country">
            <field name="demonym">Maldivan</field>
        </record>
        <record id="base.mw" model="res.country">
            <field name="demonym">Malawian</field>
        </record>
        <record id="base.mx" model="res.country">
            <field name="demonym">Mexican</field>
        </record>
        <record id="base.my" model="res.country">
            <field name="demonym">Malaysian</field>
        </record>
        <record id="base.mz" model="res.country">
            <field name="demonym">Mozambican</field>
        </record>
        <record id="base.na" model="res.country">
            <field name="demonym">Namibian</field>
        </record>
        <record id="base.nc" model="res.country">
            <field name="demonym">New Caledonian</field>
        </record>
        <record id="base.ne" model="res.country">
            <field name="demonym">Nigerien</field>
        </record>
        <record id="base.nf" model="res.country">
            <field name="demonym">Norfolk Islander</field>
        </record>
        <record id="base.ng" model="res.country">
            <field name="demonym">Nigerian</field>
        </record>
        <record id="base.ni" model="res.country">
            <field name="demonym">Nicaraguan</field>
        </record>
        <record id="base.nl" model="res.country">
            <field name="demonym">Dutch</field>
        </record>
        <record id="base.no" model="res.country">
            <field name="demonym">Norwegian</field>
        </record>
        <record id="base.np" model="res.country">
            <field name="demonym">Nepalese</field>
        </record>
        <record id="base.nr" model="res.country">
            <field name="demonym">Nauruan</field>
        </record>
        <record id="base.nu" model="res.country">
            <field name="demonym">Niuean</field>
        </record>
        <record id="base.nz" model="res.country">
            <field name="demonym">New Zealander</field>
        </record>
        <record id="base.om" model="res.country">
            <field name="demonym">Omani</field>
        </record>
        <record id="base.pa" model="res.country">
            <field name="demonym">Panamanian</field>
        </record>
        <record id="base.pe" model="res.country">
            <field name="demonym">Peruvian</field>
        </record>
        <record id="base.pf" model="res.country">
            <field name="demonym">French Polynesian</field>
        </record>
        <record id="base.pg" model="res.country">
            <field name="demonym">Papua New Guinean</field>
        </record>
        <record id="base.ph" model="res.country">
            <field name="demonym">Filipino</field>
        </record>
        <record id="base.pk" model="res.country">
            <field name="demonym">Pakistani</field>
        </record>
        <record id="base.pl" model="res.country">
            <field name="demonym">Polish</field>
        </record>
        <record id="base.pm" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.pn" model="res.country">
            <field name="demonym">Pitcairn Islander</field>
        </record>
        <record id="base.pr" model="res.country">
            <field name="demonym">Puerto Rican</field>
        </record>
        <record id="base.ps" model="res.country">
            <field name="demonym">Palestinian</field>
        </record>
        <record id="base.pt" model="res.country">
            <field name="demonym">Portuguese</field>
        </record>
        <record id="base.pw" model="res.country">
            <field name="demonym">Palauan</field>
        </record>
        <record id="base.py" model="res.country">
            <field name="demonym">Paraguayan</field>
        </record>
        <record id="base.qa" model="res.country">
            <field name="demonym">Qatari</field>
        </record>
        <record id="base.re" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.ro" model="res.country">
            <field name="demonym">Romanian</field>
        </record>
        <record id="base.rs" model="res.country">
            <field name="demonym">Serbian</field>
        </record>
        <record id="base.ru" model="res.country">
            <field name="demonym">Russian</field>
        </record>
        <record id="base.rw" model="res.country">
            <field name="demonym">Rwandan</field>
        </record>
        <record id="base.sa" model="res.country">
            <field name="demonym">Saudi Arabian</field>
        </record>
        <record id="base.sb" model="res.country">
            <field name="demonym">Solomon Islander</field>
        </record>
        <record id="base.sc" model="res.country">
            <field name="demonym">Seychellois</field>
        </record>
        <record id="base.sd" model="res.country">
            <field name="demonym">Sudanese</field>
        </record>
        <record id="base.se" model="res.country">
            <field name="demonym">Swedish</field>
        </record>
        <record id="base.sg" model="res.country">
            <field name="demonym">Singaporean</field>
        </record>
        <record id="base.sh" model="res.country">
            <field name="demonym">Saint Helenian</field>
        </record>
        <record id="base.si" model="res.country">
            <field name="demonym">Slovene</field>
        </record>
        <record id="base.sj" model="res.country">
            <field name="demonym">Norwegian</field>
        </record>
        <record id="base.sk" model="res.country">
            <field name="demonym">Slovak</field>
        </record>
        <record id="base.sl" model="res.country">
            <field name="demonym">Sierra Leonean</field>
        </record>
        <record id="base.sm" model="res.country">
            <field name="demonym">Sammarinese</field>
        </record>
        <record id="base.sn" model="res.country">
            <field name="demonym">Senegalese</field>
        </record>
        <record id="base.so" model="res.country">
            <field name="demonym">Somali</field>
        </record>
        <record id="base.sr" model="res.country">
            <field name="demonym">Surinamer</field>
        </record>
        <record id="base.ss" model="res.country">
            <field name="demonym">South Sudanese</field>
        </record>
        <record id="base.st" model="res.country">
            <field name="demonym">Sao Tomean</field>
        </record>
        <record id="base.sv" model="res.country">
            <field name="demonym">Salvadoran</field>
        </record>
        <record id="base.sx" model="res.country">
            <field name="demonym">Dutch</field>
        </record>
        <record id="base.sy" model="res.country">
            <field name="demonym">Syrian</field>
        </record>
        <record id="base.sz" model="res.country">
            <field name="demonym">Swazi</field>
        </record>
        <record id="base.tc" model="res.country">
            <field name="demonym">Turks and Caicos Islander</field>
        </record>
        <record id="base.td" model="res.country">
            <field name="demonym">Chadian</field>
        </record>
        <record id="base.tf" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.tg" model="res.country">
            <field name="demonym">Togolese</field>
        </record>
        <record id="base.th" model="res.country">
            <field name="demonym">Thai</field>
        </record>
        <record id="base.tj" model="res.country">
            <field name="demonym">Tadzhik</field>
        </record>
        <record id="base.tk" model="res.country">
            <field name="demonym">Tokelauan</field>
        </record>
        <record id="base.tm" model="res.country">
            <field name="demonym">Turkmen</field>
        </record>
        <record id="base.tn" model="res.country">
            <field name="demonym">Tunisian</field>
        </record>
        <record id="base.to" model="res.country">
            <field name="demonym">Tongan</field>
        </record>
        <record id="base.tr" model="res.country">
            <field name="demonym">Turkish</field>
        </record>
        <record id="base.tt" model="res.country">
            <field name="demonym">Trinidadian</field>
        </record>
        <record id="base.tv" model="res.country">
            <field name="demonym">Tuvaluan</field>
        </record>
        <record id="base.tw" model="res.country">
            <field name="demonym">Taiwanese</field>
        </record>
        <record id="base.tz" model="res.country">
            <field name="demonym">Tanzanian</field>
        </record>
        <record id="base.ua" model="res.country">
            <field name="demonym">Ukrainian</field>
        </record>
        <record id="base.ug" model="res.country">
            <field name="demonym">Ugandan</field>
        </record>
        <record id="base.uk" model="res.country">
            <field name="demonym">British</field>
        </record>
        <record id="base.um" model="res.country">
            <field name="demonym">American</field>
        </record>
        <record id="base.us" model="res.country">
            <field name="demonym">American</field>
        </record>
        <record id="base.uy" model="res.country">
            <field name="demonym">Uruguayan</field>
        </record>
        <record id="base.uz" model="res.country">
            <field name="demonym">Uzbekistani</field>
        </record>
        <record id="base.va" model="res.country">
            </record>
        <record id="base.vc" model="res.country">
            <field name="demonym">Saint Vincentian</field>
        </record>
        <record id="base.ve" model="res.country">
            <field name="demonym">Venezuelan</field>
        </record>
        <record id="base.vg" model="res.country">
            <field name="demonym">Virgin Islander</field>
        </record>
        <record id="base.vi" model="res.country">
            <field name="demonym">Virgin Islander</field>
        </record>
        <record id="base.vn" model="res.country">
            <field name="demonym">Vietnamese</field>
        </record>
        <record id="base.vu" model="res.country">
            <field name="demonym">Ni-Vanuatu</field>
        </record>
        <record id="base.wf" model="res.country">
            <field name="demonym">Wallis and Futuna Islander</field>
        </record>
        <record id="base.ws" model="res.country">
            <field name="demonym">Samoan</field>
        </record>
        <record id="base.ye" model="res.country">
            <field name="demonym">Yemeni</field>
        </record>
        <record id="base.yt" model="res.country">
            <field name="demonym">French</field>
        </record>
        <record id="base.za" model="res.country">
            <field name="demonym">South African</field>
        </record>
        <record id="base.zm" model="res.country">
            <field name="demonym">Zambian</field>
        </record>
        <record id="base.zw" model="res.country">
            <field name="demonym">Zimbabwean</field>
        </record>
    </data>
</odoo>
