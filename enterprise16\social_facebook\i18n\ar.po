# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_facebook
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "<b class=\"d-block mb-2\">Facebook Page</b>"
msgstr "<b class=\"d-block mb-2\">صفحة Facebook</b> "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"التعليقات \"/>"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"الإعجابات \"/>"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_live_post__facebook_post_id
msgid "Actual Facebook ID of the post"
msgstr " معرّف Facebook الفعلي للمنشور "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr "حدث خطأ ما. "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App ID"
msgstr "معرّف التطبيق "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App Secret"
msgstr "سر التطبيق "

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "صورة الكاتب "

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_res_config_settings__facebook_use_own_account
msgid ""
"Check this if you want to use your personal Facebook Developer Account "
"instead of the provided one."
msgstr ""
"قم بتحديد ذلك إذا كنت ترغب في استخدام حساب مطور Facebook الخاص بك عوضاً عن "
"المقدم إليك. "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_comments_count
msgid "Comments"
msgstr "التعليقات"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__display_facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__display_facebook_preview
msgid "Display Facebook Preview"
msgstr "عرض معاينة Facebook "

#. module: social_facebook
#: model:ir.model.fields.selection,name:social_facebook.selection__social_media__media_type__facebook
#: model:social.media,name:social_facebook.social_media_facebook
msgid "Facebook"
msgstr "فيسبوك"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_access_token
msgid "Facebook Access Token"
msgstr "رمز وصول Facebook "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_account_id
msgid "Facebook Account ID"
msgstr "معرّف حساب Facebook "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_app_id
msgid "Facebook App ID"
msgstr "معرّف تطبيق Facebook "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_client_secret
msgid "Facebook App Secret"
msgstr "سر تطبيق Facebook "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_author_id
msgid "Facebook Author ID"
msgstr "معرّف كاتب Facebook "

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Facebook Comments"
msgstr "تعليقات Facebook "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "Facebook Developer Account"
msgstr "حساب مطور فيسبوك "

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_access_token
msgid ""
"Facebook Page Access Token provided by the Facebook API, this should never be set manually.\n"
"            It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"رمز الوصول إلى صفحة Facebook مقدم من الواجهة البرمجية لـ Facebook. يجب ألا يتم تعيين ذلك يدوياً أبداً.\n"
"            يُستخدم لمصادقة الطلبات عند النشر أو قراءة المعلومات من هذا الحساب. "

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_account_id
msgid ""
"Facebook Page ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"معرّف صفحة Facebook المقدم من قِبَل الواجهة البرمجية لـ Facebook. يجب ألا "
"يتم تعيين ذلك يدوياً أبداً. "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_post_id
msgid "Facebook Post ID"
msgstr "معرّف منشور Facebook "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_preview
msgid "Facebook Preview"
msgstr "معاينة Facebook "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token or it may have expired."
msgstr "لم يقدم Facebook رمز وصول صالح أو ربما قد انتهت صلاحيته. "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token."
msgstr "لم يقدم Facebook رمز وصول صالح. "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_is_event_post
msgid "Is event post"
msgstr "منشور فعالية "

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_likes_count
#, python-format
msgid "Likes"
msgstr "الإعجابات"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_media__media_type
msgid "Media Type"
msgstr "نوع الوسائط الاجتماعية "

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_mentions
msgid "Page Mentions"
msgstr "الإشارات في الصفحة "

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_posts
msgid "Page Posts"
msgstr "منشورات الصفحة "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Post Image"
msgstr "صورة المنشور "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid ""
"Post not found. It could be because the post has been deleted on the Social "
"Platform."
msgstr ""
"لم يتم العثور على المنشور. قد يكون ذلك نتيجة حذف المنشور في منصة التواصل "
"الاجتماعي. "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Published by Facebook Page •"
msgstr "تم النشر بواسطة صفحة Facebook • "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_reach
msgid "Reach"
msgstr "تم الوصول "

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_shares_count
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Shares"
msgstr "المشاركات "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_account
msgid "Social Account"
msgstr "الحساب الاجتماعي "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_live_post
msgid "Social Live Post"
msgstr "منشور اجتماعي حي "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_media
msgid "Social Media"
msgstr "مواقع التواصل الاجتماعي"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post
msgid "Social Post"
msgstr "منشور اجتماعي "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post_template
msgid "Social Post Template"
msgstr "قالب المنشور الاجتماعي "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream
msgid "Social Stream"
msgstr "الوسائط الاجتماعية "

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream_post
msgid "Social Stream Post"
msgstr "منشور الوسائط الاجتماعية "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr "لا توجد صفحة مرتبطة بهذا الحساب "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator. "
msgstr "غير مصرح به. يرجى التواصل مع مديرك. "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "غير معروف"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_use_own_account
msgid "Use your own Facebook Account"
msgstr "استخدم حساب Facebook الخاص بك "

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"يستخدم لإجراء المقارنات عند الحاجة إلى تقييد بعض الخصائص لموقع تواصل اجتماعي"
" محدد ('facebook'، 'twitter'، ...). "

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_user_likes
msgid "User Likes"
msgstr "إعجابات المستخدم "

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "أدوات العرض"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_live_post.py:0
#: code:addons/social_facebook/models/social_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: %s)."
msgstr "لم نتمكن من رفع صورتك. حاول تقليل حجمها ثم نشرها مجدداً (الخطأ: %s). "

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "لا تملك اشتراكاً سارياً. يرجى شراء واحد من هنا: %s "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "added an event"
msgstr "قام بإضافة فعالية "

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "• <i class=\"fa fa-globe\"/>"
msgstr "• <i class=\"fa fa-globe\"/>"
