# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_landed_costs
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid " already out"
msgstr " 이미 종료"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.stock_landed_cost_view_kanban
msgid "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_account_move_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"저장 가능 제품은 재고를 관리하는 품목입니다. 창고 앱이 설치되어 있어야 합니다.\n"
"소모품은 재고가 관리되지 않는 품목입니다.\n"
"서비스는 귀하가 제공하는 비물질 품목입니다."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__account_id
msgid "Account"
msgstr "계정"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_journal_id
msgid "Account Journal"
msgstr "계정 분개장"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_ids
msgid "Activities"
msgstr "활동"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Activity Type Icon"
msgstr "업무 유형 아이콘"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Additional Costs"
msgstr "추가 비용"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__additional_landed_cost
msgid "Additional Landed Cost"
msgstr "추가 하차 비용"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__target_model
msgid "Apply On"
msgstr "적용"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_current_cost_price
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_current_cost_price
msgid "By Current Cost"
msgstr "현재 비용별"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_quantity
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_quantity
msgid "By Quantity"
msgstr "수량별"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_volume
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_volume
msgid "By Volume"
msgstr "부피별"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_weight
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_weight
msgid "By Weight"
msgstr "중량별"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cancel"
msgstr "취소"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__cancel
msgid "Cancelled"
msgstr "취소 됨"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_company
msgid "Companies"
msgstr "회사"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company"
msgstr "회사"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company related to this journal"
msgstr "이 분개장과 관련된 회사"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Compute"
msgstr "계산"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__price_unit
msgid "Cost"
msgstr "비용"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_line_id
msgid "Cost Line"
msgstr "비용 명세"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__cost_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cost Lines"
msgstr "비용 명세서"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Cost and adjustments lines do not match. You should maybe recompute the "
"landed costs."
msgstr "비용 및 조정 명세가 일치하지 않습니다. 하차 비용을 다시 계산해야 합니다."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
msgid "Create Landed Costs"
msgstr "하차 비용 만들기"

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Create a new landed cost"
msgstr "새 하차 비용 만들기"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_uid
msgid "Created by"
msgstr "작성자"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_date
msgid "Created on"
msgstr "작성일자"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__currency_id
msgid "Currency"
msgstr "통화"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__date
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Date"
msgstr "일자"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_config_settings__lc_journal_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.res_config_settings_view_form
msgid "Default Journal"
msgstr "기본 분개장"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method"
msgstr "기본 분할 방법"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method when used for Landed Cost"
msgstr "도착가에 사용하는 기본 분할 방법"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__name
msgid "Description"
msgstr "설명"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__display_name
msgid "Display Name"
msgstr "표시명"

#. module: stock_landed_costs
#: model:mail.message.subtype,name:stock_landed_costs.mt_stock_landed_cost_open
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Done"
msgstr "완료"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__draft
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Draft"
msgstr "임시"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__equal
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__equal
msgid "Equal"
msgstr "동일"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (파트너)"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예 : fa-tasks"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Future Activities"
msgstr "향후 활동"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Group By"
msgstr "그룹별"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__id
msgid "ID"
msgstr "ID"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__landed_cost_ok
msgid ""
"Indicates whether the product is a landed cost: when receiving a vendor "
"bill, you can allocate this cost on preceding receipts."
msgstr "품목이 수입부대비용인지 여부를 나타냅니다. 업체 청구서를 받을 경우 이전 수령 품목에 이 비용을 배분할 수 있습니다."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__is_landed_costs_line
msgid "Is Landed Costs Line"
msgstr "하차 비용 명세 여부"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Is a Landed Cost"
msgstr "하차 비용 여부"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__description
msgid "Item Description"
msgstr "항목 설명"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Journal"
msgstr "분개장"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_move_id
msgid "Journal Entry"
msgstr "분개"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move_line
msgid "Journal Item"
msgstr "분개 항목"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_layer__stock_landed_cost_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed Cost"
msgstr "하차 비용"

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.action_stock_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_ids
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree2
msgid "Landed Costs"
msgstr "양륙비 포함 원가"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_visible
msgid "Landed Costs Visible"
msgstr "하차 비용 표시"

#. module: stock_landed_costs
#: model:mail.message.subtype,description:stock_landed_costs.mt_stock_landed_cost_open
msgid "Landed cost validated"
msgstr "승인된 하차 비용"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost____last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines____last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Late Activities"
msgstr "지연된 활동"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_company__lc_journal_id
msgid "Lc Journal"
msgstr "Lc  분개장"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_ids
msgid "Messages"
msgstr "메시지"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__name
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Name"
msgstr "이름"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "New"
msgstr "신규"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__final_cost
msgid "New Value"
msgstr "새 값"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_id
msgid "Next Activity Type"
msgstr "다음 업무 유형"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류 메시지 수"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Only draft landed costs can be validated"
msgstr "초안의 하차 비용만 승인이 가능합니다"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__former_cost
msgid "Original Value"
msgstr "원래 가격"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Picking"
msgstr "선별"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please configure Stock Expense Account for product: %s."
msgstr "제품에 대한 재고 비용 계정을 구성하십시오 : %s"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please define %s on which those additional costs should apply."
msgstr "해당 추가 비용을 적용해야 하는 %s 항목을 설정하십시오."

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__done
msgid "Posted"
msgstr "전기됨"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_product_template
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__product_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__product_id
msgid "Product"
msgstr "품목"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__product_type
msgid "Product Type"
msgstr "품목 유형"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "발주서 내역"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__quantity
msgid "Quantity"
msgstr "수량"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid "Split Method"
msgstr "분할 방법"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__state
msgid "State"
msgstr "시/도"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Status"
msgstr "상태"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동에 기조한 상태입니다\n"
"기한초과: 이미 기한이 지났습니다\n"
"오늘: 활동 날짜가 오늘입니다\n"
"계획: 향후 활동입니다."

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost
msgid "Stock Landed Cost"
msgstr "재고 부대 비용"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost_lines
msgid "Stock Landed Cost Line"
msgstr "재고 하차 비용 명세"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__move_id
msgid "Stock Move"
msgstr "재고 이동"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "재고 평가 레이어"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Today Activities"
msgstr "오늘 활동"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__amount_total
msgid "Total"
msgstr "합계"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__picking_ids
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__target_model__picking
msgid "Transfers"
msgstr "이동"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Validate"
msgstr "승인"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Validated landed costs cannot be cancelled, but you could create negative "
"landed costs to reverse them"
msgstr "승인된 운송비용은 취소될 수 없습니다, 그러나 마이너스의 배송비용을 생성하여 복구시킬 수 있습니다"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation"
msgstr "평가"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_adjustment_lines
msgid "Valuation Adjustment Lines"
msgstr "평가 조정 명세"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__valuation_adjustment_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation Adjustments"
msgstr "평가 조정"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__vendor_bill_id
msgid "Vendor Bill"
msgstr "공급업체 청구서"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__volume
msgid "Volume"
msgstr "부피"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__weight
msgid "Weight"
msgstr "무게"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"You cannot apply landed costs on the chosen %s(s). Landed costs can only be "
"applied for products with FIFO or average costing method."
msgstr ""
"선택한 %s 항목에 수입부대비용을 적용시킬 수 없습니다. 수입부대비용은 FIFO (선입선출법)이나 평균원가법을 사용하는 품목에만 적용하실"
" 수 있습니다."

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/product.py:0
#, python-format
msgid ""
"You cannot change the product type or disable landed cost option because the"
" product is used in an account move line."
msgstr "해당 품목이 계정 이동 내역에서 사용되기 때문에 품목 유형을 변경하거나 수입부대비용 옵션을 비활성화시킬 수 없습니다."
