<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_payment_register_form" model="ir.ui.view">
            <field name="name">account.payment.register.form</field>
            <field name="model">account.payment.register</field>
            <field name="arch" type="xml">
                <form string="Register Payment">
                    <!-- Invisible fields -->
                    <field name="line_ids" invisible="1"/>
                    <field name="can_edit_wizard" invisible="1" force_save="1"/>
                    <field name="can_group_payments" invisible="1" force_save="1"/>
                    <field name="early_payment_discount_mode" invisible="1" force_save="1"/>
                    <field name="payment_type" invisible="1" force_save="1"/>
                    <field name="partner_type" invisible="1" force_save="1"/>
                    <field name="source_amount" invisible="1" force_save="1"/>
                    <field name="source_amount_currency" invisible="1" force_save="1"/>
                    <field name="source_currency_id" invisible="1" force_save="1"/>
                    <field name="company_id" invisible="1" force_save="1"/>
                    <field name="partner_id" invisible="1" force_save="1"/>
                    <field name="country_code" invisible="1" force_save="1"/>

                    <field name="show_partner_bank_account" invisible="1"/>
                    <field name="require_partner_bank_account" invisible="1"/>
                    <field name="available_journal_ids" invisible="1"/>
                    <field name="available_payment_method_line_ids" invisible="1"/>
                    <field name="available_partner_bank_ids" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="hide_writeoff_section" invisible="1"/>

                    <div role="alert" class="alert alert-info" attrs="{'invisible': [('hide_writeoff_section', '=', False)]}">
                        <p><b>Early Payment Discount of <field name="payment_difference"/> has been applied.</b></p>
                    </div>
                    <group>
                        <group name="group1">
                            <field name="journal_id" options="{'no_open': True, 'no_create': True}" required="1"/>
                            <field name="payment_method_line_id"
                                   required="1"  options="{'no_create': True, 'no_open': True}"/>
                            <field name="partner_bank_id"
                                   attrs="{'invisible': ['|', ('show_partner_bank_account', '=', False), '|', ('can_edit_wizard', '=', False), '&amp;', ('can_group_payments', '=', True), ('group_payment', '=', False)],
                                           'required': [('require_partner_bank_account', '=', True), ('can_edit_wizard', '=', True), '|', ('can_group_payments', '=', False), ('group_payment', '=', False)], 'readonly': [('payment_type', '=', 'inbound')]}"
                                    context="{'default_allow_out_payment': True}"/>
                            <field name="group_payment"
                                   attrs="{'invisible': [('can_group_payments', '=', False)]}"/>
                        </group>
                        <group name="group2">
                            <label for="amount"
                                   attrs="{'invisible': ['|', ('can_edit_wizard', '=', False), '&amp;', ('can_group_payments', '=', True), ('group_payment', '=', False)]}"/>
                            <div name="amount_div" class="o_row"
                                 attrs="{'invisible': ['|', ('can_edit_wizard', '=', False), '&amp;', ('can_group_payments', '=', True), ('group_payment', '=', False)]}">
                                <field name="amount"/>
                                <field name="currency_id"
                                       required="1"
                                       options="{'no_create': True, 'no_open': True}"
                                       groups="base.group_multi_currency"/>
                            </div>
                            <field name="payment_date"/>
                            <field name="communication"
                                   attrs="{'invisible': ['|', ('can_edit_wizard', '=', False), '&amp;', ('can_group_payments', '=', True), ('group_payment', '=', False)]}"/>
                        </group>
                        <group name="group3"
                               attrs="{'invisible': ['|', ('payment_difference', '=', 0.0), '|', ('early_payment_discount_mode', '=', True), '|', ('can_edit_wizard', '=', False), '&amp;', ('can_group_payments', '=', True), ('group_payment', '=', False)]}">
                            <label for="payment_difference"/>
                            <div>
                                <field name="payment_difference"/>
                                <field name="payment_difference_handling" widget="radio" nolabel="1"/>
                                <div attrs="{'invisible': ['|', ('hide_writeoff_section', '=', True), ('payment_difference_handling','=','open')]}">
                                    <label for="writeoff_account_id" string="Post Difference In" class="oe_edit_only"/>
                                    <field name="writeoff_account_id"
                                           string="Post Difference In"
                                           options="{'no_create': True}"
                                           attrs="{'required': [('payment_difference_handling', '=', 'reconcile'), ('early_payment_discount_mode', '=', False)]}"/>
                                    <label for="writeoff_label" class="oe_edit_only" string="Label"/>
                                    <field name="writeoff_label" attrs="{'required': [('payment_difference_handling', '=', 'reconcile')]}"/>
                                </div>
                            </div>
                        </group>
                    </group>
                    <footer>
                        <button string="Create Payment" name="action_create_payments" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
