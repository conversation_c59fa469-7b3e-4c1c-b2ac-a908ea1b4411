# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * voip
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-29 12:08+0000\n"
"PO-Revision-Date: 2015-09-07 15:13+0000\n"
"Last-Translator: <>\n"
"Language-Team: Malayalam (India) (http://www.transifex.com/odoo/odoo-9/"
"language/ml_IN/)\n"
"Language: ml_IN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_sip_always_transfer
msgid ""
"All your outbound calls will be redirected to your handset when the customer "
"accepts your call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_always_transfer
msgid "Always Redirect to Handset"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_create_uid
msgid "Created by"
msgstr "രൂപപ്പെടുത്തിയത്"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_create_date
msgid "Created on"
msgstr "നിർമിച്ച ദിവസം"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_display_name
msgid "Display Name"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:57
#, python-format
msgid "From "
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_external_phone
msgid "Handset Extension"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_id
msgid "ID"
msgstr "ID"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:58
#, python-format
msgid "Incoming call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:88
#, python-format
msgid "Incoming call from "
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator___last_update
msgid "Last Modified on"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_write_uid
msgid "Last Updated by"
msgstr "അവസാനം അപ്ഡേറ്റ് ചെയ്തത്"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_write_date
msgid "Last Updated on"
msgstr "അവസാനം അപ്ഡേറ്റ് ചെയ്ത ദിവസം"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_ring_number
msgid "Number of Rings"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:27
#, python-format
msgid "One or more parameter is missing. Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.res_user_form
msgid "PBX Configuration"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:153
#, python-format
msgid "Please check your configuration.</br> (Reason receives :"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:281
#, python-format
msgid ""
"Problem during the connection. Check if the application is allowed to access "
"your microphone from your browser."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_login
msgid "SIP Login / Browser's Extension"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_password
msgid "SIP Password"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:179
#, python-format
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.</br> (Reason "
"receives :%s)"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_sip_ring_number
msgid ""
"The number of rings before the call is defined as refused by the customer."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:51
#, python-format
msgid ""
"The server configuration could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:47
#, python-format
msgid "The websocket uri could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "Users"
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_user_config
msgid "VOIP Configuration"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:273
#, python-format
msgid "Your browser could not support WebRTC. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/sip_js.js:152
#, python-format
msgid "the connection cannot be made. "
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_configurator
msgid "voip.configurator"
msgstr ""
