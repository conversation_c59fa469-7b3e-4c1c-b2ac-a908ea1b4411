# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_batch
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid ""
"<span class=\"o_form_label fw-bold\" attrs=\"{'invisible':[('auto_batch', "
"'=', False)]}\">Group by</span>"
msgstr ""
"<span class=\"o_form_label fw-bold\" attrs=\"{'invisible':[('auto_batch', "
"'=', False)]}\">التجميع حسب</span>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>FROM</strong>"
msgstr "<strong>من</strong> "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>رقم الدفعة/الرقم التسلسلي:</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr "<strong>التغليف</strong> "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>باركود المنتج</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr "<strong>المسؤول:</strong> "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>TO</strong>"
msgstr "<strong>إلى</strong> "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>→</strong>"
msgstr "<strong>→</strong>"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of lines if the transfer is added to it.\n"
"Leave this value as '0' if no line limit."
msgstr ""
"لن تتم إضافة الشحنة تلقائياً إلى الدفعات التي تتخطى هذا العدد من البنود في حال تمت إضافة الشحنة إليها. \n"
"قم بتعيين القيمة كـ \"0\" إذا لم يكن هناك حد لعدد البنود. "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of transfers.\n"
"Leave this value as '0' if no transfer limit."
msgstr ""
"لن تتم إضافة الشحنة تلقائياً إلى الدفعات التي تتخطى هذا العدد من الشحنات في حال تمت إضافة الشحنة إليها. \n"
"قم بتعيين القيمة كـ \"0\" إذا لم يكن هناك حد لعدد الشحنات. "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Add Operations"
msgstr "إضافة عمليات "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to"
msgstr "إضافة عمليات الانتقاء إلى "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to"
msgstr "إضافة إلى"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_move_line.py:0
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree_detailed_wave
#, python-format
msgid "Add to Wave"
msgstr "إضافة إلى عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
msgid "Add to batch"
msgstr "الإضافة إلى الدفعة "

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_add_to_wave_action_stock_picking
msgid "Add to wave"
msgstr "إضافة إلى عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Allocation"
msgstr "التخصيص "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__allowed_picking_ids
msgid "Allowed Picking"
msgstr "عمليات الانتقاء المسموح بها "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#, python-format
msgid "Assigned to %s Responsible"
msgstr "مسند إلى %s المسؤول "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_auto_confirm
msgid "Auto-confirm"
msgstr "التأكيد التلقائي "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__auto_batch
msgid "Automatic Batches"
msgstr "الدفعات التلقائية "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
msgid "Automatically group batches by contacts."
msgstr "تجميع الدفعات تلقائياً حسب جهات الاتصال. "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Automatically group batches by destination country."
msgstr "تجميع الدفعات تلقائياً حسب دولة الوجهة. "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Automatically group batches by their destination location."
msgstr "تجميع الدفعات تلقائياً حسب موقع الوجهة. "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Automatically group batches by their source location."
msgstr "تجميع الدفعات تلقائياً حسب موقع المصدر. "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__auto_batch
msgid ""
"Automatically put pickings into batches as they are confirmed when possible."
msgstr ""
"وضع البضاعة التي تم انتقاؤها في مجموعات تلقائياً عند تأكيدها، عند الإمكان. "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr "باركود"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_form_inherit
msgid "Batch"
msgstr "الدفعة "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_move_line_view_search_inherit_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_internal_search_inherit
#, python-format
msgid "Batch Transfer"
msgstr "الشحنة المجمعة "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Transfer Lines"
msgstr "بنود النقل على دفعات "

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Batch Transfers"
msgstr "الشحنات المجمعة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Transfers not finished"
msgstr "لم تنتهِ عمليات النقل على دفعات "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this transfer"
msgstr "الدفعة المتعلقة بهذه الشحنة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Batches"
msgstr "الدفعات "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "إلغاء "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__cancel
msgid "Cancelled"
msgstr "ملغي"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Cannot create wave transfers"
msgstr "تعذر إنشاء عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Check Availability"
msgstr "التحقق من التوافر "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "اختر مخطط بطاقات العناوين "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Type of Labels To Print"
msgstr "اختر نوع بطاقات العناوين لطباعتها "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Clear quantities"
msgstr "مسح الكميات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__company_id
msgid "Company"
msgstr "الشركة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Confirm"
msgstr "تأكيد"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Contact"
msgstr "جهة الاتصال"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_batch
msgid "Count Picking Batch"
msgstr "عدد عمليات الانتقاء المجمعة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_wave
msgid "Count Picking Wave"
msgstr "عدد عمليات جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Create a new batch transfer"
msgstr "إنشاء عملية نقل على دفعات جديدة "

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid "Create a new wave transfer"
msgstr "إنشاء عملية جمع العمليات من شحنات مختلفة  "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Destination Country"
msgstr "الدولة الوجهة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Destination Location"
msgstr "الموقع الوجهة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Detailed Operations"
msgstr "العمليات المفصلة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__done
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Done"
msgstr "منتهي "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__draft
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Draft"
msgstr "مسودة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال fa-tasks "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "التجميع حسب "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr "المُعرف"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#, python-format
msgid ""
"If the Automatic Batches feature is enabled, at least one 'Group by' option "
"must be selected."
msgstr ""
"إذا كانت خاصية الدفعات التلقائية مفعلة، يجب تحديد خيار \"تجميع حسب\" واحد "
"على الأقل. "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__in_progress
msgid "In progress"
msgstr "قيد التنفيذ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__line_ids
msgid "Line"
msgstr "بند"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of transfers associated to this batch"
msgstr "قائمة بعمليات النقل المرتبطة بهذه المجموعة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid "Maximum lines per batch"
msgstr "الحد الأقصى للبنود لكل دفعة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid "Maximum transfers per batch"
msgstr "الحد الأقصى للتحويلات لكل دفعة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__mode
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__mode
msgid "Mode"
msgstr "الوضع"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree
msgid "Move Lines"
msgstr "بنود الحركات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "My Transfers"
msgstr "شحناتي "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الاخطاء"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_id
msgid "Operation Type"
msgstr "نوع العملية"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Operations"
msgstr "العمليات"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__picking_ids
msgid "Picking"
msgstr "الانتقاء "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "نوع الاستلام"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Please add 'Done' quantities to the batch picking to create a new pack."
msgstr "يرجى إضافة الكميات \"المنتهية\" إلى انتقاء الدفعة لإنشاء طرد جديد. "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "طباعة"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print Labels"
msgstr "طباعة بطاقات العناوين "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "المنتج"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "تحركات المنتج (بنود حركة المخزون)"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Put in Pack"
msgstr "تغليف في طرد "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "الكمية"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "المسؤول "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr "التاريخ المجدول"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__scheduled_date
msgid ""
"Scheduled date for the transfers to be processed.\n"
"              - If manually set then scheduled date for all transfers in batch will automatically update to this date.\n"
"              - If not manually changed and transfers are added/removed/updated then this will be their earliest scheduled date\n"
"                but this scheduled date will not be set for all transfers in batch."
msgstr ""
"التاريخ المجدول لمعالجة عمليات النقل.\n"
"              - إذا تم تعيينه يدوياً، فسيتم تحديث التاريخ المجدول لكافة عمليات النقل على دفعات لهذا التاريخ.\n"
"              - إذا لم يتم تغييره يدوياً، وتمت إضافة/إزالة/تحديث عمليات النقل، فسيكون ذلك أقرب تاريخ مجدول\n"
"                ولكن لن يتم تعيين هذا التاريخ المجدول لكافة عمليات النقل على دفعات. "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Transfer"
msgstr "البحث في عمليات النقل على دفعات "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Set quantities"
msgstr "تعيين الكميات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid "Show Allocation Button"
msgstr "إظهار زر التخصيص "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid "Show Check Availability"
msgstr "إظهار التحقق من التوافر "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_clear_qty_button
msgid "Show Clear Qty Button"
msgstr "إظهار زر مسح الكميات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_lots_text
msgid "Show Lots Text"
msgstr "إظهار نص الدفعات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_set_qty_button
msgid "Show Set Qty Button"
msgstr "إظهار زر تعيين الكميات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_validate
msgid "Show Validate Button"
msgstr "إظهار زر التصديق "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Show all records which has next action date is before today"
msgstr "عرض كافة السجلات المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Source Location"
msgstr "الموقع المصدري "

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr "تم تغيير المرحلة"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "الحالة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "الحالة"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Transfer"
msgstr "عمليات النقل على دفعات للمخزون "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move
msgid "Stock Move"
msgstr "حركة المخزون"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "وجهة طرد المخزون "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_line_ids
msgid "Stock move lines"
msgstr "بنود حركة المخزون "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_ids
msgid "Stock moves"
msgstr "حركات المخزون "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr "الملخص:"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"The following transfers cannot be added to batch transfer %s. Please check their states and operation types, if they aren't immediate transfers.\n"
"\n"
"Incompatibilities: %s"
msgstr ""
"عمليات النقل التالية لا يمكن إضافتها إلى عملية النقل على دفعات %s. يرجى التحقق من حالاتها وأنواع العمليات، إذا لم تكن عمليات نقل مباشرة. \n"
"\n"
"أوجه التعارض: %s "

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"الهدف من النقل المجمع هو تجميع العمليات التي\n"
"            قد تحتاج إلى إجرائها معاً لزيادة الكفاءة.\n"
"            قد تكون مفيدة أيضاً لتعيين الوظائف (شخص واحد = دفعة واحدة) أو\n"
"            المساعدة في إدارة أوقات العمليات (المهام التي يجب إنجازها الساعة 1 مساءً). "

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid ""
"The goal of the wave transfer is to group operations from different transfer\n"
"                    together in order to increase their efficiency.\n"
"                    It may also be useful to assign jobs (one person = one batch) or\n"
"                    help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"الهدف من عملية جمع العمليات من شحنات مختلفة هو تجميع العمليات من شحنات مختلفة\n"
"                    معاً لزيادة الكفاءة.\n"
"                    قد تكون مفيدة أيضاً عند إسناد الوظائف (شخص واحد = مجموعة واحدة) أو\n"
"                    المساعدة على إدارة أوقات العمليات (المهام التي يجب أن تكتمل بحلول الساعة 1 مساءً). "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected operations should belong to a unique company."
msgstr "يجب أن تنتمي العمليات المحددة إلى شركة فريدة. "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
#, python-format
msgid "The selected pickings should belong to an unique company."
msgstr "يجب أن تنتمي عمليات الانتقاء المحددة إلى شركة فريدة. "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to a unique company."
msgstr "يجب أن تنتمي عمليات النقل المحددة إلى شركة فريدة. "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to the same operation type"
msgstr "يجب أن تنتمي عميات النقل المحددة إلى نفس نوع العملية "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__is_wave
msgid "This batch is a wave"
msgstr "هذه الشحنة هي عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "To Do"
msgstr "المهام المراد تنفيذها"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer"
msgstr "الشحنة"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Transferred by"
msgstr "تم النقل بواسطة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Transfers"
msgstr "التحويلات "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "نوع العملية"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل. "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Validate"
msgstr "تصديق "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_warehouse
msgid "Warehouse"
msgstr "المستودع "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__wave_id
msgid "Wave Transfer"
msgstr "عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_add_to_wave
msgid "Wave Transfer Lines"
msgstr "بنود عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_picking_tree_wave
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_wave_menu
msgid "Wave Transfers"
msgstr "جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Waves"
msgstr "جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
msgid "When checked, create the batch in draft status"
msgstr "عندما يكون محدداً، قم بإنشاء الدفعة بحالة المسودة "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You cannot delete Done batch transfers."
msgstr "لا يمكنك حذف عمليات النقل على دفعات المنتهية. "

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You have to set some pickings to batch."
msgstr "عليك تعيين بعض عمليات الانتقاء لتنفيذها دفعة واحدة. "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__new
msgid "a new batch transfer"
msgstr "عملية نقل على دفعات جديدة "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__new
msgid "a new wave transfer"
msgstr "عملية جمع العمليات من شحنات مختلفة "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__existing
msgid "an existing batch transfer"
msgstr "عملية نقل على دفعات موجودة بالفعل "

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__existing
msgid "an existing wave transfer"
msgstr "عملية جمع العمليات من شحنات مختلفة موجودة بالفعل "
