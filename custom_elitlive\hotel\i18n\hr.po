# Croatian translation for openobject-addons
# Copyright (c) 2010 Rosetta Contributors and Canonical Ltd 2010
# This file is distributed under the same license as the openobject-addons package.
# <AUTHOR> <EMAIL>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: openobject-addons\n"
"Report-Msgid-Bugs-To: FULL NAME <EMAIL@ADDRESS>\n"
"POT-Creation-Date: 2009-11-26 07:33+0000\n"
"PO-Revision-Date: 2010-09-08 09:16+0000\n"
"Last-Translator: <PERSON><PERSON> (Aplikacija d.o.o.) <<EMAIL>>\n"
"Language-Team: Croatian <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Launchpad-Export-Date: 2011-01-18 05:26+0000\n"
"X-Generator: Launchpad (build 12177)\n"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree
msgid "Generate Folio"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Recreate Procurement"
msgstr ""

#. module: hotel
#: field:hotel.folio,checkout_date:0
#: field:hotel_folio.line,checkout_date:0
msgid "Check Out"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_view_form
msgid "Amenities"
msgstr ""

#. module: hotel
#: constraint:ir.actions.act_window:0
msgid "Invalid model name in the action definition."
msgstr "Pogrešno ime modela u definiciji akcije."

#. module: hotel
#: field:hotel.floor,name:0
msgid "Floor Name"
msgstr "Naziv kata"

#. module: hotel
#: field:product.product,iscategid:0
msgid "Is categ id"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities_type
msgid "amenities Type"
msgstr ""

#. module: hotel
#: view:hotel.room_amenities:0
msgid "Amenity rate"
msgstr ""

#. module: hotel
#: field:product.category,isservicetype:0
msgid "Is Service Type"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Set to Draft"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "to"
msgstr ""

#. module: hotel
#: wizard_view:hotel.folio.total_folio,init:0
msgid "Folio List"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_services_form
msgid "Services"
msgstr "Usluge"

#. module: hotel
#: view:hotel.folio:0
msgid "Cancel Order"
msgstr "Otkaži narudžbu"

#. module: hotel
#: view:hotel.folio:0
msgid "Confirm Order"
msgstr "Potvrdite narudžbu"

#. module: hotel
#: model:product.category,name:hotel.hotel_room_amenities_type_0_product_category
msgid "All Aminities"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Notes"
msgstr ""

#. module: hotel
#: view:hotel.floor:0
msgid " Hotel Floor"
msgstr " Kat hotela"

#. module: hotel
#: rml:folio.total:0
msgid "Customer Name"
msgstr "Naziv kupca"

#. module: hotel
#: field:product.product,isroom:0
msgid "Is Room"
msgstr "Soba"

#. module: hotel
#: model:product.category,name:hotel.hotel_service_type_1_product_category
msgid "Fixed"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_service_line
msgid "hotel Service line"
msgstr "Linija hotelske usluge"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree_all
msgid "All Folio"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: field:hotel.room,room_amenities:0
msgid "Room Amenities"
msgstr "Oprema sobe"

#. module: hotel
#: view:hotel.service_type:0
#: model:ir.actions.act_window,name:hotel.open_hotel_service_type_form_tree
#: model:ir.model,name:hotel.model_hotel_service_type
msgid "Service Type"
msgstr "Vrsta usluge"

#. module: hotel
#: field:hotel_folio.line,order_line_id:0
msgid "order_line_id"
msgstr ""

#. module: hotel
#: constraint:product.category:0
msgid "Error ! You can not create recursive categories."
msgstr "Greška! Ne možete stvoriti rekurzivne kategorije."

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio
msgid "hotel folio new"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Extra Info"
msgstr "Dodatne informacije"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.hotel_room_category_action
msgid "Rooms by Category"
msgstr "Sobe po kategorijama"

#. module: hotel
#: model:product.category,name:hotel.hotel_room_amenities_type_3_product_category
msgid "Single Bed"
msgstr "Jednokrevetna"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_service_type_form_tree
msgid "Service Types"
msgstr "Vrste usluga"

#. module: hotel
#: model:product.category,name:hotel.hotel_room_amenities_type_2_product_category
msgid "Tables"
msgstr "Stolovi"

#. module: hotel
#: view:hotel.folio:0
msgid "Folio"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "From"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Recreate Invoice"
msgstr "Ponovo izradi račun"

#. module: hotel
#: field:product.category,isamenitype:0
msgid "Is amenities Type"
msgstr ""

#. module: hotel
#: view:hotel.room:0
msgid " Room Amenities"
msgstr ""

#. module: hotel
#: field:hotel.folio,checkin_date:0
#: field:hotel_folio.line,checkin_date:0
msgid "Check In"
msgstr "Prijava"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.hotel_amenity_category_action
#: model:ir.ui.menu,name:hotel.menu_hotel_amenity_category_action
msgid "Amenities by Category"
msgstr ""

#. module: hotel
#: field:hotel.services,service_id:0
msgid "Service_id"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Invoice Lines"
msgstr "Retci računa"

#. module: hotel
#: view:hotel.folio:0
msgid "Untaxed amount"
msgstr "Prije poreza"

#. module: hotel
#: field:hotel.room_amenities_type,cat_id:0
#: field:hotel.room_type,cat_id:0
#: field:hotel.service_type,ser_id:0
msgid "category"
msgstr "kategorija"

#. module: hotel
#: view:hotel.room:0
#: field:hotel.room,avail_status:0
msgid "Room Status"
msgstr "Status sobe"

#. module: hotel
#: field:hotel.room_amenities,room_categ_id:0
msgid "Product Category"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Manual Description"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_service_type_0_product_category
msgid "All Services"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: model:ir.actions.act_window,name:hotel.action_hotel_room_form
#: model:ir.model,name:hotel.model_hotel_room
msgid "Hotel Room"
msgstr "Hotelska soba"

#. module: hotel
#: rml:folio.total:0
msgid "Folio No."
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Inventory Moves"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_configuration_menu
msgid "Configuration"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Total amount"
msgstr "Ukupni iznos"

#. module: hotel
#: view:hotel.floor:0
msgid " Hotel Floors"
msgstr ""

#. module: hotel
#: constraint:ir.ui.view:0
msgid "Invalid XML for View Architecture!"
msgstr ""

#. module: hotel
#: field:hotel.folio,order_id:0
msgid "order_id"
msgstr ""

#. module: hotel
#: field:hotel.room_amenities,amenity_rate:0
msgid "Amenity Rate"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Procurement"
msgstr ""

#. module: hotel
#: field:hotel.floor,sequence:0
msgid "Sequence"
msgstr "Sekvenca"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_type_view_form
msgid "Amenity Types"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_hotel_room_category_action
msgid "Room by Category"
msgstr ""

#. module: hotel
#: field:hotel.room_amenities,rcateg_id:0
msgid "Amenity Catagory"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Other data"
msgstr ""

#. module: hotel
#: field:hotel.room,max_child:0
msgid "Max Child"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Descriptions"
msgstr ""

#. module: hotel
#: wizard_field:hotel.folio.total_folio,init,date_start:0
msgid "Start Date"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio_line
msgid "hotel folio1 room line"
msgstr ""

#. module: hotel
#: field:product.category,isroomtype:0
msgid "Is Room Type"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "Total Collection"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Automatic Declaration"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Rent(UOM)"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_services
msgid "Hotel Services and its charges"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Procurement Corrected"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Room Line"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Room No"
msgstr ""

#. module: hotel
#: view:hotel.services:0
msgid "Service rate"
msgstr ""

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_folio1_form_tree
#: model:ir.actions.act_window,name:hotel.open_hotel_folio1_form_tree_all
msgid "Hotel Folio"
msgstr ""

#. module: hotel
#: field:hotel.folio,room_lines:0
#: field:hotel.folio,service_lines:0
msgid "unknown"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Guest Name"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Service Line"
msgstr ""

#. module: hotel
#: view:hotel.room_amenities_type:0
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_type_view_form
msgid "Hotel Room Amenities Type"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Folio Line"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Date"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
#: view:hotel.room:0
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Properties"
msgstr ""

#. module: hotel
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Purchase Description"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Compute"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities
msgid "Room amenities"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_type_3_product_category
msgid "No Room"
msgstr ""

#. module: hotel
#: field:hotel_service.line,service_line_id:0
msgid "service_line_id"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_amenities_type_1_product_category
msgid "Beds"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_service_type_2_product_category
msgid "Variable"
msgstr ""

#. module: hotel
#: field:hotel.room,floor_id:0
msgid "Floor No"
msgstr ""

#. module: hotel
#: view:hotel.services:0
#: model:ir.actions.act_window,name:hotel.action_hotel_services_form
msgid "Hotel Services"
msgstr ""

#. module: hotel
#: view:hotel.room_amenities:0
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_view_form
msgid "Hotel Room Amenities"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "History"
msgstr ""

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_floor_form_tree
msgid "Floor Structure"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "Net Total :-"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Related invoices"
msgstr ""

#. module: hotel
#: selection:hotel.room,avail_status:0
msgid "Assigned"
msgstr ""

#. module: hotel
#: selection:hotel.room,avail_status:0
msgid "Unassigned"
msgstr ""

#. module: hotel
#: model:ir.module.module,shortdesc:hotel.module_meta_information
#: model:ir.ui.menu,name:hotel.hotel_management_menu
msgid "Hotel Management"
msgstr ""

#. module: hotel
#: wizard_button:hotel.folio.total_folio,init,end:0
msgid "Cancel"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Information"
msgstr ""

#. module: hotel
#: constraint:ir.model:0
msgid ""
"The Object name must start with x_ and not contain any special character !"
msgstr ""

#. module: hotel
#: field:hotel.room,product_id:0
msgid "Product_id"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Hotel Folio1"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: model:ir.model,name:hotel.model_hotel_floor
#: model:ir.ui.menu,name:hotel.menu_open_hotel_floor_form_tree
msgid "Floor"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_type_0_product_category
msgid "All Rooms"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Create Invoice"
msgstr ""

#. module: hotel
#: wizard_button:hotel.folio.total_folio,init,print_report:0
msgid "Print Report"
msgstr ""

#. module: hotel
#: model:ir.actions.report.xml,name:hotel.hotel_folio_details
#: model:ir.actions.wizard,name:hotel.wizard_hotel_total
msgid "Folio Total"
msgstr ""

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_room_type_form_tree
#: model:ir.model,name:hotel.model_hotel_room_type
msgid "Room Type"
msgstr ""

#. module: hotel
#: view:hotel.room:0
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Description"
msgstr ""

#. module: hotel
#: field:hotel_folio.line,folio_id:0
#: field:hotel_service.line,folio_id:0
msgid "folio_id"
msgstr ""

#. module: hotel
#: view:hotel.room_amenities:0
#: view:hotel.services:0
msgid "Sale Description"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_amenities_type_4_product_category
msgid "Double Bed"
msgstr ""

#. module: hotel
#: field:product.product,isservice:0
msgid "Is Service id"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_type_form_tree
msgid "Room Types"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "CheckOut"
msgstr ""

#. module: hotel
#: model:ir.actions.act_window,name:hotel.hotel_service_category_action
#: model:ir.ui.menu,name:hotel.menu_hotel_service_category_action
msgid "Services by Category"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_form
msgid "Rooms"
msgstr ""

#. module: hotel
#: view:hotel.room:0
msgid "Room rate"
msgstr ""

#. module: hotel
#: wizard_field:hotel.folio.total_folio,init,date_end:0
msgid "End Date"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_type_2_product_category
msgid "Double"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "CheckIn"
msgstr ""

#. module: hotel
#: field:hotel.room,max_adult:0
msgid "Max Adult"
msgstr ""

#. module: hotel
#: model:ir.module.module,description:hotel.module_meta_information
msgid ""
"\n"
"    Module for Hotel/Resort/Property management. You can manage:\n"
"    * Configure Property\n"
"    * Hotel Configuration\n"
"    * Check In, Check out\n"
"    * Manage Folio\n"
"    * Payment\n"
"\n"
"    Different reports are also provided, mainly for hotel statistics.\n"
"    "
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_report_menu
msgid "Reports"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Related packings"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Folio No"
msgstr ""

#. module: hotel
#: view:hotel.room_type:0
msgid " Hotel Room Type"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Invoice Corrected"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.wizard_hotel_menu
msgid "Hotel Folio Report"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "States"
msgstr ""

#. module: hotel
#: model:product.category,name:hotel.hotel_room_type_1_product_category
msgid "Single"
msgstr ""

#. module: hotel
#: constraint:product.product:0
msgid "Error: Invalid ean code"
msgstr ""

#. module: hotel
#: view:hotel.folio:0
msgid "Rent"
msgstr ""

#. module: hotel
#: rml:folio.total:0
msgid "Total"
msgstr ""
