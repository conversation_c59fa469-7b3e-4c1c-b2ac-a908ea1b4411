<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="hr_appraisal_skill_rule_hr_user" model="ir.rule">
        <field name="name">Employee skill: HR appraisal user: read all</field>
        <field name="model_id" ref="model_hr_appraisal_skill"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4,ref('hr_appraisal.group_hr_appraisal_user'))]"/>
    </record>

    <record id="hr_appraisal_skill_rule_employee_update" model="ir.rule">
        <field name="name">Appraisal skill: employee: create/write/unlink by manager</field>
        <field name="model_id" ref="model_hr_appraisal_skill"/>
        <field name="domain_force">[('manager_ids.user_id', 'in', user.ids)]</field>
        <field name="perm_read" eval="False"/>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>
</odoo>
