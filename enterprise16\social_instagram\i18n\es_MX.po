# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "HACE 34 SEGUNDOS"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comentarios\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"Me gusta\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">Mi página de Instagram</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<span>Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required by Instagram.</span><br/>\n"
"                <span>We don't automatically resize your image to avoid undesired result.</span><br/>\n"
"                <span>More information on:</span>"
msgstr ""
"<span>Su imagen debe tener una relación de aspecto de 4:5 y 1.91:1, como lo requiere instagram.</span><br/>\n"
"                <span>Su imagen no se redimensionará para así evitar resultados inesperados.</span><br/>\n"
"                <span>Para más información visite:</span>"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "An image is required when posting on Instagram."
msgstr "Se requiere una imagen al publicar en instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "ID de la aplicación"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "Secreto de la aplicación"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Autor de la imagen"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr ""
"Marque esto si quiere usar su propia cuenta de desarrollador de Instagram en"
" lugar de la proporcionada."

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Could not find any account to add."
msgstr "No se encontró una cuenta que agregar."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "Mostrar la vista previa de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"ID de la cuenta de Facebook proporcionada por la API de Facebook, esto jamás se debe configurar manualmente.\n"
"La cuenta (\"profesional\") de Instagram siempre está vinculada a una cuenta de Facebook."

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
msgid "Instagram"
msgstr "Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "Token de acceso de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"Token de acceso a Instagram que proporcionó la API de Facebook, esto jamás se debe configurar de forma manual.\n"
"            Se usa para autentificar solicitudes cuando se publica o se lee información de esta cuenta."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "ID de la cuenta de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"ID de la cuenta de Instagram que proporcionó la API de Facebook, esto jamás "
"se debe configurar de forma manual."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "ID de la aplicación de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "Secreto de la aplicación de Instagram"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
#, python-format
msgid "Instagram Comments"
msgstr "Comentarios de Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "Cuenta de desarrollador de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "ID de la cuenta de Instagram y Facebook"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "ID del autor de Instagram y Facebook"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_id
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_id
msgid "Instagram Image"
msgstr "Imagen de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "Me gusta de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "ID de la publicación de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "URL de la publicación de Instagram"

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "Publicaciones de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "Vista previa de Instagram"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Instagram did not provide a valid access token."
msgstr "Instagram no proporcionó un token de acceso válido."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Me gusta"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "Tipo de medios"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "No Instagram accounts linked with your Facebook page"
msgstr "No hay cuentas de Instagram vinculadas a su página de Facebook."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Only .jpg/.jpeg images can be posted on Instagram."
msgstr "Solo puede publicar imágenes en formato .jpg y .jpeg en Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Only the first attached image will be posted on Instagram."
msgstr "Solo se publicará la primer imagen adjunta en Instagram."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
#, python-format
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr ""
"Verifique que los comentarios están habilitados para esta publicación en la "
"plataforma."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "Publicar imagen"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "Cuenta de red social"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "Publicación en redes sociales en vivo"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "Redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "Publicación en redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicación en redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "Flujo social"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "Publicación en el flujo social"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr ""
"El ID de Facebook del autor de esta publicación de Instagram que se usa para"
" obtener la imagen de peril."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "Ocurrió un problema de autenticación durante su solicitud."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "No está autorizado. Póngase en contacto con su administrador."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "Use su propia cuenta de Instragram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr ""
"Se usa para permitir el acceso a Instagram para obtener la imagen de la "
"publicación"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Se utiliza para hacer comparaciones cuando necesitamos restringir algunas "
"características para una red social específica (\"Facebook\", \"Twitter\", "
"...)."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "No cuenta con una suscripción activa. Compre una aquí: %s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Your image appears to be corrupted, please try loading it again."
msgstr "Parece que su imagen está dañada, intente cargarla de nuevo."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#, python-format
msgid ""
"Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required "
"by Instagram."
msgstr ""
"Su imagen debe tener una relación de 4:5 y 1.91:1, como lo requiere "
"Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "https://www.facebook.com/help/instagram/1631821640426723"
msgstr "https://www.facebook.com/help/instagram/1631821640426723"
