# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_product
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2019-01-16 08:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_ir_attachment
msgid "Attachment"
msgstr "Συνημμένο"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Δημιουργία"

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "Νέα"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product Workspace"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "Είδη"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Select the tags to be applied to product's new documents"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"Select the workspace to be used for product's documents for the products of\n"
"                                    your company."
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr "Εργασία"

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor Credit Note"
msgstr "Πιστωτικό Προμηθευτή"

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr ""
