# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_reports_sbr_icp
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-15 15:40+0000\n"
"PO-Revision-Date: 2024-04-15 17:49+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.2\n"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid ""
"A new module (l10n_nl_reports_sbr_status_info) needs to be installed for the "
"service to work correctly."
msgstr ""
"Er moet een nieuwe module (l10n_nl_reports_sbr_status_info) worden "
"geïnstalleerd om de service correct te laten werken."

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_type
msgid ""
"BPL: if the taxpayer files a turnover tax return as an individual "
"entrepreneur.INT: if the turnover tax return is made by an intermediary."
msgstr ""
"BPL: als de belastingplichtige als individueel ondernemer aangifte "
"omzetbelasting doet. INT: als de aangifte omzetbelasting wordt gedaan door "
"een intermediair."

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__can_report_be_sent
msgid "Can Report Be Sent"
msgstr "Kan rapport worden verzonden"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__password
msgid "Certificate or private key password"
msgstr "Wachtwoord certificaat of privésleutel"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__is_test
msgid ""
"Check this if you want the system to use the pre-production environment with "
"test certificates."
msgstr ""
"Vink dit aan als je wilt dat het systeem de pre-productieomgeving met "
"testcertificaten gebruikt."

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Close"
msgstr "Sluiten"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Closing Entry"
msgstr "Afsluitboeking"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_initials
msgid "Contact Initials"
msgstr "Voorletters contactpersoon"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_surname
msgid "Contact Last Name"
msgstr "Achternaam contactpersoon"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_prefix
msgid "Contact Name Infix"
msgstr "Tussenvoegsels contactpersoon"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_phone
msgid "Contact Phone"
msgstr "Telefoonnummer contactpersoon"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_type
msgid "Contact Type"
msgstr "Soort contactpersoon"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Create Closing Entry"
msgstr "Afsluitboeking maken"

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Create EC Sales (ICP) XBRL for SBR"
msgstr "ICP-rapport maken voor SBR"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Download"
msgstr "Downloaden"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_l10n_nl_ec_sales_report_handler
msgid "Dutch EC Sales Report Custom Handler for SBR"
msgstr "Nederlands ICP custom handler voor SBR"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid "EC Sales (ICP) SBR"
msgstr "ICP SBR"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Go to Apps"
msgstr "Ga naar Apps"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "ICP report sent"
msgstr "ICP-rapportage verzonden"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__id
msgid "ID"
msgstr "ID"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__is_test
msgid "Is Test"
msgstr ""
"Is Test"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_l10n_nl_reports_sbr_icp_icp_wizard
msgid "L10n NL Intra-Communautaire Prestaties for SBR Wizard"
msgstr "L10n NL Intra-Communautaire Prestaties voor SBR Wizard"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_res_company__l10n_nl_reports_sbr_icp_last_sent_date_to
msgid "Last Date Sent (ICP)"
msgstr "Laatste datum verzonden (ICP)"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "New SBR File"
msgstr "Nieuw SBR-bestand"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"No Closing Entry was found for the selected period. Please create one and "
"post it before sending your report."
msgstr ""
"Er is geen afsluitboeking gevonden voor de geselecteerde periode. Maak er "
"een aan en boek deze voordat je je rapport verstuurt."

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__date_to
msgid "Period Ending Date"
msgstr "Periode einddatum"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__date_from
msgid "Period Starting Date"
msgstr "Periode startdatum"

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Send"
msgstr "Verzenden"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Sending your report"
msgstr "Uw rapport versturen"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_res_company__l10n_nl_reports_sbr_icp_last_sent_date_to
msgid ""
"Stores the date of the end of the last period submitted to the Digipoort "
"Services for ICP"
msgstr ""
"Slaat de datum op van het einde van de laatste periode die is ingediend bij "
"de Belastingdienst"

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__tax_consultant_number
msgid "Tax Consultant Number"
msgstr "Belastingconsulentennummer"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The Closing Entry for the selected period is still in draft. Please post it "
"before sending your report."
msgstr ""
"De afsluitboeking voor de geselecteerde periode is nog in concept. Plaats "
"deze eerst voordat je je rapport verstuurt."

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The ICP report from %s to %s was sent to Digipoort.<br/>We will post its "
"processing status in this chatter once received.<br/>Discussion id: %s"
msgstr ""
"Het ICP-rapport van %s tot %s is verzonden naar Digipoort.<br/>We zullen de "
"verwerkingsstatus in deze chatter posten zodra deze is ontvangen.<br/"
">Discussie id: %s"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The Tax Services returned the error hereunder. Please upgrade your module "
"and try again before submitting a ticket."
msgstr ""
"De Belastingdienst heeft onderstaande foutmelding teruggestuurd. Upgrade uw "
"module en probeer het opnieuw voordat u een ticket indient."

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__password
msgid "The password is not needed for just printing the XBRL file."
msgstr "Het wachtwoord is niet nodig voor het afdrukken van het XBRL-bestand."

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__tax_consultant_number
msgid ""
"The tax consultant number of the office aware of the content of this report."
msgstr ""
"Het belastingconsulentennummer van de opsteller van het bericht, die als "
"gemachtigde van de belastingplichtige/aangever is aangesteld."

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid "XBRL"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"Your ICP report is being sent to Digipoort. Check its status in the closing "
"entry's chatter."
msgstr ""
"Uw ICP-rapport wordt verzonden naar Digipoort. Controleer de status in de "
"afsluitende chatter."

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.icp_report_sbr
msgid "iso4217:EUR"
msgstr ""

#~ msgid "Success"
#~ msgstr "Succes"

#~ msgid "The ICP report from %s to %s was successfully sent to Digipoort."
#~ msgstr ""
#~ "Het ICP-rapport van %s naar %s is succesvol verzonden naar Digipoort."

#~ msgid "Your ICP report has been successfully sent."
#~ msgstr "Uw ICP-rapport is succesvol verzonden."
