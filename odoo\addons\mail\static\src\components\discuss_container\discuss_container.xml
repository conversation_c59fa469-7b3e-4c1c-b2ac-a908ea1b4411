<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.DiscussContainer" owl="1">
        <div class="o_DiscussContainer h-100 d-flex flex-column">
            <Discuss t-if="messaging and messaging.discuss and messaging.discuss.discussView and messaging.isInitialized" className="'flex-grow-1'" record="messaging.discuss.discussView"/>
            <div t-else="" class="o_DiscussContainer_spinner d-flex flex-grow-1 align-items-center justify-content-center">
                <i class="o_DiscussContainer_spinnerIcon fa fa-circle-o-notch fa-spin me-2"/>Please wait...
            </div>
        </div>
    </t>
</templates>
