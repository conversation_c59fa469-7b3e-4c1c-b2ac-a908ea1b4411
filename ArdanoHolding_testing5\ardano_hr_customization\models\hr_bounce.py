from odoo import api, models, fields
from odoo.exceptions import ValidationError

class HrBounce(models.Model):
    _name = 'hr.bounce'
    _rec_name = 'name'

    name = fields.Char(string="معامل التكليف", required=True)
    amount_percentage = fields.Float(string="Percentage", required=True,widget="percentage")
    contract_ids = fields.One2many('bounce.line', 'bounce_id', "Contracts")



class BounceContractLine(models.Model):
    _name = 'bounce.line'
    _rec_name = 'contract_class_id'

    bounce_id = fields.Many2one('hr.bounce')
    contract_class_id = fields.Many2one('contract.classification', string="Contract Classification")
    percentage = fields.Float("Percentage", related="contract_class_id.percentage", readonly=True)