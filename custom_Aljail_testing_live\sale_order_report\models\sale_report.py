# -*- coding: utf-8 -*-
# See LICENSE file for full copyright and licensing details.

from odoo import fields, models
import odoo


class SaleReport(models.Model):
    _inherit = "sale.report"

    brand_id = fields.Many2one("product.tag", "Brand", copy=False, readonly=True)
    branch_id = fields.Many2one('sale.order.branch', readonly=True, string="الفرع")


    def _select_sale(self):
        res = super(SaleReport, self)._select_sale()
        select_str = res + """,t.brand_id as brand_id,s.branch_id as branch_id"""
        return select_str

    def _group_by_sale(self):
        res = super(SaleReport, self)._group_by_sale()
        group_str = res + """,t.brand_id,s.branch_id"""
        return group_str


    # def _query(self, with_clause='', fields={}, groupby='', from_clause=''):
    #     fields['brand_id'] = ", t.brand_id as brand_id"
    #
    #     groupby += ', t.brand_id '
    #
    #     return super(SaleReport, self)._query(with_clause, fields, groupby, from_clause)
