<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_biotime_config_form" model="ir.ui.view">
        <field name="name">biotime.config.form</field>
        <field name="model">biotime.config</field>
        <field name="arch" type="xml">
            <form string="Biotime Configuration">
                <header>
                    <button string="Get All Terminal" name="action_get_all_terminals" type="object" />
                    <button string="Get All Employees" name="action_get_all_employees" type="object" />
                    <button string="Get Today Attendance" name="action_get_today_attendance" type="object" />
                    <button name="action_pull_specific_dates" 
                            attrs="{'invisible': ['|',('pull_from_date','=',False),('pull_to_date','=',False)]}" 
                            type="object" 
                            string="PULL ATTENDANCE FROM/TO" 
                            class="oe_highlight"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="server_url" />
                            <field name="username" />
                            <field name="password" password="1" />
                            <field name="company_id" options="{'no_create': True, 'no_open': True}" groups="base.group_multi_company" />
                        </group>
                        <group>
                            <field name="pull_from_date" />
                            <field name="pull_to_date" />
                        </group>
                    </group>
                    <notebook>
                        <page string="Sync Status" name="sync_status">
                            <group>
                                <group>
                                    <field name="last_sync_time" readonly="1"/>
                                    <field name="last_sync_status" readonly="1"/>
                                    <field name="last_sync_punch_count" readonly="1"/>
                                </group>
                                <group>
                                    <field name="last_sync_message" readonly="1" widget="html"/>
                                </group>
                            </group>
                        </page>
                        <page string="Attendance Processing Rules" name="attendance_rules">
                            <group>
                                <group string="Missing Punch Handling">
                                    <field name="missing_checkout_handling" widget="radio"/>
                                    <field name="missing_checkin_handling" widget="radio"/>
                                </group>
                                <group string="Default Work Hours">
                                    <field name="default_work_hours_from" widget="float_time"/>
                                    <field name="default_work_hours_to" widget="float_time"/>
                                    <field name="work_hours_tolerance"/>
                                    <field name="use_employee_contracts"/>
                                </group>
                            </group>
                            <group string="Night Shift Handling">
                                <field name="handle_night_shifts"/>
                                <field name="night_shift_hours" widget="float_time" attrs="{'invisible': [('handle_night_shifts', '=', False)]}"/>
                                <field name="night_shift_span_days" attrs="{'invisible': [('handle_night_shifts', '=', False)]}"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <p><strong>How these settings work:</strong></p>
                                <ul>
                                    <li><strong>Missing Check-out Handling:</strong></li>
                                    <ul>
                                        <li><strong>Ignore:</strong> Leaves punches with missing pairs as draft for manual processing</li>
                                        <li><strong>Auto-close at End of Day:</strong> Adds missing check-out at the end of the work day</li>
                                        <li><strong>Close at Next Check-in:</strong> Uses the time of the next check-in as the check-out time</li>
                                        <li><strong>Use Default Hours:</strong> Uses the configured default hours to create missing check-outs</li>
                                    </ul>
                                    <li><strong>Missing Check-in Handling:</strong></li>
                                    <ul>
                                        <li><strong>Ignore:</strong> Leaves punches with missing check-ins as draft for manual processing</li>
                                        <li><strong>Auto-open at Punch Time:</strong> Treats the first punch as a check-in (uses actual punch time)</li>
                                        <li><strong>Use Start of Day:</strong> Creates an artificial check-in at the configured start time, treats the punch as check-out</li>
                                    </ul>
                                    <li><strong>Night Shift Handling:</strong></li>
                                    <ul>
                                        <li>When enabled, the system will detect and properly handle night shifts that span across midnight</li>
                                        <li>Check-outs occurring after midnight but within the configured night shift duration will be associated with the previous day's check-in</li>
                                        <li>This prevents creating incorrect short attendances when shifts span across days</li>
                                    </ul>
                                    <li><strong>Default Work Hours:</strong></li>
                                    <ul>
                                        <li>These hours are used when handling missing check-ins/check-outs if no employee contract schedule is found or available</li>
                                        <li>When "Use Employee Contracts" is enabled, the system will first try to get work schedule from the employee's contract</li>
                                        <li>If no contract schedule is found, the system falls back to these default hours</li>
                                    </ul>
                                </ul>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers" />
                    <field name="activity_ids" widget="mail_activity" />
                    <field name="message_ids" widget="mail_thread" />
                </div>
            </form>
        </field>
    </record>

    <record id="view_biotime_config_search" model="ir.ui.view">
        <field name="name">biotime.config.search</field>
        <field name="model">biotime.config</field>
        <field name="arch" type="xml">
            <search string="Biotime Configuration">
                <field name="name"/>
                <field name="server_url"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}"/>
                    <filter string="Last Sync Status" name="sync_status" context="{'group_by': 'last_sync_status'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_biotime_config_tree" model="ir.ui.view">
        <field name="name">view.biotime_config.tree</field>
        <field name="model">biotime.config</field>
        <field name="arch" type="xml">
            <tree string="Biotime" decoration-muted="active == False">
                <field name="active" invisible="1"/>
                <field name="name" />
                <field name="server_url" />
                <field name="last_sync_time"/>
                <field name="last_sync_status"/>
                <field name="last_sync_punch_count"/>
            </tree>
        </field>
    </record>
    <record id="action_biotime_config_view" model="ir.actions.act_window">
        <field name="name">Biotime Settings</field>
        <field name="res_model">biotime.config</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'active_test': False}</field>
    </record>
</odoo>

