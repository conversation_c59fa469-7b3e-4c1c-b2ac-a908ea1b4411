<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="tax_report_ple_purchase_8_1" model="account.report">
        <field name="name">VAT Report (RCE Purchase 8.4)</field>
        <field name="root_report_id" ref="account.generic_tax_report" />
        <field name="country_id" ref="base.pe" />
        <field name="filter_fiscal_position" eval="True" />
        <field name="availability_condition">country</field>
        <field name="custom_handler_model_id" ref="model_l10n_pe_tax_ple_8_1_report_handler" />
        <field name="column_ids">
            <record id="tax_report_ple_8_1_date" model="account.report.column">
                <field name="name">Date</field>
                <field name="expression_label">date</field>
                <field name="figure_type">date</field>
            </record>
            <record id="tax_report_ple_8_1_doc_type" model="account.report.column">
                <field name="name">Doc. Type</field>
                <field name="expression_label">document_type</field>
                <field name="figure_type">none</field>
            </record>
            <record id="tax_report_ple_8_1_customer_vat" model="account.report.column">
                <field name="name">Customer VAT</field>
                <field name="expression_label">customer_vat</field>
                <field name="figure_type">none</field>
            </record>
            <record id="tax_report_ple_8_1_customer" model="account.report.column">
                <field name="name">Customer</field>
                <field name="expression_label">customer</field>
                <field name="figure_type">none</field>
            </record>
            <record id="tax_report_ple_8_1_amount_total" model="account.report.column">
                <field name="name">Amount Total</field>
                <field name="expression_label">amount_total</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_base_igv" model="account.report.column">
                <field name="name">BASE IGV</field>
                <field name="expression_label">base_igv</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_tax_igv" model="account.report.column">
                <field name="name">IGV</field>
                <field name="expression_label">tax_igv</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_vat_igv_g_ng" model="account.report.column">
                <field name="name">IGV GyNG</field>
                <field name="expression_label">vat_igv_g_ng</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_vat_igv_ng" model="account.report.column">
                <field name="name">IGV NG</field>
                <field name="expression_label">vat_igv_ng</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_base_exo" model="account.report.column">
                <field name="name">EXO</field>
                <field name="expression_label">base_exo</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_base_ina" model="account.report.column">
                <field name="name">INA</field>
                <field name="expression_label">base_ina</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_tax_ivap" model="account.report.column">
                <field name="name">IVAP</field>
                <field name="expression_label">tax_ivap</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_vat_icbper" model="account.report.column">
                <field name="name">ICBPER</field>
                <field name="expression_label">vat_icbper</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_tax_isc" model="account.report.column">
                <field name="name">ISC</field>
                <field name="expression_label">tax_isc</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_base_free" model="account.report.column">
                <field name="name">FREE</field>
                <field name="expression_label">base_free</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_vat_other" model="account.report.column">
                <field name="name">Other Taxes</field>
                <field name="expression_label">vat_other</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="tax_report_ple_8_1_base_withholdings" model="account.report.column">
                <field name="name">Withholdings</field>
                <field name="expression_label">base_withholdings</field>
                <field name="figure_type">monetary</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="ple_81_line" model="account.report.line">
                <field name="name">RCE 8.4</field>
                <field name="groupby">move_id</field>
                <field name="expression_ids">
                    <record id="ple_81_line_document_type" model="account.report.expression">
                        <field name="label">document_type</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">document_type</field>
                    </record>
                    <record id="ple_81_line_date" model="account.report.expression">
                        <field name="label">date</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">date</field>
                    </record>
                    <record id="ple_81_line_customer_vat" model="account.report.expression">
                        <field name="label">customer_vat</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">customer_vat</field>
                    </record>
                    <record id="ple_81_line_customer" model="account.report.expression">
                        <field name="label">customer</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">customer</field>
                    </record>
                    <record id="ple_81_line_amount_total" model="account.report.expression">
                        <field name="label">amount_total</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">amount_total</field>
                    </record>
                    <record id="ple_81_line_base_igv" model="account.report.expression">
                        <field name="label">base_igv</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">base_igv</field>
                    </record>
                    <record id="ple_81_line_tax_igv" model="account.report.expression">
                        <field name="label">tax_igv</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">tax_igv</field>
                    </record>
                    <record id="ple_81_line_vat_igv_g_ng" model="account.report.expression">
                        <field name="label">vat_igv_g_ng</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">vat_igv_g_ng</field>
                    </record>
                    <record id="ple_81_line_vat_igv_ng" model="account.report.expression">
                        <field name="label">vat_igv_ng</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">vat_igv_ng</field>
                    </record>
                    <record id="ple_81_line_base_exo" model="account.report.expression">
                        <field name="label">base_exo</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">base_exo</field>
                    </record>
                    <record id="ple_81_line_base_ina" model="account.report.expression">
                        <field name="label">base_ina</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">base_ina</field>
                    </record>
                    <record id="ple_81_line_tax_ivap" model="account.report.expression">
                        <field name="label">tax_ivap</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">tax_ivap</field>
                    </record>
                    <record id="ple_81_line_vat_icbper" model="account.report.expression">
                        <field name="label">vat_icbper</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">vat_icbper</field>
                    </record>
                    <record id="ple_81_line_tax_isc" model="account.report.expression">
                        <field name="label">tax_isc</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">tax_isc</field>
                    </record>
                    <record id="ple_81_line_base_free" model="account.report.expression">
                        <field name="label">base_free</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">base_free</field>
                    </record>
                    <record id="ple_81_line_vat_other" model="account.report.expression">
                        <field name="label">vat_other</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">vat_other</field>
                    </record>
                    <record id="ple_81_line_base_withholdings" model="account.report.expression">
                        <field name="label">base_withholdings</field>
                        <field name="engine">custom</field>
                        <field name="formula">_report_custom_engine_ple_81</field>
                        <field name="subformula">base_withholdings</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
