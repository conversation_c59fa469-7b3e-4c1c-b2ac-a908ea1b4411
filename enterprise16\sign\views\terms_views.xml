<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="sign.sign_terms_conditions_setting_banner" name="Sign Terms and Conditions Setting Banner">
        <div id="terms_conditions" t-ignore="true" class="text-center">
            <span>This is a preview of your Terms &amp; Conditions.</span>
            <a t-att-href="backend_url"><i class="fa fa-arrow-right me-1"/>Back to settings</a>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </template>

    <template id="sign_terms_conditions_page" name="Terms &amp; Conditions">
        <t t-call="web.frontend_layout">
            <t t-set="o_portal_fullwidth_alert" groups="sign.group_sign_manager">
                <t t-call="sign.sign_terms_conditions_setting_banner">
                    <t t-set="backend_url" t-value="'/web#action=sign.sign_settings_action&amp;model=res.config.settings'"/>
                </t>
            </t>
            <div class="oe_structure" id="oe_structure_terms_conditions"/>
                <div class="container oe_website_terms_conditions">
                    <div id="o_terms_conditions">
                        <div t-field="company.sign_terms_html"/>
                    </div>
                </div>
        </t>
    </template>
</odoo>
