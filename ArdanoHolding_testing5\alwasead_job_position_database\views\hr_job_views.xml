<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enhanced Job Position Form View -->
        <record id="view_hr_job_form_enhanced" model="ir.ui.view">
            <field name="name">hr.job.form.enhanced</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_hr_job_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="before">
                    <header>
                        <button name="action_create_jva_form" type="object" 
                                string="Create JVA Form" class="btn-primary"
                                attrs="{'invisible': [('staffing_status', '!=', 'understaffed')]}"/>
                        <field name="approval_state" widget="statusbar" 
                               statusbar_visible="draft,dept_approved,division_approved,final_approved"/>
                    </header>
                </xpath>
                
                <xpath expr="//field[@name='name']" position="after">
                    <field name="staffing_status" widget="badge" 
                           decoration-success="staffing_status == 'adequate'"
                           decoration-warning="staffing_status == 'overstaffed'"
                           decoration-danger="staffing_status == 'understaffed'"/>
                </xpath>

                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="division_id"/>
                    <field name="unit_id"/>
                </xpath>

                <xpath expr="//field[@name='no_of_recruitment']" position="after">
                    <field name="required_positions"/>
                    <field name="current_employees"/>
                    <field name="vacant_positions"/>
                </xpath>

                <xpath expr="//field[@name='description']" position="replace">
                    <notebook>
                        <page string="Job Information (معلومات الوظيفة)" name="job_info">
                            <group>
                                <group string="Basic Information">
                                    <field name="supervisor_id"/>
                                    <field name="place_of_work"/>
                                    <field name="job_category_id"/>
                                    <field name="job_grade_id"/>
                                </group>
                                <group string="Staffing">
                                    <field name="active_jva_count"/>
                                    <button name="action_view_jva_forms" type="object" 
                                            string="View JVA Forms" class="oe_link"
                                            attrs="{'invisible': [('active_jva_count', '=', 0)]}"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Job Description (الوصف الوظيفي)" name="job_description">
                            <group>
                                <field name="job_overview" placeholder="One clear paragraph summarizing the primary mission of the job"/>
                                <field name="key_responsibilities" placeholder="List of 6-12 specific tasks and outcomes"/>
                                <field name="key_competencies" placeholder="Key skills for the position to perform"/>
                                <field name="authorities_decisions" placeholder="What the position can decide or approve"/>
                                <field name="education_background" placeholder="Degrees, certifications, special licenses"/>
                                <field name="technical_skills" placeholder="Software, tools, procedures, standards"/>
                                <field name="kpis" placeholder="KPIs for measuring performance"/>
                                <field name="working_conditions" placeholder="Working conditions, environment, and risk exposure"/>
                            </group>
                        </page>
                        
                        <page string="Equipment &amp; Access (المعدات والتصاريح)" name="equipment_access">
                            <group>
                                <group string="Required Equipment">
                                    <field name="equipment_ids" nolabel="1">
                                        <tree editable="bottom">
                                            <field name="sequence" widget="handle"/>
                                            <field name="equipment_type"/>
                                            <field name="equipment_name"/>
                                            <field name="quantity"/>
                                            <field name="mandatory"/>
                                            <field name="estimated_cost"/>
                                        </tree>
                                    </field>
                                </group>
                                <group string="Software/Platform Access">
                                    <field name="access_ids" nolabel="1">
                                        <tree editable="bottom">
                                            <field name="sequence" widget="handle"/>
                                            <field name="access_type"/>
                                            <field name="system_name"/>
                                            <field name="access_level"/>
                                            <field name="mandatory"/>
                                            <field name="requires_license"/>
                                        </tree>
                                    </field>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Approval Tracking (تتبع الموافقات)" name="approval_tracking">
                            <group>
                                <group string="Preparation">
                                    <field name="prepared_by"/>
                                    <field name="prepared_date"/>
                                </group>
                                <group string="Approvals">
                                    <field name="approved_by_dept_head"/>
                                    <field name="approved_by_division_head"/>
                                    <field name="approved_by_general_manager"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Job Position Tree View -->
        <record id="view_hr_job_tree_enhanced" model="ir.ui.view">
            <field name="name">hr.job.tree.enhanced</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_hr_job_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="division_id"/>
                    <field name="job_category_id"/>
                    <field name="job_grade_id"/>
                </xpath>
                
                <xpath expr="//field[@name='no_of_recruitment']" position="after">
                    <field name="required_positions"/>
                    <field name="current_employees"/>
                    <field name="vacant_positions"/>
                    <field name="staffing_status" widget="badge" 
                           decoration-success="staffing_status == 'adequate'"
                           decoration-warning="staffing_status == 'overstaffed'"
                           decoration-danger="staffing_status == 'understaffed'"/>
                </xpath>
            </field>
        </record>

        <!-- Job Position Search View -->
        <record id="view_hr_job_search_enhanced" model="ir.ui.view">
            <field name="name">hr.job.search.enhanced</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_job_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="division_id"/>
                    <field name="job_category_id"/>
                    <field name="job_grade_id"/>
                    <field name="supervisor_id"/>
                </xpath>
                
                <xpath expr="//filter[@name='archived']" position="after">
                    <separator/>
                    <filter string="Understaffed" name="understaffed"
                            domain="[('staffing_status', '=', 'understaffed')]"/>
                    <filter string="Adequately Staffed" name="adequate"
                            domain="[('staffing_status', '=', 'adequate')]"/>
                    <filter string="Overstaffed" name="overstaffed"
                            domain="[('staffing_status', '=', 'overstaffed')]"/>
                </xpath>
                
                <xpath expr="//group" position="inside">
                    <filter string="Division" name="group_division" 
                            context="{'group_by': 'division_id'}"/>
                    <filter string="Job Category" name="group_category" 
                            context="{'group_by': 'job_category_id'}"/>
                    <filter string="Job Grade" name="group_grade" 
                            context="{'group_by': 'job_grade_id'}"/>
                    <filter string="Staffing Status" name="group_staffing" 
                            context="{'group_by': 'staffing_status'}"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
