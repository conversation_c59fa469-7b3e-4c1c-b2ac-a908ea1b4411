# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_se_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-09 09:33+0000\n"
"PO-Revision-Date: 2022-11-09 09:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_TOTAL
msgid "ASSETS"
msgstr ""

#. module: l10n_se_reports
#: model:ir.model,name:l10n_se_reports.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4040_TOTAL
msgid "Accounts payable"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302010
msgid "Accounts receivable"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_40110_TOTAL
msgid "Accrued expenses and prepaid income"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4030_TOTAL
msgid "Advances from customers"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_201050
msgid "Advances regarding intangible non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301050
msgid "Advances to suppliers"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10106010
msgid "Allocated year-end results"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.column,name:l10n_se_reports.account_financial_report_ec_sales_goods
msgid "Amount of goods"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.column,name:l10n_se_reports.account_financial_report_ec_sales_service
msgid "Amount of services"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.column,name:l10n_se_reports.account_financial_report_ec_sales_tri
msgid "Amount of third party sales"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.column,name:l10n_se_reports.account_financial_report_bs_column
#: model:account.report.column,name:l10n_se_reports.account_financial_report_pnl_by_cost_column
msgid "Balance"
msgstr ""

#. module: l10n_se_reports
#: model:account.report,name:l10n_se_reports.account_financial_report_bs
msgid "Balance sheet"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_101050_TOTAL
msgid "Balanced gain or loss"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4010_TOTAL
msgid "Bond loans"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_201010
msgid "Capitalized expenses for development work and similar work"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_3040_TOTAL
msgid "Cash and bank"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_2_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_2
msgid "Changes in inventory"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_102030_TOTAL
msgid "Changes in the equity fund"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_102040_TOTAL
msgid "Changes in the fair value reserve"
msgstr ""

#. module: l10n_se_reports
#: code:addons/l10n_se_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_201020
msgid "Concessions, patents, licenses, trademarks and similar rights"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_30_TOTAL
msgid "Current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101035
msgid "Dedicated funds"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_102020_TOTAL
msgid "Deposits or withdrawals during the year"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_8_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_8
msgid ""
"Depreciation and write-downs of tangible and intangible non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_9_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_9
msgid "Depreciation of current assets in addition to normal depreciation"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_16_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_16
msgid ""
"Depreciation of financial non-current assets and short-term investments"
msgstr ""

#. module: l10n_se_reports
#: model:account.report,name:l10n_se_reports.swedish_ec_sales_report
msgid "EC sales Report"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_TOTAL
msgid "EQUITY, PROVISIONS AND LIABILITIES"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202030
msgid "Equipment, tools and installations"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_102010_TOTAL
msgid "Equity at the beginning of the financial year"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10_TOTAL
msgid ""
"Equity, with information on what constitutes unrestricted equity and "
"restricted equity"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4050_TOTAL
msgid "Exchange liabilities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10104030
msgid "Fair value fund"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_2030_TOTAL
msgid "Financial assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_FP_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_FP_TOTAL
msgid "Financial items"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301030
msgid "Finished goods and goods for sale"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10102020
msgid "Free share premium fund"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10104040
msgid "Fund for development expenditure"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301020
msgid "Goods under production"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_201040
msgid "Goodwill"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202050
msgid "Improvement leasehold"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_13_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_13
msgid "Income from other companies in which there is an ownership interest"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_14_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_14
msgid ""
"Income from other securities and receivables that are non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_12_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_12
msgid ""
"Income from shares in associated companies and jointly controlled companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_11_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_11
msgid "Income from shares in group companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_2010_TOTAL
msgid "Intangible assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_17_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_17
msgid "Interest expenses and similar expenses"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_3010_TOTAL
msgid "Inventories, etc."
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203050
msgid "Investments in other companies"
msgstr ""

#. module: l10n_se_reports
#: code:addons/l10n_se_reports/models/account_sales_report.py:0
#: code:addons/l10n_se_reports/models/account_sales_report.py:0
#, python-format
msgid "KVR"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202010
msgid "Land and buildings"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_40_TOTAL
msgid "Liabilities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4060_TOTAL
msgid "Liabilities to Group companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4070_TOTAL
msgid "Liabilities to associated companies and jointly controlled companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4020_TOTAL
msgid "Liabilities to credit institutions"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4080_TOTAL
msgid "Liabilities to other companies in which there is an ownership interest"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203080
msgid ""
"Loans to co-owners and others, to whom co-owners are in such a relationship "
"as stated in ch. 1 § 3, 4 or 5 of the Swedish Companies Act (2005: 551)"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202020
msgid "Machinery and other technical facilities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10102510
msgid "Membership efforts"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_1_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_1
msgid "Net sales"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_20_TOTAL
msgid "Non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202040
msgid ""
"Ongoing new facilities and advances regarding tangible non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301040
msgid "Ongoing work on behalf of others"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_K_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_K_TOTAL
msgid "Operating costs"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_I_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_I_TOTAL
msgid "Operating income, stock changes, etc."
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_RR_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_RR_TOTAL
msgid "Operating results"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10104050
msgid "Other"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_6_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_6
msgid "Other external expenses"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101040_TOTAL
msgid "Other funds"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_15_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_15
msgid "Other interest income and similar income"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301060
msgid "Other inventory assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_40100_TOTAL
msgid "Other liabilities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_20309
msgid "Other long-term receivables"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203070
msgid "Other long-term securities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_10_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_10
msgid "Other operating expenses"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_4_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_4
msgid "Other operating income"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_3030
msgid "Other provisions"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302050
msgid "Other receivables"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_303020
msgid "Other short-term investments"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_202060
msgid "Other tangible non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_20_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_20
msgid "Other taxes"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101025_TOTAL
msgid "Paid-in and issue contributions"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_7_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_7
msgid "Personnel costs"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302060
msgid "Prepayments and accrued income"
msgstr ""

#. module: l10n_se_reports
#: model:account.report,name:l10n_se_reports.account_financial_report_pnl_by_cost
#: model:ir.actions.client,name:l10n_se_reports.action_account_report_se_pnl
msgid "Profit and Loss"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_30_TOTAL
msgid "Provisions"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_3010
msgid "Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_3020
msgid "Provisions for taxes"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10102520
msgid "Publisher's contribution"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_5_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_301010
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_5
msgid "Raw materials and consumables"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_3020_TOTAL
msgid "Receivables"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203040
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302030
msgid "Receivables from associated companies and jointly controlled companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203020
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302020
msgid "Receivables from group companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203060
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_302040
msgid ""
"Receivables from other companies in which there is an ownership interest"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_201030
msgid "Rental rights and similar rights"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10104010
msgid "Reserve fund"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10102010
msgid "Restricted share premium fund / Stake issue"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101030_TOTAL
msgid "Revaluation fund"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101010_TOTAL
msgid "Share Capital"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101020_TOTAL
msgid "Share premium funds"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203030
msgid "Shares in associated companies and jointly controlled companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_203010
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_303010
msgid "Shares in group companies"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_3030_TOTAL
msgid "Short-term investments"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_10_TOTAL
msgid "Subscribed but unpaid capital"
msgstr ""

#. module: l10n_se_reports
#: model:ir.model,name:l10n_se_reports.model_l10n_se_ec_sales_report_handler
msgid "Swedish EC Sales Report Custom Handler"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_A_2020_TOTAL
msgid "Tangible non-current assets"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_4090_TOTAL
msgid "Tax liabilities"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_19_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_19
msgid "Tax on this year's profit"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_RS_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_RS_TOTAL
msgid "Taxes"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_bs_EL_10106020_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_10106020
msgid "Unallocated year-end results"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_20_TOTAL
msgid "Untaxed reserves"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.column,name:l10n_se_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_3_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_3
msgid "Work for own account"
msgstr ""

#. module: l10n_se_reports
#: code:addons/l10n_se_reports/models/account_generic_tax_report.py:0
#: code:addons/l10n_se_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "XML"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_C_18_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_C_18
msgid "Year-end appropriations"
msgstr ""

#. module: l10n_se_reports
#: model:account.report.expression,report_line_name:l10n_se_reports.account_financial_report_pnl_by_cost_TOTAL_balance
#: model:account.report.line,name:l10n_se_reports.account_financial_report_bs_EL_101060_TOTAL
#: model:account.report.line,name:l10n_se_reports.account_financial_report_pnl_by_cost_TOTAL
msgid "Year-end results"
msgstr ""

#. module: l10n_se_reports
#: code:addons/l10n_se_reports/models/account_sales_report.py:0
#, python-format
msgid "You can only export Monthly or Quarterly reports."
msgstr ""
