# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_amazon
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_amazon
#: model:mail.template,body_html:sale_amazon.order_sync_failure
msgid ""
"<div>\n"
"            <p>The synchronization of the Amazon order with reference <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> encountered an error and was not completed.</p>\n"
"            <p>Unless the order is canceled in SellerCentral, no other synchronization will be attempted.</p>\n"
"            <p>To schedule a new synchronization attempt, proceed as follows:\n"
"                <ol>\n"
"                    <li>Enter the Developer Tools.</li>\n"
"                    <li>Open the form of the Amazon Account on which the order was placed.</li>\n"
"                    <li>Navigate to the Order Follow-up tab.</li>\n"
"                    <li>Set a date for <em>Last Orders Sync</em> that is anterior to the last status update of the order.</li>\n"
"                    <li>Save the changes and click on the <em>SYNC ORDERS</em> button.</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>If the problem persists, contact <a href=\"https://www.odoo.com/help/\">Odoo support</a>.</p>\n"
"        </div>\n"
"        "
msgstr ""
"<div>\n"
"            <p>아마존 주문 참조번호 <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> 와 관련하여 오류로 인해 동기화가 완료되지 못했습니다.</p>\n"
"            <p>판매자 페이지에서 주문이 취소되지 않는 한, 동기화는 더 실행되지 않습니다.</p>\n"
"            <p>새로 동기화를 시도하도록 예약하려면 다음과 같이 진행하십시오:\n"
"                <ol>\n"
"                    <li>개발자 도구에 접속하십시오.</li>\n"
"                    <li>주문받은 Amazon 계정의 양식을 엽니다.</li>\n"
"                    <li>주문 후속조치 탭으로 이동합니다.</li>\n"
"                    <li>날짜 설정을 <em>최근 주문 동기화</em>로 하여 주문의 최근 상태 업데이트 이전으로 설정합니다.</li>\n"
"                    <li>변경 사항을 저장하고 <em>주문 동기화</em> 버튼을 클릭합니다.</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>문제가 계속될 경우에는 <a href=\"https://www.odoo.com/help/\">Odoo 고객지원</a>으로 문의주시기 바랍니다.</p>\n"
"        </div>\n"
"        "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__api_ref
msgid "API Identifier"
msgstr "API 식별자"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_access_key
msgid "AWS Access Key"
msgstr "AWS 액세스 키"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_secret_key
msgid "AWS Secret Key"
msgstr "AWS 보안 키"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_session_token
msgid "AWS Session Token"
msgstr "AWS 세션 토큰"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__account_id
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Account"
msgstr "계정"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Account Name"
msgstr "계정 이름"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Active"
msgstr "활성"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"After validation of the credentials, the marketplaces\n"
"                                                to which this account has access will be\n"
"                                                synchronized and automatically made available."
msgstr ""
"자격 증명을 확인한 후 이 계정이 접근할 수 있는 \n"
"                                                  마켓플레이스가 동기화되어 \n"
"                                                  자동으로 사용할 수 있게 됩니다."

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_account
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Amazon Account"
msgstr "아마존 계정"

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_account_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Accounts"
msgstr "아마존 계정"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,field_description:sale_amazon.field_res_users__amazon_email
msgid "Amazon Email"
msgstr "아마존 이메일"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "Amazon Item Ref"
msgstr "Amazon 아이템 번호"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_location__amazon_location
msgid "Amazon Location"
msgstr "아마존 위치"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_marketplace
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_form
msgid "Amazon Marketplace"
msgstr "아마존 마켓 플레이스"

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_marketplace_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Marketplaces"
msgstr "아마존 마켓 플레이스"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_offer
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_offer_id
msgid "Amazon Offer"
msgstr "아마존 제의"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "Amazon Offers"
msgstr "아마존 제의"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_order_ref
msgid "Amazon Order Ref"
msgstr "아마존 주문 참조"

#. module: sale_amazon
#: model:product.template,name:sale_amazon.default_product_product_template
msgid "Amazon Sale"
msgstr "아마존 세일"

#. module: sale_amazon
#: model:product.template,name:sale_amazon.shipping_product_product_template
msgid "Amazon Shipping"
msgstr "아마존 배송"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Amazon Sync Pending"
msgstr "아마존 동기화 보류"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_crm_team__amazon_team
msgid "Amazon Team"
msgstr "아마존 팀"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Amazon accounts correspond to Amazon Seller Central accounts."
msgstr "Amazon 계정은 Amazon Seller Central 계정에 해당합니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_marketplace.py:0
#, python-format
msgid "Amazon marketplaces cannot be deleted."
msgstr "아마존 마켓 플레이스는 삭제할 수 없습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Amazon move : %s"
msgstr "아마존 이동 : %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"Since the current carrier doesn't automatically provide a tracking "
"reference, you need to set one manually."
msgstr ""
"Amazon에서는 배송 시 추적 번호를 요청하고 있습니다. 현재 배송업체에서는 자동으로 추적 번호를 안내하고 있지 않으므로 수동으로 "
"설정해야 합니다. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"You need to assign a carrier to this delivery."
msgstr "Amazon에서는 모든 배송 건에 대해 배송조회 번호를 제출해야 합니다. 해당 배송 건에 대해 배송업체를 지정해야 합니다."

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AE
msgid "Amazon.ae"
msgstr "Amazon.ae"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_CA
msgid "Amazon.ca"
msgstr "Amazon.ca"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_JP
msgid "Amazon.co.jp"
msgstr "Amazon.co.jp"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_UK
msgid "Amazon.co.uk"
msgstr "Amazon.co.uk"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_US
msgid "Amazon.com"
msgstr "Amazon.com"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AU
msgid "Amazon.com.au"
msgstr "Amazon.com.au"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BE
msgid "Amazon.com.be"
msgstr "Amazon.com.be"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BR
msgid "Amazon.com.br"
msgstr "Amazon.com.br"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_MX
msgid "Amazon.com.mx"
msgstr "Amazon.com.mx"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_TR
msgid "Amazon.com.tr"
msgstr "Amazon.com.tr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_DE
msgid "Amazon.de"
msgstr "Amazon.de"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_EG
msgid "Amazon.eg"
msgstr "Amazon.eg"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_ES
msgid "Amazon.es"
msgstr "Amazon.es"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_FR
msgid "Amazon.fr"
msgstr "Amazon.fr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IN
msgid "Amazon.in"
msgstr "Amazon.in"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IT
msgid "Amazon.it"
msgstr "Amazon.it"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_NL
msgid "Amazon.nl"
msgstr "Amazon.nl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_PL
msgid "Amazon.pl"
msgstr "Amazon.pl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SA
msgid "Amazon.sa"
msgstr "Amazon.sa"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SE
msgid "Amazon.se"
msgstr "Amazon.se"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SG
msgid "Amazon.sg"
msgstr "Amazon.sg"

#. module: sale_amazon
#: model:mail.template,name:sale_amazon.order_sync_failure
msgid "Amazon: Order Synchronization Failure"
msgstr "Amazon: 주문 동기화 실패"

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_orders_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_orders
msgid "Amazon: sync orders"
msgstr "아마존 : 주문 동기화"

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_pickings_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_pickings
msgid "Amazon: sync pickings"
msgstr "아마존 : 선별 동기화"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred"
msgstr "오류가 발생했습니다."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred while linking your account with Amazon."
msgstr "계정을 Amazon 과 연결하는 동안 오류가 발생했습니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "Available Marketplaces"
msgstr "사용 가능한 마켓 플레이스"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "Back to my account"
msgstr "내 계정으로 돌아가기"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__company_id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__company_id
msgid "Company"
msgstr "회사"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "APO 연결을 설정할 수 없습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the feed URL."
msgstr "피드 URL 연결을 설정할 수 없습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the proxy."
msgstr "프록시 연결을 설정할 수 없습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/controllers/onboarding.py:0
#, python-format
msgid "Could not find Amazon account with id %s"
msgstr "%s 아이디는 Amazon 계정에서 찾을 수 없습니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_uid
msgid "Created by"
msgstr "작성자"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_date
msgid "Created on"
msgstr "작성일자"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Credentials"
msgstr "자격 인증"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/res_config_settings.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
#, python-format
msgid "Default Products"
msgstr "기본 제품"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__display_name
msgid "Display Name"
msgstr "표시명"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Error code: %s; description: %s"
msgstr "오류 코드: %s; 내용: %s"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__eu-west-1
msgid "Europe"
msgstr "유럽"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-west-2
msgid "Far East"
msgstr "극동 지역"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Amazon"
msgstr "아마존이 고객 주문 처리함"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Merchant"
msgstr "판매자가 고객 주문 처리함"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_channel
msgid "Fulfillment Channel"
msgstr "고객 주문 처리 채널"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fba
msgid "Fulfillment by Amazon"
msgstr "아마존이 고객 주문 처리"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fbm
msgid "Fulfillment by Merchant"
msgstr "판매자가 고객 주문 처리"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"Gift message:\n"
"%s"
msgstr ""
"선물 메시지 : \n"
"%s"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__id
msgid "ID"
msgstr "ID"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active
msgid ""
"If made inactive, this account will no longer be synchronized with Amazon."
msgstr "비활성화하면 이 계정은 더 이상 Amazon과 동기화되지 않습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"If the date is set in the past, orders placed on this Amazon Account before the first synchronization of the module might be synchronized with Odoo.\n"
"If the date is set in the future, orders placed on this Amazon Account between the previous and the new date will not be synchronized with Odoo."
msgstr ""
"날짜가 이전에 설정된 경우 모듈의 첫 번째 동기화 전에 이 Amazon 계정에 배치된 주문이 Odoo와 동기화될 수 있습니다.\n"
"향후 날짜를 설정하면 이전 날짜와 새 날짜 사이에 이 Amazon 계정에 배치된 주문이 Odoo와 동기화되지 않습니다."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"If this account gained access to new marketplaces,"
"                                         synchronize and add them to the "
"current sync marketplaces"
msgstr "이 계정으로 새로운 마켓 플레이스에 액세스 한 경우 동기화하여 현재 동기화 마켓 플레이스에 추가하십시오"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Inactive"
msgstr "비활성"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_location
msgid "Inventory Locations"
msgstr "재고 공간"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__is_follow_up_displayed
msgid "Is Follow Up Displayed"
msgstr "후속 조치 여부"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token
msgid "LWA Access Token"
msgstr "LWA 액세스 토큰"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__refresh_token
msgid "LWA Refresh Token"
msgstr "LWA 새로고침 토큰"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__last_orders_sync
msgid "Last Orders Sync"
msgstr "마지막 주문 동기화"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Link with Amazon"
msgstr "Amazon과 연결"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Link your Amazon account with Odoo to start synchronizing your\n"
"                                Amazon orders."
msgstr ""
"Amazon 계정을 Odoo와 연결하시면 Amazon 주문 동기화를\n"
"                                시작하실 수 있습니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__marketplace_id
msgid "Marketplace"
msgstr "마켓 플레이스"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Marketplaces"
msgstr "마켓 플레이스"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__name
msgid "Name"
msgstr "이름"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-east-1
msgid "North America"
msgstr "북미 지역"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_product__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_template__offer_count
msgid "Offer Count"
msgstr "제안 수"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: code:addons/sale_amazon/models/product_product.py:0
#: code:addons/sale_amazon/models/product_template.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_product_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_template_view_form
#, python-format
msgid "Offers"
msgstr "제안"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Only available marketplaces can be selected"
msgstr "사용 가능한 마켓 플레이스만 선택할 수 있습니다"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__order_count
msgid "Order Count"
msgstr "주문 수"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Order Follow-up"
msgstr "주문 후속 조치"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#, python-format
msgid "Orders"
msgstr "주문"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_template
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_id
msgid "Product"
msgstr "품목"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_template_id
msgid "Product Template"
msgstr "품목 양식"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_product
msgid "Product Variant"
msgstr "품목 세부선택"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Products delivered to Amazon customers must have their respective parts in "
"the same package. Operations related to the product %s were not all "
"confirmed at once."
msgstr ""
"Amazon 고객에게 배송되는 품목은 동일한 패키지에 해당하는 부속품들이 포함되어 있어야 합니다. %s 품목과 관련된 작업이 모두 동시에"
" 승인되지 못했습니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__region
msgid "Region"
msgstr "지역"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register your Amazon account"
msgstr "아마존 계정 등록"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register yours to start synchronizing your orders into Odoo."
msgstr "주문을 Odoo로 동기화하기 위해 등록하십시오."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token
msgid "Restricted Data Token"
msgstr "특별 취급 데이터 토큰"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__sku
msgid "SKU"
msgstr "SKU"

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_offer_unique_sku
msgid "SKU must be unique for a given account and marketplace."
msgstr "SKU는 특정 계정 및 마켓 플레이스에 대해 고유해야 합니다."

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_crm_team
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__team_id
msgid "Sales Team"
msgstr "영업팀"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__user_id
msgid "Salesperson"
msgstr "영업사원"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Select the marketplace on which your seller account\n"
"                                                was originally created."
msgstr ""
"판매자 계정이 원래 생성된 마켓 플레이스를 \n"
"                                                 선택합니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__seller_central_url
msgid "Seller Central URL"
msgstr "판매자 센트럴 URL"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__seller_key
msgid "Seller Key"
msgstr "판매자 키"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__base_marketplace_id
msgid "Sign-up Marketplace"
msgstr "마켓 플레이스 가입"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__location_id
msgid "Stock Location"
msgstr "재고 공간"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Successfully updated the marketplaces available to this account!"
msgstr "이 계정으로 이용 가능한 마켓 플레이스를 성공적으로 업데이트했습니다!"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "Sync Marketplaces"
msgstr "마켓 플레이스 동기화"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Orders"
msgstr "주문 동기화"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Pickings"
msgstr "선별 동기화"

#. module: sale_amazon
#: model:mail.template,subject:sale_amazon.order_sync_failure
msgid ""
"Synchronization of Amazon order {{ ctx.get('amazon_order_ref') }} has failed"
msgstr "Amazon 주문 {{ ctx.get('amazon_order_ref') }} 동기화에 실패했습니다"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__tax_included
msgid "Tax Included"
msgstr "세금 포함"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__region
msgid ""
"The Amazon region of the marketplace. Please refer to the Selling Partner "
"API documentation to find the correct region."
msgstr "Amazon 마켓플레이스 지역입니다. 올바른 지역을 찾으시려면 파트너 판매자 API 설명 자료를 참조하십시오."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "The Amazon-defined item reference."
msgstr "Amazon에서 정의한 아이템 번호입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__api_ref
msgid "The Amazon-defined marketplace reference."
msgstr "Amazon에서 설정한 마켓플레이스 번호입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order__amazon_order_ref
msgid "The Amazon-defined order reference."
msgstr "Amazon에서 설정한 주문 번호입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__team_id
msgid "The Sales Team assigned to Amazon orders for reporting"
msgstr "보고를 위해 아마존 주문에 지정된 영업 팀"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__sku
msgid "The Stock Keeping Unit."
msgstr "재고 관리 단위"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "API와의 통신에 실패했습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Error code: %s; description: %s"
msgstr ""
"API와의 통신에 실패했습니다.\n"
"오류 코드: %s; 설명: %s"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,help:sale_amazon.field_res_users__amazon_email
msgid "The encrypted email of the customer. Does not forward mails."
msgstr "암호화된 고객 이메일. 메일을 포워딩하지 않습니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__last_orders_sync
msgid ""
"The last synchronization date for orders placed on this account. Orders "
"whose status has not changed since this date will not be created nor updated"
" in Odoo."
msgstr ""
"이 계정의 주문에 대한 마지막 동기화 날짜입니다. 이 날짜 이후 상태가 변경되지 않은 주문은 Odoo에서 생성되거나 업데이트되지 "
"않습니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__location_id
msgid ""
"The location of the stock managed by Amazon under the Amazon Fulfillment "
"program."
msgstr "Amazon 풀필먼트 프로그램에 따라 Amazon에서 관리하는 재고 위치입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__refresh_token
msgid "The long-lived token that can be exchanged for a new access token."
msgstr "신규 액세스 토큰으로 교환할 수 있는 장기 사용 토큰입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__marketplace_id
msgid "The marketplace of this offer."
msgstr "이 제안에 대한 마켓플레이스입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "The marketplaces this account has access to."
msgstr "이 계정으로 액세스할 수 있는 마켓플레이스입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "The marketplaces this account sells on."
msgstr "이 계정으로 판매하는 마켓플레이스입니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_credentials_expiry
msgid "The moment at which the AWS credentials become invalid."
msgstr "AWS 자격 증명이 무효화되는 시점입니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token_expiry
msgid "The moment at which the Restricted Data Token becomes invalid."
msgstr "특별 취급 데이터 토큰이 무효화되는 시기입니다."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token_expiry
msgid "The moment at which the token becomes invalid."
msgstr "토큰이 무효화되는 시점입니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/sale_order.py:0
#, python-format
msgid ""
"The order has been canceled by the Amazon customer while someproducts have "
"already been delivered. Please create a return for this order to adjust the "
"stock."
msgstr ""
"일부 품목이 이미 배송된 상태에서 Amazon 고객이 주문을 취소했습니다. 재고를 조정하려면 이 주문에 대한 반품 항목을 생성하십시오."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__base_marketplace_id
msgid ""
"The original sign-up marketplace of this account. Used for authentication "
"only."
msgstr "이 계정의 원래 가입 마켓 플레이스 인증에만 사용됩니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__account_id
msgid "The seller account used to manage this product."
msgstr "이 제품을 관리하는데 사용하는 판매자 계정입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_access_key
msgid "The short-lived key used to identify the assumed ARN role on AWS."
msgstr "AWS에서 ARN을 식별하는데 사용하는 단기용 키입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_secret_key
msgid ""
"The short-lived key used to verify the access to the assumed ARN role on "
"AWS."
msgstr "AWS에서 ARN에 대한 액세스를 식별하는데 사용하는 단기 키입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__restricted_data_token
msgid ""
"The short-lived token used instead of the LWA Access Token to access "
"restricted data"
msgstr "특별 취급 데이터에 액세스하기 위한 LWA 액세스 토큰 대신 사용하는 단기 키입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__access_token
msgid "The short-lived token used to query Amazon API on behalf of a seller."
msgstr "판매자를 대신하여 Amazon API를 쿼리하는데 사용하는 단기 토큰입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_session_token
msgid ""
"The short-lived token used to query the SP-API with the assumed ARN role on "
"AWS."
msgstr "AWS에서 ARN에 대한 SP-API를 쿼리하는데 사용하는 단기용 토큰입니다."

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__name
msgid "The user-defined name of the account."
msgstr "사용자가 설정한 계정명입니다."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_marketplace_unique_api_ref
msgid "There can only exist one marketplace for a given API Identifier."
msgstr "주어진 API 식별자에는 하나의 마켓 플레이스만 존재할 수 있습니다."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_unique_amazon_order_ref
msgid ""
"There can only exist one sale order for a given Amazon Order Reference."
msgstr "지정된 Amazon 주문 참조에 대해 하나의 판매 주문만 존재할 수 있습니다."

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_line_unique_amazon_item_ref
msgid ""
"There can only exist one sale order line for a given Amazon Item Reference."
msgstr "지정된 Amazon Item Reference에 대해 하나의 판매 주문 내역만 존재할 수 있습니다."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"This action will disconnect your account with Amazon and"
"                                          cannot be undone. Are you sure you"
" want to proceed?"
msgstr "이 작업 실행 시 Amazon과의 계정 연결이 해제되며 취소할 수 없게 됩니다. 계속하시겠습니까?"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_picking
msgid "Transfer"
msgstr "전송"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Unlink account"
msgstr "계정 연결 해제"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Update Available Marketplaces"
msgstr "사용 가능한 마켓 플레이스 업데이트"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "View on Seller Central"
msgstr "셀러 센트럴에서 보기"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Warning"
msgstr "경고"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Whether the picking must be notified to Amazon or not."
msgstr ""

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__tax_included
msgid "Whether the price includes the tax amount or not."
msgstr "가격에 세금 포함 여부"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_location__amazon_location
msgid ""
"Whether this location represents the stock of a seller managed by Amazon "
"under the Amazon Fulfillment program or not."
msgstr ""

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_crm_team__amazon_team
msgid "Whether this sales team is associated with Amazon orders or not."
msgstr ""

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "You first need to authorize the Amazon account %s."
msgstr "먼저 Amazon 계정 %s에 권한을 지정해야 합니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"You first need to set the marketplaces to synchronize for the Amazon account"
" %s."
msgstr "먼저 Amazon 계정 %s에 대해 동기화할 마켓플레이스를 설정해야 합니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"You reached the maximum number of requests for this operation; please try "
"again later."
msgstr "이 작업에서 가능한 최대 요청 수에 도달했습니다. 나중에 다시 시도하시기 바랍니다."

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Your Amazon account is linked with Odoo."
msgstr "귀하의 Amazon 계정은 Odoo와 연결되어 있습니다."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"[%s] %s\n"
"Condition: %s - %s"
msgstr ""
"[%s] %s\n"
"조건 : %s - %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Delivery Charges for %s"
msgstr "[%s] %s에 대한 배송료"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Gift Wrapping Charges for %s"
msgstr "[%s] %s에 대한 선물 포장 요금"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "e.g. American Market"
msgstr "예 : 미국 시장"
