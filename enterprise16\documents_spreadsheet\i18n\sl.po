# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
#, python-format
msgid "\"%s\" saved as template"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/components/spreadsheet_component.js:0
#, python-format
msgid "%s - Template"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid ""
"<strong class=\"o_form_label\">Spreadsheets</strong>\n"
"                            <span class=\"fa fa-lg fa-building-o ms-1\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Ask an admin to configure the workspace to be accessible to the users you "
"want."
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#, python-format
msgid "Blank spreadsheet"
msgstr "Prazna preglednica"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"By default, the spreadsheets in this workspace will only be seen and updated"
" by their <strong>creator</strong>."
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Cancel"
msgstr "Prekliči"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "Podjetja"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Confirm"
msgstr "Potrdi"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "Avtorji prispevkov"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#, python-format
msgid "Create"
msgstr "Ustvari"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.xml:0
#, python-format
msgid "Create Spreadsheet"
msgstr "Ustvari preglednico"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__data
msgid "Data"
msgstr "Podatki"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
msgid "Document"
msgstr "Dokument"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__documents_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__documents_spreadsheet_folder_id
msgid "Documents Spreadsheet Folder"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Edit"
msgstr "Uredi"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Enjoy collaborative work on your spreadsheets."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__raw
msgid "File Content (raw)"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "ID"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "If you want to work together on those spreadsheets :"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr "Zadnji datum posodobitve"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Make a copy"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Manage and work with all the <strong>spreadsheets</strong> created in other "
"applications."
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Move them to another workspace"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr "Moje predloge"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "Naziv"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
#, python-format
msgid "New Spreadsheet"
msgstr "Nova preglednica"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
#, python-format
msgid "New sheet saved in Documents"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_selector_dialog/document_selector_panel.js:0
#, python-format
msgid "New spreadsheet created in Documents"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
#, python-format
msgid "New spreadsheet template created"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_page
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_single
msgid "Odoo Spreadsheets not available for download"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/inspector/documents_inspector.js:0
#: code:addons/documents_spreadsheet/static/src/documents_view/kanban/documents_kanban_model.js:0
#, python-format
msgid "Only XLSX files can be opened with Odoo Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_revision_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
msgid "Save as template"
msgstr "Shrani kot predlogo"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: documents_spreadsheet
#: model:documents.folder,name:documents_spreadsheet.documents_spreadsheet_folder
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_revision_view_search
msgid "Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "Pregled preglednice"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "Posnetek preglednice"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr "Predlog preglednic"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/inspector/documents_inspector.js:0
#, python-format
msgid ""
"Spreadsheets mass download not yet supported.\n"
" Download spreadsheets individually instead."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "Ime predloge"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/document.py:0
#, python-format
msgid "The file is not a xlsx file"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/document.py:0
#, python-format
msgid "The xlsx file is corrupted"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/document.py:0
#: code:addons/documents_spreadsheet/models/document.py:0
#, python-format
msgid "The xlsx file is too big"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "Ikona"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/components/control_panel/spreadsheet_breadcrumbs.xml:0
#, python-format
msgid "Toggle favorite"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
#, python-format
msgid "Untitled spreadsheet template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "Uporabnik"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr "Delovni prostor"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
#, python-format
msgid ""
"Your file is about to be saved as an Odoo Spreadsheet to allow for edition."
msgstr ""
