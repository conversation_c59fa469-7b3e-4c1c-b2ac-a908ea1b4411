<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="planning_slot_view_search_inherit_sale_project_forecast" model="ir.ui.view">
        <field name="name">planning.slot.search.inherit.sale.project.forecast</field>
        <field name="model">planning.slot</field>
        <field name="inherit_id" ref="sale_planning.planning_slot_view_search_inherit_sale_planning"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='my_project']" position="after">
                <xpath expr="//filter[@name='my_sale_orders']" position="move"/>
            </xpath>
        </field>
    </record>

    <!-- Override action contexts to allow group_expand to apply for project_id and task_id -->
    <record id="planning.planning_action_my_calendar" model="ir.actions.act_window">
        <field name="context">{
            'search_default_my_shifts': 1, 'search_default_open_shifts': 1,
            'planning_expand_employee': 1, 'planning_expand_role': 1, 'planning_expand_sale_line_id': 1,
            'planning_expand_project': 1,
        }</field>
    </record>

    <record id="planning.planning_action_schedule_by_resource" model="ir.actions.act_window">
        <field name="context">{
            'search_default_group_by_employee': 1, 'planning_expand_employee': 1, 'planning_expand_role': 1,
            'planning_expand_sale_line_id': 1, 'planning_expand_project': 1, 'planning_expand_resource': 1,
        }</field>
    </record>

    <record id="planning.planning_action_schedule_by_role" model="ir.actions.act_window">
        <field name="context">{
            'search_default_group_by_employee': 2, 'planning_groupby_role': True,
            'planning_expand_sale_line_id': 1, 'planning_expand_project': 1,
        }</field>
    </record>

    <record id="project_forecast.planning_action_schedule_by_project" model="ir.actions.act_window">
        <field name="domain">[('start_datetime', '!=', False), ('end_datetime', '!=', False)]</field>
    </record>

    <record id="project_forecast.project_forecast_action_schedule_by_employee" model="ir.actions.act_window">
        <field name="domain">[('start_datetime', '!=', False), ('end_datetime', '!=', False)]</field>
    </record>

    <record id="sale_planning.sale_planning_action_schedule_by_sale_order" model="ir.actions.act_window">
        <field name="context">{
            'planning_groupby_sale_order': True,
            'planning_expand_employee': 1, 'planning_expand_role': 1, 'planning_expand_sale_line_id': 1,
            'planning_expand_project': 1,
        }</field>
    </record>
</odoo>
