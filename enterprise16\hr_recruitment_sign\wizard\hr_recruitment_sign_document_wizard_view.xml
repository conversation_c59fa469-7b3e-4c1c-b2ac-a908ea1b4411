<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_recruitment_sign_view" model="ir.ui.view">
        <field name="name">hr_recruitment_sign</field>
        <field name="model">hr.recruitment.sign.document.wizard</field>
        <field name="arch" type="xml">
            <form string="Signature Request">
                <div role="alert" class="alert alert-warning text-center"
                    attrs="{'invisible': [('template_warning', '=', False)]}"
                    title="No template available">
                    <span class="fa fa-exclamation-triangle"/>
                    <field name="template_warning" nolabel="1" readonly="1"/>
                </div>
                <field name="applicant_id" invisible="1"/>
                <field name="has_both_template" invisible="1"/>
                <field name="sign_template_responsible_ids" invisible="1"/>
                <field name="possible_template_ids" invisible="1"/>
                <group class="o_label_nowrap">
                    <field name="sign_template_ids"
                        options="{'no_create': True}"
                        kanban_view_ref="%(sign.sign_template_view_kanban_mobile)s"
                        widget="many2many_tags"
                        force_save="1"/>
                    <field name="partner_name"/>
                </group>
                <group attrs="{'invisible': [('sign_template_ids', '=', [])]}" string="Sign Request Options">
                    <field name="applicant_role_id" options="{'no_create': True}"/>
                    <field name="responsible_id" options="{'no_create_edit': True}"
                        attrs="{
                            'invisible': [('has_both_template', '=', False)],
                            'required': [('has_both_template', '=', True)]}"/>
                </group>
                <group class="o_label_nowrap" string="Mail Options">
                    <field name="subject" placeholder="Signature Request"/>
                    <field name="cc_partner_ids" widget="many2many_tags"
                        placeholder="Write email or search contact..."/>
                </group>
                <field name="message" placeholder="Optional Message..." widget="html"/>
                <field name="attachment_ids" widget="many2many_binary" string="Attach a file"/>
                <footer>
                    <button name="validate_signature" string="Send" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="sign_recruitment_wizard_action" model="ir.actions.act_window">
        <field name="name">Document Signature</field>
        <field name="res_model">hr.recruitment.sign.document.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
