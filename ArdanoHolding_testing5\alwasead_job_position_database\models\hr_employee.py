# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Enhanced organizational hierarchy
    division_id = fields.Many2one(
        'hr.division',
        string='Division',
        related='department_id.division_id',
        store=True,
        readonly=True
    )
    unit_id = fields.Many2one(
        'hr.unit',
        string='Unit/Team',
        help="Unit or team within the department"
    )

    # Job position enhancements
    job_grade_id = fields.Many2one(
        'hr.job.grade',
        string='Job Grade',
        related='job_id.job_grade_id',
        store=True,
        readonly=True
    )
    job_category_id = fields.Many2one(
        'hr.job.category',
        string='Job Category',
        related='job_id.job_category_id',
        store=True,
        readonly=True
    )

    # Equipment assignments
    assigned_equipment_ids = fields.Many2many(
        'hr.job.equipment',
        'hr_job_equipment_employee_rel',
        'employee_id',
        'equipment_id',
        string='Assigned Equipment'
    )
    equipment_count = fields.Integer(
        string='Equipment Count',
        compute='_compute_equipment_count'
    )

    # Access credentials
    assigned_access_ids = fields.Many2many(
        'hr.job.access',
        'hr_job_access_employee_rel',
        'employee_id',
        'access_id',
        string='Assigned Access'
    )
    access_count = fields.Integer(
        string='Access Count',
        compute='_compute_access_count'
    )

    # Performance tracking
    current_kpis = fields.Text(
        string='Current KPIs',
        related='job_id.kpis',
        readonly=True
    )

    # Salary calculation fields
    location_factor = fields.Float(
        string='Location Factor',
        compute='_compute_location_factor',
        help="Salary factor based on work location"
    )
    grade_multiplier = fields.Float(
        string='Grade Multiplier',
        related='job_grade_id.base_salary_multiplier',
        readonly=True
    )

    @api.depends('assigned_equipment_ids')
    def _compute_equipment_count(self):
        for employee in self:
            employee.equipment_count = len(employee.assigned_equipment_ids)

    @api.depends('assigned_access_ids')
    def _compute_access_count(self):
        for employee in self:
            employee.access_count = len(employee.assigned_access_ids)

    @api.depends('job_id', 'job_id.place_of_work')
    def _compute_location_factor(self):
        """Calculate location factor based on job work place"""
        for employee in self:
            if employee.job_id and employee.job_id.place_of_work == 'site':
                employee.location_factor = 1.2  # 20% increase for site work
            else:
                employee.location_factor = 1.0  # Base rate for office work

    def action_view_equipment(self):
        """View assigned equipment"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assigned Equipment'),
            'res_model': 'hr.job.equipment',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.assigned_equipment_ids.ids)],
        }

    def action_view_access(self):
        """View assigned access"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assigned Access'),
            'res_model': 'hr.job.access',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.assigned_access_ids.ids)],
        }

    def action_assign_equipment(self):
        """Open wizard to assign equipment"""
        if not self.job_id:
            raise UserError(_('Employee must have a job position to assign equipment.'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assign Equipment'),
            'res_model': 'hr.employee.equipment.assign.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_employee_id': self.id,
                'default_job_id': self.job_id.id
            }
        }

    def action_assign_access(self):
        """Open wizard to assign access"""
        if not self.job_id:
            raise UserError(_('Employee must have a job position to assign access.'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Assign Access'),
            'res_model': 'hr.employee.access.assign.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_employee_id': self.id,
                'default_job_id': self.job_id.id
            }
        }

    @api.onchange('job_id')
    def _onchange_job_id(self):
        """Update department and unit when job changes"""
        if self.job_id:
            if self.job_id.department_id:
                self.department_id = self.job_id.department_id
            if self.job_id.unit_id:
                self.unit_id = self.job_id.unit_id

    @api.onchange('department_id')
    def _onchange_department_id(self):
        """Clear unit if department changes"""
        if self.department_id and self.unit_id:
            if self.unit_id.department_id != self.department_id:
                self.unit_id = False

    def calculate_adjusted_salary(self, base_salary):
        """Calculate salary with location factor and grade multiplier"""
        if not base_salary:
            return 0.0
        
        adjusted_salary = base_salary
        
        # Apply grade multiplier
        if self.grade_multiplier:
            adjusted_salary *= self.grade_multiplier
        
        # Apply location factor
        if self.location_factor:
            adjusted_salary *= self.location_factor
        
        return adjusted_salary
