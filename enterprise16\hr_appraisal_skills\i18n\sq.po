# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_skills
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid ""
"<span class=\"o_appraisal_overlay\" attrs=\"{'invisible': [('state', '!=', 'new')]}\">\n"
"                            Skills tab will be active once the appraisal is confirmed.\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal_skills
#. odoo-javascript
#: code:addons/hr_appraisal_skills/static/src/js/appraisal_skills_one2many.xml:0
#, python-format
msgid "Add new skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__appraisal_id
msgid "Appraisal"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.actions.act_window,name:hr_appraisal_skills.hr_appraisal_skill_report_action
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill_report
msgid "Appraisal Skills Report"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.actions.act_window,help:hr_appraisal_skills.hr_appraisal_skill_report_action
msgid "Appraisal skills appear only for marked appraisals."
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__company_id
msgid "Company"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_uid
msgid "Created by"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_date
msgid "Created on"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__current_skill_level_id
msgid "Current Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__current_level_progress
msgid "Current Skill Progress"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__decline
msgid "Decline"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Department"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__display_name
msgid "Display Name"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Employee"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal
msgid "Employee Appraisal"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__display_name
msgid "Employee Name"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_skill_id
msgid "Employee Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill
msgid "Employee Skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__evolution
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Evolution"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__evolution_sequence
msgid "Evolution Sequence"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__id
msgid "ID"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__improvement
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Improvement"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__just_added
msgid "Just added"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__justification
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__justification
msgid "Justification"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__manager_ids
msgid "Manager"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "No Change"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__previous_skill_level_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__previous_skill_level_id
msgid "Previous Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__previous_level_progress
msgid "Previous Skill Progress"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,help:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Regression"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__same
msgid "Same"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__skill_id
msgid "Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_level_id
msgid "Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Skill Type"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal__skill_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid "Skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.ui.menu,name:hr_appraisal_skills.menu_appraisal_skills_report
#: model_terms:ir.actions.act_window,help:hr_appraisal_skills.hr_appraisal_skill_report_action
msgid "Skills Evolution"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.constraint,message:hr_appraisal_skills.constraint_hr_appraisal_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been added."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been deleted."
msgstr ""
