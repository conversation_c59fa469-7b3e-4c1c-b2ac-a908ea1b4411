# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_enterprise
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:05+0000\n"
"PO-Revision-Date: 2019-08-26 09:38+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "# Customers"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "# Lines"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Average Order"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields,field_description:sale_enterprise.field_sale_report__days_to_confirm
msgid "Days To Confirm"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Days to Confirm"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale_enterprise.view_order_product_search_inherit
msgid "Fully Invoiced"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields,field_description:sale_enterprise.field_sale_report__invoice_status
msgid "Invoice Status"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__no
msgid "Nothing to Invoice"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Orders"
msgstr ""

#. module: sale_enterprise
#: model:ir.actions.act_window,name:sale_enterprise.sale_report_action_dashboard
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_pivot
msgid "Sales Analysis"
msgstr ""

#. module: sale_enterprise
#: model:ir.model,name:sale_enterprise.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Sales Teams"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:sale_enterprise.view_order_product_search_inherit
msgid "To Invoice"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Total Sales"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Total, Tax Included"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "Untaxed Total"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_dashboard
msgid "days"
msgstr ""
