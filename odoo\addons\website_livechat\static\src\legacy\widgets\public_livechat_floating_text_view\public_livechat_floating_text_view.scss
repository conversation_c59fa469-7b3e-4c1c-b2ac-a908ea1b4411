
.o_PublicLivechatFloatingTextView {
    animation: o_PublicLivechatFloatingTextView_animation 0.6s ease-in-out forwards;
    display: flex;
    position: absolute;
    box-shadow: $box-shadow;
    background-color: $white;
    color: $gray-800;
    right: 4.0rem;
    bottom: 2.0rem;
    border-radius: 0.5rem 0.5rem 0 0.5rem;
    transform-origin: 100% 100%;
    user-select: none;
}

@keyframes o_PublicLivechatFloatingTextView_animation {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    20% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
    }
    75% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.o_PublicLivechatFloatingTextView_background {
    transition: filter 0.3s;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 0.5rem 0.5rem 0 0.5rem;
    background-color: $white;
}

.o_PublicLivechatFloatingTextView_text {
    padding: 0.75rem 1rem;
    position: relative;
    color: $gray-800;
    font-size: 1.2rem;
    font-weight: 500;
    font-family: var(--body-font-family);
    text-shadow: none;
}
