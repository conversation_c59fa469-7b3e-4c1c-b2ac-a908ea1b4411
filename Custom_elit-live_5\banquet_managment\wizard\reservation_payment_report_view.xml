<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_reservation_payment_report_wizard" model="ir.ui.view">
            <field name="name">reservation.payment.report.wizard.form</field>
            <field name="model">reservation.payment.report.wizard</field>
            <field name="arch" type="xml">
                <form string="Reservation Payment Report">
                    <group>
                        <group>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="check_in_state"/>
                        </group>
                    </group>
                    <footer>
                        <button name="print_report" string="Export Excel" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_reservation_payment_report_wizard" model="ir.actions.act_window">
            <field name="name">Reservation Payment Report</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">reservation.payment.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <menuitem id="menu_reservation_payment_report"
            name="Reservation Payment Report"
            parent="hotel.hotel_report_menu"
            action="action_reservation_payment_report_wizard"
            sequence="4"/>
    </data>
</odoo>