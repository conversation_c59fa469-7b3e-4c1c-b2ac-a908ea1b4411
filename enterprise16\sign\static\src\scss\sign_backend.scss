.o_web_client {
    .o_content {
        .favorite_sign_button {
            position: absolute;
            @include o-hover-text-color($o-main-color-muted, $o-main-favorite-color);

            &:hover:before {
                content: "\f005";
            }

            &.favorite_sign_button_enabled {
                @include o-hover-text-color($o-main-favorite-color, $o-main-color-muted);

                &:hover:before {
                    content: "\f006";
                }
            }
        }
        .o_form_view {
            .o_field_pdfviewer, .o_field_pdf_viewer {
                width: 100%;
                height: 600px;
                border: 0;
            }
        }
    }
    .modal-content {
        .note-editable {
            border: 0;
        }
        .o_field_one2many.o_sign_flat_o2m {
            table {
                border-collapse: initial;
                border: none;
                --table-hover-bg: #{$o-view-background-color};
                --table-striped-bg: #{$o-view-background-color};
            }
            tr.o_data_row {
                border-bottom: none !important;
                background-color: white !important;
                cursor: pointer !important;
            }
            th {
                border-bottom: none !important;
                width: auto !important;
            }
            tbody > tr {
                border: none;
                outline: none;
            }
            tbody > tr:not(.o_data_row) {
                display: none;
            }
            tbody .o_form_uri:first-line {
                color: #666666 !important;
                background-color: white;
                border: none !important;
                box-shadow: none !important;
            }
            tbody > tr:nth-child(1) {
                box-shadow: none !important;
                border: none !important;
            }
            tbody > tr > td.o_list_many2one {
                outline: none;
                border: none !important;
                color: #212529 !important;
                padding-left: 0px;
                cursor: default !important;
                input {
                    border-bottom: 1px solid var(--o-input-border-color) !important;
                }
            }
            tbody > tr > td.o_list_many2one:nth-child(1) {
                @include media-breakpoint-down(lg) {
                    width: 100px !important;
                }
                @include media-breakpoint-down(sm, $o-extra-grid-breakpoints) {
                    display: block;
                    font-weight: normal;
                    color: $o-brand-secondary!important;
                    > span {
                        margin-bottom: 0!important;
                    }
                }
                width: 150px !important;
                color: $o-gray-900 !important;
                font-weight: 500;
            }
            tbody > tr > td:not(:nth-child(1)){
                color: $o-gray-900 !important;
                padding-left: 0px;
                cursor: text !important;
                border: none;
                &:nth-child(2) {
                    @include media-breakpoint-down(sm, $o-extra-grid-breakpoints) {
                        display: block !important;
                        min-height: 47px;
                    }
                }
                &:nth-child(3) {
                    width: 80px !important;
                    text-align:right;
                    @include media-breakpoint-down(sm, $o-extra-grid-breakpoints) {
                        vertical-align: bottom !important;
                        padding-bottom: 20px;
                        input {
                            margin-bottom: 0px;
                        }
                    }
                }
            }
            tfoot {
                display: none;
            }
            // hide the optionalColumnsDropdown button in a listview added by web_enterprise
            div.o_optional_columns_dropdown {
                display: none;
            }
            th.o_column_sortable:not(:empty):last-child {
                padding-right: 1px !important;
            }
        }
    }

    /* Module pages */
    .o_sign_template, .o_sign_document {
        display: flex;
        flex-flow: column nowrap;
        @include o-position-absolute(0, 0, 0, 0);
        @include media-breakpoint-down(md) {
            position: static;
            .o_sign_pdf_iframe {
                flex: 0 0 100%;
            }
        }
        background-color: $o-view-background-color;
    }

    /* Template edit view */
    .o_sign_template {
        .fa-pencil {
            cursor: pointer;
            opacity: 0.5;
            transition: opacity ease 250ms;
        }
        :hover > .fa-pencil {
            opacity: 1.0; 
        }

        .o_sign_template_name_input {
            width: auto;
            display: inline-block;
            border: 1px dashed white;
            &:focus {
                border: 1px solid silver;
            }
            &[disabled] {
                background-color: transparent;
                border: none;
            }
        }

        .alert.o_sign_template_saved_info {
            padding: 5px;
            opacity: 0;
        }

        .o_sign_duplicate_sign_template {
            padding: 0;
            margin-bottom: 2px;
        }
    }

    .o_sign_create_partner {
        .fa-exclamation-circle {
            padding: 0 10px;
            color: map-get($theme-colors, 'danger');
        }
        .fa-check-circle {
            padding-left: 10px;
            color: map-get($theme-colors, 'success');
        }
    }

    .o_sign_add_partner {
        border-bottom: 1px dashed $o-brand-secondary;
    }

    .o_sign_delete_field_button {
        float: right;
        &:before {
            font-family: FontAwesome;
            content: "";
        }
    }

    #o_sign_pdf_ext {
        padding-top: 0;
        padding-bottom: 0;
    }

    .o_sign_resend_access_button {
        padding: 0;
    }
    
    .o_popover_offset {
        top: 162px !important;
        left: -4px !important #{"/*rtl:ignore*/"};
        left: 4px !important #{"/*rtl:remove*/"};
    }

    .o_sign_item_custom_popover{

        label {
            display: inline-block;
            max-width: 100%;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 1em;
        }

        .o_input {
            padding: 5px;
        }

        .o_sign_responsible_select {
            display: block;
            position: relative;
        }
    }

    .popover {
        .o_sign_delete_field_button {
            float: right;
            margin-top: 8px;
            margin-bottom: 8px;
        }

        .o_sign_delete_field_button.fa-trash {
            cursor: pointer;
            opacity: 0.5;
            transition: opacity ease 250ms;
            margin-top: 8px;
        }
        :hover > .o_sign_delete_field_button.fa-trash {
            opacity: 1.0;
        }
    }

    .o_sign_close_button {
        padding: 0;
        background-color: transparent;
        border: 0;
        float: right;
        line-height: 1;
        text-shadow: 0 1px 0 #FFFFFF;
        opacity: .5;
    }
}

.o_block_scroll {
    overflow: hidden;
}

.o_kanban_view {
    .o_kanban_record {
        .o_kanban_record_body {
            .o_signers {
                max-height: 6.3rem;
                overflow: auto;
            }
        }

        &.o_sign_sticky_bottom {
            .o_kanban_record_body {
                margin-bottom: 2rem;
            }

            .o_kanban_record_bottom {
                position: absolute;
                bottom: 0.5rem;
                width: 100%;
                @extend .pe-4;
            }
        }
    }
}

.o_sign_template_kanban {
    .o_kanban_record {
        .o_kanban_record_top {
            .o_dropdown_kanban {
                .dropdown-menu {
                    // the content of items in the dropdown menu of sign template is very long
                    // the width of the dropdown menu is extended to 12.9rem
                    // which leaves the same space on the left and right of the colorpicker
                    min-width: 12.9rem;
                    .oe_kanban_colorpicker {
                        // the colorpicker is allowed to display more than 3 colors per line
                        max-width: 12.9rem;
                    }
                }
            }
        }
        .o_kanban_record_body {
            .o_signers {
                .fa-times {
                    position: absolute;
                }
                input {
                    position: absolute;
                }
                span {
                    margin-left: 1.3rem;
                }
            }
        }
    }
}

@include media-breakpoint-down(md) {
    .o_kanban_view {
        .o_kanban_record {
            &.o_sign_sticky_bottom {
                .o_kanban_record_body {
                    margin-bottom: 3rem;
                }
                .o_kanban_record_bottom {
                    position: absolute;
                    bottom: 0.5rem;
                    width: 100%;
                    padding-right: 32px !important;
                }
            }
        }
    }
}

.o_sign_attachments {
    .o_attachments {
        display: flex;
        flex-wrap: wrap;

        .o_attachment {
            width: auto;
        }
    }
}

@include media-breakpoint-down(md) {
    .o_sign_signer_status_wrapper {
        padding-right: 16px;
    }
}

.o_sign_signer_status_wrapper {
    height: 2.7rem;
}
