# Translation of Odoo Server.
# This file contains the translation of the following modules:
#     * l10n_pe_edi_stock
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-20 17:29+0000\n"
"PO-Revision-Date: 2022-05-20 17:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.sunat_guiaremision
msgid "09"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.sunat_guiaremision
msgid "1.0"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.sunat_guiaremision
msgid "2.1"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Delivery Guide Number</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Licence</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Operator</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Reason for Transfer</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid ""
"<strong>This document is a printed representation of a Guía de Remisión. "
"Please review in www.sunat.gob.pe</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Transfer Start Date</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "<strong>Transport Type</strong>"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "A document type is required for the company"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_pe_edi_stock.l10n_pe_edi_vehicle_actions
msgid "Add a new vehicle for PE delivery guide"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_observation
msgid ""
"Additional information to be displayed in the Delivery Slip in order to "
"clarify or complement information about the transferred products. It has a "
"maximum length of 250 characters."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "An identification number is required for the company."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model,name:l10n_pe_edi_stock.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_departure_start_date
msgid ""
"By default this is when the transfer is validated, but to make it possible "
"to still send the delivery guide when the transport is some days later, the "
"user can still adapt this date."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__country_code
msgid "Country Code"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_pe_edi_stock.vehicle_list_action
msgid "Create the first vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__create_date
msgid "Created on"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Decode Delivery Guide sunat generated for the %s document."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_latam_document_number
msgid "Delivery Guide Number"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_ir_attachment__display_name
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__display_name
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Download"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model,name:l10n_pe_edi_stock.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Export"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_pe_edi_stock.l10n_pe_edi_vehicle_actions
msgid ""
"For delivery guides that use private transport, you need to supply information\n"
"            about the vehicle licence and allow to define the default operator for each delivery."
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Generate Delivery Guide"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "I must add the document type of the company"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "I must add the identification number  of the company"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "I must add the licence number  of the vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_account_edi_format__id
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_ir_attachment__id
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__id
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__id
msgid "ID"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_operator_id
msgid ""
"If the transport is public, please define the transport provider, else, the "
"internal operator."
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Ignore"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Import"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_content
msgid "L10N Pe Edi Content"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_error
msgid "L10N Pe Edi Error"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_operator_id
msgid "L10N Pe Edi Operator"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_transport_type
msgid "L10N Pe Edi Transport Type"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_vehicle_id
msgid "L10N Pe Edi Vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_account_edi_format____last_update
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_ir_attachment____last_update
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle____last_update
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__license_plate
msgid "License Plate"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.l10n_pe_edi_guiaremision_report_delivery_document
msgid "Notes:"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_observation
msgid "Observation"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__operator_id
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Operator"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__13
#, python-format
msgid "Others"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "PE EDI"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model,name:l10n_pe_edi_stock.model_l10n_pe_edi_vehicle
msgid "PE EDI Vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_status
msgid "PE EDI status"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.ui.menu,name:l10n_pe_edi_stock.menu_stock_config_settings_pe
msgid "Peru"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Please set a Delivery Address as the delivery guide needs one."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_transport_type__02
#, python-format
msgid "Private transport"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_transport_type__01
#, python-format
msgid "Public transport"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Purchase"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_reason_for_transfer
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Reason for transfer"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Retry"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__01
#, python-format
msgid "Sale"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__14
#, python-format
msgid "Sale subject to buyer confirmation"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_status__sent
msgid "Sent"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The EDI document was successfully cancel and signed by the government."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid ""
"The EDI document was successfully created and signed by the government."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The Sunat Delivery Guide failed to cancel"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The Sunat Delivery Guide failed to sign"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The Sunat document Delivery Guide decode"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The client must have a defined district"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The client must have a defined district."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "This company has no connection with the Sunat configured."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__operator_id
msgid ""
"This value will be used by default in the picking. Define this value when "
"the operator is always the same for the vehicle."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_status__to_send
msgid "To Send"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model,name:l10n_pe_edi_stock.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_stock_picking__l10n_pe_edi_departure_start_date
msgid "Transfer Start Date"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__04
#, python-format
msgid "Transfer between establishments of the same company"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__18
#, python-format
msgid "Transfer issuer itinerant cp"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Transfer start date"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:l10n_pe_edi_stock.selection__stock_picking__l10n_pe_edi_reason_for_transfer__19
#, python-format
msgid "Transfer to primary zone"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Transport"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,field_description:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__name
msgid "Transport Name"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Transport type"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Unsigned Delivery Guide CFDI generated for the %s document."
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.model.fields,help:l10n_pe_edi_stock.field_l10n_pe_edi_vehicle__license_plate
msgid "Value to be used on the XML for delivery guide."
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.view_picking_edi_form
msgid "Vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.ui.menu,name:l10n_pe_edi_stock.menu_stock_pe_vehicles
msgid "Vehicles"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.actions.act_window,name:l10n_pe_edi_stock.l10n_pe_edi_vehicle_actions
msgid "Vehicles (PE)"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the reason for the transfer"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the reason for the transfer."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the start date of the transfer"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the start date of the transfer."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the transfer operator."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the transfer vehicle"
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must choose the transfer vehicle."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must select a transport type to generate the delivery guide."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/account_edi_format.py:0
#, python-format
msgid "You need to configure the district on the company address."
msgstr ""

#. module: l10n_pe_edi_stock
#: code:addons/l10n_pe_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You need to specify a Document Number"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.sunat_guiaremision
msgid "false"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.vehicle_form_view
msgid "vehicle Form"
msgstr ""

#. module: l10n_pe_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_pe_edi_stock.vehicle_tree_view
msgid "vehicle Tree"
msgstr ""

#. module: l10n_pe_edi_stock
#: model:ir.actions.act_window,name:l10n_pe_edi_stock.vehicle_list_action
msgid "vehicles"
msgstr ""
