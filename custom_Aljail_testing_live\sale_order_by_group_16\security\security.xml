<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.module.category" id="module_sale_order_by_branch">
        <field name="name">Restrict Sale Order by Branch</field>
        <field name="description">Restrict Sale Order by Branch</field>
        <field name="sequence">45</field>
    </record>

    <record id="group_sale_order_branch" model="res.groups">
        <field name="name">User: Branch Documents</field>
        <field name="category_id" ref="module_sale_order_by_branch"/>
    </record>

    <record model="ir.rule" id="sale_order_by_branch_records_rule">
        <field name="name">Restrict Sale Order by Branch</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="domain_force">[('branch_id', 'in', [c.id for c in user.branch_id])]</field>
        <field name="groups" eval="[(4, ref('sale_order_by_group_16.group_sale_order_branch'))]"/>
    </record>

    <record model="ir.rule" id="account_move_by_branch_records_rule">
        <field name="name">Restrict Invoice by Branch</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="domain_force">['|',('branch_id', 'in', [c.id for c in user.branch_id]),('branch_id','=',False)]</field>
        <field name="groups" eval="[(4, ref('sale_order_by_group_16.group_sale_order_branch'))]"/>
    </record>

    <record model="ir.rule" id="branch_records_rule">
        <field name="name">Restrict Branch IDs</field>
        <field name="model_id" ref="sale_order_by_group_16.model_sale_order_branch"/>
        <field name="domain_force">[('id', 'in', [c.id for c in user.branch_id])]</field>
        <!--        <field name="domain_force">[('branch_id', 'in', user.read(['branch_id'])[0]['branch_id'])]</field>-->
        <!--        <field name="domain_force">[('branch_id','in',user.branch_id)]</field>-->
        <field name="groups" eval="[(4, ref('sale_order_by_group_16.group_sale_order_branch'))]"/>
    </record>

</odoo>