<odoo>
    <template id="report_package_barcode_expiry" inherit_id="stock.report_package_barcode">
        <xpath expr="//t[@name='product_barcode_lot_datamatrix']" position="before">
            <t t-if="l.use_expiration_date">
                <t t-if="l.lot_id.use_date" t-set="product_barcode" t-value="product_barcode + '15' + l.lot_id.use_date.strftime('%y%m%d')"/>
                <t t-if="l.lot_id.expiration_date" t-set="product_barcode" t-value="product_barcode + '17' + l.lot_id.expiration_date.strftime('%y%m%d')"/>
            </t>
        </xpath>
    </template>
</odoo>
