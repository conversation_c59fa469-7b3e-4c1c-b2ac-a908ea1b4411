odoo.define('hotel_checkin.dashboard', function (require) {
    "use strict";

    var KanbanController = require('web.KanbanController');
    var KanbanView = require('web.KanbanView');
    var KanbanModel = require('web.KanbanModel');
    var KanbanRenderer = require('web.KanbanRenderer');
    var viewRegistry = require('web.view_registry');
    var core = require('web.core');
    var QWeb = core.qweb;
    var _t = core._t;

    var HotelCheckinKanbanController = KanbanController.extend({
        /**
         * @override
         */
        init: function (parent, model, renderer, params) {
            this._super.apply(this, arguments);
            this.hasSearchBar = params.hasSearchBar;
        },

        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                if (self.hasSearchBar) {
                    self.$('.o_hotel_checkin_search input').on('input', function (e) {
                        self._onSearchInput(e);
                    });
                }
            });
        },

        /**
         * Handle search input
         * @private
         * @param {Event} e
         */
        _onSearchInput: function (e) {
            var value = e.target.value.toLowerCase();
            this.$('.o_kanban_record').each(function () {
                var $record = $(this);
                var text = $record.text().toLowerCase();
                $record.toggle(text.indexOf(value) >= 0);
            });
        },

        /**
         * @override
         */
        _onQuickCreate: function (e) {
            // Override to handle quick check-in if needed
            this._super.apply(this, arguments);
        },
    });

    var HotelCheckinKanbanRenderer = KanbanRenderer.extend({
        /**
         * @override
         */
        init: function (parent, state, params) {
            this._super.apply(this, arguments);
            this.hasSearchBar = params.hasSearchBar;
            this.resortName = params.resortName || _t("Hotel Resort");
        },

        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                if (self.hasSearchBar) {
                    var $header = $(QWeb.render('HotelCheckinHeader', {
                        resortName: self.resortName
                    }));
                    self.$el.before($header);
                }
            });
        },

        /**
         * @override
         */
        _renderView: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                self.$el.addClass('o_hotel_checkin_kanban');
            });
        },
    });

    var HotelCheckinKanbanModel = KanbanModel.extend({
        // Add custom model methods if needed
    });

    var HotelCheckinKanbanView = KanbanView.extend({
        config: _.extend({}, KanbanView.prototype.config, {
            Controller: HotelCheckinKanbanController,
            Renderer: HotelCheckinKanbanRenderer,
            Model: HotelCheckinKanbanModel,
        }),

        /**
         * @override
         */
        init: function (viewInfo, params) {
            this._super.apply(this, arguments);
            var options = viewInfo.arch.attrs.options ? JSON.parse(viewInfo.arch.attrs.options) : {};
            this.hasSearchBar = 'search_bar' in options ? options.search_bar : true;
            this.resortName = options.resort_name || _t("Beach Vista Resort");
        },

        /**
         * @override
         */
        getController: function (parent) {
            return this._super.apply(this, arguments).then(function (controller) {
                // Set custom controller properties if needed
                return controller;
            });
        },

        /**
         * @override
         */
        getRenderer: function (parent, state) {
            return this._super.apply(this, arguments).then(function (renderer) {
                renderer.hasSearchBar = this.hasSearchBar;
                renderer.resortName = this.resortName;
                return renderer;
            }.bind(this));
        },
    });

    // Register the custom kanban view
    viewRegistry.add('hotel_checkin_kanban', HotelCheckinKanbanView);

    return {
        Controller: HotelCheckinKanbanController,
        Renderer: HotelCheckinKanbanRenderer,
        Model: HotelCheckinKanbanModel,
        View: HotelCheckinKanbanView,
    };
}); 