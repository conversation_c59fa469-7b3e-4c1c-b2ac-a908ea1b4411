<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_next_gen_x1" model="ir.ui.view">
        <field name="name">account.move.form.x1</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='price_unit']" position="after">
                <field name="price_unit_after_discount" groups="cubes_next_generation_customs.group_detailed_discount_sales" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='discount']" position="after">
                <field name="original_total_amount" groups="cubes_next_generation_customs.group_detailed_discount_sales" optional="hide"/>
            </xpath>
              <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='product_id']" position="after">
                <field name="barcode"/>
            </xpath>
            <xpath expr="//field[@name='ref']" position="before">
                <field name="source_so_id" attrs="{'invisible':[('source_so_id','=',False)]}"/>
            </xpath>
             <xpath expr="//field[@name='partner_id']" position="after">
                <field name="partner_balance"/>
            </xpath>
        </field>
    </record>
</odoo>
