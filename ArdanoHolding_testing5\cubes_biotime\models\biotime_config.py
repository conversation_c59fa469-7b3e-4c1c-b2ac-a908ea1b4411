# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import requests, json
from datetime import datetime, timedelta
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

_logger = logging.getLogger(__name__)

class BioTime(models.Model):
    _name = 'biotime.config'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'BioTime Configuration'

    name = fields.Char(string="Name", tracking=True)
    server_url = fields.Char(string="Server URL", default="http://**************:8081", tracking=True)
    username = fields.Char(string="Username", default="admin", tracking=True)
    password = fields.Char(string="Password", default="saddam@1990")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id, tracking=True)
    active = fields.Boolean(string="Active", default=True, tracking=True,
                           help="If unchecked, this Biotime server will not be synchronized by the scheduled actions")

    pull_from_date = fields.Datetime('Pull From Date')
    pull_to_date = fields.Datetime('Pull To Date')
    
    # Track last sync
    last_sync_time = fields.Datetime('Last Successful Sync', tracking=True)
    last_sync_status = fields.Selection([
        ('success', 'Success'),
        ('partial', 'Partial Success'),
        ('failed', 'Failed')
    ], string="Last Sync Status", default=False, tracking=True)
    last_sync_message = fields.Text('Last Sync Result', tracking=True)
    last_sync_punch_count = fields.Integer('Punches Retrieved Last Sync', default=0, tracking=True)

    # Attendance Processing Rules
    missing_checkout_handling = fields.Selection([
        ('ignore', 'Ignore (Leave as Draft)'),
        ('auto_close', 'Auto-close at End of Day'),
        ('next_checkin', 'Close at Next Check-in'),
        ('default_hours', 'Use Default Hours')
    ], string="Missing Check-out Handling", default='ignore', 
       help="How to handle punches with missing check-outs")
       
    missing_checkin_handling = fields.Selection([
        ('ignore', 'Ignore (Leave as Draft)'),
        ('auto_open', 'Auto-open at Punch Time'),
        ('start_of_day', 'Use Start of Day')
    ], string="Missing Check-in Handling", default='auto_open',
       help="How to handle punches with missing check-ins")
    
    default_work_hours_from = fields.Float(string="Default Work Hours From", 
                                      default=8.0,
                                      help="Default start time (24h format) to use when auto-creating missing check-ins")
                                      
    default_work_hours_to = fields.Float(string="Default Work Hours To", 
                                    default=17.0,
                                    help="Default end time (24h format) to use when auto-creating missing check-outs")
                                    
    work_hours_tolerance = fields.Float(string="Work Hours Tolerance (minutes)", 
                                    default=5.0,
                                    help="Tolerance in minutes when comparing punch times to default hours")

    # Work Schedule Source Setting
    use_employee_contracts = fields.Boolean(
        string="Use Employee Contracts",
        default=False,
        help="If enabled, the system will use work schedules from employee contracts instead of default hours when available"
    )

    # Night Shift Handling
    handle_night_shifts = fields.Boolean(
        string="Handle Night Shifts", 
        default=False,
        help="Enable special handling for night shifts that span across midnight"
    )
    
    night_shift_hours = fields.Float(
        string="Night Shift Duration (hours)",
        default=8.0,
        help="Maximum duration of night shift in hours (used for detecting shifts spanning midnight)"
    )
    
    night_shift_span_days = fields.Integer(
        string="Night Shift Max Days Span",
        default=1,
        help="Maximum number of days a night shift can span (typically 1 for overnight shifts)"
    )

    def action_get_all_terminals(self):
        """Get all terminals from Biotime"""
        for rec in self:
            terminal_env = self.env['biotime.terminal'].sudo()
            url = "%s/iclock/api/terminals/" % rec.server_url

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            page = 1
            while True:
                response = requests.request("GET", f"{url}?page={page}", headers=headers, data=payload)
                data = response.json()

                if data.get('data'):
                    for terminal in data['data']:
                        check = terminal_env.search([
                            ('biotime_id', '=', rec.id),
                            ('terminal_sn', '=', terminal.get('sn')),
                        ])
                        if not check:
                            terminal_env.create({
                                'name': terminal.get('terminal_name') if terminal.get(
                                    'terminal_name') else 'New Device',
                                'terminal_id': terminal.get('id'),
                                'terminal_sn': terminal.get('sn'),
                                'ip_address': terminal.get('ip_address'),
                                'alias': terminal.get('alias'),
                                'terminal_tz': terminal.get('terminal_tz'),
                                'biotime_id': rec.id
                            })

                    if 'next' in data and data['next']:
                        page += 1
                    else:
                        break
                else:
                    break

    def action_get_all_employees(self, page=1):
        """Get all employees from Biotime"""
        for rec in self:
            employee_env = self.env['biotime.employee'].sudo()
            url = "%s/personnel/api/employees/?page=%s" % (rec.server_url, page)

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            response = requests.request("GET", url, headers=headers, data=payload)
            data = response.json()
            if data.get('data'):
                for employee in data['data']:
                    check = employee_env.search([
                        ('biotime_id', '=', rec.id),
                        ('employee_id', '=', employee.get('id')),
                    ])
                    if not check:
                        employee_env.create({
                            'name': employee.get('first_name'),
                            'employee_id': employee.get('id'),
                            'emp_code': employee.get('emp_code'),
                            'biotime_id': rec.id
                        })
                    else:
                        check.emp_code = employee.get('emp_code')
                        check.employee_id = employee.get('id')
            if data.get('next'):
                self.action_get_all_employees(page=data.get('next').split('page=')[1])

    def action_pull_specific_dates(self):
        """Pull attendance for specific dates"""
        self.ensure_one()
        if not self.pull_from_date or not self.pull_to_date:
            raise ValidationError(_("Please set both From and To dates"))

        # Process one day at a time to avoid timeouts
        current_date = self.pull_from_date
        while current_date <= self.pull_to_date:
            try:
                _logger.info(f"Processing date: {current_date.date()}")
                self.action_get_today_attendance(
                    from_date=current_date,
                    to_date=current_date + timedelta(days=1)
                )
                self.env.cr.commit()  # Commit after each successful day
                current_date += timedelta(days=1)
            except Exception as e:
                _logger.error(f"Error processing date {current_date.date()}: {str(e)}")
                self.env.cr.rollback()
                raise ValidationError(_(
                    "Error processing date %s: %s\nPlease try again.",
                    current_date.date(),
                    str(e)
                ))

    def generate_access_token(self):
        for rec in self:
            try:
                url = "%s/jwt-api-token-auth/" % rec.server_url
                
                session = requests.Session()
                retries = Retry(
                    total=5,
                    backoff_factor=1,
                    status_forcelist=[408, 429, 500, 502, 503, 504]
                )
                adapter = HTTPAdapter(max_retries=retries)
                session.mount("http://", adapter)
                session.mount("https://", adapter)

                response = session.post(
                    url,
                    headers={'Content-Type': 'application/json'},
                    json={
                        "username": rec.username,
                        "password": rec.password
                    },
                    timeout=(10, 30)  # (connect timeout, read timeout)
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    _logger.error(f"Token generation failed: Status {response.status_code}, Response: {response.text}")
                    raise ValidationError(_("Failed to generate access token."))

            except requests.exceptions.Timeout:
                _logger.error("Timeout while generating access token")
                raise ValidationError(_("Connection to Biotime server timed out. Please try again later."))
            except requests.exceptions.ConnectionError:
                _logger.error("Connection error while generating access token")
                raise ValidationError(_("Could not connect to Biotime server. Please check your network connection."))
            except Exception as e:
                _logger.error(f"Error generating access token: {str(e)}")
                raise ValidationError(_("Error generating access token: %s") % str(e))

    def action_get_today_attendance(self, from_date=False, to_date=False):
        for rec in self:
            total_punches = 0
            success_count = 0
            error_count = 0
            sync_message = ""
            sync_status = "success"
            
            if not from_date:
                # If no date provided, use last sync time or default to today starting at midnight
                if rec.last_sync_time:
                    from_date = rec.last_sync_time
                else:
                    from_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            if not to_date:
                to_date = datetime.now()
                
            # For logging
            _logger.info(f"Pulling attendance data from {from_date} to {to_date}")
            
            # Get all terminals for this configuration
            terminals = self.env['biotime.terminal'].sudo().search([('biotime_id', '=', rec.id)])
            terminal_count = len(terminals)
            
            if terminal_count == 0:
                _logger.warning("No terminals found for this configuration")
                rec.write({
                    'last_sync_status': 'failed',
                    'last_sync_message': "No terminals configured for this BioTime connection."
                })
                continue
            
            _logger.info(f"Found {terminal_count} terminals to process")
            
            # Process terminals in batches to avoid memory issues
            batch_size = 3  # Smaller batch size
            for i in range(0, terminal_count, batch_size):
                terminal_batch = terminals[i:i+batch_size]
                _logger.info(f"Processing terminal batch {i//batch_size + 1}/{(terminal_count+batch_size-1)//batch_size}")
                
                # Process this batch of terminals
                for terminal in terminal_batch:
                    try:
                        _logger.info(f"Processing terminal {terminal.name} (SN: {terminal.terminal_sn})")
                        batch_punches, batch_success, batch_errors = self._process_terminal_data(
                            terminal, from_date, to_date
                        )
                        
                        _logger.info(f"Terminal {terminal.terminal_sn} completed: {batch_punches} punches, {batch_success} successes, {batch_errors} errors")
                        
                        total_punches += batch_punches
                        success_count += batch_success
                        error_count += batch_errors
                        
                        if batch_errors > 0:
                            sync_status = "partial"
                            sync_message += f"Terminal {terminal.name}: {batch_punches} punches, {batch_errors} errors\n"
                        
                        # Commit after each terminal to save progress
                        self.env.cr.commit()
                        
                    except Exception as e:
                        _logger.error(f"Error processing terminal {terminal.terminal_sn}: {str(e)}")
                        error_count += 1
                        sync_status = "partial"
                        sync_message += f"Error processing terminal {terminal.name} ({terminal.terminal_sn}): {str(e)}\n"
                        # Continue with next terminal despite the error
            
            # Final commit to ensure all changes are saved
            self.env.cr.commit()
            
            # Update last sync information
            _logger.info(f"Sync completed: {total_punches} punches, {success_count} successes, {error_count} errors")
            
            if error_count > 0 and success_count == 0:
                rec.write({
                    'last_sync_status': 'failed',
                    'last_sync_message': f"Sync failed. No punches processed successfully. Errors: {error_count}\n{sync_message}"
                })
            elif total_punches > 0 or success_count > 0:
                rec.write({
                    'last_sync_time': to_date,
                    'last_sync_status': sync_status,
                    'last_sync_message': f"Retrieved {total_punches} punches. Processed: {success_count}, Errors: {error_count}\n{sync_message}",
                    'last_sync_punch_count': total_punches
                })
            else:
                rec.write({
                    'last_sync_time': to_date,
                    'last_sync_status': 'success',
                    'last_sync_message': "Sync completed. No new punches found.",
                    'last_sync_punch_count': 0
                })
    
    def _process_terminal_data(self, terminal, from_date, to_date):
        """Process attendance data for a single terminal with optimizations for large datasets"""
        _logger.info(f"Processing terminal: {terminal.terminal_sn}")
        page = 1
        punch_count = 0
        success_count = 0
        error_count = 0
        
        # Use smaller page sizes for better performance with large datasets
        page_size = 100
        max_pages_per_batch = 5
        
        # Process data in smaller page batches to avoid memory issues
        while True:
            all_transactions = []
            pages_in_this_batch = 0
            
            # Fetch a batch of pages
            while pages_in_this_batch < max_pages_per_batch:
                try:
                    # Add page_size parameter to API call
                    response = terminal.action_get_transactions(
                        page=page,
                        from_date=from_date,
                        to_date=to_date,
                        page_size=page_size
                    )
                    
                    if not response or not response.get('data'):
                        # No more data to fetch
                        break

                    current_records = response.get('data', [])
                    record_count = len(current_records)
                    _logger.info(f"Terminal {terminal.terminal_sn}: Retrieved {record_count} records from page {page}")
                    
                    if record_count == 0:
                        break
                        
                    # We won't check for duplicates within the response - API should handle this
                    all_transactions.extend(current_records)
                    
                    # Check if we've retrieved all data
                    if not response.get('next'):
                        # No more pages
                        break
                        
                    page += 1
                    pages_in_this_batch += 1
                    
                except Exception as e:
                    _logger.error(f"Error retrieving page {page} for terminal {terminal.terminal_sn}: {str(e)}")
                    error_count += 1
                    break
            
            # Process this batch of records
            if all_transactions:
                _logger.info(f"Processing {len(all_transactions)} records for terminal {terminal.terminal_sn}")
                # Process in smaller chunks to avoid timeout
                chunk_size = 50
                for i in range(0, len(all_transactions), chunk_size):
                    chunk = all_transactions[i:i+chunk_size]
                    _logger.info(f"Processing chunk {i//chunk_size + 1}/{(len(all_transactions)+chunk_size-1)//chunk_size}")
                    
                    for record in chunk:
                        try:
                            self._store_and_process_punch(record, terminal)
                            success_count += 1
                        except Exception as e:
                            _logger.error(f"Error processing record: {str(e)}")
                            error_count += 1
                    
                    # Update counts
                    punch_count += len(chunk)
                    
                    # Commit after each chunk to save progress
                    self.env.cr.commit()
            
            # If we didn't get a full batch of pages, we're done
            if pages_in_this_batch < max_pages_per_batch:
                break
        
        return punch_count, success_count, error_count

    def _store_and_process_punch(self, record, terminal):
        """Store raw punch data and process it if possible"""
        emp_code = record.get('emp_code')
        punch_time = record.get('punch_time') or '00:00:00'
        punch_state = record.get('punch_state', 'unknown')
        
        # Find the biotime employee - use direct SQL for faster query
        biotime_employee = self.env['biotime.employee'].sudo().search([
            ('emp_code', '=', emp_code),
            ('biotime_id', '=', self.id)
        ], limit=1)
        
        # Convert time string to datetime
        try:
            punch_datetime = datetime.strptime(punch_time, '%Y-%m-%d %H:%M:%S')
            # Apply timezone adjustment based on terminal settings
            tz_adjustment = terminal.terminal_tz and int(terminal.terminal_tz) or 0
            adjusted_time = punch_datetime - timedelta(hours=tz_adjustment)
            _logger.debug(f"Converted {punch_time} to {adjusted_time} (tz adj: {tz_adjustment})")
        except Exception as e:
            _logger.error(f"Error processing time {punch_time}: {str(e)}")
            adjusted_time = datetime.now()
        
        # If punch_state is unknown, try to determine it based on chronology
        if punch_state not in ['0', '1', '2', '3', '4', '5']:
            punch_state = self._determine_punch_state_by_chronology(
                biotime_employee, 
                adjusted_time
            )
        
        # Check if this punch already exists - use exists() for better performance
        existing_punch = self.env['biotime.punch'].sudo().search([
            ('emp_code', '=', emp_code),
            ('punch_time', '=', punch_time),
            ('terminal_sn', '=', terminal.terminal_sn)
        ], limit=1)
        
        # Create or update punch record
        if existing_punch:
            # Only update if needed
            if not existing_punch.processed:
                existing_punch.write({
                    'biotime_employee_id': biotime_employee.id if biotime_employee else False,
                    'employee_id': biotime_employee.odoo_employee_id.id if biotime_employee and biotime_employee.odoo_employee_id else False,
                    'punch_state': punch_state,
                    'verify_type': record.get('verify_type'),
                    'work_code': record.get('work_code'),
                })
            punch = existing_punch
        else:
            # Create new punch record
            punch_vals = {
                'biotime_employee_id': biotime_employee.id if biotime_employee else False,
                'employee_id': biotime_employee.odoo_employee_id.id if biotime_employee and biotime_employee.odoo_employee_id else False,
                'biotime_terminal_id': terminal.id,
                'emp_code': emp_code,
                'terminal_sn': terminal.terminal_sn,
                'punch_time': punch_time,
                'punch_datetime': adjusted_time,
                'punch_state': punch_state,
                'verify_type': record.get('verify_type'),
                'work_code': record.get('work_code'),
                'state': 'draft',  # Always start as draft
            }
            punch = self.env['biotime.punch'].sudo().create(punch_vals)
        
        # Process the punch if it has valid state and employee
        # Don't process if it's already processed
        if not punch.processed and punch.punch_state in ['0', '1'] and punch.employee_id:
            try:
                punch._process_punch()
                _logger.info(f"Processed punch for {emp_code} at {punch_time} as {punch_state}")
            except Exception as e:
                _logger.error(f"Error processing punch: {str(e)}")
        
        return punch
        
    def _determine_punch_state_by_chronology(self, biotime_employee, punch_datetime):
        """Determine punch state based on chronology"""
        if not biotime_employee or not biotime_employee.odoo_employee_id:
            return 'unknown'
            
        employee = biotime_employee.odoo_employee_id
        
        # Check if the employee has an open attendance
        open_attendance = self.env['hr.attendance'].sudo().search([
            ('employee_id', '=', employee.id),
            ('check_in', '!=', False),
            ('check_out', '=', False)
        ], limit=1)
        
        # If employee has an open attendance, this should be a check-out
        if open_attendance:
            # Verify this punch is after the check-in
            if open_attendance.check_in < punch_datetime:
                return '1'  # Check Out
            else:
                # This is unusual - the punch is before the check-in
                return 'unknown'
        else:
            # No open attendance, should be a check-in
            
            # Get the last attendance to verify chronology
            last_attendance = self.env['hr.attendance'].sudo().search([
                ('employee_id', '=', employee.id),
            ], limit=1, order='check_in DESC')
            
            if not last_attendance:
                # No previous attendance, must be first check-in
                return '0'  # Check In
            
            # Check if this punch is after the last check-out
            if last_attendance.check_out and last_attendance.check_out < punch_datetime:
                return '0'  # Check In
            else:
                # This could be a missing check-out from previous day
                # Let's check if it's a new day
                last_date = last_attendance.check_in.date()
                punch_date = punch_datetime.date()
                
                if punch_date > last_date:
                    return '0'  # New day, treat as Check In
                
                # If we get here, the chronology is unclear
                return 'unknown'


