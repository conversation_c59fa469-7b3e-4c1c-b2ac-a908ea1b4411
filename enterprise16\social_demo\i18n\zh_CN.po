# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_demo
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_youtube_account
msgid "Channel: My Company"
msgstr "频道: My Company"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4_product_template
msgid "Desk"
msgstr "桌子"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4b_product_template
msgid "Desk Variant B"
msgstr "书桌变体B"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4c_product_template
msgid "Desk Variant C"
msgstr "办公桌变体C"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4d_product_template
msgid "Desk Variant D"
msgstr "桌子变体D"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_instagram_account
msgid "Instagram Posts: My Company"
msgstr "Instagram帖子: My Company"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search
msgid "Keyword: #mycompany"
msgstr "关键字: My Company"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search_competitor
msgid "Keyword: #mycompetitor"
msgstr "关键字: #mycompetitor"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_facebook_page
msgid "Page Posts: My Company"
msgstr "帖子页面: #mycompetitor"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_linkedin_page
msgid "Posts: My Company"
msgstr "帖子: My Company"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_account
msgid "Social Account"
msgstr "社交帐户"

#. module: social_demo
#: model:utm.campaign,title:social_demo.social_utm_campaign
msgid "Social Campaign"
msgstr "社会营销"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_live_post
msgid "Social Live Post"
msgstr "社区在线提交"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_post
msgid "Social Post"
msgstr "社会职务"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream
msgid "Social Stream"
msgstr "社交圈"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream_post
msgid "Social Stream Post"
msgstr "社交流媒体帖"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_account
msgid "Tweets of: My Company"
msgstr "推文: My Company"

#. module: social_demo
#: model:ir.model,name:social_demo.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM营销"

#. module: social_demo
#. odoo-javascript
#: code:addons/social_demo/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "You cannot upload videos in demo mode."
msgstr "您不能在演示模式里上传视频。"
