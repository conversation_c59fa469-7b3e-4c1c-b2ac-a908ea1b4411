# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children < 19"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_children
msgid "# Insured Children < 19 y/o"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children >= 19"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults
msgid "# Insured Children >= 19 y/o"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__months_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__months_count
msgid "# Months"
msgstr "# Meses"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid "# dependent children for salary attachement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_juniors_dependent
msgid "# disabled people (<65)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_senior_dependent
msgid "# disabled seniors (>=65)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid "# people (<65)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid "# seniors (>=65)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "%s %s quarter %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "%s - %s"
msgstr "%s - %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "%s - Part Time %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_leave.py:0
#, python-format
msgid "%s is in %s. Fill in the appropriate eDRS here: %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#, python-format
msgid "%s-individual-account-%s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "'Holiday Attest (Year N) - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "'Holiday Attest (Year N-1) - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_double_holiday_13th_month
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Recibo de nómina - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_fees
msgid "'Termination - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_individual_account
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Compensation granted to insurance inspectors in reimbursement of costs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Individual agreement available"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Km"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Mention: MOBILITY ALLOWANCE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Options granted by a foreign company not having an establishment in "
"Belgium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Social Security package"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Total compensation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* nature"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* number of days out of border area"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* percentage(s)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* specific remunerations paid in 2021 for services from the first to the "
"third quarters of 2021 inclusive and / or for services provided during the "
"second and fourth quarters of 2020"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* total amount of all remuneration paid under a student employment contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "*10€ + 1*"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid ", bank account of"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "- Test"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"- Without Income: The spouse of the income recipient has no professional income.\n"
"\n"
"- High income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which exceeds %s€ net per month.\n"
"\n"
"- Low Income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which does not exceed %s€ net per month.\n"
"\n"
"- Low Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which does not exceed %s€ net per month.\n"
"\n"
"- High Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which exceeds %s€ net per month."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- actually provided from 07/01/2021"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- actually provided until 30.06.2021 inclusive"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.01.2021 and 30.09.2021 included with "
"employers belonging to crucial sectors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.07.2021 and 31.12.2021 as part of the "
"recovery plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements based on serious standards"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements in the absence of serious standards"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- mobility allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 01.01.2021 to 30.09.2021 inclusive with employers belonging "
"to crucial sectors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 07/01/2021 to 12/31/2021 included as part of the recovery "
"plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- reimbursements based on supporting documents"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
".\n"
"                            <span>Your next employer or payment agency will not pay for these days off. So you are advised to keep this amount\n"
"                            until you take these days off.</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0,87% of gross reference remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "1 day is 7 hours and 36 minutes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid ""
"1 day is the number of the amount of hours per day in the working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1) Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "10-digit code given by ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1001"
msgstr "1001"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1002"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1003"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1011"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1012"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1013"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "102 : Staff Costs :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "103 : Benefits Above Salary :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "105"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "110"
msgstr "110"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "111"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "112"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "113"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__12mo
msgid "12 months +"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "120"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1200"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1201"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1202"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1203"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "121"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1210"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1211"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1212"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1213"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "130"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "132"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "133"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "134"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_thirteen_month
msgid "13th Month Slip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__1
msgid "1st"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Awarded in 2021"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Compensation, not mentioned in 2°, 3°, 4°"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Ordinary remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° which count for the limit up to 180 hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) overtime worked in 2020 but paid in 2021"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "205"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "210"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "211"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "212"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "213"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "233"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "234"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "240"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "242"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "243"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "247"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "250"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "251"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "252"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "254"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "263"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "267"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "271"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "273"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "273S"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_ip_273S
msgid "273S PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_273S_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_273s
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_273S
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "273S Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_tree
msgid "273S Sheets"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "274"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_274_10
msgid "274.10 PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10
msgid "274.10 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_274_XX_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_274_XX
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_tree
msgid "274.XX Sheets"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx_line
msgid "274.XX Sheets Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "275"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "276"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "277"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "278"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "279"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "280"
msgstr "280"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_action_view_tree
msgid "281.10 Forms"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_10
msgid "281.10 PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_10
msgid "281.10 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "281.10 Sheet: Income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_action_view_tree
msgid "281.45 Forms"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_45
msgid "281.45 PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_45
msgid "281.45 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "281.45 Sheet - Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "283"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "284"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "285"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "286"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "287"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "290"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "2a. Income recipient"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__2
msgid "2nd"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Actions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Awarded before 2021"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "3-digit code given by ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "3. Name and address of income debtor:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "305"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "306"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "307"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "308"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "310"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "311"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "312"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "313"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "317"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "335"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "336"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "337"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "338"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "340"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "341"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "342"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "343"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "387"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "395"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "396"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "397"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "398"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__3
msgid "3rd"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° Bonuses, premiums and stock options"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° which are taken into account for the limit up to 360 hours (*)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "4. Gross amount of income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__4
msgid "4th"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "4° Benefits in kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "5. Deducted fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__55yo
msgid "55+ years old"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "57.75 %"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5801"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5802"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5803"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58031"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58032"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58033"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5811"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5812"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5813"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58131"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58132"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58133"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5821"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5822"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5823"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5831"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5832"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5833"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5841"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5842"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5843"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5851"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5852"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5853"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "6,8% of gross reference remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6,8% of gross reference remuneration - additional time off amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "6. Amount of retained withholding tax"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "66.81 %"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "7,67% of gross reference remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"7,67% of gross reference remuneration * (time off not taken) / (right to "
"time off)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "865"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "870"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "9-digit code given by ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "9998"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__25yo
msgid "< 25 years old"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Contributions Summary :</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Deductions Summary :</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Remunerations Summary :</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Services Summary :</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Student Contributions Summary :</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"<span attrs=\"{'invisible': "
"[('notice_duration_month_before_2014','=',0)]}\"> months and </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">Company number: </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">NISS: </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"fw-bold\">Nature of beneficiary: </span>\n"
"                                    <span>Natural Person</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"ms-3\" attrs=\"{'invisible': [('wage_type', '=', "
"'hourly')]}\">%</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\"> / month</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/ mes</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ worked day</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/ año</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('transport_mode_private_car', '=', False)]}\">Reimboursed amount</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" "
"attrs=\"{'invisible': [('transport_mode_private_car', '=', "
"False)]}\">Distance home-work</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Company Information</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">ONSS</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">SDWorx</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">From</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">To</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">10. Taxable at the rate of"
" 33% :</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">11. Remuneration obtained "
"by athletes within the framework of their sporting activity :</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">12. Remuneration obtained "
"by sports competition referees for their refereeing services, or by "
"trainers, coaches and accompanying persons for their activity for the "
"benefit of sportsmen :</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">13. PC Privé</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">14. Contribution to travel"
" costs :</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">15. Impulse Fund</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">16. Deductions for "
"supplementary pensions</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">17. Remuneration for "
"overtime in the hospitality industry which qualifies for the "
"exemption</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">18. Overtime which gives "
"the right to extra pay</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">19. Remuneration which is "
"taken into account for the exemption for voluntary overtime</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">20. Withholding "
"Taxes</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">21. Special contributions "
"for social security</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">22. Public sector staff "
"without an employment contract</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">23. Employment "
"bonus</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">24. Miscellaneous "
"information</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">25. Remuneration and other"
" benefits received from a related foreign company</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">26. Foreign seasonal "
"worker in agriculture and horticulture subject to professional withholding "
"tax</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">27. Foreign executive or "
"researcher</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">6. REMUNERATION (other "
"than referred to under 10, 11a and 12a)</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">7. Miscellaneous taxable "
"income</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">8. Bad weather "
"stamps</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">9. Non-recurring benefits "
"linked to results</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Nature des revenus</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte Professionnel "
"Retenu</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"dû</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables "
"répondant aux conditions d’application de la dispense</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span> included:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "<span> weeks</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> years old</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ mes</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>/Holiday year </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Actual and similar remuneration for the period from </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Attendance (hours)</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Holiday exercise </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Individual Account Report for year </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>No</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>Social contributions on the various vacation pay have already been "
"paid.</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take in the\n"
"                            near future in </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take in the near\n"
"                            future in </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>Yes</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>You must return this certificate to your next employer, or failing that, to your allowance payment agency.\n"
"                            Social security contributions on holiday pay have already been retained.</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>days</span>"
msgstr "<span>días</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<span>included:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>km</span>"
msgstr "<span>km</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>to </span>"
msgstr "<span>a </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / month</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / year</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "<span>€</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid ""
"<strong class=\"o_group_col_12\" attrs=\"{'invisible': ['|', ('leave_type_id', '=', False), ('found_leave_allocation', '=', True)]}\" style=\"color:#ff6600;\">\n"
"                        No time off allocation has been found for this time off type, no changes will occur to time off for this employee.\n"
"                    </strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\" "
"id=\"previous_employer_title\" attrs=\"{'invisible': "
"[('company_country_code', '!=', 'BE')]}\" "
"groups=\"hr_payroll.group_hr_payroll_user\">Previous Employer</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\">Previous "
"Occupations</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong> Departure Date: </strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>1. Nr.</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<strong>1. N° :</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>2. Date of entry: </strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>3. Income debtor:</strong> <br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Beneficiary:</strong><br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>5. National number:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Address</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Amount</strong>"
msgstr "<strong>Importe</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Au:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Authorized signature</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Bank Account</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Basic Salary</strong>"
msgstr "<strong>Salario base</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Code</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Company Information</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Computed on </strong>"
msgstr "<strong>Calculado en </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Contract Start Date</strong>"
msgstr "<strong>Fecha de inicio del contrato</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Contract Type </strong>"
msgstr "<strong>Tipo de contrato </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Designation</strong>"
msgstr "<strong>Designación</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Du:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong>Eco-Vouchers Amount</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Email</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Employee</strong>"
msgstr "<strong>Empleado</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Identification No</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Identification</strong>"
msgstr "<strong>Identificación</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Marital Status</strong>"
msgstr "<strong>Estado civil</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Montant</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Name</strong>"
msgstr "<strong>Nombre</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Nr.</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Pay Period</strong>"
msgstr "<strong>Periodo de pago</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double complementary</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<strong>Pay holiday double</strong> only if the majority of vacation days\n"
"                                have not yet been taken"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Pay simple</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Person in charge</strong>"
msgstr "<strong>Persona encargada</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Reference</strong>"
msgstr "<strong>Referencia</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Registration Number</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Salary Computation</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Société:</strong> <br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Start notice period</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>TOTAL</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Days</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Time</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Working Schedule</strong>"
msgstr "<strong>Horario de trabajo</strong>"

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa_location_unit__unique
msgid ""
"A DMFA location cannot be set more than once for the same company and "
"partner."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "A benefit in kind is paid when the employee uses its laptop at home."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "A. Packages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "A. Total: (6a + 6b + 6c + 6d, 1° + 6d, 2°)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "APR"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_advantage_any_kind
msgid "ATN"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_inverse_atn_warrant
msgid "ATN Warrant"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "AUG"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid "Absence Work Entry Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_ONSS
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_onss_employer
msgid "Accounting: ONSS (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_basic
msgid "Accounting: ONSS Basic (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_cpae
msgid "Accounting: ONSS CPAE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_ffe
msgid "Accounting: ONSS FFE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_special_ffe
msgid "Accounting: ONSS Special FFE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_temporary_unemployment
msgid "Accounting: ONSS Temporary Unemployment (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_wage_restreint
msgid "Accounting: ONSS Wage Restreint (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_owed_remuneration
msgid "Accounting: Owed Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_remuneration
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_remuneration
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_remuneration
msgid "Accounting: Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked full time"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked part-time"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid ""
"Adapted salary, according to the sacrifices defined on the contract "
"(Example: Extra-legal time off, a percentage of the salary invested in a "
"group insurance, etc...)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__2
msgid "Add"
msgstr "Agregar"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_additional_gross
msgid "Additional Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Additional Information"
msgstr "Información adicional"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_additional_paid
msgid "Additional Time (Paid)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_additional_leave
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_additional_leave
msgid "Additional Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional amount to deduct"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional time off (european, ...)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Administration générale de la Fiscalité"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Adresse e-mail :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Advantages"
msgstr "Ventajas"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_after_contract_public_holiday
msgid "After Contract Public Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_after_contract_public_holiday
#, python-format
msgid "After Contract Public Holidays"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__employee_age
msgid "Age of Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__aggregation_level
msgid "Aggregation Level"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__alloc_employee_ids
msgid "Alloc Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__alloc_paid_leave_id
msgid "Alloc Paid Leave"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Allocation Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocation_n_ids
msgid "Allocations N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_onss_restructuring
msgid "Allow ONSS Reduction for Restructuring"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Ambulatory Insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_notes
msgid "Ambulatory Insurance: Additional Info"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_children
msgid "Ambulatory: # Insured Children < 19"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults
msgid "Ambulatory: # Insured Children >= 19"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_adult
msgid "Ambulatory: Amount per Adult"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_child
msgid "Ambulatory: Amount per Child"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_amount
msgid "Ambulatory: Insurance Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_spouse
msgid "Ambulatory: Insured Spouse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Amount"
msgstr "Importe"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount of the employer's intervention"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid ""
"Amount of the holiday pay paid by the previous employer already recovered."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Amount of the holiday pay paid by the previous employer to recover."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_adult
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Adult"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_child
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Child"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
msgid ""
"Amount the employee receives in the form of meal vouchers per worked day."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Amounts to Recover"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_salary_revalued
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination
msgid "Annual salary revalued"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_variable_salary
msgid "Annual variable salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Another reason"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Anticipated Holiday Pay Retenue"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__4
msgid "April"
msgstr "Abril"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_asignment_salary
msgid "Assignment of Salary"
msgstr "Asignación del salario"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "At the end of the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_attachment_salary
msgid "Attachment of Salary"
msgstr "Embargo de salario"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__8
msgid "August"
msgstr "Agosto"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of full-time workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of part-time workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of total workers or FTEs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
msgid "Average remuneration by month current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration by month previous year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration for the 12 months preceding unpaid leave"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "B. Real"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Bachelors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_basic
msgid "Basic Complementary Double Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_basic_pay_simple
msgid "Basic Pay Simple"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic
msgid "Basic Salary"
msgstr "Salario básico total"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_basic
msgid "Basic double"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_freelance
msgid "Became Freelance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Belgian Localization"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.server,name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation_ir_actions_server
#: model:ir.cron,cron_name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation
msgid "Belgian Payroll: Update time off allocations on schedule change"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_configuration
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_reporting_l10n_be
msgid "Belgium"
msgstr "Bélgica"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Beneficiary holdings"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__benefit_name
msgid "Benefit Name"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_company_car_annual
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car_2
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Benefit in Kind (Company Car)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet_2
msgid "Benefit in Kind (Internet)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop_2
msgid "Benefit in Kind (Laptop)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile_2
msgid "Benefit in Kind (Phone Subscription)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_atn_deduction
msgid "Benefit in Kind Deductions (All)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefit in kind deduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind (Company Car)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Benefits in kind and bonuses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind submitted to ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind without ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__has_bicycle
msgid "Bicycle to work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_breast_feeding
msgid "Breastfeeding Break"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Business closure fund cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__department
msgid "By Department"
msgstr "Por departamento"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__employee
msgid "By Employee"
msgstr "Por empleado"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By contract type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By gender"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By professional category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By reason for termination of contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "CANCEL"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdd
msgid "CDD"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdi
msgid "CDI"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cip
msgid "CIP"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_job_view_form
msgid "CP200 Category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_december_slip_wizard
msgid "CP200: December Slip Computation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line_wizard
msgid "CP200: Double Pay Recovery Line Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_wizard
msgid "CP200: Double Pay Recovery Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"CSV format: you may edit it directly with your favorite spreadsheet "
"software, the rightmost column (value) contains the commission value over 3 "
"months. When you're done, reimport the file to generate the commission "
"payslips with the accurate commissions."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre I. - Calcul du Précompte Mobilier (Pr.M) à payer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre II. - Bénéficiaire(s) des revenus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Calculation Basis"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__3
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_canteen
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_canteen_cost
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_l10n_be_canteen_cost
msgid "Canteen Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Canteen Costs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__capped_amount_34
msgid "Capped Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Car BIK"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__a
msgid "Category A"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid ""
"Category A - Executive functions:\n"
"Included in this class are functions characterized by performing a limited number of simple and repetitive tasks. For example: the worker exclusively responsible for typing.\n"
"\n"
"Category B - Support functions.\n"
"Included in this class are functions characterized by making a contribution to the achievement of a larger mission. For example: the administrative employee or the receptionist.\n"
"\n"
"Category C - Management functions.\n"
"Included in this class are functions characterized by carrying out a complete set of tasks which, together, constitute one and the same mission. For example: the personnel administration employee or the PC technician.\n"
"\n"
"Category D - Advisory functions.\n"
"Included in this class are functions characterized by monitoring and developing the same professional process within the framework of a specific objective. For example: the programmer, accountant or consultant"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__b
msgid "Category B"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__c
msgid "Category C"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__d
msgid "Category D"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__certificate
msgid "Certificate Level"
msgstr "Nivel de certificado"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"Certificate Relating To Annual Holidays For The Civil Year Ending The "
"Contract Of Employment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"Certificate Relating To Annual Vacations For The Calendar Year Preceding The"
" End Of The Contract Of Employment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_pem_certificate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_pem_passphrase
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_pem_certificate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_pem_passphrase
msgid "Certificate to allow access to batch declarations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_wizard_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard
msgid "Change Employee Language"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard_line
msgid "Change Employee Language Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_schedule_change_wizard
msgid "Change contract working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Checkout"
msgstr "Finalizar compra"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_child_alw
msgid "Child Allowance Belgium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_child_support
msgid "Child Support"
msgstr "Subsidio familiar por hijos"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Circ. administrative 19.03.1982 Ci. RH. 241/315.785"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__t
msgid "Circuit Test (T)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__classic_holiday_pay
msgid "Classic Holiday Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Close"
msgstr "Cerrar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__code
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Code"
msgstr "Código"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_fixed_commission
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid "Commission"
msgstr "Comisión"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__commission_amount
msgid "Commission Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_commission_on_target
msgid "Commission on Target"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions
msgid "Commissions"
msgstr "Comisiones"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions_adjustment
msgid "Commissions Adjustment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__company_id
msgid "Company"
msgstr "Compañía"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__company_car_total_depreciated_cost
msgid "Company Car Total Depreciated Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_company_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_company_number
msgid "Company Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_already_paid
msgid "Complementary Double Holidays (Already Paid)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_december
msgid "Complementary Double Holidays (Lost due to working time reduction)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute December Holiday Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute Double Pay Recovery"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"Computed value base on leaving type, arrival of the employee in the company,"
" ..."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure Default Values for Belgian Advantages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure ONSS codes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_children
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_children
msgid "Considered number of dependent children"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_juniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_juniors
msgid "Considered number of dependent juniors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_seniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_seniors
msgid "Considered number of dependent seniors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__contract_id
msgid "Contract"
msgstr "Contrato"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__contract_next_year_id
msgid "Contract Active Next Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Informe de análisis de contratos y empleados"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contract for the execution of a clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Historial de contratos"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contributions paid and payments to collective funds"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_corona
msgid "Corona Unemployment"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"Count of dependent people/children or disabled dependent people/children "
"must be positive."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"Count of disabled dependent people/children must be less or equal to the "
"number of dependent people/children."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Create 274.XX Sheets"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_10_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Create 281.10 Form"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_45_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create 281.45 Form"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Create PDFs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create XML"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Create new contract and adapt time off allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_credit_time
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_credit_time
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "Tiempo de crédito"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "Tiempo de crédito"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Credit time contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Currency :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count_description
msgid "Current Occupation Duration (Description)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count
msgid "Current Occupation Duration (Months)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__current_resource_calendar_id
msgid "Current Resource Calendar"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Current Working Schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Current contract is finished before the end of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Cycle Allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_cycle_transportation
msgid "Cycle Transportation (Days Count)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "DEC"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.hr_payslip_report_action_dmfa
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_dmfa
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "DMFA"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "DMFA Employer Class"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_tree
msgid "DMFA Reports"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "DMFA code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa
msgid "DMFA xml report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_dmfa_location_unit
msgid "DMFA: Work Locations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Data for:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Date"
msgstr "Fecha"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_start
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_from
msgid "Date From"
msgstr "Fecha desde"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_end
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_to
msgid "Date To"
msgstr "Fecha hasta"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Date de réception de la déclaration :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__day
msgid "Day"
msgstr "Día"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__private_car_missing_days
msgid "Days Not Granting Private Car Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__representation_fees_missing_days
msgid "Days Not Granting Representation Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Days Per Week :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Days Unpaid time off current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Days Unpaid time off previous year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__12
msgid "December"
msgstr "Diciembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Slip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid ""
"Decides whether the employee will still work during his notice period or "
"not."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__s
msgid "Declaration Test (S)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__declaration_type
msgid "Declaration Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_32
msgid "Deducted Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_33
msgid "Deducted Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_34
msgid "Deducted Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_deduction
msgid "Deduction"
msgstr "Deducción"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Tipo de entrada de trabajo por defecto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_calendar
msgid "Default Working Hours"
msgstr "Horas laborables por defecto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__department_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__department_id
msgid "Department"
msgstr "Departamento"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_description
msgid "Departure Description"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_departure_reason
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__leaving_type_id
msgid "Departure Reason"
msgstr "Motivo de salida"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_holiday_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_holiday_attests
msgid "Departure: Holiday Attests"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_notice
msgid "Departure: Notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_notice_wizard_action
msgid "Departure: Notice period and payslip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Departures"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__name
msgid "Description"
msgstr "Descripción"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled
msgid "Disabled"
msgstr "Deshabilitado"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "Disabled Children"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "Disabled Spouse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Discard"
msgstr "Descartar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Dismissal"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__display_l10n_be_scale
msgid "Display L10N Be Scale"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_dmfa
msgid "DmfA"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "DmfA Declaration ("
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go_filename
msgid "Dmfa Go Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf_filename
msgid "Dmfa Pdf Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature_filename
msgid "Dmfa Signature Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml_filename
msgid "Dmfa Xml Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Doctors / Civil Engineers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Domicile, siège social ou siège du principal établissement administratif "
"(adresse complète) :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__done
msgid "Done"
msgstr "Hecho"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_december_pay
msgid "Double December Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_basic
msgid "Double December Pay Basic"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_gross
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category_gross
msgid "Double December Pay Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_net
msgid "Double December Pay Net"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_pp
msgid "Double December Pay Withholding Tax"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_already_paid
msgid "Double Holiday (Already Paid)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_dp
msgid "Double Holiday Pay"
msgstr "Paga extra de verano"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_pay_december
msgid "Double Holiday Pay (Lost due to working time reduction)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Amount:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n
msgid "Double Holiday Pay N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n1
msgid "Double Holiday Pay N-1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_recovery
msgid "Double Holiday Pay Recovery"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_double_holiday
msgid "Double Holidays Slip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_december_slip_wizard_action
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Double Pay Recovery Computation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__double_pay_to_recover
msgid "Double Pay To Recover"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Download the 273S PDF file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the 274.XX PDF file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Download the 281.10 XML file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Download the 281.45 XML file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet PDF file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate PDF file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XLSX details file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XML Export file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "During the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Early holiday pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_eco_checks
msgid "Eco Vouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_eco_vouchers_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#, python-format
msgid "Eco-Vouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_line_wizard
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_wizard
msgid "Eco-Vouchers Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_economic_unemployment
msgid "Economic Unemployment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training_time_off
msgid "Educational Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__effective_date
msgid "Effective Date"
msgstr "Fecha efectiva"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "Employee"
msgstr "Empleado"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employee :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato del empleado"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Employee Departure"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Employee Departure - Holiday Attests"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_reimbursement
msgid "Employee Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__without
msgid "Employee doesn't work during his notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__with
msgid "Employee works during his notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "Employee's monthly gross wage for the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Employee's working schedule."
msgstr "Horario de trabajo del empleado."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employees"
msgstr "Empleados"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employees Information"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid Configured Gender"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid Configured Language"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid NISS Numbers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employer Class :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employer contribution to the Fund"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__commercial
msgid "Employers with industrial or commercial purposes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__non_commercial
msgid "Employers without industrial or commercial purposes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_employees
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_employment_bonus
msgid "Employment Bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End Date"
msgstr "Fecha de finalización"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_end
msgid "End Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End date of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__end_notice_period
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__end_notice_period
msgid "End notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "End-of-year bonus, 13th month or other similar amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Entries"
msgstr "Entradas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__error_message
msgid "Error Message"
msgstr "Mensaje de error"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "Error while importing file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Established on"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Established on :"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_european_leaves_deduction
#, python-format
msgid "European Leaves Deduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_european
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_european
msgid "European Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Exempted Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_expatriate
msgid "Expatriate Allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid "Expense Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export"
msgstr "Exportar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export Complete"
msgstr "Exportación completada"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Export Employee Leaves for SD Worx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__export_file
msgid "Export File"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__export_filename
msgid "Export Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_export_sdworx_leaves_wizard
msgid "Export Leaves to SDWorx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Export PDF file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__group_export_sdworx_leaves
#: model:res.groups,name:l10n_be_hr_payroll.group_export_sdworx_leaves
msgid "Export Time Off to SDWorx"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_export_sdworx_leaves_wizard
#, python-format
msgid "Export Work Entries to SDWorx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Export XLS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XLSX details"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XML file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__export
msgid "Export the employees file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_extra_legal
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_extra_legal
msgid "Extra Legal Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "FEB"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "FINPROF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__2
msgid "February"
msgstr "Febrero"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Female"
msgstr "Femenino"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_ffe_employer_type
msgid "Ffe Employer Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.10: Précompte professionnel versé."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"Fiche 274.32: Dispense de précompte professionnel pour doctorants/ingénieurs"
" civils."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.33: Dispense de précompte professionnel pour master."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.34: Dispense de précompte professionnel pour bacheliers."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "Field Year does not seem to be a year. It must be an integer."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__file_type
msgid "File Type"
msgstr "Tipo del archivo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_in_company
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "First contract in company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntarism
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntarism
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Fiscal Voluntarism"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntary_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntary_rate
msgid "Fiscal Voluntary Rate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Fixed and variable remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Fixed term contract (CDD)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_flemish_training_time_off
msgid "Flemish Educational Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "For The Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Foreign Expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Formal continuous trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__found_leave_allocation
msgid "Found Leave Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déductibles"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déduits"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Frequency (Month)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "From"
msgstr "De"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_fuel_card
msgid "Fuel Card"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Full Time"
msgstr "Tiempo completo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fte
msgid "Full Time Equivalent (Today)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_wage
msgid "Full Time Equivalent Wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_time_off_allocation
msgid "Full Time Off Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_resource_calendar_id
msgid "Full Working Schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Fund or company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_gross
msgid "GROSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Generate Export File"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate PDF report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Generate Payslips"
msgstr "Generar recibos de nómina"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Generate Time Off Allocations"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-javascript
#: code:addons/l10n_be_hr_payroll/static/src/xml/payslip_batch_tree_view.xml:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.action_hr_payroll_generate_warrant_payslips
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#, python-format
msgid "Generate Warrant Payslips"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips_line
msgid "Generate Warrant Payslips Lines"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate XML report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Generar los recibos de nómina para todos los empleados seleccionados"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Generation Complete"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_273s
msgid "Get 273S report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_274_10
msgid "Get 274.10 report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_281_10
msgid "Get 281.10 report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_281_45
msgid "Get 281.45 report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_dmfa_pdf_report
msgid "Get DmfA declaration as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_balance
msgid "Get Social Balance Sheet as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_security_certificate
msgid "Get Social Security Certificate as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gifts in kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Global Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go
msgid "Go file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Grants and other financial benefits received (to be deducted)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_salary
msgid "Gross"
msgstr "Bruto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n
msgid "Gross Annual Remuneration Current Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n1
msgid "Gross Annual Remuneration Previous Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_december_2013
msgid "Gross Annual Salary as of December 31, 2013"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_salary
msgid "Gross Double December Pay Salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_gross_salary
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__gross_salary
msgid "Gross Salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic_12_92
msgid "Gross Yearly Salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__inferior
msgid "Gross annual salary < 32.254 €"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__superior
msgid "Gross annual salary > 32.254 €"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Gross cost directly linked to training"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Gross reference remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gross salary before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_group_insurance
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_group_insurance
msgid "Group Insurance (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_group_insurance_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Group Insurance Export"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Group Insurance Global Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
msgid "Group Insurance Sacrifice Rate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_wizard
msgid "Group Insurance Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_line_wizard
msgid "Group Insurance Wizard Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account
msgid "HR Individual Account Report By Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account_line
msgid "HR Individual Account Report By Employee Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10_line
msgid "HR Payroll 281.10 Line Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45_line
msgid "HR Payroll 281.45 Line Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Entrada de trabajo de RR. HH."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Tipo de entrada de trabajo de RR. HH."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_has_ambulatory_insurance
msgid "Has Ambulatory Insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_hospital_insurance
msgid "Has Hospital Insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid "Has Valid Schedule Change Contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_hiring_bonus
msgid "Hiring Bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n_holidays
msgid "Holiday Pay (N Year)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n1_holidays
msgid "Holiday Pay (N-1 Year)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Provision"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n
msgid "Holiday Pay Recovery (Year N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n1
msgid "Holiday Pay Recovery (Year N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Supplement Retenue"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
msgid "Holiday pay amount on the holiday attest from the previous employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Holiday pay details:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday pay supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__km_home_work
msgid "Home-Work Distance"
msgstr "Distancia de la casa al trabajo"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_residence
msgid "Home/Residence Allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Hospital Insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_adult
msgid "Hospital Insurance Amount per Adult"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_child
msgid "Hospital Insurance Amount per Child"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_hospital_insurance_notes
msgid "Hospital Insurance: Additional Info"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Hours/Week"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "IDENTIFICATION DU REDEVABLE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
msgid "IP percentage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_ip_part
msgid "IP. Part."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Identification du bénéficiaire"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Identification of the company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid ""
"If checked, the wizard will create another contract after the new working "
"schedule contract, with current working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "If other people are dependent on the employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled
msgid "If the employee is declared disabled by law"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Import Complete"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__import_file
msgid "Import File"
msgstr "Importar archivo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__import
msgid "Import the employee file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_impulsion_plan
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Impulsion Plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Included between 0 and 100%"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_individual_account
msgid "Individual Account"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_individual_account
msgid "Individual Account Report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_individual_account_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Individual Accounts"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#, python-format
msgid "Individual Accounts - Year %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_individual_account
msgid "Indivual Accounts"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Informal continuous trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Information on training for workers during the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__initial_time_off_allocation
msgid "Initial Time Off Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Initial trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insurance_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insurance Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults_total
msgid "Insured Relative Adults Total"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_spouse
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insured Spouse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_part
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip
msgid "Intellectual Property"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_deduction
msgid "Intellectual Property Income Deduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_internet
msgid "Internet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__internet
msgid "Internet Subscription"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__invalid
msgid "Invalid"
msgstr "No válido"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"Invalid NISS number for those employees:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_value
msgid "Ip Value"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__is_test
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__is_test
msgid "Is It a test ?"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale
msgid "Is below CP200 salary scale"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JAN"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUL"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUN"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__1
msgid "January"
msgstr "Enero"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_job
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Joint Commission :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__7
msgid "July"
msgstr "Julio"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__6
msgid "June"
msgstr "Junio"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_key
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_key
msgid "KEY file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Keep Time Off Right"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_key
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_key
msgid "Key to allow access to batch declarations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults_total
msgid "L10N Be Ambulatory Insured Adults Total"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_run__l10n_be_display_eco_voucher_button
msgid "L10N Be Display Eco Voucher Button"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_ffe_employer_type
msgid "L10N Be Ffe Employer Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_amount
msgid "L10N Be Group Insurance Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_cost
msgid "L10N Be Group Insurance Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale_warning
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale_warning
msgid "L10N Be Is Below Scale Warning"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_december
msgid "L10N Be Is December"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_double_pay
msgid "L10N Be Is Double Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_amount
msgid "L10N Be Max Seizable Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_warning
msgid "L10N Be Max Seizable Warning"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid "L10N Be Scale Category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__lang
msgid "Language"
msgstr "Idioma"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "Laptop"
msgstr "Portátil"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Le précompte mobilier est supporté par le débiteur des revenus à la décharge"
" du bénéficiaire :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_allocation_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__leave_allocation_id
msgid "Leave Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__leave_ids
msgid "Leaves"
msgstr "Permisos"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__line_ids
msgid "Line"
msgstr "Línea"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__line_ids
msgid "Lines"
msgstr "Líneas"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_long_sick
msgid "Long Term Sick"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAR"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAY"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Made by Odoo"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Male"
msgstr "Masculino"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_paid_leave
msgid "Manage the Allocation of Paid Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_employee
msgid "Manage the Allocation of Paid Time Off Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_notice
msgid "Manage the Employee Departure - Notice Duration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_holiday_attests
msgid "Manage the Employee Departure Holiday Attests"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Management staff"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__3
msgid "March"
msgstr "Marzo"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Mark as reported in payslip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__certificate__civil_engineer
msgid "Master: Civil Engineering"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Masters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_maternity
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_maternity
msgid "Maternity Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave_allocation__max_leaves_allocated
msgid "Max Leaves Allocated"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__maximum_days
msgid "Maximum Days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__5
msgid "May"
msgstr "Mayo"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ch_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Meal Voucher"
msgstr "Cheque gourmet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_employer
msgid "Meal Voucher (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_average_monthly_amount
msgid "Meal Voucher Average Monthly Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__meal_voucher_count
msgid "Meal Voucher Count"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_monthly_by_employer
msgid "Meal Voucher Paid Monthly By Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_by_employer
msgid "Meal Voucher Paid by Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_action
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_meal_voucher_amount
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_meal_voucher
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_view_pivot
msgid "Meal Vouchers"
msgstr "Vales de despensa"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employee Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employer Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Mean Working Hours :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_medical_assistance
msgid "Medical Assistance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_net
msgid "Misc. Net"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_impos
msgid "Misc. Taxable"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_mobile
msgid "Mobile"
msgstr "Móvil"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "Mobile Subscription"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Mobility Bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__1
msgid "Modification"
msgstr "Modificación"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant brut des revenus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant du Pr.M"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant imposable"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant à payer :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__month
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__month
msgid "Month"
msgstr "Mes"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ambulatory_insurance
msgid "Monthly ambulatory insurance (employer's share)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fuel_card
msgid "Monthly amount the employee receives on his fuel card."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_benefit_in_kind
msgid "Monthly benefit in kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid ""
"Monthly gross amount that the employee receives if the target is reached."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_group_insurance
msgid "Monthly group insurance (employer's share)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_hospital_insurance
msgid "Monthly hospital insurance (employer's share)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid ""
"Monthly net amount the employee receives to cover his representation fees."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "Multiple_PRP_Declaration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N-1 Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__normal
msgid "N/A"
msgstr "N/A"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__niss
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__niss
msgid "NISS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "NISS :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__niss
msgid "NISS Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "NOV"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__name
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name"
msgstr "Nombre"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_negative_net
msgid "Negative Net"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_net_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_net_termination
msgid "Net"
msgstr "Neto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_warrant_deduction
msgid "Net Deductions from Wages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_net
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net Salary"
msgstr "Salario neto"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Net cost to the business"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_12mo
msgid "Net part payable by the Onem (12+ months)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_25yo
msgid "Net part payable by the Onem (< 25 years old)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net salary paid by third party"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__new_resource_calendar_id
msgid "New Resource Calendar"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "New Wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__resource_calendar_id
msgid "New Working Schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_schedule_change_allocation.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "New working schedule on %(contract_name)s.<br/>New total : %(days)s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "No"
msgstr "No"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No KEY file defined on the Payroll Configuration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_onss
msgid "No ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"No ONSS registration number nor company ID was found for company %s. Please "
"provide at least one."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No PEM Certificate defined on the Payroll Configuration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_withholding_taxes
msgid "No Withholding Taxes"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#, python-format
msgid "No first contract date found for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No start/end notice period defined for %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Nom, prénoms, dénomination sociale ou officielle :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_non_respect_motivation
msgid "Non respect motivation (= 2 weeks)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Non-university higher education"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__resident_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__resident_bool
msgid "Nonresident"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid ""
"Note: The double holiday pay should only be computed if the reduction in "
"working time took place between 01/01 and 30/06 compared to year N-1."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_notice
msgid "Notice (Unprovided)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_notice_duration
msgid "Notice Duration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_month_before_2014
msgid "Notice Duration in month"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_week_after_2014
msgid "Notice Duration in weeks"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "Notice duration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice duration computed"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#, python-format
msgid ""
"Notice period not set for %s. Please, set the departure notice period first."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_salary
msgid "Notice salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__11
msgid "November"
msgstr "Noviembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of Affected Employees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Number of Days"
msgstr "Número de días"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Number of Hours"
msgstr "Número de horas"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of completed training hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Number of days of unpaid time off taken during current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Number of days of unpaid time off taken during previous year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days on which you should recover the holiday pay."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
msgid "Number of days to recover (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days to recover (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of dependent children declared as disabled"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_number
msgid "Number of disabled children"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid ""
"Number of juniors dependent on the employee, including the disabled ones"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of overtime hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid ""
"Number of seniors dependent on the employee, including the disabled ones"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Numbers of joint committees : 20000"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° d’entreprise ou, à défaut, n° national :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° téléphone :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N°273S - 2020"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "OCT"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss
msgid "ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_termination
msgid "ONSS (Double Holiday)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer
msgid "ONSS (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_onss_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_onss_termination
msgid "ONSS (Simple Holiday)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_onss_total
msgid "ONSS (TOTAL)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_total
msgid "ONSS (Total)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "ONSS Company ID"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Company ID :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation on termination fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation: Charges redistribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer_detail
msgid "ONSS Detail (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Double Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "ONSS Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid "ONSS Expeditor Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid ""
"ONSS Expeditor Number provided when registering service on the technical "
"user"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Number :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "ONSS Reduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "ONSS Registration Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Registration Number :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_restructuring
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_onss_restructuring
msgid "ONSS Restructuring Reduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Student"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Thirteen Month"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS termination fees cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Occasional worker in the Horeca sector"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__line_ids
msgid "Occupation Lines"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Occupation Rate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Occupations #"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__10
msgid "October"
msgstr "Octubre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Offical Company Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__oldest_contract_id
msgid "Oldest Contract"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid ""
"Only employees with a Bachelor/Master/Doctor/Civil Engineer degree can "
"benefit from the withholding taxes exemption."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa__unique
msgid ""
"Only one DMFA per year and per quarter is allowed. Another one already "
"exists."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll/report/hr_contract_history.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__0
msgid "Original"
msgstr "Original"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__0
msgid "Original send"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "Other Dependent People"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Other employer contributions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Other exempted amount from ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_other_annual
msgid "Other monthly/yearly"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Others"
msgstr "Otros"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_outplacement
msgid "Outplacement (if more than 30 weeks notice duration)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Overseas Social Security"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Overtime worked"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_file
msgid "PDF File"
msgstr "Archivo PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_filename
msgid "PDF Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf
msgid "PDF file"
msgstr "Archivo PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_pem_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_pem_certificate
msgid "PEM Certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_pem_passphrase
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_pem_passphrase
msgid "PEM Passphrase"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_pfi
msgid "PFI"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_cl
msgid "Paid Time Off"
msgstr "Ausencia pagada"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.allocating_paid_time_off_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_allocating_paid_time_off_view
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
#, python-format
msgid "Paid Time Off Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "Paid Time Off For The Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid "Paid Time Off To Allocate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_parental_time_off
msgid "Parental Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__part_time
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_search
msgid "Part Time"
msgstr "Tiempo parcial"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Part Time :"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "Part Time of %s must be stated at %s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Part-Time"
msgstr "Tiempo parcial"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_partial_incapacity
msgid "Partial Incapacity (due to illness)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_legal
msgid "Paternity Time Off (Legal)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_company
msgid "Paternity Time Off (Paid by Company)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Recibo de nómina"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double
msgid "Pay double"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_complementary
msgid "Pay double complementary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_pay_variable_salary
msgid "Pay on variable salary (15.34 of the annual amount)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_simple
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_simple
msgid "Pay simple"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Payment Structured Communication:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Informe del análisis de la nómina"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__payslip_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip"
msgstr "Recibo de nómina"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lotes de recibos de nómina"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_double_holiday_13th_month
msgid "Payslip Double Holidays / 13th Month"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr "Días trabajados en el recibo de nómina"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip previous year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n_ids
msgid "Payslips N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n1_ids
msgid "Payslips N-1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_filename
msgid "Pdf Filename"
msgstr "Nombre del archivo PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_to_generate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_to_generate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_to_generate
msgid "Pdf To Generate"
msgstr "PDF a generar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Pension"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Period"
msgstr "Periodo"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Period : From"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Permanent contract (CDI)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"Please configure a gender (either male or female) for the following employees:\n"
"\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid ""
"Please configure the 'Company Number' and the 'Revenue Code' on the Payroll "
"Settings."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Please find attached some data that will be useful to you to establish the "
"Social Report for the accounting year noted below. The Social Report for the"
" previous year may be useful for you to complete information concerning the "
"previous accounting year."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"Please note that the declaration by batch is rather complex and requires "
"some technical knowledge (using some concepts like ssh keys, SFTP servers, "
"and electronical signatures). You may want to take a look at the"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"Please provide an employer class for company %s. The employer class is given"
" by the ONSS and should be encoded in the Payroll setting."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid "Post Change Contract Creation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Posted Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_32
msgid "Pp Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_33
msgid "Pp Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_34
msgid "Pp Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Pr.M versé"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Premium from the Impulse Fund for general medicine obtained by a general "
"practitioner approved to settle in a \"priority\" area"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Previous Occupations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Primary education"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Print"
msgstr "Imprimir"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Private Car"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__private_car_reimbursed_amount
msgid "Private Car Reimbursed Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Private Car Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_private_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_private_car
msgid "Private car"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_professional_tax_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_professional_tax_termination
msgid "Professional Tax"
msgstr "Impuesto profesional"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_bank_holiday
msgid "Public Holiday"
msgstr "Festivo"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_phc
msgid "Public Holiday Compensation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_reimbursed_amount
msgid "Public Transport Reimbursed amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Public Transportation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_public_transport
msgid "Public Transportation (Tram - Bus - Metro)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_employee_amount
msgid "Public transport paid by the employee (Monthly)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter
msgid "Quarter"
msgstr "Trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 2"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 3"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 4"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_end
msgid "Quarter End"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter End :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_start
msgid "Quarter Start"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter Start :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "REVENUS MOBILIERS des DROITS D'AUTEUR et DROITS VOISINS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate PDF report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate XML report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__r
msgid "Real File (R)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_departure_reason__reason_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_reason_code
msgid "Reason Code"
msgstr "Código de motivo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
msgid "Recovered Simple Holiday Pay (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid "Recovered Simple Holiday Pay (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery_additional
msgid "Recovery Additional Time"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_recovery
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery
msgid "Recovery Bank Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__reference
msgid "Reference"
msgstr "Referencia"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reference Mean Working Hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__reference_month
msgid "Reference Month"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Reference Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
msgid "Reference Working Time"
msgstr "Tiempo de trabajo de referencia"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__reference_year
msgid "Reference Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_expense_refund
msgid "Refund Expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses"
msgstr "Gastos reembolsados"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses (Code 330)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Reimbursed Expenses (Representation Fees)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_reimbursement
msgid "Reimbursement"
msgstr "Reembolso"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__remuneration_n1
msgid "Remuneration N-1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n
msgid "Remuneration fictitious current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n1
msgid "Remuneration fictitious previous year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"Remuneration of statutory holidays occurring within 30 days of the end date "
"of the contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reorganization Measures :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_replacement
msgid "Replacement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Replacement contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_representation_fees
msgid "Representation Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees_volatile
msgid "Representation Fees (Without Serious Standards)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_rep_fees_regul
msgid "Representation Fees Regularization"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__requires_new_contract
msgid "Requires New Contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid "Respect of the notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_ch_worker
msgid "Retain on Meal Voucher"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Retenue de versement: précompte professionnel (salaires)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_revenue_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_revenue_code
msgid "Revenue Code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Right to time off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_specific_CP
msgid "Rules specific to Auxiliary Joint Committee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réduction CPDI"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réservé à l'administration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "SD Worx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__sdworx_code
msgid "SDWorx code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "SEP"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "SOCIAL BALANCE SHEET -  COMPLETE SCHEME"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "SOCIAL SECURITY CERTIFICATE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "SU"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Salaries paid in relation to previous years"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_advance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Advance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Assignment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Attachments"
msgstr "Embargos de salario"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__structure_type_id
msgid "Salary Structure Type"
msgstr "Tipo de estructura salarial"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_visibility
msgid "Salary as of December 2013"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Search Meal Voucher Report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Secondary education"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Secondment allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__1
msgid "Send grouped corrections"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_sending
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_sending
msgid "Sending Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_scale_seniority
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__l10n_be_scale_seniority
msgid "Seniority at Hiring"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__9
msgid "September"
msgstr "Septiembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Service Public Fédéral FINANCES"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__sheet_id
msgid "Sheet"
msgstr "Hoja"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10_filename
msgid "Sheet 274 10 Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntary_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntary_rate
msgid "Should be between 0 and 100 %"
msgstr "Debe estar entre 0 y 100 %"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_part_sick
msgid "Sick Time Off (Without Guaranteed Salary)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry.py:0
#, python-format
msgid "Sick time off to report to DRS for %s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature
msgid "Signature file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_simple_december
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_simple_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_december_pay
msgid "Simple December Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_simple_pay_december
msgid "Simple Holiday Pay (Lost due to working time reduction)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_simple_holiday_pay_variable_salary
msgid "Simple Holiday Pay - Variable Salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n
msgid "Simple Holiday Pay N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n1
msgid "Simple Holiday Pay N-1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
msgid "Simple Holiday Pay to Recover (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Simple Holiday Pay to Recover (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__slip_ids
msgid "Slip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_small_unemployment
msgid "Small Unemployment"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_small_unemployment
msgid "Small Unemployment (Brief Holiday)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename
msgid "Social Balance Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_balance_sheet_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_balance
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_balance_sheet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Social Balance Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_security_certificate_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_security_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Social Security Certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename
msgid "Social Security Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_onss_rule
msgid "Social contribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_solicitation_time_off
msgid "Solicitation Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_solidarity_cotisation
msgid "Solidarity Cotisation - Student Job"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Solidarity Cotisation: Company Cars"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "Some employee has no contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Special Social Cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_m_onss_total
msgid "Special Social Cotisation (Total)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_spec_soc_contribution
msgid "Special social contribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_mis_ex_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_mis_ex_onss
msgid "Special social cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status_explanation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status_explanation
msgid "Spouse Fiscal Status Explanation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Staff movements during the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid "Standard 38 hours/week"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_start
msgid "Start Period"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Start date must be earlier than end date."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Start date must be later than the current contract's start date."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start date of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__start_notice_period
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid "Start notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state
msgid "State"
msgstr "Provincia"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "State the Dimona at %s to declare the arrival of %s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Status of employed persons"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_stock_option
msgid "Stock Option"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_strike
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_strike
msgid "Strike"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Structural reductions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__struct_id
msgid "Structure"
msgstr "Estructura"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__structure_type_id
msgid "Structure Type"
msgstr "Tipo de estructura"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_student_regular_pay
msgid "Student: Regular Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Students"
msgstr "Estudiantes"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Sub-total Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "System 5 :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "TOTAL"
msgstr "TOTAL"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Taking into account for remuneration:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Taux du Pr.M"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status
msgid "Tax status for spouse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Adaptation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__taxable_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Taxable Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_32
msgid "Taxable Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_33
msgid "Taxable Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_34
msgid "Taxable Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Amounts (325)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_taxable_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_taxable_termination
msgid "Taxable Termination Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays
msgid "Terminaison Holidays"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_additional_leave
msgid "Terminaison Holidays Additional Leave"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double
msgid "Terminaison Holidays Double Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double_basic
msgid "Terminaison Holidays Double Pay Basic"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_simple
msgid "Terminaison Holidays Simple Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_fees
#, python-format
msgid "Termination"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_termination_fees
msgid "Termination Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "Termination Holidays Current Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "Termination Holidays Previous Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Termination fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:res.company,sign_terms:l10n_be_hr_payroll.res_company_be
#: model_terms:res.company,sign_terms_html:l10n_be_hr_payroll.res_company_be
msgid "Terms &amp; Conditions"
msgstr "Términos y condiciones"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "The DMFA Code will identify the work entry in DMFA report."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_fiscal_voluntary_rate
msgid "The Fiscal Voluntary rate on wage should be between 0 and 100."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_ip_rate
msgid "The IP rate on wage should be between 0 and 100."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid "The SDWorx code should have 7 characters or should be left empty!"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The VAT or the ZIP number is not specified on your company"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The belgian postcode length shouldn't exceed 4 characters and should contain"
" only numbers for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry_type.py:0
#, python-format
msgid "The code should have 4 characters!"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid "The code should have 7 characters!"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The company is not correctly configured on your employees. Please be sure "
"that the following pieces of information are set: street, zip, city, phone "
"and vat"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid ""
"The company number should contain digits only, starts with a '0' or a '1' "
"and be 10 characters long."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The company phone number shouldn't exceed 12 characters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
msgid ""
"The computed amount is the sum of the new right to time off and the number "
"of time off already taken by the employee. Example: Moving from a full time "
"to a 4/5 part time with 6 days already taken will result into an amount of "
"80%% of 14 days + 6 days (rounded down) = 17 days."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"The contract %s for %s is not of one the following types: CDI, CDD. "
"Replacement, For a clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"The contract %s for %s is not of one the following types: CP200 Employees or"
" Student"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid "The employee %s doesn't have a specified certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The employee first name shouldn't exceed 30 characters for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid "The employee is occupied from the %s to the %s."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid ""
"The employee is occupied from the %s to the %s. There is nothing to recover "
"as the employee is there for more than 12 months"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__internet
msgid "The employee's internet subcription will be paid up to this amount."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "The employee's mobile subscription will be paid up to this amount."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "The file has been downloaded."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"The files won't be posted in the employee portal if you don't have the "
"Documents app."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The following employees don't have a valid private address (with a street, a zip, a city and a country):\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid language for the selected salary structure.\n"
"                        <br/>\n"
"                        Please assign them a language below before continuing."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"The following work addesses do not have any ONSS identification code:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"The following work entry types do not have any DMFA code set:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_group_insurance_rate
msgid ""
"The group insurance salary sacrifice rate on wage should be between 0 and "
"100."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid "The payslips should be from the same company."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid ""
"The payslips should cover the same period:\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The phone number is not specified on your company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"The printed pdf contains all the data to encode into the 'DmfA declaration "
"web interface' that you can find"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#, python-format
msgid "The reference period is from the 1st of June %s to the 31th of May %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The seized amount (%s€) is above the belgian ceilings. Given a global net "
"salary of %s€ for the pay period and %s dependent children, the maximum "
"seizable amount is equal to %s€"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "The time Percentage in R&D should be between 0-100"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid ""
"The wage is under the minimum scale of %s€ for a seniority of %s years."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"El tipo de entrada de trabajo que se utiliza al generar entradas de trabajo "
"que se ajustan al horario de trabajo."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid "There is no SDWorx code defined for the following employees:\n"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid "There is no SDWorx code defined for the following work entry types:\n"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid ""
"There is no SDWorx code defined on the company. Please configure it on the "
"Payroll Settings"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "There is no defined expeditor number for the company."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "There is no payslip to generate for those employees"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid "There is no valid payslip to declare."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Thirteen Month"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This document is a translation. This is not a legal document."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid "This employee doesn't have a first contract date"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr "Esta es una entrada de trabajo de tiempo de crédito."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid ""
"This will add the eco-vouchers amount on the payslips in waiting state, and "
"create a new payslip for the employees without such a payslip. Are you sure "
"you want to proceed ?"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__threshold
msgid "Threshold"
msgstr "Umbral"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Time Off"
msgstr "Ausencia"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave_allocation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Time Off Allocation"
msgstr "Asignación de ausencias"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_n_ids
msgid "Time Off N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__holiday_status_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_type_id
msgid "Time Off Type"
msgstr "Tipo de ausencia"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__rd_percentage
msgid "Time Percentage in R&D"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocated
msgid "Time off allocated during current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Time off already taken"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_taken
msgid "Time off taken during current year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "To"
msgstr "A"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "To Allocate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid ""
"To benefit from this increase in the elusive or non-transferable quotas, the worker whose remuneration is subject to seizure or transfer, must declare it using a form, the model of which has been published in the Belgian Official Gazette. of 30 November 2006.\n"
"\n"
"He must attach to this form the documents establishing the reality of the charge invoked.\n"
"\n"
"Source: Opinion on the indexation of the amounts set in Article 1, paragraph 4, of the Royal Decree of 27 December 2004 implementing Articles 1409, § 1, paragraph 4, and 1409, § 1 bis, paragraph 4 , of the Judicial Code relating to the limitation of seizure when there are dependent children, MB, December 13, 2019."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the next employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the worker"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "Toggle Explanation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_total_n
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_total_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total"
msgstr "Total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total (FTE)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Basic before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Employer Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time (Code 1021)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time + Part-Time (Code 1023)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_with_ip
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_gross_with_ip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Net (Advance included)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Part-Time (Code 1022)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Total Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total actual number of hours worked or FTE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total allocated days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Total amount paid or awarded (the taxable part must be added to Total "
"remuneration 2.060)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total basic wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total_cost
msgid "Total cost employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total net wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Total of all Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total time off days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Total to pay on"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total: (14a + 14b + 14c)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Totaux :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_reimbursed_amount
msgid "Train Transport Reimbursed amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_employee_amount
msgid "Train transport paid by the employee (Monthly)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training
msgid "Training"
msgstr "Formación"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_training
msgid "Training Time Off"
msgstr "Ausencia por formación"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Transportation"
msgstr "Transportación"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_treatment
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_treatment
msgid "Treatment Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Unemployment with company supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "University education"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unjustified_reason
msgid "Unjustified Reason"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Unpaid Time Off"
msgstr "Ausencia no pagada"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_unpredictable
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unpredictable
msgid "Unpredictable Reason"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_unreasonable_dismissal
msgid "Unreasonable dismissal"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "Unsupported country code %s. Please contact an administrator."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_schedule_change_allocation
msgid "Update allocation on schedule change"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__has_bicycle
msgid "Use a bicycle as a transport mode to go to work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
msgid "Use this to limit the employees to compute"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_users
msgid "User"
msgstr "Usuario"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_public
msgid "Uses another public transportation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_car
msgid "Uses company car"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_private_car
msgid "Uses private car"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_train
msgid "Uses train transportation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "VAT Number :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__done
msgid "Valid"
msgstr "Válido"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_work_entry_type_view_form
msgid "Valid For Advantages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate"
msgstr "Validar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Validate & Compute holiday attests"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate & Compute termination fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__validation_state
msgid "Validation State"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Various bonuses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__batch
msgid "Via Batch"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__web
msgid "Via Web"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntarism
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntarism
msgid "Voluntarily increase withholding tax rate."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_wage
msgid "Wage"
msgstr "Salario"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__wage_type
msgid "Wage Type"
msgstr "Tipo de salario"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid "Wage With Sacrifices"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__waiting
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__waiting
msgid "Waiting"
msgstr "Esperando"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrants_cost
msgid "Warrant monthly cost for the employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrant_value_employee
msgid "Warrant monthly value for the employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_structure_warrant
msgid "Warrants"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"We draw your attention to the fact that this information is based on the data in Odoo and / or that you\n"
"                            have introduced in Odoo and that it is important that they be accompanied by a verification on your part\n"
"                            according to the particularities related to contract of the worker or your company which Odoo would not\n"
"                            know."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"When you're done with the commission edition, click on the 'Generate "
"Payslip' button to generate a batch of payslips using the commissions you've"
" provided."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid ""
"Whether or not the employee has a contract candidate for a working schedule "
"change"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__company
msgid "Whole Company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_pension
msgid "With High Pensions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_income
msgid "With High income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_income
msgid "With Low Income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_pension
msgid "With Low Pensions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Withdrawal not retained"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_warrant_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_salary_withholding_taxes
msgid "Withholding Tax"
msgstr "Retención en la fuente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__payment_reference
msgid "Withholding Tax Payment Reference"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_pp
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_pp_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_pp_total
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_withholding_taxes_total
msgid "Withholding Taxes (Total)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Capping (Bachelors)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_withholding_taxes_exemption
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Withholding Taxes Exemption"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Bachelors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid ""
"Withholding Taxes Exemption (Scientific Research) - Doctors / Civil "
"Engineers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Masters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_reduction
msgid "Withholding reduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__without_income
msgid "Without Income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__wizard_id
msgid "Wizard"
msgstr "Asistente"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_work_accident
msgid "Work Accident"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_work_accident
msgid "Work Accident Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Address :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_work_entry_daily_benefit_report
msgid "Work Entry Related Benefit Report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Place :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa_location_unit
msgid "Work Place defined by ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid "Work Time Rate"
msgstr "Tasa de tiempo de trabajo"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_action_work_address_codes
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_location_unit_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_location_unit_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Work address DMFA codes"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Work addresses without ONSS identification code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Work entries counts for meal vouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Work entries counts for private car reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
msgid "Work entries counts for representation fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Work entries counts for time off right for next year."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Tipo de entrada de trabajo para asistencias regulares."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"La tasa de tiempo de trabajo versus el horario de trabajo a tiempo completo "
"debe estar entre 0 y 100 %."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Worker"
msgstr "Trabajador"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Worker Code :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_onss
msgid "Worker Social Contribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Workers for whom the company has submitted a DIMONA declaration or who are "
"registered in the general staff register"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__partner_id
msgid "Working Address"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Working Schedule"
msgstr "Horario de trabajo"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.schedule_change_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Working Schedule Change"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid ""
"Working schedule would stay unchanged by this action. Please select another "
"working schedule."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_file
msgid "XLS file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_file
msgid "XML File"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_filename
msgid "XML Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml
msgid "XML file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_filename
msgid "Xls Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_filename
msgid "Xml Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_validation_state
msgid "Xml Validation State"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__year
msgid "Year"
msgstr "Año"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
msgid "Year of the period to consider"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_year_end_bonus
msgid "Year-end bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Yearly Advantages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission
msgid "Yearly Commission"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission_cost
msgid "Yearly Commission Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__eco_checks
msgid "Yearly amount the employee receives in the form of eco vouchers."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#, python-format
msgid ""
"You don't have the right to do this. Please contact your administrator!"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_december_slip_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_group_insurance_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#, python-format
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "You should upload a file to import first."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:res.company,sign_terms:l10n_be_hr_payroll.res_company_be
#: model_terms:res.company,sign_terms_html:l10n_be_hr_payroll.res_company_be
msgid "Your conditions..."
msgstr "Sus condiciones..."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Youth Hiring Plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_youth_time_off
msgid "Youth Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Advantages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Code 250"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Early vacation pay (other than referred to under 11b and 12b):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Forfaitaires"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Movements by cycle or by speed-pedelec"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Nom et prénom, ou dénomination"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Normal contributions and premiums"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"a) Provided in 2021 as part of the fight against COVID-19 and / or as part "
"of the recovery plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Public transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Total number of overtime hours actually worked"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) based on income received from employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) from employers who do not use the cash register system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) in the construction sector with registration system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "and at the"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears (other than referred to in 9b, 11c and 12c):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) Basis for the calculation of the additional salary relating to overtime "
"giving right to a reduction of :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Benefits in kind :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Contributions and bonuses for individual continuation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Early vacation pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Employer-specific expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Organized public transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other codes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Rue et numéro/boîte"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Réels"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) based on income received from a related foreign company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) from employers who use the cash register system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) in other sectors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) provided in 2020 as part of the fight against COVID-19 to employers "
"belonging to critical sectors and / or to employers belonging to critical "
"sectors"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Cancellation indemnities (other than referred to under 11d and 12d) and "
"redeployment indemnities:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "c) Code pays, code postal et commun"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Contributions and premiums for free supplementary pensions for salaried "
"workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Loyalty stamps"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Other means of transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Tips"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Cancellation indemnities"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Frontier workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "d) Numéro d'identification fiscale (facultatif)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Remuneration for the month of December (Public authority) (2):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Stock options"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_day
msgid "days"
msgstr "días"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "e) Exempt income received in fulfillment of a flexi-job contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "f) Beneficiary premium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "false"
msgstr "False"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_clearly_defined_work
msgid "for clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "for more informations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "fr"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "g) Mobility budget: total amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__generate
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__generate
msgid "generate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__get
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__get
msgid "get"
msgstr "obtener"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "h) First employment agreement: compensatory supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "here"
msgstr "aquí"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"i) Volunteer firefighter and volunteer Civil Protection officer paid "
"allowances"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "if recipient children is/are declared disabled by law"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__resident_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__resident_bool
msgid "if recipient lives in a foreign country"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "if recipient spouse is declared disabled by law"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "if spouse has professionnel income or not"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "j) Student job"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "k) Consumer checks"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_month
msgid "months"
msgstr "meses"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "number of dependent children declared as disabled"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "o) Prime Corona"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "process overview"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "the official documentation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "to"
msgstr "al"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_week
msgid "weeks"
msgstr "semanas"
