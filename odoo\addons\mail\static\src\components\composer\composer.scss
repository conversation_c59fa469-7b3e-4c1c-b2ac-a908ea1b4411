// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_Composer {
    // Could be replaced by .d-grid after the migration to BS5.
    display: grid;
    grid-template-areas:
        "sidebar-header core-header"
        "sidebar-main core-main"
        "sidebar-footer core-footer";
    grid-template-columns: auto 1fr;
    grid-template-rows: auto 1fr auto;

    &.o-has-current-partner-avatar {
        grid-template-columns: $o-mail-message-sidebar-width 1fr;
    }
}

.o_Composer_attachmentList {
    max-height: 300px;

    &.o-composer-is-compact {
        max-height: 100px;
    }
}

.o_Composer_buttons {
    min-height: $o-mail-composer-text-input-height + $border-width; // match minimal-height of input, including border width
}

.o_Composer_buttonEmojis:focus {
    outline: none;
    box-shadow: none;
}

.o_Composer_coreFooter {
    grid-area: core-footer;
}

.o_Composer_coreHeader {
    grid-area: core-header;
}

.o_Composer_coreMain {
    grid-area: core-main;
    min-width: 0;
}

.o_Composer_currentPartnerAvatar {
    width: $o-mail-thread-avatar-size;
    height: $o-mail-thread-avatar-size;
}

.o_Composer_sidebarMain {
    grid-area: sidebar-main;
    justify-self: center;
}

.o_Composer_textInput:not(.o-composer-is-compact) {
    min-height: $o-mail-composer-text-input-height;
}

.o_Composer_threadTextualTypingStatus:before {
    // invisible character so that typing status bar has constant height, regardless of text content.
    content: "\200b"; /* unicode zero width space character */
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_Composer_buttonDiscard {
    border: $border-width solid lighten($gray-400, 5%);
}
