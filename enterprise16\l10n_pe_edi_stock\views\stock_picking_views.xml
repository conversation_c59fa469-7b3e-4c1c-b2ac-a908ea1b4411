<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_picking_edi_form" model="ir.ui.view">
        <field name="model">stock.picking</field>
        <field name="name">stock.picking.edi.form</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='origin']" position="after">
                <field name="country_code" invisible="1"/>
                <field name="l10n_pe_edi_status" readonly="1" attrs="{'invisible': ['|', ('state', '!=', 'done'), ('country_code', '!=', 'PE')]}"/>
                <field name="l10n_pe_edi_transport_type"
                    attrs="{'invisible': [('country_code', '!=', 'PE')],
                            'readonly': [('l10n_pe_edi_status', '=', 'sent')]}"
                    string="Transport type"/>
                <field name="l10n_pe_edi_reason_for_transfer"
                    attrs="{'invisible': [('l10n_pe_edi_transport_type', '=', False)],
                            'readonly': [('l10n_pe_edi_status', '=', 'sent')]}"
                    string="Reason for transfer"/>
                <field name="l10n_pe_edi_departure_start_date"
                    attrs="{'invisible': [('l10n_pe_edi_transport_type', '=', False)],
                            'readonly': [('l10n_pe_edi_status', '=', 'sent')]}" widget="date"
                    string="Transfer start date"/>
            </xpath>
            <xpath expr="//header" position="after">
                <div class="alert alert-warning" role="alert" style="margin-bottom:0px;"
                    attrs="{'invisible': [('l10n_pe_edi_error', '=', False)]}">
                    <field name="l10n_pe_edi_error" readonly="1"/>
                    <button
                        name="action_send_delivery_guide"
                        type="object"
                        class="oe_link"
                        string="Retry"
                        attrs="{'invisible': [('l10n_pe_edi_status', 'not in', (False, 'to_send', 'to_cancel'))]}"/>
                    <button
                        name="l10n_pe_edi_action_clear_error"
                        type="object"
                        class="oe_link"
                        string="Ignore"
                        attrs="{'invisible': [('l10n_pe_edi_status', 'not in', [False, 'to_send', 'cancelled'])]}"/>
                    <button
                        name="l10n_pe_edi_action_download"
                        type="object"
                        class="oe_link"
                        string="Download"
                        groups="base.group_no_one"
                        attrs="{'invisible': [('l10n_pe_edi_status', 'not in', [False, 'to_send', 'cancelled'])]}"/>
                </div>
            </xpath>
            <xpath expr="//header//button[@name='action_cancel']" position="after">
                <button name="action_send_delivery_guide" type="object"
                    string="Generate Delivery Guide"
                    attrs="{'invisible':['|', '|', '|',('state', '!=', 'done'), ('country_code', '!=', 'PE'), ('picking_type_code', '!=', 'outgoing'), ('l10n_pe_edi_status', '!=', False)]}"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
                 <page string="PE EDI" name="pe_edi" attrs="{'invisible': [('l10n_pe_edi_transport_type', '=', False)], 'readonly': [('l10n_pe_edi_status', 'not in', (False, 'to_send', 'to_cancel'))]}">
                    <group>
                        <group string="Transport" name="pe_edi_group_transport" attrs="{'readonly': [('l10n_pe_edi_status', 'not in', (False, 'to_send', 'to_cancel'))]}">
                            <field name="l10n_latam_document_number" attrs="{'readonly': [('country_code', '=', 'PE')]}"/>
                            <field
                                name="l10n_pe_edi_vehicle_id"
                                string="Vehicle"
                                attrs="{'readonly': [('l10n_pe_edi_status', 'not in', (False, 'to_send', 'to_cancel'))], 'invisible': [('l10n_pe_edi_transport_type', '=', '01')]}"/>
                            <field
                                name="l10n_pe_edi_operator_id"
                                string="Operator"
                                attrs="{'readonly': [('l10n_pe_edi_status', 'not in', (False, 'to_send', 'to_cancel'))]}"/>
                            <field name="l10n_pe_edi_observation"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
