<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_pe.tax_group_igv" model="account.tax.group">
        <field name="l10n_pe_edi_code">IGV</field>
    </record>
    <record id="l10n_pe.tax_group_igv_g_ng" model="account.tax.group">
        <field name="l10n_pe_edi_code">IGV</field>
    </record>
    <record id="l10n_pe.tax_group_igv_ng" model="account.tax.group">
        <field name="l10n_pe_edi_code">IGV</field>
    </record>
    <record id="l10n_pe.tax_group_ivap" model="account.tax.group">
        <field name="l10n_pe_edi_code">IVAP</field>
    </record>
    <record id="l10n_pe.tax_group_isc" model="account.tax.group">
        <field name="l10n_pe_edi_code">ISC</field>
    </record>
    <record id="l10n_pe.tax_group_exp" model="account.tax.group">
        <field name="l10n_pe_edi_code">EXP</field>
    </record>
    <record id="l10n_pe.tax_group_gra" model="account.tax.group">
        <field name="l10n_pe_edi_code">GRA</field>
    </record>
    <record id="l10n_pe.tax_group_exo" model="account.tax.group">
        <field name="l10n_pe_edi_code">EXO</field>
    </record>
    <record id="l10n_pe.tax_group_ina" model="account.tax.group">
        <field name="l10n_pe_edi_code">INA</field>
    </record>
    <record id="l10n_pe.tax_group_other" model="account.tax.group">
        <field name="l10n_pe_edi_code">OTROS</field>
    </record>
    <record id="l10n_pe.tax_group_det" model="account.tax.group">
        <field name="l10n_pe_edi_code">DET</field>
    </record>
    <record id="l10n_pe.tax_group_icbper" model="account.tax.group">
        <field name="l10n_pe_edi_code">ICBPER</field>
    </record>
</odoo>
