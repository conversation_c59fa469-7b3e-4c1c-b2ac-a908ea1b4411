from odoo import api, fields, models
from datetime import datetime, timedelta
# class Partner(models.Model):
#     _inherit = 'res.partner'

#     is_coa_installed = fields.Many2one(
#         comodel_name='res.partner'
#     )

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    remove_draft_web_reservation = fields.Integer(
        string='Remove Draft Web Reservation',
        store=True,
        config_parameter='remove_draft_web_reservation', default=30)

    test = fields.Boolean('Do nat make separate Invoices')

    real_check_out = fields.Datetime(
        string='Real Check Out',
        store=True,
        config_parameter='real_check_out'
    )

    checkin = fields.Char(
        string='Check Out',
        store=True,
        config_parameter='checkin',
        default="2:00:00"
    )
    checkout = fields.Char(
        string='Check Out',
        store=True,
        config_parameter='checkout',
        default="11:59:59"

    )

    @api.model
    def set_values(self):
        """Convert Char to Config Parameter"""
        super().set_values()
        IrConfigParam = self.env['ir.config_parameter'].sudo()
        IrConfigParam.set_param('hotel_management.checkin', self.checkin or "00:00")
        IrConfigParam.set_param('hotel_management.checkout', self.checkout or "00:00")
        IrConfigParam.set_param('hotel_management.real_check_out', self.real_check_out or datetime.now())

    @api.model
    def get_values(self):
        """Retrieve Config Parameter"""
        res = super().get_values()
        IrConfigParam = self.env['ir.config_parameter'].sudo()
        res.update(
            checkin=IrConfigParam.get_param('hotel_management.checkin', default="00:00"),
            checkout=IrConfigParam.get_param('hotel_management.checkout', default="00:00"),
            real_check_out=IrConfigParam.get_param('hotel_management.real_check_out', default=datetime.now()),
        )
        return res
