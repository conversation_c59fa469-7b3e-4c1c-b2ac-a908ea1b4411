# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_demo
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_youtube_account
msgid "Channel: My Company"
msgstr "Canale: la mia azienda"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4_product_template
msgid "Desk"
msgstr "Scrivania"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4b_product_template
msgid "Desk Variant B"
msgstr "Variante B scrivania"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4c_product_template
msgid "Desk Variant C"
msgstr "Variante C scrivania"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4d_product_template
msgid "Desk Variant D"
msgstr "Variante D scrivania"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_instagram_account
msgid "Instagram Posts: My Company"
msgstr "Post Instagram: La mia azienda"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search
msgid "Keyword: #mycompany"
msgstr "Parola chiave: #mycompany"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search_competitor
msgid "Keyword: #mycompetitor"
msgstr "Parola chiave: #ilmioconcorrente"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_facebook_page
msgid "Page Posts: My Company"
msgstr "Post della pagina: La mia azienda"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_linkedin_page
msgid "Posts: My Company"
msgstr "Post: La mia azienda"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_account
msgid "Social Account"
msgstr "Account social"

#. module: social_demo
#: model:utm.campaign,title:social_demo.social_utm_campaign
msgid "Social Campaign"
msgstr "Campagna social"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_live_post
msgid "Social Live Post"
msgstr "Post social dal vivo"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_post
msgid "Social Post"
msgstr "Post social"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream
msgid "Social Stream"
msgstr "Flusso social"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream_post
msgid "Social Stream Post"
msgstr "Post flusso social"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_account
msgid "Tweets of: My Company"
msgstr "Tweet di: La mia azienda"

#. module: social_demo
#: model:ir.model,name:social_demo.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campagna UTM"

#. module: social_demo
#. odoo-javascript
#: code:addons/social_demo/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "You cannot upload videos in demo mode."
msgstr "Non puoi caricare video in modalità demo."
