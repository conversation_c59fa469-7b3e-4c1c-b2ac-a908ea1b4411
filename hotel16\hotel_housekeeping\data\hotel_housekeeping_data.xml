<?xml version="1.0" ?>
<odoo>
	<data noupdate="0">
			
		<record id="hotel_housekeeping_activity_type_0" model="hotel.housekeeping.activity.type">
			<field name="parent_id" ref="null"/>
			<field name="name">All Activities</field>
		</record>

		<record id="hotel_housekeeping_activity_type_1" model="hotel.housekeeping.activity.type">
			<field name="parent_id" model="product.category" search="[('isactivitytype','=',True)]" ref="hotel_housekeeping_activity_type_0"/>
			<field name="name">Room Activity</field>
		</record>
		
		<record id="hotel_housekeeping_activity_type_2" model="hotel.housekeeping.activity.type">
			<field name="parent_id" model="product.category" search="[('isactivitytype','=',True)]" ref="hotel_housekeeping_activity_type_0"/>
			<field name="name">Bed Activity</field>
		</record>

	</data>
</odoo>