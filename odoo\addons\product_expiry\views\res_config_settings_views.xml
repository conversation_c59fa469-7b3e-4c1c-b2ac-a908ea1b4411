<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_stock" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.product.expiry.stock</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='group_lot_on_delivery_slip']" position="after">
                <div class="col-12 col-lg-6 o_setting_box" attrs="{'invisible': ['|', ('group_lot_on_delivery_slip', '=', False), ('module_product_expiry', '=', False)]}" id="group_expiry_date_on_delivery_slip">
                    <div class="o_setting_left_pane">
                        <field name="group_expiry_date_on_delivery_slip"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="group_expiry_date_on_delivery_slip"/>
                        <div class="text-muted">
                            Expiration dates will appear on the delivery slip
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
