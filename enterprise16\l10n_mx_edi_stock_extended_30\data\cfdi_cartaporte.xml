<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cfdi_cartaporte_comex_30" inherit_id="l10n_mx_edi_stock_30.cfdi_cartaporte_30">

        <xpath expr="//*[name()='cartaporte20:CartaPorte']" position="attributes">
            <attribute name="t-att-RegimenAduanero">regimen_aduanero</attribute>
        </xpath>

        <xpath expr="//*[@t-att-Calle='record.partner_id.street']" position="attributes">
            <attribute name="t-att-Colonia">None</attribute>
            <attribute name="t-att-Municipio">record._l10n_mx_edi_get_municipio(record.partner_id)</attribute>
        </xpath>

        <xpath expr="//*[@t-att-Calle='warehouse_partner.street']" position="attributes">
            <attribute name="t-att-Colonia">None</attribute>
            <attribute name="t-att-Municipio">warehouse_partner.city_id.l10n_mx_edi_code</attribute>
        </xpath>

        <xpath expr="//*[name()='cfdi:InformacionAduanera']" position="replace"/>

        <xpath expr="//*[name()='cartaporte20:Mercancia']" position="attributes">
            <attribute name="t-att-TipoMateria">move.product_id.l10n_mx_edi_material_type if record.l10n_mx_edi_is_export else None</attribute>
            <attribute name="t-att-DescripcionMateria">move.product_id.l10n_mx_edi_material_description if (record.l10n_mx_edi_is_export and move.product_id.l10n_mx_edi_material_type == '05') else None</attribute>
        </xpath>

        <xpath expr="//*[name()='cartaporte20:CantidadTransporta']" position="before">
            <cartaporte30:DocumentacionAduanera
                xmlns:cartaporte30="http://www.sat.gob.mx/CartaPorte30"
                t-if="record.l10n_mx_edi_is_export"
                t-att-TipoDocumento="tipo_documento"
                t-att-NumPedimento="num_pedimento"
                t-att-IdentDocAduanero="ident_doc_aduanero"
                t-att-RFCImpo="rfc_impo"
            />
        </xpath>
    </template>
</odoo>
