# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_sms
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_sms
#: model:sms.template,body:stock_sms.sms_template_data_stock_delivery
msgid ""
"\n"
"                {{ (object.company_id.name + ': We are glad to inform you that your order n° ' + object.origin + ' has been shipped.' if object.origin else object.company_id.name + ': We are glad to inform you that your order has been shipped.') + (' Your tracking reference is ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}\n"
"            "
msgstr ""
"\n"
"                {{ (object.company_id.name + ': เรายินดีที่จะแจ้งให้คุณทราบว่าคำสั่งซื้อของคุณ n° ' + object.origin + ' ได้ถูกจัดส่งแล้ว' if object.origin else object.company_id.name + ': เรายินดีที่จะแจ้งให้คุณทราบว่าคำสั่งซื้อของคุณได้รับการจัดส่งแล้ว') + (' เลขอ้างอิงการติดตามของคุณคือ ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') และ object.carrier_tracking_ref else '') }}\n"
"            "

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Cancel"
msgstr "ยกเลิก"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_company
msgid "Companies"
msgstr "หลายบริษัท"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Confirm"
msgstr "ยืนยัน"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_confirm_stock_sms
msgid "Confirm Stock SMS"
msgstr "ยืนยัน SMS สต็อกสินค้า"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: stock_sms
#: model:sms.template,name:stock_sms.sms_template_data_stock_delivery
msgid "Delivery: Send by SMS Text Message"
msgstr "การจัดส่ง: ส่งทางข้อความ SMS"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Disable SMS"
msgstr "ปิดการใช้งาน SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__has_received_warning_stock_sms
msgid "Has Received Warning Stock Sms"
msgstr "ได้รับ SMS แจ้งสต็อกสินค้าแล้ว"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__id
msgid "ID"
msgstr "ไอดี"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__pick_ids
msgid "Pick"
msgstr "การรับสินค้า"

#. module: stock_sms
#: code:addons/stock_sms/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
#, python-format
msgid "SMS"
msgstr "SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_move_sms_validation
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Confirmation"
msgstr "การยืนยัน SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Template"
msgstr "เทมเพลต SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_move_sms_validation
msgid "SMS Validation with stock move"
msgstr "ตรวจสอบ SMS พร้อมการย้ายสต็อก"

#. module: stock_sms
#: model:ir.model.fields,help:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,help:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
msgid "SMS sent to the customer once the order is delivered."
msgstr "SMS ส่งถึงลูกค้าเมื่อมีการจัดส่งคำสั่งซื้อ"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid ""
"You are about to confirm this Delivery Order by SMS Text Message.<br/>\n"
"                This feature can easily be disabled from the Settings of Inventory or by clicking on \"Disable SMS\".<br/>"
msgstr ""
"คุณกำลังจะยืนยันคำสั่งจัดส่งนี้ทางข้อความ SMS<br/>\n"
"                ฟีเจอร์นี้สามารถปิดการใช้งานได้อย่างง่ายดายจากการตั้งค่าในสินค้าคงคลัง หรือ คลิกที่ \"ปิดการใช้งาน SMS\"<br/>"
