# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_ebay
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_ebay
#: model:mail.template,body_html:sale_ebay.ebay_desciption_default
msgid ""
"\n"
"                  \n"
"          ${object.ebay_description | safe}\n"
"        \n"
"      "
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:383
#, python-format
msgid ""
" If you want to set quantity to 0, the Out Of Stock option should be enabled"
" and the listing duration should set to Good 'Til Canceled"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:386
#, python-format
msgid ""
" You need to have at least 2 variations selected for a multi-variations listing.\n"
" Or if you try to delete a variation, you cannot do it by unselecting it. Setting the quantity to 0 is the safest method to make a variation unavailable."
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "10 Days"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "3 Days"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "30 Days (only for fixed price)"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "5 Days"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "7 Days"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:259
#, python-format
msgid ""
"All the quantities must be greater than 0 or you need to enable the Out Of "
"Stock option."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_best_offer
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_best_offer
msgid "Allow Best Offer"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_type:0
msgid "Auction"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_country_ebay_available
#: model:ir.model.fields,field_description:sale_ebay.field_res_currency_ebay_available
msgid "Availability To Use For eBay API"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_buy_it_now_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_buy_it_now_price
msgid "Buy It Now Price"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Cancel"
msgstr "Cancelar"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_category_id
msgid "Category"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_category_2_id
msgid "Category 2 (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_category_id
msgid "Category ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_category_parent_id
msgid "Category Parent ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_category_type
msgid "Category Type"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_code
msgid "Code"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:352
#, python-format
msgid "Configure The eBay Integrator Now"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_partner
msgid "Contact"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Countries & Currencies"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_country
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_country
msgid "Country"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_create_date
msgid "Created on"
msgstr "Creado en"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_currency
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_currency
msgid "Currency"
msgstr "Moneda"

#. module: sale_ebay
#: selection:ebay.category,category_type:0
msgid "Custom Store Category"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Describe the product characteristics..."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_template_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_template_id
msgid "Description Template"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_dev_id
msgid "Developer Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_listing_duration
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_listing_duration
msgid "Duration"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_end_items_listings
msgid "Ebay: End Product Templates Listings on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_list_items
msgid "Ebay: List Product Templates on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_relist_items
msgid "Ebay: Relist Product Templates on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_revise_items
msgid "Ebay: Revise Product Templates on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_unlink_items_listings
msgid "Ebay: Unlink eBay Product Templates Listings"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_categories_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_categories
#: model:ir.cron,name:sale_ebay.ir_cron_sale_ebay_categories
msgid "Ebay: update categories"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_status_10_ir_actions_server
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_status_5_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_status_10
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_status_5
#: model:ir.cron,name:sale_ebay.ir_cron_sale_ebay_status_10
#: model:ir.cron,name:sale_ebay.ir_cron_sale_ebay_status_5
msgid "Ebay: update product status"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "End Item's Listing"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:245
#, python-format
msgid ""
"Error Encountered.\n"
" No Variant Set To Be Listed On eBay."
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:389
#, python-format
msgid ""
"Error Encountered.\n"
"'%s'"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_fixed_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_fixed_price
msgid "Fixed Price"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_type:0
msgid "Fixed price"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_full_name
msgid "Full Name"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_gallery_plus
msgid "Gallery Plus"
msgstr ""

#. module: sale_ebay
#: selection:product.template,ebay_listing_duration:0
msgid "Good 'Til Cancelled (only for fixed price)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_id
msgid "ID"
msgstr "ID (identificación)"

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Import eBay config data and sync transactions"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:381
#, python-format
msgid ""
"Impossible to revise a listing into a multi-variations listing.\n"
" Create a new listing."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_item_condition_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_item_condition_id
msgid "Item Condition"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category___last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition___last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing___last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy___last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_last_sync
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_last_sync
msgid "Last update"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_leaf_category
msgid "Leaf Category"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link Existing Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Link With Existing eBay Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_ebay_link_listing
msgid "Link with Existing eBay Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link with eBay Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "List Item on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_listing_type
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_listing_type
msgid "Listing Type"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_location
msgid "Location"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_domain
msgid "Mode"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode and credentials"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_name
msgid "Name"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/sale_ebay.py:197
#, python-format
msgid "No Business Policies"
msgstr ""

#. module: sale_ebay
#: selection:ebay.category,category_type:0
msgid "Official eBay Category"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:351
#, python-format
msgid "One parameter is missing."
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:377
#, python-format
msgid "Or the condition is not compatible with the category."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_out_of_stock
msgid "Out Of Stock"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_seller_payment_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_seller_payment_policy_id
msgid "Payment Policy"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Policies"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_policy_id
msgid "Policy ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_private_listing
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_private_listing
msgid "Private Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_product
msgid "Product"
msgstr "Producto"

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Product Categories"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_template
msgid "Product Template"
msgstr "Plantilla producto"

#. module: sale_ebay
#: selection:res.config.settings,ebay_domain:0
msgid "Production"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_prod_app_id
msgid "Production App Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_prod_cert_id
msgid "Production Cert Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_prod_token
msgid "Production Token"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Products & Transactions"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_use
msgid "Publish On eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_quantity
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_quantity
msgid "Quantity"
msgstr "Cantidad"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_quantity_sold
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_quantity_sold
msgid "Quantity Sold"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Relist Item"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_seller_return_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_seller_return_policy_id
msgid "Return Policy"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Revise Item"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_crm_team
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_sales_team
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Channel"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales channel to manage eBay sales"
msgstr ""

#. module: sale_ebay
#: selection:res.config.settings,ebay_domain:0
msgid "Sandbox"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_sandbox_app_id
msgid "Sandbox App Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_sandbox_cert_id
msgid "Sandbox Cert Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_sandbox_token
msgid "Sandbox Token"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_seller_shipping_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_seller_shipping_policy_id
msgid "Shipping Policy"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_start_date
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_start_date
msgid "Start Date"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_price
msgid "Starting Price for Auction"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Storage"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Storage location of your products"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_store_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_store_category_id
msgid "Store Category (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_store_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_store_category_2_id
msgid "Store Category 2 (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_subtitle
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_subtitle
msgid "Subtitle"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_short_summary
msgid "Summary"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sync now"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Synchronization"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:718
#, python-format
msgid "The Buyer Chose The Following Delivery Method :\n"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:714
#: code:addons/sale_ebay/models/product.py:715
#, python-format
msgid "The Buyer Posted :\n"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product_ebay_subtitle
#: model:ir.model.fields,help:sale_ebay.field_product_template_ebay_subtitle
msgid ""
"The subtitle is restricted to 55 characters. Fees can be claimed by eBay for"
" this feature"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product_ebay_title
#: model:ir.model.fields,help:sale_ebay.field_product_template_ebay_title
msgid "The title is restricted to 80 characters"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product_ebay_category_2_id
#: model:ir.model.fields,help:sale_ebay.field_product_template_ebay_category_2_id
msgid ""
"The use of a secondary category is not allowed on every eBay sites. Fees can"
" be claimed by eBay for this feature"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product_ebay_template_id
#: model:ir.model.fields,help:sale_ebay.field_product_template_ebay_template_id
msgid "This field contains the template that will be used."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_title
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_title
msgid "Title"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy_policy_type
msgid "Type"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_ebay_update_carrier
msgid "Update eBay Carrier Information"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_sync_stock
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_sync_stock
msgid "Use Stock Quantity"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_use
msgid "Use eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Variants"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_zip_code
msgid "Zip"
msgstr ""

#. module: sale_ebay
#: model:crm.team,name:sale_ebay.ebay_sales_team
#: model:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay Account"
msgstr ""

#. module: sale_ebay
#: model:product.category,name:sale_ebay.product_category_ebay
msgid "eBay Delivery Services"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_description
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_description
msgid "eBay Description"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site_ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_id
msgid "eBay ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing_ebay_id
msgid "eBay Listing ID"
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay Options"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_listing_status
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_listing_status
msgid "eBay Status"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_partner_ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_res_users_ebay_id
msgid "eBay User ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_variant_url
msgid "eBay Variant URL"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings_ebay_site
msgid "eBay Website"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/sale_ebay.py:53
#, python-format
msgid ""
"eBay error: Impossible to synchronize the categories. \n"
"'%s'"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:482
#: code:addons/sale_ebay/models/product.py:555
#, python-format
msgid ""
"eBay error: Impossible to synchronize the products. \n"
"'%s'"
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/models/product.py:379
#, python-format
msgid "eBay is unreachable. Please try again later."
msgstr ""

#. module: sale_ebay
#: model:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay parameters"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product_ebay_url
#: model:ir.model.fields,field_description:sale_ebay.field_product_template_ebay_url
msgid "eBay url"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_category
msgid "ebay.category"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_item_condition
msgid "ebay.item.condition"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_link_listing
msgid "ebay.link.listing"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_policy
msgid "ebay.policy"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_site
msgid "ebay.site"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_config_settings
msgid "res.config.settings"
msgstr ""
