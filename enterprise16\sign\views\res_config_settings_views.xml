<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.sign</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="55"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Sign" string="Sign" data-key="sign" groups="sign.group_sign_manager">
                    <h2>Sign Settings</h2>
                    <div class="row mt16 o_settings_container" name="sign">
                        <div class="col-12 o_setting_box" id="use_sign_terms">
                            <div class="o_setting_left_pane">
                                <field name="use_sign_terms"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_sign_terms"/>
                                <div class="text-muted">
                                    Show standard terms &amp; conditions on signature requests
                                </div>
                                <div class="content-group" attrs="{'invisible': [('use_sign_terms','=',False)]}">
                                    <div class="mt16">
                                        <field name="sign_terms_type" class="o_light_label" widget="radio"/>
                                        <div class="d-flex">
                                            <field name="sign_terms"
                                                    attrs="{'invisible': [('sign_terms_type', '=', 'html')]}"
                                                    class="oe_sign_terms"
                                                    placeholder="Insert your terms &amp; conditions here..."/>
                                            <field name="sign_terms_html"
                                                    attrs="{'invisible': [('sign_terms_type', '=', 'plain')]}"
                                                    class="oe_sign_terms o_light_label"
                                                    placeholder="Insert your terms &amp; conditions here..."/>
                                        </div>
                                        <field name="sign_preview_ready" invisible="1"/>
                                        <div attrs="{'invisible': [('sign_preview_ready', '=', False)]}">
                                            <a href="/sign/terms">
                                                <i class="fa fa-arrow-right"></i>
                                                Preview
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 cold-md-6, o_setting_box" id="module_sign_itsme">
                            <div class="o_setting_left_pane">
                                <field name="module_sign_itsme"/>
                            </div>
                             <div class="o_setting_right_pane">
                                <label for="module_sign_itsme"/>
                                <div class="text-muted">
                                    Allow signatories to provide their identity using itsme® (available in Belgium and the Netherlands).
                                </div>
                                <widget name="iap_buy_more_credits" service_name="itsme_proxy" hide_service="1"/>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 o_setting_box" id="enable_sign_order">
                            <div class="o_setting_left_pane">
                                <field name="group_show_sign_order"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_show_sign_order"/>
                                <div class="text-muted">
                                    Allow users to define a signing order, where recipients are only notified when it is their turn to sign.
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 o_setting_box" id="enable_sign_order">
                            <div class="o_setting_left_pane">
                                <field name="group_manage_template_access"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_manage_template_access"/>
                                <div class="text-muted">
                                    Allow users to define the users or groups which have access to the template.
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12 col-md-6 o_setting_box" id="sms">
                            <div class="o_setting_right_pane" id="sms_settings">
                                <div class="o_form_label">
                                    Authenticate by SMS
                                    <a href="https://www.odoo.com/documentation/16.0/applications/marketing/sms_marketing/pricing/pricing_and_faq.html" title="Documentation" class="ms-1 o_doc_link" target="_blank"></a>
                                </div>
                                <div class="text-muted">
                                    Deliver one-time codes by SMS to identify signatories when signing a document.
                                </div>
                                <widget name="iap_buy_more_credits" service_name="sms" hide_service="1"/>
                            </div>
                        </div>
                        <div class="col-xs-12 col-md-6 o_setting_box" id="field_types">
                            <div class="o_setting_right_pane" id="field_types_settings">
                                <div class="o_form_label">Field Types</div>
                                <div class="text-muted">
                                    Configure the field types that can be used to sign documents (placeholder, auto-completion, ...)
                                </div>
                                <button name="%(sign.sign_item_type_action)d" icon="fa-arrow-right" type="action" string="Edit field types" class="btn-link"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
