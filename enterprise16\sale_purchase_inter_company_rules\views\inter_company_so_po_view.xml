<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record model="ir.ui.view" id="view_company_inter_change_inherit_form">
        <field name="name">res.company.form.inherit</field>
        <field name="inherit_id" ref="account_inter_company_rules.view_company_inter_change_inherit_form"/>
        <field name="model">res.company</field>
        <field name="arch" type="xml">
            <field name="rule_type" position="after">
                <field name="warehouse_id" options="{'no_create_edit': True}" domain="[('company_id', '=', id)]" attrs="{'invisible': [('rule_type', 'not in', ('sale_purchase', 'purchase'))]}"/>
                <field name="auto_validation" attrs="{'invisible': [('rule_type', 'not in', ('sale', 'purchase', 'sale_purchase'))]}"/>
            </field>
        </field>
    </record>
</odoo>
