# -*- coding: utf-8 -*-
{
    'name': "CUBES Accounting Reports",

    'summary': """
        Generate detailed accounting reports with direct and indirect cost allocation""",

    'description': """
        Monthly Claims Reports with:
        * Direct and indirect cost allocation
        * Company-wide indirect cost pooling
        * Service margin calculations
        * Customer-specific reporting
        * Detailed analytic account breakdowns
    """,

    'author': "CUBES Consulting",
    'website': "https://www.erp,cubes.ly",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/16.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Accounting',
    'version': '1.0',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account_accountant'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/views.xml',
        'wizard/wizard.xml'
    ],
    'images': ['static/description/icon.png'],
    'installable': True,
    'application': False,
    'auto_install': False,
}
