# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_partner
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_partner
#: model:ir.model,name:website_partner.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_partner
#: model_terms:ir.ui.view,arch_db:website_partner.partner_detail
msgid "Short Description for List View"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,help:website_partner.field_res_users_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_published
msgid "Visible in Website"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner_website_description
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_description
msgid "Website Partner Full Description"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner_website_short_description
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_short_description
msgid "Website Partner Short Description"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_url
msgid "Website URL"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_users_website_meta_title
msgid "Website meta title"
msgstr ""
