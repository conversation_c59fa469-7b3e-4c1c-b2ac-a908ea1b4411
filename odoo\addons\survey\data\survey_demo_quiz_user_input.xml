<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">

    <record id="survey_demo_quiz_answer_1" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_demo_quiz"/>
        <field name="partner_id" ref="base.partner_demo"/>
        <field name="email"><EMAIL></field>
        <field name="end_datetime" eval="datetime.now() - timedelta(days=1, hours=3, minutes=30)"/>
        <field name="start_datetime" eval="datetime.now() - timedelta(days=1, hours=4, minutes=50)"/>
        <field name="state">done</field>
    </record>
    <record id="survey_demo_quiz_answer_2" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_demo_quiz"/>
        <field name="partner_id" ref="base.partner_admin"/>
        <field name="email"><EMAIL></field>
        <field name="end_datetime" eval="datetime.now() - timedelta(days=1, hours=2, minutes=50)"/>
        <field name="start_datetime" eval="datetime.now() - timedelta(days=1, hours=3, minutes=50)"/>
        <field name="state">done</field>
    </record>
    <record id="survey_demo_quiz_answer_3" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_demo_quiz"/>
        <field name="partner_id" ref="base.partner_demo_portal"/>
        <field name="email"><EMAIL></field>
        <field name="end_datetime" eval="datetime.now() - timedelta(days=1, hours=2, minutes=10)"/>
        <field name="start_datetime" eval="datetime.now() - timedelta(days=1, hours=2, minutes=50)"/>
        <field name="state">done</field>
    </record>
    <record id="survey_demo_quiz_answer_4" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_demo_quiz"/>
        <field name="partner_id" ref="base.res_partner_address_28"/>
        <field name="email"><EMAIL></field>
        <field name="start_datetime" eval="datetime.now() - timedelta(days=1, hours=0, minutes=50)"/>
        <field name="state">in_progress</field>
    </record>
    <record id="survey_demo_quiz_answer_5" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_demo_quiz"/>
        <field name="partner_id" ref="base.res_partner_address_34"/>
        <field name="email"><EMAIL></field>
        <field name="state">new</field>
    </record>

</data></odoo>
