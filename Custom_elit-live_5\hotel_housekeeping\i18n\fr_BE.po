# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel_housekeeping
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 10:55+0000\n"
"PO-Revision-Date: 2020-08-12 10:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__product_variant_count
msgid "# Product Variants"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__product_count
msgid "# Products"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__a_list
msgid "A List"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: hotel_housekeeping
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_activity_type_form
msgid "Account Properties"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__active
msgid "Active"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_ids
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__activity_lines
#: model:ir.ui.menu,name:hotel_housekeeping.menu_open_h_activity_form
msgid "Activities"
msgstr "Activités"

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_activity_housekeeping
msgid "Activity"
msgstr "Activité"

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.menu_action_hotel_housekeeping_activity_type_view_form
msgid "Activity Categories"
msgstr "Catégorie Activités "

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.menu_action_hotel_housekeeping_activity_type_view_form_parent
msgid "Activity Definations"
msgstr "Définition Activités"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hotel_housekeeping
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Activity Lines"
msgstr "Lignes d'activité"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_state
msgid "Activity State"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_activity_type
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping_activity_type
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.hotel_activity_housekeeping_form
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.hotel_activity_housekeeping_tree
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.hotel_activity_type_form
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.hotel_activity_type_tree
msgid "Activity Type"
msgstr "Type d'Activité"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__available_in_pos
msgid "Available in POS"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__barcode
msgid "Barcode"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__purchase_ok
msgid "Can be Purchased"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__rental
msgid "Can be Rent"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sale_ok
msgid "Can be Sold"
msgstr ""

#. module: hotel_housekeeping
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Cancel"
msgstr "Annuler"

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__state__cancel
msgid "Cancelled"
msgstr "Annulé	"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__categ_id
msgid "Category"
msgstr "Catégorie"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__route_from_categ_ids
msgid "Category Routes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__clean_type__checkin
msgid "Checkin"
msgstr "Arrivé"

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__clean_type__checkout
msgid "Checkout"
msgstr "Départ"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__child_id
msgid "Child Categories"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__clean
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__state__clean
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Clean"
msgstr "Nettoyer"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__clean_end_time
msgid "Clean End Time"
msgstr "Fin nettoyage"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__clean_start_time
msgid "Clean Start Time"
msgstr "Début nettoyage"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__clean_type
msgid "Clean Type"
msgstr "Type nettoyage"

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__quality__clean
msgid "Cleaning"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__color
msgid "Color Index"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__combination_indices
msgid "Combination Indices"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__company_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__company_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__company_id
msgid "Company"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__complete_name
msgid "Complete Name"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__standard_price
msgid "Cost"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__cost_currency_id
msgid "Cost Currency"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__cost_method
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_cost_method
msgid "Costing Method"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__create_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__create_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__create_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__create_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__create_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__create_uid
msgid "Created by"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__create_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__create_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__create_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__create_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__create_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__create_date
msgid "Created on"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__currency_id
msgid "Currency"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sale_delay
msgid "Customer Lead Time"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__partner_ref
msgid "Customer Ref"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__taxes_id
msgid "Customer Taxes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__clean_type__daily
msgid "Daily"
msgstr "Quotidien"

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__seller_ids
msgid "Define vendor pricelists."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description
msgid "Description"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description_pickingout
msgid "Description on Delivery Orders"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description_picking
msgid "Description on Picking"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description_pickingin
msgid "Description on Receptions"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__dirty
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__state__dirty
msgid "Dirty"
msgstr "Sale"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__display_name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__display_name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__display_name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__display_name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__display_name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__display_name
msgid "Display Name"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__state__done
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Done"
msgstr "Effectué"

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__end_date
msgid "Expected End Date"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__property_account_expense_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_account_expense_categ_id
msgid "Expense Account"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__removal_strategy_id
msgid "Force Removal Strategy"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__virtual_available
msgid "Forecast Quantity"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__free_qty
msgid "Free To Use Quantity "
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__housekeeper
msgid "Housekeeper"
msgstr "Gouvernante"

#. module: hotel_housekeeping
#: model:ir.actions.act_window,name:hotel_housekeeping.open_hotel_housekeeping_form_tree
#: model:ir.ui.menu,name:hotel_housekeeping.hotel_housekeeping_menu
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_tree
msgid "Housekeeping"
msgstr "Ménage"

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping_activities
msgid "Housekeeping Activities "
msgstr "Activités Ménage"

#. module: hotel_housekeeping
#: model:ir.actions.act_window,name:hotel_housekeeping.action_activity_housekeeping_view_form
#: model:ir.actions.act_window,name:hotel_housekeeping.action_h_activity_form
#: model:ir.model,name:hotel_housekeeping.model_h_activity
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__activity_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__activity_name
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_h_activity_form
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_h_activity_tree
msgid "Housekeeping Activity"
msgstr "Action Ménage"

#. module: hotel_housekeeping
#: model:ir.actions.act_window,name:hotel_housekeeping.action_activity_type_view_form
#: model:ir.actions.act_window,name:hotel_housekeeping.action_hotel_housekeeping_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_activity_type_form
msgid "Housekeeping Activity Types"
msgstr "Types Action Menage"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__quality
msgid "Housekeeping Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__id
msgid "ID"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_needaction
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_has_error
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_1920
msgid "Image"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_1024
msgid "Image 1024"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_128
msgid "Image 128"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_256
msgid "Image 256"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_512
msgid "Image 512"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__standard_price
msgid ""
"In Standard Price & AVCO: value of the product (automatically computed in AVCO).\n"
"        In FIFO: value of the last unit that left the stock (automatically computed).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__property_account_income_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_account_income_categ_id
msgid "Income Account"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__incoming_qty
msgid "Incoming"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__state__inspect
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Inspect"
msgstr "Vérifier"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__inspect_date_time
msgid "Inspect Date Time"
msgstr "Date"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__inspector
msgid "Inspector"
msgstr "Inspecteur"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__default_code
msgid "Internal Reference"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__property_stock_inventory
msgid "Inventory Location"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__valuation
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_valuation
msgid "Inventory Valuation"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__isact
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_room__isact
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_room_amenities__isact
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_services__isact
#: model:ir.model.fields,field_description:hotel_housekeeping.field_product_product__isact
msgid "Is Activity"
msgstr "Ativité"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__isactivitytype
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_room_amenities_type__isactivitytype
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_room_type__isactivitytype
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_service_type__isactivitytype
#: model:ir.model.fields,field_description:hotel_housekeeping.field_product_category__isactivitytype
msgid "Is Activity Type"
msgstr "Type Activité	"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__is_product_variant
msgid "Is Product Variant"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__isroom
msgid "Is Room"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__isroomtype
msgid "Is Room Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__isservicetype
msgid "Is Service Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__isservice
msgid "Is Service id"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__has_configurable_attributes
msgid "Is a configurable product"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__isamenitype
msgid "Is amenities Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__iscategid
msgid "Is categ id"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping____last_update
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type____last_update
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity____last_update
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping____last_update
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities____last_update
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__write_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__write_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__write_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__write_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__write_uid
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__write_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__write_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__write_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__write_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activities__write_date
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__location_id
msgid "Location"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields.selection,name:hotel_housekeeping.selection__hotel_housekeeping__quality__maintenance
msgid "Maintenance"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__valuation
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_ids
msgid "Messages"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_housekeeping__name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__name
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__name
msgid "Name"
msgstr "Nom"

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.menu_open_hotel_housekeeping_form_tree
msgid "New HouseKeeping"
msgstr "Nouveau Ménage"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__pricelist_item_count
msgid "Number of price rules"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__outgoing_qty
msgid "Outgoing"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_activity_type__parent_id
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__parent_id
msgid "Parent Category"
msgstr "Catégorie Parent"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__parent_path
msgid "Parent Path"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__pos_categ_id
msgid "Point of Sale Category"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__price
msgid "Price"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__list_price
msgid "Price at which the product is sold to customers."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__pricelist_id
msgid "Pricelist"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_product_product
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__product_variant_id
msgid "Product"
msgstr "Article"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__attribute_line_ids
msgid "Product Attributes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_product_category
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__categ_id
msgid "Product Category"
msgstr "Catégorie d'articles"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__packaging_ids
msgid "Product Packages"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__product_tmpl_id
msgid "Product Template"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__type
msgid "Product Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__h_id
msgid "Product_id"
msgstr "Product_id"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__property_stock_production
msgid "Production Location"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__product_variant_ids
msgid "Products"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__lst_price
msgid "Public Price"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description_purchase
msgid "Purchase Description"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__uom_po_id
msgid "Purchase Unit of Measure"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__putaway_rule_ids
msgid "Putaway Rules"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__qty_available
msgid "Quantity On Hand"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__quantity_svl
msgid "Quantity Svl"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__expense_policy
msgid "Re-Invoice Expenses"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__code
msgid "Reference"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__reordering_max_qty
msgid "Reordering Max Qty"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__reordering_min_qty
msgid "Reordering Min Qty"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__nbr_reordering_rules
msgid "Reordering Rules"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping
msgid "Reservation"
msgstr "Réservation"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__responsible_id
msgid "Responsible"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__room_no
msgid "Room No"
msgstr "Numéro chambre"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__route_ids
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__route_ids
msgid "Routes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__description_sale
msgid "Sales Description"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sale_line_warn
msgid "Sales Order Line"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__list_price
msgid "Sales Price"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__categ_id
msgid "Select category for the current product"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sequence
msgid "Sequence"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""

#. module: hotel_housekeeping
#: model_terms:ir.ui.view,arch_db:hotel_housekeeping.view_hotel_housekeeping_form
msgid "Set to Dirty"
msgstr "Mettre au sale"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__sales_count
msgid "Sold"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__cost_method
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__current_date
msgid "Start Date"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__state
msgid "Status"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_journal
msgid "Stock Journal"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__stock_move_ids
msgid "Stock Move"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__stock_quant_ids
msgid "Stock Quant"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__valid_product_template_attribute_line_ids
msgid "Technical compute"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__stock_move_ids
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__state
msgid "Tells the user if room is available of booked."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__to_weight
msgid "To Weigh With Scale"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__total_route_ids
msgid "Total routes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__service_type
msgid "Track Service"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__tracking
msgid "Tracking"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__uom_name
msgid "Unit of Measure Name"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__value_svl
msgid "Value Svl"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_variant_1920
msgid "Variant Image"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_variant_1024
msgid "Variant Image 1024"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_variant_128
msgid "Variant Image 128"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_variant_256
msgid "Variant Image 256"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__image_variant_512
msgid "Variant Image 512"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__price_extra
msgid "Variant Price Extra"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__variant_seller_ids
msgid "Variant Seller"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__supplier_taxes_id
msgid "Vendor Taxes"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__seller_ids
msgid "Vendors"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__volume
msgid "Volume"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__volume_uom_name
msgid "Volume unit of measure label"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_h_activity__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__weight
msgid "Weight"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_h_activity__weight_uom_name
msgid "Weight unit of measure label"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_account_input_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the source location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,help:hotel_housekeeping.field_hotel_housekeeping_activity_type__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping_activity_type__activity_id
msgid "category"
msgstr "Catégorie"

#. module: hotel_housekeeping
#: model:ir.model.fields,field_description:hotel_housekeeping.field_hotel_housekeeping__state
msgid "state"
msgstr "Etat"
