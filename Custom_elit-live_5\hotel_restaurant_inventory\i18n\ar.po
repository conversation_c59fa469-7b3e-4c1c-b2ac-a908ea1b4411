# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel_restaurant_inventory
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-01 00:00+0000\n"
"PO-Revision-Date: 2023-01-01 00:00+0000\n"
"Last-Translator: \n"
"Language-Team: Arabic\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Generator: Odoo 16.0\n"

#. module: hotel_restaurant_inventory
#: model:ir.actions.act_window,name:hotel_restaurant_inventory.action_hotel_restaurant_inventory
#: model:ir.model,name:hotel_restaurant_inventory.model_hotel_restaurant_inventory
#: model:ir.ui.menu,name:hotel_restaurant_inventory.menu_hotel_restaurant_inventory
msgid "Restaurant Inventory"
msgstr "مخزون المطعم"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields,field_description:hotel_restaurant_inventory.field_hotel_restaurant_inventory__product_id
msgid "Product"
msgstr "المنتج"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields,field_description:hotel_restaurant_inventory.field_hotel_restaurant_inventory__quantity
msgid "Quantity"
msgstr "الكمية"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields,field_description:hotel_restaurant_inventory.field_hotel_restaurant_inventory__location_id
msgid "Location"
msgstr "الموقع"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields,field_description:hotel_restaurant_inventory.field_hotel_restaurant_inventory__state
msgid "State"
msgstr "الحالة"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields.selection,name:hotel_restaurant_inventory.selection__hotel_restaurant_inventory__state__draft
msgid "Draft"
msgstr "مسودة"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields.selection,name:hotel_restaurant_inventory.selection__hotel_restaurant_inventory__state__confirm
msgid "Confirmed"
msgstr "مؤكد"

#. module: hotel_restaurant_inventory
#: model:ir.model.fields.selection,name:hotel_restaurant_inventory.selection__hotel_restaurant_inventory__state__done
msgid "Done"
msgstr "تم" 