# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrJobCategory(models.Model):
    _name = 'hr.job.category'
    _description = 'Job Category for Education Requirements and Candidate Filtering'
    _order = 'sequence, name'

    name = fields.Char(string='Category Name', required=True)
    code = fields.Char(string='Category Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    description = fields.Text(string='Description')

    # Education requirements
    education_requirements = fields.Text(
        string='Education Requirements',
        required=True,
        help="Detailed education and experience requirements for this category"
    )
    minimum_education_level = fields.Selection([
        ('high_school', 'High School'),
        ('diploma', 'High Diploma'),
        ('bachelor', 'Bachelor Degree'),
        ('master', 'Master Degree'),
        ('phd', 'PhD')
    ], string='Minimum Education Level', required=True)

    # Experience requirements for this category
    minimum_experience_years = fields.Integer(
        string='Minimum Experience Years',
        default=0,
        help="Minimum years of experience required for this category"
    )
    required_field_experience = fields.Boolean(
        string='Requires Field-Specific Experience',
        default=False,
        help="Whether experience must be in the specific field"
    )

    # Related fields
    job_count = fields.Integer(
        string='Number of Jobs',
        compute='_compute_job_count'
    )

    active = fields.Boolean(string='Active', default=True)

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Category code must be unique!'),
        ('name_unique', 'unique(name)', 'Category name must be unique!')
    ]

    @api.depends('name')
    def _compute_job_count(self):
        for category in self:
            category.job_count = self.env['hr.job'].search_count([
                ('job_category_id', '=', category.id)
            ])

    def action_view_jobs(self):
        """View all jobs in this category"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Jobs in Category: %s') % self.name,
            'res_model': 'hr.job',
            'view_mode': 'tree,form',
            'domain': [('job_category_id', '=', self.id)],
            'context': {'default_job_category_id': self.id}
        }

    @api.constrains('minimum_experience_years')
    def _check_minimum_experience_years(self):
        for category in self:
            if category.minimum_experience_years < 0:
                raise ValidationError(_('Minimum experience years cannot be negative.'))
