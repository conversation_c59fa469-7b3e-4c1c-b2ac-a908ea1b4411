<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_nl_reports_sbr_tax_report_wizard_form" model="ir.ui.view">
        <field name="name">l10n_nl_reports_sbr.tax.report.wizard.view.form</field>
        <field name="model">l10n_nl_reports_sbr.tax.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Tax Report XBRL for SBR">
                <sheet>
                    <group string="New SBR File">
                        <field name="contact_initials"/>
                        <field name="contact_surname"/>
                        <field name="contact_prefix"/>
                        <field name="contact_phone"/>
                        <field name="contact_type"/>
                        <field name="tax_consultant_number" attrs="{'required':[('contact_type', '=', 'INT')]}"/>
                        <field name='password' password="True"/>
                        <field name="is_test" groups="base.group_no_one"/>
                    </group>
                    <field name="can_report_be_sent" invisible="1"/>
                    <field name="date_to" invisible="1"/>
                    <field name="date_from" invisible="1"/>
                </sheet>
                <footer>
                    <button name="send_xbrl" string="Send" type="object" class="btn-primary" data-hotkey="q" attrs="{'invisible':[('can_report_be_sent', '=', False)]}"/>
                    <button name="action_download_xbrl_file" string="Download" type="object" class="btn-secondary" data-hotkey="l"/>
                    <button string="Close" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record model="ir.actions.server" id="action_open_closing_entry">
            <field name="name">Create Closing Entry</field>
            <field name="model_id" ref="l10n_nl_reports_sbr.model_l10n_nl_tax_report_handler"/>
            <field name="binding_model_id" ref="l10n_nl_reports_sbr.model_l10n_nl_tax_report_handler"/>
            <field name="state">code</field>
            <field name="groups_id" eval="[(4,ref('account.group_account_user'))]"/>
            <field name="code">
action = model.action_periodic_vat_entries(env.context['options'])
            </field>
     </record>
</odoo>
