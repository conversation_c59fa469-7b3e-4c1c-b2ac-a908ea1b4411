# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_demo
# 
# Translators:
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_youtube_account
msgid "Channel: My Company"
msgstr "Channel: My Company"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4_product_template
msgid "Desk"
msgstr "Sto"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4b_product_template
msgid "Desk Variant B"
msgstr "Desk Variant B"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4c_product_template
msgid "Desk Variant C"
msgstr "Desk Variant C"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4d_product_template
msgid "Desk Variant D"
msgstr "Desk Variant D"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_instagram_account
msgid "Instagram Posts: My Company"
msgstr "Instagram Posts: My Company"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search
msgid "Keyword: #mycompany"
msgstr "Keyword: #mycompany"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search_competitor
msgid "Keyword: #mycompetitor"
msgstr "Keyword: #mycompetitor"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_facebook_page
msgid "Page Posts: My Company"
msgstr "Page Posts: My Company"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_linkedin_page
msgid "Posts: My Company"
msgstr "Posts: My Company"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_account
msgid "Social Account"
msgstr "Društveni nalog"

#. module: social_demo
#: model:utm.campaign,title:social_demo.social_utm_campaign
msgid "Social Campaign"
msgstr "Social Campaign"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_live_post
msgid "Social Live Post"
msgstr "Socijalna objava uživo"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_post
msgid "Social Post"
msgstr "Objava na društvenoj mreži"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream
msgid "Social Stream"
msgstr "Social Stream"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream_post
msgid "Social Stream Post"
msgstr "Social Stream Post"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_account
msgid "Tweets of: My Company"
msgstr "Tweets of: My Company"

#. module: social_demo
#: model:ir.model,name:social_demo.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM kampanja"

#. module: social_demo
#. odoo-javascript
#: code:addons/social_demo/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "You cannot upload videos in demo mode."
msgstr "You cannot upload videos in demo mode."
