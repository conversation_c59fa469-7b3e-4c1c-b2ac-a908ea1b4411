# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * snailmail_account_reports_followup
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON>rag<PERSON>oulos <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2019-01-16 08:41+0000\n"
"Last-Translator: Fot<PERSON> Tragopoulos <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: snailmail_account_reports_followup
#: code:addons/snailmail_account_reports_followup/wizard/followup_send.py:75
#, python-format
msgid ""
"%s of the selected partner(s) had an invalid address. The corresponding "
"followups were not sent"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"                         The letter will be sent using the IAP service from Odoo.&#10;Make sure you have enough credits on your account or proceed to a recharge.                         \"/>\n"
"                        <br/>"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('letters_qty', '&gt;', 1)]}\"> Sending this "
"document will cost </span>"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid ""
"<span class=\"text-danger\" attrs=\"{'invisible': [('letters_qty', '&gt;', 1)]}\">\n"
"                                The recipient's address is incomplete.\n"
"                            </span>"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid ""
"<span class=\"text-danger\">\n"
"                                    Some customer addresses are not complete.\n"
"                                </span>"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_followup_send
msgid "Followup Send"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__id
msgid "ID"
msgstr "Κωδικός"

#. module: snailmail_account_reports_followup
#: code:addons/snailmail_account_reports_followup/wizard/followup_send.py:74
#: code:addons/snailmail_account_reports_followup/wizard/followup_send.py:81
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__invalid_partner_ids
#, python-format
msgid "Invalid Addresses"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__invalid_addresses
msgid "Invalid Addresses Count"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__letters_qty
msgid "Number of letters"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__partner_ids
msgid "Recipients"
msgstr "Αποδέκτες"

#. module: snailmail_account_reports_followup
#: model:ir.actions.act_window,name:snailmail_account_reports_followup.followup_send
msgid "Send By Post"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Send Follow-Ups"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr ""

#. module: snailmail_account_reports_followup
#. openerp-web
#: code:addons/snailmail_account_reports_followup/static/src/xml/account_reports_followup_template.xml:7
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
#, python-format
msgid "Send by Post"
msgstr "Αποστολή με Ταχυδρομείο"

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Sending these"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__snailmail_cost
msgid "Stamp(s)"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,help:snailmail_account_reports_followup.field_account_followup_followup_line__send_letter
msgid "When processing, it will send a letter by Post"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "customers"
msgstr "πελάτες"

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "documents will cost"
msgstr ""
