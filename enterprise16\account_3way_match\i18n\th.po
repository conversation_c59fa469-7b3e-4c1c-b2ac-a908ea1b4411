# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_3way_match
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__release_to_pay_manual
#: model:ir.model.fields,help:account_3way_match.field_account_move__release_to_pay_manual
#: model:ir.model.fields,help:account_3way_match.field_account_payment__release_to_pay_manual
msgid ""
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""
"* ใช่: คุณควรชำระค่าสินค้า เพราะคุณได้รับสินค้าแล้ว\n"
"* ไม่ คุณไม่ควรชำระค่าสินค้า เนื่องจากคุณยังไม่ได้รับสินค้า\n"
"* ข้อยกเว้น มีความแตกต่างระหว่างปริมาณที่ได้รับและเรียกเก็บเงิน\n"
"สถานะนี้ถูกกำหนดโดยอัตโนมัติ แต่คุณสามารถบังคับได้โดยทำเครื่องหมายที่ช่อง 'สถานะบังคับ'"

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills in Exception"
msgstr "ใบเรียกเก็บเงินที่อยู่ในข้อยกเว้น"

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills to Pay"
msgstr "บิลที่ต้องชำระ"

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__exception
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__exception
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__exception
msgid "Exception"
msgstr "ข้อยกเว้น"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__force_release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__force_release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__force_release_to_pay
msgid "Force Status"
msgstr "สถานะการบังคับ"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__force_release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_move__force_release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_payment__force_release_to_pay
msgid ""
"Indicates whether the 'Should Be Paid' status is defined automatically or "
"manually."
msgstr "ระบุว่าสถานะ 'ควรจะชำระเงิน' จะถูกกำหนดโดยอัตโนมัติหรือด้วยตนเอง"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_journal
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__no
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__no
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__no
msgid "No"
msgstr "ไม่"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__release_to_pay
msgid "Release To Pay"
msgstr "ปล่อยเพื่อชำระเงิน"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_move_line__can_be_paid
msgid "Release to Pay"
msgstr "ปล่อยจ่าย"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__release_to_pay_manual
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__release_to_pay_manual
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__release_to_pay_manual
msgid "Should Be Paid"
msgstr "ควรได้รับการชำระเงิน"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_move__release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_payment__release_to_pay
msgid ""
"This field can take the following values :\n"
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""
"ฟิลด์นี้สามารถรับค่าต่อไปนี้ได้:\n"
"* ใช่: คุณควรชำระค่าสินค้า เพราะคุณได้รับสินค้าแล้ว\n"
"* ไม่ คุณไม่ควรชำระค่าสินค้า เนื่องจากคุณยังไม่ได้รับสินค้า\n"
"* ข้อยกเว้น มีความแตกต่างระหว่างปริมาณที่ได้รับและเรียกเก็บเงิน\n"
"สถานะนี้ถูกกำหนดโดยอัตโนมัติ แต่คุณสามารถบังคับได้โดยทำเครื่องหมายที่ช่อง 'สถานะบังคับ'"

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__yes
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__yes
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__yes
msgid "Yes"
msgstr "ใช่"
