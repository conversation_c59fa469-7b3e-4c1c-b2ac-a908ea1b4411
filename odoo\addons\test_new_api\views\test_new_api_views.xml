<odoo>
    <data>
        <menuitem id="menu_main" name="Discussions" sequence="20" parent="base.menu_tests"/>

        <menuitem id="menu_sub" name="Discussions" parent="menu_main" sequence="10"/>

        <record id="action_discussions" model="ir.actions.act_window">
            <field name="name">Discussions</field>
            <field name="res_model">test_new_api.discussion</field>
            <field name="view_mode">tree,form</field>
        </record>
        <menuitem id="menu_discussions" action="action_discussions" parent="menu_sub" sequence="10"/>

        <record id="action_messages" model="ir.actions.act_window">
            <field name="name">Messages</field>
            <field name="res_model">test_new_api.message</field>
            <field name="view_mode">tree,form</field>
        </record>
        <menuitem id="menu_messages" action="action_messages" parent="menu_sub" sequence="20"/>

        <record id="action_multi" model="ir.actions.act_window">
            <field name="name">Multi</field>
            <field name="res_model">test_new_api.multi</field>
            <field name="view_mode">tree,form</field>
        </record>
        <menuitem id="menu_multi" action="action_multi" parent="menu_sub"/>

        <menuitem id="menu_config" name="Configuration" parent="menu_main" sequence="20"/>

        <record id="action_categories" model="ir.actions.act_window">
            <field name="name">Categories</field>
            <field name="res_model">test_new_api.category</field>
            <field name="view_mode">tree,form</field>
        </record>
        <menuitem id="menu_categories" action="action_categories" parent="menu_config"/>

        <!-- Discussion form view -->
        <record id="discussion_form" model="ir.ui.view">
            <field name="name">discussion form view</field>
            <field name="model">test_new_api.discussion</field>
            <field name="priority" eval="1"/>
            <field name="arch" type="xml">
                <form string="Discussion">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <!--
                                Bug if the view contains at least 5 fields.
                                The order of the dic values changes and the _check_author constraint raise.
                                <field name="categories" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            -->
                            <field name="moderator"/>
                        </group>
                        <notebook>
                            <page string="Messages">
                                <field name="messages">
                                    <tree string="Messages">
                                        <field name="name"/>
                                        <field name="body"/>
                                        <field name="important"/>
                                    </tree>
                                    <form string="Message">
                                        <group>
                                            <field name="name"/>
                                            <field name="author"/>
                                            <field name="size"/>
                                            <field name="important"/>
                                        </group>
                                        <label for="body"/>
                                        <field name="body"/>
                                    </form>
                                </field>
                            </page>
                            <page string="Emails">
                                <field name="important_emails">
                                    <tree name="Important Messages">
                                        <field name="name"/>
                                        <field name="body"/>
                                        <field name="important"/>
                                        <field name="email_to"/>
                                    </tree>
                                    <form string="Important Message">
                                        <group>
                                            <field name="name"/>
                                            <field name="author"/>
                                            <field name="size"/>
                                            <field name="important"/>
                                            <field name="email_to"/>
                                        </group>
                                        <label for="body"/>
                                        <field name="body"/>
                                    </form>
                                </field>
                            </page>
                            <page string="Participants">
                                <field name="participants" widget="many2many">
                                    <tree string="Participants">
                                        <field name="display_name"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                        <group>
                            <field name="message_concat"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Discussion form view with editable list -->
        <record id="discussion_form_2" model="ir.ui.view">
            <field name="name">discussion form view</field>
            <field name="model">test_new_api.discussion</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form string="Discussion">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="categories" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        </group>
                        <notebook>
                            <page string="Messages">
                                <field name="messages">
                                    <tree name="Messages" editable="bottom">
                                        <field name="name"/>
                                        <field name="author"/>
                                        <field name="body" required="1"/>
                                        <field name="size"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Participants">
                                <field name="participants"/>
                            </page>
                        </notebook>
                        <group>
                            <field name="message_concat"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_discussions_2" model="ir.actions.act_window">
            <field name="name">Discussions 2</field>
            <field name="res_model">test_new_api.discussion</field>
            <field name="view_ids" eval="[Command.clear(),
                          (0,0,{'view_mode':'tree'}),
                          (0,0,{'view_mode':'form', 'view_id': ref('discussion_form_2')})]"/>
        </record>
        <menuitem id="menu_discussions_2" action="action_discussions_2" parent="menu_sub" sequence="10"/>

        <!-- Discussion form view with embedded list -->
        <record id="discussion_form_3" model="ir.ui.view">
            <field name="name">discussion form view</field>
            <field name="model">test_new_api.discussion</field>
            <field name="priority" eval="11"/>
            <field name="arch" type="xml">
                <form string="Discussion">
                    <sheet>
                        <group>
                            <field name="name" context="{'generate_dummy_message': True}"/>
                        </group>
                        <group>
                            <field name="messages" />
                            <field name="important_messages">
                                <tree string="Important messages">
                                    <field name="name"/>
                                    <field name="author"/>
                                    <field name="size"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_discussions_3" model="ir.actions.act_window">
            <field name="name">Discussions 3</field>
            <field name="res_model">test_new_api.discussion</field>
            <field name="view_ids" eval="[Command.clear(),
                          (0,0,{'view_mode':'tree'}),
                          (0,0,{'view_mode':'form', 'view_id': ref('discussion_form_3')})]"/>
        </record>
        <menuitem id="menu_discussions_3" action="action_discussions_3" parent="menu_sub" sequence="11"/>


        <!-- Message tree view -->
        <record id="message_tree" model="ir.ui.view">
            <field name="name">message tree view</field>
            <field name="model">test_new_api.message</field>
            <field name="arch" type="xml">
                <tree string="Messages">
                    <field name="name"/>
                    <field name="author"/>
                    <field name="size"/>
                    <field name="important"/>
                </tree>
            </field>
        </record>

        <!-- Message form view -->
        <record id="message_form" model="ir.ui.view">
            <field name="name">message form view</field>
            <field name="model">test_new_api.message</field>
            <field name="arch" type="xml">
                <form string="Message">
                    <sheet>
                        <group>
                            <field name="discussion"/>
                            <field name="name"/>
                            <field name="author"/>
                            <field name="size"/>
                            <field name="attributes"/>
                        </group>
                        <label for="body"/>
                        <field name="body"/>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Category tree view -->
        <record id="category_tree" model="ir.ui.view">
            <field name="name">category tree view</field>
            <field name="model">test_new_api.category</field>
            <field name="arch" type="xml">
                <tree string="Categories">
                    <field name="display_name"/>
                </tree>
            </field>
        </record>

        <!-- Category form view -->
        <record id="category_form" model="ir.ui.view">
            <field name="name">category form view</field>
            <field name="model">test_new_api.category</field>
            <field name="arch" type="xml">
                <form string="Category">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="parent"/>
                            <field name="root_categ"/>
                            <field name="dummy"/>
                            <field name="color"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Multi tree view -->
        <record id="multi_tree" model="ir.ui.view">
            <field name="name">multi tree view</field>
            <field name="model">test_new_api.multi</field>
            <field name="arch" type="xml">
                <tree string="Multi">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <!-- Multi form view -->
        <record id="multi_form" model="ir.ui.view">
            <field name="name">multi form view</field>
            <field name="model">test_new_api.multi</field>
            <field name="arch" type="xml">
                <form string="Multi">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="partner"/>
                        </group>
                        <label for="lines"/>
                        <field name="lines">
                            <tree string="Lines">
                                <field name="name"/>
                                <field name="partner"/>
                            </tree>
                            <form string="Lines">
                                <group>
                                    <field name="name"/>
                                    <field name="partner"/>
                                </group>
                                <field name="tags">
                                    <tree string="Tags" editable="1">
                                        <field name="name"/>
                                    </tree>
                                </field>
                            </form>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Multi form view -->
        <record id="model_parent_form" model="ir.ui.view">
            <field name="name">model parent form view</field>
            <field name="model">test_new_api.model_parent_m2o</field>
            <field name="arch" type="xml">
                <form string="Model Parent">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="cost"/>
                        </group>
                        <label for="child_ids"/>
                        <field name="child_ids">
                            <tree string="Children" editable="bottom">
                                <field name="name"/>
                                <field name="cost"/>
                            </tree>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="model_shared_cache_compute_parent_view_form" model="ir.ui.view">
            <field name="name">model_shared_cache_compute_parent_view_form</field>
            <field name="model">test_new_api.model_shared_cache_compute_parent</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="total_amount"/>
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="parent_id"/>
                                    <field name="amount"/>
                                    <field name="user_id"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- compute_editable view -->
        <record id="compute_editable_form" model="ir.ui.view">
            <field name="name">test_new_api.compute_editable.form</field>
            <field name="model">test_new_api.compute_editable</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <field name="line_ids" mode="tree">
                            <tree editable="bottom">
                                <field name="value"/>
                                <field name="edit"/>
                                <field name="count"/>
                            </tree>
                            <form>
                                <!-- this view makes sure that field "same" is not considered -->
                                <field name="value"/>
                                <field name="edit"/>
                                <field name="count"/>
                            </form>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

    </data>
</odoo>
