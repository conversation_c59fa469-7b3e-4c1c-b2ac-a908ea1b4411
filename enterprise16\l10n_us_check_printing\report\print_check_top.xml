<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>

<template id="print_check_top">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <div t-att-data-oe-model="o._name" t-att-data-oe-id="o.id" t-foreach="o._check_get_pages()" t-as="page" class="page article ckus_page ckus_top_page" t-attf-style="top: {{ o.company_id.account_check_printing_margin_top }}in; left: {{ o.company_id.account_check_printing_margin_left }}in;">
                <t t-call="l10n_us_check_printing.ckus_check" />
                <t t-call="l10n_us_check_printing.ckus_stub"><t t-set="stub_num" t-value="1"/></t>
                <t t-call="l10n_us_check_printing.ckus_stub"><t t-set="stub_num" t-value="2"/></t>
            </div>
        </t>
    </t>
</template>

</data>
</odoo>
