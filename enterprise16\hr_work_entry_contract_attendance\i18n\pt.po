# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_attendance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:09+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: cafonso <<EMAIL>>, 2022\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,help:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_work_entry__attendance_id
msgid "Attendance"
msgstr "Assiduidade"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields.selection,name:hr_work_entry_contract_attendance.selection__hr_contract__work_entry_source__attendance
msgid "Attendances"
msgstr "Assiduidades"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato do Funcionário"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry
msgid "HR Work Entry"
msgstr ""

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Regenerar Entradas de Trabalho de Funcionário"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr ""
