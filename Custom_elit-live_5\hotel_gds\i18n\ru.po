# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* hotel_gds
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-21 05:10+0000\n"
"PO-Revision-Date: 2020-05-21 05:10+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_gds
#: selection:hotel.reservation,meal_id:0
msgid "Buffet Breakfast"
msgstr "Позавтракать"

#. module: hotel_gds
#: sql_constraint:hotel.reservation.through.gds.configuration:0
msgid "Combination Of From date, To date and shop must be unique !"
msgstr "Сочетание от даты, до даты и магазина должно быть уникальным !"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__company_id
msgid "Company"
msgstr "Компания"

#. module: hotel_gds
#: model:ir.model,name:hotel_gds.model_hotel_reservation_through_gds_configuration
msgid "Configuration through gds"
msgstr "Настройка через gds"

#. module: hotel_gds
#: model:ir.model,name:hotel_gds.model_hotel_reservation_through_gds_line
msgid "Configuration through gds line"
msgstr "Конфигурации по линии gds"

#. module: hotel_gds
#: selection:hotel.reservation,meal_id:0
msgid "Continental Breakfast"
msgstr "Континентальный завтрак"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__create_uid
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__create_date
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__create_date
msgid "Created on"
msgstr "Создан"

#. module: hotel_gds
#: model:ir.ui.menu,name:hotel_gds.dashboard_url_menu
msgid "Dashboard Configuration"
msgstr "Конфигурация Панели Управления"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__display_name
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__display_name
msgid "Display Name"
msgstr "Отображаемое Имя"

#. module: hotel_gds
#: constraint:hotel.reservation.through.gds.configuration:0
msgid "Error!\n"
"The duration of the Period(s) is/are invalid."
msgstr "Ошибка! Продолжительность периода(ов) является/являются недействительными."

#. module: hotel_gds
#: constraint:hotel.reservation.through.gds.configuration:0
msgid "Error!\n"
"The period is invalid.Some periods are overlapping ."
msgstr "Ошибка! Этот период является недействительным.Некоторые периоды накладываются друг на друга."

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__fri_bool
msgid "Friday"
msgstr "Пятница"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__name
msgid "From Date"
msgstr "С Даты"

#. module: hotel_gds
#: selection:hotel.reservation,meal_id:0
msgid "Full-board"
msgstr "Полный пансион"

#. module: hotel_gds
#: model:ir.actions.act_window,name:hotel_gds.gds_configuration_action
#: model:ir.ui.menu,name:hotel_gds.gds_configuration_id
#: model_terms:ir.ui.view,arch_db:hotel_gds.view_gds_configuration_form
#: model_terms:ir.ui.view,arch_db:hotel_gds.view_gds_configuration_tree
msgid "GDS Configuration"
msgstr "Конфигурации GDS"

#. module: hotel_gds
#: selection:hotel.reservation,meal_id:0
msgid "Half-board"
msgstr "Полу-пансион"

#. module: hotel_gds
#: model:ir.model,name:hotel_gds.model_hotel_room
msgid "Hotel Room"
msgstr "Гостиничный номер"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__id
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__id
msgid "ID"
msgstr "Номер"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration____last_update
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__write_uid
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__write_date
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__line_ids
msgid "Line ID"
msgstr "Номер линии"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__name
msgid "Line Id"
msgstr "Номер линии"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation__meal_id
msgid "Meal Type"
msgstr "Тип Питания"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__mon_bool
msgid "Monday"
msgstr "Понедельник"

#. module: hotel_gds
#: selection:hotel.reservation,meal_id:0
msgid "No Meal Included"
msgstr "Питание Не Включено"

#. module: hotel_gds
#: model:ir.model,name:hotel_gds.model_hotel_reservation
msgid "Reservation"
msgstr "Резервирование"

#. module: hotel_gds
#: model:ir.model,name:hotel_gds.model_hotel_reservation_line
msgid "Reservation Line"
msgstr "Линия Бронирования"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__room_number
msgid "Room"
msgstr "Комната"

#. module: hotel_gds
#: model_terms:ir.ui.view,arch_db:hotel_gds.hotel_room_form_inherit_gds
msgid "Room Availability"
msgstr "Наличие Свободных Номеров"

#. module: hotel_gds
#: model_terms:ir.ui.view,arch_db:hotel_gds.view_gds_room_line_form
#: model_terms:ir.ui.view,arch_db:hotel_gds.view_gds_room_line_tree
msgid "Room Line"
msgstr "Линия Комнаты"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_line__categ_id
msgid "Room Type"
msgstr "Тип номера"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__sat_bool
msgid "Saturday"
msgstr "Суббота"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__shop_id
msgid "Shop"
msgstr "Магазин"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__sun_bool
msgid "Sunday"
msgstr "Воскресенье"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__thr_bool
msgid "Thursday"
msgstr "Четверг"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_reservation_through_gds_configuration__to_date
msgid "To Date"
msgstr "На дату"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__tue_bool
msgid "Tuesday"
msgstr "Вторник"

#. module: hotel_gds
#: model:ir.model.fields,field_description:hotel_gds.field_hotel_room__wed_bool
msgid "Wednesday"
msgstr "Среда"

#. module: hotel_gds
#: model:ir.model.fields,help:hotel_gds.field_hotel_reservation_through_gds_line__room_number
msgid "Will list out all the rooms that belong to selected shop."
msgstr "Будет перечислен список всех комнат, которые принадлежат выбранному магазину."

