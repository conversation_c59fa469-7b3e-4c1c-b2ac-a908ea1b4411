<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="twitter_preview" t-name="Twitter Preview">
            <div class="o_social_preview o_social_twitter_preview bg-white p-3 border rounded overflow-hidden d-flex">
                <span class="o_social_preview_icon_wrapper me-2">
                    <i class="fa fa-twitter fa-2x"/>
                </span>
                <div>
                    <div class="o_social_preview_header">
                        <b class="text-900">Twitter Account</b>
                        <span class="text-600">@twitteraccount &#183;
                            <a t-if="live_post_link" t-attf-href="#{live_post_link}" target="blank">11m</a>
                            <t t-else="">11m</t>
                        </span>
                    </div>
                    <div class="d-table">
                        <span t-esc="message[:limit]" class="o_social_preview_message"/>
                        <span t-if="is_twitter_post_limit_exceed" t-esc="message[limit:]" class="o_social_preview_message o_social_twitter_message_exceeding"/>
                    </div>
                    <div class="o_social_stream_post_image d-flex rounded overflow-hidden">
                        <t t-foreach="images" t-as="image">
                            <a t-if="image_index == 1 and len(images) > 2" class="o_social_stream_post_image_more position-relative d-flex ms-1 mt-2">
                                <img t-attf-src="data:image/png;base64, #{image}" alt="Post Image" />
                                <div class="o_social_stream_post_image_more_overlay d-flex align-items-center h-100 w-100 text-white justify-content-center position-absolute h1 m-0" style="user-select: none;">
                                    +<t t-esc="len(images) - 2"/>
                                </div>
                            </a>
                            <img t-elif="image_index &lt; 2" class="mt-2" t-attf-src="data:image/png;base64, #{image}" alt="Post Image"  />
                        </t>
                    </div>
                </div>
            </div>
        </template>
    </data>
</odoo>
