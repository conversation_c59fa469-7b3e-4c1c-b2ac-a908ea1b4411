# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Nothing to do here!\n"
"                </p>\n"
"                <p>\n"
"                    No transactions matching your filters were found.\n"
"                </p>\n"
"            "
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "$ -2,678.00"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "$ 2,678.00"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "-1,134.50"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "1 Bank Transaction"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "<i class=\"fa fa-check\"/> Matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "<span class=\"ml4 mr4\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "<span class=\"ml4 mr4\">in</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': ['|', "
"('form_force_negative_sign', '=', False), ('form_index', '=', "
"False)]}\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('form_force_negative_sign', '=', False)]}\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Account"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Add a Transaction"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__amls_widget
msgid "Amls Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Amount in Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_distribution
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
#, python-format
msgid "Analytic"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_distribution_search
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_precision
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Bank"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Bills"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
msgid "Company"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
#, python-format
msgid "Credit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Currency"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Customer/Vendor"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Date"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
#, python-format
msgid "Debit"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Discuss"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Exchange Difference: %s"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_account_id
msgid "Form Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_amount_currency
msgid "Form Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_balance
msgid "Form Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_currency_id
msgid "Form Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_date
msgid "Form Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_extra_text
msgid "Form Extra Text"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_flag
msgid "Form Flag"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_force_negative_sign
msgid "Form Force Negative Sign"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_name
msgid "Form Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_id
msgid "Form Partner"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_currency_id
msgid "Form Partner Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_payable_account_id
msgid "Form Partner Payable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_payable_amount
msgid "Form Partner Payable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_receivable_account_id
msgid "Form Partner Receivable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_receivable_amount
msgid "Form Partner Receivable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_single_currency_mode
msgid "Form Single Currency Mode"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_suggest_amount_currency
msgid "Form Suggest Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_suggest_balance
msgid "Form Suggest Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_tax_ids
msgid "Form Tax"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "INV/2032/0003"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "INV/2032/0012"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related information at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Invoices"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Journal"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal Entry"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Label"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Larry Smith"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget____last_update
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the lines_widget"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__lines_widget
msgid "Lines Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Manual Operations"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_view_switcher.js:0
#: code:addons/account_accountant/static/src/components/matching_link_widget/matching_link_widget.xml:0
#, python-format
msgid "Match"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Match Existing Entries"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Matched Transactions"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Miscellaneous"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__next_action_todo
msgid "Next Action Todo"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "No attachments linked."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "Open Amount"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount in Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Open balance: %s"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Partner"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Payable"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Payable:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Please install the 'Accounting Reports' module."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Accountants users."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Receivable:"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__reco_models_widget
msgid "Reco Models Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#, python-format
msgid "Reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
#, python-format
msgid "Reconciliation"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Reset"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Search customer"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Set as Checked"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr ""

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Taxes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_narration
msgid "Terms and Conditions"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "To Check"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Transaction Details"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr ""

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
#: model:ir.cron,cron_name:account_accountant.auto_reconcile_bank_statement_line
msgid "Try to reconcile automatically your statement lines"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
#, python-format
msgid "Unreconciled statements lines"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Validate"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "View"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "You cannot have a fiscal year on a child company."
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have maximum one auto balance line at the same time in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have multiple liquidity journal item at the same time in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have multiple times the same journal item in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "You cannot set a lock date in the future."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "or"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "reference"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr ""
