<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_resequence_view" model="ir.ui.view">
            <field name="name">Re-sequence Journal Entries</field>
            <field name="model">account.resequence.wizard</field>
            <field name="arch" type="xml">
            <form string="Re-Sequence">
                <field name="move_ids" invisible="1"/>
                <field name="new_values" invisible="1"/>
                <field name="sequence_number_reset" invisible="1"/>
                <group>
                    <group>
                        <field name="ordering" widget="radio"/>
                    </group>
                    <group>
                        <field name="first_name"/>
                    </group>
                </group>
                <group>
                    <label for="preview_moves" string="Preview Modifications" colspan="2"/>
                    <field name="preview_moves" widget="account_resequence_widget" nolabel="1" colspan="2"/>
                </group>
                <footer>
                    <button string="Confirm" name="resequence" type="object" default_focus="1" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
            </field>
        </record>
    </data>
    <data noupdate="1">
        <record id="action_account_resequence" model="ir.actions.act_window">
            <field name="name">Resequence</field>
            <field name="res_model">account.resequence.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="account_resequence_view"/>
            <field name="target">new</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="binding_view_types">list</field>
        </record>
    </data>
</odoo>
