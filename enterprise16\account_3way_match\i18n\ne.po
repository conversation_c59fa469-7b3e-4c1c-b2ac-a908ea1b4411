# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_3way_match
# 
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: Bishisht Bhatta <<EMAIL>>, 2017\n"
"Language-Team: Nepali (https://www.transifex.com/odoo/teams/41243/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice_release_to_pay_manual
msgid ""
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities."
msgstr ""

#. module: account_3way_match
#: model:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills in Exception"
msgstr "अपवादमा रहेका बिलहरू "

#. module: account_3way_match
#: model:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills to Pay"
msgstr "भुक्तानी गर्नुपर्ने बिलहरू "

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "Exception"
msgstr "अपवाद"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice_force_release_to_pay
msgid "Force status"
msgstr ""

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice_force_release_to_pay
msgid ""
"Indicates whether the 'Can be paid' status is defined automatically or "
"manually."
msgstr ""

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_invoice
msgid "Invoice"
msgstr "इनभ्वाइस"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_invoice_line
msgid "Invoice Line"
msgstr "इनभ्वाइस लाइन"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_journal
msgid "Journal"
msgstr "जर्नल"

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "No"
msgstr "होइन"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice_line_can_be_paid
msgid "Release to Pay"
msgstr ""

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice_release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice_release_to_pay_manual
msgid "Should be paid"
msgstr "भुक्तानी गर्नैपर्ने "

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice_release_to_pay
msgid ""
"This field can take the following values :\n"
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""

#. module: account_3way_match
#: model:product.product,name:account_3way_match.demo_product
#: model:product.template,name:account_3way_match.demo_product_product_template
msgid "VR Computer"
msgstr "वीआर कम्प्युटर"

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "Yes"
msgstr "हो"
