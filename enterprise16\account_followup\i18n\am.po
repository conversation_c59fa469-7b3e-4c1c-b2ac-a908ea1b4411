# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "%s Payment Reminder - %s"
msgstr ""

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Follow-up ' + object.display_name"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__type
#: model:ir.model.fields,help:account_followup.field_res_users__type
msgid ""
"- Contact: Use this to organize the contact details of employees of a given company (e.g. CEO, CFO, ...).\n"
"- Invoice Address : Preferred address for all invoices. Selected by default when you invoice an order that belongs to this company.\n"
"- Delivery Address : Preferred address for all deliveries. Selected by default when you deliver an order that belongs to this company.\n"
"- Private: Private addresses are only visible by authorized users and contain sensitive data (employee home addresses, ...).\n"
"- Other: Other address for the company (e.g. subsidiary, ...)"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "15 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "30 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "40 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "50 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "60 Days"
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.email_template_followup_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        Exception made if there was a mistake of ours, it seems that an amount of <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"></t>\n"
"                        stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br>\n"
"                        Would your payment have been carried out after this mail was sent, please ignore this message.\n"
"                        Do not hesitate to contact our accounting department.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_4
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        Despite several reminders, your account is still not settled.\n"
"                        Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"                        I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"                        In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <br>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_2
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"                        <br>\n"
"                        It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services). Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br>\n"
"                        If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"                        <br>\n"
"                        Details of due payments is printed below.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr ""

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__account_manager
msgid "Account Manager"
msgstr ""

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Activity"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Activity Notes"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_type_id
msgid "Activity Type"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Add contacts to notify..."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__type
#: model:ir.model.fields,field_description:account_followup.field_res_users__type
msgid "Address Type"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Assigned to me"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__join_invoices
msgid "Attach Invoices"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__attachment_ids
msgid "Attachment"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__automatic
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Automatic"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body_html
msgid "Body Html"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Cancel"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Content Template"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body
msgid "Contents"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Country"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_uid
msgid "Created by"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_date
msgid "Created on"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Customer"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr ""

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
#, python-format
msgid "Date"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr ""

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up lines must be different per company"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Dear %s,\n"
"\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"\n"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Dear client, we kindly remind you that you still have unpaid invoices. "
"Please check them and take appropriate action. %s"
msgstr ""

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Description"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__activity_default_responsible_type
msgid ""
"Determine who will be assigned to the activity:\n"
"- Follow-up Responsible (default)\n"
"- Salesperson: Sales Person defined on the invoice\n"
"- Account Manager: Sales Person defined on the customer"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__display_name
msgid "Display Name"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email
msgid "Email"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_add_signature
msgid "Email Add Signature"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Recipients"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Subject"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Exclude from Follow-ups"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_recipient_ids
msgid "Extra Recipients"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Follow up"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up %s - %s"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__type__followup
msgid "Follow-up Address"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_line_id
msgid "Follow-up Level"
msgstr ""

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr ""

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__followup
msgid "Follow-up Responsible"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter generated"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr ""

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"            possible to use print and e-mail templates to send specific messages to\n"
"            the customer."
msgstr ""

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_4
msgid "Fourth reminder followup"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Group By"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__id
msgid "ID"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Invoice No."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_date
msgid "Invoice/Bill Date"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Invoices"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Kind reminder!"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__lang
msgid "Language"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__last_followup_date
msgid "Latest Follow-up"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Letter/Email Content"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__mail_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__template_id
msgid "Mail Template"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__manual
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Manual"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_next_action_date
msgid "Next reminder"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr ""

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_note
msgid "Note"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Notification"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_manual_reminder__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__followup_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the activities. If empty, we will find someone responsible."
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_origin
#, python-format
msgid "Origin"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__partner_id
msgid "Partner"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:mail.template,name:account_followup.email_template_followup_1
#, python-format
msgid "Payment Reminder"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__print
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Print"
msgstr ""

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr ""

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process Automatic Follow-ups"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Reconcile"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Remind"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_reminder_type
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_reminder_type
msgid "Reminders"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__render_model
msgid "Rendering Model"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Residual Amount"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_default_responsible_type
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_responsible_id
msgid "Responsible"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "SMS"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__salesperson
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Salesperson"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_activity
msgid "Schedule Activity"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr ""

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_2
msgid "Second reminder followup"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send & Print"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send Email"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send SMS Message"
msgstr ""

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.manual_reminder_action
msgid "Send and Print"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms
msgid "Sms"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_body
msgid "Sms Body"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Sms Content"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_template_id
msgid "Sms Template"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__subject
msgid "Subject"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_summary
msgid "Summary"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__followup_next_action_date
msgid "The date before which no follow-up action should be taken."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Can be negative if you want to send the reminder before the "
"invoice due date."
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Total Amount"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoice_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoice_ids
msgid "Unpaid Invoice"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices_count
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices_count
msgid "Unpaid Invoices Count"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "With overdue invoices"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Write your message here..."
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send an Email, but no follow-up contact has any email "
"address set for customer '%s'"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send an SMS, but no follow-up contact has any mobile/phone"
" number set for customer '%s'"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days after due date"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr ""

#. module: account_followup
#: model:mail.template,subject:account_followup.demo_followup_email_template_2
#: model:mail.template,subject:account_followup.demo_followup_email_template_4
#: model:mail.template,subject:account_followup.email_template_followup_1
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Payment Reminder - {{ object.commercial_company_name }}"
msgstr ""
