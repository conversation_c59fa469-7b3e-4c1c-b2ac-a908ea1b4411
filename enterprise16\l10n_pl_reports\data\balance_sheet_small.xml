<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_pl_small_bs" model="account.report">
        <field name="name">Bilans - Mały</field>
        <field name="root_report_id" ref="account_reports.balance_sheet"/>
        <field name="filter_date_range" eval="False"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="country_id" ref="base.pl"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="l10n_pl_small_small_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_pl_small_bs_assets" model="account.report.line">
                <field name="name">Aktywa</field>
                <field name="code">pl_small_assets</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_bs_assets_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_assets_a.balance + pl_small_assets_b.balance + pl_small_assets_c.balance + pl_small_assets_d.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_bs_assets_a" model="account.report.line">
                        <field name="name">A. Aktywa trwałe</field>
                        <field name="code">pl_small_assets_a</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_small_bs_assets_a_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_small_assets_a_1.balance + pl_small_assets_a_2.balance + pl_small_assets_a_3.balance + pl_small_assets_a_4.balance + pl_small_assets_a_5.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_small_bs_assets_a_1" model="account.report.line">
                                <field name="name">I. Wartości niematerialne i prawne</field>
                                <field name="code">pl_small_assets_a_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">02 + 07.02</field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_a_2" model="account.report.line">
                                <field name="name">II. Rzeczowe aktywa trwałe</field>
                                <field name="code">pl_small_assets_a_2</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_assets_a_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_assets_a_2_1.balance + pl_small_assets_a_2_2.balance</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="l10n_pl_small_bs_assets_a_2_1" model="account.report.line">
                                        <field name="name">– środki trwałe</field>
                                        <field name="code">pl_small_assets_a_2_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">01 + 07.01</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_assets_a_2_2" model="account.report.line">
                                        <field name="name">– środki trwałe w budowie</field>
                                        <field name="code">pl_small_assets_a_2_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">08</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_a_3" model="account.report.line">
                                <field name="name">III. Należności długoterminowe</field>
                                <field name="code">pl_small_assets_a_3</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">06</field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_a_4" model="account.report.line">
                                <field name="name">IV. Inwestycje długoterminowe</field>
                                <field name="code">pl_small_assets_a_4</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_assets_a_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_assets_a_4_1.balance + pl_small_assets_a_4_2.balance + pl_small_assets_a_4_3.balance</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="l10n_pl_small_bs_assets_a_4_1" model="account.report.line">
                                        <field name="name">– nieruchomości</field>
                                        <field name="code">pl_small_assets_a_4_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">04.01 + 07.030.1</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_assets_a_4_2" model="account.report.line">
                                        <field name="name">– długoterminowe aktywa finansowe</field>
                                        <field name="code">pl_small_assets_a_4_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">03\(03.09) + 09</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_assets_a_4_3" model="account.report.line">
                                        <field name="name">– inne</field>
                                        <field name="code">pl_small_assets_a_4_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="True"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">03.09 + 04.02 + 07.030.2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_a_5" model="account.report.line">
                                <field name="name">V. Długoterminowe rozliczenia międzyokresowe</field>
                                <field name="code">pl_small_assets_a_5</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">05</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_small_bs_assets_b" model="account.report.line">
                        <field name="name">B. Aktywa obrotowe</field>
                        <field name="code">pl_small_assets_b</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_small_bs_assets_b_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_small_assets_b_1.balance + pl_small_assets_b_2.balance + pl_small_assets_b_3.balance + pl_small_assets_b_4.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_small_bs_assets_b_1" model="account.report.line">
                                <field name="name">I. Zapasy</field>
                                <field name="code">pl_small_assets_b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">60 + 62 + 3\(30.03,30.04,30.07)</field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_b_2" model="account.report.line">
                                <field name="name">II. Należności krótkoterminowe</field>
                                <field name="code">pl_small_assets_b_2</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_assets_b_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_assets_b_2_a.balance + pl_small_assets_b_2_b.balance</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="l10n_pl_small_bs_assets_b_2_a" model="account.report.line">
                                        <field name="name">a) z tytułu dostaw i usług</field>
                                        <field name="code">pl_small_assets_b_2_a</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">20.00 + 30.03</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_assets_b_2_b" model="account.report.line">
                                        <field name="name">b) inne</field>
                                        <field name="code">pl_small_assets_b_2_b</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="True"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">22.02 + 24.06 + 29.01 + 29.03 + 30.04 + 30.07 + 64.01 + 65.01</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_b_3" model="account.report.line">
                                <field name="name">III. Inwestycje krótkoterminowe</field>
                                <field name="code">pl_small_assets_b_3</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_assets_b_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_assets_b_3_a.balance</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="l10n_pl_small_bs_assets_b_3_a" model="account.report.line">
                                        <field name="name">a) krótkoterminowe aktywa finansowe</field>
                                        <field name="code">pl_small_assets_b_3_a</field>
                                        <field name="expression_ids">
                                            <record id="l10n_pl_small_bs_assets_b_3_a_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">pl_small_assets_b_3_a_1.balance + pl_small_assets_b_3_a_2.balance</field>
                                                <field name="date_scope">normal</field>
                                            </record>
                                        </field>
                                        <field name="children_ids">
                                            <record id="l10n_pl_small_bs_assets_b_3_a_1" model="account.report.line">
                                                <field name="name">– środki pieniężne w kasie i na rachunkach</field>
                                                <field name="code">pl_small_assets_b_3_a_1</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="account_codes_formula">1\(18)</field>
                                            </record>
                                            <record id="l10n_pl_small_bs_assets_b_3_a_2" model="account.report.line">
                                                <field name="name">- inne</field>
                                                <field name="code">pl_small_assets_b_3_a_2</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="True"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="account_codes_formula">24.010.2 + 24.020.1 + 24.07 + 24.08 + 24.09</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_assets_b_4" model="account.report.line">
                                <field name="name">IV. Krótkoterminowe rozliczenia międzyokresowe</field>
                                <field name="code">pl_small_assets_b_4</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">18</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_small_bs_assets_c" model="account.report.line">
                        <field name="name">C. Należne wpłaty na kapitał (fundu sz) podstawowy</field>
                        <field name="code">pl_small_assets_c</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">80.12</field>
                    </record>
                    <record id="l10n_pl_small_bs_assets_d" model="account.report.line">
                        <field name="name">D. Udziały (akcje) własne</field>
                        <field name="code">pl_small_assets_d</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">80.13</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_bs_passives" model="account.report.line">
                <field name="name">Pasywa</field>
                <field name="code">pl_small_passives</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_bs_passives_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_passives_a.balance + pl_small_passives_b.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_bs_passives_a" model="account.report.line">
                        <field name="name">A. Kapitał (fundusz) własny</field>
                        <field name="code">pl_small_passives_a</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_small_bs_passives_a_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_small_passives_a_1.balance + pl_small_passives_a_2.balance + pl_small_passives_a_3.balance + pl_small_passives_a_4.balance + pl_small_passives_a_5.balance + pl_small_passives_a_6.balance + pl_small_passives_a_7.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_small_bs_passives_a_1" model="account.report.line">
                                <field name="name">I. Kapitał (fundusz) podstawowy</field>
                                <field name="code">pl_small_passives_a_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-80.0</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_2" model="account.report.line">
                                <field name="name">II. Kapitał (fundusz) zapasowy</field>
                                <field name="code">pl_small_passives_a_2</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-81.1 - 81.4</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_3" model="account.report.line">
                                <field name="name">III. Kapitał (fundusz) z aktualizacji wyceny</field>
                                <field name="code">pl_small_passives_a_3</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-81.3</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_4" model="account.report.line">
                                <field name="name">IV. Pozostałe kapitały (fundusze) rezerwowe</field>
                                <field name="code">pl_small_passives_a_4</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-81.2</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_5" model="account.report.line">
                                <field name="name">V. Zysk (strata) z lat ubiegłych</field>
                                <field name="code">pl_small_passives_a_5</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-82</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_6" model="account.report.line">
                                <field name="name">VI. Zysk (strata) netto</field>
                                <field name="code">pl_small_passives_a_6</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_passives_a_6_balance_account_codes" model="account.report.expression">
                                        <field name="label">balance_account_codes</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-86</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_a_6_balance_aggregate" model="account.report.expression">
                                        <field name="label">balance_aggregate</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_pl_j.balance</field>
                                        <field name="subformula">cross_report</field>
                                        <field name="date_scope">from_fiscalyear</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_a_6_balance_unallocated" model="account.report.expression">
                                        <field name="label">balance_unallocated</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[
                                            '|', ('account_id.account_type', '=', 'income'),
                                            '|', ('account_id.account_type', '=', 'income_other'),
                                            '|', ('account_id.account_type', '=', 'expense'),
                                            '|', ('account_id.account_type', '=', 'expense_depreciation'),
                                                 ('account_id.account_type', '=', 'expense_direct_cost')
                                        ]"/>
                                        <field name="subformula">-sum</field>
                                        <field name="date_scope">to_beginning_of_fiscalyear</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_a_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_passives_a_6.balance_account_codes + pl_small_passives_a_6.balance_aggregate + pl_small_passives_a_6.balance_unallocated</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_a_7" model="account.report.line">
                                <field name="name">VII. Odpisy z zysku netto w ciągu roku obrotowego (wielkość ujemna)</field>
                                <field name="code">pl_small_passives_a_7</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-80.19</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_small_bs_passives_b" model="account.report.line">
                        <field name="name">B. Zobowiązania i rezerwy na zobowiązania</field>
                        <field name="code">pl_small_passives_b</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_small_bs_passives_b_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_small_passives_b_1.balance + pl_small_passives_b_2.balance + pl_small_passives_b_3.balance + pl_small_passives_b_4.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_small_bs_passives_b_1" model="account.report.line">
                                <field name="name">I. Rezerwy na zobowiązania</field>
                                <field name="code">pl_small_passives_b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-83</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_b_2" model="account.report.line">
                                <field name="name">II. Zobowiązania długoterminowe</field>
                                <field name="code">pl_small_passives_b_2</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-24.010.1</field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_b_3" model="account.report.line">
                                <field name="name">III. Zobowiązania krótkoterminowe</field>
                                <field name="code">pl_small_passives_b_3</field>
                                <field name="expression_ids">
                                    <record id="l10n_pl_small_bs_passives_b_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">pl_small_passives_b_3_a.balance + pl_small_passives_b_3_b.balance + pl_small_passives_b_3_c.balance + pl_small_passives_b_3_d.balance</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="l10n_pl_small_bs_passives_b_3_a" model="account.report.line">
                                        <field name="name">a) z tytułu kredytów i pożyczek</field>
                                        <field name="code">pl_small_passives_b_3_a</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">- 23.02 - 20.01 - 22\(22.02)</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_b_3_b" model="account.report.line">
                                        <field name="name">b) z tytułu dostaw i usług</field>
                                        <field name="code">pl_small_passives_b_3_b</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">-21</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_b_3_c" model="account.report.line">
                                        <field name="name">c) fundusze specjalne</field>
                                        <field name="code">pl_small_passives_b_3_c</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">-85</field>
                                    </record>
                                    <record id="l10n_pl_small_bs_passives_b_3_d" model="account.report.line">
                                        <field name="name">d) inne</field>
                                        <field name="code">pl_small_passives_b_3_d</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="True"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="account_codes_formula">-23\(23.02) - 24.03 - 24.05 - 24.020.2 - 28 - 29.02 - 64.02 - 65.02</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_pl_small_bs_passives_b_4" model="account.report.line">
                                <field name="name">IV. Rozliczenia międzyokresowe</field>
                                <field name="code">pl_small_passives_b_4</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-84</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
