# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_coda
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""
"\n"
"Bewegungsdatensätze der Art 2.%s werden nicht unterstützt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "1-st (recurrent)"
msgstr "Erste (wiederkehrend)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "ATM/POS debit"
msgstr "ATM/POS-Lastschrift"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Access right to database"
msgstr "Zugangsrechte für Datenbank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Agio on supplier's bill"
msgstr "Agio auf der Rechnung des Lieferanten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the bank"
msgstr "Von der Bank ausgewiesener Gesamtbetrag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the customer"
msgstr "Vom Kunden ausgewiesener Gesamtbetrag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Avgas"
msgstr "Avgas"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bancontact/Mister Cash"
msgstr "Bancontact/Mister Cash"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Bankauszug"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bank confirmation to revisor or accountant"
msgstr "Bankbestätigung an Revisor oder Buchhalter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bill claimed back"
msgstr "Rechnung zurückgefordert"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bills - calculation of interest"
msgstr "Rechnungen - Zinsberechnung"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr "CODA Notizen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""
"CODA V%s  Auszüge werden nicht unterstützt, bitte wenden Sie sich an Ihre "
"Bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA-Parsing-Fehler bei Informationsdatensatz 3.2, Sequenznummer%s! Bitte "
"melden Sie dieses Problem über Ihren Odoo-Supportkanal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA-Parsing-Fehler bei Informationsdatensatz 3.3, Sequenznummer %s ! Bitte "
"melden Sie dieses Problem über Ihren Odoo-Supportkanal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA-Parsing-Fehler bei Bewegungsdatensatz 2.2, Sequenznummer %s! Bitte "
"melden Sie dieses Problem über Ihren Odoo-Supportkanal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA-Parsing-Fehler bei Bewegungsdatensatz 2.3, Sequenznummer %s! Bitte "
"melden Sie dieses Problem über Ihren Odoo-Supportkanal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cancellation or correction"
msgstr "Annullierung oder Berichtigung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Capital and/or interest term investment"
msgstr "Kapital- und/oder Zinsanlagen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cards"
msgstr "Karten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash deposit at an ATM"
msgstr "Bargeldeinzahlung an einem Geldautomaten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash payment"
msgstr "Barzahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal"
msgstr "Bargeldabhebung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal by your branch or agents"
msgstr "Bargeldabhebung durch Ihre Filiale oder Vertreter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal from an ATM"
msgstr "Bargeldabhebung an einem Automaten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charge for safe custody"
msgstr "Gebühr für die Verwahrung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charging fees for transactions"
msgstr "Erhebung von Gebühren für Transaktionen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheque-related costs"
msgstr "Scheckbedingte Kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheques"
msgstr "Schecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing"
msgstr "Schließen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing (periodical settlements for interest, costs,...)"
msgstr "Abschluss (periodische Abrechnungen für Zinsen, Kosten,...)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Codes proper to each bank"
msgstr "Eigene Codes für jede Bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective payments of wages"
msgstr "Sammelzahlungen für Gehälter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfer"
msgstr "Sammelüberweisung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfers"
msgstr "Sammelüberweisungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial bills"
msgstr "Handelswechsel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial paper claimed back"
msgstr "Zurückgeforderte Handelspapiere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication"
msgstr "Kommunikation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication: "
msgstr "Kommunikation:"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communicaton"
msgstr "Mitteilung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Compensation for missing coupon"
msgstr "Entschädigung für fehlenden Coupon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Correction for prepaid card"
msgstr "Korrektur für Prepaid-Karte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs"
msgstr "Kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for holding a documentary cash credit"
msgstr "Kosten für die Führung eines Dokumentenakkreditivs"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for opening a bank guarantee"
msgstr "Kosten für die Einrichtung einer Bankgarantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for the safe custody of correspondence"
msgstr "Kosten für die sichere Verwahrung der Korrespondenz"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs related to commercial paper"
msgstr "Kosten im Zusammenhang mit Handelspapieren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to electronic output"
msgstr "Kosten im Zusammenhang mit der elektronischen Ausgabe"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to incoming foreign and non-SEPA transfers"
msgstr ""
"Kosten im Zusammenhang mit eingehenden Überweisungen aus dem Ausland und von"
" außerhalb des SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to outgoing foreign transfers and non-SEPA transfers"
msgstr ""
"Kosten im Zusammenhang mit ausgehenden Auslandsüberweisungen und Nicht-SEPA-"
"Überweisungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to payment of foreign cheques"
msgstr "Kosten im Zusammenhang mit der Einlösung von Auslandsschecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to the payment of a foreign bill"
msgstr ""
"Kosten im Zusammenhang mit der Begleichung eines ausländischen Wechsels"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party"
msgstr "Geschäftspartner"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Account"
msgstr "Konto des Geschäftspartners"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Address"
msgstr "Adresse des Geschäftspartners"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter transactions"
msgstr "Gegengeschäfte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Country code of the principal"
msgstr "Ländercode des Auftraggebers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit"
msgstr "Haben"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after Proton payments"
msgstr "Kredit nach Proton-Zahlungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after a payment at a terminal"
msgstr "Guthaben nach einer Zahlung an einem Terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after collection"
msgstr "Kredit nach Einzug"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit under usual reserve"
msgstr "Kredit unter der üblichen Rücklage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit-related costs"
msgstr "Kreditbezogene Kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Creditor’s identification code"
msgstr "Identifikationsnummer des Zahlungsempfängers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Currency"
msgstr "Währung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Damage relating to bills and cheques"
msgstr "Schäden in Bezug auf Wechsel und Schecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Department store cheque"
msgstr "Scheck eines Warenhauses"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail"
msgstr "Detail"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the bank"
msgstr "Detail des von der Bank zusammengefassten Betrags"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the customer"
msgstr "Detail des vom Kunden summierten Betrags"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Simple amount with detailed data"
msgstr "Detail des einfachen Betrags mit detaillierten Daten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Difference in payment"
msgstr "Unterschied in der Zahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct Debit scheme"
msgstr "Lastschriftverfahren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct debit"
msgstr "Lastschrift"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount abroad"
msgstr "Rabatt im Ausland"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount foreign supplier's bills"
msgstr "Rabatt auf Rechnungen ausländischer Lieferanten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary credit charges"
msgstr "Akkreditivgebühren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary export credits"
msgstr "Exportakkreditive"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary import credits"
msgstr "Importakkreditive"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic commercial paper"
msgstr "Inländische Handelspapiere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic or local SEPA credit transfers"
msgstr "Inländische oder lokale SEPA-Überweisungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Download of prepaid card"
msgstr "Download Prepaid-Karte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Drawing up a certificate"
msgstr "Erstellung einer Bescheinigung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Equivalent in EUR"
msgstr "Äquivalent in EUR"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Error"
msgstr "Fehler"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension"
msgstr "Erweiterung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension of maturity date"
msgstr "Fristverlängerung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fees and commissions"
msgstr "Gebühren und Provisionen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation"
msgstr "Zentralisierung der Finanzen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (credit)"
msgstr "Finanzielle Zentralisierung (Kredit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (debit)"
msgstr "Finanzielle Zentralisierung (Debit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralization"
msgstr "Finanzielle Zentralisierung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"First credit of cheques, vouchers, luncheon vouchers, postal orders, credit "
"under usual reserve"
msgstr ""
"Erste Gutschrift von Schecks, Gutscheinen, Essensgutscheinen, "
"Postanweisungen, Gutschrift unter üblicher Rücklage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – capital and interest"
msgstr "Fester Vorschuss - Kapital und Zinsen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – interest only"
msgstr "Fester Vorschuss - nur Zinsen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr "Ausländische Bankkonten mit BBAN-Struktur werden nicht unterstützt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr "Ausländische Bankkonten mit IBAN-Struktur werden nicht unterstützt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign cheques"
msgstr "Auslandsschecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign commercial paper"
msgstr "Ausländisches Handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward purchase of foreign exchange"
msgstr "Terminkauf von Devisen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward sale of foreign exchange"
msgstr "Devisentermingeschäfte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the currency of the account"
msgstr "Bruttobetrag in der Währung des Kontos"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the original currency"
msgstr "Bruttobetrag in der Originalwährung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Handling costs instalment credit"
msgstr "Bearbeitungskosten Teilzahlungskredit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Idem without guarantee"
msgstr "Idem ohne Garantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Income from payments by GSM"
msgstr "Einnahmen aus Zahlungen von GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order"
msgstr "Einzelüberweisung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order initiated by the bank"
msgstr "Individueller Überweisungsauftrag durch die Bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Information charges"
msgstr "Informationskosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Instant SEPA credit transfer"
msgstr "Sofortige SEPA-Überweisung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Insurance costs"
msgstr "Versicherungskosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interest term investment"
msgstr "Verzinsliche Kapitalanlage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interim interest on subscription"
msgstr "Vorläufige Zinsen auf die Zeichnung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "International credit transfers - non-SEPA credit transfers"
msgstr "Internationale Überweisungen - Nicht-SEPA-Überweisungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Issues"
msgstr "Probleme"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "LPG"
msgstr "LPG"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading GSM cards"
msgstr "Laden von GSM-Karten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading Proton"
msgstr "Proton laden"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading a GSM card"
msgstr "Laden einer GSM-Karte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Long-term loan"
msgstr "Langfristiges Darlehen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Maestro"
msgstr "Maestro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management fee"
msgstr "Verwaltungsgebühr"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management/custody"
msgstr "Management/Verwaltung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Mandate reference"
msgstr "Mandatsreferenz"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Masked PAN or card number"
msgstr "Maskierte PAN- oder Kartennummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Method of calculation (VAT, withholding tax on income, commission, etc.)"
msgstr ""
"Berechnungsmethode (Mehrwertsteuer, Quellensteuer auf Einkommen, Provisionen"
" usw.)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Miscellaneous fees and commissions"
msgstr "Verschiedene Gebühren und Provisionen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Name: {name}, Town: {city}"
msgstr "Name: {name}, Stadt: {city}"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Night safe"
msgstr "Nachttresor"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "No date"
msgstr "Kein Datum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Non-presented circular cheque"
msgstr "Nicht vorgelegter Zirkularscheck"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Number of the credit card"
msgstr "Nummer der Kreditkarte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Original amount of the transaction"
msgstr "Ursprünglicher Betrag der Transaktion"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other"
msgstr "Andere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other credit applications"
msgstr "Sonstige Kreditanträge"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "PAN or card number"
msgstr "PAN oder Kartennummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit - individual transaction"
msgstr "POS-Kredit - Einzeltransaktion"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit – Globalisation"
msgstr "POS-Gutschrift - Globalisierung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS number"
msgstr "POS-Nummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS others"
msgstr "POS andere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Paid or reason for refused payment"
msgstr "Bezahlt oder Grund für abgelehnte Zahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Partial payment subscription"
msgstr "Abonnement mit Teilzahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Participation in and management of interest refund system"
msgstr "Teilnahme an und Verwaltung des Zinsvergütungssystems"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Pay-packet charges"
msgstr "Pauschalgebühren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payable coupons/repayable securities"
msgstr "Zahlbare Kupons/rückzahlbare Wertpapiere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment"
msgstr "Zahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by GSM"
msgstr "Zahlung per GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card outside the Eurozone"
msgstr "Zahlung mit einer Kreditkarte außerhalb der Eurozone"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card within the Eurozone"
msgstr "Zahlung mit einer Kreditkarte innerhalb der Eurozone"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by your branch/agents"
msgstr "Zahlung durch Ihre Filiale/Vertreter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment commercial paper"
msgstr "Zahlung von Handelspapieren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment documents abroad"
msgstr "Zahlungsbelege im Ausland"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in advance"
msgstr "Vorauszahlung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in your favour"
msgstr "Zahlung zu Ihren Gunsten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment night safe"
msgstr "Zahlung Nachtresor"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of a foreign cheque"
msgstr "Einlösung eines Auslandsschecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Payment of coupons from a deposit or settlement of coupons delivered over "
"the counter - credit under usual reserve"
msgstr ""
"Auszahlung von Kupons aus einem Depot oder Abrechnung von außerbörslich "
"gelieferten Kupons - Gutschrift unter üblicher Reserve"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of foreign bill"
msgstr "Zahlung ausländischer Rechnungen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of voucher"
msgstr "Zahlung über Gutschein"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of wages, etc."
msgstr "Zahlung von Löhnen usw."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of your cheque"
msgstr "Zahlung Ihres Schecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment with tank card"
msgstr "Zahlung mit Tankkarte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Postage"
msgstr "Porto"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Printing of forms"
msgstr "Drucken von Formularen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Private"
msgstr "Privat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Provisionally unpaid"
msgstr "Vorläufig unbezahlt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of Smartcard"
msgstr "Einkauf einer Smartcard"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of an international bank cheque"
msgstr "Einkauf eines internationalen Bankschecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of fiscal stamps"
msgstr "Einkauf von Stempelmarken"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of foreign bank notes"
msgstr "Einkauf von ausländischen Banknoten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of gold/pieces"
msgstr "Einkauf von Gold/Stücken"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of petrol coupons"
msgstr "Einkauf von Tankgutscheinen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of securities"
msgstr "Einkauf von Sicherheiten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of traveller’s cheque"
msgstr "Einkauf von Reiseschecks"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Rate"
msgstr "Wechselkurs"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reason"
msgstr "Begründung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Registering compensation for savings accounts"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Regularisation costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement"
msgstr "Erstattung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of cheque-related costs"
msgstr "Erstattung von scheckbezogenen Kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of costs"
msgstr "Kostenerstattung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheque by your branch - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheques, vouchers, etc. credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper for discount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of guaranteed foreign supplier's bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill with guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill without guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of direct debit box"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of safes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Repayable securities from a deposit or delivered at the counter - credit "
"under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Research costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Retrocession of issue commission"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Return of an irregular bill of exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA B2B"
msgstr "SEPA B2B"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA Direct Debit"
msgstr "SEPA-Lastschrift"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA core"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of foreign bank notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of gold/pieces under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of traveller’s cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Second credit of unpaid cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Securities"
msgstr "Sicherheiten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Separately charged costs and provisions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement Date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement credit cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of bank acceptances"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of discount bank acceptance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of fixed advance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of instalment credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Share option plan – exercising an option"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Short-term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount with detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount without detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Special charge for safe custody"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split Transactions"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split collective payments for CODA files"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot purchase of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot sale of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Standing order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Structured format communication"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription to securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subsidy"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Surety fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "TINA"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tender"
msgstr "Kostenvoranschlag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tenders"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term Investments"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Terminal cash deposit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Trade information"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer"
msgstr "Transfer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer from your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour – initiated by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer to your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Travel insurance premium"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type Direct Debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of R transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of structured communication not supported: "
msgstr "Art der strukturierten Mitteilung nicht unterstützt:"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Undefined transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable reimbursement"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable transfer order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unloading Proton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid debt"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid postal order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unsupported bank account structure "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Upload of prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Value (date) correction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Various transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant fallen due"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Writ service fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Wrong CODA code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your certified cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue circular cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your purchase bank cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment hire-purchase and similar claims"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment instalment credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repurchase of issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount (equivalent in foreign currency)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount on which % is calculated"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount of the calculation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cancellation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "card scheme"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "company number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conformity code or blank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conventional maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative on network"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "currency"
msgstr "Währung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date"
msgstr "Datum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of issue of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor disagrees"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor’s account problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "diesel"
msgstr "Diesel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "distribution sector"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "domestic fuel oil"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "end date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in EUR"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in the currency of the account"
msgstr "Gegenwert in der Währung des Kontos"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "europremium"
msgstr "europremium"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "exchange rate"
msgstr "Wechselkurs"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "extension zone of account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "fuel"
msgstr "Benzin"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "guarantee number (no. allocated by the bank)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification number"
msgstr "Identifikationsnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification of terminal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rates, calculation basis"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "invoice number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "issuing institution"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "last (recurrent)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "lubricants"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "message (structured of free)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum"
msgstr "minimum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum not applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "new balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "nominal interest rate or rate of charge"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "old balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "one-off"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount"
msgstr "ursprünglicher Betrag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount (given by the customer)"
msgstr "ursprünglicher Betrag (vom Kunden angegeben)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "other types"
msgstr "andere Typen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "paid"
msgstr "bezahlt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "payment day"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percentage"
msgstr "Prozentsatz"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period from {} to {}"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "petrol"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium 99+"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium plus 98 oct"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium with lead substitute"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "product code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reason not specified"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "recurrent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction on credit account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "refund"
msgstr "Rückerstattung"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "regular unleaded"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reject"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "return"
msgstr "Enter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal of purchases"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of validation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "starting date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "technical problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "teledata"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "term in days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "terminal number"
msgstr "Terminalnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "transaction type"
msgstr "Transaktionstyp"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "undefined"
msgstr "nicht definiert"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unit price"
msgstr "Einzelpreis"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unset"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unspecified"
msgstr "nicht spezifiziert"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "validation date"
msgstr "Bestätigungsdatum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "volume"
msgstr "Lautstärke"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "withdrawal"
msgstr ""
