<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Onbording Screens -->
        <record model="hr.referral.onboarding" id="welcome1">
            <field name="text">Oh no!
Villains are lurking the city!
Help us recruit a team of superheroes to save the day!</field>
            <field name="sequence">1</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/villains.png"/>
        </record>
        <record model="hr.referral.onboarding" id="welcome2">
            <field name="text">Browse through open job positions, promote them on social media, or refer friends.</field>
            <field name="sequence">2</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/refer-friends.png"/>
        </record>
        <record model="hr.referral.onboarding" id="welcome3">
            <field name="text">Collect points and exchange them for awesome gifts in the shop.</field>
            <field name="sequence">3</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/points-gift.png"/>
        </record>
        <record model="hr.referral.onboarding" id="welcome4">
            <field name="text">Compete against your colleagues to build the best justice league!</field>
            <field name="sequence">4</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/leaderboard.png"/>
        </record>

        <!-- Referral Levels -->
        <record model="hr.referral.level" id="employee_referral_level_1">
            <field name="name">1</field>
            <field name="points">0</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/level-1.png"/>
        </record>
        <record model="hr.referral.level" id="employee_referral_level_2">
            <field name="name">2</field>
            <field name="points">50</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/level-2.png"/>
        </record>
        <record model="hr.referral.level" id="employee_referral_level_3">
            <field name="name">3</field>
            <field name="points">100</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/level-3.png"/>
        </record>
        <record model="hr.referral.level" id="employee_referral_level_4">
            <field name="name">4</field>
            <field name="points">350</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/level-4.png"/>
        </record>
        <record model="hr.referral.level" id="employee_referral_level_5">
            <field name="name">5</field>
            <field name="points">800</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/level-5.png"/>
        </record>

        <!-- Referral Friends -->
        <record model="hr.referral.friend" id="employee_referral_friend_1">
            <field name="name">Hero 1</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/mystic.png"/>
            <field name="image_head" type="base64" file="hr_referral/static/src/img/mystic-head.png"/>
        </record>
        <record model="hr.referral.friend" id="employee_referral_friend_2">
            <field name="name">Hero 2</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/aborigen.png"/>
            <field name="image_head" type="base64" file="hr_referral/static/src/img/aborigen-head.png"/>
        </record>
        <record model="hr.referral.friend" id="employee_referral_friend_3">
            <field name="name">Hero 3</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/cyborg.png"/>
            <field name="image_head" type="base64" file="hr_referral/static/src/img/cyborg-head.png"/>
        </record>
        <record model="hr.referral.friend" id="employee_referral_friend_4">
            <field name="name">Hero 4</field>
            <field name="position">front</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/dog.png"/>
            <field name="image_head" type="base64" file="hr_referral/static/src/img/dog-head.png"/>
        </record>
        <record model="hr.referral.friend" id="employee_referral_friend_5">
            <field name="name">Hero 5</field>
            <field name="image" type="base64" file="hr_referral/static/src/img/girl.png"/>
            <field name="image_head" type="base64" file="hr_referral/static/src/img/girl-head.png"/>
        </record>

        <!-- Recruitment Stage -->
        <record model="hr.recruitment.stage" id="hr_recruitment.stage_job1">
            <field name="use_in_referral">True</field>
        </record>
        <record model="hr.recruitment.stage" id="hr_recruitment.stage_job2">
            <field name="use_in_referral">True</field>
        </record>
        <record model="hr.recruitment.stage" id="hr_recruitment.stage_job3">
            <field name="use_in_referral">True</field>
        </record>
        <record model="hr.recruitment.stage" id="hr_recruitment.stage_job4">
            <field name="use_in_referral">True</field>
        </record>
        <record model="hr.recruitment.stage" id="hr_recruitment.stage_job5">
            <field name="use_in_referral">True</field>
        </record>
    </data>
    <data noupdate="0">
        <function model="res.company" name="_init_default_background">
            <value model="res.company" eval="obj().search([('hr_referral_background', '=', False)]).ids"/>
        </function>
    </data>
</odoo>
