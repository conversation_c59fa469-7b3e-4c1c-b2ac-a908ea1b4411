# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Khwu<PERSON><PERSON>awang <<EMAIL>>, 2022
# Odo<PERSON> Thaidev <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__signatures_count
msgid "# Signatures"
msgstr "# ลายเซ็น"

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-att-href=\"ctx.get('salary_package_url')\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>ขอแสดงความยินดี!</h2>\n"
"    คุณสามารถกำหนดค่าแพ็คเกจเงินเดือนของคุณโดยคลิกที่ลิงก์ด้านล่าง\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-att-href=\"ctx.get('salary_package_url')\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">กำหนดค่าแพ็คเกจของคุณ</a>\n"
"    </div>\n"
"</div>\n"
"        "

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr "<i class=\"fa fa-check-circle-o mr8\"/>ขอแสดงความยินดี"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/ เดือน</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/ ปี</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Salary Package Configurator</span>"
msgstr "<span class=\"o_form_label\">ตัวกำหนดค่าแพ็คเกจเงินเดือน</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text ml4\">Previous Contract</span>"
msgstr "<span class=\"o_stat_text ml4\">สัญญาก่อนหน้า</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr "<span class=\"o_stat_text\">สัญญา</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Reviews</span>"
msgstr "<span class=\"o_stat_text\">รีวิว</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid ""
"<span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('contract_id', '!=', False)]}\">An offer template need to be selected to have an offer link.</span>\n"
"                        <span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('display_warning_message', '=', False)]}\">An applicant name needs to be set to have an offer link.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<span class=\"text-muted mr4 ml4\">|</span>"
msgstr "<span class=\"text-muted mr4 ml4\">|</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "<span> /year</span>"
msgstr "<span> /ปี</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ เดือน</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>days / year</span>"
msgstr "<span>วัน / ปี</span>"

#. module: hr_contract_salary
#: model:ir.model.constraint,message:hr_contract_salary.constraint_hr_contract_salary_advantage_required_fold_res_field_id
msgid "A folded field is required"
msgstr "จำเป็นต้องมีฟิลด์พับ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token_end_date
msgid "Access Token Validity Date"
msgstr "วันหมดอายุโทเคนการเข้าถึง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__active
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation
msgid "Activity Creation"
msgstr "การสร้างกิจกรรม"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation_type
msgid "Activity Creation Type"
msgstr "ประเภทการสร้างกิจกรรม"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_type_id
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Add a line"
msgstr "เพิ่มไลน์"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Add a section"
msgstr "เพิ่มส่วน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_advantage.py:0
#, python-format
msgid "Advanges that are not linked to a field should always be displayed."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__advantage_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__advantage_ids
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Advantage"
msgstr "สิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__res_field_id
msgid "Advantage Field"
msgstr "ฟิลด์สิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__advantage_type_id
msgid "Advantage Type"
msgstr "ประเภทสิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__uom
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__uom
msgid "Advantage Unit of Measure"
msgstr "หน่วยวัดสิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_advantage_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_advantage
msgid "Advantages"
msgstr "สิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__always
msgid "Always Selected"
msgstr "เลือกไว้เสมอ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_employee_report__final_yearly_costs
msgid "Annual Employee Budget"
msgstr "งบประมาณประจำปีของพนักงาน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Annual Employer Cost"
msgstr "ต้นทุนประจำปีของนายจ้าง"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__applicant_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__applicant_id
msgid "Applicant"
msgstr "ผู้สมัคร"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__applies_on
msgid "Applies On"
msgstr "นำไปใช้เมื่อ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Apply Now"
msgstr "สมัครตอนนี้"

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_clean_redundant_salary_data_ir_actions_server
#: model:ir.cron,cron_name:hr_contract_salary.ir_cron_clean_redundant_salary_data
msgid "Archive/Delete redundant generated salary data"
msgstr "เก็บถาวร/ลบข้อมูลเงินเดือนที่สร้างซ้ำ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_responsible_id
msgid "Assigned to"
msgstr "มอบหมายให้"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_bachelor
msgid "Bachelor"
msgstr "ปริญญาตรี"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__bank_account
msgid "Bank Account"
msgstr "บัญชีธนาคาร"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_acc_number
msgid "Bank account"
msgstr "บัญชีธนาคาร"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_birthdate
msgid "Birthdate"
msgstr "วันเกิด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__category_id
msgid "Category"
msgstr "หมวดหมู่"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_certificate
msgid "Certificate"
msgstr "ใบรับรอง"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "Changes summary since previous contract :"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__checkbox
msgid "Checkbox"
msgstr "กล่องกาเครื่องหมาย"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__child_ids
msgid "Child"
msgstr "ย่อย"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation_type
msgid ""
"Choose whether to create a next activity each time that the advantage is "
"taken by the employee or on modification only."
msgstr ""
"เลือกว่าจะสร้างกิจกรรมถัดไปทุกครั้งที่พนักงานรับสิทธิประโยชน์หรือการปรับแต่งเท่านั้น"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_city
msgid "City"
msgstr "เมือง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "โค้ด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__color
msgid "Color"
msgstr "สี"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__company_id
msgid "Company"
msgstr "บริษัทเดียว"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage_type
msgid "Contract Advantage Type"
msgstr "ประเภทสิทธิประโยชน์ของสัญญา"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage_value
msgid "Contract Advantage Value"
msgstr "ค่าสิทธิประโยชน์ของสัญญา"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Contract Information:"
msgstr "ข้อมูลสัญญา:"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_start_date
msgid "Contract Start Date"
msgstr "วันที่เริ่มสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job__default_contract_id
msgid "Contract Template"
msgstr "เทมเพลตสัญญา"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
msgid "Contract Templates"
msgstr "เทมเพลตสัญญา"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Contract Type"
msgstr "ประเภทสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid "Contract Update Document Template"
msgstr "เทมเพลตการอัปเดตเอกสารสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__contract
msgid "Contract Value"
msgstr "ค่าสัญญา"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "รายงานการวิเคราะห์สัญญาและพนักงาน"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__res_field_id
msgid "Contract field linked to this advantage"
msgstr "ฟิลด์สัญญาที่เชื่อมโยงกับสิทธิประโยชน์นี้"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__cost_res_field_id
msgid ""
"Contract field linked to this advantage cost. If not set, the advantage "
"won't be taken into account when computing the employee budget."
msgstr ""
"ฟิลด์สัญญาที่เชื่อมโยงกับต้นทุนข้อได้เปรียบนี้ หากไม่ได้กำหนดไว้ "
"สิทธิประโยชน์จะไม่ถูกนำมาพิจารณาเมื่อคำนวณงบประมาณของพนักงาน"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__fold_res_field_id
msgid "Contract field used to fold this advantage."
msgstr "ฟิลด์สัญญาที่ใช้ในการพับสิทธิประโยชน์นี้"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__manual_res_field_id
msgid "Contract field used to manually encode an advantage value."
msgstr "ฟิลด์สัญญาที่ใช้ในการเข้ารหัสค่าสิทธิประโยชน์ด้วยตนเอง"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_history
msgid "Contract history"
msgstr "ประวัติสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation__countersigned
msgid "Contract is countersigned"
msgstr "ลงนามในสัญญาแล้ว"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contracts Reviews"
msgstr "การตรวจสอบสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__cost_res_field_id
msgid "Cost Field"
msgstr "ฟิลด์ต้นทุน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__cost_field
msgid "Cost Field Name"
msgstr "ชื่อฟิลด์ต้นทุน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__country
msgid "Countries"
msgstr "ประเทศ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__country_id
msgid "Country"
msgstr "ประเทศ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Country of Birth"
msgstr "ประเทศที่เกิด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__hash_token
msgid "Created From Token"
msgstr "สร้างจากโทเคน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__currency_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__currency
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__currency
msgid "Currency"
msgstr "สกุลเงิน"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr "ปรับแต่งเงินเดือนของคุณ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__date
msgid "Date"
msgstr "วันที่"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__days
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__days
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_advantages
#, python-format
msgid "Days"
msgstr "วัน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__access_token_validity
msgid "Default Access Token Validity Duration"
msgstr "ระยะเวลาที่ถูกต้องของโทเคนการเข้าถึงเริ่มต้น"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job__default_contract_id
msgid ""
"Default contract used to generate an offer. If empty, advantages will be "
"taken from current contract of the employee/nothing for an applicant."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr "สัญญาเริ่มต้นที่ใช้เมื่อยื่นข้อเสนอให้กับผู้สมัคร"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr "เอกสารเริ่มต้นที่ผู้สมัครจะต้องเซ็นเพื่อรับข้อเสนอสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr "เอกสารเริ่มต้นที่พนักงานจะต้องลงนามเพื่ออัปเดตสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__department_id
msgid "Department"
msgstr "แผนก"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#, python-format
msgid "Details"
msgstr "รายละเอียด"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_type
msgid "Display Type"
msgstr "ประเภทการแสดง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__display_warning_message
msgid "Display Warning Message"
msgstr "แสดงข้อความเตือน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_doctor
msgid "Doctor"
msgstr "ปริญญาเอก"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__document
msgid "Document"
msgstr "เอกสาร"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "เอกสารที่จะเซ็น"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"เอกสารที่จะลงนาม เลือกได้เฉพาะเอกสารที่มีผู้รับผิดชอบ 1 หรือ 2 ฝ่ายเท่านั้น\n"
"        เอกสารที่มีผู้รับผิดชอบ 1 คนจะต้องลงนามโดยพนักงานเท่านั้นในขณะที่เอกสารที่มีผู้รับผิดชอบต่างกัน 2 คนจะต้องลงนามโดยทั้งพนักงานและผู้รับผิดชอบ\n"
"        "

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__dropdown
msgid "Dropdown"
msgstr "ดรอปดาว์น"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__dropdown-group
msgid "Dropdown Group"
msgstr "กลุ่มดรอปดาว์น"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_email
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__email
msgid "Email"
msgstr "อีเมล"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__email_to
msgid "Email To"
msgstr "ส่งอีเมลถึง"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__sign_copy_partner_id
msgid "Email address to which to transfer the signature."
msgstr "ที่อยู่อีเมลที่จะโอนลายเซ็น"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__employee
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_contract_id
msgid "Employee Contract"
msgstr "สัญญาของพนักงาน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Employee Name"
msgstr "ชื่อพนักงาน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_image_1920
msgid "Employee Photo"
msgstr "รูปถ่ายพนักงาน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation__running
msgid "Employee signs his contract"
msgstr "พนักงานลงนามในสัญญา"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer
msgid "Employee: Contract And Salary Package"
msgstr "พนักงาน: แพ็คเกจสัญญาและเงินเดือน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"Equals to the sum of the following values:\n"
"\n"
"%s"
msgstr ""
"เท่ากับผลรวมของค่าต่อไปนี้:\n"
"\n"
"%s"

#. module: hr_contract_salary
#: model:hr.contract.salary.advantage,name:hr_contract_salary.advantage_extra_time_off
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__holidays
msgid "Extra Time Off"
msgstr "การลาพิเศษ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_female
msgid "Female"
msgstr "หญิง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__field
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__field
msgid "Field Name"
msgstr "ชื่อฟิลด์"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_field
msgid "Field of Study"
msgstr "สาขาวิชา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__fixed_value
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__fixed
msgid "Fixed Value"
msgstr "ค่าคงที่"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_field
msgid "Fold Field Name"
msgstr "ชื่อฟิลด์พับ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_label
msgid "Fold Label"
msgstr "ป้ายที่พับ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_res_field_id
msgid "Fold Res Field"
msgstr "ฟิลด์ Res ที่พับ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__folded
msgid "Folded"
msgstr "พับ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_gender
msgid "Gender"
msgstr "เพศ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Generate"
msgstr "สร้าง"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Generate Offer"
msgstr "สร้างข้อเสนอ"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_generate_simulation_link
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Generate Simulation Link"
msgstr "สร้างลิงก์จำลอง"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.generate_offer_link_action
#: model:ir.actions.act_window,name:hr_contract_salary.generate_simulation_link_action
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Generate a Simulation Link"
msgstr "สร้างลิงก์จำลอง"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__color__green
msgid "Green"
msgstr "สีเขียว"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
#, python-format
msgid ""
"HR Responsible %s should be a User of Sign and have a valid email address "
"when New Contract Document Template is specified"
msgstr ""
"%s "
"ที่รับผิดชอบด้านทรัพยากรบุคคลควรเป็นผู้ใช้ที่ลงชื่อและมีที่อยู่อีเมลที่ถูกต้องเมื่อมีการระบุเทมเพลตเอกสารสัญญาใหม่"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__helper
msgid "Helper"
msgstr "ผู้ช่วย"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Hide <i class=\"fa fa-chevron-up\"/>"
msgstr "ซ่อน <i class=\"fa fa-chevron-up\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide Children"
msgstr "ซ่อนรายย่อย"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__hide_description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__hide_description
msgid "Hide Description"
msgstr "ซ่อนรายละเอียด"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide children personal info when checked."
msgstr "ซ่อนข้อมูลย่อยส่วนบุคคลเมื่อเลือก"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__hide_description
msgid "Hide the description if the advantage is not taken."
msgstr "ซ่อนรายละเอียดถ้าไม่มีการรับ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__id
msgid "ID"
msgstr "ไอดี"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__icon
msgid "Icon"
msgstr "ไอคอน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid "Impacts Monthly Total"
msgstr "ผลกระทบรายเดือนรวม"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__impacts_net_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid "Impacts Net Salary"
msgstr "ผลกระทบเงินเดือนสุทธิ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__info_type_id
msgid "Info Type"
msgstr "ประเภทข้อมูล"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "Information"
msgstr "ข้อมูล"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__integer
msgid "Integer"
msgstr "ตัวเลขจำนวนเต็ม"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__is_required
msgid "Is Required"
msgstr "จำเป็น"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__is_origin_contract_template
msgid "Is origin contract a contract template ?"
msgstr "สัญญาต้นทางเป็นเทมเพลตสัญญาหรือไม่?"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model,name:hr_contract_salary.model_hr_job
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_job_id
#, python-format
msgid "Job Position"
msgstr "ตำแหน่งงาน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__job_title
#, python-format
msgid "Job Title"
msgstr "ชื่อตำแหน่งงาน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__lang
msgid "Languages"
msgstr "ภาษา"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Let's create one"
msgstr "มาสร้างกันเถอะ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__display_type__line
msgid "Line"
msgstr "ไลน์"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__left
msgid "Main Panel"
msgstr "แผงหลัก"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_male
msgid "Male"
msgstr "ชาย"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__manual_field
msgid "Manual Field Name"
msgstr "ชื่อฟิลด์ด้วยตนเอง"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__manual
msgid "Manual Input"
msgstr "การนำเข้าด้วยตนเอง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__manual_res_field_id
msgid "Manual Res Field"
msgstr "ฟิลด์ Res ด้วยตัวเอง"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_master
msgid "Master"
msgstr "ปริญญาโท"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_personal_info.py:0
#, python-format
msgid ""
"Mismatch between res_field_id %(field)s and model %(model)s for info "
"%(personal_info)s"
msgstr ""
"ไม่ตรงกันระหว่าง res_field_id %(field)s และโมเดล%(model)s สำหรับข้อมูล "
"%(personal_info)s"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_type__periodicity__monthly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__monthly
msgid "Monthly"
msgstr "รายเดือน"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Cost"
msgstr "ค่าใช้จ่ายรายเดือน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Monthly Cost (Real)"
msgstr "ค่าใช้จ่ายรายเดือน (ตามจริง)"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Monthly Gross Salary"
msgstr "เงินเดือนรวมรายเดือน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__monthly_total
msgid "Monthly Total"
msgstr "รายเดือนรวม"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "ค่าแรงรายเดือน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__name
msgid "Name"
msgstr "ชื่อ"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Name of the field related to this personal info."
msgstr "ชื่อของฟิลด์ที่เกี่ยวข้องกับข้อมูลส่วนบุคคลนี้"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "National Identification Number"
msgstr "หมายเลขประจำตัวประชาชน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_id
msgid "Nationality"
msgstr "สัญชาติ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr "การคำนวณสุทธิ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid "New Contract Document Template"
msgstr "เทมเพลตเอกสารสัญญาใหม่"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "No"
msgstr "ไม่"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr ""
"ไม่มี ฝ่ายความรับผิดชอบ HR ที่กำหนดไว้ในตำแหน่งงาน โปรดติดต่อผู้ดูแลระบบ"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "No Template found"
msgstr "ไม่พบเทมเพลต"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
#, python-format
msgid "No private address defined on the employee!"
msgstr "ไม่มีที่อยู่ส่วนตัวที่กำหนดไว้สำหรับพนักงาน!"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"No signature template defined on the contract. Please contact the HR "
"responsible."
msgstr "ลิงก์นี้ไม่ถูกต้อง โปรดติดต่อ ฝ่ายรับผิดชอบ HR เพื่อขอใหม่..."

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Not a valid e-mail address"
msgstr "ไม่ใช่ที่อยู่อีเมลที่ถูกต้อง"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Not a valid input in integer field"
msgstr "ไม่มีการนำเข้าที่ถูกต้องในฟิลด์จำนวนเต็ม"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__holidays
msgid "Number of days of paid leaves the employee gets per year."
msgstr "จำนวนวันลาที่ได้รับค่าจ้างซึ่งพนักงานได้รับต่อปี"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_id
msgid "Offer Template"
msgstr "เทมเพลตข้อเสนอ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__url
msgid "Offer link"
msgstr "ลิงก์ข้อเสนอ"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/wizard/generate_simulation_link.py:0
#, python-format
msgid "Offer link can not be send. The applicant needs to have a name."
msgstr "ไม่สามารถส่งลิงก์ข้อเสนอได้ ผู้สมัครจะต้องมีชื่อ"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Oops"
msgstr "อุ๊ปส์"

#. module: hr_contract_salary
#: model:sign.template,redirect_url_text:hr_contract_salary.sign_template_cdi_developer
msgid "Open Link"
msgstr "ลิงก์ที่เปิด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "Origin Contract"
msgstr "ที่มาสัญญา"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Original Link"
msgstr "ลิงก์ดั่งเดิม"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_other
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_other
msgid "Other"
msgstr "อื่น ๆ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__parent_id
msgid "Parent"
msgstr "หลัก"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__percent
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__percent
msgid "Percent"
msgstr "เปอร์เซ็นต์"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__periodicity
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__periodicity
msgid "Periodicity"
msgstr "เป็นระยะ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_contact
msgid "Person to call"
msgstr "บุคคลที่จะโทร"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Personal Documents"
msgstr "เอกสารส่วนตัว"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_personal_info_action
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__personal_info_id
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_personal_info
msgid "Personal Info"
msgstr "ข้อมูลส่วนตัว"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Personal Information"
msgstr "ข้อมูลส่วนบุคคล"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_phone
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_phone
msgid "Phone number"
msgstr "เบอร์โทรศัพท์"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_place_of_birth
msgid "Place of Birth"
msgstr "สถานที่เกิด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__placeholder
msgid "Placeholder"
msgstr "ตัวอย่างข้อความ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__position
msgid "Position"
msgstr "ตำแหน่ง"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Previous Contract"
msgstr "สัญญาก่อนหน้า"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__address
msgid "Private Home Address"
msgstr "ที่อยู่บ้านส่วนตัว"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr "สัญญาที่เสนอ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_reviews_count
msgid "Proposed Contracts Count"
msgstr "จำนวนสัญญาที่เสนอ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__radio
msgid "Radio"
msgstr "คลื่นวิทยุ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__radio
msgid "Radio Buttons"
msgstr "ปุ่มวิทยุ"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#, python-format
msgid "Recompute"
msgstr "คำนวนใหม่"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer_applicant
msgid "Recruitment: Your Salary Package"
msgstr "การสรรหาบุคลากร: แพ็คเกจเงินเดือนของคุณ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__color__red
msgid "Red"
msgstr "สีแดง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Related Field"
msgstr "ฟิลด์ที่เกี่ยวข้อง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__requested_documents_field_ids
msgid "Requested Documents"
msgstr "เอกสารที่ร้องขอ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__requested_documents
msgid "Requested Documents Fields"
msgstr "ฟิลด์เอกสารที่ร้องขอ"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_model
msgid "Res Model"
msgstr "โมเดล Res"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_resume_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_resume
msgid "Resume"
msgstr "ประวัติย่อ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Review Contract &amp; Sign"
msgstr "ตรวจสอบสัญญาและเซ็น"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_payroll_structure_type__salary_advantage_ids
msgid "Salary Advantage"
msgstr "สิทธิประโยชน์เงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_tree
msgid "Salary Package Advantage"
msgstr "สิทธิประโยชน์ของแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_menu
msgid "Salary Package Configurator"
msgstr "ตัวกำหนดค่าแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_tree
msgid "Salary Package Personal Info"
msgstr "ข้อมูลส่วนตัวของแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_type
msgid "Salary Package Personal Info Type"
msgstr "ประเภทข้อมูลส่วนตัวของแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_value
msgid "Salary Package Personal Info Value"
msgstr "ค่าข้อมูลส่วนตัวของแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr "ประวัติย่อแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume_category
msgid "Salary Package Resume Category"
msgstr "หมวดหมู่ประวัติย่อแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Salary Package Summary"
msgstr "สรุปแพ็คเกจเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__structure_type_id
msgid "Salary Structure Type"
msgstr "ประเภทโครงสร้างเงินเดือน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_school
msgid "School"
msgstr "โรงเรียน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__display_type__section
msgid "Section"
msgstr "ส่วน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token
msgid "Security Token"
msgstr "โทเคนความปลอดภัย"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__dropdown
msgid "Selection"
msgstr "การเลือก"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__dropdown_selection
msgid "Selection Nature"
msgstr "ธรรมชาติการเลือก"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_copy_partner_id
msgid "Send a copy to"
msgstr "ส่งสำเนาถึง"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer_applicant
msgid "Sent automatically when you generate an offer for an application"
msgstr "ส่งโดยอัตโนมัติเมื่อคุณสร้างข้อเสนอสำหรับแอปพลิเคชัน"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer
msgid ""
"Sent manually when you generate a simulation link on the employee contract"
msgstr "ส่งด้วยตนเองเมื่อคุณสร้างลิงก์จำลองในสัญญาพนักงาน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Show <i class=\"fa fa-chevron-down\"/>"
msgstr "แสดง <i class=\"fa fa-chevron-down\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__right
msgid "Side Panel"
msgstr "แผงด้านข้าง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_frenquency
msgid "Sign Frenquency"
msgstr "ความถี่ในการลงนาม"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "ลงนามในเอกสารสัญญา"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "คำขอลงนาม - %s"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__slider
msgid "Slider"
msgstr "ตัวเลื่อน"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__slider_max
msgid "Slider Max"
msgstr "ตัวเลื่อนสูงสุด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__slider_min
msgid "Slider Min"
msgstr "ตัวเลื่อนต่ำสุด"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Some required fields are not filled"
msgstr "ช่องที่ต้องกรอกบางช่องไม่ได้กรอก"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__specific
msgid "Specific Values"
msgstr "ค่าเฉพาะ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_state_id
msgid "State"
msgstr "รัฐ"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__state
msgid "States"
msgstr "รัฐ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street
msgid "Street"
msgstr "ถนน"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street2
msgid "Street 2"
msgstr "ถนน 2"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Structure Type"
msgstr "ประเภทโครงสร้าง"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__sum
msgid "Sum of Advantages Values"
msgstr "ผลรวมของค่าสิทธิประโยชน์"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_template_id
msgid "Template to Sign"
msgstr "เทมเพลตที่จะลงนาม"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Templates"
msgstr "เทมเพลต"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__text
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__text
msgid "Text"
msgstr "ข้อความ"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation
msgid ""
"The benefit is created when the employee signs his contract at the end of "
"the salary configurator or when the HR manager countersigns the contract."
msgstr ""
"ผลประโยชน์ถูกสร้างขึ้นเมื่อพนักงานลงนามในสัญญาเมื่อสิ้นสุดการกำหนดค่าเงินเดือนหรือเมื่อผู้จัดการฝ่ายทรัพยากรบุคคลลงนามในสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr "สัญญาที่สัญญานี้ถูกทำซ้ำ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid ""
"The employee does not have a valid work email set. The Simulation Link won't"
" be able to be completed."
msgstr ""
"พนักงานไม่มีชุดอีเมลที่ทำงานที่ถูกต้อง "
"ลิงก์การจำลองจะไม่สามารถดำเนินการให้เสร็จสมบูรณ์ได้"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"The employee is not linked to an existing user, please contact the "
"administrator.."
msgstr "พนักงานไม่ได้เชื่อมโยงกับผู้ใช้ที่มีอยู่ โปรดติดต่อผู้ดูแลระบบ"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_advantage.py:0
#, python-format
msgid ""
"The minimum value for the slider should be inferior to the maximum value."
msgstr "ค่าต่ำสุดสำหรับตัวเลื่อนควรน้อยกว่าค่าสูงสุด"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__signatures_count
msgid "The number of signatures on the pdf contract with the most signatures."
msgstr "จำนวนลายเซ็นในสัญญา pdf ที่มีลายเซ็นมากที่สุด"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_type_id
msgid ""
"The type of activity that will be created automatically on the contract if "
"this advantage is chosen by the employee."
msgstr ""
"ประเภทของกิจกรรมที่จะสร้างขึ้นโดยอัตโนมัติในสัญญาถ้าพนักงานเลือกสิทธิประโยชน์นี้"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_advantages
msgid "There is no available option to customize your salary"
msgstr "ไม่มีตัวเลือกในการปรับแต่งเงินเดือนของคุณ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"They will review your contract.<br/> Feel free to contact them if you have "
"any questions."
msgstr "พวกเขาจะตรวจสอบสัญญาของคุณ <br/>โปรดติดต่อพวกเขาหากคุณมีคำถาม"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "This contract has been updated, please request an updated link.."
msgstr "สัญญานี้ได้รับการอัปเดตแล้ว โปรดร้องขอลิงก์ที่อัปเดตแล้ว"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"This link is invalid. Please contact the HR Responsible to get a new one..."
msgstr "ลิงก์นี้ไม่ถูกต้อง โปรดติดต่อ ฝ่ายรับผิดชอบ HR เพื่อขอใหม่..."

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Total real monthly cost of the employee for the employer."
msgstr "ค่าใช้จ่ายรายเดือนจริงทั้งหมดของพนักงานสำหรับนายจ้าง"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Total real yearly cost of the employee for the employer."
msgstr "ค่าใช้จ่ายจริงประจำปีทั้งหมดของพนักงานสำหรับนายจ้าง"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
msgid "Total yearly cost of the employee for the employer."
msgstr "รวมค่าใช้จ่ายประจำปีของพนักงานสำหรับนายจ้าง"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr "ระยะเวลาที่มีผลบังคับใช้สำหรับการขอแพ็คเกจเงินเดือนสำหรับผู้สมัครใหม่"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__value
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__value
msgid "Value"
msgstr "ค่า"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__value_type
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Value Type"
msgstr "ประเภทค่า"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Wage"
msgstr "ค่าแรง"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on Payroll"
msgstr "ค่าจ้างตามเงินเดือน"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on contract signature"
msgstr "ค่าแรงในการลงนามในสัญญา"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
msgid "Wage update with holidays retenues"
msgstr "อัปเดตค่าแรงพร้อมรายได้ช่วงวันหยุดยาว"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_with_holidays
msgid "Wage with Holidays"
msgstr "ค่าแรงพร้อมวันหยุด"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation_type__onchange
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__sign_frenquency__always
msgid "When the advantage is modified"
msgstr "เมื่อสิทธิประโยชน์ถูกปรับเปลี่ยน"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation_type__always
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__sign_frenquency__onchange
msgid "When the advantage is set"
msgstr "เมื่อสิทธิประโยชน์ถูกตั้งค่า"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_type__periodicity__yearly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__yearly
msgid "Yearly"
msgstr "รายปี"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__final_yearly_costs
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Yearly Cost"
msgstr "ต้นทุนรายปี"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Yearly Cost (Real)"
msgstr "ต้นทุนรายปี (ตามจริง)"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Yes"
msgstr "ใช่"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Your Personal Information"
msgstr "ข้อมูลส่วนบุคคลของคุณ"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr "สัญญาของคุณถูกส่งไปยัง:"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_zip
msgid "Zip Code"
msgstr "รหัสไปรษณีย์"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "close"
msgstr "ปิด"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_contract_employee_id
msgid "contract employee"
msgstr "พนักงานสัญญาจ้าง"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "days"
msgstr "วัน"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "e.g. Birthdate"
msgstr "เช่น วันที่เกิด"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "e.g. Meal Vouchers"
msgstr "เช่น บัตรกำนัลอาหาร"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer_applicant
msgid "{{ object.company_id.name }} : Job Offer - {{ object.name }}"
msgstr "{{ object.company_id.name }} : ข้อเสนองาน - {{ object.name }}"
