// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_CallParticipantCard {
    aspect-ratio: 16/9;
}

.o_CallParticipantCard_avatarImage {
    max-height: #{"min(100%, 100px)"}; // interpolated as not supported by Sass
    max-width: #{"min(100%, 100px)"};
    aspect-ratio: 1;
    border: solid $gray-500;

    &.o-isTalking {
        border: solid darken($o-enterprise-primary-color, 5%);
    }

    &.o-isInvitation:not(:hover) {
        animation: o_CallParticipantCard_avatarImag_borderPulse 3s linear infinite;
    }

    &.o-isInvitation:hover {
        border: solid map-get($theme-colors, 'danger');
    }
}

@keyframes o_CallParticipantCard_avatarImag_borderPulse {
    0% { border: solid white }
    20% { border: solid $gray-600 }
    35% { border: solid $gray-100 }
    50% { border: solid $gray-600 }
    70% { border: solid $gray-100 }
    85% { border: solid $gray-700 }
}

.o_CallParticipantCard_overlay {
    pointer-events: none;
    margin: Min(5%, map-get($spacers, 2));
}

.o_CallParticipantCard_overlayBottom {
    max-width: 50%;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_CallParticipantCard {
    &.o-isTalking {
        box-shadow: inset 0 0 0 map-get($spacers, 1) darken($o-enterprise-primary-color, 5%);
    }
}

.o_CallParticipantCard_liveIndicator {
    user-select: none;
}

.o_CallParticipantCard_avatarFrame:not(.o-isMinimized) {
    background-color: var(--CallParticipantCard_avatarFrame-background-color, #{$o-gray-700});
}
