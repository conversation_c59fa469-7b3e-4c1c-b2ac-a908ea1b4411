# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <mika<PERSON>.<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# Lasse L, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:41+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Lasse L, 2023\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid "%(project_name)s's Documents"
msgstr "%(project_name)s:s dokument"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"En uppsättning villkor och åtgärder som kommer att vara tillgängliga för "
"alla bilagor som matchar villkoren"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_ask_for_validation
msgid "Ask for Validation"
msgstr "Be om validering"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Bilaga"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Kategorisera och dela dina dokument med dina kunder"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_facet
msgid "Category"
msgstr "Kategori"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Skapa"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Skapa en uppgift"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Standardetiketter"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_deprecate
msgid "Deprecate"
msgstr "Ogilla"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_deprecated
msgid "Deprecated"
msgstr "Avslutad"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
#: model:ir.model.fields,field_description:documents_project.field_ir_attachment__document_ids
msgid "Document"
msgstr "Dokument"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
#, python-format
msgid "Documents"
msgstr "Dokument"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Dokument &amp; Analys"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_folder
msgid "Documents Workspace"
msgstr "Arbetsområde för dokument"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_draft
msgid "Draft"
msgstr "Utkast"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__is_shared
msgid "Is Shared"
msgstr "Är delad"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_mark_as_draft
msgid "Mark As Draft"
msgstr "Markera som utkast"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "Merged Workspace"
msgstr "Sammanslagen arbetsyta"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "New task from Documents"
msgstr "Ny uppgift från Dokument"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__document_count
msgid "Number of documents in Project"
msgstr "Antal dokument i projektet"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_count
msgid "Number of documents in Task"
msgstr "Antal dokument i uppgiften"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_documents_folder__project_ids
msgid "Project"
msgstr "Projekt"

#. module: documents_project
#: model:documents.folder,name:documents_project.documents_project_folder
msgid "Projects"
msgstr "Projekt"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_ids
#: model:ir.model.fields,field_description:documents_project.field_project_task__shared_document_ids
msgid "Shared Documents"
msgstr "Delade dokument"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_count
msgid "Shared Documents Count"
msgstr "Antal delade dokument"

#. module: documents_project
#: model:documents.facet,name:documents_project.documents_project_status
msgid "Status"
msgstr "Status"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Aktivitet"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "Task created from document"
msgstr "Uppgift skapad från dokument"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr "Arbetsytan \"%s\" behövs för projektansökan och kan inte tas bort."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"The \"%s\" workspace should either be in the \"%s\" company like this "
"project or be open to all companies."
msgstr ""
"Arbetsytan \"%s\" bör antingen vara i \"%s\"-företaget som i detta projekt "
"eller vara öppen för alla företag."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the \"%s\" project to "
"which it is linked. Please update the company of the \"%s\" project, or "
"leave the company of this workspace empty."
msgstr ""
"Denna arbetsyta bör vara kvar i samma företag som det \"%s\" projekt som den"
" är länkad till. Vänligen uppdatera företaget för \"%s\"-projektet, eller "
"lämna företaget för denna arbetsyta tomt."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Denna arbetsyta bör finnas kvar i samma företag som följande projekt som den är kopplad till:\n"
"%s\n"
"\n"
"Vänligen uppdatera företaget för dessa projekt, eller lämna företaget för denna arbetsyta tomt."

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_to_validate
msgid "To Validate"
msgstr "Att granska"

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Ladda upp <span class=\"fw-normal\">en fil eller </span>dra <span "
"class=\"fw-normal\">den hit.</span>"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__project_use_documents
msgid "Use Documents"
msgstr "Använda dokument"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_validate
msgid "Validate"
msgstr "Bekräfta"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_validated
msgid "Validated"
msgstr "Bekräftat"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,field_description:documents_project.field_project_task__documents_folder_id
msgid "Workspace"
msgstr "Arbetsyta"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Workspace in which all of the documents of this project will be categorized."
" All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Arbetsyta där alla dokument i detta projekt kommer att kategoriseras. Alla "
"bilagor till dina uppgifter läggs automatiskt till som dokument i den här "
"arbetsytan."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%s\" company:\n"
"%s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%s\" workspace blank."
msgstr ""
"Du kan inte ändra företaget för detta projekt, eftersom dess arbetsyta är länkad till de andra följande projekten som fortfarande är i företaget \"%s\":\n"
"%s\n"
"\n"
"Uppdatera företaget för alla projekt så att de ligger kvar i samma företag som sin arbetsyta, eller lämna företaget för arbetsytan \"%s\" tomt."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "You cannot set a company on the Projects workspace."
msgstr "Du kan inte ange ett företag på arbetsytan Projekt."

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.share_page
msgid "documents shared"
msgstr "delade dokument"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "new %s from %s"
msgstr "ny %s från %s"
