# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_be_coda
#
# Translators:
# <PERSON> <david<PERSON><EMAIL>>, 2016
# <PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-01-29 15:25+0000\n"
"PO-Revision-Date: 2016-03-03 07:51+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Mexico) (http://www.transifex.com/odoo/odoo-9/"
"language/es_MX/)\n"
"Language: es_MX\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr "Notas CODA"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""
"Declaraciones CODA V%s no son compatibles, por favor póngase en contacto con "
"su banco"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA error de análisis en la información de registro de datos 3.2, seq nr "
"%s! Por favor, informe este tema a través de su canal de soporte Odoo."

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA error de análisis en la información de registro de datos 3.3, seq nr "
"%s! Por favor, informe este tema a través de su canal de soporte Odoo."

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"¡Análisis de error CODA en el registro de datos de movimiento 2.3, seq nr "
"%s! Por favor, informe este tema a través de su canal de soporte Odoo."

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "Counter Party"
msgstr "Contraparte"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "Counter Party Address"
msgstr "Dirección de contraparte"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr "Cuentas de bancos extranjeros con estructura BBAN no son compatibles"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr "Cuentas de banco extranjero con la estructura IBAN no están soportadas"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Importar Estado de Cuenta de Banco"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:0
#, python-format
msgid "Unsupported bank account structure "
msgstr "Estructura de la cuenta bancaria no compatible"
