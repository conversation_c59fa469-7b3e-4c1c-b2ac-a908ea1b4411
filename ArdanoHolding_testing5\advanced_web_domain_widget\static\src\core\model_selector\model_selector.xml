<?xml version="1.0" encoding="utf-8"?>
<templates>
    <t t-name="advanced_web_domain_widget.ModelSelectorBits" owl="1">
        <div class="o_sp_input_dropdown" t-ref="autocomplete_container">
            <input t-if="env.isSmall"
                type="text"
                class="o_input"
                readonly=""
                t-att-value="props.value"
            />
            <AutoComplete t-else=""
                value="props.value || ''"
                sources="sources"
                placeholder="placeholder"
                autoSelect="props.autoSelect"
                onSelect.bind="onSelect"
            /> 
            <a role="button" class="o_dropdown_button" draggable="false" />
        </div>
    </t>
</templates>
