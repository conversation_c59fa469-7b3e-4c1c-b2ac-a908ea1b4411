# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_3way_match
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <y.shadman<PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__release_to_pay_manual
#: model:ir.model.fields,help:account_3way_match.field_account_move__release_to_pay_manual
#: model:ir.model.fields,help:account_3way_match.field_account_payment__release_to_pay_manual
msgid ""
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""
"* بله: شما باید صورتحساب را پرداخت کنید، محصولات را دریافت کرده اید\n"
"* نه، شما نباید صورتحساب را پرداخت کنید، محصولات را دریافت نکرده اید\n"
"* استثنا، بین مقادیر دریافتی و صورت‌حساب تفاوت وجود دارد\n"
"این وضعیت به طور خودکار تعریف می شود، اما می‌توانید با علامت زدن در کادر \"وضعیت الزامی\" آن را مجبور کنید."

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills in Exception"
msgstr "صورتحساب های استثناء"

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills to Pay"
msgstr "صورتحساب ها برای پرداخت"

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__exception
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__exception
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__exception
msgid "Exception"
msgstr "استثناء"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__force_release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__force_release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__force_release_to_pay
msgid "Force Status"
msgstr "وضعیت الزامی"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__force_release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_move__force_release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_payment__force_release_to_pay
msgid ""
"Indicates whether the 'Should Be Paid' status is defined automatically or "
"manually."
msgstr ""
"نشان می‌دهد وضعیت \"باید پرداخت شود\" آیا به طور خودکار یا دستی تعریف شده "
"است."

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_journal
msgid "Journal"
msgstr "روزنامه"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_move
msgid "Journal Entry"
msgstr "سند دفترروزنامه‌"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_move_line
msgid "Journal Item"
msgstr "آیتم دفترروزنامه"

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__no
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__no
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__no
msgid "No"
msgstr "خیر"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__release_to_pay
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__release_to_pay
msgid "Release To Pay"
msgstr "آماده پرداخت"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_move_line__can_be_paid
msgid "Release to Pay"
msgstr "آماده پرداخت"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_bank_statement_line__release_to_pay_manual
#: model:ir.model.fields,field_description:account_3way_match.field_account_move__release_to_pay_manual
#: model:ir.model.fields,field_description:account_3way_match.field_account_payment__release_to_pay_manual
msgid "Should Be Paid"
msgstr "باید پرداخت شود."

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_bank_statement_line__release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_move__release_to_pay
#: model:ir.model.fields,help:account_3way_match.field_account_payment__release_to_pay
msgid ""
"This field can take the following values :\n"
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""
"این فیلد می‌تواند مقادیر زیر داشته باشد :\n"
"  * بله، شما باید صورتحساب محصولات را پرداخت کنید، شما محصولات را دریافت کرده‌اید\n"
"  * نه، شما نباید صورتحساب محصولات را پرداخت کنید، شما محصولات را دریافت نکرده‌اید\n"
"  * استثنا، مغایرتی بین تعدادمحصولات صورت شده و دریافت شده وجود دارد \n"
"این وضعیت به صورت خودکار تعریف شده، اما شما می‌توانید با تیک زدن «وضعیت الزامی» اجبارا آن را تغییر دهید."

#. module: account_3way_match
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay__yes
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move__release_to_pay_manual__yes
#: model:ir.model.fields.selection,name:account_3way_match.selection__account_move_line__can_be_paid__yes
msgid "Yes"
msgstr "بله"
