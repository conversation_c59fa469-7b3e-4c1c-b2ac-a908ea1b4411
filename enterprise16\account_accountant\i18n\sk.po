# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON><PERSON><PERSON> <rast<PERSON>.<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <jar<PERSON>.<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Nothing to do here!\n"
"                </p>\n"
"                <p>\n"
"                    No transactions matching your filters were found.\n"
"                </p>\n"
"            "
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "$ -2,678.00"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "$ 2,678.00"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "-1,134.50"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Rekonciliácia"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "1 Bank Transaction"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">Tip: hromadne aktualizujte položky denníka</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>Zistite, ako spravovať zákaznícke faktúry v serveri <b>Zákazníci / "
"faktúry</b> menu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "<i class=\"fa fa-check\"/> Matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "<span class=\"ml4 mr4\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "<span class=\"ml4 mr4\">in</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': ['|', "
"('form_force_negative_sign', '=', False), ('form_index', '=', "
"False)]}\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('form_force_negative_sign', '=', False)]}\">-</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr "<span class=\"o_form_label\">Fiškálny rok</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Dobrá práca!</b> Prešli ste všetkými krokmi tejto "
"prehliadky.</strong>"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Párovanie musí obsahovať aspoň 2 riadky pohybov."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Account"
msgstr "Účet"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Skupiny účtu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr "Účet prediktívnych faktúr"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Widget pre párovanie účtu"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Tagy účtu"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Účtovníctvo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Add a Transaction"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Pridaj nový tag"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Všetky faktúry a platby boli spárované, zostatky vašich účtov sú čisté."

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Povoliť definovanie fiškálneho roku viac alebo menej ako rok"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__amls_widget
msgid "Amls Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Amount"
msgstr "Suma"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Mena sumy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Amount in Currency"
msgstr "Suma v mene"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_distribution
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
#, python-format
msgid "Analytic"
msgstr "Analytický"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_distribution_search
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__analytic_precision
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Anglosaské účtovníctvo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr "Bilancia"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Bank"
msgstr "Bankové doklady"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Bankové a hotovostné pohyby"

#. module: account_accountant
#. odoo-javascript
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Párovanie bankových dokladov"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Bankový výpis"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr "Výpis z účtu %s.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Riadok bankového výpisu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr "Bankový výpis.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Bills"
msgstr "Dodávateľské faktúry"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Zrušené"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Zmeniť dátum uzamknutia"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""
"Skontrolujte a potvrďte účet. Ak nebol nájdený žiadny dodávateľ, pred "
"validáciou ho pridajte."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Skontrolovať všetky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "Vyberte riadok, ktorého ukážka má obsahovať prílohy."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Vyberte náprotivok alebo vytvorte odpis"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Kliknite sem pre vytvorenie nového fiškálneho roka."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Zatvoriť"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Gratulácie, ste hotový!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "Pripojte svoju banku a získajte najnovšie transakcie."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Hodnota protiúčtu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr "Vytvorte model párovania"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Vytvorte náprotivok"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Vytvorte novú skupinu účtov"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Vytvoriť model"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Vytvorte svoj účet prvého dodávateľa.<br/><br/><i>Tip: Ak nemáte po ruke, "
"použite náš vzorový účet.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
#, python-format
msgid "Credit"
msgstr "Dal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Currency"
msgstr "Mena"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Customer/Vendor"
msgstr "Zákazník / dodávateľ"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Date"
msgstr "Dátum"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
#, python-format
msgid "Debit"
msgstr "Má dať"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Definujte fiškálne roky na viac alebo menej ako jeden rok"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Prehľad"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Discuss"
msgstr "Diskusie"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Nemáte prístup, vynechajte tieto dáta z mailu "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Dátum splatnosti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Dátum ukončenia"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Posledný dátum obsiahnutý vo fiškálnom roku."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Každá platba a faktúra pred týmto dátumom získa stav „Od fakturácie“, ktorý "
"skryje všetky účtovné záznamy, ktoré sa jej týkajú. Túto možnosť použite po "
"inštalácii účtovníctva, ak ste predtým používali iba fakturáciu, a potom "
"importujte všetky svoje skutočné účtovné údaje do služby Odoo."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Exchange Difference: %s"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Externý odkaz"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Filtrovať podľa účtu, štítku, partnera, sumy, atď..."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "Fiškálny rok"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Fiškálny rok 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Fiškálne roky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Posledný deň fiškálneho roka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Minulý mesiac fiškálneho roka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Vlajka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_account_id
msgid "Form Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_amount_currency
msgid "Form Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_balance
msgid "Form Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_currency_id
msgid "Form Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_date
msgid "Form Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_extra_text
msgid "Form Extra Text"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_flag
msgid "Form Flag"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_force_negative_sign
msgid "Form Force Negative Sign"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_name
msgid "Form Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_id
msgid "Form Partner"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_currency_id
msgid "Form Partner Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_payable_account_id
msgid "Form Partner Payable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_payable_amount
msgid "Form Partner Payable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_receivable_account_id
msgid "Form Partner Receivable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_partner_receivable_amount
msgid "Form Partner Receivable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_single_currency_mode
msgid "Form Single Currency Mode"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_suggest_amount_currency
msgid "Form Suggest Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_suggest_balance
msgid "Form Suggest Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_tax_ids
msgid "Form Tax"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"V ľubovoľnom zobrazení zoznamu vyberte viac záznamov a zoznam bude možné "
"upravovať. Ak aktualizujete bunku, vybrané záznamy sa aktualizujú naraz. "
"Táto funkcia sa používa na aktualizáciu viacerých interných účtovných "
"dokladov z obratovej predvahy alebo z ľubovoľného zobrazenia denníka."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "Odteraz možno budete chcieť:"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr "Vráťte sa na riadiaci panel pomocou predchádzajúceho spôsobu..."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "Prejdite na fakturáciu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Dobrá práca!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr "Skvelé! Pokračujme."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "INV/2032/0003"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "INV/2032/0012"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related information at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""
"Ak je toto políčko začiarknuté, znamená to, že používateľ nemal v čase "
"vytvárania presunu isté všetky súvisiace informácie a že je potrebné presun "
"znovu skontrolovať."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Prichádzajúci"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Nesprávny dátum fiškálneho roka: deň je mimo rozsahu pre mesiac. Mesiac: %s;"
" Day: %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "Index"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Invoices"
msgstr "Faktúry"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Prahová hodnota prepínača fakturácie"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "Je odsúhlasené"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "Je povinné zadať účet a účtovnú knihu pre vytvorenie odpisu."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Položky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Journal"
msgstr "Účtovný denník"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "Mena denníka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal Entry"
msgstr "Vstup účtovnej knihy"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Položka účtovnej knihy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Položky účtovnej knihy na zosúladenie"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "KPI hotovostnej hodnoty bankového účtu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Label"
msgstr "Popisok"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Larry Smith"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Posledný deň"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget____last_update
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Posledné párovanie:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""
"Poďme automatizovať vaše prijaté faktúry, bankové transakcie a účtovné "
"procesy."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Vráťme sa k riadiacemu panelu."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr "Poďme zosúladiť načítané bankové transakcie."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "Pozrime sa, ako vyzerá účet vo formálnom zobrazení."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""
"Vyplňme formulár pomocou AI<br/><br/><i>Tip: Ak OCR ešte nie je hotové, "
"počkajte ešte niekoľko sekúnd a skúste to znova.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Riadok"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the lines_widget"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__lines_widget
msgid "Lines Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Načítať viac... ("

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Dátum uzamknutia pre všetkých užívateľov"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Zamknúť dátum pre neporadcov"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Dátumy uzamknutia"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Manual Operations"
msgstr "Ručné operácie"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_view_switcher.js:0
#: code:addons/account_accountant/static/src/components/matching_link_widget/matching_link_widget.xml:0
#, python-format
msgid "Match"
msgstr "Zhoda"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Match Existing Entries"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr "Priraďte k položkám, ktoré nie sú z účtov pohľadávok / záväzkov"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Matched Transactions"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Miscellaneous"
msgstr "Účtovné záznamy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Rôzne účtovné záznamy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Úprava modelov"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Presunúť prílohu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Meno"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Nové"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__next_action_todo
msgid "Next Action Todo"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "No attachments linked."
msgstr "Nie sú prepojené žiadne prílohy."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Žiadni používatelia nemôžu upravovať interné účtovné doklady súvisiace s "
"daňou pred a vrátane tohto dátumu."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Žiadni používatelia, vrátane Poradcov, nemôžu upravovať účty pred a vrátane "
"tohoto dátumu. Použite ju napríklad na zamykanie fiškálneho roku ."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "Poznámky"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Iba správcovia fakturácie môžu meniť dátumy uzamknutia!"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Len používatelia s rolou \"Poradca\" môžu upravovať účty pred a vrátane "
"tohoto dátumu. Používa sa napríklad pre zamknutie obdobia v rámci otvoreného"
" fiškálneho roku."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "Open Amount"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount in Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Otvorený zostatok"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Open balance: %s"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr "Operácie"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Odchádzajúce"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Zaplaťte svoje"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Payable"
msgstr "Pohľadávka"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Payable:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Spárovanie platieb"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Stav platby pred zmenou"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Platby"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Spárovanie platieb"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "Platby bez zákazníka sa nedajú spárovať"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Please install the 'Accounting Reports' module."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr "Predpovedať účty dodávateľských dokladov"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Predvoľba na vytvorenie interných účtovných dokladov počas párovania faktúr "
"a platieb"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Konfigurácia predvolieb"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Accountants users."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr "Spracujte túto transakciu."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Receivable:"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__reco_models_widget
msgid "Reco Models Widget"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#, python-format
msgid "Reconcile"
msgstr "Zosúladiť"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Zosúladené"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
#, python-format
msgid "Reconciliation"
msgstr "Párovanie"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr ""
"Zaznamenajte náklady na predaný tovar vo svojich interných účtovných "
"dokladoch"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Reset"
msgstr "Reset"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Reziduálne"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Uložiť"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr " Uložiť & Zatvoriť"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Uložiť & Nové"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Uložiť & Nové"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "Search customer"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Vybrať partnra"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Set as Checked"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Nastavenia"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Preskočiť"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Niektoré polia sú nedefinované"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Dátum začiatku"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Dátum začatia, zahrnutý vo fiškálnom roku."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Štát"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Vyhlásenie"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "Riadok výpisu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Daň"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Daň zahrnutá v cene"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "Dátum daňového zámku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "Riadok rozdelenia daní"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_form_lines_widget.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Taxes"
msgstr "Dane"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_narration
msgid "Terms and Conditions"
msgstr "Obchodné podmienky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "Čo je v priemere"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Množstvo %s nie je platná čiastočná suma"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "Dátum ukončenia nesmie byť menší ako dátum začiatku."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr "Faktúry do tohto dátumu sa nebudú brať do úvahy ako účtovné záznamy"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""
"Systém sa pokúsi predpovedať účty na účtoch dokladov dodávateľa na základe "
"histórie predchádzajúcich prijatých faktúr"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Neexistuje nič na zladenie."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Táto platba je zaregistrovaná ale nie zladená."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr "Toto bol model, ktorý generoval navrhované čiary"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Tip: hromadne aktualizujte položky denníka"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "To Check"
msgstr "Na kontrolu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Pre zrýchlenie párovania, definujte"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Transakcia"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
msgid "Transaction Details"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr "Prevod účtov"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
#: model:ir.cron,cron_name:account_accountant.auto_reconcile_bank_statement_line
msgid "Try to reconcile automatically your statement lines"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
#, python-format
msgid "Unreconciled statements lines"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "Platný"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_rec_widget_form
#, python-format
msgid "Validate"
msgstr "Potvrdiť"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Overiť"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "View"
msgstr "Náhľad"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Sprievodca"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr "Odpis"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Dátum zápisu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "You cannot have a fiscal year on a child company."
msgstr ""

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Nemôžete sa prekrývať medzi dvoma fiškálnymi rokmi, opravte dátumy začiatku "
"a / alebo konca svojich fiškálnych rokov."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have maximum one auto balance line at the same time in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have multiple liquidity journal item at the same time in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You can't have multiple times the same journal item in the bank "
"reconciliation widget"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""
"Nemôžete zosúladiť účty splatných a pohľadávok viacerých partnerov súčasne."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "You cannot set a lock date in the future."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Zosúladili ste"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "a následných zákazníkov"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "napr. bankové poplatky"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "or"
msgstr "alebo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "modely párovania"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_widget_tutorial.xml:0
#, python-format
msgid "reference"
msgstr "referencia"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "zoastávajúce)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "sekúnd za transakciu."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "skontrolovať"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "transakcie v"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "nezaplatené faktúry"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "nezosúladené položky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "Dodávateľské faktúry"
