from odoo import fields, models, api
from lxml import etree


class Product(models.Model):
    _inherit = 'product.template'

    @api.model
    def get_views(self, views, options=None):
        # Requests the My Profile form view as last.
        # Otherwise the fields of the 'search' view will take precedence
        # and will omit the fields that are requested as SUPERUSER
        # in `get_view()`.
        profile_view = self.env.ref("product_access_view.product_template_view_form")
        profile_form = profile_view and [profile_view.id, 'form']
        if profile_form and self.env.user.has_group('product_access_view.group_product_access_view'):
            # views.remove(profile_form)
            views.append(profile_form)
        result = super().get_views(views, options)
        return result


class ProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model
    def get_views(self, views, options=None):
        # Requests the My Profile form view as last.
        # Otherwise the fields of the 'search' view will take precedence
        # and will omit the fields that are requested as SUPERUSER
        # in `get_view()`.
        profile_view = self.env.ref("product_access_view.product_product_view_form")
        profile_form = profile_view and [profile_view.id, 'form']
        if profile_form and self.env.user.has_group('product_access_view.group_product_access_view'):
            # views.remove(profile_form)
            views.append(profile_form)
        result = super().get_views(views, options)
        return result
