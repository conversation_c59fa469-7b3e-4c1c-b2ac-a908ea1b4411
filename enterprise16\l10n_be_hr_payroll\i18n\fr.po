# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON>t <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Days"
msgstr "# Jours"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours"
msgstr "# Heures"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours :"
msgstr "# Heures :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children < 19"
msgstr "# Enfants assurés < 19 ans"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_children
msgid "# Insured Children < 19 y/o"
msgstr "# Enfants assurés < 19 ans"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children >= 19"
msgstr "# Enfants assurés >= 19 ans"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults
msgid "# Insured Children >= 19 y/o"
msgstr "# Enfants assurés >= 19 ans"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__months_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__months_count
msgid "# Months"
msgstr "# Mois"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid "# dependent children for salary attachement"
msgstr "# enfants à charge pour les saisies sur salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_juniors_dependent
msgid "# disabled people (<65)"
msgstr "# personnes handicapées (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_senior_dependent
msgid "# disabled seniors (>=65)"
msgstr "# seniors handicapés (>=65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid "# people (<65)"
msgstr "# personnes (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid "# seniors (>=65)"
msgstr "# seniors (>=65)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "%s %s quarter %s"
msgstr "%s %s trimestre %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "%s - %s"
msgstr "%s - %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "%s - Part Time %s"
msgstr "%s - Temps partiel %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_leave.py:0
#, python-format
msgid "%s is in %s. Fill in the appropriate eDRS here: %s"
msgstr "%s est en %s. Remplissez la déclaration eDRS appropriée ici : %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#, python-format
msgid "%s-individual-account-%s"
msgstr "%s-compte-individuel-%s"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "'Holiday Attest (Year N) - %s' % (object.employee_id.name)"
msgstr "'Attestation de vacances (Année N) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "'Holiday Attest (Year N-1) - %s' % (object.employee_id.name)"
msgstr ""
"'Attestation de vacances (Année N-1) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_double_holiday_13th_month
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Fiche de paie - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_fees
msgid "'Termination - %s' % (object.employee_id.name)"
msgstr "'Indemnités de rupture - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_individual_account
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Amount"
msgstr "* Montant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Compensation granted to insurance inspectors in reimbursement of costs"
msgstr ""
"* Indemnités accordées aux inspecteurs des assurances en remboursement de "
"frais"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Individual agreement available"
msgstr "* Accord individuel disponible"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Km"
msgstr "* Km"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Mention: MOBILITY ALLOWANCE"
msgstr "* Mention : BUDGET MOBILITÉ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Options granted by a foreign company not having an establishment in "
"Belgium"
msgstr ""
"* Options consenties par une société étrangère n'ayant pas d'établissement "
"en Belgique"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Social Security package"
msgstr "* Forfait sécurité sociale"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Total compensation"
msgstr "* Indemnité totale"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* amount"
msgstr "* montant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* code"
msgstr "* code"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* nature"
msgstr "* nature"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* number of days out of border area"
msgstr "* nombre de jours de sortie de zone frontalière"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* percentage(s)"
msgstr "* pourcentage(s)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* specific remunerations paid in 2021 for services from the first to the "
"third quarters of 2021 inclusive and / or for services provided during the "
"second and fourth quarters of 2020"
msgstr ""
"* rémunérations spécifiques payées en 2021 pour les prestations du premier "
"au troisième trimestre 2021 inclus et/ou pour des prestations effectuées "
"pendant les deuxième et quatrième trimestres 2020"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* total amount of all remuneration paid under a student employment contract"
msgstr ""
"* montant total de toutes les rémunérations payées dans le cadre d'un "
"contrat d'occupation d'étudiant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "*10€ + 1*"
msgstr "*10€ + 1*"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid ", bank account of"
msgstr ", compte bancaire de"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "- Test"
msgstr "- Test"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"- Without Income: The spouse of the income recipient has no professional income.\n"
"\n"
"- High income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which exceeds %s€ net per month.\n"
"\n"
"- Low Income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which does not exceed %s€ net per month.\n"
"\n"
"- Low Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which does not exceed %s€ net per month.\n"
"\n"
"- High Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which exceeds %s€ net per month."
msgstr ""
"- Sans revenus : Le conjoint du bénéficiaire n'a pas de revenus professionnels propres.\n"
"\n"
"- Revenus élevés : Le conjoint du bénéficiaire des revenus a des revenus professionnels propres, autres que des pensions, des rentes ou des revenus similaires, qui dépassent %s€ net par mois.\n"
"\n"
"- Revenus faibles : Le conjoint du bénéficiaire a des revenus professionnels propres, autres que des pensions, des rentes ou des revenus similaires, qui ne dépassent pas %s€ net par mois.\n"
"\n"
"- Pensions faibles : Le conjoint du bénéficiaire a des revenus professionnels propres constitués exclusivement de pensions, de rentes ou de revenus similaires qui ne dépassent pas %s€ net par mois.\n"
"\n"
"- Pensions élevées : Le conjoint du bénéficiaire a des revenus professionnels propres constitués exclusivement de pensions, de rentes ou de revenus similaires qui dépassent %s€ net par mois."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- actually provided from 07/01/2021"
msgstr "- prestées à partir du 01.07.2021"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- actually provided until 30.06.2021 inclusive"
msgstr "- prestées jusqu'au 30.06.2021 inclus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.01.2021 and 30.09.2021 included with "
"employers belonging to crucial sectors"
msgstr ""
"- pour heures supplémentaires prestées entre le 01.01.2021 et le 30.06.2021 "
"inclus auprès d'employeurs appartenant aux secteurs cruciaux "

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.07.2021 and 31.12.2021 as part of the "
"recovery plan"
msgstr ""
"- pour heures supplémentaires prestées entre le 01.07.2021 et le 31.12.2021 "
"inclus dans le cadre de la relance"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements based on serious standards"
msgstr "- indemnités forfaitaires sur base de normes sérieuses"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements in the absence of serious standards"
msgstr "- indemnités forfaitaires en absence de normes sérieuses"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- mobility allowance"
msgstr "- budget mobilité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 01.01.2021 to 30.09.2021 inclusive with employers belonging "
"to crucial sectors"
msgstr ""
"- prestées du 01.01.2021 au 30.06.2021 inclus auprès d'employeurs "
"appartenant aux secteurs cruciaux"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 07/01/2021 to 12/31/2021 included as part of the recovery "
"plan"
msgstr ""
"- prestées du 01.07.2021 au 31.12.2021 inclus dans le cadre de la relance"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- reimbursements based on supporting documents"
msgstr "- indemnités sur base de justificatifs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
".\n"
"                            <span>Your next employer or payment agency will not pay for these days off. So you are advised to keep this amount\n"
"                            until you take these days off.</span>"
msgstr ""
".\n"
"                            <span>Votre prochain employeur ou organisme de paiement ne paiera pas ces congés ou absences. Il vous est donc conseillé de conserver ce montant\n"
"                            jusqu'à ce que vous preniez ces congés.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0,87% of gross reference remuneration"
msgstr "0,87% de la rémunération brute de référence"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "1 day is 7 hours and 36 minutes"
msgstr "1 jour équivaut à 7 heures et 36 minutes"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid ""
"1 day is the number of the amount of hours per day in the working schedule"
msgstr ""
"1 jour est le nombre du montant d'heures par jour dans l'horaire de travail"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1) Remuneration"
msgstr "1) Rémunérations"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "10-digit code given by ONSS"
msgstr "Code à 10 chiffres fourni par l'ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1001"
msgstr "1001"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1002"
msgstr "1002"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1003"
msgstr "1003"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1011"
msgstr "1011"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1012"
msgstr "1012"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1013"
msgstr "1013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "102 : Staff Costs :"
msgstr "102 : Coût du personnel :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "103 : Benefits Above Salary :"
msgstr "103 : Avantages en plus du salaire :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "105"
msgstr "105"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "110"
msgstr "110"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "111"
msgstr "111"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "112"
msgstr "112"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "113"
msgstr "113"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__12mo
msgid "12 months +"
msgstr "12 mois +"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "120"
msgstr "120"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1200"
msgstr "1200"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1201"
msgstr "1201"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1202"
msgstr "1202"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1203"
msgstr "1203"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "121"
msgstr "121"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1210"
msgstr "1210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1211"
msgstr "1211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1212"
msgstr "1212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1213"
msgstr "1213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "130"
msgstr "130"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "132"
msgstr "132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "133"
msgstr "133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "134"
msgstr "134"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_thirteen_month
msgid "13th Month Slip"
msgstr "Fiche de 13ème mois"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__1
msgid "1st"
msgstr "1er"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Awarded in 2021"
msgstr "1° Attribuées en 2021"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Compensation, not mentioned in 2°, 3°, 4°"
msgstr "1° Rémunérations autres que 2°, 3° et 4°"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Ordinary remuneration"
msgstr "1° Rémunérations ordinaires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° which count for the limit up to 180 hours"
msgstr "1° qui entrent en ligne de compte pour la limite jusqu'à 180 heures"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime"
msgstr "2) Heures supplémentaires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) overtime worked in 2020 but paid in 2021"
msgstr "2) Heures supplémentaires prestées en 2020 et payées en 2021"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "205"
msgstr "205"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "210"
msgstr "210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "211"
msgstr "211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "212"
msgstr "212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "213"
msgstr "213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "233"
msgstr "233"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "234"
msgstr "234"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "240"
msgstr "240"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "242"
msgstr "242"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "243"
msgstr "243"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "247"
msgstr "247"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "250"
msgstr "250"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "251"
msgstr "251"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "252"
msgstr "252"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "254"
msgstr "254"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "263"
msgstr "263"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "267"
msgstr "267"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "271"
msgstr "271"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "273"
msgstr "273"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "273S"
msgstr "273S"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_ip_273S
msgid "273S PDF"
msgstr "273S PDF"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_273S_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_273s
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_273S
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "273S Sheet"
msgstr "Déclaration 273S"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_tree
msgid "273S Sheets"
msgstr "Déclarations 273S"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "274"
msgstr "274"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_274_10
msgid "274.10 PDF"
msgstr "274.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10
msgid "274.10 Sheet"
msgstr "Déclaration 274.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_274_XX_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_274_XX
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_tree
msgid "274.XX Sheets"
msgstr "Déclarations 274.XX"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx_line
msgid "274.XX Sheets Line"
msgstr "Lignes de déclaration 274.XX"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "275"
msgstr "275"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "276"
msgstr "276"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "277"
msgstr "277"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "278"
msgstr "278"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "279"
msgstr "279"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "280"
msgstr "280"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_action_view_tree
msgid "281.10 Forms"
msgstr "Fiches 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_10
msgid "281.10 PDF"
msgstr "281.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_10
msgid "281.10 Sheet"
msgstr "Fiche 281.10"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "281.10 Sheet: Income"
msgstr "Fiche 281.10 : Revenus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_action_view_tree
msgid "281.45 Forms"
msgstr "Fiches 281.45"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_45
msgid "281.45 PDF"
msgstr "281.45 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_45
msgid "281.45 Sheet"
msgstr "Fiche 281.45"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "281.45 Sheet - Year"
msgstr "Fiche 281.45 - Année"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "283"
msgstr "283"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "284"
msgstr "284"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "285"
msgstr "285"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "286"
msgstr "286"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "287"
msgstr "287"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "290"
msgstr "290"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "2a. Income recipient"
msgstr "2a. Bénéficiaire des revenus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__2
msgid "2nd"
msgstr "2eme"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Actions"
msgstr "2° Actions"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Arrears"
msgstr "2° Arriérés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Awarded before 2021"
msgstr "2° Attribuées avant 2021"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "3-digit code given by ONSS"
msgstr "Code à 3 chiffres fourni par l'ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "3. Name and address of income debtor:"
msgstr "3. Nom et adresse du débiteur des revenus :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "305"
msgstr "305"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "306"
msgstr "306"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "307"
msgstr "307"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "308"
msgstr "308"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "310"
msgstr "310"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "311"
msgstr "311"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "312"
msgstr "312"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "313"
msgstr "313"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "317"
msgstr "317"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "32"
msgstr "32"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "33"
msgstr "33"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "335"
msgstr "335"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "336"
msgstr "336"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "337"
msgstr "337"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "338"
msgstr "338"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "34"
msgstr "34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "340"
msgstr "340"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "341"
msgstr "341"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "342"
msgstr "342"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "343"
msgstr "343"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "387"
msgstr "387"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "395"
msgstr "395"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "396"
msgstr "396"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "397"
msgstr "397"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "398"
msgstr "398"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__3
msgid "3rd"
msgstr "3eme"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° Bonuses, premiums and stock options"
msgstr "3° Bonus, primes et options sur actions"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° which are taken into account for the limit up to 360 hours (*)"
msgstr ""
"3° qui entrent en ligne de compte pour la limite jusqu'à 360 heures (*)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "4. Gross amount of income"
msgstr "4. Montant brut des revenus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__4
msgid "4th"
msgstr "4eme"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "4° Benefits in kind"
msgstr "4° Avantages de toute nature"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "5. Deducted fees"
msgstr "5. Frais forfaitaires"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__55yo
msgid "55+ years old"
msgstr "55+ ans"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "57.75 %"
msgstr "57,75 %"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5801"
msgstr "5801"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5802"
msgstr "5802"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5803"
msgstr "5803"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58031"
msgstr "58031"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58032"
msgstr "58032"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58033"
msgstr "58033"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5811"
msgstr "5811"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5812"
msgstr "5812"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5813"
msgstr "5813"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58131"
msgstr "58131"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58132"
msgstr "58132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58133"
msgstr "58133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5821"
msgstr "5821"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5822"
msgstr "5822"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5823"
msgstr "5823"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5831"
msgstr "5831"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5832"
msgstr "5832"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5833"
msgstr "5833"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5841"
msgstr "5841"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5842"
msgstr "5842"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5843"
msgstr "5843"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5851"
msgstr "5851"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5852"
msgstr "5852"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5853"
msgstr "5853"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "6,8% of gross reference remuneration"
msgstr "6,8% de la rémunération brute de référence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6,8% of gross reference remuneration - additional time off amount"
msgstr ""
"6,8% de la rémunération brute de référence - montant des congés additionnels"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "6. Amount of retained withholding tax"
msgstr "6. Montant du précompte retenu"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "66.81 %"
msgstr "66,81 %"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "7,67% of gross reference remuneration"
msgstr "7,67% de la rémunération brute de référence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"7,67% of gross reference remuneration * (time off not taken) / (right to "
"time off)"
msgstr ""
"7,67% de la rémunération brute de référence * (# congés non pris) / (droit "
"aux congés)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "865"
msgstr "865"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "870"
msgstr "870"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "9-digit code given by ONSS"
msgstr "Code à 9 chiffres fourni par l'ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "9998"
msgstr "9998"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__25yo
msgid "< 25 years old"
msgstr "< 25 ans"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Contributions Summary :</b>"
msgstr "<b>Résumé des cotisations :</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Deductions Summary :</b>"
msgstr "<b>Résumé des déductions :</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Remunerations Summary :</b>"
msgstr "<b>Résumé des rémunérations :</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Services Summary :</b>"
msgstr "<b>Résumé des services :</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Student Contributions Summary :</b>"
msgstr "<b>Résumé des cotisations des étudiants :</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"<span attrs=\"{'invisible': "
"[('notice_duration_month_before_2014','=',0)]}\"> months and </span>"
msgstr ""
"<span attrs=\"{'invisible': "
"[('notice_duration_month_before_2014','=',0)]}\"> mois et </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">Company number: </span>"
msgstr "<span class=\"fw-bold\">Numéro d'entreprise : </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">NISS: </span>"
msgstr "<span class=\"fw-bold\">NISS : </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"fw-bold\">Nature of beneficiary: </span>\n"
"                                    <span>Natural Person</span>"
msgstr ""
"<span class=\"fw-bold\">Nature du bénéficiaire : </span>\n"
"                                    <span>Personne physique</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"ms-3\" attrs=\"{'invisible': [('wage_type', '=', "
"'hourly')]}\">%</span>"
msgstr ""
"<span class=\"ms-3\" attrs=\"{'invisible': [('wage_type', '=', "
"'hourly')]}\">%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\"> / month</span>"
msgstr "<span class=\"ms-3\"> / mois</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/ mois</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ worked day</span>"
msgstr "<span class=\"ms-3\">/ jours presté</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/ an</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('transport_mode_private_car', '=', False)]}\">Reimboursed amount</span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('transport_mode_private_car', '=', False)]}\">Montant remboursé</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" "
"attrs=\"{'invisible': [('transport_mode_private_car', '=', "
"False)]}\">Distance home-work</span>"
msgstr ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" "
"attrs=\"{'invisible': [('transport_mode_private_car', '=', "
"False)]}\">Distance domicile-lieu de travail</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Company Information</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Informations de la société</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">ONSS</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">ONSS</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">SDWorx</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">SDWorx</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">From</span>"
msgstr "<span class=\"oe_inline\">De</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">To</span>"
msgstr "<span class=\"oe_inline\">À</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">10. Taxable at the rate of"
" 33% :</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">10. Imposable au taux de "
"33% :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">11. Remuneration obtained "
"by athletes within the framework of their sporting activity :</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">11. Rémunérations obtenues"
" par des sportifs dans le cadre de leur activité sportive :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">12. Remuneration obtained "
"by sports competition referees for their refereeing services, or by "
"trainers, coaches and accompanying persons for their activity for the "
"benefit of sportsmen :</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">12. Rémunérations obtenues"
" par des arbitres de compétitions sportives pour leurs prestations "
"arbitrales, ou par des formateurs, des entraîneurs et des accompagnateurs "
"pour leur activité au profit de sportifs :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">13. PC Privé</span>"
msgstr "<span class=\"text-start text-uppercase fw-bold\">13. PC Privé</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">14. Contribution to travel"
" costs :</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">14. Intervention dans les "
"frais de déplacement :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">15. Impulse Fund</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">15. Fonds "
"d'impulsion</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">16. Deductions for "
"supplementary pensions</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">16. Retenues pour pension "
"complémentaire</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">17. Remuneration for "
"overtime in the hospitality industry which qualifies for the "
"exemption</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">17. Rémunérations pour "
"heures supplémentaires dans l'Horeca qui entrent en ligne de compte pour "
"l'exonération</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">18. Overtime which gives "
"the right to extra pay</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">18. Heures supplémentaires"
" qui donnent droit à un sursalaire</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">19. Remuneration which is "
"taken into account for the exemption for voluntary overtime</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">19. Rémunérations qui "
"entrent en ligne de compte pour l'exonération pour heures supplémentaires "
"volontaires</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">20. Withholding "
"Taxes</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">20. Précompte "
"professionnel</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">21. Special contributions "
"for social security</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">21. Cotisation spéciale "
"pour la sécurité sociale</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">22. Public sector staff "
"without an employment contract</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">22. Personnel du secteur "
"public sans contrat de travail</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">23. Employment "
"bonus</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">23. Bonus à "
"l'emploi</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">24. Miscellaneous "
"information</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">24. Renseignements "
"divers</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">25. Remuneration and other"
" benefits received from a related foreign company</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">25. Rémunérations et "
"autres avantages reçus d'une société étrangère liée</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">26. Foreign seasonal "
"worker in agriculture and horticulture subject to professional withholding "
"tax</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">26. Non-résident employé "
"comme travailleur saisonnier dans l'agriculture et l'horticulture soumis au "
"précompte professionnel</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">27. Foreign executive or "
"researcher</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">27. Cadre ou chercheur "
"étranger</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">6. REMUNERATION (other "
"than referred to under 10, 11a and 12a)</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">6. RÉMUNÉRATIONS (autres "
"que visées sous 10, 11a et 12a)</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">7. Miscellaneous taxable "
"income</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">7. Revenus taxables "
"distinctement</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">8. Bad weather "
"stamps</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">8. Timbres "
"intempéries</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">9. Non-recurring benefits "
"linked to results</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">9. Avantages non "
"récurrents liés aux résultats</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Nature des revenus</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Nature des revenus</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte Professionnel "
"Retenu</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"retenu</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"dû</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"dû</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables "
"répondant aux conditions d’application de la dispense</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables "
"répondant aux conditions d’application de la dispense</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span> included:</span>"
msgstr "<span> inclus :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "<span> weeks</span>"
msgstr "<span> semaines</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> years old</span>"
msgstr "<span> ans</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ mois</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>/Holiday year </span>"
msgstr "<span>/année de vacances </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Actual and similar remuneration for the period from </span>"
msgstr "<span>Rémunération actuelle et similaire pour la période du </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Attendance (hours)</span>"
msgstr "<span>Présence (heures)</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Holiday exercise </span>"
msgstr "<span>Exercice de vacances </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Individual Account Report for year </span>"
msgstr "<span>Décompte individuel pour l'année</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>No</span>"
msgstr "<span>Non</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>Social contributions on the various vacation pay have already been "
"paid.</span>"
msgstr ""
"<span>Les cotisations sociales sur les différents congés payés ont déjà été "
"payées.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take in the\n"
"                            near future in </span>"
msgstr ""
"<span>Le montant couvert par ce certificat compense par anticipation les jours de vacances que vous prendrez dans le\n"
"                            futur proche en </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take in the near\n"
"                            future in </span>"
msgstr ""
"<span>Le montant couvert par ce certificat compense par anticipation les jours de vacances que vous prendrez dans le futur\n"
"                            proche en </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>Yes</span>"
msgstr "<span>Oui</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>You must return this certificate to your next employer, or failing that, to your allowance payment agency.\n"
"                            Social security contributions on holiday pay have already been retained.</span>"
msgstr ""
"<span>Vous devez retourner cette attestation à votre prochain employeur, ou à défaut, à votre organisme de versement des allocations.\n"
"                            Les cotisations sociales sur le pécule de vacances ont déjà été retenues.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>days</span>"
msgstr "<span>jours</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<span>included:</span>"
msgstr "<span>inclus :</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>km</span>"
msgstr "<span>km</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>to </span>"
msgstr "<span>à </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / month</span>"
msgstr "<span>€ / mois</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / year</span>"
msgstr "<span>€ / an</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "<span>€</span>"
msgstr "<span>€</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid ""
"<strong class=\"o_group_col_12\" attrs=\"{'invisible': ['|', ('leave_type_id', '=', False), ('found_leave_allocation', '=', True)]}\" style=\"color:#ff6600;\">\n"
"                        No time off allocation has been found for this time off type, no changes will occur to time off for this employee.\n"
"                    </strong>"
msgstr ""
"<strong class=\"o_group_col_12\" attrs=\"{'invisible': ['|', ('leave_type_id', '=', False), ('found_leave_allocation', '=', True)]}\" style=\"color:#ff6600;\">\n"
"                        Aucune allocation de congé n'a été trouvée pour ce type de congé, aucun changement ne sera appliqué aux congés pour cet employé.\n"
"                    </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\" "
"id=\"previous_employer_title\" attrs=\"{'invisible': "
"[('company_country_code', '!=', 'BE')]}\" "
"groups=\"hr_payroll.group_hr_payroll_user\">Previous Employer</strong>"
msgstr ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\" "
"id=\"previous_employer_title\" attrs=\"{'invisible': "
"[('company_country_code', '!=', 'BE')]}\" "
"groups=\"hr_payroll.group_hr_payroll_user\">Employeur précédent</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\">Previous "
"Occupations</strong>"
msgstr ""
"<strong class=\"o_horizontal_separator\" colspan=\"2\">Occupations "
"précédentes</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong> Departure Date: </strong>"
msgstr "<strong> Date de départ : </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>1. Nr.</strong>"
msgstr "<strong>1. No.</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<strong>1. N° :</strong>"
msgstr "<strong>1. N° :</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>2. Date of entry: </strong>"
msgstr "<strong>2. Date de l'entrée : </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>3. Income debtor:</strong> <br/>"
msgstr "<strong>3. Débiteur des revenus :</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Beneficiary:</strong><br/>"
msgstr "<strong>4. Expéditeur :</strong><br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>5. National number:</strong>"
msgstr "<strong>5. Numéro national :</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Address</strong>"
msgstr "<strong>Adresse</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Amount</strong>"
msgstr "<strong>Montant</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Au:</strong>"
msgstr "<strong>Au :</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>Signature autorisée</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Bank Account</strong>"
msgstr "<strong>Compte bancaire</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Basic Salary</strong>"
msgstr "<strong>Salaire de base</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Code</strong>"
msgstr "<strong>Code</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Company Information</strong>"
msgstr "<strong>Informations sur la société</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Computed on </strong>"
msgstr "<strong>Calculé le </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Contract Start Date</strong>"
msgstr "<strong>Date de début du contrat</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Contract Type </strong>"
msgstr "<strong>Type de contrat </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Designation</strong>"
msgstr "<strong>Désignation</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Du:</strong>"
msgstr "<strong>Du :</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong>Eco-Vouchers Amount</strong>"
msgstr "<strong>Montant des éco-chèques</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Email</strong>"
msgstr "<strong>Email</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Employee</strong>"
msgstr "<strong>Employé</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Identification No</strong>"
msgstr "<strong>N° d'identification</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Identification</strong>"
msgstr "<strong>Identification</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Marital Status</strong>"
msgstr "<strong>État civil</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Montant</strong>"
msgstr "<strong>Montant</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Name</strong>"
msgstr "<strong>Nom</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Nr.</strong>"
msgstr "<strong>Nr.</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Pay Period</strong>"
msgstr "<strong>Période de paie</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double complementary</strong>"
msgstr "<strong>Double pécule de vacances complémentaire</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double</strong>"
msgstr "<strong>Double pécule de vacances</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<strong>Pay holiday double</strong> only if the majority of vacation days\n"
"                                have not yet been taken"
msgstr ""
"<strong>Double pécule de vacances</strong> seulement si la majorité des jours de vacances\n"
"                                n'ont pas encore été pris"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Pay simple</strong>"
msgstr "<strong>Pécule simple</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Person in charge</strong>"
msgstr "<strong>Personnes à charge</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Reference</strong>"
msgstr "<strong>Référence</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Registration Number</strong>"
msgstr "<strong>Numéro d'enregistrement</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Salary Computation</strong>"
msgstr "<strong>Calcul du salaire</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Société:</strong> <br/>"
msgstr "<strong>Société :</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Start notice period</strong>"
msgstr "<strong>Début de la période de préavis</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>TOTAL</strong>"
msgstr "<strong>TOTAL</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Days</strong>"
msgstr "<strong>Jours prestés</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Time</strong>"
msgstr "<strong>Temps presté</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "<strong>Working Schedule</strong>"
msgstr "<strong>Horaire de travail</strong>"

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa_location_unit__unique
msgid ""
"A DMFA location cannot be set more than once for the same company and "
"partner."
msgstr ""
"Une unité d'établissement ne peut pas être être définie plus d'une fois pour"
" la même société et la même adresse."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "A benefit in kind is paid when the employee uses its laptop at home."
msgstr ""
"Un avantage de toute nature est payé lorsque l'employé utilise son "
"ordinateur à la maison."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "A. Packages"
msgstr "A. Packages"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "A. Total: (6a + 6b + 6c + 6d, 1° + 6d, 2°)"
msgstr "A. Total : (6a + 6b + 6c + 6d, 1° + 6d, 2°)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "APR"
msgstr "AVR"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_advantage_any_kind
msgid "ATN"
msgstr "ATN"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_inverse_atn_warrant
msgid "ATN Warrant"
msgstr "ATN Warrant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "AUG"
msgstr "AOUT"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid "Absence Work Entry Type"
msgstr "Type de prestation d'absence"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_ONSS
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_onss_employer
msgid "Accounting: ONSS (Employer)"
msgstr "Comptabilité : ONSS (Employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_basic
msgid "Accounting: ONSS Basic (Employer)"
msgstr "Comptabilité : ONSS de base (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_cpae
msgid "Accounting: ONSS CPAE (Employer)"
msgstr "Comptabilité : ONSS CPAE (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_ffe
msgid "Accounting: ONSS FFE (Employer)"
msgstr "Comptabilité : ONSS FFE (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_special_ffe
msgid "Accounting: ONSS Special FFE (Employer)"
msgstr "Comptabilité : ONSS FFE spécial (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_temporary_unemployment
msgid "Accounting: ONSS Temporary Unemployment (Employer)"
msgstr "Comptabilité : ONSS chômage temporaire (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_wage_restreint
msgid "Accounting: ONSS Wage Restreint (Employer)"
msgstr "Comptabilité : ONSS salaire restreint (employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_owed_remuneration
msgid "Accounting: Owed Remuneration"
msgstr "Comptabilité : Rémunération due"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_remuneration
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_remuneration
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_remuneration
msgid "Accounting: Remuneration"
msgstr "Comptabilité : Rémunération"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked full time"
msgstr "Nombre effectif d'heures prestées temps plein"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked part-time"
msgstr "Nombre effectif d'heures prestées temps partiel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid ""
"Adapted salary, according to the sacrifices defined on the contract "
"(Example: Extra-legal time off, a percentage of the salary invested in a "
"group insurance, etc...)"
msgstr ""
"Salaire adapté, selon les sacrifices définis sur le contrat (Exemple : Congé"
" extra-légal, pourcentage du salaire investi dans une assurance groupe, "
"etc...)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__2
msgid "Add"
msgstr "Ajouter"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_additional_gross
msgid "Additional Gross"
msgstr "Brut additionnel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Additional Information"
msgstr "Informations complémentaires"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_additional_paid
msgid "Additional Time (Paid)"
msgstr "Temps supplémentaire (payé)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_additional_leave
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_additional_leave
msgid "Additional Time Off"
msgstr "Congé additionnel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional amount to deduct"
msgstr "Congés additionnels à déduire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional time off (european, ...)"
msgstr "Congés additionnels (européens, ...)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Administration générale de la Fiscalité"
msgstr "Administration générale de la Fiscalité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Adresse e-mail :"
msgstr "Adresse email :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Advantages"
msgstr "Avantages"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_after_contract_public_holiday
msgid "After Contract Public Holiday"
msgstr "Jours fériés après la fin du contrat"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_after_contract_public_holiday
#, python-format
msgid "After Contract Public Holidays"
msgstr "Jours fériés après la fin du contrat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__employee_age
msgid "Age of Employee"
msgstr "Âge de l'employé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__aggregation_level
msgid "Aggregation Level"
msgstr "Niveau d'aggrégation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__alloc_employee_ids
msgid "Alloc Employee"
msgstr "Employés à allouer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__alloc_paid_leave_id
msgid "Alloc Paid Leave"
msgstr "Allocation des jours de congé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Allocation Time Off"
msgstr "Allocations de congés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocation_n_ids
msgid "Allocations N"
msgstr "Allocations N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_onss_restructuring
msgid "Allow ONSS Reduction for Restructuring"
msgstr "Autoriser la réduction ONSS pour retructuration"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr "Exporter les prestations à votre Secrétariat social"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Ambulatory Insurance"
msgstr "Assurance ambulatoire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_notes
msgid "Ambulatory Insurance: Additional Info"
msgstr "Assurance ambulatoire : Informations additionnelles"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_children
msgid "Ambulatory: # Insured Children < 19"
msgstr "Ambulatoire : # Enfants assurés < 19 ans"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults
msgid "Ambulatory: # Insured Children >= 19"
msgstr "Ambulatoire : # Enfants assurés >= 19"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_adult
msgid "Ambulatory: Amount per Adult"
msgstr "Ambulatoire : Montant par adulte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_child
msgid "Ambulatory: Amount per Child"
msgstr "Ambulatoire : Montant par enfant"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_amount
msgid "Ambulatory: Insurance Amount"
msgstr "Ambulatoire : Montant de l'assurance"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_spouse
msgid "Ambulatory: Insured Spouse"
msgstr "Ambulatoire : Conjoint(e) assuré(e)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Amount"
msgstr "Montant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount of the employer's intervention"
msgstr "Montant de l'intervention de l'employeur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid ""
"Amount of the holiday pay paid by the previous employer already recovered."
msgstr ""
"Montant du pécule de vacances payé par l'employeur précédent déjà récupéré."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Amount of the holiday pay paid by the previous employer to recover."
msgstr ""
"Montant du pécule de vacances payé par l'employeur précédent à récupérer."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_adult
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Adult"
msgstr "Montant par adulte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_child
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Child"
msgstr "Montant par enfant"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
msgid ""
"Amount the employee receives in the form of meal vouchers per worked day."
msgstr ""
"Montant que l'employé reçoit sous forme de chèque-repas par jour presté."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Amounts to Recover"
msgstr "Montants à récupérer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_salary_revalued
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination
msgid "Annual salary revalued"
msgstr "Salaire annuel réévalué"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_variable_salary
msgid "Annual variable salary"
msgstr "Salaire annuel variable"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Another reason"
msgstr "Autre raison"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Anticipated Holiday Pay Retenue"
msgstr "Retenue sur le pécule de vacance anticipé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__4
msgid "April"
msgstr "Avril"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_asignment_salary
msgid "Assignment of Salary"
msgstr "Cession de salaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "At the end of the exercise"
msgstr "À la fin de l'exercice"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_attachment_salary
msgid "Attachment of Salary"
msgstr "Saisie sur salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__8
msgid "August"
msgstr "Août"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of full-time workers"
msgstr "Nombre moyen de travailleurs à temps plein"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of part-time workers"
msgstr "Nombre moyen de travailleurs à temps partiel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of total workers or FTEs"
msgstr "Nombre moyen de travailleurs total ou ETP"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
msgid "Average remuneration by month current year"
msgstr "Rémunération moyenne par mois cette année"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration by month previous year"
msgstr "Rémunération moyenne par mois l'année précédente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration for the 12 months preceding unpaid leave"
msgstr ""
"Rémunération moyenne durant les 12 mois précédant le congé non rémunéré"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "B. Real"
msgstr "B. Réel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Bachelors"
msgstr "Bacheliers"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_basic
msgid "Basic Complementary Double Holiday"
msgstr "Double pécule complémentaire de base"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_basic_pay_simple
msgid "Basic Pay Simple"
msgstr "Simple pécule de base"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic
msgid "Basic Salary"
msgstr "Salaire de base"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_basic
msgid "Basic double"
msgstr "Double pécule de base"

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_freelance
msgid "Became Freelance"
msgstr "Devenu indépendant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Belgian Localization"
msgstr "Localisation belge"

#. module: l10n_be_hr_payroll
#: model:ir.actions.server,name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation_ir_actions_server
#: model:ir.cron,cron_name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation
msgid "Belgian Payroll: Update time off allocations on schedule change"
msgstr ""
"Paie belge : Mettre à jour l'allocation des congés en cas de changement "
"d'horaire"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_configuration
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_reporting_l10n_be
msgid "Belgium"
msgstr "Belgique"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgique : Bilan social"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgique : Attestation de sécurité sociale"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Beneficiary holdings"
msgstr "Exploitations bénéficiaires"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__benefit_name
msgid "Benefit Name"
msgstr "Nom de l'avantage"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_company_car_annual
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car_2
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Benefit in Kind (Company Car)"
msgstr "Avantage de toute nature (Voiture de société)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet_2
msgid "Benefit in Kind (Internet)"
msgstr "Avantage de toute nature (Internet)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop_2
msgid "Benefit in Kind (Laptop)"
msgstr "Avantage de toute nature (Ordinateur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile_2
msgid "Benefit in Kind (Phone Subscription)"
msgstr "Avantage de toute nature (Abonnement de téléphone)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_atn_deduction
msgid "Benefit in Kind Deductions (All)"
msgstr "Déductions des avantages de toute nature (tous)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefit in kind deduction"
msgstr "Déduction de l'avantage de toute nature"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind (Company Car)"
msgstr "Avantage de toute nature (Voiture de société)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Benefits in kind and bonuses"
msgstr "Avantages de toute nature et bonus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind submitted to ONSS"
msgstr "Avantages de toute nature soumis à l'ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind without ONSS"
msgstr "Avantage de toute nature non soumis à l'ONSS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__has_bicycle
msgid "Bicycle to work"
msgstr "Se rend au travail en vélo"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_breast_feeding
msgid "Breastfeeding Break"
msgstr "Congé allaitement"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Business closure fund cotisation"
msgstr "Cotisation au fond de fermeture des entreprises"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__department
msgid "By Department"
msgstr "Par département"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__employee
msgid "By Employee"
msgstr "Par employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By contract type"
msgstr "Par type de contrat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By gender"
msgstr "Par sexe"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By professional category"
msgstr "By catégorie professionnelle"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By reason for termination of contract"
msgstr "Par motif de résiliation du contrat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "CANCEL"
msgstr "ANNULER"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdd
msgid "CDD"
msgstr "CDD"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdi
msgid "CDI"
msgstr "CDI"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cip
msgid "CIP"
msgstr "CIP"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_job_view_form
msgid "CP200 Category"
msgstr "Catégorie CP200"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_december_slip_wizard
msgid "CP200: December Slip Computation"
msgstr "CP200 : Calcul de la fiche de décembre"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line_wizard
msgid "CP200: Double Pay Recovery Line Wizard"
msgstr "CP200 : Ligne d'assistant de récupération du double pécule"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_wizard
msgid "CP200: Double Pay Recovery Wizard"
msgstr "CP200 : Assistant de récupération du double pécule"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"CSV format: you may edit it directly with your favorite spreadsheet "
"software, the rightmost column (value) contains the commission value over 3 "
"months. When you're done, reimport the file to generate the commission "
"payslips with the accurate commissions."
msgstr ""
"Format CSV : vous pouvez le modifier directement avec votre tableur préféré,"
" la colonne la plus à droite (valeur) contient la valeur de la commission "
"sur 3 mois. Lorsque vous avez terminé, réimportez le fichier pour générer "
"les fiches de paie des commissions avec les commissions exactes."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre I. - Calcul du Précompte Mobilier (Pr.M) à payer"
msgstr "Cadre I. - Calcul du précompte mobilier (Pr.M) à payer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre II. - Bénéficiaire(s) des revenus"
msgstr "Cadre II. - Bénéficiaire(s) des revenus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Calculation Basis"
msgstr "Base de calcul"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__3
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Cancel"
msgstr "Annuler"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_canteen
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_canteen_cost
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_l10n_be_canteen_cost
msgid "Canteen Cost"
msgstr "Coût de la cantine"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Canteen Costs"
msgstr "Coûts de cantine"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__capped_amount_34
msgid "Capped Amount"
msgstr "Montant de l'écrêtement"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Car BIK"
msgstr "ATN voiture"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__a
msgid "Category A"
msgstr "Catégorie A"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid ""
"Category A - Executive functions:\n"
"Included in this class are functions characterized by performing a limited number of simple and repetitive tasks. For example: the worker exclusively responsible for typing.\n"
"\n"
"Category B - Support functions.\n"
"Included in this class are functions characterized by making a contribution to the achievement of a larger mission. For example: the administrative employee or the receptionist.\n"
"\n"
"Category C - Management functions.\n"
"Included in this class are functions characterized by carrying out a complete set of tasks which, together, constitute one and the same mission. For example: the personnel administration employee or the PC technician.\n"
"\n"
"Category D - Advisory functions.\n"
"Included in this class are functions characterized by monitoring and developing the same professional process within the framework of a specific objective. For example: the programmer, accountant or consultant"
msgstr ""
"Catégorie A - Fonctions exécutives : \n"
"Cette classe comprend les fonctions caractérisées par l'exécution d'un nombre limité de tâches simples et répétitives. Par exemple : le travailleur exclusivement responsable de la saisie.\n"
"\n"
"Catégorie B - Fonctions de support.\n"
"Cette classe comprend les fonctions caractérisées par la contribution à la réalisation d'une mission plus vaste. Par exemple : l'employé administratif ou la réceptionniste. \n"
"\n"
"Catégorie C - Fonctions de gestion.\n"
"Cette classe comprend des fonctions caractérisées par la réalisation d'un ensemble complet de tâches qui, ensemble, constituent une seule et même mission. Par exemple : l'employé de l'administration du personnel ou le technicien PC.\n"
"\n"
"Catégorie D - Fonctions consultatives.\n"
"Sont comprises dans cette classe des fonctions caractérisées par le suivi et le développement d'un même processus professionnel dans le cadre d'un objectif précis. Par exemple : le programmeur, le comptable ou le consultant"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__b
msgid "Category B"
msgstr "Catégorie B"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__c
msgid "Category C"
msgstr "Catégorie C"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__d
msgid "Category D"
msgstr "Catégorie D"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__certificate
msgid "Certificate Level"
msgstr "Niveau du certificat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"Certificate Relating To Annual Holidays For The Civil Year Ending The "
"Contract Of Employment"
msgstr "Attestation de vacances"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"Certificate Relating To Annual Vacations For The Calendar Year Preceding The"
" End Of The Contract Of Employment"
msgstr "Attestation de vacances"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_pem_certificate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_pem_passphrase
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_pem_certificate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_pem_passphrase
msgid "Certificate to allow access to batch declarations"
msgstr "Attestation permettant l'accès aux déclarations par lot"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_wizard_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard
msgid "Change Employee Language"
msgstr "Changer la langue de l'employé"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard_line
msgid "Change Employee Language Line"
msgstr "Changer la langue de l'employé : Ligne"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_schedule_change_wizard
msgid "Change contract working schedule"
msgstr "Changer l'horaire de travail du contrat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Checkout"
msgstr "Caisse"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_child_alw
msgid "Child Allowance Belgium"
msgstr "Allocations familiales Belgique"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_child_support
msgid "Child Support"
msgstr "Pension alimentaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Circ. administrative 19.03.1982 Ci. RH. 241/315.785"
msgstr "Circ. administrative 19.03.1982 Ci. RH. 241/315.785"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__t
msgid "Circuit Test (T)"
msgstr "Test de circuit (T)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__classic_holiday_pay
msgid "Classic Holiday Pay"
msgstr "Pécule de vacances classique"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Close"
msgstr "Fermer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__code
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Code"
msgstr "Code"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_fixed_commission
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid "Commission"
msgstr "Commission"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__commission_amount
msgid "Commission Amount"
msgstr "Montant de la commission"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_commission_on_target
msgid "Commission on Target"
msgstr "Commissions sur vente"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions
msgid "Commissions"
msgstr "Commissions"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions_adjustment
msgid "Commissions Adjustment"
msgstr "Ajustement des commissions"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__company_id
msgid "Company"
msgstr "Société"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__company_car_total_depreciated_cost
msgid "Company Car Total Depreciated Cost"
msgstr "Coût total d'amortissement de la voiture de société"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_company_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_company_number
msgid "Company Number"
msgstr "Numéro d'entreprise"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_already_paid
msgid "Complementary Double Holidays (Already Paid)"
msgstr "Double pécule complémentaire (déjà payé)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_december
msgid "Complementary Double Holidays (Lost due to working time reduction)"
msgstr ""
"Double pécule complémentaire (perdu à cause d'une réduction du temps de "
"travail)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute December Holiday Pay"
msgstr "Calculer le pécule de vacances décembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute Double Pay Recovery"
msgstr "Calculer la récupération du double pécule"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"Computed value base on leaving type, arrival of the employee in the company,"
" ..."
msgstr ""
"Valeur calculée sur base du type de départ, de la date d'arrivée de "
"l'employé dans la société, ..."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure Default Values for Belgian Advantages"
msgstr "Configuration des valeurs par défaut pour les avantages belges"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure ONSS codes"
msgstr "Configuration des codes ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid "Confirm"
msgstr "Confirmer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_children
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_children
msgid "Considered number of dependent children"
msgstr "Nombre considéré d'enfants à charge"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_juniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_juniors
msgid "Considered number of dependent juniors"
msgstr "Nombre considéré de jeunes adultes à charge"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_seniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_seniors
msgid "Considered number of dependent seniors"
msgstr "Nombre considéré de seniors à charge"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__contract_id
msgid "Contract"
msgstr "Contrat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__contract_next_year_id
msgid "Contract Active Next Year"
msgstr "Contrat actif l'année prochaine"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Rapport d'analyse du contrat et de l'employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contract for the execution of a clearly defined work"
msgstr "Contrat de travail pour un travail nettement défini"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Historique des contrats"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution :"
msgstr "Contribution :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution Type"
msgstr "Type de contribution"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contributions paid and payments to collective funds"
msgstr "Cotisations versées et versements aux fonds collectifs"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_corona
msgid "Corona Unemployment"
msgstr "Chômage Corona"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"Count of dependent people/children or disabled dependent people/children "
"must be positive."
msgstr ""
"Le nombre de personnes/enfants à charge ou de personnes/enfants handicapés à"
" charge doit être positif."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid ""
"Count of disabled dependent people/children must be less or equal to the "
"number of dependent people/children."
msgstr ""
"Le nombre de personnes/enfants handicapés à charge doit être inférieur ou "
"égal au nombre de personnes/enfants à charge."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Create 274.XX Sheets"
msgstr "Créer une déclaration 274.XX"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_10_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Create 281.10 Form"
msgstr "Créer une fiche 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_45_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create 281.45 Form"
msgstr "Créer une fiche 281.45"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Create PDFs"
msgstr "Créer les PDFs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create XML"
msgstr "Créer le XML"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Create new contract and adapt time off allocation"
msgstr "Créer un nouveau contrat et adapter l'allocation de congé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_credit_time
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_credit_time
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "Crédit-temps"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "Crédit-temps"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Credit time contract"
msgstr "Contrat crédit-temps"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__currency_id
msgid "Currency"
msgstr "Devise"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Currency :"
msgstr "Devise :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count_description
msgid "Current Occupation Duration (Description)"
msgstr "Durée de l'occupation actuelle (Description)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count
msgid "Current Occupation Duration (Months)"
msgstr "Durée de l'occupation actuelle (Mois)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__current_resource_calendar_id
msgid "Current Resource Calendar"
msgstr "Calendrier actuel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Current Working Schedule"
msgstr "Horaire de travail actuel"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Current contract is finished before the end of the new contract."
msgstr "Le contrat actuel arrive à échéance avant la fin du nouveau contrat."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Cycle Allowance"
msgstr "Indemnité de vélo"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_cycle_transportation
msgid "Cycle Transportation (Days Count)"
msgstr "Transport par vélo (nombre de jours)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"
msgstr "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "DEC"
msgstr "DEC"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"
msgstr "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.hr_payslip_report_action_dmfa
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_dmfa
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "DMFA"
msgstr "DMFA"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "DMFA Employer Class"
msgstr "Catégorie d'employeur DMFA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_tree
msgid "DMFA Reports"
msgstr "Déclarations DMFA"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "DMFA code"
msgstr "Code DMFA"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa
msgid "DMFA xml report"
msgstr "Déclaration DMFA XML"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_dmfa_location_unit
msgid "DMFA: Work Locations"
msgstr "DMFA : Unités d'exploitations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Data for:"
msgstr "Données pour :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Date"
msgstr "Date"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_start
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_from
msgid "Date From"
msgstr "Date de début"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_end
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_to
msgid "Date To"
msgstr "Date de fin"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Date de réception de la déclaration :"
msgstr "Date de réception de la déclaration :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__day
msgid "Day"
msgstr "Jour"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__private_car_missing_days
msgid "Days Not Granting Private Car Reimbursement"
msgstr "Jours sans remboursement pour voiture privée"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__representation_fees_missing_days
msgid "Days Not Granting Representation Fees"
msgstr "Jours sans frais de représentation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Days Per Week :"
msgstr "Jours par semaine :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Days Unpaid time off current year"
msgstr "Jours de congés non rémunérés pour l'année courante"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Days Unpaid time off previous year"
msgstr "Jours de congés non rémunérés pour l'année précédente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__12
msgid "December"
msgstr "Décembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Pay"
msgstr "Pécule de décembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Slip"
msgstr "Fiche de décembre"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid ""
"Decides whether the employee will still work during his notice period or "
"not."
msgstr "Décide si l'employé preste son préavis ou non."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__s
msgid "Declaration Test (S)"
msgstr "Test de déclaration (S)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__declaration_type
msgid "Declaration Type"
msgstr "Type de déclaration"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_32
msgid "Deducted Amount 32"
msgstr "Montant déduit 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_33
msgid "Deducted Amount 33"
msgstr "Montant déduit 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_34
msgid "Deducted Amount 34"
msgstr "Montant déduit 34"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_deduction
msgid "Deduction"
msgstr "Déduction"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Type de prestation par défaut"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_calendar
msgid "Default Working Hours"
msgstr "Heures de travail par défaut"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__department_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__department_id
msgid "Department"
msgstr "Département"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_description
msgid "Departure Description"
msgstr "Description du départ"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_departure_reason
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__leaving_type_id
msgid "Departure Reason"
msgstr "Raison du départ"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_holiday_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_holiday_attests
msgid "Departure: Holiday Attests"
msgstr "Départ : Attestations de vacances"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_notice
msgid "Departure: Notice period"
msgstr "Départ : Période de préavis"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_notice_wizard_action
msgid "Departure: Notice period and payslip"
msgstr "Départ: Période de préavis et fiche de paie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Departures"
msgstr "Départs"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__name
msgid "Description"
msgstr "Description"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled
msgid "Disabled"
msgstr "Handicapé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "Disabled Children"
msgstr "Enfants handicapés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "Disabled Spouse"
msgstr "Conjoint handicapé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Discard"
msgstr "Rejeter"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Dismissal"
msgstr "Licenciement"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__display_l10n_be_scale
msgid "Display L10N Be Scale"
msgstr "Affichage du barême"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_dmfa
msgid "DmfA"
msgstr "DmfA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "DmfA Declaration ("
msgstr "Déclaration DmfA ("

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go_filename
msgid "Dmfa Go Filename"
msgstr "Nom du fichier Go Dmfa"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf_filename
msgid "Dmfa Pdf Filename"
msgstr "Nom du fichier PDF DmfA"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature_filename
msgid "Dmfa Signature Filename"
msgstr "Nom du fichier Signature Dmfa"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml_filename
msgid "Dmfa Xml Filename"
msgstr "Nom du fichier Dmfa Xml"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Doctors / Civil Engineers"
msgstr "Docteurs / Ingénieurs Civil"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Domicile, siège social ou siège du principal établissement administratif "
"(adresse complète) :"
msgstr ""
"Domicile, siège social ou siège du principal établissement administratif "
"(adresse complète) :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__done
msgid "Done"
msgstr "Fait"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_december_pay
msgid "Double December Pay"
msgstr "Double pécule de décembre"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_basic
msgid "Double December Pay Basic"
msgstr "Base du double pécule de décembre"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_gross
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category_gross
msgid "Double December Pay Gross"
msgstr "Double pécule de décembre brut"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_net
msgid "Double December Pay Net"
msgstr "Double pécule de décembre net"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_pp
msgid "Double December Pay Withholding Tax"
msgstr "Précompte professionnel du double pécule de décembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday"
msgstr "Double pécule"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_already_paid
msgid "Double Holiday (Already Paid)"
msgstr "Double pécule (déjà payé)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday Gross"
msgstr "Double pécule brut"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_dp
msgid "Double Holiday Pay"
msgstr "Double pécule"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_pay_december
msgid "Double Holiday Pay (Lost due to working time reduction)"
msgstr ""
"Double pécule de vacances (Perdu à cause d'une réduction du temps de "
"travail)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Amount:"
msgstr "Montant global double pécule de vacances :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Contributions:"
msgstr "Cotisations globales double pécule de vacances :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n
msgid "Double Holiday Pay N"
msgstr "Double pécule N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n1
msgid "Double Holiday Pay N-1"
msgstr "Double pécule N-1"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_recovery
msgid "Double Holiday Pay Recovery"
msgstr "Récupération de double pécule"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_double_holiday
msgid "Double Holidays Slip"
msgstr "Fiche de double pécule de vacances"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_december_slip_wizard_action
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Double Pay Recovery Computation"
msgstr "Calcul de la récupération du double pécule"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__double_pay_to_recover
msgid "Double Pay To Recover"
msgstr "Double pécule à récupérer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Download the 273S PDF file:"
msgstr "Télécharger la déclaration 273S en PDF:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the 274.XX PDF file:"
msgstr "Télécharger les déclarations 274.XX en PDF :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Download the 281.10 XML file:"
msgstr "Télécharger la fiche 281.10 en XML :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Download the 281.45 XML file:"
msgstr "Télécharger la fiche 281.45 en XML:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet PDF file:"
msgstr "Télécharger le bilan social en PDF :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate PDF file:"
msgstr "Télécharger l'attestation de sécurité sociale en PDF :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XLSX details file:"
msgstr "Télécharger le détail en XLSX :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XML Export file:"
msgstr "Télécharger le fichier d'export XML:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "During the exercise"
msgstr "Durant l'exercice"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Early holiday pay"
msgstr "Pécule de vacances anticipé"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_eco_checks
msgid "Eco Vouchers"
msgstr "Éco-chèques"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_eco_vouchers_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#, python-format
msgid "Eco-Vouchers"
msgstr "Eco-chèques"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_line_wizard
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_wizard
msgid "Eco-Vouchers Wizard"
msgstr "Assistant éco-chèques"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_economic_unemployment
msgid "Economic Unemployment"
msgstr "Chômage économique"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training_time_off
msgid "Educational Time Off"
msgstr "Congé éducation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__effective_date
msgid "Effective Date"
msgstr "Date effective"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "Employee"
msgstr "Employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employee :"
msgstr "Employé :"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contrat de l'employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Employee Departure"
msgstr "Départ de l'employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Employee Departure - Holiday Attests"
msgstr "Départ de l'employé - Attestations de vacances"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_reimbursement
msgid "Employee Reimbursement"
msgstr "Remboursement employé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__without
msgid "Employee doesn't work during his notice period"
msgstr "L'employé ne preste pas son préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__with
msgid "Employee works during his notice period"
msgstr "L'employé preste son préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "Employee's monthly gross wage for the new contract."
msgstr "Brut mensuel de l'employé pour le nouveau contrat."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Employee's working schedule."
msgstr "Emploi du temps de l'employé."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employees"
msgstr "Employés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employees Information"
msgstr "Informations de l'employé"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid Configured Gender"
msgstr "Employés dont le sexe configuré est invalide"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid Configured Language"
msgstr "Employés dont la langue configurée est invalide"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Employees With Invalid NISS Numbers"
msgstr "Employés dont les numéros NISS sont invalides"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Employer"
msgstr "Employeur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employer Class :"
msgstr "Catégorie d'employeur :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employer contribution to the Fund"
msgstr "Contribution patronale aux fonds"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__commercial
msgid "Employers with industrial or commercial purposes"
msgstr "Employeurs avec un but industriel ou commercial"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__non_commercial
msgid "Employers without industrial or commercial purposes"
msgstr "Employeurs sans but industriel ou commercial"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_employees
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_employment_bonus
msgid "Employment Bonus"
msgstr "Bonus à l'emploi"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End Date"
msgstr "Date de fin"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_end
msgid "End Period"
msgstr "Fin de la période"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End date of the new contract."
msgstr "Date de fin du nouveau contrat."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__end_notice_period
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__end_notice_period
msgid "End notice period"
msgstr "Date de fin du préavis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "End-of-year bonus, 13th month or other similar amount"
msgstr "Bonus de fin d'année, 13ème mois ou autres montants similaires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Entries"
msgstr "Entrées"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__error_message
msgid "Error Message"
msgstr "Message d'erreur"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "Error while importing file"
msgstr "Erreur lors de l'import du fichier"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Established on"
msgstr "Etabli le"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Established on :"
msgstr "Etabli le :"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_european_leaves_deduction
#, python-format
msgid "European Leaves Deduction"
msgstr "Déduction des congés européens"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_european
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_european
msgid "European Time Off"
msgstr "Congé européen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Exempted Amount"
msgstr "Montant exonéré"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_expatriate
msgid "Expatriate Allowance"
msgstr "Indemnité d'expatrié"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid "Expense Fees"
msgstr "Frais de représentations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export"
msgstr "Exporter"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export Complete"
msgstr "Export complet"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Export Employee Leaves for SD Worx"
msgstr "Exporter les congés de l'employé pour SDWorx"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__export_file
msgid "Export File"
msgstr "Exporter le fichier"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__export_filename
msgid "Export Filename"
msgstr "Nom du fichier d'export"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_export_sdworx_leaves_wizard
msgid "Export Leaves to SDWorx"
msgstr "Exporter les congés vers SDWorx"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Export PDF file"
msgstr "Exporter le fichier PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__group_export_sdworx_leaves
#: model:res.groups,name:l10n_be_hr_payroll.group_export_sdworx_leaves
msgid "Export Time Off to SDWorx"
msgstr "Exporter les congés vers SDWorx"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_export_sdworx_leaves_wizard
#, python-format
msgid "Export Work Entries to SDWorx"
msgstr "Exporter les prestations vers SDWorx"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Export XLS"
msgstr "Exporter le XLS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XLSX details"
msgstr "Exporter le détail XLSX"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XML file"
msgstr "Exporter le fichier XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__export
msgid "Export the employees file"
msgstr "Exporter le fichier des employés"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_extra_legal
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_extra_legal
msgid "Extra Legal Time Off"
msgstr "Congé extra-légal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "FEB"
msgstr "FEV"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "FINPROF"
msgstr "FINPROF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__2
msgid "February"
msgstr "Février"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Female"
msgstr "Féminin"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_ffe_employer_type
msgid "Ffe Employer Type"
msgstr "Type d'employeur Ffe"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.10: Précompte professionnel versé."
msgstr "Fiche 274.10 : Précompte professionnel versé."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"Fiche 274.32: Dispense de précompte professionnel pour doctorants/ingénieurs"
" civils."
msgstr ""
"Fiche 274.32 : Dispense de précompte professionnel pour "
"doctorants/ingénieurs civils."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.33: Dispense de précompte professionnel pour master."
msgstr "Fiche 274.33 : Dispense de précompte professionnel pour master."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.34: Dispense de précompte professionnel pour bacheliers."
msgstr "Fiche 274.34 : Dispense de précompte professionnel pour bacheliers."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "Field Year does not seem to be a year. It must be an integer."
msgstr "Le champ \"Année\" se semble pas être une année. Ce doit être un entier."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__file_type
msgid "File Type"
msgstr "Type de fichier"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_in_company
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "First contract in company"
msgstr "Premier contrat dans la société"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntarism
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntarism
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Fiscal Voluntarism"
msgstr "Volontariat fiscal"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntary_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntary_rate
msgid "Fiscal Voluntary Rate"
msgstr "Taux de volontariat fiscal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Fixed and variable remuneration"
msgstr "Rémunérations fixes et variables"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Fixed term contract (CDD)"
msgstr "Contrat à durée déterminée (CDD)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_flemish_training_time_off
msgid "Flemish Educational Time Off"
msgstr "Congé éducation flamand"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "For The Period"
msgstr "Pour la période"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Foreign Expenses"
msgstr "Notes de frais étrangères"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Formal continuous trainings at the employer's expense"
msgstr "Formations continues formelles aux frais de l'employeur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__found_leave_allocation
msgid "Found Leave Allocation"
msgstr "Allocation de congé trouvée"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déductibles"
msgstr "Frais déductibles"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déduits"
msgstr "Frais déduits"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Frequency (Month)"
msgstr "Fréquence (mois)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "From"
msgstr "De"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_fuel_card
msgid "Fuel Card"
msgstr "Carte essence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Full Time"
msgstr "À plein temps"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fte
msgid "Full Time Equivalent (Today)"
msgstr "Équivalent temps plein (Aujourd'hui)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_wage
msgid "Full Time Equivalent Wage"
msgstr "Salaire en équivalent temps plein"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_time_off_allocation
msgid "Full Time Off Allocation"
msgstr "Allocation de congé à temps plein"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_resource_calendar_id
msgid "Full Working Schedule"
msgstr "Horaire temps plein"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Fund or company"
msgstr "Fonds ou société"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_gross
msgid "GROSS"
msgstr "BRUT"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Generate Export File"
msgstr "Générer le fichier d'export"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate PDF report"
msgstr "Générer la déclaration en PDF"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Generate Payslips"
msgstr "Générer les fiches de paie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Generate Time Off Allocations"
msgstr "Générer les allocations de congés"

#. module: l10n_be_hr_payroll
#. odoo-javascript
#: code:addons/l10n_be_hr_payroll/static/src/xml/payslip_batch_tree_view.xml:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.action_hr_payroll_generate_warrant_payslips
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#, python-format
msgid "Generate Warrant Payslips"
msgstr "Générer les fiches de paie de warrants"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips_line
msgid "Generate Warrant Payslips Lines"
msgstr "Générer les lignes de fiches de paie de warrants"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate XML report"
msgstr "Générer la déclaration en XML"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Générer les fiches de paie pour tous les employés sélectionnés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Generation Complete"
msgstr "Génération complète"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_273s
msgid "Get 273S report as PDF."
msgstr "Obtenir la déclaration 273S au format PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_274_10
msgid "Get 274.10 report as PDF."
msgstr "Obtenir la déclaration 274.10 au format PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_281_10
msgid "Get 281.10 report as PDF."
msgstr "Obtenir la fiche 281.10 au format PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_281_45
msgid "Get 281.45 report as PDF."
msgstr "Obtenir la fiche 281.45 au format PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_dmfa_pdf_report
msgid "Get DmfA declaration as PDF"
msgstr "Obtenir la déclaration DmfA en PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_balance
msgid "Get Social Balance Sheet as PDF"
msgstr "Obtenir le bilan social au format PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_security_certificate
msgid "Get Social Security Certificate as PDF"
msgstr "Obtenir l'attestation de charges sociales au format PDF"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gifts in kind"
msgstr "Cadeaux en nature"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Global Information"
msgstr "Informations globales"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go
msgid "Go file"
msgstr "Fichier Go"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Grants and other financial benefits received (to be deducted)"
msgstr "Subventions et autres avantages financiers reçus (à déduire)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_salary
msgid "Gross"
msgstr "Brut"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n
msgid "Gross Annual Remuneration Current Year"
msgstr "Rémunération annuelle brute année actuelle"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n1
msgid "Gross Annual Remuneration Previous Year"
msgstr "Rémunération annuelle brute année précédente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_december_2013
msgid "Gross Annual Salary as of December 31, 2013"
msgstr "Salaire annuel brut au 31 décembre 2013"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_salary
msgid "Gross Double December Pay Salary"
msgstr "Double pécule de décembre brut"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_gross_salary
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__gross_salary
msgid "Gross Salary"
msgstr "Salaire brut"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic_12_92
msgid "Gross Yearly Salary"
msgstr "Salaire brut annuel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__inferior
msgid "Gross annual salary < 32.254 €"
msgstr "Salaire annuel brut < 32.254 €"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__superior
msgid "Gross annual salary > 32.254 €"
msgstr "Salaire annuel brut > 32.254 €"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Gross cost directly linked to training"
msgstr "Coût brut directement lié à la formation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Gross reference remuneration"
msgstr "Rémunération brute de référence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gross salary before ONSS"
msgstr "Salaire brut avant ONSS"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_group_insurance
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_group_insurance
msgid "Group Insurance (Employer)"
msgstr "Assurance groupe (employeur)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_group_insurance_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Group Insurance Export"
msgstr "Exporter les assurances groupe"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Group Insurance Global Contributions:"
msgstr "Cotisations globales pour l'assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
msgid "Group Insurance Sacrifice Rate"
msgstr "Taux de sacrifice assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_wizard
msgid "Group Insurance Wizard"
msgstr "Assistant assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_line_wizard
msgid "Group Insurance Wizard Line"
msgstr "Ligne d'assistant assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account
msgid "HR Individual Account Report By Employee"
msgstr "Décompte Individuel par employé"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account_line
msgid "HR Individual Account Report By Employee Line"
msgstr "Ligne de décompte individuel par employé"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10_line
msgid "HR Payroll 281.10 Line Wizard"
msgstr "Ligne d'assistant 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "Assistant 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45_line
msgid "HR Payroll 281.45 Line Wizard"
msgstr "Ligne d'assistant 281.45"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "Assistant pour 281.45"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Prestation RH"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Type de prestations RH"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_has_ambulatory_insurance
msgid "Has Ambulatory Insurance"
msgstr "A une assurance ambulatoire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_hospital_insurance
msgid "Has Hospital Insurance"
msgstr "A une assurance hospitalisation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid "Has Valid Schedule Change Contract"
msgstr "A un contrat valide de changement d'horaire"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_hiring_bonus
msgid "Hiring Bonus"
msgstr "Bonus à l'emploi"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n_holidays
msgid "Holiday Pay (N Year)"
msgstr "Pécule de vacances (Année N)"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n1_holidays
msgid "Holiday Pay (N-1 Year)"
msgstr "Pécule de vacances (Année N-1)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Provision"
msgstr "Provision au pécule de vacances"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n
msgid "Holiday Pay Recovery (Year N)"
msgstr "Récupération du pécule de vacances (Année N)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n1
msgid "Holiday Pay Recovery (Year N-1)"
msgstr "Récupération du pécule de vacances (Année N-1)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Supplement Retenue"
msgstr "Retenues sur le double pécule de sortie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
msgid "Holiday pay amount on the holiday attest from the previous employer"
msgstr ""
"Montant du pécule de vacance sur l'attestation de l'employeur précédent"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Holiday pay details:"
msgstr "Détails du pécule de vacances :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday pay supplement"
msgstr "Double pécule de sortie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__km_home_work
msgid "Home-Work Distance"
msgstr "Distance Domicile-Lieu de Travail"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_residence
msgid "Home/Residence Allowance"
msgstr "Allocation de logement/résidence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Hospital Insurance"
msgstr "Assurance hospitalisation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_adult
msgid "Hospital Insurance Amount per Adult"
msgstr "Montant de l'assurance hospitalisation par adulte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_child
msgid "Hospital Insurance Amount per Child"
msgstr "Montant de l'assurance hospitalisation par enfant"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_hospital_insurance_notes
msgid "Hospital Insurance: Additional Info"
msgstr "Assurance hospitalisation : Informations supplémentaires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Hours/Week"
msgstr "Heures/semaine"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "IDENTIFICATION DU REDEVABLE"
msgstr "IDENTIFICATION DU REDEVABLE"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
msgid "IP percentage"
msgstr "Pourcentage d'IP"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_ip_part
msgid "IP. Part."
msgstr "IP. Part."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Identification du bénéficiaire"
msgstr "Identification du bénéficiaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Identification of the company"
msgstr "Identification de la société"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid ""
"If checked, the wizard will create another contract after the new working "
"schedule contract, with current working schedule"
msgstr ""
"Si coché, l'assistant créera un autre contrat après le nouveau contrat "
"d'horaire de travail, avec l'horaire de travail actuel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "If other people are dependent on the employee"
msgstr "Si d'autres personnes sont à charge de l'employé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled
msgid "If the employee is declared disabled by law"
msgstr "Si l'employé est déclaré handicapé par la loi"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Import Complete"
msgstr "Import terminé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__import_file
msgid "Import File"
msgstr "Importer le fichier"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__import
msgid "Import the employee file"
msgstr "Importer le fichier de l'employé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_impulsion_plan
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Impulsion Plan"
msgstr "Plan d'impulsion"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Included between 0 and 100%"
msgstr "Inclus entre 0 et 100%"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_individual_account
msgid "Individual Account"
msgstr "Décompte individuel"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_individual_account
msgid "Individual Account Report"
msgstr "Rapport de décompte individuel"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_individual_account_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Individual Accounts"
msgstr "Décomptes individuels"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#, python-format
msgid "Individual Accounts - Year %s"
msgstr "Décompte individuel - Année %s"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_individual_account
msgid "Indivual Accounts"
msgstr "Décomptes individuels"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Informal continuous trainings at the employer's expense"
msgstr "Formations continues informelles aux frais de l'employeur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Information on training for workers during the exercise"
msgstr "Informations sur la formation des travailleurs pendant l'exercice"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__initial_time_off_allocation
msgid "Initial Time Off Allocation"
msgstr "Allocation de congé initiale"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Initial trainings at the employer's expense"
msgstr "Formations initiales aux frais de l'employeur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insurance_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insurance Amount"
msgstr "Montant de l'assurance"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults_total
msgid "Insured Relative Adults Total"
msgstr "Nombre total des adultes assurés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_spouse
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insured Spouse"
msgstr "Conjoint(e) assuré(e)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_part
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip
msgid "Intellectual Property"
msgstr "Propriété intellectuelle"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_deduction
msgid "Intellectual Property Income Deduction"
msgstr "Déduction sur la propriété intellectuelle"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_internet
msgid "Internet"
msgstr "Internet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__internet
msgid "Internet Subscription"
msgstr "Abonnement Internet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__invalid
msgid "Invalid"
msgstr "Invalide"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"Invalid NISS number for those employees:\n"
" %s"
msgstr ""
"Le numéro NISS est invalide pour les employés suivants :\n"
" %s"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_value
msgid "Ip Value"
msgstr "Valeur d'IP"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__is_test
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__is_test
msgid "Is It a test ?"
msgstr "Est-ce un test ?"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale
msgid "Is below CP200 salary scale"
msgstr "Est en dessous des barême CP200"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JAN"
msgstr "JAN"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUL"
msgstr "JUIL"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUN"
msgstr "JUIN"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__1
msgid "January"
msgstr "Janvier"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_job
msgid "Job Position"
msgstr "Fonction"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Joint Commission :"
msgstr "Commission paritaire :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__7
msgid "July"
msgstr "Juillet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__6
msgid "June"
msgstr "Juin"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_key
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_key
msgid "KEY file"
msgstr "Fichier KEY"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Keep Time Off Right"
msgstr "Conserve le droit aux vacances"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_key
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_key
msgid "Key to allow access to batch declarations"
msgstr "Clé permettant l'accès aux déclarations de lots"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults_total
msgid "L10N Be Ambulatory Insured Adults Total"
msgstr "Nombre total des adultes assurés ambulatoire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_run__l10n_be_display_eco_voucher_button
msgid "L10N Be Display Eco Voucher Button"
msgstr "Affichage du bouton éco-chèques"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_ffe_employer_type
msgid "L10N Be Ffe Employer Type"
msgstr "Type d'employeur FFE"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_amount
msgid "L10N Be Group Insurance Amount"
msgstr "Montant assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_cost
msgid "L10N Be Group Insurance Cost"
msgstr "Coût de l'assurance groupe"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale_warning
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale_warning
msgid "L10N Be Is Below Scale Warning"
msgstr "Alerte en dessous des barêmes"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_december
msgid "L10N Be Is December"
msgstr "Est en décembre"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_double_pay
msgid "L10N Be Is Double Pay"
msgstr "Est un double pécule"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_amount
msgid "L10N Be Max Seizable Amount"
msgstr "Montant max saisissable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_warning
msgid "L10N Be Max Seizable Warning"
msgstr "Avertissement montant max saisissable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid "L10N Be Scale Category"
msgstr "Catégorie d'employé CP200"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__lang
msgid "Language"
msgstr "Langue"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "Laptop"
msgstr "Ordinateur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate____last_update
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Le précompte mobilier est supporté par le débiteur des revenus à la décharge"
" du bénéficiaire :"
msgstr ""
"Le précompte mobilier est supporté par le débiteur des revenus à la décharge"
" du bénéficiaire :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_allocation_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__leave_allocation_id
msgid "Leave Allocation"
msgstr "Allocation de congé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__leave_ids
msgid "Leaves"
msgstr "Congés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__line_ids
msgid "Line"
msgstr "Ligne"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__line_ids
msgid "Lines"
msgstr "Lignes"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_long_sick
msgid "Long Term Sick"
msgstr "Maladie de longue durée"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAR"
msgstr "MAR"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAY"
msgstr "MAI"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Made by Odoo"
msgstr "Fait par Odoo"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Male"
msgstr "Masculin"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_paid_leave
msgid "Manage the Allocation of Paid Time Off"
msgstr "Gérer les allocations de congés payés"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_employee
msgid "Manage the Allocation of Paid Time Off Employee"
msgstr "Gérer les allocations de congés payés - Employés"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_notice
msgid "Manage the Employee Departure - Notice Duration"
msgstr "Gérer le départ de l'employé - Durée du préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_holiday_attests
msgid "Manage the Employee Departure Holiday Attests"
msgstr "Gérer le départ de l'employé - Attestations de vacances"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Management staff"
msgstr "Personnel de direction"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__3
msgid "March"
msgstr "Mars"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Mark as reported in payslip"
msgstr "Marquer comme reporté dans la fiche de paie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__certificate__civil_engineer
msgid "Master: Civil Engineering"
msgstr "Master : Ingénieur civil"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Masters"
msgstr "Masters"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_maternity
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_maternity
msgid "Maternity Time Off"
msgstr "Congé maternité"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave_allocation__max_leaves_allocated
msgid "Max Leaves Allocated"
msgstr "Nombre maximal de congés alloués"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__maximum_days
msgid "Maximum Days"
msgstr "Nombre de jours maximum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__5
msgid "May"
msgstr "Mai"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ch_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Meal Voucher"
msgstr "Chèque-repas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_employer
msgid "Meal Voucher (Employer)"
msgstr "Chèques-repas (Cotisation patronale)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_average_monthly_amount
msgid "Meal Voucher Average Monthly Amount"
msgstr "Montant mensuel moyen en chèques-repas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__meal_voucher_count
msgid "Meal Voucher Count"
msgstr "Nombre de chèques-repas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_monthly_by_employer
msgid "Meal Voucher Paid Monthly By Employer"
msgstr "Chèques-repas : Cotisation patronale"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_by_employer
msgid "Meal Voucher Paid by Employer"
msgstr "Chèques-repas : Cotisation de l'employé"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_action
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_meal_voucher_amount
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_meal_voucher
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_view_pivot
msgid "Meal Vouchers"
msgstr "Chèques-repas"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employee Part)"
msgstr "Chèques-repas (Cotisation de l'employé)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employer Part)"
msgstr "Chèques-repas (Cotisation patronale)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Mean Working Hours :"
msgstr "Temps de travail moyen :"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_medical_assistance
msgid "Medical Assistance"
msgstr "Assistance médicale"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_net
msgid "Misc. Net"
msgstr "Div. Net"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_impos
msgid "Misc. Taxable"
msgstr "Div. Imposable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_mobile
msgid "Mobile"
msgstr "Mobile"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "Mobile Subscription"
msgstr "Abonnement téléphone"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Mobility Bonus"
msgstr "Prime de mobilité"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__1
msgid "Modification"
msgstr "Modification"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant brut des revenus"
msgstr "Montant brut des revenus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant du Pr.M"
msgstr "Montant du Pr.M"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant imposable"
msgstr "Montant imposable"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant à payer :"
msgstr "Montant à payer :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__month
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__month
msgid "Month"
msgstr "Mois"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ambulatory_insurance
msgid "Monthly ambulatory insurance (employer's share)"
msgstr "Assurance ambulatoire mensuelle (part employeur)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fuel_card
msgid "Monthly amount the employee receives on his fuel card."
msgstr "Montant mensuel que le salarié reçoit sur sa carte carburant."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_benefit_in_kind
msgid "Monthly benefit in kind"
msgstr "Avantages de toute nature mensuels"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid ""
"Monthly gross amount that the employee receives if the target is reached."
msgstr ""
"Montant mensuel brut que le salarié perçoit si l'objectif est atteint."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_group_insurance
msgid "Monthly group insurance (employer's share)"
msgstr "Assurance groupe mensuelle (part de l'employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_hospital_insurance
msgid "Monthly hospital insurance (employer's share)"
msgstr "Assurance hospitalisation mensuelle (part de l'employeur)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid ""
"Monthly net amount the employee receives to cover his representation fees."
msgstr ""
"Montant net mensuel que l'employé reçoit pour couvrir ses frais de "
"représentation."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "Multiple_PRP_Declaration"
msgstr "Multiple_PRP_Declaration"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N Year"
msgstr "Année N"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N-1 Year"
msgstr "Année N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__normal
msgid "N/A"
msgstr "N/D"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__niss
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__niss
msgid "NISS"
msgstr "NISS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "NISS :"
msgstr "NISS :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__niss
msgid "NISS Number"
msgstr "Numéro NISS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "NOV"
msgstr "NOV"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__name
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name"
msgstr "Nom"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_negative_net
msgid "Negative Net"
msgstr "Net négatif"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_net_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_net_termination
msgid "Net"
msgstr "Net"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_warrant_deduction
msgid "Net Deductions from Wages"
msgstr "Déduction nette des salaires"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_net
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net Salary"
msgstr "Salaire net"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Net cost to the business"
msgstr "Coût net pour l'entreprise"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_12mo
msgid "Net part payable by the Onem (12+ months)"
msgstr "Part nette à la charge de l'Onem (12+ mois)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_25yo
msgid "Net part payable by the Onem (< 25 years old)"
msgstr "Part nette à la charge de l'Onem (< 25 ans)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net salary paid by third party"
msgstr "Salaire net payé par un tiers"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__new_resource_calendar_id
msgid "New Resource Calendar"
msgstr "Nouveau calendrier"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "New Wage"
msgstr "Nouveau salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__resource_calendar_id
msgid "New Working Schedule"
msgstr "Nouvel horaire de travail"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_schedule_change_allocation.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "New working schedule on %(contract_name)s.<br/>New total : %(days)s"
msgstr ""
"Nouvel horaire de travail pour %(contract_name)s.<br/>Nouveau total : "
"%(days)s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "No"
msgstr "Non"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No KEY file defined on the Payroll Configuration"
msgstr "Aucun fichier KEY défini sur la configuration de la paie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_onss
msgid "No ONSS"
msgstr "Pas d'ONSS"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"No ONSS registration number nor company ID was found for company %s. Please "
"provide at least one."
msgstr ""
"Aucun numéro d'enregistrement ONSS ou numéro d'entreprise n'a été trouvé "
"pour la société %s. Veuillez fournir au moins un de ces numéros."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No PEM Certificate defined on the Payroll Configuration"
msgstr "Aucun certificat PEM défini sur la configuration de la paie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_withholding_taxes
msgid "No Withholding Taxes"
msgstr "Pas de précompte professionnel"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#, python-format
msgid "No first contract date found for employee %s"
msgstr "Aucune date de premier contrat trouvée pour l'employé %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "No start/end notice period defined for %s"
msgstr "Pas de début/fin de préavis défini pour %s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Nom, prénoms, dénomination sociale ou officielle :"
msgstr "Nom, prénoms, dénomination sociale ou officielle :"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_non_respect_motivation
msgid "Non respect motivation (= 2 weeks)"
msgstr "Pas de motif de renvoi (= 2 semaines)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Non-university higher education"
msgstr "Enseignement supérieur non universitaire"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "None"
msgstr "Aucun"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__resident_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__resident_bool
msgid "Nonresident"
msgstr "Non-résident"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid ""
"Note: The double holiday pay should only be computed if the reduction in "
"working time took place between 01/01 and 30/06 compared to year N-1."
msgstr ""
"Note : Le double pécule de vacance ne doit être calculé que si la réduction "
"du temps de travail a eu lieu entre le 01/01 et le 30/06 par rapport à "
"l'année N-1."

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_notice
msgid "Notice (Unprovided)"
msgstr "Préavis (non fourni)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_notice_duration
msgid "Notice Duration"
msgstr "Durée du préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_month_before_2014
msgid "Notice Duration in month"
msgstr "Durée du préavis en mois"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_week_after_2014
msgid "Notice Duration in weeks"
msgstr "Durée du préavis en semaines"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "Notice duration"
msgstr "Durée du préavis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice duration computed"
msgstr "Durée du préavis calculée"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#, python-format
msgid ""
"Notice period not set for %s. Please, set the departure notice period first."
msgstr ""
"La durée du préavis n'est pas spécifiée pour %s. Veuillez d'abord définir la"
" période de préavis."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_salary
msgid "Notice salary"
msgstr "Salaire lié au préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__11
msgid "November"
msgstr "Novembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of Affected Employees"
msgstr "Nombre de travailleurs concernés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Number of Days"
msgstr "Nombre de jours"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Number of Hours"
msgstr "Nombre d'heures"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of completed training hours"
msgstr "Nombre d'heures de formation effectuées"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Number of days of unpaid time off taken during current year"
msgstr "Nombre de jours de congés non rémunérés durant l'année courante"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Number of days of unpaid time off taken during previous year"
msgstr "Nombre de jours de congés non rémunérés durant l'année précédente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days on which you should recover the holiday pay."
msgstr ""
"Nombre de jours sur lesquels vous devez récupérer le pécule de vacances."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
msgid "Number of days to recover (N)"
msgstr "Nombre de jours à récupérer (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days to recover (N-1)"
msgstr "Nombre de jours à récupérer (N-1)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of dependent children declared as disabled"
msgstr "Nombre d'enfants à charge déclarés handicapés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_number
msgid "Number of disabled children"
msgstr "Nombre d'enfants handicapés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of hours"
msgstr "Nombre d'heures"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid ""
"Number of juniors dependent on the employee, including the disabled ones"
msgstr ""
"Nombre de jeunes adultes à charge de l'employé, y compris les handicapés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of overtime hours"
msgstr "Nombre d'heures supplémentaires"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid ""
"Number of seniors dependent on the employee, including the disabled ones"
msgstr "Nombre de seniors à charge de l'employé, y compris les handicapés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of workers"
msgstr "Nombre de travailleurs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Numbers of joint committees : 20000"
msgstr "Numéros de commissions paritaires : 20000"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° d’entreprise ou, à défaut, n° national :"
msgstr "N° d’entreprise ou, à défaut, n° national :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° téléphone :"
msgstr "N° téléphone :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N°273S - 2020"
msgstr "N°273S - 2020"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "OCT"
msgstr "OCT"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss
msgid "ONSS"
msgstr "ONSS"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_termination
msgid "ONSS (Double Holiday)"
msgstr "ONSS (Double pécule)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer
msgid "ONSS (Employer)"
msgstr "ONSS (Employeur)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_onss_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_onss_termination
msgid "ONSS (Simple Holiday)"
msgstr "ONSS (Pécule simple)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_onss_total
msgid "ONSS (TOTAL)"
msgstr "ONSS (TOTAL)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_total
msgid "ONSS (Total)"
msgstr "ONSS (Total)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "ONSS Company ID"
msgstr "ONSS Numéro d'entreprise"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Company ID :"
msgstr "ONSS Numéro d'entreprise :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation"
msgstr "ONSS Cotisations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation on termination fees"
msgstr "ONSS Cotisations sur les indemnités de rupture"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation: Charges redistribution"
msgstr "ONSS Cotisations : Redistribution des charges"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer_detail
msgid "ONSS Detail (Employer)"
msgstr "Détails ONSS (employeur)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Double Holiday"
msgstr "ONSS Double pécule de vacances"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "ONSS Employer"
msgstr "ONSS Employeur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid "ONSS Expeditor Number"
msgstr "Numéro d'expéditeur ONSS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid ""
"ONSS Expeditor Number provided when registering service on the technical "
"user"
msgstr ""
"Numéro d'expéditeur ONSS fourni lors de l'enregistrement du service sur "
"l'utilisateur technique"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Number :"
msgstr "Numéro ONSS :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "ONSS Reduction"
msgstr "ONSS Réductions"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "ONSS Registration Number"
msgstr "ONSS Numéro d'enregistrement"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Registration Number :"
msgstr "Numéro d'enregistrement ONSS :"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_restructuring
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_onss_restructuring
msgid "ONSS Restructuring Reduction"
msgstr "ONSS Réduction pour restructuration"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Student"
msgstr "ONSS Etudiants"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Thirteen Month"
msgstr "ONSS 13ème mois"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS cotisation"
msgstr "ONSS cotisation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS termination fees cotisation"
msgstr "ONSS cotisations sur indemnités de rupture"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Occasional worker in the Horeca sector"
msgstr "Travailleur occasionnel dans le secteur horeca"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__line_ids
msgid "Occupation Lines"
msgstr "Lignes d'occupation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Occupation Rate"
msgstr "Taux d'occupation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Occupations #"
msgstr "# Occupations"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__10
msgid "October"
msgstr "Octobre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Offical Company Information"
msgstr "Informations officielles sur l'entreprise"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__oldest_contract_id
msgid "Oldest Contract"
msgstr "Plus vieux contrat"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid ""
"Only employees with a Bachelor/Master/Doctor/Civil Engineer degree can "
"benefit from the withholding taxes exemption."
msgstr ""
"Seuls les employés titulaires d'un diplôme de "
"bachelier/master/docteur/ingénieur civil peuvent bénéficier de l'exonération"
" du précompte professionnel."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa__unique
msgid ""
"Only one DMFA per year and per quarter is allowed. Another one already "
"exists."
msgstr ""
"Seulement une déclaration DMFA est autorisée par an et par trimestre. Une "
"autre existe déjà."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll/report/hr_contract_history.py:0
#, python-format
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__0
msgid "Original"
msgstr "Original"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__0
msgid "Original send"
msgstr "Envoi original"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "Other Dependent People"
msgstr "Autres personnes à charge"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Other employer contributions"
msgstr "Autres contributions patronales"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Other exempted amount from ONSS"
msgstr "Autre montant exonéré d'ONSS"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_other_annual
msgid "Other monthly/yearly"
msgstr "Autre mensuel/annuel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Others"
msgstr "Autres"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_outplacement
msgid "Outplacement (if more than 30 weeks notice duration)"
msgstr "Externalisation (si le préavis est supérieur à 30 semaines)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Overseas Social Security"
msgstr "Sécurité Sociale d'Outre-mer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Overtime worked"
msgstr "Heures supplémentaires effectuées"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_file
msgid "PDF File"
msgstr "Fichier PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_filename
msgid "PDF Filename"
msgstr "Nom de fichier PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf
msgid "PDF file"
msgstr "Fichier PDF "

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_pem_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_pem_certificate
msgid "PEM Certificate"
msgstr "Certificat PEM"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_pem_passphrase
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_pem_passphrase
msgid "PEM Passphrase"
msgstr "Phrase secrète PEM"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_pfi
msgid "PFI"
msgstr "PFI"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_cl
msgid "Paid Time Off"
msgstr "Congés payés"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.allocating_paid_time_off_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_allocating_paid_time_off_view
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
#, python-format
msgid "Paid Time Off Allocation"
msgstr "Allocation de congés payés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "Paid Time Off For The Period"
msgstr "Congés payés pour la période"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid "Paid Time Off To Allocate"
msgstr "Congés payés à allouer"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_parental_time_off
msgid "Parental Time Off"
msgstr "Congé parental"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__part_time
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_search
msgid "Part Time"
msgstr "Temps partiel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Part Time :"
msgstr "Temps partiel :"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "Part Time of %s must be stated at %s."
msgstr "Le temps partiel de %s doit être notifié à %s."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Part-Time"
msgstr "Temps partiel"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_partial_incapacity
msgid "Partial Incapacity (due to illness)"
msgstr "Incapacité partielle (pour cause de maladie)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_legal
msgid "Paternity Time Off (Legal)"
msgstr "Congé paternité (Légal)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_company
msgid "Paternity Time Off (Paid by Company)"
msgstr "Congé paternité (Payé par la société)"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Fiche de paie"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double
msgid "Pay double"
msgstr "Double pécule"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_complementary
msgid "Pay double complementary"
msgstr "Double pécule complémentaire"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_pay_variable_salary
msgid "Pay on variable salary (15.34 of the annual amount)"
msgstr "Rémunération sur salaire variable (15,34 du montant annuel)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_simple
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_simple
msgid "Pay simple"
msgstr "Simple pécule"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Payment Structured Communication:"
msgstr "Communication structurée du paiement :"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Rapport d'analyse de paie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__payslip_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip"
msgstr "Fiche de paie"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lots de fiches de paie"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_double_holiday_13th_month
msgid "Payslip Double Holidays / 13th Month"
msgstr "Fiche de paie double pécule / 13ème mois"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr "Fiche de paie prestations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip current year"
msgstr "Fiches de paie de l'année courante"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip previous year"
msgstr "Fiches de paie de l'année précédente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n_ids
msgid "Payslips N"
msgstr "Fiches de paie N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n1_ids
msgid "Payslips N-1"
msgstr "Fiches de paie N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_filename
msgid "Pdf Filename"
msgstr "Nom du fichier PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__pdf_to_generate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__pdf_to_generate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__pdf_to_generate
msgid "Pdf To Generate"
msgstr "PDF à générer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Pension"
msgstr "Pension"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Period"
msgstr "Période"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Period : From"
msgstr "Période : Du"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Permanent contract (CDI)"
msgstr "Contrat à durée indéterminée (CDI)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"Please configure a gender (either male or female) for the following employees:\n"
"\n"
"%s"
msgstr ""
"Veuillez configurer un sexe (soit masculin, soit féminin) pour les employés suivants :\n"
"\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid ""
"Please configure the 'Company Number' and the 'Revenue Code' on the Payroll "
"Settings."
msgstr ""
"Veuillez configurer le 'Numéro d'entreprise' et le 'Code de revenu' dans les"
" configurations du module de paie."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Please find attached some data that will be useful to you to establish the "
"Social Report for the accounting year noted below. The Social Report for the"
" previous year may be useful for you to complete information concerning the "
"previous accounting year."
msgstr ""
"Veuillez trouver ci-joint quelques données qui vous seront utiles pour "
"établir le Bilan social de l'exercice comptable indiqué ci-dessous. Le Bilan"
" social de l'exercice précédent peut vous être utile pour compléter les "
"informations relatives à l'exercice comptable précédent."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"Please note that the declaration by batch is rather complex and requires "
"some technical knowledge (using some concepts like ssh keys, SFTP servers, "
"and electronical signatures). You may want to take a look at the"
msgstr ""
"Veuillez noter que la déclaration par lot est assez complexe et nécessite "
"quelques connaissances techniques (utilisation de certains concepts comme "
"les clés ssh, les serveurs SFTP et les signatures électroniques). Vous "
"voudrez peut-être jeter un œil au"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"Please provide an employer class for company %s. The employer class is given"
" by the ONSS and should be encoded in the Payroll setting."
msgstr ""
"Veuillez fournir une catégorie d'employeur pour la société %s. La catégorie "
"de l'employeur est fournie par l'ONSS et devrait être encodée dans les "
"configuration du module de paie."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid "Post Change Contract Creation"
msgstr "Création d'un contrat après modification"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Posted Employee"
msgstr "Employé détaché"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_32
msgid "Pp Amount 32"
msgstr "Montant du PP 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_33
msgid "Pp Amount 33"
msgstr "Montant du PP 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_34
msgid "Pp Amount 34"
msgstr "Montant du PP 34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Pr.M versé"
msgstr "Pr.M versé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Premium from the Impulse Fund for general medicine obtained by a general "
"practitioner approved to settle in a \"priority\" area"
msgstr ""
"Prime du Fonds d'Impulsion pour la médecine générale obtenue par un médecin "
"généraliste agréé pour s'installer dans une zone \"prioritaire\""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Previous Occupations"
msgstr "Occupations précédentes"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Primary education"
msgstr "Enseignement primaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Print"
msgstr "Imprimer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Private Car"
msgstr "Voiture privée"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__private_car_reimbursed_amount
msgid "Private Car Reimbursed Amount"
msgstr "Montant remboursé pour voiture privée"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Private Car Reimbursement"
msgstr "Remboursement voiture privée"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_private_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_private_car
msgid "Private car"
msgstr "Voiture privée"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_professional_tax_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_professional_tax_termination
msgid "Professional Tax"
msgstr "Précompte professionnel"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_bank_holiday
msgid "Public Holiday"
msgstr "Jour férié"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_phc
msgid "Public Holiday Compensation"
msgstr "Récupération de jour férié"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_reimbursed_amount
msgid "Public Transport Reimbursed amount"
msgstr "Montant remboursé pour transports en commun"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Public Transportation"
msgstr "Transports en commun"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_public_transport
msgid "Public Transportation (Tram - Bus - Metro)"
msgstr "Transports en commun (Tram - Bus - Métro)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_employee_amount
msgid "Public transport paid by the employee (Monthly)"
msgstr "Transports en commun payé par l'employé (mensuel)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter
msgid "Quarter"
msgstr "Trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 1"
msgstr "1er trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 2"
msgstr "2eme trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 3"
msgstr "3eme trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 4"
msgstr "4eme trimestre"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_end
msgid "Quarter End"
msgstr "Début du trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter End :"
msgstr "Début du trimestre :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_start
msgid "Quarter Start"
msgstr "Fin du trimestre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter Start :"
msgstr "Début du trimestre :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "REVENUS MOBILIERS des DROITS D'AUTEUR et DROITS VOISINS"
msgstr "REVENUS MOBILIERS des DROITS D'AUTEUR et DROITS VOISINS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate PDF report"
msgstr "Regénérer la déclaration en PDF"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate XML report"
msgstr "Regénérer la déclaration en XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__r
msgid "Real File (R)"
msgstr "Fichier réel (R)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_departure_reason__reason_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_reason_code
msgid "Reason Code"
msgstr "Code de la raison du départ"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
msgid "Recovered Simple Holiday Pay (N)"
msgstr "Pécule simple récupéré (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid "Recovered Simple Holiday Pay (N-1)"
msgstr "Pécule simple récupéré (N-1)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery_additional
msgid "Recovery Additional Time"
msgstr "Temps de récupération supplémentaire"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_recovery
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery
msgid "Recovery Bank Holiday"
msgstr "Récupération de jour férié"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__reference
msgid "Reference"
msgstr "Référence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reference Mean Working Hours"
msgstr "Heures de travail en moyenne de référence"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__reference_month
msgid "Reference Month"
msgstr "Mois de référence"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Reference Period"
msgstr "Période de référence"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
msgid "Reference Working Time"
msgstr "Horaire de référence"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_export_sdworx_leaves_wizard__reference_year
msgid "Reference Year"
msgstr "Année de référence"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_expense_refund
msgid "Refund Expenses"
msgstr "Rembourser les notes de frais"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses"
msgstr "Notes de frais remboursées"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses (Code 330)"
msgstr "Notes de frais remboursées (Code 330)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Reimbursed Expenses (Representation Fees)"
msgstr "Notes de frais remboursées (Frais de représentation)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_reimbursement
msgid "Reimbursement"
msgstr "Remboursement"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__remuneration_n1
msgid "Remuneration N-1"
msgstr "Rémunération N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n
msgid "Remuneration fictitious current year"
msgstr "Rémunération fictive de l'année courante"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n1
msgid "Remuneration fictitious previous year"
msgstr "Rémunération fictive de l'année précédente"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"Remuneration of statutory holidays occurring within 30 days of the end date "
"of the contract"
msgstr ""
"Rémunération des jours fériés survenant dans les 30 jours suivant la date de"
" fin du contrat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reorganization Measures :"
msgstr "Mesures de réorganisation :"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_replacement
msgid "Replacement"
msgstr "Remplacement"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Replacement contract"
msgstr "Contrat de remplacement"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_representation_fees
msgid "Representation Fees"
msgstr "Frais de représentation"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees_volatile
msgid "Representation Fees (Without Serious Standards)"
msgstr "Frais de représentation (Sans base sérieuse)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_rep_fees_regul
msgid "Representation Fees Regularization"
msgstr "Régularisation des frais de représentation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__requires_new_contract
msgid "Requires New Contract"
msgstr "Requiert un nouveau contrat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid "Respect of the notice period"
msgstr "Respect de la période de préavis"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_ch_worker
msgid "Retain on Meal Voucher"
msgstr "Retenue sur chèques-repas"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Retenue de versement: précompte professionnel (salaires)"
msgstr "Retenue de versement : précompte professionnel (salaires)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_revenue_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_revenue_code
msgid "Revenue Code"
msgstr "Code de revenu"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Right to time off"
msgstr "Droit aux vacances"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_specific_CP
msgid "Rules specific to Auxiliary Joint Committee"
msgstr "Règle spécifique à la commission paritaire auxiliaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réduction CPDI"
msgstr "Réduction CPDI"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réservé à l'administration"
msgstr "Réservé à l'administration"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "SD Worx"
msgstr "SDWorx"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__sdworx_code
msgid "SDWorx code"
msgstr "Code SDWorx"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "SEP"
msgstr "SEPT"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "SOCIAL BALANCE SHEET -  COMPLETE SCHEME"
msgstr "BILAN SOCIAL - SCHEMA COMPLET"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "SOCIAL SECURITY CERTIFICATE"
msgstr "ATTESTATION DE SECURITE SOCIALE"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "SU"
msgstr "SU"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Salaries paid in relation to previous years"
msgstr "Salaires versés par rapport aux années précédentes"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_advance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Advance"
msgstr "Avance sur salaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Assignment"
msgstr "Cession de salaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Attachments"
msgstr "Saisies sur salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__structure_type_id
msgid "Salary Structure Type"
msgstr "Type de structure de salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_visibility
msgid "Salary as of December 2013"
msgstr "Salaire en décembre 2013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Search Meal Voucher Report"
msgstr "Rapport des chèques-repas"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Secondary education"
msgstr "Enseignement secondaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Secondment allowance"
msgstr "Indemnité de détachement"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__1
msgid "Send grouped corrections"
msgstr "Envoyer des corrections groupées"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_sending
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_sending
msgid "Sending Type"
msgstr "Type d'envoi"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_scale_seniority
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__l10n_be_scale_seniority
msgid "Seniority at Hiring"
msgstr "Ancienneté à l'embauche"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__9
msgid "September"
msgstr "Septembre"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Service Public Fédéral FINANCES"
msgstr "Service Public Fédéral FINANCES"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45_line__sheet_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account_line__sheet_id
msgid "Sheet"
msgstr "Feuille"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10_filename
msgid "Sheet 274 10 Filename"
msgstr "Nom du fichier 274.10"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntary_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntary_rate
msgid "Should be between 0 and 100 %"
msgstr "Doit être compris entre 0 et 100%"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_part_sick
msgid "Sick Time Off (Without Guaranteed Salary)"
msgstr "Congé maladie (Sans salaire garanti)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry.py:0
#, python-format
msgid "Sick time off to report to DRS for %s."
msgstr "Congé maladie à reporter à la DRS pour %s."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature
msgid "Signature file"
msgstr "Fichier de signatures"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_simple_december
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_simple_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_december_pay
msgid "Simple December Pay"
msgstr "Simple pécule de décembre"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_simple_pay_december
msgid "Simple Holiday Pay (Lost due to working time reduction)"
msgstr ""
"Simple pécule de vacances (Perdu à cause d'une réduction du temps de "
"travail)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_simple_holiday_pay_variable_salary
msgid "Simple Holiday Pay - Variable Salary"
msgstr "Simple pécule de vacances - Salaire variable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n
msgid "Simple Holiday Pay N"
msgstr "Simple pécule de vacances N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n1
msgid "Simple Holiday Pay N-1"
msgstr "Simple pécule de vacances N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
msgid "Simple Holiday Pay to Recover (N)"
msgstr "Simple pécule à récupérer (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Simple Holiday Pay to Recover (N-1)"
msgstr "Simplé pécule à récupérer (N-1)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__slip_ids
msgid "Slip"
msgstr "Fiche de paie"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_small_unemployment
msgid "Small Unemployment"
msgstr "Petit chômage"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_small_unemployment
msgid "Small Unemployment (Brief Holiday)"
msgstr "Petit chômage"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename
msgid "Social Balance Filename"
msgstr "Nom du fichier bilan social"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_balance_sheet_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_balance
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_balance_sheet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Social Balance Sheet"
msgstr "Bilan social"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_security_certificate_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_security_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Social Security Certificate"
msgstr "Attestation de sécurité sociale"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename
msgid "Social Security Filename"
msgstr "Nom du fichier sécurité sociale"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_onss_rule
msgid "Social contribution"
msgstr "Cotisations sociales"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_solicitation_time_off
msgid "Solicitation Time Off"
msgstr "Congé de sollicitation"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_solidarity_cotisation
msgid "Solidarity Cotisation - Student Job"
msgstr "Cotisation de solidarité - Job étudiant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Solidarity Cotisation: Company Cars"
msgstr "Cotisation de solidarité : Voitures de société"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "Some employee has no contract."
msgstr "Certains employés n'ont pas de contrat."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Special Social Cotisation"
msgstr "Cotisation spéciale pour la sécurité sociale"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_m_onss_total
msgid "Special Social Cotisation (Total)"
msgstr "Cotisation spéciale pour la sécurité sociale (Total)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_spec_soc_contribution
msgid "Special social contribution"
msgstr "Cotisation spéciale pour la sécurité sociale"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_mis_ex_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_mis_ex_onss
msgid "Special social cotisation"
msgstr "Cotisation spéciale pour la sécurité sociale"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status_explanation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status_explanation
msgid "Spouse Fiscal Status Explanation"
msgstr "Explication du statut fiscal du conjoint"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Staff movements during the exercise"
msgstr "Mouvement du personnel au cours de l'exercice"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid "Standard 38 hours/week"
msgstr "38 Heures/semaine standard"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start Date"
msgstr "Date de début"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_start
msgid "Start Period"
msgstr "Début de la période"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Start date must be earlier than end date."
msgstr "La date de début doit être antérieure à la date de fin."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid "Start date must be later than the current contract's start date."
msgstr ""
"La date de début doit être postérieure à la date de début du contrat actuel."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start date of the new contract."
msgstr "Date de début du nouveau contrat."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__start_notice_period
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid "Start notice period"
msgstr "Début de la période de préavis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state
msgid "State"
msgstr "Statut"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "State the Dimona at %s to declare the arrival of %s."
msgstr "Déclarer la Dimona à %s pour déclarer l'arrivée de %s."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Status of employed persons"
msgstr "Statut des personnes employées"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_stock_option
msgid "Stock Option"
msgstr "Options sur actions"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_strike
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_strike
msgid "Strike"
msgstr "Grève"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Structural reductions"
msgstr "Réductions structurelles"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__struct_id
msgid "Structure"
msgstr "Structure"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__structure_type_id
msgid "Structure Type"
msgstr "Type de structure"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_student_regular_pay
msgid "Student: Regular Pay"
msgstr "Étudiants : Fiche de paie classique"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Students"
msgstr "Étudiants"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Sub-total Gross"
msgstr "Sous-total brut"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "System 5 :"
msgstr "Système 5 :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "TOTAL"
msgstr "SOMME"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Taking into account for remuneration:"
msgstr "Pris en compte pour la rémunération :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Taux du Pr.M"
msgstr "Taux du Pr.M"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status
msgid "Tax status for spouse"
msgstr "Statut fiscal du conjoint"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Adaptation"
msgstr "Adaptation imposable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__taxable_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Taxable Amount"
msgstr "Montant imposable"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_32
msgid "Taxable Amount 32"
msgstr "Montant imposable 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_33
msgid "Taxable Amount 33"
msgstr "Montant imposable 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_34
msgid "Taxable Amount 34"
msgstr "Montant imposable 34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Amounts (325)"
msgstr "Montant imposable (325)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_taxable_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_taxable_termination
msgid "Taxable Termination Amount"
msgstr "Montant imposable Indemnité de rupture"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays
msgid "Terminaison Holidays"
msgstr "Pécule de sortie"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_additional_leave
msgid "Terminaison Holidays Additional Leave"
msgstr "Pécule de sortie Congés additionnels"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double
msgid "Terminaison Holidays Double Pay"
msgstr "Double pécule de sortie"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double_basic
msgid "Terminaison Holidays Double Pay Basic"
msgstr "Double pécule de sortie de base"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_simple
msgid "Terminaison Holidays Simple Pay"
msgstr "Simple pécule de sortie"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_fees
#, python-format
msgid "Termination"
msgstr "Départ"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_termination_fees
msgid "Termination Fees"
msgstr "Indemnités de rupture"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "Termination Holidays Current Year"
msgstr "Attestation de vacances, année courante"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "Termination Holidays Previous Year"
msgstr "Attestation de vacances, année précédente"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Termination fees"
msgstr "Indemnités de rupture"

#. module: l10n_be_hr_payroll
#: model_terms:res.company,sign_terms:l10n_be_hr_payroll.res_company_be
#: model_terms:res.company,sign_terms_html:l10n_be_hr_payroll.res_company_be
msgid "Terms &amp; Conditions"
msgstr "Conditions générales"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "The DMFA Code will identify the work entry in DMFA report."
msgstr "Le code DMFA identifiera la prestation dans la déclaration DMFA."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_fiscal_voluntary_rate
msgid "The Fiscal Voluntary rate on wage should be between 0 and 100."
msgstr "Le taux de volontariat fiscal doit être compris entre 0 et 100."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_ip_rate
msgid "The IP rate on wage should be between 0 and 100."
msgstr "Le taux d'IP doit être compris entre 0 et 100."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
#, python-format
msgid "The SDWorx code should have 7 characters or should be left empty!"
msgstr "Le code SDWorx doit avoir 7 caractères ou doit être laissé vide !"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The VAT or the ZIP number is not specified on your company"
msgstr ""
"Le numéro de TVA ou le code postal n'est pas précisé pour votre société"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The belgian postcode length shouldn't exceed 4 characters and should contain"
" only numbers for employee %s"
msgstr ""
"La longueur du code postal belge ne doit pas dépasser 4 caractères et ne "
"doit contenir que des chiffres pour l'employé %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry_type.py:0
#, python-format
msgid "The code should have 4 characters!"
msgstr "Le code doit comporter 4 caractères !"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid "The code should have 7 characters!"
msgstr "Le code doit comporter 7 caractères !"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The company is not correctly configured on your employees. Please be sure "
"that the following pieces of information are set: street, zip, city, phone "
"and vat"
msgstr ""
"La société n'est pas correctement configurée sur vos employés. Assurez-vous "
"que les informations suivantes sont configurées : rue, code postal, numéro "
"de téléphone et TVA"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
#, python-format
msgid ""
"The company number should contain digits only, starts with a '0' or a '1' "
"and be 10 characters long."
msgstr ""
"Le numéro de société ne peut contenir que des chiffres, doit commencer par "
"un '0' ou un '1' et doit faire 10 caractères de long."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The company phone number shouldn't exceed 12 characters"
msgstr ""
"Le numéro de téléphone de la société ne peut pas dépasser 12 caractères"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
msgid ""
"The computed amount is the sum of the new right to time off and the number "
"of time off already taken by the employee. Example: Moving from a full time "
"to a 4/5 part time with 6 days already taken will result into an amount of "
"80%% of 14 days + 6 days (rounded down) = 17 days."
msgstr ""
"Le montant calculé est la somme du nouveau droit au congés et du nombre de "
"congés déjà pris par le salarié. Exemple : Passer d'un temps plein à un "
"temps partiel 4/5 avec 6 jours déjà pris se traduira par un montant de 80%% "
"de 14 jours + 6 jours (arrondi à l'inférieur) = 17 jours."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"The contract %s for %s is not of one the following types: CDI, CDD. "
"Replacement, For a clearly defined work"
msgstr ""
"Le contrat %s pour %s n'est pas d'un des types suivants : CDI, CDD. "
"remplacement, pour une tâche nettement définie"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid ""
"The contract %s for %s is not of one the following types: CP200 Employees or"
" Student"
msgstr ""
"Le contrat %s pour %s n'est pas l'un des types suivants : Employé CP200 ou "
"Etudiant"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#, python-format
msgid "The employee %s doesn't have a specified certificate"
msgstr "L'employé %s n'a pas de certificat spécifié"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The employee first name shouldn't exceed 30 characters for employee %s"
msgstr ""
"Le prénom de l'employé ne doit pas dépasser 30 caractères pour l'employé %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid "The employee is occupied from the %s to the %s."
msgstr "L'employé est occupé du %s au %s."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid ""
"The employee is occupied from the %s to the %s. There is nothing to recover "
"as the employee is there for more than 12 months"
msgstr ""
"L'employé est occupé du %s au %s. Il n'y a rien à récupérer puisque "
"l'employé est ici depuis plus de 12 mois"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__internet
msgid "The employee's internet subcription will be paid up to this amount."
msgstr "L'abonnement internet de l'employé sera payé à hauteur de ce montant."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "The employee's mobile subscription will be paid up to this amount."
msgstr ""
"L'abonnement téléphone de l'employé sera payé à hauteur de ce montant."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "The file has been downloaded."
msgstr "Le fichier a été téléchargé."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"The files won't be posted in the employee portal if you don't have the "
"Documents app."
msgstr ""
"Les fichiers ne seront pas publiés sur le portail des employés si vous "
"n'avez pas l'application Documents."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid ""
"The following employees don't have a valid private address (with a street, a zip, a city and a country):\n"
"%s"
msgstr ""
"Les employés suivants n'ont pas d'adresse privée valide (avec une rue, un code postal, une ville et un pays) :\n"
"%s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid language for the selected salary structure.\n"
"                        <br/>\n"
"                        Please assign them a language below before continuing."
msgstr ""
"Les employés suivants ont une langue invalide pour la structure salariale sélectionnée.\n"
"                        <br/>\n"
"                        Veuillez leur assigner une langue ci-dessous avant de continuer."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"The following work addesses do not have any ONSS identification code:\n"
" %s"
msgstr ""
"Les adresses professionnelles suivantes n'ont pas de code d'identification ONSS :\n"
" %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid ""
"The following work entry types do not have any DMFA code set:\n"
" %s"
msgstr ""
"Les types de prestations suivants n'ont pas de code DMFA :\n"
" %s"

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_group_insurance_rate
msgid ""
"The group insurance salary sacrifice rate on wage should be between 0 and "
"100."
msgstr ""
"Le taux de sacrifice de l'assurance groupe doit être compris entre 0 et 100."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid "The payslips should be from the same company."
msgstr "Les fiches de paie doivent provenir de la même société."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid ""
"The payslips should cover the same period:\n"
"%s"
msgstr ""
"Les fiches de paie doivent couvrir la même période : \n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "The phone number is not specified on your company"
msgstr "Le numéro de téléphone n'est pas précisé pour votre entreprise"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"The printed pdf contains all the data to encode into the 'DmfA declaration "
"web interface' that you can find"
msgstr ""
"Le pdf imprimé contient toutes les données à encoder dans 'l'interface web "
"de déclaration DmfA' que vous pouvez trouver"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#, python-format
msgid "The reference period is from the 1st of June %s to the 31th of May %s"
msgstr ""
"La période de référence est à partir du 1er juin %s jusqu'au 31 mai %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The seized amount (%s€) is above the belgian ceilings. Given a global net "
"salary of %s€ for the pay period and %s dependent children, the maximum "
"seizable amount is equal to %s€"
msgstr ""
"Le montant saisi (%s€) est supérieur aux barèmes belges. Étant donné un "
"salaire net global de %s€ pour la période de paie et %s enfants à charge, le"
" montant maximal saisissable est égal à %s€"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid "The time Percentage in R&D should be between 0-100"
msgstr "Le pourcentage d'occupation en R&D doit être compris entre 0-100"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#, python-format
msgid ""
"The wage is under the minimum scale of %s€ for a seniority of %s years."
msgstr ""
"Le salaire est inférieur au barème minimum de %s€ pour une ancienneté de %s "
"ans."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"Le type d'entrée de travail utilisé lors de la génération d'entrées de "
"travail pour s'adapter à l'horaire de travail à temps plein."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid "There is no SDWorx code defined for the following employees:\n"
msgstr "Aucun code SDWorx n'est défini pour les employés suivants :\n"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid "There is no SDWorx code defined for the following work entry types:\n"
msgstr ""
"Aucun code SDWorx n'est défini pour les type de prestations suivants :\n"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#, python-format
msgid ""
"There is no SDWorx code defined on the company. Please configure it on the "
"Payroll Settings"
msgstr ""
"Aucun code SDWorx n'est défini pour votre société. Veuillez le configurer "
"dans les paramètres de l'application Paie"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#, python-format
msgid "There is no defined expeditor number for the company."
msgstr "Il n'y a pas de numéro d'expéditeur défini pour l'entreprise."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "There is no payslip to generate for those employees"
msgstr "Il n'y a aucune fiche de paie à générer pour ces employés"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#, python-format
msgid "There is no valid payslip to declare."
msgstr "Il n'y a aucune fiche de paie valide à déclarer."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Thirteen Month"
msgstr "Treizième mois"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This document is a translation. This is not a legal document."
msgstr "Ce document est une traduction. Ce n'est pas un document légal."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#, python-format
msgid "This employee doesn't have a first contract date"
msgstr "L'employé n'a pas de date de premier contrat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr "C'est une prestation crédit-temps."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid ""
"This will add the eco-vouchers amount on the payslips in waiting state, and "
"create a new payslip for the employees without such a payslip. Are you sure "
"you want to proceed ?"
msgstr ""
"Cela ajoutera le montant des éco-chèques sur les fiches de paie en attente, "
"et créera une nouvelle fiche de paie pour les employés qui n'en ont pas. "
"Êtes-vous sur de vouloir continuer ?"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__threshold
msgid "Threshold"
msgstr "Seuil"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Time Off"
msgstr "Congés"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave_allocation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Time Off Allocation"
msgstr "Allocation de congé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_n_ids
msgid "Time Off N"
msgstr "Congés N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__holiday_status_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_type_id
msgid "Time Off Type"
msgstr "Type de congé"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__rd_percentage
msgid "Time Percentage in R&D"
msgstr "Pourcentage d'occupation en R&D"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocated
msgid "Time off allocated during current year"
msgstr "Congés alloués durant l'année courante"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Time off already taken"
msgstr "Congés déjà pris"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_taken
msgid "Time off taken during current year"
msgstr "Congés pris durant l'année courante"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "To"
msgstr "Vers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "To Allocate"
msgstr "À allouer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid ""
"To benefit from this increase in the elusive or non-transferable quotas, the worker whose remuneration is subject to seizure or transfer, must declare it using a form, the model of which has been published in the Belgian Official Gazette. of 30 November 2006.\n"
"\n"
"He must attach to this form the documents establishing the reality of the charge invoked.\n"
"\n"
"Source: Opinion on the indexation of the amounts set in Article 1, paragraph 4, of the Royal Decree of 27 December 2004 implementing Articles 1409, § 1, paragraph 4, and 1409, § 1 bis, paragraph 4 , of the Judicial Code relating to the limitation of seizure when there are dependent children, MB, December 13, 2019."
msgstr ""
"Pour bénéficier de cette augmentation des quotités insaisissables ou incessibles, le travailleur dont la rémunération fait l'objet d'une saisie ou d'une cession, doit la déclarer au moyen d'un formulaire dont le modèle a été publié au Moniteur belge du 30 novembre 2006.\n"
"\n"
"Il doit joindre à ce formulaire les pièces établissant la réalité de la charge invoquée.\n"
"\n"
"Source : Avis sur l'indexation des montants fixés à l'article 1er, alinéa 4, de l'arrêté royal du 27 décembre 2004 portant exécution des articles 1409, § 1er, alinéa 4, et 1409, § 1er bis, alinéa 4, du Code judiciaire relatifs à la limitation de la saisie lorsqu'il y a des enfants à charge, MB, 13 décembre 2019."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the next employer"
msgstr "A l'attention du prochain employeur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the worker"
msgstr "A l'attention de l'employé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "Toggle Explanation"
msgstr "Activer/Désactiver l'explication"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_total_n
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_total_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total"
msgstr "Total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total (FTE)"
msgstr "Total (ETP)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Basic before ONSS"
msgstr "Total de base avant ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Employer Cost"
msgstr "Coût employeur total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time (Code 1021)"
msgstr "Total Temps Plein (Code 1021)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time + Part-Time (Code 1023)"
msgstr "Total Temps Plein + Temps Partiel (Code 1023)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_with_ip
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_gross_with_ip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Gross"
msgstr "Brut total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Net (Advance included)"
msgstr "Net total (Avances incluses)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Part-Time (Code 1022)"
msgstr "Total Temps Partiel (Code 1022)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Total Year"
msgstr "Total de l'année"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total actual number of hours worked or FTE"
msgstr "Nombre total effectif d'heures prestées ou ETP"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total allocated days"
msgstr "Nombre total de jours alloués"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Total amount paid or awarded (the taxable part must be added to Total "
"remuneration 2.060)"
msgstr ""
"Montant total payé ou attribué (la partie imposable doit être ajoutée à la "
"rémunération totale 2.060)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total basic wage"
msgstr "Salaire de base total"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total_cost
msgid "Total cost employer"
msgstr "Coût employeur total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total net wage"
msgstr "Salaire net total"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Total of all Contributions:"
msgstr "Total des cotisations :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total time off days"
msgstr "Nombre total de jours de congé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_double_holiday_13th_month
msgid "Total to pay on"
msgstr "Total à verser sur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total: (14a + 14b + 14c)"
msgstr "Total : (14a + 14b + 14c)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Totaux :"
msgstr "Totaux :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_reimbursed_amount
msgid "Train Transport Reimbursed amount"
msgstr "Montant remboursé pour transport en train"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_employee_amount
msgid "Train transport paid by the employee (Monthly)"
msgstr "Transport en train payé par l'employé (mensuel)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training
msgid "Training"
msgstr "Formation"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_training
msgid "Training Time Off"
msgstr "Congé éducation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Transportation"
msgstr "Transport"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_treatment
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_treatment
msgid "Treatment Type"
msgstr "Type de traitement"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Unemployment with company supplement"
msgstr "Chômage avec complément d'entreprise"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "University education"
msgstr "Enseignement universitaire"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unjustified_reason
msgid "Unjustified Reason"
msgstr "Raison non justifiée"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Unpaid Time Off"
msgstr "Congés non payés"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_unpredictable
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unpredictable
msgid "Unpredictable Reason"
msgstr "Raison impérieuse"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_unreasonable_dismissal
msgid "Unreasonable dismissal"
msgstr "Licenciement abusif"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#, python-format
msgid "Unsupported country code %s. Please contact an administrator."
msgstr ""
"Code pays %s non pris en charge. Veuillez contacter un administrateur."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_schedule_change_allocation
msgid "Update allocation on schedule change"
msgstr "Mettre à jour l'allocation en cas de changement d'horaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__has_bicycle
msgid "Use a bicycle as a transport mode to go to work"
msgstr "Utiliser le vélo comme moyen de transport pour aller au travail"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
msgid "Use this to limit the employees to compute"
msgstr "Utilisez cette option pour limiter le nombre d'employés à calculer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_users
msgid "User"
msgstr "Utilisateur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_public
msgid "Uses another public transportation"
msgstr "Utilise un autre moyen de transport en public"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_car
msgid "Uses company car"
msgstr "Utilise une voiture de société"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_private_car
msgid "Uses private car"
msgstr "Utilise une voiture privée"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_train
msgid "Uses train transportation"
msgstr "Utilise les transports en commun"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "VAT Number :"
msgstr "Numéro de TVA :"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__done
msgid "Valid"
msgstr "Disponible"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_work_entry_type_view_form
msgid "Valid For Advantages"
msgstr "Valide pour les avantages"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate"
msgstr "Valider"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Validate & Compute holiday attests"
msgstr "Valider & calculer les attestations de vacances"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate & Compute termination fees"
msgstr "Valider & Calculer les indemnités de rupture"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__validation_state
msgid "Validation State"
msgstr "Statut de validation"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Various bonuses"
msgstr "Bonus divers"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__batch
msgid "Via Batch"
msgstr "Via Lot"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__web
msgid "Via Web"
msgstr "Via Web"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fiscal_voluntarism
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__fiscal_voluntarism
msgid "Voluntarily increase withholding tax rate."
msgstr "Augmenter volontairement le taux de précompte professionnel."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_wage
msgid "Wage"
msgstr "Salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__wage_type
msgid "Wage Type"
msgstr "Type de salaire"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid "Wage With Sacrifices"
msgstr "Salaire avec sacrifices"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__waiting
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__waiting
msgid "Waiting"
msgstr "En attente"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrants_cost
msgid "Warrant monthly cost for the employer"
msgstr "Coût mensuel des warrants pour l'employeur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrant_value_employee
msgid "Warrant monthly value for the employee"
msgstr "Valeur mensuelle des warrants pour l'employé"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_structure_warrant
msgid "Warrants"
msgstr "Warrants"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"We draw your attention to the fact that this information is based on the data in Odoo and / or that you\n"
"                            have introduced in Odoo and that it is important that they be accompanied by a verification on your part\n"
"                            according to the particularities related to contract of the worker or your company which Odoo would not\n"
"                            know."
msgstr ""
"Attention : ces informations sont basées sur les données dans Odoo et/ou sur ce que vous\n"
"                            avez introduit dans Odoo. Il est essentiel de les vérifier\n"
"                            selon les particularités liées au contrat du travailleur ou de votre entreprise qu'Odoo ne peut pas\n"
"                            connaître."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"When you're done with the commission edition, click on the 'Generate "
"Payslip' button to generate a batch of payslips using the commissions you've"
" provided."
msgstr ""
"Lorsque vous avez terminé avec l'édition des commissions, cliquez sur le "
"bouton 'Générer la fiche de paie' pour générer un lot de fiches de paie en "
"utilisant les commissions que vous avez fournies."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid ""
"Whether or not the employee has a contract candidate for a working schedule "
"change"
msgstr ""
"Si l'employé a ou non un contrat candidat pour un changement d'horaire de "
"travail"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__company
msgid "Whole Company"
msgstr "La société entière"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_pension
msgid "With High Pensions"
msgstr "Avec pensions élevées"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_income
msgid "With High income"
msgstr "Avec revenus élevés"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_income
msgid "With Low Income"
msgstr "Avec revenus faibles"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_pension
msgid "With Low Pensions"
msgstr "Avec pensions faibles"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Withdrawal not retained"
msgstr "Précompte non retenu"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_warrant_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_salary_withholding_taxes
msgid "Withholding Tax"
msgstr "Précompte professionnel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__payment_reference
msgid "Withholding Tax Payment Reference"
msgstr "Communication de paiement du précompte professionnel"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_pp
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes"
msgstr "Précompte professionnel"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_pp_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_pp_total
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_withholding_taxes_total
msgid "Withholding Taxes (Total)"
msgstr "Précompte professionnel (Total)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Capping (Bachelors)"
msgstr "Écrêtement du précompte professionnel (Bacheliers)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_withholding_taxes_exemption
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Withholding Taxes Exemption"
msgstr "Exonération du précompte professionnel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Bachelors"
msgstr ""
"Exonération du précompte professionnel (recherche scientifique) - Bacheliers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid ""
"Withholding Taxes Exemption (Scientific Research) - Doctors / Civil "
"Engineers"
msgstr ""
"Exonération du précompte professionnel (recherche scientifique) - Docteurs /"
" Ingénieurs civils"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Masters"
msgstr ""
"Exonération du précompte professionnel (recherche scientifique) - Master"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_reduction
msgid "Withholding reduction"
msgstr "Réduction du précompte professionnel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__without_income
msgid "Without Income"
msgstr "Sans revenus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__wizard_id
msgid "Wizard"
msgstr "Assistant"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_work_accident
msgid "Work Accident"
msgstr "Accident de travail"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_work_accident
msgid "Work Accident Time Off"
msgstr "Congé accident de travail"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Address :"
msgstr "Adresse professionnelle :"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_work_entry_daily_benefit_report
msgid "Work Entry Related Benefit Report"
msgstr "Rapport des avantages de toute nature"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Place :"
msgstr "Lieu de travail :"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa_location_unit
msgid "Work Place defined by ONSS"
msgstr "Lieu de travail défini par l'ONSS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid "Work Time Rate"
msgstr "Taux de temps de travail"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_action_work_address_codes
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_location_unit_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_location_unit_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Work address DMFA codes"
msgstr "Code DMFA des adresses professionnelles"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Work addresses without ONSS identification code"
msgstr "Adresses professionnelles sans code d'identification ONSS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Work entries counts for meal vouchers"
msgstr "Compte des prestations pour les chèques-repas"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Work entries counts for private car reimbursement"
msgstr "Compte des prestations pour les remboursement voiture privée"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
msgid "Work entries counts for representation fees"
msgstr "Compte des prestations pour les frais de représentation"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Work entries counts for time off right for next year."
msgstr ""
"Compte des prestations pour le doit aux vacances de l'année prochaine."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Type de prestation pour les présences régulières."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Le ratio du taux de temps de travail par rapport à l'horaire complet à temps"
" plein devrait se situer entre 0 et 100 %."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Worker"
msgstr "Travailleur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Worker Code :"
msgstr "Code travailleur :"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_onss
msgid "Worker Social Contribution"
msgstr "Cotisation sociale des travailleurs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Workers"
msgstr "Travailleurs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Workers for whom the company has submitted a DIMONA declaration or who are "
"registered in the general staff register"
msgstr ""
"Travailleurs pour lesquels l'entreprise a déposé une déclaration DIMONA ou "
"qui sont inscrits au registre général du personnel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__partner_id
msgid "Working Address"
msgstr "Adresse professionnelle"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Working Schedule"
msgstr "Heures de travail"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.schedule_change_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Working Schedule Change"
msgstr "Changement d'horaire"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
#, python-format
msgid ""
"Working schedule would stay unchanged by this action. Please select another "
"working schedule."
msgstr ""
"L'horaire de travail reste inchangé par cette action. Veuillez sélectionner "
"un autre horaire de travail."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_file
msgid "XLS file"
msgstr "Fichier XLS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_file
msgid "XML File"
msgstr "Fichier XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_filename
msgid "XML Filename"
msgstr "Nom du fichier XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml
msgid "XML file"
msgstr "Fichier XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_filename
msgid "Xls Filename"
msgstr "Nom du fichier XLS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_filename
msgid "Xml Filename"
msgstr "Nom du fichier XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_validation_state
msgid "Xml Validation State"
msgstr "Statut de validation du XML"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__year
msgid "Year"
msgstr "Année"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
msgid "Year of the period to consider"
msgstr "Année de la période à considérer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_year_end_bonus
msgid "Year-end bonus"
msgstr "Bonus de fin d'année"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Yearly Advantages"
msgstr "Avantages annuels"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission
msgid "Yearly Commission"
msgstr "Commissions annuelles"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission_cost
msgid "Yearly Commission Cost"
msgstr "Coût des commissions annuelles"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__eco_checks
msgid "Yearly amount the employee receives in the form of eco vouchers."
msgstr "Montant annuel que le salarié reçoit sous forme d'éco-chèques."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#, python-format
msgid ""
"You don't have the right to do this. Please contact your administrator!"
msgstr ""
"Vous n'avez pas le droit d'effectuer cette action. Veuillez contacter un "
"administrateur !"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_december_slip_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_group_insurance_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#, python-format
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Vous devez être connecté à une entreprise belge pour utiliser cette "
"fonctionnalité"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
#, python-format
msgid "You should upload a file to import first."
msgstr "Vous devez d'abord charger un fichier à importer."

#. module: l10n_be_hr_payroll
#: model_terms:res.company,sign_terms:l10n_be_hr_payroll.res_company_be
#: model_terms:res.company,sign_terms_html:l10n_be_hr_payroll.res_company_be
msgid "Your conditions..."
msgstr "Vos conditions..."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Youth Hiring Plan"
msgstr "Plan d'embauche des jeunes"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_youth_time_off
msgid "Youth Time Off"
msgstr "Vacances jeunes"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Advantages"
msgstr "a) Avantages"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Code 250"
msgstr "a) Code 250"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Early vacation pay (other than referred to under 11b and 12b):"
msgstr "a) Pécule de vacances anticipé (autre que visé sous 11b et 12b) :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Forfaitaires"
msgstr "a) Forfaitaires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Movements by cycle or by speed-pedelec"
msgstr "a) Déplacements en cycle ou en speed pedelec"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Nom et prénom, ou dénomination"
msgstr "a) Nom et prénom, ou dénomination"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Normal contributions and premiums"
msgstr "a) Cotisations et primes normales"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"a) Provided in 2021 as part of the fight against COVID-19 and / or as part "
"of the recovery plan"
msgstr ""
"a) prestées en 2021 dans le cadre de la lutte contre la COVID-19 et/ou dans "
"le cadre de la relance"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Public transport"
msgstr "a) Transport en commun :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration"
msgstr "a) Rémunérations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration:"
msgstr "a) Rémunérations : "

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Total number of overtime hours actually worked"
msgstr "a) Nombre total d'heures supplémentaires effectivement prestées"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) based on income received from employer"
msgstr "a) Calculé sur les revenus reçus de l'employeur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) from employers who do not use the cash register system"
msgstr ""
"a) Auprès d'employeurs qui n'utilisent pas le système de caisse "
"enregistreuse"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) in the construction sector with registration system"
msgstr "a) dans le secteur de la construction avec système d'enregistrement"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "and at the"
msgstr "et à "

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears"
msgstr "b) Arriérés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears (other than referred to in 9b, 11c and 12c):"
msgstr "b) Arriérés (autres visés sous 9b, 11c et 12c) :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) Basis for the calculation of the additional salary relating to overtime "
"giving right to a reduction of :"
msgstr ""
"b) Base de calcul du sursalaire relatif aux heures supplémentaires donnant "
"droit à une réduction de :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Benefits in kind :"
msgstr "b) Avantages de toute nature :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Contributions and bonuses for individual continuation"
msgstr "b) Cotisations et primes pour la continuation individuelle"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Early vacation pay"
msgstr "b) Pécule de vacances anticipé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Employer-specific expenses"
msgstr "b) Dépenses propres à l'employeur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Organized public transport"
msgstr "b) Transport collectif organisé"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other codes"
msgstr "b) Autres codes"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Rue et numéro/boîte"
msgstr "b) Rue et numéro/boîte"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Réels"
msgstr "b) Réels"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) based on income received from a related foreign company"
msgstr "b) Calculé sur les revenus reçus d'une société étrangère liée"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) from employers who use the cash register system"
msgstr ""
"b) Auprès d'employeurs qui utilisent le système de caisse enregistreuse"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) in other sectors"
msgstr "b) dans d'autres secteurs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) provided in 2020 as part of the fight against COVID-19 to employers "
"belonging to critical sectors and / or to employers belonging to critical "
"sectors"
msgstr ""
"b) prestées en 2020 dans le cadre de la lutte contre la COVID-19 auprès "
"d'employeurs appartenant aux secteurs critiques et/ou auprès d'employeurs "
"appartenant aux secteurs cruciaux"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Arrears"
msgstr "c) Arriérés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Cancellation indemnities (other than referred to under 11d and 12d) and "
"redeployment indemnities:"
msgstr ""
"c) Indemnités de dédit (autres que visées sous 11d et 12d) et indemnités de "
"reclassement:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "c) Code pays, code postal et commun"
msgstr "c) Code pays, code postal et commun"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Contributions and premiums for free supplementary pensions for salaried "
"workers"
msgstr ""
"c) Cotisations et primes de pension libre complémentaire pour les "
"travailleurs salariés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Loyalty stamps"
msgstr "c) Timbres fidélité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Other means of transport"
msgstr "c) Autres moyens de transport"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Tips"
msgstr "c) Pourboires"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Cancellation indemnities"
msgstr "d) Indemnités de dédit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Frontier workers"
msgstr "d) Travailleurs frontaliers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "d) Numéro d'identification fiscale (facultatif)"
msgstr "d) Numéro d'identification fiscale (facultatif)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Remuneration for the month of December (Public authority) (2):"
msgstr "d) Rémunérations du mois de décembre (Autorité publique) (2) :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Stock options"
msgstr "d) Options sur actions"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_day
msgid "days"
msgstr "jours"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "e) Exempt income received in fulfillment of a flexi-job contract"
msgstr ""
"e) Revenus exonérés perçus en exécution d'un contrat de travail flexi-job"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "f) Beneficiary premium"
msgstr "f) Prime bénéficiaire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "false"
msgstr "faux"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_clearly_defined_work
msgid "for clearly defined work"
msgstr "pour un travail clairement défini"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "for more informations"
msgstr "pour plus d'informations"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "fr"
msgstr "fr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "g) Mobility budget: total amount"
msgstr "g) Budget mobilité : montant total"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__generate
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__generate
msgid "generate"
msgstr "générer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__get
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__get
msgid "get"
msgstr "obtenir"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "h) First employment agreement: compensatory supplement"
msgstr "h) Convention de premier emploi : supplément compensatoire"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "here"
msgstr "ici"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"i) Volunteer firefighter and volunteer Civil Protection officer paid "
"allowances"
msgstr ""
"i) Indemnités versées aux sapeurs-pompiers volontaires et aux agents de la "
"protection civile volontaires"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "if recipient children is/are declared disabled by law"
msgstr "si les enfants du bénéficiaire sont déclarés handicapés par la loi"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__resident_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__resident_bool
msgid "if recipient lives in a foreign country"
msgstr "si le bénéficiaire vit dans un pays étranger"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "if recipient spouse is declared disabled by law"
msgstr ""
"si le (la) conjoint(e) du bénéficiaire est déclaré(e) handicapé(e) par la "
"loi"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "if spouse has professionnel income or not"
msgstr "si le conjoint a un revenu professionnel ou non"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "j) Student job"
msgstr "j) Job d'étudiant"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "k) Consumer checks"
msgstr "k) Chèques consommation"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_month
msgid "months"
msgstr "mois"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "number of dependent children declared as disabled"
msgstr "nombre d'enfants à charge déclarés handicapés"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "o) Prime Corona"
msgstr "o) Prime Corona"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "process overview"
msgstr "vue d'ensemble du processus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "the official documentation"
msgstr "la documentation officielle"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "to"
msgstr "à"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_week
msgid "weeks"
msgstr "semaines"
