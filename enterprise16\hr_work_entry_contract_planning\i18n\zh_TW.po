# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_planning
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,help:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        定義了工作記錄生成的來源\n"
"\n"
"        工作安排：工作記錄將從以下的工作時間生成。\n"
"        出勤：工作記錄將從員工的出勤生成。(需要出勤應用程序)\n"
"        計劃：工作記錄將從員工的計劃生成。(需要計劃應用程序)\n"
"    "

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_contract
msgid "Employee Contract"
msgstr "員工契約"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_resource_resource__flexible_hours
msgid "Flexible Hours"
msgstr "靈活工時"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR工時紀錄"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields.selection,name:hr_work_entry_contract_planning.selection__hr_contract__work_entry_source__planning
msgid "Planning"
msgstr "規劃"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_planning_slot
msgid "Planning Shift"
msgstr "計劃排班"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_work_entry__planning_slot_id
msgid "Planning Slot"
msgstr "計劃時段"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "重新生成員工工時紀錄"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_resource_resource
msgid "Resources"
msgstr "資源"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "工作記錄來源"
