# -*- coding: utf-8 -*-
from odoo import api, models
from odoo.tools.misc import formatLang
from dateutil.relativedelta import relativedelta
from datetime import datetime


from dateutil import rrule
from odoo.exceptions import ValidationError

class StockReportReportXls(models.AbstractModel):
    _name = 'report.cubes_sales_brand_report.sales_brand_report_xls.xlsx'
    _inherit = 'report.report_xlsx.abstract'

    def generate_xlsx_report(self, workbook, data, wizard):
        categories = data.get('category_group')
        dates = list(rrule.rrule(rrule.MONTHLY, dtstart=wizard.from_date, until=wizard.to_date))
        if wizard.to_date.day < wizard.from_date.day:
            dates.append(datetime.combine(wizard.to_date, datetime.min.time()))
        dates[-1] = dates[-1].replace(day=wizard.to_date.day)

        domain = []
        if wizard.brand_id:
            domain.append(('brand_id', '=', wizard.brand_id.id))
        if wizard.categ_id:
            domain.append(('categ_id', '=', wizard.categ_id.id))
        products = self.env['product.product'].sudo().with_context({'months_array': dates, 'branch_id': wizard.branch_id.id}).search(domain)
        dates = [x.strftime("%b %Y") for x in dates]

        table_head = workbook.add_format({'align': 'left', 'bold': True, 'border': 1})
        table_line = workbook.add_format({'align': 'left', 'border': 1})
        sheet = workbook.add_worksheet('Stock Report')
        row_count = 0
        col_count = 0
        sheet.write(row_count, 0, 'Product', table_head)
        d_count = 0
        for d in dates:
            d_count += 1
            sheet.write(row_count, d_count, 'Qty Delivered %s' % d, table_head)
        sheet.write(row_count, d_count+1, 'Total Qty Delivered', table_head)
        sheet.write(row_count, d_count+2, 'On Hand', table_head)
        index = 0
        for product in products:
            index += 1
            row_count += 1
            sheet.write(row_count, 0, product.name, table_line)
            d_count = 0
            total_delivered = 0
            for m in product.months_qty_delivered.split('-')[0].split(','):
                d_count += 1
                sheet.write(row_count, d_count, m, table_line)
                total_delivered += float(m)
            sheet.write(row_count, d_count+1, total_delivered, table_line)
            sheet.write(row_count, d_count+2, product.qty_available, table_line)

