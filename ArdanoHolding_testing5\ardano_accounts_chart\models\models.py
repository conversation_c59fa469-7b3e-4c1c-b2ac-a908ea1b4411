# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProjectTask(models.Model):
    _inherit = 'account.account'

    type_of_account = fields.Selection(string='نوع الحساب', selection=[
        ('main_account', 'Main Account'),
        ('subaccount', 'Subaccount'),
        ('detailed_account', 'Detailed Account'),
        ('analytic_account', 'Analytic Account'),
    ])

    main_account = fields.Many2one(comodel_name='account.account', domain=[('type_of_account', '=', 'main_account')],
                                   string='الحساب الرئيسيي')
    sub_account = fields.Many2one(comodel_name='account.account', domain=[('type_of_account', '=', 'subaccount')],
                                  string='الحساب الفرعي')
    detailed_account = fields.Many2one(comodel_name='account.account',
                                       domain=[('type_of_account', '=', 'detailed_account')],
                                       string='الحساب التفصيلي')

    @api.onchange('sub_account')
    def _onchange_sub_account(self):
        for rec in self:
            if rec.sub_account:
                rec.main_account = rec.sub_account.main_account.id

    @api.onchange('detailed_account')
    def _onchange_detailed_account(self):
        for rec in self:
            if rec.detailed_account:
                rec.sub_account = rec.detailed_account.sub_account.id
                rec.main_account = rec.sub_account.main_account.id

