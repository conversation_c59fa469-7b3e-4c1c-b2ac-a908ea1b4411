<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="planning_project_stage_0" model="project.task.type">
            <field name="sequence">1</field>
            <field name="name">New</field>
            <field name="legend_blocked">Blocked</field>
        </record>

        <record id="planning_project_stage_1" model="project.task.type">
            <field name="sequence">5</field>
            <field name="name">Planned</field>
            <field name="legend_blocked">Blocked</field>
        </record>

        <record id="planning_project_stage_2" model="project.task.type">
            <field name="sequence">10</field>
            <field name="name">In Progress</field>
            <field name="legend_blocked">Blocked</field>
        </record>

        <record id="planning_project_stage_3" model="project.task.type">
            <field name="sequence">20</field>
            <field name="name">Done</field>
            <field name="legend_blocked">Blocked</field>
            <field name="fold" eval="True"/>
        </record>

        <record id="planning_project_stage_4" model="project.task.type">
            <field name="sequence">25</field>
            <field name="name">Cancelled</field>
            <field name="legend_blocked">Blocked</field>
            <field name="fold" eval="True"/>
        </record>

        <record id="fsm_project" model="project.project">
            <field name="name">Field Service</field>
            <field name="is_fsm" eval="True"/>
            <field name="allow_task_dependencies" eval="False"/>
            <field name="allow_subtasks" eval="False"/>
            <field name="allow_timesheets" eval="True"/>
            <field name="type_ids" eval="[(4, ref('planning_project_stage_0')), (4, ref('planning_project_stage_1')), (4, ref('planning_project_stage_2')), (4, ref('planning_project_stage_3')), (4, ref('planning_project_stage_4'))]"/>
            <field name="allow_worksheets" eval="True"/>
        </record>
    </data>
</odoo>
