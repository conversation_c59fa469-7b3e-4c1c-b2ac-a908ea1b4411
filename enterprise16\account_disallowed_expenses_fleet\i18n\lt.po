# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses_fleet
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# UAB "Draugiš<PERSON> sprendimai" <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "Car Category"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "Įmonė"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_category
msgid "Disallowed Expenses Category"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_fleet_report_handler
msgid "Disallowed Expenses Fleet Custom Handler"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_vehicle__rate_ids
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses_fleet.fleet_vehicle_view_form
msgid "Disallowed Expenses Rate"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__id
msgid "ID"
msgstr "ID"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Žurnalo įrašas"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "Pradžios data"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,help:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "This checkbox makes the vehicle mandatory while booking a vendor bill."
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__vehicle_id
msgid "Vehicle"
msgstr "Automobilis"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_disallowed_expenses_rate
msgid "Vehicle Disallowed Expenses Rate"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses_fleet.disallowed_expenses_fleet_search_template_extra_options
msgid "Vehicle Split"
msgstr ""
