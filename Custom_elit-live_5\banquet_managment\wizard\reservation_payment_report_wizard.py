from odoo import api, fields, models, http
from odoo.http import request, content_disposition
from datetime import datetime
import xlsxwriter
import io

class ReservationPaymentReport(models.TransientModel):
    _name = 'reservation.payment.report.wizard'

    date_from = fields.Date(
        string='Date From',
        required=True,
        default=fields.Date.context_today
    )
    date_to = fields.Date(
        string='Date To',
        required=True,
        default=fields.Date.context_today
    )

    check_in_state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirm'),
        ('done', 'Done'),
        ('cancel', 'Cancel'),
    ], default='confirm', string='Check In State')

    def print_report(self):
        """Generate Excel report for reservation payments."""
        # Get base URL for the Excel download
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        
        # Construct URL with parameters
        url = f"{base_url}/banquet_managment/reservation_payment_excel"
        url += f"?date_from={self.date_from}&date_to={self.date_to}"
        url += f"&check_in_state={self.check_in_state or ''}"
        
        return {
            'type': 'ir.actions.act_url',
            'url': url,
            'target': 'self',
        }

class ReservationPaymentExcel(http.Controller):
    @http.route('/banquet_managment/reservation_payment_excel', type='http', auth='user')
    def reservation_payment_excel(self, date_from, date_to, check_in_state=None, **kwargs):
        """Generate Excel file for reservation payment report."""
        try:
            # Create Excel workbook with RTL support
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet('تقرير المدفوعات')
            
            # Enable RTL for the worksheet
            worksheet.right_to_left()

            # Add formats with RTL support
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#F2F2F2',
                'border': 1,
                'font_size': 12,
                'font_name': 'Arial'
            })
            
            cell_format = workbook.add_format({
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'font_size': 10,
                'font_name': 'Arial'
            })
            
            date_format = workbook.add_format({
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'font_size': 10,
                'font_name': 'Arial',
                'num_format': 'yyyy-mm-dd'
            })
            
            amount_format = workbook.add_format({
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00',
                'font_size': 10,
                'font_name': 'Arial'
            })
            
            total_format = workbook.add_format({
                'bold': True,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00',
                'bg_color': '#F2F2F2',
                'font_size': 11,
                'font_name': 'Arial'
            })
            
            grand_total_format = workbook.add_format({
                'bold': True,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00',
                'bg_color': '#E6F2FF',  # Light blue background
                'font_size': 12,
                'font_name': 'Arial'
            })

            # Set column widths
            worksheet.set_column('A:A', 20)  # Reservation No
            worksheet.set_column('B:B', 30)  # Customer Name
            worksheet.set_column('C:C', 20)  # Room Name
            worksheet.set_column('D:D', 15)  # Check In Date
            worksheet.set_column('E:E', 15)  # Check Out Date
            worksheet.set_column('F:F', 15)  # Paid Reservation
            worksheet.set_column('G:G', 15)  # Paid Guarantee

            # Write headers in Arabic
            headers = [
                'رقم الحجز',
                'اسم العميل',
                'الشاليه',
                'تاريخ الوصول',
                'تاريخ المغادرة',
                'المبلغ المدفوع',
                'مبلغ الضمان'
            ]
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)

            # Get data
            reservation = request.env['hotel.reservation']
            if check_in_state:
                reservation = reservation.search([('state', '=', check_in_state)])
            else:
                reservation = reservation.search([('state', 'in', ['draft', 'done', 'confirm'])])
            
            # Filter by confirmation date
            reservation = reservation.filtered(
                lambda rec: rec.confirmation_date and 
                str(rec.confirmation_date.date()) >= date_from and 
                str(rec.confirmation_date.date()) <= date_to
            )
            
            if not check_in_state:
                reservation = reservation.filtered(lambda rec: rec.state in ['draft', 'done', 'confirm'])

            # Write data
            row = 1
            total_reservation = 0
            total_guarantee = 0

            for rec in reservation:
                # Get room name from reservation lines
                room_name = ''
                checkin_date = None
                checkout_date = None
                if rec.reservation_line:
                    first_line = rec.reservation_line[0]
                    room_name = first_line.room_number.name if first_line.room_number else ''
                    checkin_date = first_line.checkin
                    checkout_date = first_line.checkout

                worksheet.write(row, 0, rec.name or '', cell_format)
                worksheet.write(row, 1, rec.partner_id.name or '', cell_format)
                worksheet.write(row, 2, room_name, cell_format)
                worksheet.write(row, 3, checkin_date, date_format)
                worksheet.write(row, 4, checkout_date, date_format)
                # Only show total_cost1 if is_auto_payment is True
                total_cost = rec.total_cost1 if rec.is_auto_payment else 0.0
                worksheet.write(row, 5, total_cost or 0.0, amount_format)
                # Only show guarrante_amount if is_guarrantte_amount is True
                guarantee_amount = rec.guarrante_amount if rec.is_guarrantte_amount else 0.0
                worksheet.write(row, 6, guarantee_amount or 0.0, amount_format)
                
                total_reservation += total_cost or 0.0
                total_guarantee += guarantee_amount or 0.0
                row += 1

            # Write subtotals
            total_row = row + 1
            worksheet.write(total_row, 4, 'المجموع', header_format)
            worksheet.write(total_row, 5, total_reservation, total_format)
            worksheet.write(total_row, 6, total_guarantee, total_format)

            # Write grand total
            grand_total_row = total_row + 2
            worksheet.write(grand_total_row, 4, 'المجموع الكلي', header_format)
            worksheet.write(grand_total_row, 5, total_reservation + total_guarantee, grand_total_format)

            # Close workbook
            workbook.close()

            # Prepare response with Arabic filename
            output.seek(0)
            filename = f'تقرير_المدفوعات_{date_from}_الى_{date_to}.xlsx'
            
            return request.make_response(
                output.read(),
                headers=[
                    ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                    ('Content-Disposition', content_disposition(filename))
                ]
            )
            
        except Exception as e:
            return request.not_found(f"Error generating Excel file: {str(e)}")

class ReservationReportAbstractModel(models.AbstractModel):
    _name = 'report.banquet_managment.reservation_report_temp_id'

    def _get_report_values(self, docids=None, data=None):
        reservation = self.env['hotel.reservation']
        if data.get('check_in_state'):
            reservation = reservation.search([('state', '=', data.get('check_in_state'))])
        else:
            reservation = reservation.search([('state', 'in', ['draft', 'done', 'confirm'])])
            
        # Filter by confirmation date instead of check-in date
        reservation = reservation.filtered(
            lambda rec: rec.confirmation_date and 
            str(rec.confirmation_date.date()) >= data.get('date_from') and 
            str(rec.confirmation_date.date()) <= data.get('date_to')
        )
        
        if not data.get('check_in_state'):
            reservation = reservation.filtered(lambda rec: rec.state in ['draft', 'done', 'confirm'])

        payments = self.env['reservation.parking.payment']
        if data.get('check_in_state'):
            if data.get('check_in_state') != 'draft':
                payments = payments.search([('reservation_id', 'in', reservation.ids)])

        return {
            'record': payments,
            'reservation': reservation
        }
