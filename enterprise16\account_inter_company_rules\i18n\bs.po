# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * inter_company_rules
# 
# Translators:
# <PERSON>, 2018
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:89
#, python-format
msgid " Invoice: "
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
msgid ", generate a"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid ""
"<strong>\n"
"                                    <span attrs=\"{'invisible': [('applicable_on','!=', 'purchase')]}\" class=\"ml4\">Sale Order</span>\n"
"                                    <span attrs=\"{'invisible': [('applicable_on','!=', 'sale')]}\" class=\"ml4\">Purchase Order</span>\n"
"                                    <span attrs=\"{'invisible':[('applicable_on','!=', 'sale_purchase')]}\" class=\"ml4\"> Sale and Purchase Order</span>\n"
"                                </strong> using"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
msgid ""
"<strong>\n"
"                                <span attrs=\"{'invisible': [('applicable_on','!=', 'purchase')]}\" class=\"ml8\">Sale Order </span>\n"
"                                <span attrs=\"{'invisible': [('applicable_on','!=', 'sale')]}\" class=\"ml8\">Purchase Order </span>\n"
"                                <span attrs=\"{'invisible': [('applicable_on','!=', 'sale_purchase')]}\" class=\"ml8\"> Sale and Purchase Order </span>\n"
"                            </strong>\n"
"                            using"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company__applicable_on
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings__applicable_on
msgid "Applicable On"
msgstr "Primjenjivo na"

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice__auto_generated
msgid "Auto Generated Document"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order__auto_generated
msgid "Auto Generated Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order__auto_generated
msgid "Auto Generated Sales Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company__auto_validation
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings__auto_validation
msgid "Auto Validation"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:82
#: code:addons/inter_company_rules/models/sale_order.py:77
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings__warehouse_id
msgid ""
"Default value to set on Purchase Orders that will be created based on Sales "
"Orders made to this company."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company__warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company__intercompany_user_id
msgid "Inter Company User"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:42
#: code:addons/inter_company_rules/models/sale_order.py:46
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Rules"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:81
#, python-format
msgid "Please define %s journal for this company: \"%s\" (id:%d)."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:39
#, python-format
msgid "Provide at least one user for inter company relation for % "
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:44
#: code:addons/inter_company_rules/models/sale_order.py:43
#, python-format
msgid "Provide one user for intercompany relation for % "
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_purchase_order
#: selection:res.company,applicable_on:0
#: selection:res.config.settings,applicable_on:0
msgid "Purchase Order"
msgstr "Nabavna narudžba"

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company__intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings__rule_type
msgid "Rule Type"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_sale_order
#: selection:res.company,applicable_on:0
#: selection:res.config.settings,applicable_on:0
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: inter_company_rules
#: selection:res.company,applicable_on:0
#: selection:res.config.settings,applicable_on:0
msgid "Sale and Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings__rules_company_id
msgid "Select Company"
msgstr "Odaberite kompaniju"

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings__rules_company_id
msgid "Select company to setup Inter company rules."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice__auto_invoice_id
msgid "Source Invoice"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order__auto_purchase_order_id
msgid "Source Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order__auto_sale_order_id
msgid "Source Sales Order"
msgstr ""

#. module: inter_company_rules
#: selection:res.company,rule_type:0 selection:res.config.settings,rule_type:0
msgid "Synchronize Invoices & Bills"
msgstr ""

#. module: inter_company_rules
#: selection:res.company,rule_type:0 selection:res.config.settings,rule_type:0
msgid "Synchronize Sales & Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company__warehouse_id
msgid "Warehouse"
msgstr "Skladište"

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings__warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "When a company validates a"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:47
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different "
"than purchase price list currency."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/res_company.py:37
#, python-format
msgid ""
"You cannot select to create invoices based on other invoices\n"
"                    simultaneously with another option ('Create Sales Orders when buying to this\n"
"                    company' or 'Create Purchase Orders when selling to this company')!"
msgstr ""

#. module: inter_company_rules
#: selection:res.company,auto_validation:0
#: selection:res.config.settings,auto_validation:0
msgid "draft"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "generate a"
msgstr ""

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
msgid "to"
msgstr "za"

#. module: inter_company_rules
#: model_terms:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "to this company,"
msgstr ""

#. module: inter_company_rules
#: selection:res.company,auto_validation:0
#: selection:res.config.settings,auto_validation:0
msgid "validated"
msgstr ""
