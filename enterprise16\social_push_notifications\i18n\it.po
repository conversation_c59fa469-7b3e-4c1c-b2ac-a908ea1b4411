# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_push_notifications
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "<span class=\"ps-2\">seconds</span>"
msgstr "<span class=\"ps-2\">secondi</span>"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_post_ids
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Tutti i post correlati dei social media"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
#, python-format
msgid "Allow"
msgstr "Consenti"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Body"
msgstr "Corpo"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Delay"
msgstr "Ritardo"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
#, python-format
msgid "Deny"
msgstr "Nega"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__display_push_notification_attributes
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__display_push_notification_attributes
msgid "Display Push Notifications Attributes"
msgstr "Visualizza attributi notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__display_push_notifications_preview
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__display_push_notifications_preview
msgid "Display Push Notifications Preview"
msgstr "Visualizza anteprima notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_post__visitor_domain
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__visitor_domain
msgid "Domain to send push notifications to visitors."
msgstr "Dominio per inviare notifiche push ai visitatori."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_enable_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_enable_push_notifications
msgid "Enable Web Push Notifications"
msgstr "Abilita notifiche push web"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
#, python-format
msgid "Enable push notifications to be notified about new features."
msgstr ""
"Abilita le notifiche push per ricevere notifiche di nuove funzionalità."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_admin_key_file
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_admin_key_file
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_admin_key_file
msgid "Firebase Admin Key File"
msgstr "File chiave amministratore Firebase"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
#, python-format
msgid "Firebase Admin Key File is missing from the configuration."
msgstr "File chiave amministratore Firebase mancante nella configurazione."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_project_id
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_project_id
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_project_id
msgid "Firebase Project ID"
msgstr "ID progetto Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_push_certificate_key
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_push_certificate_key
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_push_certificate_key
msgid "Firebase Push Certificate Key"
msgstr "Chiave certificato push Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_sender_id
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_sender_id
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_sender_id
msgid "Firebase Sender ID"
msgstr "ID mittente Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_web_api_key
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_web_api_key
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_web_api_key
msgid "Firebase Web API Key"
msgstr "Chiave API web Firebase"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.push_notifications_preview
msgid "Google Chrome ·"
msgstr "Google Chrome ·"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__id
msgid "ID"
msgstr "ID"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Icon"
msgstr "Icona"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Icon Image"
msgstr "Immagine icona"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Key File"
msgstr "File chiave"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_view_form
msgid "Local Time"
msgstr "Ora locale"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_media__media_type
msgid "Media Type"
msgstr "Tipo di social"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_live_post.py:0
#: code:addons/social_push_notifications/models/social_post_template.py:0
#, python-format
msgid "New Message"
msgstr "Nuovo messaggio"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_delay
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_delay
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_delay
msgid "Notification Request Delay (seconds)"
msgstr "Ritardo richiesta di notifica (secondi)"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_icon
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_icon
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_icon
#, python-format
msgid "Notification Request Icon"
msgstr "Icona richiesta di notifica"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_body
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_body
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_body
msgid "Notification Request Text"
msgstr "Testo richiesta di notifica"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_title
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_title
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_title
msgid "Notification Request Title"
msgstr "Titolo richiesta di notifica"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Notification Title"
msgstr "Titolo notifica"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_kanban
msgid "Notifications"
msgstr "Notifiche"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_push_notifications_count
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_push_notifications_count
msgid "Number Of Push Notifications"
msgstr "Numero di notifiche push"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Project ID"
msgstr "ID progetto"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_kanban
msgid "Push"
msgstr "Invia push"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Push Certificate Key ID"
msgstr "ID chiave certificato push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_image
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_image
msgid "Push Icon Image"
msgstr "Immagine icona push"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_search
msgid "Push Notification Off"
msgstr "Notifica push disattivata"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_search
msgid "Push Notification On"
msgstr "Notifica push attivata"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Push Notification Options"
msgstr "Opzioni notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_title
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_title
msgid "Push Notification Title"
msgstr "Titolo notifica push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_push_notification_ids
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_push_notification_ids
#: model:ir.model.fields.selection,name:social_push_notifications.selection__social_media__media_type__push_notifications
#: model:social.media,name:social_push_notifications.social_media_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
msgid "Push Notifications"
msgstr "Notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor__has_push_notifications
msgid "Push Notifications Enabled"
msgstr "Notifiche push attivate"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notifications_preview
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notifications_preview
msgid "Push Notifications Preview"
msgstr "Anteprima notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__push_token
msgid "Push Subscription"
msgstr "Sottoscrizione push"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website_visitor_push_subscription
msgid "Push Subscription for a Website Visitor"
msgstr "Abbonamento push per visitatore sito web"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor__push_subscription_ids
msgid "Push Subscriptions"
msgstr "Sottoscrizioni push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_target_url
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_target_url
msgid "Push Target URL"
msgstr "URL obiettivo push"

#. module: social_push_notifications
#: model:ir.model.constraint,message:social_push_notifications.constraint_website_visitor_push_subscription_push_token_uniq
msgid "Push token can't be duplicated!"
msgstr "Il token push non può essere duplicato."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_live_post__reached_visitor_ids
msgid "Reached Visitors"
msgstr "Visitatori raggiunti"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
msgid "Send Push"
msgstr "Invia push"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_tree
msgid "Send Push Notification"
msgstr "Invia notifica push"

#. module: social_push_notifications
#: model:ir.actions.server,name:social_push_notifications.social_send_push_notifications_action_server
msgid "Send Push Notifications"
msgstr "Invia notifiche push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__use_visitor_timezone
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__use_visitor_timezone
msgid "Send at Visitors' Timezone"
msgstr "Invia al fuso orario dei visitatori"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid ""
"Send push notifications and configure this website's notifications "
"permission request"
msgstr ""
"Invia notifiche push e configura la richiesta di autorizzazione notifiche "
"del sito web"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Sender ID"
msgstr "ID mittente"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_account
msgid "Social Account"
msgstr "Account social"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_live_post
msgid "Social Live Post"
msgstr "Post social dal vivo"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_media
msgid "Social Media"
msgstr "Social media"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_post
msgid "Social Post"
msgstr "Post social"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_post_template
msgid "Social Post Template"
msgstr "Modello post social"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/website_visitor.py:0
#, python-format
msgid "Some selected visitors do not allow push notifications."
msgstr "Alcuni dei visitatori selezionati non consentono le notifiche push."

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Target URL"
msgstr "URL obiettivo"

#. module: social_push_notifications
#: model:ir.model.constraint,message:social_push_notifications.constraint_social_account_website_unique
msgid "There is already a configuration for this website."
msgstr "Esiste già una configurazione per il sito web."

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_account__website_id
msgid ""
"This firebase configuration will only be used for the specified website"
msgstr "La configurazione Firebase verrà usata solo per il sito web indicato"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_post__push_notification_image
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__push_notification_image
msgid "This icon will be displayed in the browser notification"
msgstr "L'icona verrà visualizzata nella notifica del browser"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Title"
msgstr "Titolo"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campagna UTM"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Use your own Firebase Account for this website's push notifications"
msgstr ""
"Utilizzo del proprio account Firebase per le notifiche push del sito web"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_use_own_account
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_use_own_account
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_use_own_account
msgid "Use your own Firebase account"
msgstr "Usa account Firebase personale"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Utilizzato per effettuare confronti quando è necessario limitare alcune "
"funzionalità di un particolare social (\"Facebook\", \"Twitter\"...)."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__visitor_domain
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__visitor_domain
msgid "Visitor Domain"
msgstr "Dominio visitatore"

#. module: social_push_notifications
#: model:ir.ui.menu,name:social_push_notifications.social_visitor
msgid "Visitors"
msgstr "Visitatori"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
#, python-format
msgid "Want to discover new versions?"
msgstr "Vuoi scoprire le nuove versioni?"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Web API Key"
msgstr "Chiave API web"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__website_id
msgid "Website"
msgstr "Sito web"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website_visitor
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__website_visitor_id
msgid "Website Visitor"
msgstr "Visitatore sito web"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
#, python-format
msgid "You can't delete a Push Notification account."
msgstr "Impossibile eliminare un account per notifiche push."

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
#, python-format
msgid ""
"You have to either install \"firebase_admin>=2.17.0\" or "
"\"google_auth>=1.18.0\" to be able to send push notifications."
msgstr ""
"Per poter inviare le notifiche push è necessario installare "
"\"firebase_admin>=2.17.0\" oppure \"google_auth>=1.18.0\"."

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"************\""
msgstr "es. \"************\""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"BIzbSyXhhsFHEgphW55CSg5cV7h7c_S-AuTMKc9\""
msgstr "es. \"BIzbSyXhhsFHEgphW55CSg5cV7h7c_S-AuTMKc9\""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"CCSc77KP_LX8dTAogFakOoJ_VqNP15u0-43psDJe__a9B...\""
msgstr "es. \"CCSc77KP_LX8dTAogFakOoJ_VqNP15u0-43psDJe__a9B...\""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"my-project-id\""
msgstr "es. \"ID-mio-progetto\""

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_post__use_visitor_timezone
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__use_visitor_timezone
msgid ""
"e.g: If you post at 15:00 your time, all visitors will receive the post at "
"15:00 their time."
msgstr ""
"es.: se viene pubblicato alle 15:00 ora locale, tutti i visitatori "
"riceveranno il post alle 15:00 del loro fuso orario."
