# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account_followup
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"The letter will be sent using the IAP service from Odoo.&#10;Make sure you have enough credits on your account or proceed to a recharge.\"/>\n"
"                        <br/>"
msgstr ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Advertencia\" title=\"Se usará el servicio de compras dentro de la aplicación de Odoo para enviar la carta.&#10;Asegúrese de tener créditos suficientes en su cuenta o recárgelos.\"/>\n"
"                        <br/>"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "By post"
msgstr "Por correo postal"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Criterios de seguimiento"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Reporte de seguimiento"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Enviar una carta"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "Sending this document by post will cost"
msgstr "Enviar este documento por correo postal costará"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail
msgid "Snailmail"
msgstr "Correo postal"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail_cost
msgid "Stamps"
msgstr "Estampas"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Asistente para enviar recordatorios manuales a los clientes"

#. module: snailmail_account_followup
#. odoo-python
#: code:addons/snailmail_account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a letter by post, but no follow-up contact has any "
"address set"
msgstr ""
"Está intentando enviar una carta por correo postal, pero ningún contacto de "
"seguimiento tiene una dirección establecida"
