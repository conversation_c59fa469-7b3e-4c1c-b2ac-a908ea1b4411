# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_kwh
msgid "1 000 kWh"
msgstr "1 000 kWh"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_m3
msgid "1 000 m3"
msgstr "1 000 m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_p/st
msgid "1 000 p/st"
msgstr "1 000 p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__100_p/st
msgid "100 p/st"
msgstr "100 p/st"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"                Typer:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> Muligheder:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> Partnere:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Account move line"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Active"
msgstr "Aktiv"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "All"
msgstr "Alle"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Arrival"
msgstr "Ankomst"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "Ankomst land"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "Per land"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "Per type"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Check the expired"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Check the premature"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "Vare"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_commodity_code
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_code_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Commodity Code"
msgstr "Varekode"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_code_id
msgid "Commodity code"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "Virksomhed land"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_country_code
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "Land"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "Oprindelses land"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_invoice_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_invoice_transaction_code_id
msgid "Default invoice transaction code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_refund_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_refund_transaction_code_id
msgid "Default refund transaction code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default transport mode"
msgstr ""

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Dispatch"
msgstr "Afsend"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr "Udvidet"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr "Udvidet tilstand"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Include VAT"
msgstr "Inkluder moms"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_incoterm_code
msgid "Incoterm Code"
msgstr "Incoterm kode"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "Intrastat kode"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat Country"
msgstr "Instrastat land"

#. module: account_intrastat
#: model:account.report,name:account_intrastat.intrastat_report
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
msgid "Intrastat Report"
msgstr "Instrastat rapport"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report_handler
msgid "Intrastat Report Custom Handler"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr "Instrastat transport tilstand"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr "Intrastat kode"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr "Instrastat land, ankomst for salg, afsendelse for indkøb"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr "Instrastat medlem"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr "Instrastat region"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Intrastat transaction code"
msgstr ""

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid commodity intrastat code products."
msgstr ""

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid transaction intrastat code entries."
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "Postering"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_intrastat
#: model:res.country,name:account_intrastat.xi
msgid "Northern Ireland"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Only with VAT numbers"
msgstr "Kun med moms tal"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
msgid "Origin"
msgstr "Oprindelse"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_product_origin_country
msgid "Origin Country"
msgstr "Oprindelsesland"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_partner_vat
msgid "Partner VAT"
msgstr "Partner Moms"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Product"
msgstr "Produkt"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr "Produkt Land"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product Variant"
msgstr "Varevariant"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "Region"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_region_code
msgid "Region Code"
msgstr "Regions kode"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have expired intrastat"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have premature intrastat"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have undefined"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some products have undefined"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard"
msgstr "Standard"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Supplementary Unit"
msgstr ""

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_supplementary_units
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "Supplementary Units"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "Supplementary Units per Product"
msgstr ""

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_system
msgid "System"
msgstr "System"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "Landet hvis momsrapport skal bruges for denne virksomhed"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
#: model:ir.model.fields,help:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "The number of supplementary units per product quantity."
msgstr ""

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Total"
msgstr "I alt"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "Transaktion"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transaction_code
msgid "Transaction Code"
msgstr "Transaktions kode"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "Transport"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transport_code
msgid "Transport Code"
msgstr "Transport kode"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr "Triplet kode/type/land_id skal være unik."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Undefined supplementary unit products."
msgstr ""

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Undefined weight products."
msgstr ""

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_value
msgid "Value"
msgstr "Værdi"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "View Journal Entry"
msgstr "Vis journalpost"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_weight
msgid "Weight"
msgstr "Vægt"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "With VAT numbers"
msgstr "Med moms numre"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__c/k
msgid "c/k"
msgstr "c/k"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ce/el
msgid "ce/el"
msgstr "ce/el"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "commodity codes"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ct/l
msgid "ct/l"
msgstr "ct/l"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__gi_f_/_s
msgid "gi F / S"
msgstr "gi F / S"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_90_%_sdt
msgid "kg 90 % sdt"
msgstr "kg 90 % sdt"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_h2o2
msgid "kg H2O2"
msgstr "kg H2O2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_k2o
msgid "kg K2O"
msgstr "kg K2O"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_koh
msgid "kg KOH"
msgstr "kg KOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_n
msgid "kg N"
msgstr "kg N"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_naoh
msgid "kg NaOH"
msgstr "kg NaOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_p2o5
msgid "kg P2O5"
msgstr "kg P2O5"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_u
msgid "kg U"
msgstr "kg U"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_met_am_
msgid "kg met.am."
msgstr "kg met.am."

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg/net_eda
msgid "kg/net eda"
msgstr "kg/net eda"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__l_alc__100_%
msgid "l alc. 100 %"
msgstr "l alc. 100 %"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m2
msgid "m2"
msgstr "m2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m3
msgid "m3"
msgstr "m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__p/st
msgid "p/st"
msgstr "p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__pa
msgid "pa"
msgstr "pa"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "product"
msgstr "produkt"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "product's commodity codes"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "supplementary units"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__t__co2
msgid "t. CO2"
msgstr "t. CO2"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "transaction codes"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "weights"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "when they are required."
msgstr ""
