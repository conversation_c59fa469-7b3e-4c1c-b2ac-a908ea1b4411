<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.DiscussSidebarMailbox" owl="1">
        <button class="o_DiscussSidebarMailbox btn d-flex align-items-center py-1 px-0 border-0 rounded-0 fw-normal text-dark"
            t-att-class="{
                'bg-100': discussSidebarMailboxView.mailbox.thread !== messaging.discuss.activeThread,
                'o-active bg-200': discussSidebarMailboxView.mailbox.thread === messaging.discuss.activeThread,
                'o-starred-box': discussSidebarMailboxView.mailbox === messaging.starred,
            }" t-attf-class="{{ className }}" t-on-click="discussSidebarMailboxView.mailbox.thread.onClick" t-att-data-mailbox-local-id="discussSidebarMailboxView.mailbox.localId" t-att-data-mailbox-name="discussSidebarMailboxView.mailbox.name"
            t-ref="root"
        >
            <ThreadIcon className="'o_DiscussSidebarMailbox_item ms-4 me-2'" thread="discussSidebarMailboxView.mailbox.thread"/>
            <div class="o_DiscussSidebarMailbox_item o_DiscussSidebarMailbox_name me-2 text-truncate">
                <t t-esc="discussSidebarMailboxView.mailbox.name"/>
            </div>
            <div t-attf-class="o_DiscussSidebarMailbox_item flex-grow-1 {{ discussSidebarMailboxView.mailbox.counter === 0 ? 'me-3': '' }}"/>
            <t t-if="discussSidebarMailboxView.mailbox.counter > 0">
                <div t-attf-class="o_DiscussSidebarMailbox_counter o_DiscussSidebarMailbox_item badge rounded-pill {{ discussSidebarMailboxView.mailbox === messaging.starred ? 'bg-400 text-light' : 'text-bg-primary' }} ms-1 me-3">
                    <t t-esc="discussSidebarMailboxView.mailbox.counter"/>
                </div>
            </t>
        </button>
    </t>
</templates>
