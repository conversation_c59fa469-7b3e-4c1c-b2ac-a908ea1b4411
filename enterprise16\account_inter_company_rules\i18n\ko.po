# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_inter_company_rules
# 
# Translators:
# <PERSON>, 2022
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "%s Invoice: %s"
msgstr "%s 청구서 : %s"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_generated
msgid "Auto Generated Document"
msgstr "자동으로 생성된 문서"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr "%(company)s 회사의 %(origin)s에서 자동 생성되었습니다."

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_company
msgid "Companies"
msgstr "회사"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid "Create as"
msgstr "다음으로 생성"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__not_synchronize
msgid "Do not synchronize"
msgstr "동기화 하지 마십시오"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/res_company.py:0
#: code:addons/account_inter_company_rules/models/res_config_settings.py:0
#, python-format
msgid ""
"Generate a bill/invoice when a company confirms an invoice/bill for %s."
msgstr "회사가 %s에 대한 청구서/공급업체 청구서를 확인하면 청구서/공급업체 청구서를 생성합니다."

#. module: account_inter_company_rules
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Transactions"
msgstr "회사간 자료 교환"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_transaction_message
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_transaction_message
msgid "Intercompany Transaction Message"
msgstr "회사간 거래 메시지"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move
msgid "Journal Entry"
msgstr "분개"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_line
msgid "Journal Item"
msgstr "분개 항목"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr "회사 간 규칙에 의거한 문서를 작성할 책임자."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Rule"
msgstr "규칙"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rules_company_id
msgid "Select Company"
msgstr "회사 선택"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr "선택한 회사에서 회사 간 규칙을 설정할 유형을 선택합니다."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_invoice_id
msgid "Source Invoice"
msgstr "원본 청구서"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__invoice_and_refund
msgid "Synchronize invoices/bills"
msgstr "청구서 및 공급업체 청구서 동기화"
