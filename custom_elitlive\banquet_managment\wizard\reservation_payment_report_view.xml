<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

       <record id="reservation_payment_wizard_id" model="ir.ui.view">
           <field name="name">Reservation Payment Report</field>
           <field name="model">reservation.payment.report.wizard</field>
           <field name="arch" type="xml">
               <form string="_form">
                   <sheet>
                       <group>
                           <group>
                               <field name="date_from"/>
                               <field name="check_in_state"/>
                           </group>
                           <group>
                               <field name="date_to"/>
                               <field name="is_guarantee"/>
                               <field name="is_auto_payment"/>
                           </group>
                       </group>
                   </sheet>
                   <footer>
                       <button type="object" name="print_report" string="Generate Report" class="oe_highlight"/>
                   </footer>
               </form>
           </field>
       </record>


        <record id="report_wizard_action" model="ir.actions.act_window">
            <field name="name">Report Wizard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">reservation.payment.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="reservation_payment_wizard_menu" name="Reservation Payment" parent="hotel.hotel_report_menu" action="report_wizard_action" sequence="1"/>

    </data>
</odoo>