# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_facebook
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Tunç Sabah, 2022
# Tu<PERSON>y <PERSON>ıl <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "<b class=\"d-block mb-2\">Facebook Page</b>"
msgstr "<b class=\"d-block mb-2\">Facebook Sayfası</b>"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_live_post__facebook_post_id
msgid "Actual Facebook ID of the post"
msgstr "Gönderilerin gerçek Facebook ID'si"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr "Bir hata oluştu."

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App ID"
msgstr "Uygulama ID'si"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App Secret"
msgstr "Gizli Uygulama"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Üretici Resmi"

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_res_config_settings__facebook_use_own_account
msgid ""
"Check this if you want to use your personal Facebook Developer Account "
"instead of the provided one."
msgstr ""
"Bunu verdiğiniz hesap yerine kişisel Facebook Geliştirici Hesabı kullanmak "
"istiyorsanız işaretleyin."

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_comments_count
msgid "Comments"
msgstr "Yorumlar"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__display_facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__display_facebook_preview
msgid "Display Facebook Preview"
msgstr "Facebook Önizlemesini Görüntüle"

#. module: social_facebook
#: model:ir.model.fields.selection,name:social_facebook.selection__social_media__media_type__facebook
#: model:social.media,name:social_facebook.social_media_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_access_token
msgid "Facebook Access Token"
msgstr "Facebook Erişim Token'ı"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_account_id
msgid "Facebook Account ID"
msgstr "Facebook Hesap ID'si"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_app_id
msgid "Facebook App ID"
msgstr "Facebook Uygulama ID'si"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_client_secret
msgid "Facebook App Secret"
msgstr "Facebook Uygulama Gizliliği"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_author_id
msgid "Facebook Author ID"
msgstr "Facebook Üretici ID'si"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Facebook Comments"
msgstr "Facebook Yorumları"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "Facebook Developer Account"
msgstr "Facebook Developer Account"

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_access_token
msgid ""
"Facebook Page Access Token provided by the Facebook API, this should never be set manually.\n"
"            It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"Facebook API tarafından sağlanan Facebook Sayfası Erişim Token'ı, asla manuel olarak ayarlanmamalıdır.\n"
"Bu hesaba bilgi gönderirken veya bu hesaptan bilgi okurken isteklerin kimliğini doğrulamak için kullanılır."

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_account_id
msgid ""
"Facebook Page ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"Facebook API tarafından sağlanan Facebook sayfa ID'si, asla manuel olarak "
"ayarlanmamalıdır."

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_post_id
msgid "Facebook Post ID"
msgstr "Facebook Gönderi ID'si"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_preview
msgid "Facebook Preview"
msgstr "Facebook Önizleme"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token or it may have expired."
msgstr ""
"Facebook geçerli bir erişim belirteci sağlamadı veya süresi dolmuş olabilir."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token."
msgstr "Facebook, geçerli bir erişim belirteci sağlamadı."

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_is_event_post
msgid "Is event post"
msgstr "Etkinlik gönderisi"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_likes_count
#, python-format
msgid "Likes"
msgstr "Beğeniler"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_media__media_type
msgid "Media Type"
msgstr "Medya Türü"

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_mentions
msgid "Page Mentions"
msgstr "Sayfa Bahsedilenleri"

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_posts
msgid "Page Posts"
msgstr "Sayfa Gönderileri"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Post Image"
msgstr "Gönderi Resmi"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid ""
"Post not found. It could be because the post has been deleted on the Social "
"Platform."
msgstr ""
"Gönderi bulunamadı. Bunun nedeni, gönderinin Sosyal Platform'da silinmiş "
"olması olabilir."

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Published by Facebook Page •"
msgstr "Facebook sayfası tarafından yayınlandı •"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_reach
msgid "Reach"
msgstr "Ulaşma"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_shares_count
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Shares"
msgstr "Paylaşımlar"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_account
msgid "Social Account"
msgstr "Sosyal Hesap"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_live_post
msgid "Social Live Post"
msgstr "Sosyal Canlı Gönderi"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_media
msgid "Social Media"
msgstr "Sosyal Medya"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post
msgid "Social Post"
msgstr "Sosyal Gönderi"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post_template
msgid "Social Post Template"
msgstr "Sosyal Gönderi Şablonu"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream
msgid "Social Stream"
msgstr "Sosyal Medya Akışı"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream_post
msgid "Social Stream Post"
msgstr "Sosyal Medya Yayını"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr "Bu hesaba bağlı bir sayfa yok."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator. "
msgstr "Yetkisiz. Lütfen yöneticinizle iletişime geçin."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "Bilinmeyen"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_use_own_account
msgid "Use your own Facebook Account"
msgstr "Kendi Facebook hesabınızı kullanın"

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Bazı özellikleri belirli bir medyayla sınırlamamız gerektiğinde "
"karşılaştırmalar yapmak için kullanılır ('facebook', 'twitter', ...)."

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_user_likes
msgid "User Likes"
msgstr "Kullanıcı Beğenileri"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "Görünümler"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_live_post.py:0
#: code:addons/social_facebook/models/social_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: %s)."
msgstr ""
"Görselinizi yükleyemedik, görselin boyutunu azaltıp yüklemeyi tekrar "
"deneyiniz ( hata: %s)."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""
"Hesap bağlantısı kurulamıyor, lütfen sistem yöneticinize başvurun. Daha "
"Fazla Bilgi İçin : %s"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "added an event"
msgstr "bir etkinlik ekledi"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "• <i class=\"fa fa-globe\"/>"
msgstr "• <i class=\"fa fa-globe\"/>"
