<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="account_consolidation.group_consolidation_user" model="res.groups">
            <field name="name">Consolidation user</field>
            <field name="implied_ids" eval="[(4, ref('account.group_account_manager'))]"/>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>
        <record id="base.user_root" model="res.users">
            <field name="groups_id" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
        </record>

        <record id="base.user_admin" model="res.users">
            <field name="groups_id" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
        </record>

        <data noupdate="1">

        <record id="company_consolidation_rule" model="ir.rule">
            <field name="name">Companies for consolidation group</field>
            <field name="model_id" ref="model_res_company"/>
            <field eval="False" name="global"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">[('id','in', user.company_ids.ids)]</field>
        </record>

        <record id="chart_consolidation_rule" model="ir.rule">
            <field name="name">Charts for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_chart"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('company_ids', '=', False), ('company_ids', 'in', user.company_ids.ids)]</field>
        </record>

        <record id="analysis_period_consolidation_rule" model="ir.rule">
            <field name="name">Analysis periods for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_period"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('chart_id.company_ids', '=', False), ('chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>

        <record id="company_period_consolidation_rule" model="ir.rule">
            <field name="name">Company periods for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_company_period"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('chart_id.company_ids', '=', False), ('chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>

        <record id="consolidation_account_consolidation_rule" model="ir.rule">
            <field name="name">Consolidation accounts for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_account"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('chart_id.company_ids', '=', False), ('chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>

        <record id="consolidation_account_section_consolidation_rule" model="ir.rule">
            <field name="name">Consolidation sections for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_group"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('chart_id.company_ids', '=', False), ('chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>

        <record id="consolidation_journal_consolidation_rule" model="ir.rule">
            <field name="name">Consolidation journals for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_journal"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('chart_id.company_ids', '=', False), ('chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>
        <record id="consolidation_journal_line_consolidation_rule" model="ir.rule">
            <field name="name">Consolidation journal lines for consolidation group</field>
            <field name="model_id" ref="account_consolidation.model_consolidation_journal_line"/>
            <field name="groups" eval="[(4, ref('account_consolidation.group_consolidation_user'))]"/>
            <field name="domain_force">['|', ('account_id.chart_id.company_ids', '=', False), ('account_id.chart_id.company_ids', 'in', user.company_ids.ids)]</field>
        </record>
    </data>
</odoo>
