# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا خاصة "
"بالشركة فقط. \" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"مجموعة الشروط والإجراءات التي سوف تكون متُاحة لكافة المرفقات التي تستوفي "
"الشروط "

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "قم بمركزة الملفات المرفقة بالمنتجات "

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "إنشاء"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr "جداول البيانات"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "علامات التصنيف الافتراضية "

#. module: documents_product
#: model:documents.facet,name:documents_product.documents_product_documents_facet
msgid "Documents"
msgstr "المستندات"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "إعدادات المنتج للمستندات "

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr "MSDS"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "جديد"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr "الخطط "

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "المنتج"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr "علامات تصنيف المنتج "

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
msgid "Product Workspace"
msgstr "مساحة عمل المنتج "

#. module: documents_product
#: model:ir.model.fields.selection,name:documents_product.selection__documents_workflow_rule__create_model__product_template
msgid "Product template"
msgstr "قالب المنتج"

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "المنتجات"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr "المواصفات "

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "مساحة العمل"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr "مساحة العمل الافتراضية للمنتج "
