{"version": 9, "sheets": [{"id": "Sheet1", "name": "Sheet1", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 127.36083984375}}, "merges": [], "cells": {"A1": {"style": 1}, "A2": {"style": 1}, "A3": {"style": 1}, "B1": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"user_id\",\"false\")", "dependencies": [], "value": "None"}}, "B2": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": "United States"}}, "B3": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"user_id\",\"false\",\"country_id\",\"233\",\"measure\",\"__count\")", "dependencies": [], "value": "Count"}}, "C2": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\")", "dependencies": [], "value": "Total"}}, "C3": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"measure\",\"__count\")", "dependencies": [], "value": "Count"}}, "C1": {"style": 1}, "A4": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"14\")", "dependencies": [], "value": "Azure Interior"}}, "A5": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"10\")", "dependencies": [], "value": "Deco Addict"}}, "A6": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"11\")", "dependencies": [], "value": "Gemini Furniture"}}, "A7": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"15\")", "dependencies": [], "value": "Lumber Inc"}}, "A8": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"12\")", "dependencies": [], "value": "Ready Mat"}}, "A9": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"13\")", "dependencies": [], "value": "The Jackson Group"}}, "A10": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"9\")", "dependencies": [], "value": "Wood Corner"}}, "A11": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"1\")", "dependencies": [], "value": "YourCompany"}}, "A12": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"parent_id\",\"false\")", "dependencies": [], "value": "None"}}, "A13": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\")", "dependencies": [], "value": "Total"}}, "B4": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"14\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": 3}}, "B5": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"10\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B6": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"11\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B7": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"15\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B8": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"12\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B9": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"13\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B10": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"9\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B11": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"1\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B12": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"false\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": ""}}, "B13": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"user_id\",\"false\",\"country_id\",\"233\")", "dependencies": [], "value": 3}}, "C4": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"14\")", "dependencies": [], "value": 3}}, "C5": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"10\")", "dependencies": [], "value": ""}}, "C6": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"11\")", "dependencies": [], "value": ""}}, "C7": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"15\")", "dependencies": [], "value": ""}}, "C8": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"12\")", "dependencies": [], "value": ""}}, "C9": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"13\")", "dependencies": [], "value": ""}}, "C10": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"9\")", "dependencies": [], "value": ""}}, "C11": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"1\")", "dependencies": [], "value": ""}}, "C12": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"parent_id\",\"false\")", "dependencies": [], "value": ""}}, "C13": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\")", "dependencies": [], "value": 3}}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true}, {"id": "daaf9b38-a53f-47d6-81c5-7d84e6abc0f5", "name": "Sheet2", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 191.07861328125}, "1": {"size": 110.6220703125}, "2": {"size": 218.3798828125}, "3": {"size": 78.52197265625}, "4": {"size": 60.9326171875}, "5": {"size": 78.45849609375}, "6": {"size": 83.30810546875}}, "merges": [], "cells": {"A1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"display_name\")", "dependencies": [], "value": "Display Name"}}, "B1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"phone\")", "dependencies": [], "value": "Phone"}}, "C1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"email\")", "dependencies": [], "value": "Email"}}, "D1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"user_id\")", "dependencies": [], "value": "Salesperson"}}, "E1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"activity_ids\")", "dependencies": [], "value": "Activities"}}, "F1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"city\")", "dependencies": [], "value": "City"}}, "G1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"country_id\")", "dependencies": [], "value": "Country"}}, "A2": {"formula": {"text": "=LIST(\"1\",\"1\",\"display_name\")", "dependencies": [], "value": "Azure Interior"}}, "B2": {"formula": {"text": "=LIST(\"1\",\"1\",\"phone\")", "dependencies": [], "value": "+58 ************"}}, "C2": {"formula": {"text": "=LIST(\"1\",\"1\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D2": {"formula": {"text": "=LIST(\"1\",\"1\",\"user_id\")", "dependencies": [], "value": ""}}, "E2": {"formula": {"text": "=LIST(\"1\",\"1\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F2": {"formula": {"text": "=LIST(\"1\",\"1\",\"city\")", "dependencies": [], "value": "Fremont"}}, "G2": {"formula": {"text": "=LIST(\"1\",\"1\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A3": {"formula": {"text": "=LIST(\"1\",\"2\",\"display_name\")", "dependencies": [], "value": "Azure Interior, <PERSON>"}}, "B3": {"formula": {"text": "=LIST(\"1\",\"2\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C3": {"formula": {"text": "=LIST(\"1\",\"2\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D3": {"formula": {"text": "=LIST(\"1\",\"2\",\"user_id\")", "dependencies": [], "value": ""}}, "E3": {"formula": {"text": "=LIST(\"1\",\"2\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F3": {"formula": {"text": "=LIST(\"1\",\"2\",\"city\")", "dependencies": [], "value": "Fremont"}}, "G3": {"formula": {"text": "=LIST(\"1\",\"2\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A4": {"formula": {"text": "=LIST(\"1\",\"3\",\"display_name\")", "dependencies": [], "value": "Azure <PERSON>, <PERSON><PERSON>"}}, "B4": {"formula": {"text": "=LIST(\"1\",\"3\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C4": {"formula": {"text": "=LIST(\"1\",\"3\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D4": {"formula": {"text": "=LIST(\"1\",\"3\",\"user_id\")", "dependencies": [], "value": ""}}, "E4": {"formula": {"text": "=LIST(\"1\",\"3\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F4": {"formula": {"text": "=LIST(\"1\",\"3\",\"city\")", "dependencies": [], "value": "Fremont"}}, "G4": {"formula": {"text": "=LIST(\"1\",\"3\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A5": {"formula": {"text": "=LIST(\"1\",\"4\",\"display_name\")", "dependencies": [], "value": "Azure Interior, <PERSON>"}}, "B5": {"formula": {"text": "=LIST(\"1\",\"4\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C5": {"formula": {"text": "=LIST(\"1\",\"4\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D5": {"formula": {"text": "=LIST(\"1\",\"4\",\"user_id\")", "dependencies": [], "value": ""}}, "E5": {"formula": {"text": "=LIST(\"1\",\"4\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F5": {"formula": {"text": "=LIST(\"1\",\"4\",\"city\")", "dependencies": [], "value": "Fremont"}}, "G5": {"formula": {"text": "=LIST(\"1\",\"4\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A6": {"formula": {"text": "=LIST(\"1\",\"5\",\"display_name\")", "dependencies": [], "value": "Deco Addict"}}, "B6": {"formula": {"text": "=LIST(\"1\",\"5\",\"phone\")", "dependencies": [], "value": "+32 10 588 558"}}, "C6": {"formula": {"text": "=LIST(\"1\",\"5\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D6": {"formula": {"text": "=LIST(\"1\",\"5\",\"user_id\")", "dependencies": [], "value": ""}}, "E6": {"formula": {"text": "=LIST(\"1\",\"5\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F6": {"formula": {"text": "=LIST(\"1\",\"5\",\"city\")", "dependencies": [], "value": "Pleasant Hill"}}, "G6": {"formula": {"text": "=LIST(\"1\",\"5\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A7": {"formula": {"text": "=LIST(\"1\",\"6\",\"display_name\")", "dependencies": [], "value": "<PERSON><PERSON>, <PERSON>"}}, "B7": {"formula": {"text": "=LIST(\"1\",\"6\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C7": {"formula": {"text": "=LIST(\"1\",\"6\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D7": {"formula": {"text": "=LIST(\"1\",\"6\",\"user_id\")", "dependencies": [], "value": ""}}, "E7": {"formula": {"text": "=LIST(\"1\",\"6\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F7": {"formula": {"text": "=LIST(\"1\",\"6\",\"city\")", "dependencies": [], "value": "Pleasant Hill"}}, "G7": {"formula": {"text": "=LIST(\"1\",\"6\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A8": {"formula": {"text": "=LIST(\"1\",\"7\",\"display_name\")", "dependencies": [], "value": "<PERSON><PERSON>, <PERSON>"}}, "B8": {"formula": {"text": "=LIST(\"1\",\"7\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C8": {"formula": {"text": "=LIST(\"1\",\"7\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D8": {"formula": {"text": "=LIST(\"1\",\"7\",\"user_id\")", "dependencies": [], "value": ""}}, "E8": {"formula": {"text": "=LIST(\"1\",\"7\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F8": {"formula": {"text": "=LIST(\"1\",\"7\",\"city\")", "dependencies": [], "value": "Pleasant Hill"}}, "G8": {"formula": {"text": "=LIST(\"1\",\"7\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A9": {"formula": {"text": "=LIST(\"1\",\"8\",\"display_name\")", "dependencies": [], "value": "<PERSON><PERSON>, <PERSON>"}}, "B9": {"formula": {"text": "=LIST(\"1\",\"8\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C9": {"formula": {"text": "=LIST(\"1\",\"8\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D9": {"formula": {"text": "=LIST(\"1\",\"8\",\"user_id\")", "dependencies": [], "value": ""}}, "E9": {"formula": {"text": "=LIST(\"1\",\"8\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F9": {"formula": {"text": "=LIST(\"1\",\"8\",\"city\")", "dependencies": [], "value": "Pleasant Hill"}}, "G9": {"formula": {"text": "=LIST(\"1\",\"8\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A10": {"formula": {"text": "=LIST(\"1\",\"9\",\"display_name\")", "dependencies": [], "value": "Gemini Furniture"}}, "B10": {"formula": {"text": "=LIST(\"1\",\"9\",\"phone\")", "dependencies": [], "value": "****** 349 2324"}}, "C10": {"formula": {"text": "=LIST(\"1\",\"9\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D10": {"formula": {"text": "=LIST(\"1\",\"9\",\"user_id\")", "dependencies": [], "value": ""}}, "E10": {"formula": {"text": "=LIST(\"1\",\"9\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F10": {"formula": {"text": "=LIST(\"1\",\"9\",\"city\")", "dependencies": [], "value": "Fairfield"}}, "G10": {"formula": {"text": "=LIST(\"1\",\"9\",\"country_id\")", "dependencies": [], "value": "United States"}}, "A11": {"formula": {"text": "=LIST(\"1\",\"10\",\"display_name\")", "dependencies": [], "value": "Gemini Furniture, <PERSON>"}}, "B11": {"formula": {"text": "=LIST(\"1\",\"10\",\"phone\")", "dependencies": [], "value": "(*************"}}, "C11": {"formula": {"text": "=LIST(\"1\",\"10\",\"email\")", "dependencies": [], "value": "<EMAIL>"}}, "D11": {"formula": {"text": "=LIST(\"1\",\"10\",\"user_id\")", "dependencies": [], "value": ""}}, "E11": {"formula": {"text": "=LIST(\"1\",\"10\",\"activity_ids\")", "dependencies": [], "value": ""}}, "F11": {"formula": {"text": "=LIST(\"1\",\"10\",\"city\")", "dependencies": [], "value": "Fairfield"}}, "G11": {"formula": {"text": "=LIST(\"1\",\"10\",\"country_id\")", "dependencies": [], "value": "United States"}}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true}], "entities": {}, "styles": {"1": {"fillColor": "#f2f2f2"}, "2": {"fillColor": "#f2f2f2", "bold": true}, "3": {"fillColor": "#f2f2f2", "textColor": "#756f6f"}}, "borders": {}, "revisionId": "55c2d489-2d19-4896-a253-e350830d2021", "pivots": {"1": {"model": "res.partner", "rowGroupBys": ["parent_id"], "colGroupBys": ["user_id", "country_id"], "measures": [{"field": "__count", "operator": "sum"}], "domain": [], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "default_is_company": true}, "id": 1}}, "lists": {"1": {"model": "res.partner", "domain": [], "orderBy": [], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "allowed_company_ids": [1], "default_is_company": true}, "columns": ["display_name", "phone", "email", "user_id", "activity_ids", "city", "country_id"], "id": "1"}}, "globalFilters": [{"id": "de5794c5-75fe-4f74-b9cb-6c59937a2c69", "label": "MyFilter1", "type": "relation", "rangeType": "year", "listFields": {"1": {"field": "user_ids", "type": "many2many"}}, "defaultValue": [2], "modelName": "res.users", "pivotFields": {"1": {"field": "user_ids", "type": "many2many"}}}]}