<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.CallParticipantCard" owl="1">
        <t t-if="callParticipantCard">
            <div class="o_CallParticipantCard position-relative cursor-pointer d-flex flex-column align-items-center justify-content-center mh-100 mw-100 p-1 rounded-1"
                t-att-class="{
                    'o-isTalking': !callParticipantCard.isMinimized and callParticipantCard.isTalking,
                    'o-isInvitation opacity-50': !callParticipantCard.rtcSession,
                }"
                t-att-title="callParticipantCard.channelMember.persona.name"
                t-att-aria-label="callParticipantCard.channelMember.persona.name"
                t-attf-class="{{ className }}"
                t-on-click="callParticipantCard.onClick"
                t-on-contextmenu="callParticipantCard.onContextMenu"
                t-ref="root"
            >
                <!-- card -->
                <t t-if="callParticipantCard.callParticipantVideoView">
                    <CallParticipantVideo record="callParticipantCard.callParticipantVideoView"/>
                </t>
                <t t-else="">
                    <div class="o_CallParticipantCard_avatarFrame d-flex align-items-center justify-content-center h-100 w-100 rounded-1" t-att-class="{ 'o-isMinimized': callParticipantCard.isMinimized }" draggable="false">
                        <img alt="Avatar"
                             t-att-class="{
                                'o-isTalking': callParticipantCard.isTalking,
                                'o-isInvitation': !callParticipantCard.rtcSession,
                             }"
                             class="o_CallParticipantCard_avatarImage h-100 rounded-circle border-5 o_object_fit_cover"
                             t-att-src="callParticipantCard.channelMember.avatarUrl"
                             draggable="false"
                        />
                    </div>
                </t>

                <t t-if="callParticipantCard.rtcSession">
                    <!-- overlay -->
                    <span class="o_CallParticipantCard_overlay o_CallParticipantCard_overlayBottom position-absolute bottom-0 start-0 d-flex overflow-hidden">
                        <t t-if="!callParticipantCard.isMinimized">
                            <span class="o_CallParticipantCard_name p-1 rounded-1 bg-black-75 text-truncate" t-esc="callParticipantCard.channelMember.persona.name"/>
                        </t>
                        <t t-if="callParticipantCard.rtcSession.isScreenSharingOn and callParticipantCard.isMinimized and !callParticipantCard.rtcSession.channel.rtc">
                            <small class="o_CallParticipantCard_liveIndicator o-isMinimized rounded-pill text-bg-danger d-flex align-items-center fw-bolder" title="live" aria-label="live">
                                LIVE
                            </small>
                        </t>
                    </span>
                    <div class="o_CallParticipantCard_overlay o_CallParticipantCard_overlayTop position-absolute top-0 end-0 d-flex flex-row-reverse">
                        <t t-if="callParticipantCard.rtcSession.isSelfMuted and !callParticipantCard.rtcSession.isDeaf">
                            <span class="o_CallParticipantCard_overlayTopElement d-flex flex-column justify-content-center me-1 rounded-circle bg-900" t-att-class="{'o-isMinimized p-1': callParticipantCard.isMinimized, 'p-2': !callParticipantCard.isMinimized }" title="muted" aria-label="muted">
                                <i class="fa fa-microphone-slash"/>
                            </span>
                        </t>
                        <t t-if="callParticipantCard.rtcSession.isDeaf">
                            <span class="o_CallParticipantCard_overlayTopElement d-flex flex-column justify-content-center me-1 rounded-circle bg-900" t-att-class="{'o-isMinimized p-1': callParticipantCard.isMinimized, 'p-2': !callParticipantCard.isMinimized }" title="deaf" aria-label="deaf">
                                <i class="fa fa-deaf"/>
                            </span>
                        </t>
                        <t t-if="callParticipantCard.rtcSession.channel.rtc and callParticipantCard.rtcSession.isAudioInError">
                            <span class="o_CallParticipantCard_overlayTopElement d-flex flex-column justify-content-center me-1 p-2 rounded-circle bg-900 text-danger" title="Issue with audio">
                                <i class="fa fa-exclamation-triangle"/>
                            </span>
                        </t>
                        <t t-if="callParticipantCard.rtcSession.channel.rtc and !callParticipantCard.rtcSession.rtcAsCurrentSession and !['connected', 'completed'].includes(callParticipantCard.rtcSession.connectionState)">
                            <span class="o_CallParticipantCard_overlayTopElement d-flex flex-column justify-content-center me-1 p-2 rounded-circle bg-900" t-att-title="callParticipantCard.rtcSession.connectionState">
                                <i class="fa fa-exclamation-triangle o_CallParticipantCard_connectionState text-warning"/>
                            </span>
                        </t>
                        <t t-if="callParticipantCard.rtcSession.isScreenSharingOn and !callParticipantCard.isMinimized and !callParticipantCard.rtcSession.channel.rtc">
                            <span class="o_CallParticipantCard_liveIndicator rounded-pill text-bg-danger d-flex align-items-center me-1 fw-bolder" title="live" aria-label="live">
                                LIVE
                            </span>
                        </t>
                    </div>

                    <!-- volume popover -->
                    <t t-if="!callParticipantCard.rtcSession.isOwnSession">
                        <i class="o_CallParticipantCard_volumeMenuAnchor position-absolute bottom-0 start-50" t-on-click="callParticipantCard.onClickVolumeAnchor" t-ref="volumeMenuAnchor"/>
                    </t>
                </t>
            </div>
        </t>
    </t>

</templates>
