# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty_taxcloud
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_loyalty_taxcloud
#: code:addons/sale_loyalty_taxcloud/models/sale_order.py:0
#, python-format
msgid ""
"Any order that has discounts and uses TaxCloud must be invoiced all at once to prevent faulty tax computation with Taxcloud.\n"
"The following orders must be completely invoiced:\n"
"%s"
msgstr ""
"任何有折扣並使用 TaxCloud 的訂單，都必須同一時間開立發票，以防止 TaxCloud 計稅時出錯。\n"
"以下訂單必須完整開立發票：\n"
"%s"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_account_move_line__reward_id
msgid "Discount reward"
msgstr "折扣獎賞"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "日記帳分錄"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "日記帳項目"

#. module: sale_loyalty_taxcloud
#: code:addons/sale_loyalty_taxcloud/models/sale_order_line.py:0
#, python-format
msgid ""
"Orders with coupons or promotions programs that use TaxCloud for automatic tax computation cannot be modified after having been invoiced.\n"
"To modify this order, you must first cancel or refund all existing invoices."
msgstr ""
"使用 TaxCloud 自動計稅並套用優惠券或促銷計劃的訂單，在開立發票後不可修改。\n"
"若要修改此訂單，必須先取消或退款給所有現有發票。"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單明細"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_account_move_line__price_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_sale_order_line__price_taxcloud
msgid "Taxcloud Price"
msgstr "TaxCloud 價錢"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,help:sale_loyalty_taxcloud.field_account_move_line__reward_id
msgid "The loyalty reward that created this line."
msgstr "建立此資料行的會員獎賞。"
