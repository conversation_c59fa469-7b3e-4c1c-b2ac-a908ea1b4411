# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_coda
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""
"\n"
"Opname van bewegingsgegevens van type 2.%s zijn niet ondersteund"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "1-st (recurrent)"
msgstr "1-st (recurrent)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "ATM/POS debit"
msgstr "ATM/POS debet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Access right to database"
msgstr "Toegangsrecht tot databank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Agio on supplier's bill"
msgstr "Agio's op leverancierwissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the bank"
msgstr "Bedrag getotaliseerd door de bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the customer"
msgstr "Bedrag getotaliseerd door de cliënt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Avgas"
msgstr "Avgas"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bancontact/Mister Cash"
msgstr "Bancontact/Mister Cash"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Bankafschrift"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bank confirmation to revisor or accountant"
msgstr "Bankbevestiging aan revisor of accountant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bill claimed back"
msgstr "Teruggevraagde wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bills - calculation of interest"
msgstr "Handelswissels"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr "CODA notities"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""
"CODA V%s afschriften zijn niet ondersteund, gelieve uw bank te contacteren."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA parseerfout op gegevensopname informatie 3.2, volgnr %s.\n"
"Rapporteer aub dit probleem via uw Odoo ondersteuningskanaal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA parseerfout op gegevensopname informatie 3.3, volgnr %s.\n"
"Rapporteer dit probleem aub via het Odoo ondersteuningskanaal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA parseerfout op opname van bewegingsgegevens 2.2, volgnr %s.\n"
"Rapporteer dit probleem via je Odoo ondersteuningskanaal aub."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"CODA parseerfout op opname bewegingsgegevens 2.3, volgnr %s.\n"
"Rapporteer aub dit probleem via uw Odoo ondersteuningskanaal."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cancellation or correction"
msgstr "Annulering of correctie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Capital and/or interest term investment"
msgstr "Kapitaal en/of intrest termijnbelegging"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cards"
msgstr "Kaarten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash deposit at an ATM"
msgstr "Geldstorting aan een automaat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash payment"
msgstr "Storting in contanten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal"
msgstr "Geldopneming"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal by your branch or agents"
msgstr "Geldopneming door uw bijhuis of agenten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal from an ATM"
msgstr "Geldopneming via automaat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charge for safe custody"
msgstr "Bewaarloon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charging fees for transactions"
msgstr "Tarifering van verrichtingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheque-related costs"
msgstr "Kosten i.v.m. cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheques"
msgstr "Cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing"
msgstr "Sluiten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing (periodical settlements for interest, costs,...)"
msgstr "Afsluiting (periodieke afrekeningen van intresten, kosten,...)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Codes proper to each bank"
msgstr "Codes eigen aan elke bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective payments of wages"
msgstr "Collectieve loonbetalingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfer"
msgstr "Collectieve overschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfers"
msgstr "Collectieve overschrijvingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial bills"
msgstr "Handelswissels"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial paper claimed back"
msgstr "Teruggevraagd handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication"
msgstr "Kenmerk"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication: "
msgstr "Mededeling:"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communicaton"
msgstr "Mededeling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Compensation for missing coupon"
msgstr "Schadevergoeding ontbrekende coupon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Correction for prepaid card"
msgstr "Correctie voor voorafbetaalde kaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs"
msgstr "Kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for holding a documentary cash credit"
msgstr "Kosten voor het aanhouden van een kaskrediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for opening a bank guarantee"
msgstr "Openingskost bankgarantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for the safe custody of correspondence"
msgstr "Kosten voor bewaring van briefwisseling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs related to commercial paper"
msgstr "Kosten i.v.m. handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to electronic output"
msgstr "Kosten voor elektronische output"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to incoming foreign and non-SEPA transfers"
msgstr "Kosten op inkomende internationale en non-sepa overschrijvingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to outgoing foreign transfers and non-SEPA transfers"
msgstr "Kosten op uitgaande internationale en non-sepa overschrijvingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to payment of foreign cheques"
msgstr "Kosten op betaling van buitenlandse cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to the payment of a foreign bill"
msgstr "Kosten op de betaling van buitelandse wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party"
msgstr "Tegenpartij"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Account"
msgstr "Tegenpartij rekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Address"
msgstr "Tegenpartij adres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter transactions"
msgstr "Loketverrichtingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Country code of the principal"
msgstr "Landcode van de opdrachtgever"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit"
msgstr "Credit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after Proton payments"
msgstr "Credit van betalingen via Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after a payment at a terminal"
msgstr "Credit van een betaling via terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after collection"
msgstr "Credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit under usual reserve"
msgstr "Credit onder gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit-related costs"
msgstr "Kosten i.v.m. kredieten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Creditor’s identification code"
msgstr "Identificatiecode van de schuldeiser"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Currency"
msgstr "Valuta"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Damage relating to bills and cheques"
msgstr "Schade bij wissels en cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Department store cheque"
msgstr "warenhuischeque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail"
msgstr "Detail"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the bank"
msgstr "Detail van het bedrag getotaliseerd door de bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the customer"
msgstr "Detail van het bedrag getotaliseerd door de cliënt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Simple amount with detailed data"
msgstr "Detail van enkelvoudig bedrag met details"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Difference in payment"
msgstr "Verschil in storting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct Debit scheme"
msgstr "Direct Debit scheme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct debit"
msgstr "Direct debit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount abroad"
msgstr "Disconto buitenland"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount foreign supplier's bills"
msgstr "Disconto buitenlandse leverancierwissels"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary credit charges"
msgstr "Kosten documentair krediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary export credits"
msgstr "Documentaire kredieten export"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary import credits"
msgstr "Documentaire kredieten import"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic commercial paper"
msgstr "Binnenlands handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic or local SEPA credit transfers"
msgstr "Binnenlandse/lokale overschrijvingen - SEPA direct transfers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Download of prepaid card"
msgstr "Ontladen van voorafbetaalde kaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Drawing up a certificate"
msgstr "Aanmaken attest"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Equivalent in EUR"
msgstr "Tegenwaarde in EUR"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Error"
msgstr "Fout"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension"
msgstr "Extensie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension of maturity date"
msgstr "Verlenging van de vervaldag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fees and commissions"
msgstr "Kosten en provisies"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation"
msgstr "Financiële centralisatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (credit)"
msgstr "Financiële centralisatie (credit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (debit)"
msgstr "Financiële centralisatie (debit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralization"
msgstr "Financiële centralisatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"First credit of cheques, vouchers, luncheon vouchers, postal orders, credit "
"under usual reserve"
msgstr ""
"Eerste credit van cheques, bons, maaltijdbons, postassignaties, credit onder"
" gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – capital and interest"
msgstr "Vast voorschot - kapitaal en rente"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – interest only"
msgstr "Vast voorschot - enkel rente"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr ""
"Buitenlandse bankrekeningen met een BBAN structuur worden niet ondersteund."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr "Buitenlandse bankaccounts met IBAN structuur zijn niet ondersteund."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign cheques"
msgstr "Buitenlandse cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign commercial paper"
msgstr "Buitenlands handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward purchase of foreign exchange"
msgstr "Aankoop van deviezen op termijn"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward sale of foreign exchange"
msgstr "Verkoop van deviezen op termijn"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the currency of the account"
msgstr "Brutobedrag in de munt van de rekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the original currency"
msgstr "Brutobedrag in oorspronkelijke munt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Handling costs instalment credit"
msgstr "Dossierkosten afbetalingskrediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Idem without guarantee"
msgstr "Idem zonder aval"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Income from payments by GSM"
msgstr "Ontvangsten betalingen met GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order"
msgstr "Enkelvoudige overschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order initiated by the bank"
msgstr "Enkelvoudige overschrijving geïnitieerd door de bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Information charges"
msgstr "Inlichtingskosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Instant SEPA credit transfer"
msgstr "Instant SEPA overschrijvingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Insurance costs"
msgstr "Verzekeringskosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interest term investment"
msgstr "Interest termijnbelegging"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interim interest on subscription"
msgstr "Tussentijdse interesten bij inschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "International credit transfers - non-SEPA credit transfers"
msgstr "Internationale overschrijvingen - non-SEPA credit transfers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Issues"
msgstr "Emissies"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "LPG"
msgstr "LPG"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading GSM cards"
msgstr "Opladen van GSM kaarten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading Proton"
msgstr "Opladen Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading a GSM card"
msgstr "Opladen van GSM kaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Long-term loan"
msgstr "Lening op lange termijn"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Maestro"
msgstr "Maestro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management fee"
msgstr "Commissie beheersloon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management/custody"
msgstr "Beheer/bewaring"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Mandate reference"
msgstr "Mandaat referentie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Masked PAN or card number"
msgstr "Gemaskeerde PAN of kaartnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Method of calculation (VAT, withholding tax on income, commission, etc.)"
msgstr "Berekeningswijze (BTW, roerende voorheffing, provisie, enz.)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Miscellaneous fees and commissions"
msgstr "Diverse kosten en provisies"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Name: {name}, Town: {city}"
msgstr "Naam: {name}, Lokaliteit: {city}"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Night safe"
msgstr "Nachtkluis"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "No date"
msgstr "Geen datum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Non-presented circular cheque"
msgstr "Niet aangeboden circulaire cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Number of the credit card"
msgstr "Nummer van de kredietkaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Original amount of the transaction"
msgstr "Oorspronkelijk bedrag van de verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other"
msgstr "Overige"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other credit applications"
msgstr "Overige kredietvorderingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "PAN or card number"
msgstr "PAN of kaartnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit - individual transaction"
msgstr "Credit POS - individuele verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit – Globalisation"
msgstr "Credit POS - Globalisatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS number"
msgstr "POS-nummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS others"
msgstr "POS andere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Paid or reason for refused payment"
msgstr "Betaald of reden voor geweigerde betaling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Partial payment subscription"
msgstr "Gedeeltelijke storting inschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Participation in and management of interest refund system"
msgstr "Toetreding en beheer renteristornosysteem"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Pay-packet charges"
msgstr "Kosten loonzakje"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payable coupons/repayable securities"
msgstr "Betaalbare coupons/terugbetaalbare effecten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment"
msgstr "Betaling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by GSM"
msgstr "Betaling met GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card outside the Eurozone"
msgstr "Betaling met debetkaart buiten eurozone"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card within the Eurozone"
msgstr "Betaling met debetkaart binnen eurozone"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by your branch/agents"
msgstr "Storting door uw bijhuis/agenten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment commercial paper"
msgstr "Betaling handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment documents abroad"
msgstr "Betaling documenten buitenland"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in advance"
msgstr "Vervroegde betaling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in your favour"
msgstr "Storting te uwen gunste"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment night safe"
msgstr "Storting nachtkluis"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of a foreign cheque"
msgstr "Betaling buitenlandse cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Payment of coupons from a deposit or settlement of coupons delivered over "
"the counter - credit under usual reserve"
msgstr ""
"Betaling van coupons afkomstig uit bewaargeving of afgegeven aan het loket -"
" credit ogv"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of foreign bill"
msgstr "Betaling buitenlandse wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of voucher"
msgstr "Betaling van bon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of wages, etc."
msgstr "Betaling lonen, e.d."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of your cheque"
msgstr "Betaling van uw cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment with tank card"
msgstr "Betaling met tankkaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Postage"
msgstr "Portkosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Printing of forms"
msgstr "Aanmaak formulieren"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Private"
msgstr "Privé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Provisionally unpaid"
msgstr "Voorlopig onbetaald"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of Smartcard"
msgstr "Aankoop smartcard"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of an international bank cheque"
msgstr "Aankoop internationale bankcheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of fiscal stamps"
msgstr "Aankoop fiscale zegels"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of foreign bank notes"
msgstr "Aankoop deviezen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of gold/pieces"
msgstr "Aankoop goud/penningen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of petrol coupons"
msgstr "Aankoop benzinebons"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of securities"
msgstr "Aankoop effecten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of traveller’s cheque"
msgstr "Aankoop reischeque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Rate"
msgstr "Wisselkoers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reason"
msgstr "Reden"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Registering compensation for savings accounts"
msgstr "Klasseervergoeding voor spaarrekeningen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Regularisation costs"
msgstr "Regularisatiekosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement"
msgstr "Vergoeden"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of cheque-related costs"
msgstr "Terugbetaling van kosten i.v.m. cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of costs"
msgstr "Terugbetaling van kosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheque by your branch - credit under usual reserve"
msgstr "Remise cheque door uw bijhuis - credit onder gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheques, vouchers, etc. credit after collection"
msgstr "Remise cheques, bons, enz. credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit after collection"
msgstr "Remise handelspapier - credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit under usual reserve"
msgstr "Remise handelspapier - credit onder gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper for discount"
msgstr "Remise handelspapier ter disconto"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit after collection"
msgstr "Remise buitenlandse documenten Credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit under usual reserve"
msgstr "Remise documenten buitenland Credit ogv"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit after collection"
msgstr "Remise buitelandse wissel credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit under usual reserve"
msgstr "Remise buitenlandse wissel credit ogv"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit after collection"
msgstr "Remise buitenlandse cheque credit na inning"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit under usual reserve"
msgstr "Remise buitenlandse cheque credit onder gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of guaranteed foreign supplier's bill"
msgstr "Remise buitenlandse leverancierswissel met aval"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill with guarantee"
msgstr "Remise leverancierswissel met aval"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill without guarantee"
msgstr "Remise leverancierswissel zonder aval"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of direct debit box"
msgstr "Huur domiciliëringsbus"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of safes"
msgstr "Safehuur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Repayable securities from a deposit or delivered at the counter - credit "
"under usual reserve"
msgstr ""
"Terugbetaalbare effecten afkomstig uit bewaargeving of afgegeven aan het "
"loket - credit ogv"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Research costs"
msgstr "Opzoekingskosten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Retrocession of issue commission"
msgstr "Retrocessie provisie inschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Return of an irregular bill of exchange"
msgstr "Terugzending onregelmatige wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal"
msgstr "Terugboeking"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheque"
msgstr "Terugboeking cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheques"
msgstr "Terugboeking cheques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of voucher"
msgstr "Terugboeking bon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA B2B"
msgstr "SEPA B2B"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA Direct Debit"
msgstr "SEPA automatische incasso"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA core"
msgstr "SEPA core"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of foreign bank notes"
msgstr "Verkoop deviezen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of gold/pieces under usual reserve"
msgstr "Verkoop goud/penningen onder gewoon voorbehoud"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of securities"
msgstr "Verkoop van effecten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of traveller’s cheque"
msgstr "Verkoop reischeque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Second credit of unpaid cheque"
msgstr "Tweede creditering van onbetaald gestelde cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Securities"
msgstr "Beveiliging"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Separately charged costs and provisions"
msgstr "Los aangerekende kosten en provisies"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement Date"
msgstr "Settlement Date"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement credit cards"
msgstr "Afrekening kredietkaarten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of bank acceptances"
msgstr "Vereffening bankaccepten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of discount bank acceptance"
msgstr "Afrekening disconto bankaccept"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of fixed advance"
msgstr "Aanzuivering vast voorschot"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of instalment credit"
msgstr "Uitbetaling afbetalingskrediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of mortgage loan"
msgstr "Vereffening hypothecaire lening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of securities"
msgstr "Liquidatie effecten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Share option plan – exercising an option"
msgstr "Aandelen optieplan - uitoefening optie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Short-term loan"
msgstr "Lening op korte termijn"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount with detailed data"
msgstr "Enkelvoudig bedrag met details"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount without detailed data"
msgstr "Enkelvoudig bedrag zonder details"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Special charge for safe custody"
msgstr "Bijzonder bewaarloon"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split Transactions"
msgstr "Transactie splitsen"

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split collective payments for CODA files"
msgstr "Gesplitste collectieve betalingen voor CODA-bestanden"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot purchase of foreign exchange"
msgstr "Aankoop van deviezen contant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot sale of foreign exchange"
msgstr "Verkoop van deviezen contant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Standing order"
msgstr "Doorlopende opdracht"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Structured format communication"
msgstr "Formaat gestructureerde mededeling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription fee"
msgstr "Abonnementsgeld"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription to securities"
msgstr "Intekening op effecten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subsidy"
msgstr "Subsidie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Surety fee"
msgstr "Borgstellingsprovisie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "TINA"
msgstr "TINA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tender"
msgstr "Aanbesteding"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tenders"
msgstr "Aanbestedingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term Investments"
msgstr "Termijnbeleggingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term loan"
msgstr "Lening op termijn"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Terminal cash deposit"
msgstr "Storting aan terminaal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Trade information"
msgstr "Handelsinlichtingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer"
msgstr "Verplaatsing"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer from your account"
msgstr "Overschrijving van uw rekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour"
msgstr "Overschrijving te uwen gunste"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour – initiated by the bank"
msgstr "Overschrijving te uwen gunste - geïnitieerd door de bank"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer to your account"
msgstr "Overschrijving naar uw rekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Travel insurance premium"
msgstr "Premie reisbijstandsverzekering"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type Direct Debit"
msgstr "Type Direct Debit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of R transaction"
msgstr "Type R transactie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of structured communication not supported: "
msgstr "Type van niet-ondersteunde gestructureerde mededeling:"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Undefined transaction"
msgstr "Niet gedefinieerde verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable reimbursement"
msgstr "Onuitvoerbare terugbetaling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable transfer order"
msgstr "Onuitvoerbare overschrijving"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unloading Proton"
msgstr "Ontlading Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid commercial paper"
msgstr "Onbetaald handelspapier"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid debt"
msgstr "Onbetaalde invordering"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign bill"
msgstr "Onbetaalde buitenlandse wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign cheque"
msgstr "Onbetaalde buitenlandse cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid postal order"
msgstr "Niet uitbetaalde postassignatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid voucher"
msgstr "Onbetaalde bon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unsupported bank account structure "
msgstr "Niet ondersteunde bankaccount structuur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Upload of prepaid card"
msgstr "Opladen van voorafbetaalde kaart"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Value (date) correction"
msgstr "Correctie valutering"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Various transactions"
msgstr "Diverse verrichtingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant"
msgstr "Warrant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant fallen due"
msgstr "Vervallen warrant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Writ service fee"
msgstr "Kosten gerechtsdeurwaarder"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Wrong CODA code"
msgstr "Verkeerde CODA-code"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your certified cheque"
msgstr "Uw gecertificeerde cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue"
msgstr "Uw emissie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue circular cheque"
msgstr "Uw emissie circulaire cheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your purchase bank cheque"
msgstr "Aanmaak bankcheque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment hire-purchase and similar claims"
msgstr "Uw terugbetaling leasing en soortgelijke vorderingen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment instalment credits"
msgstr "Uw terugbetaling afbetalingskredieten"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment mortgage loan"
msgstr "Uw terugbetaling hypothecaire lening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repurchase of issue"
msgstr "Uw terugkoop emissie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "account number of the credit"
msgstr "rekeningnummer van het krediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount (equivalent in foreign currency)"
msgstr "bedrag (tegenwaarde in devies)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of interest"
msgstr "bedrag rente"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of the bill"
msgstr "bedrag van de wisse"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount on which % is calculated"
msgstr "bedrag waarop % wordt berekend"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount"
msgstr "basisbedrag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount of the calculation"
msgstr "basisbedrag van de berekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cancellation"
msgstr "Annulering"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "card scheme"
msgstr "kaartschema"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "company number"
msgstr "ondernemingsnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conformity code or blank"
msgstr "code conformiteit of blanco"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conventional maturity date"
msgstr "conventionele vervaldag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative"
msgstr "cumul"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative on network"
msgstr "cumul op netwerk"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "currency"
msgstr "munteenheid"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date"
msgstr "datum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of first transaction"
msgstr "datum eerste verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of issue of the bill"
msgstr "datum van afgifte van de wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of last transaction"
msgstr "datum laatste verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of transaction"
msgstr "datum verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor disagrees"
msgstr "schuldenaar niet akkoord"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor’s account problem"
msgstr "probleem met rekening schuldenaar"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit amount"
msgstr "bedrag deposito"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit number"
msgstr "nummer deposito"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "diesel"
msgstr "diesel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "distribution sector"
msgstr "distributiecentrum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "domestic fuel oil"
msgstr "huisbrandolie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "end date"
msgstr "einddatum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in EUR"
msgstr "tegenwaarde in EUR"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in the currency of the account"
msgstr "tegenwaarde in de munt van de rekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "europremium"
msgstr "eurosuper"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "exchange rate"
msgstr "wisselkoers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "extension zone of account number of the credit"
msgstr "uitbreidingszone rekeningnummer van het krediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "fuel"
msgstr "brandstof"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "guarantee number (no. allocated by the bank)"
msgstr "borgstellingsnummer (nummer toegekend door de bank)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of payment"
msgstr "uur van storting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of transaction"
msgstr "uur van verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification number"
msgstr "identificatienummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification of terminal"
msgstr "identificatie van terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest"
msgstr "rente"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rate"
msgstr "minimum van toepassing"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rates, calculation basis"
msgstr "rentegetallen, berekeningsbasis"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "invoice number"
msgstr "factuurnummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "issuing institution"
msgstr "Uitgevende instelling"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "last (recurrent)"
msgstr "last (recurrent)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "lubricants"
msgstr "smeermiddelen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date"
msgstr "vervaldag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date of the bill"
msgstr "vervaldag van de wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "message (structured of free)"
msgstr "mededeling (gestructureerd of vrij)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum"
msgstr "minimum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum applicable"
msgstr "minimum van toepassing"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum not applicable"
msgstr "minimum niet van toepassing"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum rate"
msgstr "minimumtarief"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "new balance of the credit"
msgstr "nieuw saldo van het krediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "nominal interest rate or rate of charge"
msgstr "nominale rentevoet of lastenpercentage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of days"
msgstr "aantal dagen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of the bill"
msgstr "nummer van de wissel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "old balance of the credit"
msgstr "oud saldo van het krediet"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "one-off"
msgstr "one-off"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount"
msgstr "oorspronkelijk bedrag"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount (given by the customer)"
msgstr "oorspronkelijk bedrag (opgegeven door de cliënt)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "other types"
msgstr "andere"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "paid"
msgstr "betaald"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "payment day"
msgstr "stortingsdatum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percent"
msgstr "percent"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percentage"
msgstr "percentage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period from {} to {}"
msgstr "periode van {} tot {}"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period number"
msgstr "periodenummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "petrol"
msgstr "petroleum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium 99+"
msgstr "super 99+"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium plus 98 oct"
msgstr "super plus 98 oct"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium with lead substitute"
msgstr "super met loodsubstituut"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "product code"
msgstr "productcode"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "rate"
msgstr "koers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reason not specified"
msgstr "reden niet bepaald"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "recurrent"
msgstr "recurrent"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction"
msgstr "referte verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction on credit account"
msgstr "referte verrichting op kredietrekening"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "refund"
msgstr "credit factuur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "regular unleaded"
msgstr "normaal loodvrij"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reject"
msgstr "onuitvoerbaar"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "return"
msgstr "terug"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal"
msgstr "rechtzetting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal of purchases"
msgstr "terugboeking aankopen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of first transaction"
msgstr "volgnummer eerste verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of last transaction"
msgstr "volgnummer laatste verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of transaction"
msgstr "volgnummer verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of validation"
msgstr "volgnummer validatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "starting date"
msgstr "aanvangsdatum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "technical problem"
msgstr "technisch probleem"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "teledata"
msgstr "teledata"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "term in days"
msgstr "termijn in dagen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "terminal number"
msgstr "terminal nummer"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "transaction type"
msgstr "soort verrichting"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "undefined"
msgstr "Niet gedefinieerd"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unit price"
msgstr "eenheidsprijs"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unset"
msgstr "terugstellen"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unspecified"
msgstr "niet bepaald"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "validation date"
msgstr "datum van validatie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "volume"
msgstr "volume"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "withdrawal"
msgstr "afhaling"
