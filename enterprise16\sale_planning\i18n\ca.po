# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_planning
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON>, 2022
# j<PERSON><PERSON><PERSON>, 2022
# jabiri7, 2022
# Ó<PERSON>r Fonseca <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Óscar Fonseca <<EMAIL>>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        as\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        com\n"
"                    </span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Planned</span>"
msgstr "<span class=\"o_stat_text\">Planificat</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">To plan</span>"
msgstr "<span class=\"o_stat_text\">Per planificar</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span>Hours</span>"
msgstr "<span>Hores</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
msgid "<strong>Sales Order Item  — </strong>"
msgstr "<strong>Article de comanda de venda — </strong>"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__allocated_hours
msgid "Allocated Time"
msgstr "Temps assignat"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Billable"
msgstr "Facturable"

#. module: sale_planning
#: model:ir.ui.menu,name:sale_planning.sale_planning_menu_schedule_by_sale_order
msgid "By Sales Order"
msgstr "Per comanda de venda"

#. module: sale_planning
#: model:product.template,name:sale_planning.developer_product_product_template
msgid "Developer (Plan services)"
msgstr "Desenvolupador (Planificar serveis)"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__end_datetime
msgid "End Date"
msgstr "Data de finalització"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_tester
msgid "Functional Tester"
msgstr "Tester funcional"

#. module: sale_planning
#: model:product.template,name:sale_planning.technical_maintainance_product_product_template
msgid "IT Technical Maintenance (Plan services)"
msgstr "Manteniment tècnic informàtic (Planificar serveis)"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_technician
msgid "IT Technician"
msgstr "Tècnic informàtic"

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,help:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_planning.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order. Only employees with this role will "
"automatically be assigned shifts for Sales Orders containing this service."
msgstr ""
"En cas que estigui activat, quan es confirmi la comanda de venda es generarà"
" automàticament un torn per al rol seleccionat. Només els empleats amb "
"aquest rol se'ls assignaran torns per a la comanda de venda que conté aquest"
" servei."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "My Sales Orders"
msgstr "Les meves comandes de venda"

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "No shifts found. Let's create one!"
msgstr "No s'ha trobat cap torn. Creem-ne un!"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Non Billable"
msgstr "No facturable"

#. module: sale_planning
#: model:ir.model.constraint,message:sale_planning.constraint_planning_slot_check_datetimes_set_or_plannable_slot
msgid ""
"Only slots linked to a sale order with a plannable service can be "
"unscheduled."
msgstr ""
"Només els slots vinculats a una comanda de venda amb un servei planificable "
"poden ser no programats."

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.planning_action_orders_planned
msgid "Orders Planned"
msgstr "Comandes planificades"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.developer_product_product_template
msgid "Our developer will help you to add new features to your Software."
msgstr ""
"El nostre desenvolupador us ajudarà a afegir noves funcions al vostre "
"programari."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Plan Orders"
msgstr "Planificar comandes"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_enabled
msgid "Plan Services"
msgstr "Planificar serveis"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid ""
"Plannable services should be a service product, product\n"
"%s."
msgstr ""
"Els serveis planificables han de ser un producte de servei, un producte\n"
"%s."

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid "Plannable services should use an UoM within the %s category."
msgstr ""
"Els serveis planificables haurien d'utilitzar una UdM dins de la categoria "
"%s."

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Informe d'anàlisi de planificació"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_first_sale_line_id
msgid "Planning First Sale Line"
msgstr "Planificació de la primera línia de venda"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_planned
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_planned
msgid "Planning Hours Planned"
msgstr "Horari de planificació previst"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_to_plan
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_to_plan
msgid "Planning Hours To Plan"
msgstr "Planificació de les hores per planificar"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_initial_date
msgid "Planning Initial Date"
msgstr "Data inicial de planificació"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_role
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_role_id
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_role_id
msgid "Planning Role"
msgstr "Planificació de rol"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_slot
msgid "Planning Shift"
msgstr "Torn del planning"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_slot_ids
msgid "Planning Slot"
msgstr "Slot de planificació"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_product_template
msgid "Product"
msgstr "Producte"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__role_product_ids
msgid "Role Product"
msgstr "Funció del producte"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_order_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Sales Order"
msgstr "Comanda"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_line_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_planning.period_report_template
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.resource_sale_planning
msgid "Sales Order Item"
msgstr "Element de la comanda de venda"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""
"L'element de la comanda de venda pel qual aquest torn es durà a terme. Quan "
"les comandes de vendes es planegen automàticament, tant les hores restants "
"de l'element de la comanda com el rol definit al servei es tenen en compte."

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "Schedule by Sales Order"
msgstr "Programar per comanda de venda"

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Programa els recursos humans i materials a través de rols, projectes i "
"comandes de venda."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Service"
msgstr "Servei"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_role__product_ids
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__role_product_ids
msgid "Services"
msgstr "Serveis"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/controllers/main.py:0
#, python-format
msgid "Shift"
msgstr "Torn"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__start_datetime
msgid "Start Date"
msgstr "Data inicial"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.technical_maintainance_product_product_template
msgid "Take a rest. We'll do our best."
msgstr "Descansa. Farem el possible."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "The sales orders have successfully been assigned."
msgstr "Les comandes de venda s'han assignat correctament."

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "There are no sales orders to assign or no employees are available."
msgstr ""
"No hi ha comandes de venda per assignar o no hi ha empleats disponibles."

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
#, python-format
msgid ""
"This Sale Order Item doesn't have a target value of planned hours. Planned "
"hours :"
msgstr ""
"Aquest element de comanda de venda no té un objectiu d'hores planejades. "
"Hores planejades:"

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/planning_gantt_controller.js:0
#, python-format
msgid ""
"This resource is not available for this shift during the selected period."
msgstr ""
"Aquest recurs no està disponible per a aquest torn durant el període "
"seleccionat."

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_view_form_in_gantt_inherit_sale_planning
msgid "Unschedule"
msgstr "No programar"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
#, python-format
msgid "View Planning"
msgstr "Visualitzar planificació"

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "View Shifts"
msgstr "Visualitzar torns"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_planner
msgid "Work Planner"
msgstr "Planificador de treball"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "e.g. Consultant"
msgstr "p.e Consultor"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order_line.py:0
#, python-format
msgid "remaining"
msgstr "restant"
