<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_invoice_document_intrastat_2019" inherit_id="account.report_invoice_document">
        <xpath expr="//th[@name='th_priceunit']" position="before">
            <t t-set="display_origin" t-value="any(l.intrastat_product_origin_country_id for l in o.invoice_line_ids)"/>
            <th class="text-end" t-if="display_origin">Origin</th>
        </xpath>
        <xpath expr="//td//span[@t-field='line.price_unit']/.." position="before">
            <td t-if="display_origin" class="text-end">
                <span t-field="line.intrastat_product_origin_country_id"/>
            </td>
        </xpath>
    </template>
</odoo>
