<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_pl_micro_bs" model="account.report">
        <field name="name">Bilans - Mikro</field>
        <field name="root_report_id" ref="account_reports.balance_sheet"/>
        <field name="filter_date_range" eval="False"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="country_id" ref="base.pl"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="l10n_pl_micro_bs_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_pl_micro_bs_assets" model="account.report.line">
                <field name="name">Aktywa</field>
                <field name="code">pl_micro_assets</field>
                <field name="expression_ids">
                    <record id="l10n_pl_micro_bs_assets_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_micro_assets_a.balance + pl_micro_assets_b.balance + pl_micro_assets_c.balance + pl_micro_assets_d.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_micro_bs_assets_a" model="account.report.line">
                        <field name="name">A. Aktywa trwałe, w tym środki trwałe</field>
                        <field name="code">pl_micro_assets_a</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">0</field>
                    </record>
                    <record id="l10n_pl_micro_bs_assets_b" model="account.report.line">
                        <field name="name">B. Aktywa obrotowe</field>
                        <field name="code">pl_micro_assets_b</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_micro_bs_assets_b_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_micro_assets_b_1.balance + pl_micro_assets_b_2.balance + pl_micro_assets_b_3.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_micro_bs_assets_b_1" model="account.report.line">
                                <field name="name">– zapasy</field>
                                <field name="code">pl_micro_assets_b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">60 + 62 +  3\(30.03,30.04,30.07)</field>
                            </record>
                            <record id="l10n_pl_micro_bs_assets_b_2" model="account.report.line">
                                <field name="name">– należności krótkoterminowe</field>
                                <field name="code">pl_micro_assets_b_2</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">30.03 + 30.07 + 22.02 + 20.00 + 24.06</field>
                            </record>
                            <record id="l10n_pl_micro_bs_assets_b_3" model="account.report.line">
                                <field name="name">– inne</field>
                                <field name="code">pl_micro_assets_b_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">1 + 30.04 + 24.010.2 + 24.020.1 + 24.07 + 24.08 + 24.09 + 29.01 + 29.03 + 64.01 + 65.01</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_micro_bs_assets_c" model="account.report.line">
                        <field name="name">C. Należne wpłaty na kapitał podstawowy</field>
                        <field name="code">pl_micro_assets_c</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">80.12</field>
                    </record>
                    <record id="l10n_pl_micro_bs_assets_d" model="account.report.line">
                        <field name="name">D. Udziały (akcje) własne</field>
                        <field name="code">pl_micro_assets_d</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">80.13</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_micro_bs_passives" model="account.report.line">
                <field name="name">Pasywa</field>
                <field name="code">pl_micro_passives</field>
                <field name="expression_ids">
                    <record id="l10n_pl_micro_bs_passives_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_micro_passives_a.balance + pl_micro_passives_b.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_micro_bs_passives_a" model="account.report.line">
                        <field name="name">A. Kapitał (fundusz) własny, w tym kapitał (fundusz) podstawowy</field>
                        <field name="code">pl_micro_passives_a</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_micro_bs_passives_a_balance_account_codes" model="account.report.expression">
                                <field name="label">balance_account_codes</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-80.0 - 80.19 - 81 - 82 - 85 - 86</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="l10n_pl_micro_bs_passives_a_balance_aggregate" model="account.report.expression">
                                <field name="label">balance_aggregate</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_micro_pl_f.balance</field>
                                <field name="subformula">cross_report</field>
                                <field name="date_scope">from_fiscalyear</field>
                            </record>
                            <record id="l10n_pl_micro_bs_passives_a_balance_unallocated" model="account.report.expression">
                                <field name="label">balance_unallocated</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[
                                    '|', ('account_id.account_type', '=', 'income'),
                                    '|', ('account_id.account_type', '=', 'income_other'),
                                    '|', ('account_id.account_type', '=', 'expense'),
                                    '|', ('account_id.account_type', '=', 'expense_depreciation'),
                                         ('account_id.account_type', '=', 'expense_direct_cost')
                                ]"/>
                                <field name="subformula">-sum</field>
                                <field name="date_scope">to_beginning_of_fiscalyear</field>
                            </record>
                            <record id="l10n_pl_micro_bs_passives_a_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_micro_passives_a.balance_account_codes + pl_micro_passives_a.balance_aggregate + pl_micro_passives_a.balance_unallocated</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_micro_bs_passives_b" model="account.report.line">
                        <field name="name">B. Zobowiązania i rezerwy na zobowiązania</field>
                        <field name="code">pl_micro_passives_b</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_micro_bs_passives_b_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_micro_passives_b_1.balance + pl_micro_passives_b_2.balance + pl_micro_passives_b_3.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="l10n_pl_micro_bs_passives_b_1" model="account.report.line">
                                <field name="name">– rezerwy na zobowiązania</field>
                                <field name="code">pl_micro_passives_b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-83</field>
                            </record>
                            <record id="l10n_pl_micro_bs_passives_b_2" model="account.report.line">
                                <field name="name">– zobowiązania z tytułu kredytów i pożyczek</field>
                                <field name="code">pl_micro_passives_b_2</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-24.010.1 - 23.02</field>
                            </record>
                            <record id="l10n_pl_micro_bs_passives_b_3" model="account.report.line">
                                <field name="name">- inne</field>
                                <field name="code">pl_micro_passives_b_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="foldable" eval="True"/>
                                <field name="account_codes_formula">-84 - 64.02 - 65.02 - 29.02 - 24.03 - 24.05 - 24.020.2 - 23\(23.02) - 22\(22.02) - 20.01 - 21.0 - 28</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
