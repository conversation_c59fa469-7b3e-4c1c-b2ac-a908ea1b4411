# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> AĞCA <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Halil, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Halil, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
#, python-format
msgid "%s (Rental)"
msgstr "%s (Kiralık)"

#. module: sale_renting
#: model:ir.actions.report,print_report_name:sale_renting.action_report_rental_saleorder
msgid ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"
msgstr ""
"(object.rental_status not in ('draft', 'sent') ve 'Teslimat veya İade "
"Makbuzu - %s' %(object.name)) or 'Sipariş - %s' % (object.name)"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "/day"
msgstr "/gün"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "/hour"
msgstr "/saat"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                    Default Delay Costs\n"
"                                </span>"
msgstr ""
"<span class=\"o_form_label\">\n"
"                                    Varsayılan Gecikme Maliyetleri\n"
"                                </span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_product_form_view_rental_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental_gantt
msgid "<span class=\"o_stat_text\">in Rental</span>"
msgstr "<span class=\"o_stat_text\">Kiralık</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "<span> to </span>"
msgstr "<span> -></span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Order # — </strong>"
msgstr "<strong>Sipariş # — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Pickup  — </strong>"
msgstr "<strong>Alış  — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Return — </strong>"
msgstr "<strong>Dönüş — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Satış Temsilcisi:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Sevkiyat Adresi:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Status — </strong>"
msgstr "<strong>Durum — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Add"
msgstr "Ekle"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Additional costs for late returns"
msgstr "Geç geri dönüşler için ek maliyetler"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,help:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_product_rentable
msgid "Allow renting of this product."
msgstr "Bu ürünün kiralanmasına izin verin."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__analytic_account_id
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Apply after"
msgstr "Sonra başvur"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Ask customer to sign documents on the spot."
msgstr "Müşteriden yerinde belgeleri imzalamasını isteyin."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "At first, let's create some products to rent."
msgstr "İlk olarak, kiralamak için bazı ürünler oluşturalım."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__pricing_id
msgid "Best Pricing Rule based on duration"
msgstr "Süreye göre en iyi fiyatlandırma kuralı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_product_rentable
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_product_template_search_view
msgid "Can be Rented"
msgstr "Kiralanabilir"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Cancel"
msgstr "İptal"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__cancel
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to create a new quotation."
msgstr "Yeni bir teklif oluşturmak için burayı tıklayın."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to register the pickup."
msgstr "Alımı kaydetmek için buraya tıklayın."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to set up your first rental product."
msgstr "İlk kiralama ürününüzü kurmak için buraya tıklayın."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to start filling the quotation."
msgstr "Teklifi doldurmaya başlamak için buraya tıklayın."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__color
msgid "Color"
msgstr "Renk"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__company_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Company"
msgstr "Şirket"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_config
msgid "Configuration"
msgstr "Yapılandırma"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_wizard
msgid "Configure the rental of a product"
msgstr "Bir ürünün kiralanmasını yapılandırma"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Confirm the order when the customer agrees with the terms."
msgstr "Müşteri şartları kabul ettiğinde siparişi onaylayın."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Confirm the returned quantities and hit Validate."
msgstr "İade edilen miktarları onaylayın ve Doğrula'ya basın."

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Confirmed"
msgstr "Doğrulanmış"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Confirmed Orders"
msgstr "Onaylı Siparişler"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_create_rental_order
msgid "Create Rental Orders"
msgstr "Kiralama Siparişleri Oluşturun"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid "Create a new quotation, the first step of a new rental!"
msgstr "Yeni bir teklif oluşturun, yeni bir kiralamanın ilk adımı!"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "Create a new rental order"
msgstr "Yeni bir kiralama siparişi oluşturun"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid "Create a new rental product!"
msgstr "Yeni bir kiralık ürün oluşturun!"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Create or select a customer here."
msgstr "Burada bir müşteri oluşturun veya seçin."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_rental_order
msgid "Created In App Rental"
msgstr "Uygulama Kiralamada Oluşturuldu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__currency_id
msgid "Currency"
msgstr "Para Birimi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__partner_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__partner_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer"
msgstr "Müşteri"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__country_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer Country"
msgstr "Müşteri Ülkesi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__commercial_partner_id
msgid "Customer Entity"
msgstr "Müşteri Varlığı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__card_name
msgid "Customer Name"
msgstr "Müşteri Adı"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_orders_customers
msgid "Customers"
msgstr "Müşteriler"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__price
msgid "Daily Amount"
msgstr "Günlük miktar"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__quantity
msgid "Daily Ordered Qty"
msgstr "Günlük Sipariş Miktarı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_delivered
msgid "Daily Picked-Up Qty"
msgstr "Günlük Alınan Miktar"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_returned
msgid "Daily Returned Qty"
msgstr "Günlük İade Miktarı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Date"
msgstr "Tarih"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Dates"
msgstr "Tarihler"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__day
msgid "Days"
msgstr "Gün"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_product
msgid "Delay Product"
msgstr "Gecikmeli Ürün"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__description
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Description"
msgstr "Açıklama"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__module_sale_renting_sign
msgid "Digital Documents"
msgstr "Dijital Belgeler"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__draft
msgid "Draft Quotation"
msgstr "Taslak Teklif"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__duration
msgid "Duration"
msgstr "Süre"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Edited Rental Line"
msgstr "Düzenlenmiş Kiralama Hattı"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Enter the product name."
msgstr "Ürün adını girin."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid ""
"Enter the requested dates and check the price.\n"
" Then, click here to add the product."
msgstr ""
"İstenen tarihleri ​​girin ve fiyatı kontrol edin.\n"
"Ardından ürünü eklemek için burayı tıklayın."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
#, python-format
msgid "Expected"
msgstr "Beklenen"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Expected Return"
msgstr "Beklenen Getiri"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_daily
msgid "Extra Day"
msgstr "Ek gün"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_hourly
msgid "Extra Hour"
msgstr "Ekstra Saat"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "Extras:"
msgstr "Ekstralar:"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_daily
msgid "Fine by day overdue"
msgstr "Vadesi geçmiş para cezası"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_hourly
msgid "Fine by hour overdue"
msgstr "Vadesi geçmiş saate göre para cezası"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "Fixed rental price"
msgstr "Sabit kiralama fiyatı"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Go to the orders menu."
msgstr "Siparişler menüsüne gidin."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Group By"
msgstr "Grupla"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__has_late_lines
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_late_lines
msgid "Has Late Lines"
msgstr "Geç Hatları Var"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_pickable_lines
msgid "Has Pickable Lines"
msgstr "Seçilebilir Hatları Var"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_returnable_lines
msgid "Has Returnable Lines"
msgstr "İade Edilebilir Hatları Var"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__pricing_explanation
msgid "Helper text to understand rental price computation."
msgstr "Kiralama fiyatı hesaplamasını anlamak için yardımcı metin."

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__hour
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__hour
msgid "Hours"
msgstr "Saat"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__id
msgid "ID"
msgstr "ID"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing Address:"
msgstr "Fatura Adresi:"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing and Shipping Address:"
msgstr "Fatura ve Teslim Adresi:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__is_late
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__late
msgid "Is Late"
msgstr "Geciken"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_rental
msgid "Is Rental"
msgstr "Kiralama mı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_late
msgid "Is overdue"
msgstr "Gecikme"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Late"
msgstr "Geciken"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Delivery"
msgstr "Geç teslimat"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Late Pickup"
msgstr "Geç Teslim Alma"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Return"
msgstr "Geç Dönüş"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Let's now create an order."
msgstr "Şimdi bir sipariş oluşturalım."

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_2_product_template
msgid "Meeting Room"
msgstr "Toplantı odası"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_res_company_min_extra_hour
msgid "Minimal delay time before applying fines has to be positive."
msgstr ""
"Para cezalarını uygulamadan önceki minimum gecikme süresi pozitif olmalıdır."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__min_extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__min_extra_hour
msgid "Minimum delay time before applying fines."
msgstr "Para cezalarını uygulamadan önce minimum gecikme süresi."

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__month
msgid "Months"
msgstr "Ay"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "My Orders"
msgstr "Siparişlerim"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__next_action_date
msgid "Next Action"
msgstr "Sonraki İşlem"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "No data yet!"
msgstr "Henüz veri yok!"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid ""
"No rental price is defined on the product.\n"
"The price used is the sales price."
msgstr ""
"Üründe kiralama fiyatı tanımlanmamıştır.\n"
"Kullanılan fiyat satış fiyatıdır."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Ok"
msgstr "Tamam"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid ""
"Once the quotation is confirmed, it becomes a rental order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Teklif onaylandıktan sonra kira emri olur.<br> Bir fatura oluşturabilir ve "
"ödemeyi tahsil edebilirsiniz."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Once the rental is done, you can register the return."
msgstr "Kiralama tamamlandıktan sonra, iadeyi kaydedebilirsiniz."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__order_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Order"
msgstr "Sipariş"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__order_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_id
msgid "Order #"
msgstr "Sipariş #"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Order Date"
msgstr "Sipariş Tarihi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__order_line_id
msgid "Order Line"
msgstr "Sipariş Kalemi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__name
msgid "Order Reference"
msgstr "Sipariş Referansı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_line_id
msgid "Order line #"
msgstr "Sipariş Satırı #"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_order_menu
#: model:ir.ui.menu,name:sale_renting.rental_orders_all
msgid "Orders"
msgstr "Siparişler"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "PICKUP"
msgstr "ALMAK"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_day
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_day
msgid "Per Day"
msgstr "Günlük"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_hour
msgid "Per Hour"
msgstr "Saat başı"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Alış / Dönüş ürünleri"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Picked-Up"
msgstr "Aldı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_delivered
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Picked-up"
msgstr "Aldı"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__pickedup
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickedup"
msgstr "Aldı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pickup_date
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__pickup
#: model:ir.ui.menu,name:sale_renting.rental_orders_pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Pickup"
msgstr "Almak"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__pickup_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Date"
msgstr "Tarih almak"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Receipt #"
msgstr "Alış Makbuzu#"

#. module: sale_renting
#: model:ir.actions.report,name:sale_renting.action_report_rental_saleorder
msgid "Pickup and Return Receipt"
msgstr "Teslim Alma ve İade Makbuzu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__reservation_begin
msgid "Pickup date - padding time"
msgstr "Teslim alma tarihi - doldurma süresi"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Pickup:"
msgstr "Alıcı:"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_rental_wizard_rental_period_coherence
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_period_coherence
msgid "Please choose a return date that is after the pickup date."
msgstr "Lütfen teslim tarihinden sonraki bir dönüş tarihi seçin."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricing_explanation
msgid "Price Computation"
msgstr "Fiyat Hesaplama"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricelist_id
msgid "Pricelist"
msgstr "Fiyat Listesi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricing_id
msgid "Pricing"
msgstr "Fiyatlandırma"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_template
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__product_id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__product_id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_product
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product"
msgstr "Ürün"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__categ_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__categ_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Product Category"
msgstr "Ürün Kategorisi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_name
msgid "Product Reference"
msgstr "Ürün referansı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_tmpl_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Ürün Şablonu"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_product
msgid "Product Variant"
msgstr "Ürün Varyantı"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product to charge extra time"
msgstr "Ekstra şarj etmek için ürün"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__product_id
msgid "Product to rent (has to be rentable)"
msgstr "Kiralanacak ürün (kiralanabilir olmalıdır)"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_product_template_action
#: model:ir.ui.menu,name:sale_renting.menu_rental_products
msgid "Products"
msgstr "Ürünler"

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_1_product_template
msgid "Projector"
msgstr "Projektör"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom_qty
msgid "Qty Ordered"
msgstr "Sipariş Miktarı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_delivered
msgid "Qty Picked-Up"
msgstr "Alınan Miktar"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_returned
msgid "Qty Returned"
msgstr "Adet İade Edildi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__quantity
msgid "Quantity"
msgstr "Miktar"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__qty_in_rent
#: model:ir.model.fields,field_description:sale_renting.field_product_template__qty_in_rent
msgid "Quantity currently in rent"
msgstr "Şu anda kiralanan miktar"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__draft
msgid "Quotation"
msgstr "Teklif"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sent
msgid "Quotation Sent"
msgstr "Teklif Gönderildi"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Quotations"
msgstr "Teklifler"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "RETURN"
msgstr "DÖNÜŞ"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_configurator_action
msgid "Rent a product"
msgstr "Ürün kiralama"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order_line__temporal_type__rental
#: model:ir.ui.menu,name:sale_renting.rental_menu_root
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Rental"
msgstr "Kiralık"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_report
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_graph_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_pivot_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Rental Analysis"
msgstr "Kira Analizi"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Kira Analiz Raporu"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Rental Order"
msgstr "Kiralama Siparişi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__rental_order_line_id
msgid "Rental Order Line"
msgstr "Kiralama Sipariş Hattı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__rental_order_wizard_id
msgid "Rental Order Wizard"
msgstr "Kiralama Siparişi Sihirbazı"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_order_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_pickup_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_return_action
msgid "Rental Orders"
msgstr "Kiralama Siparişleri"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "Kiralama Takvimi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__rental_status
msgid "Rental Status"
msgstr "Kiralama Durumu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__report_line_status
msgid "Rental Status (advanced)"
msgstr "Kiralama Durumu (ileri düzey)"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__rental_wizard_line_ids
msgid "Rental Wizard Line"
msgstr "Kiralık Sihirbaz Hattı"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_sale_renting_periods
msgid "Rental periods"
msgstr "Kiralama süreleri"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "Kiralık Sipariş Satırı Geçici"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Rentals"
msgstr "Kiralama"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_reporting
msgid "Reporting"
msgstr "Raporlama"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Reservations"
msgstr "Rezervasyonlar"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_reserved
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__reserved
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Reserved"
msgstr "Rezerve"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__return_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__return_date
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__return
#: model:ir.ui.menu,name:sale_renting.rental_orders_return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Return"
msgstr "İade"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__return_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Return Date"
msgstr "Dönüş tarihi"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Return:"
msgstr "Dönüş:"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_returned
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__qty_returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__returned
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
#, python-format
msgid "Returned"
msgstr "İade"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_temporal_recurrence
msgid "Sale temporal Recurrence"
msgstr "Satış Zamansal Tekrarlama"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__done
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__done
msgid "Sales Done"
msgstr "Satış Tamamlandı"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sale
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sale
msgid "Sales Order"
msgstr "Satış Siparişi"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Satış Sipariş Satırı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__team_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Sales Team"
msgstr "Satış Ekibi"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__user_id
msgid "Salesman"
msgstr "Satış Personeli"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__user_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Save the product."
msgstr "Ürünü kaydedin."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Save the quotation."
msgstr "Teklifi kaydedin."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_schedule
msgid "Schedule"
msgstr "Çalışma Saatleri"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
#: model:ir.actions.act_window,name:sale_renting.action_rental_order_schedule
#, python-format
msgid "Scheduled Rentals"
msgstr "Tarifeli Kiralama"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Select your rental product."
msgstr "Kiralık ürününüzü seçin."

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_config_settings
#: model:ir.ui.menu,name:sale_renting.menu_rental_settings
msgid "Settings"
msgstr "Ayarlar"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Some delay cost will be added to the sales order."
msgstr "Müşteri siparişine biraz gecikme maliyeti eklenecektir."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__start_date
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
msgid "State"
msgstr "İl/Eyalet"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__status
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__state
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__state
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Status"
msgstr "Durumu"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Amount"
msgstr "Günlük Tutarın Toplamı"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Ordered Qty"
msgstr "Günlük sipariş edilen miktarın toplamı"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Picked-Up Qty"
msgstr "Günlük Toplanan Miktarın Toplamı"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__temporal_type
msgid "Temporal Type"
msgstr "Zamansal Tür"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__duration
msgid "The duration unit is based on the unit of the rental pricing rule."
msgstr "Süre birimi, kiralama fiyatlandırma kuralının birimine bağlıdır."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_company__extra_product
msgid "The product is used to add the cost to the sales order"
msgstr "Ürün, müşteri siparişine maliyet eklemek için kullanılır"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_late
msgid "The products haven't been returned in time"
msgstr "Ürünler zamanında iade edilmedi"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "The rental configuration is available here."
msgstr "Kiralama yapılandırmasına buradan ulaşabilirsiniz."

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid "There isn't any scheduled pickup or return."
msgstr "Programlanmış herhangi bir karşılama veya iade yok."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_day
msgid ""
"This is the default extra cost per day set on newly created products. You "
"can change this value for existing products directly on the product itself."
msgstr ""
"Bu, yeni oluşturulan ürünlerde belirlenen günlük varsayılan ek maliyettir. "
"Mevcut ürünler için bu değeri doğrudan ürünün kendisinde "
"değiştirebilirsiniz."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_hour
msgid ""
"This is the default extra cost per hour set on newly created products. You "
"can change this value for existing products directly on the product itself."
msgstr ""
"Bu, yeni oluşturulan ürünlerde belirlenen saat başına varsayılan ek "
"maliyettir. Mevcut ürünler için bu değeri doğrudan ürünün kendisinde "
"değiştirebilirsiniz."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__unit_price
msgid ""
"This price is based on the rental price rule that gives the cheapest price "
"for requested duration."
msgstr ""
"Bu fiyat, istenen süre için en ucuz fiyatı veren kiralama fiyatı kuralına "
"dayanmaktadır."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_product
msgid "This product will be used to add fines in the Rental Order."
msgstr ""
"Bu ürün Kiralama Siparişinde para cezaları eklemek için kullanılacaktır."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"Those values are applied to any new rental product and can be changed on "
"product forms."
msgstr ""
"Bu değerler herhangi bir yeni kiralık ürüne uygulanır ve ürün formlarında "
"değiştirilebilir."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_orders_today
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "To Do Today"
msgstr "Bugün Yapılacak"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Pickup"
msgstr "Alış"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Return"
msgstr "Geri ver"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_tree
msgid "Total Tax Included"
msgstr "Vergili Toplam"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__duration_unit
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__unit
msgid "Unit"
msgstr "Adet"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__unit_price
msgid "Unit Price"
msgstr "Birim Fiyat"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__uom_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_uom
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom
msgid "Unit of Measure"
msgstr "Ölçü Birimi"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Validate"
msgstr "Doğrula"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
#, python-format
msgid "Validate a pickup"
msgstr "Bir karşılamayı doğrulayın"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
#, python-format
msgid "Validate a return"
msgstr "Bir dönüşü doğrulayın"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Validate the operation after checking the picked-up quantities."
msgstr "Alınan miktarları kontrol ettikten sonra işlemi doğrulayın."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid ""
"Want to <b>rent products</b>? \n"
" Let's discover Odoo Rental App."
msgstr ""
"Ürün<b>kiralamak istiyorum</b>? \n"
"Odoo Kiralama App keşfedelim."

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__week
msgid "Weeks"
msgstr "Hafta"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__year
msgid "Years"
msgstr "Yıl"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid ""
"You can also create additional products or services to sell by checking *Can"
" be Sold* in the product form (e.g. insurance)."
msgstr ""
"Ayrıca, ürün formundaki (örneğin sigorta) * Satılabilir * seçeneğini "
"işaretleyerek satmak için ek ürün veya hizmetler de oluşturabilirsiniz."

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid ""
"You can search on a larger period using the filters here above\n"
"                <br>\n"
"                or create a new rental order."
msgstr ""
"Yukarıdaki filtreleri kullanarak daha büyük bir dönemde arama yapabilirsiniz\n"
"                <br>\n"
"                veya yeni bir kiralama siparişi oluşturun."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_processing.py:0
#, python-format
msgid "You can't return more than what's been picked-up."
msgstr "Alınandan daha fazlasını geri getiremezsiniz."

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_stock_coherence
msgid "You cannot return more than what has been picked up."
msgstr "Alınandan daha fazla geri dönemezsiniz."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "You're done with your fist rental. Congratulations !"
msgstr "İlk kiralamanız bitti. Tebrikler!"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "hours"
msgstr "saat"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
#, python-format
msgid "to"
msgstr "kime"
