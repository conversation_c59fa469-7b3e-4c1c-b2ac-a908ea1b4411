# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_sign
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: Kevilyn Rosa, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_applicant__sign_request_count
msgid "# Signatures"
msgstr "Assinatura"

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
#, python-format
msgid "%s and %s are the signatories."
msgstr "%s e %s são signatários."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_applicant_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">Solicitações de assinatura</span>"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_id
msgid "Applicant"
msgstr "Candidato"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid "Applicant Role"
msgstr "Função do aplicante"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_name
msgid "Applicant's Name"
msgstr "Nome do Candidato"

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid ""
"Applicant's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr ""
"Função do candidato nos modelos a serem assinados. A mesma função deve estar"
" presente em todos os modelos"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Attach a file"
msgstr "Anexar um arquivo"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__attachment_ids
msgid "Attachment"
msgstr "Anexo"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_id
msgid "Contact"
msgstr "Contato"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__cc_partner_ids
msgid "Copy to"
msgstr "Cópia para"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_date
msgid "Created on"
msgstr "Criado em"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Discard"
msgstr "Descartar"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: hr_recruitment_sign
#: model:ir.actions.act_window,name:hr_recruitment_sign.sign_recruitment_wizard_action
msgid "Document Signature"
msgstr "Assinatura do documento"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "Documentos a serem assinados"

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the applicant while documents with 2 different responsible will have to be signed by both the applicant and the responsible.\n"
"        "
msgstr ""
"Documentos a serem assinados. Somente documentos com 1 ou 2 responsáveis diferentes podem ser selecionados.\n"
"          Os documentos com 1 responsável só precisarão ser assinados pelo solicitante, enquanto os documentos com 2 responsáveis diferentes precisarão ser assinados pelo solicitante e pelo responsável."

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "Tem ambos os modelos"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Mail Options"
msgstr "Opções de e-mail"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__message
msgid "Message"
msgstr "Mensagem"

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
#, python-format
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr ""
"Não foi possível encontrar um modelo apropriado. Verifique se você o "
"configurou corretamente."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "No template available"
msgstr "Nenhum modelo disponível"

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
#, python-format
msgid "Only %s has to sign."
msgstr "Apenas %s tem que assinar."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Optional Message..."
msgstr "Mensagem opcional..."

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__possible_template_ids
msgid "Possible Template"
msgstr "Modelo possível"

#. module: hr_recruitment_sign
#: model:ir.actions.server,name:hr_recruitment_sign.action_request_signature
msgid "Request Signature"
msgstr "Solicitar assinatura "

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "Responsável"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Send"
msgstr "Enviar"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Sign Request Options"
msgstr "Opções de solicitação de assinatura"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "Responsável pelo modelo de assinatura"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_recruitment_sign_document_wizard
msgid "Sign document in recruitment"
msgstr "Assinar documento no recrutamento"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Signature Request"
msgstr "Solicitar assinatura"

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "Solicitação de assinatura- %s"

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
#, python-format
msgid "Signature Requests"
msgstr "Solicitações de Assinatura"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__subject
msgid "Subject"
msgstr "Assunto"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "Aviso de modelo"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Write email or search contact..."
msgstr "Escreva um e-mail ou pesquise o contato..."

#. module: hr_recruitment_sign
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
#, python-format
msgid "You must define a Contact Name for this applicant."
msgstr "Você deve definir um Nome de Contato para este candidato."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.message_signature_request
msgid "requested a new signature on the following documents:"
msgstr "solicitou uma nova assinatura nos seguintes documentos:"
