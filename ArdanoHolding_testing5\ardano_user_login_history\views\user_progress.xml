<odoo>
    <data>

        <record id="user_progress_history" model="ir.ui.view">
            <field name="name">user_progress_history</field>
            <field name="model">res.users</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="project_ids"/>
                    <field name="work_order_ids"/>
                    <field name="delivery_order_ids"/>
                    <field name="approval_ids"/>
                    <field name="bill_ids"/>
                </tree>
            </field>
        </record>

        <record id="user_progress_history_form" model="ir.ui.view">
            <field name="name">user_progress_history_form</field>
            <field name="model">res.users</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name" readonly="1"/>
                        </group>
                        <div class="oe_button_box" name="button_box">
                            <button class="btn btn-primary btn-lg btn-block m-3" type="object" name="get_projects"
                                    string="مراكز التكلفه">
                                <field name="len_project_ids"/>
                            </button>
                            <button class="btn btn-primary btn-lg btn-block m-3" type="object" name="get_work_orders"
                                    string="أوامر العمل">
                                <field name="work_order_len"/>
                            </button>
                            <button class="btn btn-primary btn-lg btn-block m-3" type="object"
                                    name="get_delivery_orders"
                                    string="أوامر التوريد">
                                <field name="delivery_order_len"/>
                            </button>
                            <button class="btn btn-primary btn-lg btn-block m-3" type="object"
                                    name="get_delivery_orders"
                                    string="أوامر سداد">
                                <field name="approval_len"/>
                            </button>

                        </div>

                    </sheet>
                </form>
            </field>
        </record>

        <record id="user_progress_log_act_window" model="ir.actions.act_window">
            <field name="name">User Progress Log</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.users</field>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('user_progress_history')}),

            ]"/>
<!--(0, 0, {'view_mode': 'form', 'view_id': ref('user_progress_history_form')})-->
        </record>


    </data>
</odoo>