<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="group_fsm_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="base.module_category_services_field_service"/>
            <field name="implied_ids" eval="[
                (4, ref('hr_timesheet.group_hr_timesheet_user')),
                (4, ref('project.group_project_user'))
                ]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="group_fsm_manager" model="res.groups">
            <field name="category_id" ref="base.module_category_services_field_service"/>
            <field name="name">Administrator</field>
            <field name="implied_ids" eval="[(4, ref('industry_fsm.group_fsm_user')), (4, ref('project.group_project_manager'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4, ref('industry_fsm.group_fsm_manager'))]"/>
        </record>

        <record id="group_fsm_quotation_from_task" model="res.groups">
            <field name="name">Create new quotations directly from the tasks</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record model="ir.rule" id="report_project_task_user_fsm_rule">
            <field name="name">Tasks Analysis: FSM project visibility</field>
            <field name="model_id" ref="model_report_project_task_user_fsm"/>
            <field name="domain_force">[
            '|',
                ('project_id.privacy_visibility', '!=', 'followers'),
                '|',
                    ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                    '|',
                        ('task_id.message_partner_ids', 'in', [user.partner_id.id]),
                        ('user_ids', 'in', user.id),
            ]</field>
            <field name="groups" eval="[(4,ref('industry_fsm.group_fsm_user'))]"/>
        </record>

        <record model="ir.rule" id="report_project_task_user_fsm_manager_rule">
            <field name="name">Tasks Analysis: FSM project visibility for manager</field>
            <field name="model_id" ref="model_report_project_task_user_fsm"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('industry_fsm.group_fsm_manager'))]"/>
        </record>
    </data>
</odoo>
