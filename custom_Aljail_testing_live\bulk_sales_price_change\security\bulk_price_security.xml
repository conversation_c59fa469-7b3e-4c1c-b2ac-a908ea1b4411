<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_bulk_price_update" model="res.groups">
        <field name="name">Bulk Price Update</field>
        <field name="category_id" ref="base.module_category_sales_sales"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="bulk_price_history_rule" model="ir.rule">
        <field name="name">Bulk Price History: See Own Records</field>
        <field name="model_id" ref="model_bulk_price_history"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bulk_price_update'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo> 