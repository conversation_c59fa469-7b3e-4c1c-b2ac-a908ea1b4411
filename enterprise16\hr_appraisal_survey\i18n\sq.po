# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_survey
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "- Feedback requested by -"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.survey,description:hr_appraisal_survey.appraisal_360_feedback_template
msgid ""
"360 Degree Feedback is a system or process in which managers will ask feedback from the people who work around the employee.\n"
"            This typically includes the employee's manager, peers, and direct reports."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.appraisal_360_feedback_template
msgid "360 Feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model:mail.template,body_html:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"                        <br><br>\n"
"                        An appraisal feedback was requested about <t t-out=\"object.appraisal_id.employee_id.name or 'this'\">this</t>.\n"
"                        <br>\n"
"                        Please take time to fill the survey.\n"
"                        <br><br>\n"
"                        Thank you!\n"
"                        <br><br>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"background-color:#F8F8F8;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.get_start_url()\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                <t t-if=\"object.survey_id.certification\">\n"
"                                    Start Certification\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    Start Survey\n"
"                                </t>\n"
"                            </a>\n"
"                        </div>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Edit Survey"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_view_form
msgid ""
"<span class=\"o_stat_text\">Feedback</span>\n"
"                        <span class=\"o_stat_text\">Survey</span>"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_1
msgid "Ability to cope with multidisciplinarity of team"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_6
msgid "Ability to follow and complete work as instructed"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_5
msgid "Ability to manage planning resources, risks, budgets and deadlines"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_4
msgid "About us"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_1
msgid "About you"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_13
msgid ""
"Adaptability: Ability to adapt oneself to organizational changes while "
"keeping efficiency"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.actions.act_window,help:hr_appraisal_survey.survey_survey_action_appraisal
msgid "Add a new survey"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Add employees..."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_3
msgid "Additional Comments"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_6
msgid "Admit my mistakes"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_5
msgid "Almost always"
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid ""
"An appraisal feedback was requested. Please take time to fill the <a "
"href=\"%s\" target=\"_blank\">survey</a>"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_11
msgid "Analytical and synthetic mind"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__deadline
msgid "Answer Deadline"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_user_input__appraisal_id
msgid "Appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__employee_id
msgid "Appraisal Employee"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_survey__is_appraisal
msgid "Appraisal Managers Only"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_department__appraisal_survey_template_id
msgid "Appraisal Survey"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_res_company__appraisal_survey_template_id
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_res_config_settings__appraisal_survey_template_id
msgid "Appraisal Survey Template"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_3
msgid "Appraisal for Period"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "Appraisal of"
msgstr ""

#. module: hr_appraisal_survey
#: model:mail.template,name:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Appraisal: Ask Feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_5
msgid "Appraiser"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_view_form
msgid "Ask Feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_appraisal_ask_feedback
msgid "Ask Feedback for Appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_appraisal__employee_feedback_ids
msgid "Asked Feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "At the conclusion of the appraisal time period"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "At the outset of the appraisal time period"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__attachment_ids
msgid "Attachments"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__author_id
msgid "Author"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "Back to the appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Cancel"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_survey_survey__is_appraisal
msgid "Check this option to restrict the answers to appraisal managers only."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_3
msgid "Collaborate effectively with others to achieve shared goals"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_9
msgid ""
"Communication skills (written & verbally): clearness, concision, exactitude"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_res_company
msgid "Companies"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_3
msgid ""
"Compliance to internal rules and processes (timesheets completion, etc.)"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_9
msgid "Conclusion"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__body
msgid "Contents"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_2
msgid "Create space for different ideas and options to be voiced"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__create_uid
msgid "Created by"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__create_date
msgid "Created on"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_14
msgid "Creativity and forward looking aptitude"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"Critical or key elements of performance and professional development needs "
"(if any), should also be noted at this time"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_8
msgid "Customer commitment"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_4
msgid "Date of review"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_7
msgid "Decision making"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.res_config_settings_view_form_hr_appraisal_survey
msgid "Default Template"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_2
msgid "Delegation: Ability to efficiently assign tasks to other people"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_hr_department
msgid "Department"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Did not meet standards and expectations"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__display_name
msgid "Display Name"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_10
msgid "Do you have any comment to tell me and help me improve?"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_hr_appraisal
msgid "Employee Appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.appraisal_feedback_template
msgid "Employee Appraisal Form"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_5
msgid "Employee Comments"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.opinion_form
msgid "Employee Opinion Form"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_3
msgid "Employee Performance in Key Areas"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_2
msgid "Enthusiasm & implication toward projects/assignments"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Exceeds standards and expectations"
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Fill the feedback form on survey"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__email_from
msgid "From"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_5
msgid "How do you feel to work with me? Do I ... "
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug5
msgid "I'm not from the company."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug3
msgid "I'm part of your management."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug4
msgid "I'm referring to you."
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__id
msgid "ID"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_4
msgid ""
"Identify professional, performance, or project objectives you recommend for "
"employee’s continued career development over the coming year."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_5
msgid "Initiative and self autonomy"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"It is the joint responsibility of the employee and the supervisor "
"(appraiser) to establish a feasible work plan for the coming year, including"
" major employee responsibilities and corresponding benchmarks against which "
"results will be evaluated."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"It is the primary responsibility of the supervisor to gather the necessary "
"input from the appropriate sources of feedback (internal and/or external "
"customers, peers)."
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__lang
msgid "Language"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_3
msgid ""
"Leadership: create a challenging and motivating work environment aligned "
"with the company's strategy"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_4
msgid "Leadership: sustain subordinates in their professional growth"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_1
msgid "Listen well to others"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__template_id
msgid "Mail Template"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Meet standards and expectations"
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Missing email"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_1
msgid "Name"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_1
msgid "Never"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_1
msgid "Objectives"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_4
msgid "Often"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Optional message"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_appraisal_ask_feedback__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "Overall Purpose Of Employee Appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_2
msgid "Personal Performance Objectives"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_2
msgid "Position Title"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_1
msgid "Professional Development Objectives"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4
msgid "Professional Development and Performance Plan"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_3
msgid "Project Objectives"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_12
msgid "Promptness and attendance record"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_2
msgid "Rarely"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__employee_ids
msgid "Recipients"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_7
msgid "Recognize the contributions of teammates and peers"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__render_model
msgid "Rendering Model"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "Responded:"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_2
msgid "Results"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_1
msgid ""
"Results of the bottom-up survey and mitigation actions to face technical, "
"organizational, structural and/or relational issues"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_5
msgid "Seek to understand the problem before working on a solution"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Send"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_hr_appraisal__survey_ids
msgid "Sent out surveys"
msgstr ""

#. module: hr_appraisal_survey
#: model:mail.template,description:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Sent to employees to gather appraisal feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_4
msgid "Show good judgment in decision making"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Significantly below standards and expectations"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid ""
"Significantly exceeds standards and expectations required of the position"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_3
msgid "Sometimes"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__subject
#: model:survey.question,title:hr_appraisal_survey.appraisal_3_1
msgid "Subject"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Subject..."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_3_2
msgid "Supervisors only"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_appraisal__survey_ids
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_question_answer__survey_id
msgid "Survey"
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/models/hr_appraisal.py:0
#: code:addons/hr_appraisal_survey/models/hr_appraisal.py:0
#: code:addons/hr_appraisal_survey/models/survey.py:0
#, python-format
msgid "Survey Feedback"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_question_answer
msgid "Survey Label"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__survey_template_id
msgid "Survey Template"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.actions.act_window,name:hr_appraisal_survey.survey_survey_action_appraisal
#: model:ir.ui.menu,name:hr_appraisal_survey.menu_hr_appraisal_surveys
msgid "Surveys"
msgstr ""

#. module: hr_appraisal_survey
#: model:mail.template,subject:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Take part in {{ object.employee_id.name or 'this' }} appraisal"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_4
msgid ""
"Team spirit: ability to work efficiently with peers, manage the conflicts "
"with diplomacy"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_10
msgid "Technical skills regarding to the job requirements"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid ""
"The appraiser should rate the employee’s major work accomplishments and "
"performance according to the metric provided below:"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The employee may choose to offer comments or explanation regarding the "
"completed review."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The employee will be responsible for completing a draft of the Appraisal "
"Form as a tool for self-appraisal and a starting point for the supervisor’s "
"evaluation. The employee can add examples of achievements for each "
"criterion. Once the form had been filled, the employee send it to their "
"supervisor."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The supervisor synthesizes and integrates all input into the completed "
"appraisal. The motivation of the evaluation is explained in the ad hoc "
"fields."
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid ""
"This employee doesn't have any mail address registered and will not receive any email. \n"
"The following employees do not have any email : \n"
"%s"
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_hr_department__appraisal_survey_template_id
msgid ""
"This field is used with 360 Feedback setting on Appraisal App, the aim is to"
" define a default Survey Template related to this department."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "This is a Test Survey."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.survey,description:hr_appraisal_survey.appraisal_feedback_template
msgid ""
"This survey allows you to give a feedback about your collaboration with an "
"employee. Filling it helps us improving the appraisal process."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_15
msgid "Time management: projects/tasks are completed on time"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"To assist employees in their professional growth, through the identification"
" of strengths and opportunities for development"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "To initiate a clear and open communication of performance expectations"
msgstr ""

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_5_1
msgid ""
"Use the following space to make any comments regarding the above performance"
" evaluation."
msgstr ""

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__user_body
msgid "User Contents"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.survey_user_input_view_tree
msgid "View Results"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug1
msgid "We're colleagues, for the same manager."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug2
msgid "We're colleagues, in different teams."
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_6
msgid "What should I do in order to improve on my day-to-day job?"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_7
msgid "What's my greatest strength?"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_8
msgid "What's my greatest weakness?"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_3
msgid "What's the relation between us?"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_2
msgid "What's your name?"
msgstr ""

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2
msgid "Work Plan"
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.actions.act_window,help:hr_appraisal_survey.survey_survey_action_appraisal
msgid ""
"You can create surveys used for appraisals. Design easily your appraisal,\n"
"                send invitations and analyze answers."
msgstr ""

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "or"
msgstr ""
