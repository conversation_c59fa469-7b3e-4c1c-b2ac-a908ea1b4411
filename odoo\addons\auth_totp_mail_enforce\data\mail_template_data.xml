<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_totp_mail_code" model="mail.template">
            <field name="name">Settings: 2Fa New Login</field>
            <field name="model_id" ref="base.model_res_users" />
            <field name="subject">Your two-factor authentication code</field>
            <field name="email_to">{{ object.email_formatted }}</field>
            <field name="email_from">{{ (object.company_id.email_formatted or user.email_formatted) }}</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-size: 13px;">
    Dear <t t-out="object.partner_id.name or ''"></t><br/><br/>
    <p>Someone is trying to log in into your account with a new device.</p>
    <ul>
        <t t-set="not_available">N/A</t>
        <li>Location: <t t-out="ctx.get('location') or not_available"/></li>
        <li>Device: <t t-out="ctx.get('device') or not_available"/></li>
        <li>Browser: <t t-out="ctx.get('browser') or not_available"/></li>
        <li>IP address: <t t-out="ctx.get('ip') or not_available"/></li>
    </ul>
    <p>If this is you, please enter the following code to complete the login:</p>
    <t t-set="code_expiration" t-value="object._get_totp_mail_code()"/>
    <t t-set="code" t-value="code_expiration[0]"/>
    <t t-set="expiration" t-value="code_expiration[1]"/>
    <div style="margin: 16px 0px 16px 0px; text-align: center;">
        <span t-out="code" style="background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;"/>
    </div>
    <small>Please note that this code expires in <t t-out="expiration"/>.</small>

    <p style="margin: 16px 0px 16px 0px;">
        If you did NOT initiate this log-in,
        you should immediately change your password to ensure account security.
    </p>

    <p style="margin: 16px 0px 16px 0px;">
        We also strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.
    </p>

    <p style="margin: 16px 0px 16px 0px; text-align: center;">
        <a t-att-href="object.get_totp_invite_url()"
            style="background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
            Activate my two-factor authentication
        </a>
    </p>
</div>
            </field>
        </record>
    </data>
</odoo>
