<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_sale_discount" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.sale.discount</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="10"/>
        <field name="inherit_id" ref="sale.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//div[@data-key='sale_management']/div[hasclass('o_settings_container')][2]/div[hasclass('o_setting_box')][2]" position="after">

                <div class="col-xs-12 col-md-6 o_setting_box">
                    <div class="o_setting_left_pane">
                        <field name="so_order_approval"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="so_order_approval"/>
                        <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." groups="base.group_multi_company"/>
                        <div class="text-muted">
                            Managers must approve discount
                        </div>
                        <div class="content-group" attrs="{'invisible': [('so_order_approval', '=', False)]}">
                            <div class="row mt16">
                                <label for="so_double_validation_limit" class="col-md-4 o_light_label"/>
                                <field name="so_double_validation_limit"/>
                            </div>
                        </div>
                    </div>
                </div>

            </xpath>
        </field>
    </record>
</odoo>