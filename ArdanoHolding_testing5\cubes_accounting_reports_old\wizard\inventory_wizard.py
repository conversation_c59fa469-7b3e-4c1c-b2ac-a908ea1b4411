# -*- coding:utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID
import xlsxwriter
from io import BytesIO
import base64
import logging
import os
_logger = logging.getLogger(__name__)

import time
from datetime import datetime
from dateutil import relativedelta
from odoo.exceptions import ValidationError

class InventoryReport(models.TransientModel):
    _name = 'inventory.wizard.report'
    _description = 'Inventory Report Wizard'

    partner_id = fields.Many2one('res.partner', string="Customer")
    date_from = fields.Date(string="Date From", required=True, default=time.strftime('%Y-%m-01'))
    date_to = fields.Date(string="Date To", required=True,
                          default=str(datetime.now() + relativedelta.relativedelta(months=+1, day=1, days=-1))[:10])

    service_margin = fields.Float(default = '.15')
    include_operational = fields.Boolean(string="Include Operational Expenses", default=True)

    gentextfile = fields.Binary('Click On Save As Button To Download File', readonly=True)

    def print_inventory_report_excel(self):
        if self.date_from > self.date_to:
            raise ValidationError('Start Date must be less than End Date')

        xls_filename = 'inventory_report.xlsx'
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        from_date = self.date_from
        to_date = self.date_to
        
        # STEP 1: Find company-wide indirect costs
        company_indirect_costs = {
            'equity_saving': 0,
            'warranty': 0,
            'rental': 0,
            'salary': 0
        }
        
        # Find equity saving cost (sum ALL entries, not just one)
        equity_saving_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'equity_saving_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in equity_saving_entries:
            equity_saving_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in equity_saving_lines:
                company_indirect_costs['equity_saving'] += line.debit
        
        # Find warranty cost (sum ALL entries, not just one)
        warranty_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'warranty_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in warranty_entries:
            warranty_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in warranty_lines:
                company_indirect_costs['warranty'] += line.debit
        
        # Find rental cost (sum ALL entries, not just one)
        rental_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'rental_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in rental_entries:
            rental_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in rental_lines:
                company_indirect_costs['rental'] += line.debit
        
        # Find salary cost (sum ALL entries, not just one)
        salary_entries = self.env['account.move'].search([
            ('indirect_cost', '=', 'salary_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ])
        
        for entry in salary_entries:
            salary_lines = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', '=', entry.id)
            ])
            for line in salary_lines:
                company_indirect_costs['salary'] += line.debit
        
        # STEP 2: Calculate company-wide total direct costs based on ALL partners
        company_total_direct_costs = 0
        partner_direct_costs = {}
        
        # Always get ALL partners for company-wide calculations
        All_analytic_accounts = self.env['account.analytic.account'].search([])
        all_partners = set(All_analytic_accounts.mapped('partner_id'))
        
        # Calculate each partner's direct costs and total company direct costs (for ALL partners)
        for partner in all_partners:
            if not partner:
                continue
                
            analytic_accounts = self.env['account.analytic.account'].search([
                ('partner_id', '=', partner.id)
            ])
            
            partner_total = 0
            for analytic in analytic_accounts:
                lines = self.env['account.analytic.line'].search([
                    ('account_id', '=', analytic.id),
                    ('date', '>=', self.date_from), 
                    ('date', '<=', self.date_to),
                    ('general_account_id.account_type', '=', 'income')
                ])
                
                for line in lines:
                    partner_total += line.amount
            
            partner_direct_costs[partner.id] = partner_total
            company_total_direct_costs += partner_total
        
        # Get partners to include in the report
        if self.partner_id:
            report_partners = self.partner_id
        else:
            report_partners = all_partners
            
        # STEP 3: Process each partner that should be in the report
        for partner in report_partners:
            if not partner:
                continue
                
            sheet = workbook.add_worksheet(partner.name)
            format1 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            format2 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            date_style = workbook.add_format({'text_wrap': True, 'num_format': 'dd-mm-yyyy', 'bottom': True, 'right': True, 'left': True, 'top': True})
            format2.set_align('center')
            format2.set_align('vcenter')
            format2.set_color('white')
            format2.set_bg_color('blue')
            
            # Create number format for 3 decimal places
            number_format = workbook.add_format({'align': 'center', 'border': True, 'num_format': '#,##0.000'})
            
            header_row_style = workbook.add_format(
                { 'align': 'center', 'border': True})

            header2_row_style = workbook.add_format(
                {'bg_color': '#ffff00', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header2_row_style.set_align('center')
            header2_row_style.set_align('vcenter')
            header3_row_style =  workbook.add_format(
                {'bg_color': '#92d050', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header3_row_style.set_align('center')
            header3_row_style.set_align('vcenter')
            header4_row_style = workbook.add_format(
                {'bg_color': '#fac090', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header4_row_style.set_align('center')
            header4_row_style.set_align('vcenter')
            header5_row_style = workbook.add_format(
                { 'bold': True, 'align': 'center', 'border': True,'text_wrap': True})
            header5_row_style.set_align('center')
            header5_row_style.set_align('vcenter')

            header6_row_style = workbook.add_format(
                { 'font_size': 12,'align': 'center', 'border': True, 'text_wrap': True})
            header6_row_style.set_align('center')
            header6_row_style.set_align('vcenter')
            
            # Add number format to headers that will contain numbers
            header_number_format = workbook.add_format(
                {'bg_color': '#ffff00', 'color': '#000000', 'bold': True, 'align': 'center', 
                 'border': True, 'num_format': '#,##0.000'})
            header3_number_format = workbook.add_format(
                {'bg_color': '#92d050', 'color': '#000000', 'bold': True, 'align': 'center', 
                 'border': True, 'num_format': '#,##0.000'})

            # Set column widths based on whether operational expenses are included
            if self.include_operational:
                sheet.set_column('A:A', 25)
                sheet.set_column('B:B', 20)
                sheet.set_column('C:C', 20)
                sheet.set_column('D:D', 20)
                sheet.set_column('E:E', 20)
                header_range = 'B11:E11'
                date_range = 'B8:E8'
                title_range = 'B7:E7'
            else:
                sheet.set_column('A:A', 25)
                sheet.set_column('B:B', 20)
                sheet.set_column('C:C', 20)
                sheet.set_column('D:D', 20)
                header_range = 'B11:D11'
                date_range = 'B8:D8'
                title_range = 'B7:D7'

            # Add logo and header
            try:
                logo_path = os.path.join(os.path.dirname(__file__), '..', 'static', 'images', 'logo.png')
                if os.path.exists(logo_path):
                    sheet.insert_image('B2', logo_path)
            except:
                pass
                
            sheet.merge_range(title_range, 'تقرير المخزون - ' + partner.name, header6_row_style)
            sheet.merge_range('B8:B8', 'Date ', format2)
            sheet.merge_range(date_range, str(' From  ' + str(from_date) + ' To  ' + str(to_date)), header6_row_style)
            sheet.merge_range(header_range,'التكلفة المباشرة', header2_row_style)
            
            # Headers based on operational expenses inclusion
            sheet.merge_range('A12:A13', 'اسم المركز', header5_row_style)
            sheet.merge_range('B12:B13', 'المواد السلعية', header2_row_style)
            sheet.merge_range('C12:C13', 'مواد وادوات التنظيف', header2_row_style)
            
            if self.include_operational:
                sheet.merge_range('D12:D13', 'المصروفات التشغيلية', header2_row_style)
                sheet.merge_range('E12:E13', 'الاجمالي', header3_row_style)
            else:
                sheet.merge_range('D12:D13', 'الاجمالي', header3_row_style)

            # Get analytic accounts for this partner
            analytic_accounts = self.env['account.analytic.account'].search([
                ('partner_id', '=', partner.id)
            ])

            row = 14
            total_goods = 0
            total_cleaning = 0
            total_operational = 0
            grand_total = 0

            for analytic in analytic_accounts:
                # Get goods materials
                goods_lines = self.env['account.analytic.line'].search([
                    ('account_id', '=', analytic.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('general_account_id.account_type', '=', 'income'),
                    ('cubes_type', '=', 'goods')
                ])
                goods_amount = sum(goods_lines.mapped('amount'))
                
                # Get cleaning materials
                cleaning_lines = self.env['account.analytic.line'].search([
                    ('account_id', '=', analytic.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('general_account_id.account_type', '=', 'income'),
                    ('cubes_type', '=', 'cleaning')
                ])
                cleaning_amount = sum(cleaning_lines.mapped('amount'))
                
                # Get operational expenses (other types)
                operational_lines = self.env['account.analytic.line'].search([
                    ('account_id', '=', analytic.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('general_account_id.account_type', '=', 'income'),
                    ('cubes_type', 'not in', ['goods', 'cleaning'])
                ])
                operational_amount = sum(operational_lines.mapped('amount'))
                
                # Calculate total for this analytic account based on inclusion setting
                if self.include_operational:
                    analytic_total = goods_amount + cleaning_amount + operational_amount
                else:
                    analytic_total = goods_amount + cleaning_amount
                
                # Write data
                sheet.write(row, 0, analytic.name, header_row_style)
                sheet.write(row, 1, goods_amount, number_format)
                sheet.write(row, 2, cleaning_amount, number_format)
                
                if self.include_operational:
                    sheet.write(row, 3, operational_amount, number_format)
                    sheet.write(row, 4, analytic_total, number_format)
                else:
                    sheet.write(row, 3, analytic_total, number_format)
                
                # Add to totals
                total_goods += goods_amount
                total_cleaning += cleaning_amount
                total_operational += operational_amount
                
                if self.include_operational:
                    grand_total += goods_amount + cleaning_amount + operational_amount
                else:
                    grand_total += goods_amount + cleaning_amount
                
                row += 1

            # Add total row
            sheet.write(row, 0, 'اجمالي المطالبات الداخلية', header3_row_style)
            sheet.write(row, 1, total_goods, header3_number_format)
            sheet.write(row, 2, total_cleaning, header3_number_format)
            
            if self.include_operational:
                sheet.write(row, 3, total_operational, header3_number_format)
                sheet.write(row, 4, grand_total, header3_number_format)
            else:
                sheet.write(row, 3, grand_total, header3_number_format)

        workbook.close()
        output.seek(0)
        data = output.read()
        output.close()

        self.gentextfile = base64.b64encode(data)
        return {
            'type': 'ir.actions.act_url',
            'url': '/web/content/?model=inventory.wizard.report&id=%s&field=gentextfile&download=true&filename=%s' % (
                self.id, xls_filename),
            'target': 'self',
        } 