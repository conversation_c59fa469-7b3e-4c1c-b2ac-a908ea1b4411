.o_popover_record_selector .popover-arrow::after {
    border-bottom-color: map-get($theme-colors, 'primary');
}

.o_record_selector_popover:not(.o_legacy_record_selector_popover) {
    width: 265px;
    background: white;
    --o-input-background-color: white;

    &:focus {
        outline: none;
    }

    .o_record_selector_popover_header {
        color: white;
        background: map-get($theme-colors, 'primary');
        font-weight: bold;
        padding: 5px 0 5px 0.4em;

        .o_record_selector_title {
            width: 100%;
            @include o-text-overflow;
            padding: 0px 35px;
            text-align: center;
        }
        .o_record_selector_search {
            padding-right: 0.4rem;
            > .o_input {
                font-size: 13px;
                padding: 5px 0.4rem;
                text-align: left;
                line-height: normal;
            }
        }
        .o_record_selector_popover_option {
            @include o-position-absolute($top: 0);
            padding: 8px;

            &.o_record_selector_prev_page {
                left: 0;
            }
            &.o_record_selector_close {
                right: 0;
            }
            &:hover {
                background: darken(map-get($theme-colors, 'primary'), 10%);
            }
        }
    }
    .o_record_selector_popover_body {
        .o_record_selector_page {
            position: relative;
            max-height: 320px;
            overflow: auto;
            margin: 0;
            padding: 0;

            > .o_record_selector_item {
                list-style: none;
                position: relative;
                padding: 5px 0 5px 0.4em;
                cursor: pointer;
                font-family: Arial;
                font-size: 13px;
                color: #444;
                border-bottom: 1px solid #eee;
                &.active {
                    background: #f5f5f5;
                }
                .o_record_selector_item_title {
                    font-size: 12px;
                }
                .o_record_selector_relation_icon {
                    @include o-position-absolute($top: 0, $right: 0, $bottom: 0);
                    display: flex;
                    align-items: center;
                    padding: 10px;
                }
            }
        }
    }
    .o_record_selector_popover_footer {
        background: map-get($theme-colors, 'primary');
        padding: 5px 0.4em;

        > input {
            font-size: 13px;
            width: 100%;
            padding: 0 0.4rem;
        }
    }
}
