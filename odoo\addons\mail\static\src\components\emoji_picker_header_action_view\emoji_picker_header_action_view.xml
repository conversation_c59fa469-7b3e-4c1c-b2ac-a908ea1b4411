<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.EmojiPickerHeaderActionView" owl="1">
        <div class="o_EmojiPickerHeaderActionView d-flex" t-attf-class="{{ className }}" t-ref="root">
            <t
                t-component="constructor.components[emojiPickerHeaderActionView.contentComponentName]"
                t-props="{
                    className: `o_EmojiPickerHeaderActionView_content`,
                    record: emojiPickerHeaderActionView.content,
                }"
            />
        </div>
    </t>

</templates>
