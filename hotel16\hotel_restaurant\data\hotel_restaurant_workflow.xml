<?xml version="1.0" ?>
<odoo>
	<data>

		<!-- Workflow definition -->

		<record model="workflow" id="wkf_table_resrvation">
			<field name="name">wkf.table.reservation</field>
			<field name="osv">hotel.restaurant.reservation</field>
			<field name="on_create">True</field>
		</record>

		<record model="workflow.activity" id="act_draft">
			<field name="wkf_id" ref="wkf_table_resrvation" />
			<field name="flow_start">True</field>
			<field name="name">draft</field>

		</record>

		<record model="workflow.activity" id="act_confirm">
			<field name="wkf_id" ref="wkf_table_resrvation" />
			<field name="name">confirm</field>
			<field name="kind">function</field>
			<field name="action">table_reserved()</field>
			<field name="split_mode">OR</field>
		</record>

		<record model="workflow.activity" id="act_cancel">
			<field name="wkf_id" ref="wkf_table_resrvation" />
			<field name="name">cancel</field>
			<field name="kind">function</field>
			<field name="action">table_cancel()</field>

		</record>
		<record model="workflow.activity" id="act_done">
			<field name="wkf_id" ref="wkf_table_resrvation" />
			<field name="name">done</field>
			<field name="flow_stop">True</field>
			<field name="kind">stopall</field>
			<field name="action">table_done()</field>

		</record>

		<!-- Transition -->
		<record model="workflow.transition" id="t1">
			<field name="act_from" ref="act_draft" />
			<field name="act_to" ref="act_confirm" />
			<field name="signal">confirm</field>
		</record>

		<record model="workflow.transition" id="t2">
			<field name="act_from" ref="act_confirm" />
			<field name="act_to" ref="act_done" />
			<field name="signal">done</field>
		</record>

		<record model="workflow.transition" id="t3">
			<field name="act_from" ref="act_confirm" />
			<field name="act_to" ref="act_cancel" />
			<field name="signal">cancel</field>
		</record>

		<record model="workflow.transition" id="t7">
			<field name="act_from" ref="act_cancel" />
			<field name="act_to" ref="act_draft" />
			<field name="signal">settodraft</field>

		</record>
	</data>
</odoo>