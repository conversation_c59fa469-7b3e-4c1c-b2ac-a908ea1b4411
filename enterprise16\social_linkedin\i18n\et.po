# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>eli Õigus <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr "<b>LinkedIn postitus</b>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr "Postituse tegelik LinkedIn ID"

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages data: %r."
msgstr ""

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages: %r."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr "Rakenduse ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr "App Secret"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Autori pilt"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr ""
"Märgi see valik, kui soovid kasutada enda isiklikku LinkedIni arendaja "
"kontot pakutud konto asemel."

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr "Kuva LinkedIni eelvaadet"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Meeldimised"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr "LinkedIn konto ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr "LinkedIn konto URN"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
#, python-format
msgid "LinkedIn Comments"
msgstr "LinkedIn kommentaarid"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr "LinkedIn meeldimised"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr "LinkedIn eelvaade"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr "LinkedIn Vanity nimi"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "LinkedIn juurdepääsu võti"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr "LinkedIn autori ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr "LinkedIn autori URN"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr "LinkedIn autori pildi URL"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "LinkedIn did not provide a valid access token."
msgstr "LinkedIn ei andnud kehtivat juurdepääsu võtit."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr "LinkedIn postituse URN"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr "Linkedin arendaja konto"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr "Meedia tüüp"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr "Postita pilt"

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "Postitused"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr "Sotsiaalkonto"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr "Sotsiaalne otsepostitus"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "Sotsiaalmeedia"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "Sotsiaalmeedia postitus"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr "Sotsiaalmeedia postituse mall"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "Sotsiaalmeedia voog"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr "Sotsiaalmeedia striiming postitus"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr "Ligipääsu võtit kasutatakse REST API-le päringu esitamiseks"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"Selle teenuse taotletud url tagastas vea. Palun võtke ühendust rakenduse "
"loojaga. "

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr "Selle kontoga pole lingitud ühtegi lehte"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "Päringu ajal ilmnes autoriseerimisprobleem. "

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "Autoriseerimata. Palun võtke ühendust oma administraatoriga."

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "Tundmatu"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr "Kasutage enda LinkedIni kontot"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Kasutatakse võrdluste tegemiseks, kui meil on vaja piirata mõningaid "
"funktsioone konkreetsele meediale (\"facebook\", \"twitter\", ...)."

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr "Vanity nimi, mida kasutatakse autori lingi genereerimiseks."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr ""
"Me ei saanud teie pilti üles laadida, proovige selle suurust vähendada ja "
"postitage see uuesti (viga: Failed during upload registration)."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr ""
"Pildi üleslaadimine ebaõnnestus. Proovige vähendada pildi suurust ning "
"seejärel postitage uuesti. "

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#, python-format
msgid "Wrong stream type for \"%s\""
msgstr "Vale vootüüp \"%s\""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Teil pole aktiivset tellimust. Palun osta üks siit: %s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid "unknown"
msgstr "tundmatu"
