<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_company_form_inherit_l10n_pe_edi" model="ir.ui.view">
        <field name="name">res.company.form.inherit.l10n_pe_edi</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('o_address_format')]" position="after">
                <field name="l10n_pe_edi_address_type_code"
                       placeholder="Address Type Code"
                       attrs="{'invisible': [('country_code', '!=', 'PE')]}"/>
            </xpath>
        </field>
    </record>
</odoo>
