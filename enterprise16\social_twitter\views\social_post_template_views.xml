<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="social_post_template_view_form" model="ir.ui.view">
        <field name="name">social.post.template.view.form.inherit.social.twitter</field>
        <field name="model">social.post.template</field>
        <field name="priority">10</field>
        <field name="inherit_id" ref="social.social_post_template_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='previews_placeholder']" position="inside">
                <field name="has_twitter_accounts" invisible="1" />
                <field name="twitter_preview" readonly="1" nolabel="1" colspan="2" widget="social_post_preview" media_type="twitter"
                    attrs="{'invisible': ['|', ('has_twitter_accounts', '=', False), ('message', 'in', [False, ''])]}"
                    class="o_social_preview_wrapper m-0 px-4 py-4 bg-200"/>
            </xpath>
            <field name="message" position="after">
                <field name="message_length" invisible="True"/>
                <field name="is_twitter_post_limit_exceed" invisible="True"/>
                <field name="twitter_post_limit_message" class="text-muted" attrs="{'invisible': ['|', ('has_twitter_accounts', '=', False), ('is_twitter_post_limit_exceed', '=', True)]}" string=""/>
                <field name="twitter_post_limit_message"  class="text-danger" attrs="{'invisible': ['|', ('has_twitter_accounts', '=', False), ('is_twitter_post_limit_exceed', '=', False)]}" string=""/>
            </field>
        </field>
    </record>
</data>
</odoo>
