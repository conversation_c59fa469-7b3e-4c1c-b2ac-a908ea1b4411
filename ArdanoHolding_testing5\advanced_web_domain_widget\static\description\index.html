<div class="container">
    <div class="oe_styling_v8">

        <!-- Nav START -->
        <div class="position-relative my-5">
            <div class="row no-gutters shadow-sm" style="border-radius: 14px">
                <nav class="
    navbar navbar-expand-lg navbar-light
    bg-light
    overflow-hidden
    w-100
" style="
    background-color: #fff !important;
    border-radius: 14px;
    padding: 0px;
    border: 1px solid rgb(0 0 0 / 12%);
">
                    <span class="navbar-brand" style="
    background-color: #0A0A0A;
    padding: 18px 20px 11px 20px;
    color:white;
    height: 100%;
    ">
                        Terabits
                    </span>


                    <div class="ml-auto text-right" id="navbarNav">
                        <ul class="nav navbar-nav ml-auto text-right">

                        </ul>
                    </div>




                    <div class="ml-auto text-right" id="navbarNav">
                        <ul class="nav navbar-nav ml-auto text-right">



                            <li class="nav-item" style="padding: 17px 10px">
                                <span class="badge badge-pill badge-primary" style="
            background-color: #7c7bad;
            border: 1px solid #7c7bbb4f;
            font-size: 14px;
            color: white;
            margin: 3px;
            padding-left: 23px;
        ">
                                    <i class="fa fa-check-circle position-absolute" style="
            font-size: 16px;
            margin-left: -17px;
            margin-top: -2px;
            "></i>
                                    Community
                                </span>
                                <span class="badge badge-pill badge-primary" style="
            background-color: #660066;
            border: 1px solid rgb(144 90 123 / 29%);
            font-size: 14px;
            color: white;
            margin: 3px;
            padding-left: 23px;
        ">
                                    <i class="fa fa-check-circle position-absolute" style="
            font-size: 16px;
            margin-left: -17px;
            margin-top: -2px;
            "></i>
                                    Enterprise
                                </span>



                                <span class="badge badge-pill badge-primary" style="
            background-color: #330033;
            border: 1px solid rgb(144 90 123 / 29%);
            font-size: 14px;
            color: white;
            margin: 3px;
            padding-left: 23px;
        ">
                                    <i class="fa fa-check-circle position-absolute" style="
            font-size: 16px;
            margin-left: -17px;
            margin-top: -2px;
            "></i>
                                    Odoo.sh
                                </span>

                            </li>

                        </ul>
                    </div>
                </nav>
            </div>
        </div>
        <!-- Nav END -->

        <!-- main intro -->
        <div class="position-relative">
            <div class="row pb32 pt32 align-items-center bg-white my-5 shadow" style="border-radius: 14px">
                <div class="col-12">
                    <h2 class="oe_slogan" style="color:#660066; font-family:Montserrat; font-weight:700; text-align:center; text-transform:uppercase; margin-bottom:18px; margin-top:18px">
                        Advanced Web Domain Widget
                    </h2>
                    <img class="img img-responsive center-block" style="border-top-left-radius:10px; border-top-right-radius:10px" src="https://www.terabits.xyz/index/img/advanced_web_domain_widget/16.0/img.png">
                    <h3 style="color: #3c4858;text-align: center;margin-bottom:18px;">
                        <span style="color: #000066">
                            "Now use the feature of select any models record in domain while using any relational field."
                        </span>
                    </h3>

                    <p class="mb16 text-black-dark px-3" style="font-family:Roboto;    text-align: justify; font-weight:normal; color:#11335b; font-size:16px">
                        Odoo base domain widget allows you to only match value or id while user wants to create domain using any relational fields. So, user confused when model has multiple record's id and he/she does't remembered. So, we have simplified that by showing models
                        record to the user. so, he/she can select by finding record and select it. our module will autometic adds ids of selected records in domain. To select related model's record and create domain, we allowed additional
                        two domain operators ('in', 'not in').

                    </p>

                    <!-- <section id="ah_module_heading" style="padding-top:30px">
                        <div class="container">
                            <div class="ah_center text-center">
                                <h2 align="center">
                                    <a style="background-color:#EF233C; color:white; border-color:#EF233C;
                                box-shadow: 0 3px 6px 0px #df4a4f75; padding:1%; text-decoration:none; border-radius:6px; display:inline-block" class="btn btn-success btn-lg" href="https://youtu.be/rgW9rnD0sJM" target="_blank" savefrom_lm_index="0" savefrom_lm="1"><span class="o_ripple d-block position-absolute rounded-circle" style="height:398px; width:398px"></span>How it works? click here to watch a demo
                                    </a><span style="padding: 0; margin: 0; margin-left: 5px;"></span>
                                </h2>
                            </div>
                        </div>
                    </section> -->


                </div>
            </div>
        </div>


        <!-- features highlight -->
        <section class="oe_container pb-5">
            <div class="mt64 mb64">
                <h2 style="color:#660066; font-family:'Montserrat'; text-align:center; margin:25px auto; text-transform:uppercase" class="oe_slogan">
                    <b>Highlights</b>
                </h2>
                <div class="row">
                    <div class="col-md-6 col-sm-12 mt32">
                        <div class="container shadow" style="border-radius:10px; padding:10px 0px;height: 100%;">
                            <div class="col-md-3" style="float:left">
                                <img class="img img-responsive" src="img/feature2.png">
                            </div>
                            <div class="col-md-9" style="padding-left:0; float:left; width:70%">
                                <h3 class="mt16 mb0" style="font-family:Roboto; font-weight:500; font-size:20px">Select any models records</h3>
                                <p class=" mt8" style="font-family:Roboto">
                                    Easy to create domain of relational fields by selecting any models record in domain. We provide additional operators ('in' and 'not in') to create relational fields domain.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-12 mt32">
                        <div class="container shadow" style="border-radius:10px; padding:10px 0px;height: 100%;">
                            <div class="col-md-3" style="float:left">
                                <img class="img img-responsive" src="img/feature7.png">
                            </div>
                            <div class="col-md-9" style="padding-left:0; float:left; width:70%">
                                <h3 class="mt16 mb0" style="font-family:Roboto; font-weight:500; font-size:20px">Autometic id add in domain</h3>
                                <p class=" mt8" style="font-family:Roboto">
                                    When user select models records from popup, there will generate tags of record's names and add records id in domain.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- tabs -->
        <section class="info-tabs">
            <div>
                <div class="tabs">
                    <div class="justify-content-center d-flex">
                        <!-- Nav pills -->
                        <ul class="nav my-3 o_tab_nav justify-content-center" style="border-radius:6px 6px; background-color:transparent">
                            <li class="nav-item mx-1" style="border-radius:6px 0 0 6px">
                                <a class="nav-link px-md-4 list-group-item-default list-group-item-action" data-toggle="pill" href="#pills-guide" style="border-radius:6px; opacity:1; padding:16px">USERGUIDE</a>
                            </li>
                            <li class="nav-item mx-1">
                                <a class="nav-link px-md-4 list-group-item-default list-group-item-action active" data-toggle="pill" href="#pills-screenshot" style="border-radius:6px; opacity:1; padding:16px">SCREENSHOTS</a>
                            </li>
                            <li class="nav-item mx-1">
                                <a class="nav-link px-md-4 list-group-item-default list-group-item-action" data-toggle="pill" href="#pills-faqs" style="border-radius:6px; opacity:1; padding:16px">FAQs</a>
                            </li>
                            <li class="nav-item mx-1">
                                <a class="nav-link px-md-4 list-group-item-default list-group-item-action" data-toggle="pill" href="#pills-release" style="border-radius:6px; opacity:1; padding:16px">RELEASES</a>
                            </li>
                        </ul>
                    </div>
                    <div class="tab-content shadow" style="border-radius: 10px;padding: 10px 0px;" id="pills-tabContent">
                        <div class="tab-pane fade" id="pills-guide" role="tabpanel" aria-labelledby="pills-guide-tab">

                            <div class="screenshot-description" style="margin: 1% 3%;">
                                <div style="border-radius: 10px;background-color: #f9f9f9;text-align:center;">
                                    <h4 style='font-weight: 600;padding: 20px;'>You just need to change in xml files to use our advanced domain feature.
                                    </h4>
                                </div>
                            </div>

                            <div class="screenshot-description" style="margin: 1% 3%;">
                                <div style="border-radius: 10px;background-color: #f9f9f9;">
                                    <li style='font-weight: 600;padding: 20px;'>
                                        <span>Replace name of odoo's 'domain' widget to 'terabits_domain' widget.
                                        </span>
                                    </li>
                                </div>
                            </div>
                            <div class="screenshot" style="text-align: center;">
                                <img class="img img-fluid oe_screenshot" style="width: 90%;margin: 2%;" src="img/screens/ss4.png" alt="Odoo's domain widget" />
                            </div>
                            <div class="screenshot" style="text-align: center;">
                                <img class="img img-fluid oe_screenshot" style="width: 90%;margin: 2%;" src="img/screens/ss5.png" alt="Terabits's domain widget" />
                            </div>
                        </div>

                        <!-- screenshots tab -->
                        <div class="tab-pane fade text-center active show" id="pills-screenshot" role="tabpanel" aria-labelledby="pills-screenshot-tab">
                            <div class="screenshot-description" style="margin: 1% 3%;">
                                <div style="border-radius: 10px;background-color: #f9f9f9;">
                                    <h4 style='font-weight: 600;padding: 20px;'>Here is odoo's 'domain' widget for domain creation.</h4>
                                </div>
                            </div>
                            <div class="screenshot" style="text-align: center;">
                                <img class="img img-fluid oe_screenshot" style="width: 90%;margin: 2%;" src="img/screens/ss1.png" alt="Odoo domain widget" />
                            </div>
                            <div class="screenshot-description" style="margin: 1% 3%;">
                                <div style="border-radius: 10px;background-color: #f9f9f9;">
                                    <h4 style='font-weight: 600;padding: 20px;'>Here is customized 'terabits_domain' widget for domain creation.</h4>
                                </div>
                            </div>
                            <div class="screenshot" style="text-align: center;">
                                <img class="img img-fluid oe_screenshot" style="width: 90%;margin: 2%;" src="img/screens/ss2.png" alt="Terabits domain widget" />
                            </div>
                            <div class="screenshot" style="text-align: center;">
                                <img class="img img-fluid oe_screenshot" style="width: 90%;margin: 2%;" src="img/screens/ss3.png" alt="Terabits domain widget" />
                            </div>
                        </div>

                        <!-- faq tab -->
                        <div class="tab-pane fade" id="pills-faqs" role="tabpanel" aria-labelledby="pills-faqs-tab">
                            <div class="s_faq mt32 mb32" style="background-color: transparent !important;padding: 0px 50px;">
                                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

                                    <div class="panel-group" style="border: 1px solid #ddd;border-radius: 10px;margin-bottom: 20px;">
                                        <div class="panel panel-default" style="box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);padding: 15px 15px;border-radius: 10px;">
                                            <div class="panel-heading mt0" style="border:1px solid transparent !important">
                                                <h4 class="panel-title" style="margin: 0;">
                                                    <a class="collapsed" style="color: #000;font-size: 20px;font-weight: 500;font-family: Roboto;padding: 15px 15px 15px 50px;line-height: 158%;" data-toggle="collapse" href="#collapse7" aria-expanded="false">
                                                        <span class="panelcontent">
                                                            Why should I use this app?
                                                        </span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="collapse7" style="background-color: rgb(240, 244, 247); padding: 15px;" class="panel-collapse collapse">
                                                <div>
                                                    <p class="mb0" style="height: auto;font-family: Roboto;padding-left: 2%;margin-left: 2%;">
                                                        This module provides domains with an additional feature to select any models record while using any relational field and create a domain after selecting it. for that, we provide two additional operators ('in' and 'not in') that allow the user to select
                                                        a record of any model. after select any record, its id automatic adds in domain.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-group" style="border: 1px solid #ddd;border-radius: 10px;margin-bottom: 20px;">
                                        <div class="panel panel-default" style="box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);padding: 15px 15px;border-radius: 10px;">
                                            <div class="panel-heading mt0" style="border:1px solid transparent !important">
                                                <h4 class="panel-title" style="margin: 0;">
                                                    <a class="collapsed" style="color: #000;font-size: 20px;font-weight: 500;font-family: Roboto;padding: 15px 15px 15px 50px;line-height: 158%;" data-toggle="collapse" href="#collapse1" aria-expanded="false">
                                                        <span class="panelcontent">
                                                            What is user's main benifit ?
                                                        </span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="collapse1" style="background-color: rgb(240, 244, 247); padding: 15px;" class="panel-collapse collapse">
                                                <div>
                                                    <p class="mb0" style="height: auto;font-family: Roboto;padding-left: 2%;margin-left: 2%;">
                                                        User's main benifit is that, he/she does not have to remember models record id while he/she have to create domain based on relational fields, because we direct show all models record so user only have to select its record.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-group" style="border: 1px solid #ddd;border-radius: 10px;margin-bottom: 20px;">
                                        <div class="panel panel-default" style="box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);padding: 15px 15px;border-radius: 10px;">
                                            <div class="panel-heading mt0" style="border:1px solid transparent !important">
                                                <h4 class="panel-title" style="margin: 0;">
                                                    <a class="collapsed" style="color: #000;font-size: 20px;font-weight: 500;font-family: Roboto;padding: 15px 15px 15px 50px;line-height: 158%;" data-toggle="collapse" href="#collapse4" aria-expanded="false">
                                                        <span class="panelcontent">
                                                            Need some customization in this app, whom can I contact?
                                                        </span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="collapse4" style="background-color: rgb(240, 244, 247); padding: 15px;" class="panel-collapse collapse">
                                                <div>
                                                    <p class="mb0" style="height: auto;font-family: Roboto;padding-left: 2%;margin-left: 2%;">
                                                        Please drop an <NAME_EMAIL> or raise a ticket through the Odoo store itself.

                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-group" style="border: 1px solid #ddd;border-radius: 10px;margin-bottom: 20px;">
                                        <div class="panel panel-default" style="box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);padding: 15px 15px;border-radius: 10px;">
                                            <div class="panel-heading mt0" style="border:1px solid transparent !important">
                                                <h4 class="panel-title" style="margin: 0;">
                                                    <a class="collapsed" style="color: #000;font-size: 20px;font-weight: 500;font-family: Roboto;padding: 15px 15px 15px 50px;line-height: 158%;" data-toggle="collapse" href="#collapse5" aria-expanded="false">
                                                        <span class="panelcontent">
                                                            Do you provide any free support?
                                                        </span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="collapse5" style="background-color: rgb(240, 244, 247); padding: 15px;" class="panel-collapse collapse">
                                                <div>
                                                    <p class="mb0" style="height: auto;font-family: Roboto;padding-left: 2%;margin-left: 2%;">
                                                        Yes, I do provide free support for 90 days for any queries or any bug/issue fixing.

                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-group" style="border: 1px solid #ddd;border-radius: 10px;margin-bottom: 20px;">
                                        <div class="panel panel-default" style="box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);padding: 15px 15px;border-radius: 10px;">
                                            <div class="panel-heading mt0" style="border:1px solid transparent !important">
                                                <h4 class="panel-title" style="margin: 0;">
                                                    <a class="collapsed" style="color: #000;font-size: 20px;font-weight: 500;font-family: Roboto;padding: 15px 15px 15px 50px;line-height: 158%;" data-toggle="collapse" href="#collapse6" aria-expanded="false">
                                                        <span class="panelcontent">
                                                            What is Support Policy of this Module?
                                                        </span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="collapse6" style="background-color: rgb(240, 244, 247); padding: 15px;" class="panel-collapse collapse">
                                                <div>
                                                    <p class="mb0" style="height: auto;font-family: Roboto;padding-left: 2%;margin-left: 2%;">
                                                        In case of if any bug raised in the listed features of this module, I am committed to providing support free of cost. You will need to provide me server ssh access or database access in order to solve the issue.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- release tab -->
                        <div class="tab-pane  px-0 px-sm-5 fade" id="pills-release" style="padding-top: 0.25rem !important;" role="tabpanel" aria-labelledby="pills-release-tab">

                            <div class="row my-4 p-3 pb-2 position-relative">
                                <div class="col-12 p-4 shadow-sm bg-white">
                                    <h2>Changelog(s)</h2>
                                    <hr class="mb-4 mt-0">
                                    <!-- v16.0.1.0.2 -->
                                    <h2 class="h4 pb-2">
                                        <span style="color:#00B5DB" class="pr-2">v16.0.1.0.2</span>-<span class="pl-2">December
                                            10, 2022</span>
                                    </h2>
                                    <ul class="list-unstyled">
                                        <li class="d-flex align-items-start mb-3">
                                            Minor bug fix.
                                        </li>
                                    </ul>
                                    <!-- v16.0.0.0.0 -->
                                    <h2 class="h4 pb-2">
                                        <span style="color:#00B5DB" class="pr-2">v16.0.0.0.0</span>-<span class="pl-2">November
                                            23, 2022</span>
                                    </h2>
                                    <ul class="list-unstyled">
                                        <li class="d-flex align-items-start mb-3">
                                            Initial release for v16
                                        </li>
                                    </ul>

                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </section>
        <!-- tabs end -->

        <!-- Help START -->
        <div class="row pb16 pt-5 mb-5">
            <div class="alert alert-warning text-center w-100" style="padding:21px 51px; background-color:#ffffff; border:1px solid #dee2e6; color:#414d5c; margin:auto; display:block; border-radius:1px; min-width:90%; border-top:3px solid #151765">
                <div>
                    <div style="background-color:rgb(255 164 0 / 12%); color:#151765" class="badge border-0 rounded-circle p-3">
                        <i class="fa fa-question-circle fa-2x"></i>
                    </div>
                </div>
                <h2 style="color:#3c4858" class="mt-2 mb-4">
                    Need a help for this module?
                </h2>
                <h4 style="color:#3c4858; font-weight:400" class="mt-2 mb-4">
                    Contact me
                    <b style="color:#151765; background-color:#e0f0ff; padding:3px 10px; border-radius:3px"><EMAIL></b> for your queries
                </h4>
            </div>
        </div>
        <!-- Help END -->

    </div>
</div>
        