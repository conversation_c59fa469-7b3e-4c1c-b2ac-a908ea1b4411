# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_attendance
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:09+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,help:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        Визначає джерело для створення робочих записів\n"
"\n"
"Робочий графік: робочі записи будуть створені на основі робочих годин, наведених нижче.\n"
"Відвідуваність: записи про роботу створюватимуться на основі відвідуваності працівника. (потрібен додаток Відвідування)\n"
"Планування: записи про роботу будуть створені на основі планування працівника. (потрібен додаток Планування)\n"
"    "

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_work_entry__attendance_id
msgid "Attendance"
msgstr "Відвідування"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields.selection,name:hr_work_entry_contract_attendance.selection__hr_contract__work_entry_source__attendance
msgid "Attendances"
msgstr "Відвідування"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "Трудовий контракт"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Робочий запис відділу кадрів"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Створити заново робочі записи співробітника"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "Джерело робочого запису"
