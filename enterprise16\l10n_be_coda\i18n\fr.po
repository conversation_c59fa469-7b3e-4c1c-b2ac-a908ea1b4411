# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_coda
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""
"\n"
"Les transactions de type 2.%s ne sont pas prises en charge"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "1-st (recurrent)"
msgstr "1er (récurrent)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "ATM/POS debit"
msgstr "Débit ATM/PdV"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Access right to database"
msgstr "Droit d'accès à la base de données"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Agio on supplier's bill"
msgstr "Agios sur une facture fournisseur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the bank"
msgstr "Montant totalisé par la banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the customer"
msgstr "Montant totalisé par le client"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Avgas"
msgstr "Avgas"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bancontact/Mister Cash"
msgstr "Bancontact/Mister Cash"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Relevé bancaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bank confirmation to revisor or accountant"
msgstr "Confirmation de la banque au réviseur ou au comptable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bill claimed back"
msgstr "Facture revendiquée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bills - calculation of interest"
msgstr "Factures - calcul des intérêts"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr "Notes CODA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""
"Les relevés au format CODA V%s ne sont pas pris en charge, contactez votre "
"banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"Erreur de parsing du CODA sur l'enregistrement 3.2, séquence %s! Merci de "
"rapporter ce problème via votre canal d'assistance Odoo."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"Erreur de parsing du fichier CODA sur l'enregistrement 3.3, séquence %s! "
"Merci de rapporter ce problème à l'assistance Odoo."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"Erreur de parsing du fichier CODA sur l'enregistrement 2.2, séquence %s! "
"Merci de rapporter ce problème à l'assistance d'Odoo."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""
"Erreur de parsing du fichier CODA sur l'enregistrement 2.3, séquence %s! "
"Merci de rapporter ce problème à l'assistance d'Odoo."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cancellation or correction"
msgstr "Annulation ou correction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Capital and/or interest term investment"
msgstr "Investissement à terme en capital et/ou intérêts"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cards"
msgstr "Cartes"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash deposit at an ATM"
msgstr "Dépôt en espèces à un guichet automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash payment"
msgstr "Paiement en espèces"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal"
msgstr "Retrait en espèces"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal by your branch or agents"
msgstr "Retrait en espèces par votre succursale ou vos agents"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal from an ATM"
msgstr "Retrait d'espèces à un guichet automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charge for safe custody"
msgstr "Frais de garde"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charging fees for transactions"
msgstr "Redevance pour les transactions"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheque-related costs"
msgstr "Coûts liés aux chèques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheques"
msgstr "Chèques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing"
msgstr "Clôture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing (periodical settlements for interest, costs,...)"
msgstr "Clôture (règlements périodiques d'intérêts, de frais,...)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Codes proper to each bank"
msgstr "Codes propres à chaque banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective payments of wages"
msgstr "Paiements collectifs des salaires"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfer"
msgstr "Virement collectif"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfers"
msgstr "Virements collectifs"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial bills"
msgstr "Factures commerciales"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial paper claimed back"
msgstr "Effet de commerce revendiqué"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication"
msgstr "Communication"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication: "
msgstr "Communication :"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communicaton"
msgstr "Communication"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Compensation for missing coupon"
msgstr "Compensation d'un coupon manquant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Correction for prepaid card"
msgstr "Correction pour la carte prépayée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs"
msgstr "Coûts"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for holding a documentary cash credit"
msgstr "Coûts de la tenue d'une lettre de crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for opening a bank guarantee"
msgstr "Coûts pour l'ouverture d'une garantie bancaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for the safe custody of correspondence"
msgstr "Frais de garde de la correspondance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs related to commercial paper"
msgstr "Coûts liés à l'effet de commerce"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to electronic output"
msgstr "Coûts liés à la sortie électronique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to incoming foreign and non-SEPA transfers"
msgstr "Coûts liés aux transferts entrants étrangers et non-SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to outgoing foreign transfers and non-SEPA transfers"
msgstr "Coûts liés aux transferts sortants étrangers et non-SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to payment of foreign cheques"
msgstr "Coûts liés au paiement de chèques étrangers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to the payment of a foreign bill"
msgstr "Coûts liés au paiement d'une facture étrangère"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party"
msgstr "Contre-partie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Account"
msgstr "Compte pour la contre-partie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Address"
msgstr "Adresse de la contre-partie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter transactions"
msgstr "Contre-opérations"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Country code of the principal"
msgstr "Code de pays du principal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit"
msgstr "Crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after Proton payments"
msgstr "Crédit après les paiements Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after a payment at a terminal"
msgstr "Crédit après le paiement à un terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after collection"
msgstr "Crédit après l'encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit under usual reserve"
msgstr "Crédit sous la réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit-related costs"
msgstr "Coûts liés au crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Creditor’s identification code"
msgstr "Code d'identification du créditeur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Currency"
msgstr "Devise"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Damage relating to bills and cheques"
msgstr "Dommages relatifs aux factures et aux chèques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Department store cheque"
msgstr "Chèque d'un grand magasin"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail"
msgstr "Détail"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the bank"
msgstr "Détail du montant totalisé par la banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the customer"
msgstr "Détail du montant totalisé par le client"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Simple amount with detailed data"
msgstr "Détail d'un montant simple avec données détaillées"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Difference in payment"
msgstr "Différence de paiement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct Debit scheme"
msgstr "Méthode de prélèvement automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct debit"
msgstr "Prélèvement automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount abroad"
msgstr "Remise à l'étranger"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount foreign supplier's bills"
msgstr "Remise des factures des fournisseurs étrangers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary credit charges"
msgstr "Frais liés au crédit documentaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary export credits"
msgstr "Lettre de crédit à l'exportation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary import credits"
msgstr "Lettre de crédit à l'importation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic commercial paper"
msgstr "Effet de commerce national"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic or local SEPA credit transfers"
msgstr "Virements SEPA nationaux ou locaux"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Download of prepaid card"
msgstr "Téléchargement de la carte prépayée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Drawing up a certificate"
msgstr "Établissement d'un certificat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Equivalent in EUR"
msgstr "Équivalent en euros"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Error"
msgstr "Erreur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension"
msgstr "Extension"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension of maturity date"
msgstr "Prolongation de la date d'échéance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fees and commissions"
msgstr "Frais et commissions"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation"
msgstr "Centralisation financière"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (credit)"
msgstr "Centralisation financière (crédit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (debit)"
msgstr "Centralisation financière (débit)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralization"
msgstr "Centralisation financière"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"First credit of cheques, vouchers, luncheon vouchers, postal orders, credit "
"under usual reserve"
msgstr ""
"Premier crédit de chèques, de bons, de tickets restaurant, de mandats de "
"poste, crédit sous réserve habituelle."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – capital and interest"
msgstr "Avance à terme fixe - capital et intérêts"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – interest only"
msgstr "Avance à terme fixe - intérêts uniquement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr ""
"Les comptes étrangers avec une structure BBAN ne sont pas pris en charge"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr ""
"Les comptes étrangers avec une structure IBAN ne sont pas pris en charge"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign cheques"
msgstr "Chèques étrangers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign commercial paper"
msgstr "Effet de commerce étranger"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward purchase of foreign exchange"
msgstr "Achat à terme de devises étrangères"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward sale of foreign exchange"
msgstr "Vente à terme de devises étrangères"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the currency of the account"
msgstr "Montant brut dans la devise du compte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the original currency"
msgstr "Montant brut dans la devise d'origine"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Handling costs instalment credit"
msgstr "Frais de gestion d'un crédit à tempérament"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Idem without guarantee"
msgstr "Idem sans garantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Income from payments by GSM"
msgstr "Revenu des paiements par GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order"
msgstr "Mandat de virement individuel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order initiated by the bank"
msgstr "Mandat de virement individuel initié par la banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Information charges"
msgstr "Frais d'information"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Instant SEPA credit transfer"
msgstr "Virement SEPA instantané"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Insurance costs"
msgstr "Coûts d'assurance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interest term investment"
msgstr "Intérêts d'un investissement à terme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interim interest on subscription"
msgstr "Intérêts intercalaires sur abonnement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "International credit transfers - non-SEPA credit transfers"
msgstr "Virements internationaux - Virements non-SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Issues"
msgstr "Problèmes"

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "LPG"
msgstr "LPG"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading GSM cards"
msgstr "Chargement des cartes GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading Proton"
msgstr "Chargement de Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading a GSM card"
msgstr "Chargement d'une carte GSM"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Long-term loan"
msgstr "Prêt à long terme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Maestro"
msgstr "Maestro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management fee"
msgstr "Frais de gestion"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management/custody"
msgstr "Gestion/garde"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Mandate reference"
msgstr "Référence du mandat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Masked PAN or card number"
msgstr "PAN ou numéro de carte masqué"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Method of calculation (VAT, withholding tax on income, commission, etc.)"
msgstr "Mode de calcul (TVA, précompte mobilier, commission, etc.)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Miscellaneous fees and commissions"
msgstr "Frais et commissions divers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Name: {name}, Town: {city}"
msgstr "Nom : {name}, Ville : {city}"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Night safe"
msgstr "Coffre de nuit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "No date"
msgstr "Aucune date"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Non-presented circular cheque"
msgstr "Chèque circulaire non soumis"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Number of the credit card"
msgstr "Numéro de la carte de crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Original amount of the transaction"
msgstr "Montant initial de la transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other"
msgstr "Autre"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other credit applications"
msgstr "Autres demandes de crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "PAN or card number"
msgstr "PAN ou numéro de carte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit - individual transaction"
msgstr "Crédit PdV - transaction individuelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit – Globalisation"
msgstr "Crédit PdV - Globalisation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS number"
msgstr "Numéro PdV"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS others"
msgstr "PdV autres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Paid or reason for refused payment"
msgstr "Payé ou raison du refus de paiement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Partial payment subscription"
msgstr "Paiement partiel de l'abonnement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Participation in and management of interest refund system"
msgstr ""
"Participation au système de remboursement des intérêts et gestion de celui-"
"ci"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Pay-packet charges"
msgstr "Frais enveloppe appointements"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payable coupons/repayable securities"
msgstr "Coupons payables/titres remboursables"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment"
msgstr "Paiement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by GSM"
msgstr "Paiement par téléphone portable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card outside the Eurozone"
msgstr "Paiement au moyen d'une carte de paiement hors zone euro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card within the Eurozone"
msgstr "Paiement au moyen d'une carte de paiement dans la zone euro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by your branch/agents"
msgstr "Paiement par votre succursale/agents"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment commercial paper"
msgstr "Paiement d'un effet de commerce"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment documents abroad"
msgstr "Documents de paiement à l'étranger"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in advance"
msgstr "Paiement anticipé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in your favour"
msgstr "Paiement en votre faveur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment night safe"
msgstr "Paiement du coffre de nuit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of a foreign cheque"
msgstr "Paiement d'un chèque étranger"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Payment of coupons from a deposit or settlement of coupons delivered over "
"the counter - credit under usual reserve"
msgstr ""
"Paiement de coupons provenant d'un dépôt ou règlement de coupons livrés au "
"guichet - crédit sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of foreign bill"
msgstr "Paiement d'une facture étrangère"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of voucher"
msgstr "Paiement du voucher"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of wages, etc."
msgstr "Paiement des salaires, etc."

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of your cheque"
msgstr "Paiement de votre chèque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment with tank card"
msgstr "Paiement avec carte essence"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Postage"
msgstr "Port"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Printing of forms"
msgstr "L'impression de formulaires"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Private"
msgstr "Privé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Provisionally unpaid"
msgstr "Provisoirement non payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of Smartcard"
msgstr "Achat d'une carte à puce"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of an international bank cheque"
msgstr "Achat d'un chèque bancaire international"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of fiscal stamps"
msgstr "Achat de timbres fiscaux"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of foreign bank notes"
msgstr "Achat de billets de banque étrangers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of gold/pieces"
msgstr "Achat d'or/de pièces"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of petrol coupons"
msgstr "Achat de chèques carburant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of securities"
msgstr "Achat de titres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of traveller’s cheque"
msgstr "Achat d'un chèque de voyage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Rate"
msgstr "Taux"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reason"
msgstr "Motif"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Registering compensation for savings accounts"
msgstr "Enregistrement de la compensation pour les comptes d'épargne"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Regularisation costs"
msgstr "Coûts de régularisation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement"
msgstr "Remboursement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of cheque-related costs"
msgstr "Remboursement des coûts liés aux chèques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of costs"
msgstr "Remboursement des coûts"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheque by your branch - credit under usual reserve"
msgstr ""
"Remise d'un chèque par votre succursale - crédit dans la réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheques, vouchers, etc. credit after collection"
msgstr "Remise de chèques, bons, etc. crédit après encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit after collection"
msgstr "Remise d'un effet de commerce - crédit après encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit under usual reserve"
msgstr "Remise d'un effet de commerce - crédit sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper for discount"
msgstr "Remise d'un effet de commerce pour remise"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit after collection"
msgstr "Remise de documents à l'étranger - crédit après encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit under usual reserve"
msgstr "Remise de documents à l'étranger - crédit sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit after collection"
msgstr "Remise d'une facture étrangère crédit après encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit under usual reserve"
msgstr "Remise d'une facture étrangère crédit sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit after collection"
msgstr "Remise d'un chèque étranger crédit après encaissement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit under usual reserve"
msgstr "Remise d'un chèque étranger crédit sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of guaranteed foreign supplier's bill"
msgstr "Remise de la facture du fournisseur étranger garantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill with guarantee"
msgstr "Remise d'une facture fournisseur avec garantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill without guarantee"
msgstr "Remise d'une facture fournisseur sans garantie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of direct debit box"
msgstr "Location d'une boîte de prélèvement automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of safes"
msgstr "Location de coffres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Repayable securities from a deposit or delivered at the counter - credit "
"under usual reserve"
msgstr ""
"Titres remboursables provenant d'un dépôt ou livrés au guichet - crédit sous"
" réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Research costs"
msgstr "Coûts de recherche"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Retrocession of issue commission"
msgstr "Rétrocession de la commission d'émission"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Return of an irregular bill of exchange"
msgstr "Restitution d'une lettre de change irrégulière"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal"
msgstr "Extourne"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheque"
msgstr "Extourne d'un chèque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheques"
msgstr "Extourne de chèques"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of voucher"
msgstr "Extourne d'un bon"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA B2B"
msgstr "SEPA B2B"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA Direct Debit"
msgstr "Prélèvement automatique SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA core"
msgstr "SEPA classique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of foreign bank notes"
msgstr "Vente de billets de banque étrangers"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of gold/pieces under usual reserve"
msgstr "Vente d'or/pièces sous réserve habituelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of securities"
msgstr "Vente de titres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of traveller’s cheque"
msgstr "Vente d'un chèque de voyage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Second credit of unpaid cheque"
msgstr "Deuxième crédit de chèque impayé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Securities"
msgstr "Titres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Separately charged costs and provisions"
msgstr "Frais et provisions imputés séparément"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement Date"
msgstr "Date de règlement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement credit cards"
msgstr "Règlement de cartes de crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of bank acceptances"
msgstr "Règlement d'acceptations de banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of discount bank acceptance"
msgstr "Règlement d'une acceptation bancaire d'escompte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of fixed advance"
msgstr "Règlement d'une avance à terme fixe"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of instalment credit"
msgstr "Règlement du crédit à tempérament"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of mortgage loan"
msgstr "Règlement de prêt hypothécaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of securities"
msgstr "Règlement de titres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Share option plan – exercising an option"
msgstr "Plan d'options sur actions - exercice d'une option"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Short-term loan"
msgstr "Prêt à court terme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount with detailed data"
msgstr "Montant simple avec données détaillées"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount without detailed data"
msgstr "Montant simple sans données détaillées"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Special charge for safe custody"
msgstr "Frais spéciaux de garde"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split Transactions"
msgstr "Diviser les transactions"

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split collective payments for CODA files"
msgstr "Paiements collectifs fractionnés pour les fichiers CODA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot purchase of foreign exchange"
msgstr "Achat au comptant de devises étrangères"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot sale of foreign exchange"
msgstr "Vente au comptant de devises étrangères"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Standing order"
msgstr "Ordre permanent"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Structured format communication"
msgstr "Communication au format structuré"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription fee"
msgstr "Frais d'abonnement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription to securities"
msgstr "Abonnement à des titres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subsidy"
msgstr "Subsides"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Surety fee"
msgstr "Frais de cautionnement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "TINA"
msgstr "TINA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tender"
msgstr "Offre"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tenders"
msgstr "Offres"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term Investments"
msgstr "Investissements à terme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term loan"
msgstr "Crédit à terme"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Terminal cash deposit"
msgstr "Dépôt en espèces au terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Trade information"
msgstr "Informations commerciales"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer"
msgstr "Transfert"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer from your account"
msgstr "Virement depuis votre compte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour"
msgstr "Virement en votre faveur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour – initiated by the bank"
msgstr "Virement en votre faveur – initié par la banque"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer to your account"
msgstr "Virement sur votre compte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Travel insurance premium"
msgstr "Prime d'assurance voyage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type Direct Debit"
msgstr "Type prélèvement automatique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of R transaction"
msgstr "Type de transaction R"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of structured communication not supported: "
msgstr "Type de communication structurée non pris en charge :"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Undefined transaction"
msgstr "Transaction non définie"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable reimbursement"
msgstr "Remboursement inexécutable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable transfer order"
msgstr "Mandat de virement inexécutable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unloading Proton"
msgstr "Déchargement de Proton"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid commercial paper"
msgstr "Effet de commerce non payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid debt"
msgstr "Dette non payée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign bill"
msgstr "Facture fournisseur non payée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign cheque"
msgstr "Chèque étranger non payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid postal order"
msgstr "Mandat de poste non payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid voucher"
msgstr "Voucher non payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unsupported bank account structure "
msgstr "Structure de compte bancaire non prise en charge"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Upload of prepaid card"
msgstr "Déchargement d'une carte prépayée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Value (date) correction"
msgstr "Correction de valeur (date)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Various transactions"
msgstr "Transactions diverses"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant"
msgstr "Mandat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant fallen due"
msgstr "Mandat arrivé à échéance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Writ service fee"
msgstr "Frais de service d'ordonnance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Wrong CODA code"
msgstr "Mauvais code CODA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your certified cheque"
msgstr "Votre chèque certifié"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue"
msgstr "Votre émission"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue circular cheque"
msgstr "Votre chèque circulaire d'émission"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your purchase bank cheque"
msgstr "Chèque bancaire de votre achat"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment hire-purchase and similar claims"
msgstr "Votre remboursement location-vente et créances assimilées"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment instalment credits"
msgstr "Vos crédits d'échéance de remboursement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment mortgage loan"
msgstr "Votre remboursement de prêt hypothécaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repurchase of issue"
msgstr "Votre rachat d'émission"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "account number of the credit"
msgstr "numéro de compte du crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount (equivalent in foreign currency)"
msgstr "montant (équivalent en devise étrangère)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of interest"
msgstr "montant des intérêts"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of the bill"
msgstr "montant de la facture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount on which % is calculated"
msgstr "montant sur lequel le % est appliqué"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount"
msgstr "montant de base"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount of the calculation"
msgstr "montant de base du calcul"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cancellation"
msgstr "annulation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "card scheme"
msgstr "système de carte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "company number"
msgstr "numéro de société"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conformity code or blank"
msgstr "code de conformité ou vide"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conventional maturity date"
msgstr "date d'échéance conventionnelle"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative"
msgstr "cumulatif"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative on network"
msgstr "cumulatif sur le réseau"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "currency"
msgstr "devise"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date"
msgstr "date"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of first transaction"
msgstr "date de la première transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of issue of the bill"
msgstr "date d'émission de la facture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of last transaction"
msgstr "date de la dernière transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of transaction"
msgstr "date de transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor disagrees"
msgstr "le débiteur n'est pas d'accord"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor’s account problem"
msgstr "problème de compte débiteur"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit amount"
msgstr "montant du dépôt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit number"
msgstr "numéro de dépôt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "diesel"
msgstr "diesel"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "distribution sector"
msgstr "secteur de la distribution"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "domestic fuel oil"
msgstr "fioul domestique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "end date"
msgstr "date de fin"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in EUR"
msgstr "équivalent en EUR"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in the currency of the account"
msgstr "équivalent dans la devise du compte"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "europremium"
msgstr "europremium"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "exchange rate"
msgstr "taux de change"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "extension zone of account number of the credit"
msgstr "zone d'extension du numéro de compte du crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "fuel"
msgstr "carburant"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "guarantee number (no. allocated by the bank)"
msgstr "numéro de garantie (n° attribué par la banque)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of payment"
msgstr "heure de paiement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of transaction"
msgstr "heure de transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification number"
msgstr "numéro d'identification"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification of terminal"
msgstr "identification du terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest"
msgstr "intérêt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rate"
msgstr "taux d'intérêt"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rates, calculation basis"
msgstr "taux d'intérêt, base de calcul"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "invoice number"
msgstr "numéro de facture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "issuing institution"
msgstr "institution émettrice"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "last (recurrent)"
msgstr "dernier (récurrent)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "lubricants"
msgstr "lubrifiants"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date"
msgstr "date d'échéance"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date of the bill"
msgstr "date d'échéance de la facture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "message (structured of free)"
msgstr "message (structuré ou libre)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum"
msgstr "minimum"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum applicable"
msgstr "minimale applicable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum not applicable"
msgstr "minimale non applicable"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum rate"
msgstr "taux minimal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "new balance of the credit"
msgstr "nouveau total du crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "nominal interest rate or rate of charge"
msgstr "taux d'intérêt nominal ou taux de commission"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of days"
msgstr "nombre de jours"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of the bill"
msgstr "numéro de la facture"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "old balance of the credit"
msgstr "ancien solde du crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "one-off"
msgstr "unique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount"
msgstr "montant initial"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount (given by the customer)"
msgstr "montant initial (donné par le client)"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "other types"
msgstr "autres types"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "paid"
msgstr "payé"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "payment day"
msgstr "jour de paiement"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percent"
msgstr "pourcent"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percentage"
msgstr "pourcentage"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period from {} to {}"
msgstr "période de {} à {}"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period number"
msgstr "numéro de période"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "petrol"
msgstr "essence"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium 99+"
msgstr "premium 99+"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium plus 98 oct"
msgstr "premium plus 98 oct"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium with lead substitute"
msgstr "premium avec substitut de plomb"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "product code"
msgstr "code article"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "rate"
msgstr "taux"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reason not specified"
msgstr "raison non précisée"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "recurrent"
msgstr "récurrent"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction"
msgstr "référence de la transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction on credit account"
msgstr "référence de l'opération sur le compte de crédit"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "refund"
msgstr "avoir"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "regular unleaded"
msgstr "ordinaire sans plomb"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reject"
msgstr "rejeter"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "return"
msgstr "retour"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal"
msgstr "extourner"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal of purchases"
msgstr "extourne d'achats"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of first transaction"
msgstr "numéro de séquence de la première transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of last transaction"
msgstr "numéro de séquence de la dernière transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of transaction"
msgstr "numéro de séquence de la transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of validation"
msgstr "numéro de séquence de validation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "starting date"
msgstr "date de début"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "technical problem"
msgstr "problème technique"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "teledata"
msgstr "teledata"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "term in days"
msgstr "durée en jours"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "terminal number"
msgstr "numéro du terminal"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "transaction type"
msgstr "type de transaction"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "undefined"
msgstr "Indéfini"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unit price"
msgstr "prix unitaire"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unset"
msgstr "non défini"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unspecified"
msgstr "non spécifié"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "validation date"
msgstr "date de validation"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "volume"
msgstr "volume"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "withdrawal"
msgstr "retrait"
