<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_template_form_view_invoice_policy_inherit_sale_planning" model="ir.ui.view">
        <field name="name">product.template.inherit.sale.planning.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="sale.product_template_form_view"/>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_tooltip']" position="after">
                <label for="planning_enabled" attrs="{'invisible': ['|', ('sale_ok', '=', False), ('type', '!=', 'service')]}"/>
                <div attrs="{'invisible': ['|', ('sale_ok', '=', False), ('type', '!=', 'service')]}">
                    <field name="planning_enabled" class="oe_inline"/>
                    <span attrs="{'invisible': [('planning_enabled', '=', False)]}" class="oe_inline me-2">
                        as
                    </span>
                    <field name="planning_role_id"
                           attrs="{'invisible': [('planning_enabled', '=', False)], 'required': [('planning_enabled', '=', True)]}"
                           class="oe_inline" nolabel="1" placeholder=" e.g. Consultant"/>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
