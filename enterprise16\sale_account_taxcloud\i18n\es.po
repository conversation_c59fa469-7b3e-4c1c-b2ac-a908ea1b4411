# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_taxcloud
# 
# Translators:
# <PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Go to Settings."
msgstr "Vaya a los ajustes"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "¿Taxcloud está configurado?"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"Introduzca sus credenciales de TaxCloud para calcular sus tipos impositivos "
"automáticamente."

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Pago anticipado para factura de ventas"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de pedido de venta"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Tax"
msgstr "Impuesto"

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "No se han podido recuperar los impuestos de TaxCloud:"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Update taxes"
msgstr "Actualizar impuestos"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Use TaxCloud API"
msgstr "Usar la API de TaxCloud"

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"Se usa para determinar si se debe aconsejar o no al usuario que configure "
"TaxCloud."
