<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_partner_property_form" model="ir.ui.view">
        <field name="name">res.partner.property.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.view_partner_property_form"/>
        <field name="arch" type="xml">
            <field name="property_supplier_payment_term_id" position="after">
                <field name="property_payment_method_id" options="{'no_open': True, 'no_create': True}" groups="account.group_account_invoice,account.group_account_readonly"/>
            </field>
        </field>
    </record>
</odoo>
