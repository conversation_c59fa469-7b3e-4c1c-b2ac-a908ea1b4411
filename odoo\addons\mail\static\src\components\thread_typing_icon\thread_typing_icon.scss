// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ThreadTypingIcon_separator {
    min-width: 1px;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_ThreadTypingIcon_dot {
    &.o-sizeMedium {
        width: $o-mail-thread-typing-icon-size-medium;
        height: $o-mail-thread-typing-icon-size-medium;
    }

    &.o-sizeSmall {
        width: $o-mail-thread-typing-icon-size-small;
        height: $o-mail-thread-typing-icon-size-small;
    }
}

// ------------------------------------------------------------------
// Animation
// ------------------------------------------------------------------

.o_ThreadTypingIcon_dot.o-animationBounce {

    // Note: duplicated animation because dependent on size, and current SASS version doesn't support var()
    &.o-sizeMedium {
        animation: o_ThreadTypingIcon_dot_animationBounce_sizeMedium_animation 1.5s linear infinite;
    }

    &.o-sizeSmall {
        animation: o_ThreadTypingIcon_dot_animationBounce_sizeSmall_animation 1.5s linear infinite;
    }

    &.o_ThreadTypingIcon_dot2 {
        animation-delay: -1.35s;
    }

    &.o_ThreadTypingIcon_dot3 {
        animation-delay: -1.2s;
    }
}

.o_ThreadTypingIcon_dot.o-animationPulse {
    animation: o_ThreadTypingIcon_dot_animationPulse_animation 1.5s linear infinite;

    &.o_ThreadTypingIcon_dot2 {
        animation-delay: -1.35s;
    }

    &.o_ThreadTypingIcon_dot3 {
        animation-delay: -1.2s;
    }
}

@keyframes o_ThreadTypingIcon_dot_animationBounce_sizeMedium_animation {
    0%, 40%, 100% {
        transform: initial;
    }
    20% {
        transform: translateY(-$o-mail-thread-typing-icon-size-medium);
    }
}

@keyframes o_ThreadTypingIcon_dot_animationBounce_sizeSmall_animation {
    0%, 40%, 100% {
        transform: initial;
    }
    20% {
        transform: translateY(-$o-mail-thread-typing-icon-size-small);
    }
}

@keyframes o_ThreadTypingIcon_dot_animationPulse_animation {
    0%, 40%, 100% {
        opacity: initial;
    }
    20% {
        opacity: 25%;
    }
}
