# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_facebook
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <j<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "<b class=\"d-block mb-2\">Facebook Page</b>"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_live_post__facebook_post_id
msgid "Actual Facebook ID of the post"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App ID"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App Secret"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_res_config_settings__facebook_use_own_account
msgid ""
"Check this if you want to use your personal Facebook Developer Account "
"instead of the provided one."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_comments_count
msgid "Comments"
msgstr "Comments"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__display_facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__display_facebook_preview
msgid "Display Facebook Preview"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields.selection,name:social_facebook.selection__social_media__media_type__facebook
#: model:social.media,name:social_facebook.social_media_facebook
msgid "Facebook"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_access_token
msgid "Facebook Access Token"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_account_id
msgid "Facebook Account ID"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_app_id
msgid "Facebook App ID"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_client_secret
msgid "Facebook App Secret"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_author_id
msgid "Facebook Author ID"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Facebook Comments"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "Facebook Developer Account"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_access_token
msgid ""
"Facebook Page Access Token provided by the Facebook API, this should never be set manually.\n"
"            It's used to authenticate requests when posting to or reading information from this account."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_account_id
msgid ""
"Facebook Page ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_post_id
msgid "Facebook Post ID"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_preview
msgid "Facebook Preview"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token or it may have expired."
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Facebook did not provide a valid access token."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_is_event_post
msgid "Is event post"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_likes_count
#, python-format
msgid "Likes"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_media__media_type
msgid "Media Type"
msgstr ""

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_mentions
msgid "Page Mentions"
msgstr ""

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_posts
msgid "Page Posts"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Post Image"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid ""
"Post not found. It could be because the post has been deleted on the Social "
"Platform."
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Published by Facebook Page •"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_reach
msgid "Reach"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_shares_count
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Shares"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_account
msgid "Social Account"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_live_post
msgid "Social Live Post"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_media
msgid "Social Media"
msgstr "Social Media"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post
msgid "Social Post"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post_template
msgid "Social Post Template"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream
msgid "Social Stream"
msgstr ""

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream_post
msgid "Social Stream Post"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator. "
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
#: code:addons/social_facebook/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "Nezināms"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_use_own_account
msgid "Use your own Facebook Account"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_user_likes
msgid "User Likes"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "Views"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_live_post.py:0
#: code:addons/social_facebook/models/social_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: %s)."
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "added an event"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "• <i class=\"fa fa-globe\"/>"
msgstr ""
