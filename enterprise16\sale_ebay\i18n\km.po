# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_ebay
# 
# Translators:
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" If you want to set quantity to 0, the Out Of Stock option should be enabled"
" and the listing duration should set to Good 'Til Canceled"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" You need to have at least 2 variations selected for a multi-variations listing.\n"
" Or if you try to delete a variation, you cannot do it by unselecting it. Setting the quantity to 0 is the safest method to make a variation unavailable."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_10
msgid "10 Days"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_3
msgid "3 Days"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_30
msgid "30 Days (only for fixed price)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_5
msgid "5 Days"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_7
msgid "7 Days"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Storage</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Synchronization</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Account</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">eBay Marketplace Account Deletion/Closure "
"Notifications</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Options</span>"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "ធ្វើអោយសកម្មនូវរូបិយបណ្ណ័ផ្សេងទៀត"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Add other countries"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"All the quantities must be greater than 0 or you need to enable the Out Of "
"Stock option."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_best_offer
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_best_offer
msgid "Allow Best Offer"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__chinese
msgid "Auction"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_buy_it_now_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_buy_it_now_price
msgid "Buy It Now Price"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Cancel"
msgstr "លុបចោល"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_id
msgid "Category"
msgstr "ប្រភេទ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_2_id
msgid "Category 2 (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_id
msgid "Category ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_parent_id
msgid "Category Parent ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_type
msgid "Category Type"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__code
msgid "Code"
msgstr "កូដ"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_config_settings
msgid "Config Settings"
msgstr "កំណត់រចនាសម្ព័ន្ធ"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Configure The eBay Integrator Now"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_country_all_form
msgid "Countries"
msgstr "ប្រទេស"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Countries & Currencies"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_country
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_country
msgid "Country"
msgstr "ប្រទេស"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_currency
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Currency"
msgstr "រូបិយប័ណ្ណ"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__store
msgid "Custom Store Category"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Describe the product characteristics..."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_template_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_template_id
msgid "Description Template"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_dev_id
msgid "Developer Key"
msgstr "កូនសោរអភិវឌ្ឍន៌"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Documentation"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_duration
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_duration
msgid "Duration"
msgstr "អំឡុងពេល"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay Synchronisation could not confirm because of the following error:\n"
"%s"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay could not synchronize order:\n"
"%s"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_orders_sync
msgid "Ebay: get new orders"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_recovery
msgid "Ebay: orders recovery"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_stock_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_stock_sync
msgid "Ebay: synchronise stock"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_categories_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_categories
msgid "Ebay: update categories"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "End Item's Listing"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
" No Variant Set To Be Listed On eBay."
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
"'%s'"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Fixed Price"
msgstr "តំលៃថេរ"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__fixedpriceitem
msgid "Fixed price"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__full_name
msgid "Full Name"
msgstr "ឈ្មោះ​ពេញ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_gallery_plus
msgid "Gallery Plus"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Generate Token"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__gtc
msgid "Good 'Til Cancelled (only for fixed price)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,help:sale_ebay.field_res_currency__ebay_available
msgid "If activated, can be used for eBay."
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Import eBay config data and sync transactions"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Impossible to revise a listing into a multi-variations listing.\n"
" Create a new listing."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_item_condition_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_item_condition_id
msgid "Item Condition"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_last_sync
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_last_sync
msgid "Last update"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__leaf_category
msgid "Leaf Category"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link Existing Listing"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Link With Existing eBay Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_ebay_link_listing
msgid "Link with Existing eBay Listing"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link with eBay Listing"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "List Item on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_type
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_type
msgid "Listing Type"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_location
msgid "Location"
msgstr "តំបន់"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_account_deletion_endpoint
msgid "Marketplace account deletion notification endpoint"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode"
msgstr "របៀប"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode and credentials"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid "No Business Policies"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__ebay
msgid "Official eBay Category"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "One parameter is missing."
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Or the condition is not compatible with the category."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_out_of_stock
msgid "Out Of Stock"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_payment_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_payment_policy_id
msgid "Payment Policy"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"Please provide your ebay production keys before enabling the account "
"deletion notifications."
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Policies"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_id
msgid "Policy ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_private_listing
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_private_listing
msgid "Private Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_template
msgid "Product"
msgstr "ផលិតផល"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Product Categories"
msgstr "ប្រភេទផលិតផល"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_product
msgid "Product Variant"
msgstr "ការផ្លាស់ប្តូរផលិតផល"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "Product created from eBay transaction %s"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__prod
msgid "Production"
msgstr "ផលិតកម្ម"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_app_id
msgid "Production App Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_cert_id
msgid "Production Cert Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_token
msgid "Production Token"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_use
msgid "Publish On eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity
msgid "Quantity On eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity_sold
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity_sold
msgid "Quantity Sold"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Relist Item"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_return_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_return_policy_id
msgid "Return Policy"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Revise Item"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_sale_order
msgid "Sales Order"
msgstr "លក់តាមការបញ្ជាទិញ"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team"
msgstr "ក្រុមលក់"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team to manage eBay sales"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__sand
msgid "Sandbox"
msgstr "សាកល្បង"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_app_id
msgid "Sandbox App Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_cert_id
msgid "Sandbox Cert Key"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_token
msgid "Sandbox Token"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Sell on eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_shipping_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_shipping_policy_id
msgid "Shipping Policy"
msgstr "គោលនយោបាយដឹកជញ្ជូន"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_start_date
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_start_date
msgid "Start Date"
msgstr "កាលបរិច្ឆេទ​ចាប់ផ្តើម"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_price
msgid "Starting Price for Auction"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Storage location of your products"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_id
msgid "Store Category (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_2_id
msgid "Store Category 2 (Optional)"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_subtitle
msgid "Subtitle"
msgstr "ចំណងជើងតូច"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__short_summary
msgid "Summary"
msgstr "សង្ខេប"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sync now"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Chose The Following Delivery Method :\n"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Posted :\n"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"The python 'cryptography' module is not installed on your server.\n"
"It is necessary to support eBay account deletion notifications, please contact your system administrator to install it."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_subtitle
msgid ""
"The subtitle is restricted to 55 characters. Fees can be claimed by eBay for"
" this feature"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_title
msgid "The title is restricted to 80 characters"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_category_2_id
msgid ""
"The use of a secondary category is not allowed on every eBay sites. Fees can"
" be claimed by eBay for this feature"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"There is no last synchronization date in your System Parameters. Create a "
"System Parameter record with the key \"ebay_last_sync\" and the value set to"
" the date of the oldest order you wish to synchronize in the format \"YYYY-"
"MM-DD\"."
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"This function should not be called with a range of more than 30 days, as "
"eBay does not handle longer timespans. Instead use synchronize_orders which "
"split in as many calls as needed."
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_partner.py:0
#, python-format
msgid ""
"This is an automated notification as a deletion request has been received from eBay concerning the account \"%s (%s)\". The account has been anonymised already and his portal access revoked (if they had any).\n"
"\n"
"However, personal information might remain in linked documents, please review them according to laws that apply."
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_title
msgid "Title"
msgstr "ចំណងជើង​"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_stock_picking
msgid "Transfer"
msgstr "ផ្ទេរ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_type
msgid "Type"
msgstr "ប្រភេទ"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_sync_stock
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_sync_stock
msgid "Use Stock Quantity"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_use
msgid "Use eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,field_description:sale_ebay.field_res_currency__ebay_available
msgid "Use on eBay"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Variants"
msgstr "វ៉ាដ្យង់"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_verification_token
msgid "Verification Token"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_zip_code
msgid "Zip"
msgstr "ពង្រួម"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_category
msgid "eBay Category"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_description
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_description
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "eBay Description"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_domain
msgid "eBay Environment"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_fixed_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_fixed_price
msgid "eBay Fixed Price"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_id
msgid "eBay ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_item_condition
msgid "eBay Item Condition"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_link_listing
msgid "eBay Link Listing"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__ebay_id
msgid "eBay Listing ID"
msgstr ""

#. module: sale_ebay
#: model:mail.activity.type,summary:sale_ebay.ebay_GDPR_notification
msgid "eBay Odoo connector notification - account deletion"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_policy
msgid "eBay Policy"
msgstr ""

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_site
msgid "eBay Site"
msgstr "វេបសាយ eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_status
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_status
msgid "eBay Status"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_partner__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_res_users__ebay_id
msgid "eBay User ID"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_variant_url
msgid "eBay Variant URL"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_site
msgid "eBay Website"
msgstr ""

#. module: sale_ebay
#: model:mail.activity.type,name:sale_ebay.ebay_GDPR_notification
msgid "eBay connector: account deletion notification"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay documentation"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid ""
"eBay error: Impossible to synchronize the categories. \n"
"'%s'"
msgstr ""

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "eBay is unreachable. Please try again later."
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay parameters"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"eBay requires supporting customer account deletion/closure notifications.\n"
"                                Please follow the"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_url
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_url
msgid "eBay url"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_end_items_listings
msgid "eBay: End product listings"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_list_items
msgid "eBay: List products"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_relist_items
msgid "eBay: Relist products"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_revise_items
msgid "eBay: Revise products"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_unlink_items_listings
msgid "eBay: Unlink product listings"
msgstr ""

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_ebay_update_carrier
msgid "eBay: Update carrier information"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_currency
msgid "ebay Currency"
msgstr ""

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sales_team
msgid "ebay Sales Team"
msgstr ""

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "to setup this mechanism."
msgstr ""

#. module: sale_ebay
#: code:addons/sale_ebay/tools/ebaysdk.py:0
#, python-format
msgid ""
"An unexpected error occured from eBay.\n"
"Please check your credentials and try again later."
msgstr ""
