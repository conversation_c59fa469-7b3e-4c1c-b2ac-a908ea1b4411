odoo.define("hotel_room_dashboard_view.frozen_header", function (require) {
    "use strict";
    
    var core = require("web.core");
    var ajax = require("web.ajax");
    
    $(document).ready(function() {
        // Apply frozen header enhancements after a short delay to ensure calendar is loaded
        setTimeout(function() {
            setupFrozenHeader();
        }, 1000);
        
        function setupFrozenHeader() {
            // Set up an observer to ensure the header stays fixed after dynamic content changes
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length) {
                        applyFrozenHeaderStyles();
                    }
                });
            });
            
            // Run initial setup
            applyFrozenHeaderStyles();
            
            // Observe calendar for changes
            var calendarEl = document.getElementById('booking_calendar');
            if (calendarEl) {
                observer.observe(calendarEl, { childList: true, subtree: true });
            }
            
            // Re-apply styles when window is resized
            $(window).on('resize', function() {
                applyFrozenHeaderStyles();
            });
            
            // Apply styles when dashboard is reloaded
            $(document).on('click', '#dashboard_reload', function() {
                setTimeout(applyFrozenHeaderStyles, 500);
            });
            
            // Apply styles when shop selection changes
            $('#shops').on('change', function() {
                setTimeout(applyFrozenHeaderStyles, 1000);
            });
        }
        
        function applyFrozenHeaderStyles() {
            // Calculate appropriate max height for calendar container
            var windowHeight = $(window).height();
            var calendarTop = $('#calendar-wrapper').offset() ? $('#calendar-wrapper').offset().top : 250;
            var maxHeight = windowHeight - calendarTop - 50; // 50px for padding/margin
            
            // Apply max height to calendar wrapper
            $('#calendar-wrapper').css({
                'max-height': maxHeight + 'px',
                'overflow-y': 'auto'
            });
            
            // Ensure all headers are properly fixed
            $('.fc-head, .fc-day-header').css({
                'position': 'sticky',
                'top': '0',
                'z-index': '10',
                'background-color': 'white'
            });
            
            // Fix resource headers on the left side
            $('.fc-resource-area .fc-cell-content').css({
                'position': 'sticky',
                'left': '0',
                'background-color': 'white',
                'z-index': '8'
            });
            
            // Make sure day headers are sticky at the top
            $('.fc-time-area .fc-divider').css({
                'position': 'sticky',
                'top': '0',
                'z-index': '9',
                'background-color': 'white'
            });
            
            // Adjust scrolling behavior for content
            $('.fc-scroller').css({
                'height': 'auto !important',
                'overflow-y': 'visible !important'
            });
            
            // Add a class to indicate scrollable areas
            $('.fc-scroller').addClass('scrollable-calendar-content');
        }
    });
}); 