# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_demo
# 
# Translators:
# <PERSON><PERSON><PERSON> <naws<PERSON><EMAIL>>, 2022
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_youtube_account
msgid "Channel: My Company"
msgstr "Канал: Моя компания"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4_product_template
msgid "Desk"
msgstr "Стол"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4b_product_template
msgid "Desk Variant B"
msgstr "Настольный вариант B"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4c_product_template
msgid "Desk Variant C"
msgstr "Настольный вариант C"

#. module: social_demo
#: model:product.template,name:social_demo.product_product_4d_product_template
msgid "Desk Variant D"
msgstr "Вариант стола D"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_instagram_account
msgid "Instagram Posts: My Company"
msgstr "Посты в Instagram: Моя компания"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search
msgid "Keyword: #mycompany"
msgstr "Ключевое слово: #mycompany"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_search_competitor
msgid "Keyword: #mycompetitor"
msgstr "Ключевое слово: #мойконкурент"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_facebook_page
msgid "Page Posts: My Company"
msgstr "Страница сообщений: Моя компания"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_linkedin_page
msgid "Posts: My Company"
msgstr "Posts: Моя компания"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_account
msgid "Social Account"
msgstr "Учетная запись соц. сети"

#. module: social_demo
#: model:utm.campaign,title:social_demo.social_utm_campaign
msgid "Social Campaign"
msgstr "Социальная кампания"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_live_post
msgid "Social Live Post"
msgstr "Пост в социальных сетях"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_post
msgid "Social Post"
msgstr "Пост в социальной сети"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream
msgid "Social Stream"
msgstr "Источник"

#. module: social_demo
#: model:ir.model,name:social_demo.model_social_stream_post
msgid "Social Stream Post"
msgstr "Пост источника соц. сети"

#. module: social_demo
#: model:social.stream,name:social_demo.social_stream_twitter_account
msgid "Tweets of: My Company"
msgstr "Твиты: Моя компания"

#. module: social_demo
#: model:ir.model,name:social_demo.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Кампания"

#. module: social_demo
#. odoo-javascript
#: code:addons/social_demo/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "You cannot upload videos in demo mode."
msgstr "Вы не можете загружать видео в демонстрационном режиме."
