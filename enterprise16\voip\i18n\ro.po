# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>_nexterp, 2024
# <PERSON><PERSON>, 2024
# <PERSON>yall Kindmurr, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Lyall Kindmurr, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr "# de Cazuri"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "%(number of missed calls)s missed calls"
msgstr "%(number of missed calls)s apeluri pierdute"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "1 missed call"
msgstr "1 apel pierdut"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "2 missed calls"
msgstr "2 apeluri pierdute"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"A hardware error occured while trying to access audio recording device. "
"Please make sure your drivers are up to date and try again."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Accept"
msgstr "Acceptă"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr "Activitate"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Add to Call Queue"
msgstr "Adăugare la Coada de Apeluri"

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr "Adăugare la coada de apeluri"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occured involving the audio recording device "
"(%(errorName)s):</br>%(errorMessage)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occurred during the instantiation of the User Agent:</br></br> "
"%(error message)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Are you sure that you want to close this website? There's a call ongoing."
msgstr ""
"Sunteți sigur că doriți să închideți acest site? Există un apel în "
"desfășurare."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr "Întreabă"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr "Backspace"

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr "Apeluri"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr "Data Apel"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr "Nume Apel"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call duration: %(min)smin %(sec)ssec"
msgstr "Durata apelului: %(min)smin %(sec)ssec"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Call rejected (reason: \"%(reasonPhrase)s\")"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr "Apel către%s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/voip_service.js:0
#, python-format
msgid "Calling %s"
msgstr "Apelare %s"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr "Dată Apleuri"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Cancel"
msgstr "Anulează"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr "Anulare activitate"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr "Anulat"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please grant it and try again. Otherwise, make sure this website"
" runs over HTTPS and that your browser is not set to deny access to media "
"devices."
msgstr ""
"Nu se poate accesa dispozitivul de înregistrare audio. Dacă ați refuzat "
"accesul la microfon, vă rugăm să îl acordați și să încercați din nou. În caz"
" contrar, asigurați-vă că acest site rulează prin HTTPS și că browserul dvs."
" nu este setat să refuze accesul la dispozitivele media."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone must be used\n"
"        "
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr "Click pentru deblocare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr "Închide"

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Connecting..."
msgstr "Conectare..."

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr "Contact"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Contacts"
msgstr "Contacte"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
msgid "Created on"
msgstr "Creat în"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr "Data creării"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr "Client"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr "Dată"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr "Demo"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__direction
msgid "Direction"
msgstr "Direcție"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr "Afișare Panou Apelare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr "Document"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr "Nu Reprograma"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr "Data scadenței"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr "Durată"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr "Durată în minute."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Edit"
msgstr "Editare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "End Call"
msgstr "Închidere Apel"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Enter number or name"
msgstr "Introduceți un număr sau un nume"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr "Introduceți un număr..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Failed to start the user agent. The URL of the websocket server may be "
"wrong. Please have an administrator verify the websocket server URL in the "
"General Settings."
msgstr ""
"Nu s-a reușit pornirea agentului utilizator. URL-ul serverului websocket "
"poate fi greșit. Vă rugăm să solicitați unui administrator verificarea URL-"
"ului serverului websocket în Setările Generale."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr ""
"Oferă ordinea secvenței atunci când se afișează o listă de apeluri "
"telefonice."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr "Grupează după"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr "Închideți, dar țineți apelul la coadă"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr "Reținut"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr ""
"Dacă este activată, plasarea unui apel în Odoo va transfera apelul către "
"\"Numărul dispozitivului extern\". Utilizați această opțiune pentru a plasa "
"apelul în Odoo, dar pentru a-l gestiona de pe un alt dispozitiv - de "
"exemplu, telefonul de pe biroul dumneavoastră."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__external_device_number
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr "În 1 săptămână"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr "În 15 zile"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr "În 2 luni"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr "În Coada apelurilor"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr "În apel pentru:"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""
"Pentru a urmări apelul, puteți declanșa o solicitare pentru \n"
"        un alt apel, întâlnire."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__incoming
msgid "Incoming"
msgstr "Intrări"

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr "Apel de la %s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr "Apel de la %s (%s)"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Incoming call from..."
msgstr "Apel de la..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "Se află în coada de apeluri"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr "Tastatură"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr "Apel telefonic ultima oară văzut"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr "Activitate Legată"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr "Mesaj legat de chat"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr "Apel telefonic Voip legat"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr "Înregistrați rezumatul unui apel telefonic"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr "Apel telefonic înregistrat"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Make a call using:"
msgstr "Apelați folosind:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marchează ca efectuat"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr "Apel Ratat"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr "Apel Ratat de la %s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr "Mobil"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mobile:"
msgstr "Mobil:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr "Mut"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr "Apelurile mele telefonice"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Next activities"
msgstr "Următoarele activități"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr "Nu a fost reținut"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr "Notă"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""
"Odoo vă permite să vă conectați apelurile de intrare în decurs pentru a urmări\n"
"    istoricul comunicarii cu un client sau pentru a informa alt\n"
"        membru al echipei."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__outgoing
msgid "Outgoing"
msgstr "Ieșiri"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr "IP Server PBX"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr "Adresa PBX sau Websocket lipsește. Verificați setările."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr "În așteptare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Phone:"
msgstr "Telefon:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr "Detalii Apel telefonic"

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr "Apeluri telefonice"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr "Vă rugăm să acceptați utilizarea microfonului."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr "Producție"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Recent"
msgstr "Recent"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr "Actualizare Panou"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject"
msgstr "Respinge"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject call"
msgstr "Respingere apel"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr "Respingeți toate apelurile primite"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr "Respins"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr "Apeluri Refuzate de la %s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Remember ?"
msgstr "Țineți minte ?"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr "Eliminare din coada apelurilor"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr "Eliminare din coadă"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr "Responsabil"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr "Sună..."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from your database"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr "Planificare Activitate Nouă"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr "Planifică activitate"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Next"
msgstr "Programează în continuare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search"
msgstr "Caută"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr "Cauta Apeluri telefonice"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr "Trimite e-mail"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone"
" number set: %(record_names)s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr "Dată Specifică"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr "Timp începere"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr "Stare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr "Sumar"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Take call"
msgstr "Răspunde Apel"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP address of your PBX Server"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr "URL-ul WebSocket-ului dvs."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The connection cannot be made.</br> Please check your configuration."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.</br> (Reason "
"received: %(reasonPhrase)s)"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_secret
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The person you try to contact is currently unavailable."
msgstr ""
"Persoana pe care încercați să o contactați este momentan indisponibilă."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr "Apelul telefonic nu are număr"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""
"Starea este setată la De Făcut, când este creat un apel.\n"
"Când apelul s-a încheiat, starea este setată pe Susținut\n"
"Dacă apelul nu mai este aplicabil, starea poate fi setată pe Anulat."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_username
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr "De făcut"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr "Mâine"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr "Transfer"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr "Neatribuit"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr "Operator"

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr "Setări utilizator"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr "Apel telefonic VOIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr "Asistent de jurnal de apeluri telefonice VOIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr "Apeluri telefonice VOIP după raportul utilizatorului"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
#, python-format
msgid "VoIP"
msgstr "VoIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr "Mediul VoIP"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Voip"
msgstr "Voip"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr "WebSocket"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr "Sunteți deja într-un apel"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid ""
"You must allow the access to the microphone on your device. Otherwise, the "
"VoIP call receiver will not hear you."
msgstr ""
"Trebuie să permiteți accesul la microfonul de pe dispozitiv. În caz contrar,"
" receptorul de apel VoIP nu vă va auzi."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr "Browserul dvs. nu poate suporta WebRTC. Verificați configurația."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr ""
"Certificările dvs. nu sunt setate corect. Vă rugăm să contactați "
"administratorul."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr "min"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr "sec"
