<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="documents_folder" model="documents.folder">
            <field name="name">Renovation Projects</field>
        </record>

        <record id="documents_folder_facet_1" model="documents.facet">
            <field name="name">Status</field>
            <field name="sequence">1</field>
            <field name="folder_id" ref="documents_folder"/>
        </record>
        <record id="documents_folder_facet_1_tag_1" model="documents.tag">
            <field name="name">New/Unsorted</field>
            <field name="sequence">1</field>
            <field name="facet_id" ref="documents_folder_facet_1"/>
        </record>
        <record id="documents_folder_facet_1_tag_2" model="documents.tag">
            <field name="name">In Use</field>
            <field name="sequence">2</field>
            <field name="facet_id" ref="documents_folder_facet_1"/>
        </record>
        <record id="documents_folder_facet_1_tag_3" model="documents.tag">
            <field name="name">Done/Archived</field>
            <field name="sequence">3</field>
            <field name="facet_id" ref="documents_folder_facet_1"/>
        </record>
    
        <record id="documents_folder_template" model="documents.folder">
            <field name="name">Template</field>
            <field name="parent_folder_id" ref="documents_folder"/>
        </record>
    
        <record id="documents_folder_template_child_1" model="documents.folder">
            <field name="name">Plans</field>
            <field name="parent_folder_id" ref="documents_folder_template"/>
        </record>
    
        <record id="documents_folder_template_child_2" model="documents.folder">
            <field name="name">Photos</field>
            <field name="parent_folder_id" ref="documents_folder_template"/>
        </record>
    
        <record id="documents_folder_template_child_3" model="documents.folder">
            <field name="name">Miscellaneous</field>
            <field name="parent_folder_id" ref="documents_folder_template"/>
        </record>

        <record id="product_1" model="product.template">
            <field name="name">Renovation Architect (Workspace Template)</field>
            <field name="standard_price">30</field>
            <field name="list_price">45</field>
            <field name="detailed_type">service</field>
            <field name="service_tracking">project_only</field>
            <field name="template_folder_id" ref="documents_folder_template"/>
        </record>
  
        <record id="sale_order_1" model="sale.order">
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="client_order_ref">DOC</field>
        </record>
    
        <record id="sale_order_1_sale_line_1" model="sale.order.line">
            <field name="order_id" ref="sale_order_1"/>
            <field name="sequence" eval="1"/>
            <field name="product_id" search="[('product_tmpl_id', '=', ref('product_1'))]"/>
            <field name="product_uom_qty">30</field>
        </record>
    
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_1')]]" context="{'action_no_send_mail': True}"/>

        <!-- Share workspace so that its documents are accessible in the portal -->
        <record id="sale_order_1_documents_share_link" model="documents.share">
            <field name="name">Customer Share Link</field>
            <field name="folder_id" model="documents.folder" eval="obj().search([('name', '=like', 'DOC%')], order='id desc', limit=1)"/>
            <field name="type">domain</field>
            <field name="action">downloadupload</field>
            <field name="include_sub_folders">True</field>
        </record>

        <!-- Share project in portal (readonly) -->
        <function model="project.project" name="write">
            <value model="project.project" search="[('sale_line_id', '=', ref('sale_order_1_sale_line_1'))]"/>
            <value eval="{
                'allow_billable': True,
                'privacy_visibility': 'portal',
                'documents_tag_ids': [Command.link(ref('documents_folder_facet_1_tag_1'))],
            }"/>
        </function>
        <record id="sale_order_1_project_follower_portal" model="mail.followers">
            <field name="res_model">project.project</field>
            <field name="res_id" model="project.project" search="[('sale_line_id', '=', ref('sale_order_1_sale_line_1'))]"/>
            <field name="partner_id" ref="base.partner_demo_portal"/>
        </record>

        <record id="sale_order_1_project_document_1" model="documents.document">
            <field name="name">plan.jpg</field>
            <field name="datas" type="base64" file="documents_project_sale/data/files/plan.jpg"/>
            <field name="folder_id" model="documents.folder" eval="obj().search([('name', '=', 'Plans')], order='id desc', limit=1)"/>
            <field name="res_model">project.project</field>
            <field name="res_id" model="project.project" search="[('sale_line_id', '=', ref('sale_order_1_sale_line_1'))]"/>
            <field name="owner_id" ref="base.user_admin"/>
            <field name="partner_id" ref="base.res_partner_2"/>
        </record>

        <record id="sale_order_1_task_1" model="project.task">
            <field name="name">Visit with the customer</field>
            <field name="project_id" search="[('sale_line_id', '=', ref('sale_order_1_sale_line_1'))]"/>
        </record>

        <record id="sale_order_1_task_1_document_1" model="documents.document">
            <field name="name">welcome.jpg</field>
            <field name="datas" type="base64" file="documents_project_sale/data/files/welcome.jpg"/>
            <field name="folder_id" model="documents.folder" eval="obj().search([('name', '=like', 'DOC%')], order='id desc', limit=1)"/>
            <field name="res_model">project.task</field>
            <field name="res_id" ref="sale_order_1_task_1"/>
            <field name="owner_id" ref="base.public_user"/>
            <field name="partner_id" ref="base.res_partner_2"/>
        </record>
    </data>
</odoo>
