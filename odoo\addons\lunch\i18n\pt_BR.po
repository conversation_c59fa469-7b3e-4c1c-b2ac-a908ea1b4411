# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* lunch
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# dani<PERSON>ibe<PERSON> <danimaribe<PERSON>@gmail.com>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <adrian<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Mait<PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_temaki
msgid "1 Avocado - 1 Salmon - 1 Eggs - 1 Tuna"
msgstr "1 abacate - 1 salmão - 1 ovo - 1 atum"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_chirashi
msgid "2 Tempuras, Cabbages, Onions, Sesame Sauce"
msgstr "2 tempurás, repolhos, cebolas, molho de gergelim"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_5
#: model:lunch.product,name:lunch.product_4formaggi
msgid "4 Formaggi"
msgstr "4 formaggi"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_salmon
msgid "4 Sushi Salmon - 6 Maki Salmon - 4 Sashimi Salmon"
msgstr "4 sushi de salmão - 6 maki de salmão - 4 sashimi de salmão"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_maki
msgid "6 Maki Salmon - 6 Maki Tuna - 6 Maki Shrimp/Avocado"
msgstr "6 maki de salmão - 6 maki de atum - 6 maki de camarão/abacate"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Send notification\" "
"title=\"Send notification\"/>"
msgstr ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Send notification\" "
"title=\"Send notification\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-paper-plane\" role=\"img\" aria-label=\"Send button\" "
"title=\"Send button\"/>"
msgstr ""
"<i class=\"fa fa-paper-plane\" role=\"img\" aria-label=\"Send button\" "
"title=\"Send button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"
msgstr ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"
msgstr ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Lunch Overdraft</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Reception notification</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: lunch
#: model:mail.template,body_html:lunch.lunch_order_mail_supplier
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Lunch Order</span><br>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"></t>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"></t>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"></t>\n"
"        <p>\n"
"        Dear <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>,\n"
"        </p><p>\n"
"        Here is, today orders for <t t-out=\"order.get('company_name', '')\">LunchCompany</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br>\n"
"            <p>Location</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"></t> : <t t-out=\"site['address'] or ''\"></t></p>\n"
"            </t>\n"
"            <br>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Product</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comments</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Person</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Site</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Qty</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Price</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi salmon</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Soy sauce</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">With wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">lap</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Office 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">$ 1.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td></td>\n"
"                    <td></td>\n"
"                    <td></td>\n"
"                    <td></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">$ 10.00</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>Do not hesitate to contact us if you have any questions.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid ""
"A cashmove can either be an expense or a payment.<br>\n"
"            An expense is automatically created at the order receipt.<br>\n"
"            A payment represents the employee reimbursement to the company."
msgstr ""
"Uma movimentação de caixa pode ser uma despesa ou um pagamento.<br>\n"
"Uma despesa é criada automaticamente no recebimento do pedido.<br>\n"
"Um pagamento representa um reembolso do funcionário à empresa."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "A product is defined by its name, category, price and vendor."
msgstr "Um produto é definido pelo seu nome, categoria, preço e vendedor."

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__am
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__am
msgid "AM"
msgstr "AM"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__active
#: model:ir.model.fields,field_description:lunch.field_lunch_order__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__active
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__active
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Active"
msgstr "Ativo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoração de Atividade Excepcional"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_state
msgid "Activity State"
msgstr "Estado de Atividade"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone do Tipo de Atividade"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Add To Cart"
msgstr "Adicionar ao carrinho"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__address
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Address"
msgstr "Endereço"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_manager
msgid "Administrator"
msgstr "Administrador"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__name
msgid "Alert Name"
msgstr "Nome do alerta"

#. module: lunch
#: model:lunch.alert,name:lunch.alert_office_3
msgid "Alert for Office 3"
msgstr "Alerta para Escritório 3"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__alert
msgid "Alert in app"
msgstr "Alerta no aplicativo"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_alert_menu
msgid "Alerts"
msgstr "Alertas"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__amount
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__amount
msgid "Amount"
msgstr "Montante"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_1
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_2
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_3
msgid "Are extras available for this product"
msgstr "Há extras disponíveis para este produto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_attachment_count
msgid "Attachment Count"
msgstr "Contagem de Anexos"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_supplier_automatic_email_time_range
msgid "Automatic Email Sending Time should be between 0 and 12"
msgstr "O tempo de envio automático de e-mails deve estar entre 0 e 12"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Availability"
msgstr "Disponibilidade"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Available Today"
msgstr "Disponível hoje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_1
msgid "Available Toppings 1"
msgstr "Adicionais disponíveis 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_2
msgid "Available Toppings 2"
msgstr "Adicionais disponíveis 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_3
msgid "Available Toppings 3"
msgstr "Adicionais disponíveis 3"

#. module: lunch
#: model:lunch.product,name:lunch.product_bacon
#: model:lunch.product,name:lunch.product_bacon_0
msgid "Bacon"
msgstr "Bacon"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_bacon
#: model_terms:lunch.product,description:lunch.product_bacon_0
msgid "Beef, Bacon, Salad, Cheddar, Fried Onion, BBQ Sauce"
msgstr "Carne bovina, bacon, salada, cheddar, cebola frita, molho BBQ"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_burger_0
#: model_terms:lunch.product,description:lunch.product_cheeseburger
msgid "Beef, Cheddar, Salad, Fried Onions, BBQ Sauce"
msgstr "Carne bovina, cheddar, salada, cebola frita, molho BBQ"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_1
#: model:lunch.product,name:lunch.product_Bolognese
msgid "Bolognese Pasta"
msgstr "Macarrão à bolonhesa"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_country
msgid "Brie, Honey, Walnut Kernels"
msgstr "Queijo brie, mel, miolo de nozes"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_burger
msgid "Burger"
msgstr "Hambúrguer"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "By Employee"
msgstr "Por Funcionário"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "By User"
msgstr "Por Usuário"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Cancel"
msgstr "Cancelar"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__cancelled
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Cancelled"
msgstr "Cancelado"

#. module: lunch
#: code:addons/lunch/models/lunch_supplier.py:0
#, python-format
msgid "Cannot send an email to this supplier!"
msgstr "Não é possível enviar um e-mail para esse fornecedor!"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_payment
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_payment
msgid "Cash Moves"
msgstr "Movimentações de caixa"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_cashmove_report
msgid "Cashmoves report"
msgstr "Relatório de movimentações de caixa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Categories"
msgstr "Categorias"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Category"
msgstr "Categoria"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__chat
msgid "Chat notification"
msgstr "Notificação de chat"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_ham
msgid "Cheese And Ham"
msgstr "Queijo e presunto"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_burger_0
#: model:lunch.product,name:lunch.product_cheeseburger
msgid "Cheese Burger"
msgstr "X-búrguer"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_ham
msgid "Cheese, Ham, Salad, Tomatoes, cucumbers, eggs"
msgstr "Queijo, presunto, salada, tomate, pepino, ovos"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_4
#: model:lunch.product,name:lunch.product_chicken_curry
msgid "Chicken Curry"
msgstr "Frango ao curry"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_chirashi
msgid "Chirashi"
msgstr "Chirashi"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__city
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "City"
msgstr "Cidade"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"            Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"            Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> red X to announce that the order isn't available."
msgstr ""
"Clique em <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> para anunciar que o pedido foi feito.<br>\n"
"Clique em <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> para anunciar que o pedido foi recebido.<br>\n"
"Clique em <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> X vermelho para anunciar que o pedido não está disponível."

#. module: lunch
#: model:lunch.product,name:lunch.product_club
#: model:lunch.product,name:lunch.product_club_0
msgid "Club"
msgstr "Sanduíche natural"

#. module: lunch
#: model:lunch.product,name:lunch.product_coke_0
msgid "Coca Cola"
msgstr "Coca-cola"

#. module: lunch
#: model:ir.model,name:lunch.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__company_id
msgid "Company"
msgstr "Empresa"

#. module: lunch
#: model:ir.model,name:lunch.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_config
msgid "Configuration"
msgstr "Configuração"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/mixins/lunch_renderer_mixin.js:0
#, python-format
msgid "Configure Your Order"
msgstr "Configure seu pedido"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm"
msgstr "Confirmar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm Orders"
msgstr "Confirmar pedidos"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_control_accounts
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_control_accounts
msgid "Control Accounts"
msgstr "Controlar Contas"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_control_suppliers
#: model:ir.ui.menu,name:lunch.lunch_order_menu_control_suppliers
msgid "Control Vendors"
msgstr "Controle dos fornecedores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__country_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Country"
msgstr "País"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid "Create a new payment"
msgstr "Criar novo pagamento"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Create a new product category"
msgstr "Criar nova categoria de produto"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "Create a new product for lunch"
msgstr "Criar novo produto de almoço"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid "Create new lunch alerts"
msgstr "Criar novos alertas de almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_date
msgid "Created on"
msgstr "Criado em"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__cron_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__cron_id
msgid "Cron"
msgstr "Cron"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__currency_id
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Currently inactive"
msgstr "Atualmente inativo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__date
msgid "Date"
msgstr "Data"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__delivery
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__delivery
msgid "Delivery"
msgstr "Entrega"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__description
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__description
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_description
#: model:ir.model.fields,field_description:lunch.field_lunch_product__description
msgid "Description"
msgstr "Descrição"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Discard"
msgstr "Descartar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mode
msgid "Display"
msgstr "Mostrar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_location__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_reorder_button
msgid "Display Reorder Button"
msgstr "Exibir botão de 'pedir novamente'"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_drinks
msgid "Drinks"
msgstr "Bebidas"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "Edit order"
msgstr "Editar pedido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__mail
msgid "Email"
msgstr "E-mail"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_month
msgid "Employee who ordered last month"
msgstr "Funcionário que fez o pedido no mês passado"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_week
msgid "Employee who ordered last week"
msgstr "Funcionário que fez o pedido na semana passada"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_year
msgid "Employee who ordered last year"
msgstr "Funcionário que fez o pedido no ano passado"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__everyone
msgid "Everyone"
msgstr "Todos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_1
msgid "Extra 1 Label"
msgstr "Rótulo do adicional 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_1
msgid "Extra 1 Quantity"
msgstr "Quantidade do adicional 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_2
msgid "Extra 2 Label"
msgstr "Rótulo do adicional 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_2
msgid "Extra 2 Quantity"
msgstr "Quantidade do adicional 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_3
msgid "Extra 3 Label"
msgstr "Rótulo do adicional 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_3
msgid "Extra 3 Quantity"
msgstr "Quantidade do adicional 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_toppings
msgid "Extras"
msgstr "Adicionais"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_1
msgid "Extras 1"
msgstr "Adicionais 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_2
msgid "Extras 2"
msgstr "Adicionais 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_3
msgid "Extras 3"
msgstr "Adicionais 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__favorite_lunch_product_ids
msgid "Favorite Lunch Product"
msgstr "Produto favorito de almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__favorite_user_ids
msgid "Favorite User"
msgstr "Usuário Favorito"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font Awesome. Ex: fa-tasks"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Formato de endereço de email \"Nome <email@provedor>\""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email_formatted
msgid "Formatted Email"
msgstr "Email Formatado"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_italiana
msgid "Fresh Tomatoes, Basil, Mozzarella"
msgstr "Tomates frescos, manjericão, muçarela"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__fri
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__fri
msgid "Fri"
msgstr "Sex"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Friday"
msgstr "Sexta"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_3
#: model:lunch.product,name:lunch.product_gouda
msgid "Gouda Cheese"
msgstr "Queijo gouda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_club
#: model_terms:lunch.product,description:lunch.product_club_0
msgid "Ham, Cheese, Vegetables"
msgstr "Presunto, queijo, legumes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__has_message
msgid "Has Message"
msgstr "Tem uma mensagem"

#. module: lunch
#: model:ir.module.category,description:lunch.module_lunch_category
msgid ""
"Helps you handle your lunch needs, if you are a manager you will be able to "
"create new products, cashmoves and to confirm or cancel orders."
msgstr ""
"Ajuda a lidar com as suas necessidades de almoço, se você é um gerente você "
"será ser capaz de criar novos produtos, movimentos de caixa e para confirmar"
" ou cancelar ordens."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Here you can access all categories for the lunch products."
msgstr "Aqui você pode acessar todas as categorias para "

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Here you can see today's orders grouped by vendors."
msgstr "Você pode ver os pedidos de hoje agrupados por fornecedores aqui."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid ""
"Here you can see your cash moves.<br>A cash move can either be an expense or a payment.\n"
"            An expense is automatically created when an order is received while a payment is a reimbursement to the company encoded by the manager."
msgstr ""
"Você pode ver suas movimentações de caixa aqui.<br>Uma movimentação de caixa pode ser uma despesa ou um pagamento.\n"
"Uma despesa é criada automaticamente quando um pedido é recebido, enquanto um pagamento é um reembolso à empresa codificado pelo gerente."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__id
#: model:ir.model.fields,field_description:lunch.field_lunch_location__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__id
msgid "ID"
msgstr "ID"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma atividade excepcional."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, algumas mensagens tem erro de entrega."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1920
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1920
msgid "Image"
msgstr "Imagem"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1024
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1024
msgid "Image 1024"
msgstr "Imagem 1024"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_1920
msgid "Image 1920"
msgstr "Imagem 1920"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_256
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_256
msgid "Image 256"
msgstr "Imagem 256"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_512
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_512
msgid "Image 512"
msgstr "Imagem 512"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Information, allergens, ..."
msgstr "Informações, alergias..."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__available_today
msgid "Is Displayed Today"
msgstr "Está exibido hoje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_favorite
msgid "Is Favorite"
msgstr "É Favorito"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_new
msgid "Is New"
msgstr "É novo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__last_lunch_location_id
msgid "Last Lunch Location"
msgstr "Local do último almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_location____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_topping____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__last_order_date
msgid "Last Order Date"
msgstr "Data do último pedido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__location_ids
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_location_ids
msgid "Location"
msgstr "Local"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__name
msgid "Location Name"
msgstr "Nome do Local"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_location_menu
msgid "Locations"
msgstr "Locais"

#. module: lunch
#: model:ir.module.category,name:lunch.module_lunch_category
#: model:ir.ui.menu,name:lunch.menu_lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Lunch"
msgstr "Almoço"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_alert
msgid "Lunch Alert"
msgstr "alerta de almoço"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_alert_action
msgid "Lunch Alerts"
msgstr "Alertas de almoço"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_cashmove.py:0
#: code:addons/lunch/report/lunch_cashmove_report.py:0
#: model:ir.model,name:lunch.model_lunch_cashmove
#, python-format
msgid "Lunch Cashmove"
msgstr "Movimentação de caixa de almoço"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_topping
msgid "Lunch Extras"
msgstr "Adicionais de almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__lunch_location_id
msgid "Lunch Location"
msgstr "Local do almoço"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_location_action
#: model:ir.model,name:lunch.model_lunch_location
msgid "Lunch Locations"
msgstr "Locais de almoço"

#. module: lunch
#: model:lunch.product,name:lunch.product_maki
msgid "Lunch Maki 18pc"
msgstr "Almoço Maki 18 pc"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_minimum_threshold
msgid "Lunch Minimum Threshold"
msgstr "Limite mínimo de almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_notify_message
msgid "Lunch Notify Message"
msgstr "Mensagem de notificação de almoço"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order
msgid "Lunch Order"
msgstr "Pedido de Almoço"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product
msgid "Lunch Product"
msgstr "Produto de almoço"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product_category
msgid "Lunch Product Category"
msgstr "Categoria de produto de almoço"

#. module: lunch
#: model:lunch.product,name:lunch.product_salmon
msgid "Lunch Salmon 20pc"
msgstr "Almoço Salmão 20 pc"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_supplier
msgid "Lunch Supplier"
msgstr "Fornecedor de almoço"

#. module: lunch
#: model:lunch.product,name:lunch.product_temaki
msgid "Lunch Temaki mix 3pc"
msgstr "Almoço Mix Temaki 3 pc"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "Lunch notification"
msgstr "Notificação de almoço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_notify_message
msgid "Lunch notification message"
msgstr "Mensagem de notificação de almoço"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_cancel
msgid "Lunch: Cancel meals"
msgstr "Almoço: cancelar refeições"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_confirm
msgid "Lunch: Receive meals"
msgstr "Almoço: receber refeições"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_notify
msgid "Lunch: Send notifications"
msgstr "Almoço: enviar notificações"

#. module: lunch
#: model:mail.template,name:lunch.lunch_order_mail_supplier
msgid "Lunch: Supplier Order"
msgstr "Almoço: pedido ao fornecedor"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_alert_cron_sa_236
msgid "Lunch: alert chat notification (Alert for Office 3)"
msgstr "Almoço: notificação de alerta no chat (alerta para o Escritório 3)"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_237
msgid "Lunch: send automatic email to Coin gourmand"
msgstr "Almoço: enviar e-mail automático para o Coin Gourmand"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_232
msgid "Lunch: send automatic email to Lunch Supplier"
msgstr "Almoço: enviar e-mail automático para o fornecedor de almoço"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_238
msgid "Lunch: send automatic email to Pizza Inn"
msgstr "Almoço: enviar e-mail automático para o Pizza Inn"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_240
msgid "Lunch: send automatic email to Sushi Shop"
msgstr "Almoço: enviar e-mail automático para o Sushi Shop"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_239
msgid "Lunch: send automatic email to The Corner"
msgstr "Almoço: enviar e-mail automático para o The Corner"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_main_attachment_id
msgid "Main Attachment"
msgstr "Anexo Principal"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Moeda principal da empresa"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_admin
msgid "Manager"
msgstr "Gerente"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_minimum_threshold
msgid "Maximum Allowed Overdraft"
msgstr "Excedeu o máximo permitido"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Maximum overdraft that your employees can reach"
msgstr "Limite máximo que seus funcionários podem exceder"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__message
msgid "Message"
msgstr "Mensagem"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da Mensagem"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__moment
msgid "Moment"
msgstr "Momento"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mon
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__mon
msgid "Mon"
msgstr "Seg"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Monday"
msgstr "Segunda"

#. module: lunch
#: model:lunch.product,name:lunch.product_mozzarella
msgid "Mozzarella"
msgstr "Muçarela"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_mozzarella
msgid "Mozzarella, Pesto, Tomatoes"
msgstr "Muçarela, pesto, tomates"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_account
msgid "My Account"
msgstr "Minha Conta"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_form
msgid "My Account History"
msgstr "Histórico da minha conta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "My Account grouped"
msgstr "Minha conta agrupada"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da Minha Atividade"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_title
msgid "My Lunch"
msgstr "Meu almoço"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_tree
msgid "My Order History"
msgstr "Meu histórico de pedidos"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "My Orders"
msgstr "Meus Pedidos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__name
msgid "Name"
msgstr "Nome"

#. module: lunch
#: model:lunch.product,name:lunch.product_Napoli
msgid "Napoli Pasta"
msgstr "Napoli pasta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban_order
msgid "New"
msgstr "Novo"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_form
msgid "New Order"
msgstr "Novo Pedido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__new_until
msgid "New Until"
msgstr "Novo até"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Próxima Atividade do Calendário de Eventos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo Final para Próxima Atividade"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_summary
msgid "Next Activity Summary"
msgstr "Próximo Sumário de Atividade"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da Próxima Atividade"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__no_delivery
msgid "No Delivery"
msgstr "Sem entrega"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid "No cash move yet"
msgstr "Sem movimentação de caixa ainda"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/views/no_content_helper.xml:0
#, python-format
msgid "No location found"
msgstr "Nenhum local encontrado"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "No lunch location available."
msgstr "Não há local para almoço disponível."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "No lunch order yet"
msgstr "Sem pedidos de almoço ainda"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid "No previous order found"
msgstr "Nenhum pedido anterior foi encontrado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "None"
msgstr "Nenhum"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__0_more
msgid "None or More"
msgstr "Nenhum ou mais"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Not Received"
msgstr "Não Recebido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__note
msgid "Notes"
msgstr "Observações"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Nothing to order today"
msgstr "Nada para pedir hoje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_moment
msgid "Notification Moment"
msgstr "Momento da notificação"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_time
msgid "Notification Time"
msgstr "Hora da notificação"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_alert_notification_time_range
msgid "Notification time must be between 0 and 12"
msgstr "A hora da notificação deve estar entre 0 e 12"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__notified
msgid "Notified"
msgstr "Notificado"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of errors"
msgstr "Número de Erros"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem ação"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1_more
msgid "One or More"
msgstr "Um ou mais"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1
msgid "Only One"
msgstr "Apenas um"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Order"
msgstr "Pedido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__date
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Order Date"
msgstr "Data do Pedido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__order_deadline_passed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__order_deadline_passed
msgid "Order Deadline Passed"
msgstr "Prazo do pedido passou"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "Order Now"
msgstr "Peça Agora"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__automatic_email_time
msgid "Order Time"
msgstr "Hora do pedido"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action_order
msgid "Order Your Lunch"
msgstr "Pedir almoço"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Order lines Tree"
msgstr "Árvore de linhas do pedido"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__ordered
msgid "Ordered"
msgstr "Pedido"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Orders"
msgstr "Pedidos"

#. module: lunch
#: model:mail.template,subject:lunch.lunch_order_mail_supplier
msgid "Orders for {{ ctx['order']['company_name'] }}"
msgstr "Pedidos para {{ ctx['order']['company_name'] }}"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Overdraft"
msgstr "Cheque especial"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__pm
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__pm
msgid "PM"
msgstr "PM"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pasta
msgid "Pasta"
msgstr "Massas"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
msgid "Payment"
msgstr "Pagamento"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""
"Os pagamentos são usados ​​para registrar movimentos de liquidez. Você pode "
"processar esses pagamentos por seus próprios meios ou usando os recursos "
"instalados."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__phone
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__phone
msgid "Phone"
msgstr "Telefone"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pizza
msgid "Pizza"
msgstr "Pizza"

#. module: lunch
#: model:lunch.product,name:lunch.product_funghi
msgid "Pizza Funghi"
msgstr "Pizza funghi"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_2
#: model:lunch.product,name:lunch.product_italiana
msgid "Pizza Italiana"
msgstr "Pizza italiana"

#. module: lunch
#: model:lunch.product,name:lunch.product_margherita
#: model:lunch.product,name:lunch.product_pizza_0
msgid "Pizza Margherita"
msgstr "Pizza margherita"

#. module: lunch
#: model:lunch.product,name:lunch.product_vege
msgid "Pizza Vegetarian"
msgstr "Pizza vegetariana"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/views/no_content_helper.xml:0
#, python-format
msgid "Please create a location to start ordering."
msgstr "Crie um local para começar a fazer pedidos."

#. module: lunch
#: model_terms:lunch.alert,message:lunch.alert_office_3
msgid "Please order"
msgstr "Por gentileza, peça"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__price
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__price
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Price"
msgstr "Preço"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product"
msgstr "Produto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_available_at
msgid "Product Availability"
msgstr "Disponibilidade do Produto"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_category_action
#: model:ir.ui.menu,name:lunch.lunch_product_category_menu
msgid "Product Categories"
msgstr "Categorias de Produtos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
msgid "Product Categories Form"
msgstr "Formulário de categorias de produtos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Product Category"
msgstr "Categoria de Produtos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__product_count
msgid "Product Count"
msgstr "Contagem de produtos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__product_image
msgid "Product Image"
msgstr "Imagem do Produto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__name
msgid "Product Name"
msgstr "Nome do Produto"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Search"
msgstr "Busca de produtos"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "Product is no longer available."
msgstr "O produto não está mais disponível."

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action
#: model:ir.actions.act_window,name:lunch.lunch_product_action_statbutton
#: model:ir.ui.menu,name:lunch.lunch_product_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_kanban
msgid "Products"
msgstr "Produtos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
msgid "Products Form"
msgstr "Formulário de Produtos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Products List"
msgstr "Lista de produtos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_tree
msgid "Products Tree"
msgstr "Árvore de Produtos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__quantity
msgid "Quantity"
msgstr "Quantidade"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Re-order"
msgstr "Reordenar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Receive"
msgstr "Recebimento"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Received"
msgstr "Recebido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__recipients
msgid "Recipients"
msgstr "Destinatários"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid "Register a payment"
msgstr "Registre um pagamento"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Reset"
msgstr "Redefinir"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__responsible_id
msgid "Responsible"
msgstr "Responsável"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_user_id
msgid "Responsible User"
msgstr "Usuário Responsável"

#. module: lunch
#: model:lunch.product,name:lunch.product_chirashi
msgid "Salmon and Avocado"
msgstr "Salmão e abacate"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sandwich
msgid "Sandwich"
msgstr "Sanduíche"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sat
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sat
msgid "Sat"
msgstr "Sáb"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Saturday"
msgstr "Sábado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Save"
msgstr "Salvar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Search"
msgstr "Pesquisar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Send"
msgstr "Enviar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Send Notification"
msgstr "Enviar notificação"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__send_by
msgid "Send Order By"
msgstr "Enviar pedido até"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Send Orders"
msgstr "Enviar pedidos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Send this message to your users when their order has been delivered."
msgstr "Envie essa mensagem aos usuários quando o pedido deles for entregue."

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__sent
msgid "Sent"
msgstr "Enviado"

#. module: lunch
#: model:mail.template,description:lunch.lunch_order_mail_supplier
msgid "Sent to vendor with the order of the day"
msgstr "Enviado ao fornecedor com o pedido do dia"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_config_settings_action
#: model:ir.ui.menu,name:lunch.lunch_settings_menu
msgid "Settings"
msgstr "Definições"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__show_confirm_button
msgid "Show Confirm Button"
msgstr "Mostrar botão de confirmar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__show_order_button
msgid "Show Order Button"
msgstr "Mostrar botão de pedir"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__until
msgid "Show Until"
msgstr "Mostrar até"

#. module: lunch
#: model:lunch.product,name:lunch.product_spicy_tuna
msgid "Spicy Tuna"
msgstr "Atum picante"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__state_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "State"
msgstr "Estado"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__state
msgid "Status"
msgstr "Situação"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseado em atividades\n"
"Atrasado: Data definida já passou\n"
"Hoje: Data de atividade é hoje\n"
"Planejado: Atividades futuras."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street
msgid "Street"
msgstr "Endereço"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street 2..."
msgstr "Complemento..."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street..."
msgstr "Endereço..."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street2
msgid "Street2"
msgstr "Complemento"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "Summary of all lunch orders, grouped by vendor and by date."
msgstr ""
"Resumo de todos os pedidos de almoço, agrupados por fornecedor e por data."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sun
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sun
msgid "Sun"
msgstr "Dom"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Sunday"
msgstr "Domingo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__supplier_id
msgid "Supplier"
msgstr "Fornecedor"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sushi
msgid "Sushi"
msgstr "Sushi"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_temaki
msgid "Temaki"
msgstr "Temaki"

#. module: lunch
#: model:lunch.product,name:lunch.product_country
msgid "The Country"
msgstr "O país"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following product categories are archived. You should either unarchive the categories or change the category of the product.\n"
"%s"
msgstr ""
"As seguintes categorias de produtos estão arquivadas. É necessário desarquivar as categorias ou alterar a categoria do produto.\n"
"%s"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following suppliers are archived. You should either unarchive the suppliers or change the supplier of the product.\n"
"%s"
msgstr ""
"Os seguintes fornecedores estão arquivados. É necessário desarquivar os fornecedores ou alterar o fornecedor do produto.\n"
"%s"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_product_category__product_count
msgid "The number of products related to this category"
msgstr "O número de produtos relacionados a essa categoria"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "The orders for this vendor have already been sent."
msgstr "Os pedidos para esse fornecedor já foram enviados."

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_supplier.py:0
#, python-format
msgid "The orders have been confirmed!"
msgstr "Os pedidos foram confirmados!"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_supplier.py:0
#, python-format
msgid "The orders have been sent!"
msgstr "Os pedidos foram enviados!"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__responsible_id
msgid ""
"The responsible is the person that will order lunch for everyone. It will be"
" used as the 'from' when sending the automatic email."
msgstr ""
"O responsável é a pessoa que pedirá o almoço para todos. Ele será usado como"
" o \"de\" ao enviar o e-mail automático."

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "The vendor related to this order is not available today."
msgstr "O fornecedor relacionado a esse pedido não está disponível hoje."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid ""
"There is no previous order recorded. Click on \"My Lunch\" and then create a"
" new lunch order."
msgstr ""
"Não há nenhum pedido anterior registrado. Clique em \"Meu almoço\" e, em "
"seguida, crie um novo pedido de almoço."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid "There is no product available today"
msgstr "Não há produtos disponíveis hoje"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__recurrency_end_date
msgid "This field is used in order to "
msgstr "Este campo é usado para"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_today
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_today
msgid "This is True when if the supplier is available today"
msgstr "Isso é verdadeiro se o fornecedor estiver disponível hoje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__thu
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__thu
msgid "Thu"
msgstr "Qui"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Thursday"
msgstr "Quinta"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tz
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tz
msgid "Timezone"
msgstr "Fuso horário"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__new
msgid "To Order"
msgstr "Para Pedido"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_payment_dialog
msgid "To add some money to your wallet, please contact your lunch manager."
msgstr ""
"Para adicionar dinheiro à sua carteira, entre em contato com o gerente de "
"almoço."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_location_action
msgid "To see some locations, create one using the create button"
msgstr "Para ver locais, crie um usando o botão 'Criar'"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid ""
"To see some products, check if your vendors are available today and that you"
" have configured some products"
msgstr ""
"Para ver produtos, verifique se seus fornecedores estão disponíveis hoje e "
"se você configurou alguns produtos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Today"
msgstr "Hoje"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_by_supplier
#: model:ir.ui.menu,name:lunch.lunch_order_menu_by_supplier
msgid "Today's Orders"
msgstr "Pedidos de hoje"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_4formaggi
msgid "Tomato sauce, Olive oil, Fresh Tomatoes, Onions, Vegetables, Parmesan"
msgstr ""
"Molho de tomate, azeite de oliva, tomates frescos, cebolas, legumes, "
"parmesão"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_Napoli
msgid "Tomatoes, Basil"
msgstr "Tomates, manjericão"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_margherita
#: model_terms:lunch.product,description:lunch.product_pizza_0
msgid "Tomatoes, Mozzarella"
msgstr "Tomates, muçarela"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_vege
msgid "Tomatoes, Mozzarella, Mushrooms, Peppers, Olives"
msgstr "Tomates, muçarela, cogumelos, pimentões, azeitonas"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_funghi
msgid "Tomatoes, Mushrooms, Mozzarella"
msgstr "Tomates, cogumelos, muçarela"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__topping_category
msgid "Topping Category"
msgstr "Categoria de adicional"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_1
msgid "Topping Ids 1"
msgstr "IDs de adicionais 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_2
msgid "Topping Ids 2"
msgstr "IDs de adicionais 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_3
msgid "Topping Ids 3"
msgstr "IDs de adicionais 3"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
#, python-format
msgid "Total"
msgstr "Total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__price
msgid "Total Price"
msgstr "Preço Total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tue
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tue
msgid "Tue"
msgstr "Ter"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Tuesday"
msgstr "Terça"

#. module: lunch
#: model:lunch.product,name:lunch.product_tuna
msgid "Tuna"
msgstr "Atum"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_tuna
msgid "Tuna, Mayonnaise"
msgstr "Atum, maionese"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de atividade de exceção registrada."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__recurrency_end_date
msgid "Until"
msgstr "Até"

#. module: lunch
#: model:ir.model,name:lunch.model_res_users
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__user_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "User"
msgstr "Usuário"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_user
msgid "User : Order your meal"
msgstr "Usuário: pedir refeição"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__partner_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Vendor"
msgstr "Fornecedor"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Vendor Orders by Date"
msgstr "Pedidos ao fornecedor por data"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_vendors_action
#: model:ir.ui.menu,name:lunch.lunch_vendors_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Vendors"
msgstr "Fornecedores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__wed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__wed
msgid "Wed"
msgstr "Qua"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Wednesday"
msgstr "Quarta"

#. module: lunch
#. odoo-python
#: code:addons/lunch/controllers/main.py:0
#, python-format
msgid ""
"You are trying to impersonate another user, but this can only be done by a "
"lunch manager"
msgstr ""
"Você está tentando se passar por outro usuário, mas isso só pode ser feito "
"por um gerente de almoço"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You have to order one and only one %s"
msgstr "Você deve pedir somente 1 %s"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You should order at least one %s"
msgstr "Você deve pedir pelo menos 1 %s"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "Your Account"
msgstr "Sua Conta"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "Your Cart ("
msgstr "Seu carrinho ("

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#, python-format
msgid "Your Order"
msgstr "Seu pedido"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid ""
"Your wallet does not contain enough money to order that. To add some money "
"to your wallet, please contact your lunch manager."
msgstr ""
"Sua carteira não tem dinheiro suficiente para fazer o pedido. Para adicionar"
" dinheiro à sua carteira, entre em contato com o gerente de almoço."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "ZIP"
msgstr "CEP"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__zip_code
msgid "Zip"
msgstr "CEP"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "alert form"
msgstr "formulário de alerta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_form
msgid "cashmove form"
msgstr "formulário de movimento de caixa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
msgid "cashmove tree"
msgstr "árvore de movimento de caixa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "e.g. Order before 11am"
msgstr "Por exemplo, fazer o pedido antes das 11h"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "e.g. The Pizzeria Inn"
msgstr "Por exemplo, The Pizzeria Inn"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "lunch cashmove"
msgstr "movimento de caixa de almoço"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "lunch employee payment"
msgstr "pagamento de almoço do funcionário"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "on"
msgstr "em"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "to"
msgstr "até"
