<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="mail_notification_layout" name="Mail: mail notification layout template">
<html t-att-lang="lang">
<head>
    <meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
</head>
<body style="font-family:Verdana, Arial,sans-serif; color: #454748;">
<t t-set="subtype_internal" t-value="subtype and subtype.internal"/>
<!-- HEADER -->
<t t-call="mail.notification_preview"/>
<div style="max-width: 900px; width: 100%;">
<div t-if="has_button_access" itemscope="itemscope" itemtype="http://schema.org/EmailMessage">
    <div itemprop="potentialAction" itemscope="itemscope" itemtype="http://schema.org/ViewAction">
        <link itemprop="target" t-att-href="button_access['url']"/>
        <link itemprop="url" t-att-href="button_access['url']"/>
        <meta itemprop="name" t-att-content="button_access['title']"/>
    </div>
</div>
<div t-if="subtitles or has_button_access or actions or not is_discussion"
        summary="o_mail_notification" style="padding: 0px;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin-top: 5px;">
        <tbody>
            <tr>
                <td valign="center">
                    <img t-att-src="'/logo.png?company=%s' % (company.id or 0)" style="padding: 0px; margin: 0px; height: auto; max-width: 200px; max-height: 36px;" t-att-alt="'%s' % company.name"/>
                </td>
            </tr>
            <tr>
                <td valign="center">
                    <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 10px 0px;"/>
                </td>
            </tr>
            <tr>
                <td valign="center" style="white-space:nowrap;">
                    <table cellspacing="0" cellpadding="0" border="0">
                        <tbody>
                            <tr>
                                <td t-if="has_button_access" t-att-style="'border-radius: 3px; text-align: center; background: ' + (company.secondary_color if company.secondary_color else '#875A7B')">
                                    <a t-att-href="button_access['url']" style="font-size: 12px; color: #FFFFFF; display: block; padding: 8px 12px 11px; text-decoration: none !important; font-weight: 400;">
                                        <t t-out="button_access['title']"/>
                                    </a>
                                </td>
                                <td t-if="has_button_access">&amp;nbsp;&amp;nbsp;</td>

                                <td t-if="subtitles" style="font-size: 12px;">
                                     <t t-foreach="subtitles" t-as="subtitle">
                                        <span t-attf-style="{{ 'font-weight:bold;' if subtitle_first else '' }}"
                                              t-out="subtitle"/>
                                        <br t-if="not subtitle_last"/>
                                    </t>
                                </td>
                                <td t-else=""><span style="font-weight:bold;" t-out="record_name"/><br/></td>
                                <td>&amp;nbsp;&amp;nbsp;</td>

                                <td t-if="actions">
                                    <t t-foreach="actions" t-as="action">
                                        |
                                        <a t-att-href="action['url']" style="font-size: 12px; color: #875A7B; text-decoration:none !important;">
                                            <t t-out="action['title']"/>
                                        </a>
                                    </t>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td valign="center">
                    <hr width="100%"
                        style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0;margin: 10px 0px;"/>
                    <p t-if="subtype_internal" style="background-color: #f2dede; padding: 5px; margin-bottom: 16px; font-size: 13px;">
                        <strong>Internal communication</strong>: Replying will post an internal note. Followers won't receive any email notification.
                    </p>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<!-- CONTENT -->
<div t-out="message.body" style="font-size: 13px;"/>
<ul t-if="tracking_values">
    <t t-foreach="tracking_values" t-as="tracking">
        <li><t t-out="tracking[0]"/>: <t t-out="tracking[1]"/> &#8594; <t t-out="tracking[2]"/></li>
    </t>
</ul>
<t class="o_signature">
    <div t-if="email_add_signature and not is_html_empty(signature)" t-out="signature" style="font-size: 13px;"/>
</t>
<!-- FOOTER -->
<div style="margin-top:32px;">
    <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;"/>
    <b t-out="company.name" style="font-size:11px;"/><br/>
    <p style="color: #999999; margin-top:2px; font-size:11px;">
        <t t-out="company.phone"/>
        <t t-if="company.email and company.phone"> |</t>
        <a t-if="company.email" t-att-href="'mailto:%s' % company.email" style="text-decoration:none; color: #999999;" t-out="company.email"/>
        <t t-if="company.website and (company.phone or company.email)"> |</t>
        <a t-if="company.website" t-att-href="'%s' % company.website" style="text-decoration:none; color: #999999;" t-out="company.website"/>
    </p>
</div>
<p style="color: #555555; font-size:11px;">
    Powered by <a target="_blank" href="https://www.odoo.com?utm_source=db&amp;utm_medium=email" style="color: #875A7B;">Odoo</a>
</p>
</div>
</body></html>
        </template>

        <template id="mail_notification_light">
<html t-att-lang="lang">
<head>
    <meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
</head>
<body>
<t t-set="subtype_internal" t-value="False"/>
<t t-call="mail.notification_preview"/>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;"><tr><td align="center">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="590" style="padding: 24px; background-color: white; color: #454748; border-collapse:separate;">
<tbody>
    <!-- HEADER -->
    <tr>
        <td align="center" style="min-width: 590px;">
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: white; padding: 0; border-collapse:separate;">
                <tr><td valign="middle">
                    <span style="font-size: 10px;">Your <t t-out="model_description or 'document'"/></span>
                    <br/>
                    <t t-if="has_button_access">
                        <a t-att-href="button_access['url']">
                            <span style="font-size: 20px; font-weight: bold;">
                                <t t-out="message.record_name and message.record_name.replace('/','-') or ''"/>
                            </span>
                        </a>
                    </t>
                    <t t-else="">
                        <span style="font-size: 20px; font-weight: bold;">
                            <t t-out="message.record_name and message.record_name.replace('/','-') or ''"/>
                        </span>
                    </t>
                </td><td valign="middle" align="right">
                    <img t-att-src="'/logo.png?company=%s' % (company.id or 0)" style="padding: 0px; margin: 0px; height: 48px;" t-att-alt="'%s' % company.name"/>
                </td></tr>
                <tr><td colspan="2" style="text-align:center;">
                  <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;"/>
                </td></tr>
            </table>
        </td>
    </tr>
    <!-- CONTENT -->
    <tr>
        <td style="min-width: 590px;">
            <t t-out="message.body"/>
        </td>
    </tr>
    <!-- FOOTER -->
    <tr>
        <td align="center" style="min-width: 590px; padding: 0 8px 0 8px; font-size:11px;">
            <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;"/>
            <b t-out="company.name"/><br/>
            <div style="color: #999999;">
                <t t-out="company.phone"/>
                <t t-if="company.email and company.phone"> |</t>
                <a t-if="company.email" t-att-href="'mailto:%s' % company.email" style="text-decoration:none; color: #999999;" t-out="company.email"/>
                <t t-if="company.website and (company.phone or company.email)"> |</t>
                <a t-if="company.website" t-att-href="'%s' % company.website" style="text-decoration:none; color: #999999;" t-out="company.website"/>
            </div>
        </td>
    </tr>
</tbody>
</table>
</td></tr>
<!-- POWERED BY -->
<tr><td align="center" style="min-width: 590px;">
        Powered by <a target="_blank" href="https://www.odoo.com?utm_source=db&amp;utm_medium=email" style="color: #875A7B;">Odoo</a>
</td></tr>
</table>
</body>
</html>
        </template>

        <template id="notification_preview">
<div style="display: none; max-height: 0px; overflow: hidden; color:#fff; font-size:0px; line-height:0px">
    <t t-if="tracking_values">
        <t t-out="tracking_values[0][0]"/>: <t t-out="tracking_values[0][1]"/> &#8594; <t t-out="tracking_values[0][2]"/>
        <t t-if="len(tracking_values) > 1"> |...</t>
        <t t-if="message.preview"> | </t>
    </t>
    <t t-if="subtype_internal">Internal communication: </t><t t-out="message.preview"/>
    <!--Trailing whitespace to push back email content so that it doesn't appear in preview. Specific characters to use may change over time -->
    <t t-out="'&#847; &#8203; ' * 140"/>
</div>
        </template>

        <template id="mail_notification_layout_with_responsible_signature"
                  name="Mail: mail notification layout with responsible signature (user_id of the record)"
                  inherit_id="mail.mail_notification_layout" primary="True">
            <xpath expr="//t[hasclass('o_signature')]" position="replace">
                <t class="o_signature">
                    <div t-if="email_add_signature and 'user_id' in record and record.user_id and not record.env.user._is_superuser() and not is_html_empty(record.user_id.sudo().signature)"
                         t-out="record.user_id.sudo().signature" style="font-size: 13px;"/>
                </t>
            </xpath>
        </template>
    </data>
</odoo>
