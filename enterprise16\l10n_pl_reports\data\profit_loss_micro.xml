<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_pl_micro_pl" model="account.report">
        <field name="name">Rachunek zysków i strat - Mikro</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="country_id" ref="base.pl"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="filter_multi_company">selector</field>
        <field name="default_opening_date_filter">this_year</field>
        <field name="column_ids">
            <record id="l10n_pl_micro_pl_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_pl_micro_pl_a" model="account.report.line">
                <field name="name">A. Przychody podstawowej działalności operacyjnej i zrównane z nimi, w tym zmiana stanu produktów</field>
                <field name="code">pl_micro_pl_a</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">-70.0 - 73.0 - 74.0</field>
            </record>
            <record id="l10n_pl_micro_pl_b" model="account.report.line">
                <field name="name">B. Koszty podstawowej działalności operacyjnej</field>
                <field name="code">pl_micro_pl_b</field>
                <field name="expression_ids">
                    <record id="l10n_pl_micro_pl_b_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_micro_pl_b_1.balance + pl_micro_pl_b_2.balance + pl_micro_pl_b_3.balance + pl_micro_pl_b_4.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_micro_pl_b_1" model="account.report.line">
                        <field name="name">I. Amortyzacja</field>
                        <field name="code">pl_micro_pl_b_1</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">42</field>
                    </record>
                    <record id="l10n_pl_micro_pl_b_2" model="account.report.line">
                        <field name="name">II. Zużycie materiałów i energii</field>
                        <field name="code">pl_micro_pl_b_2</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.01</field>
                    </record>
                    <record id="l10n_pl_micro_pl_b_3" model="account.report.line">
                        <field name="name">III. Wynagrodzenia, ubezpieczenia społeczne i inne świadczenia</field>
                        <field name="code">pl_micro_pl_b_3</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.04 + 40.05 + 41</field>
                    </record>
                    <record id="l10n_pl_micro_pl_b_4" model="account.report.line">
                        <field name="name">IV. Pozostałe koszty</field>
                        <field name="code">pl_micro_pl_b_4</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.02 + 40.03 + 43 + 49 + 5 + 70.1 + 73.1 + 74.1</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_micro_pl_c" model="account.report.line">
                <field name="name">C. Pozostałe przychody i zyski, w tym aktualizacja wartości aktywów</field>
                <field name="code">pl_micro_pl_c</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">-75.0 - 76.0 - 77.0 - 999997</field>
            </record>
            <record id="l10n_pl_micro_pl_d" model="account.report.line">
                <field name="name">D. Pozostałe koszty i straty, w tym aktualizacja wartości aktywów</field>
                <field name="code">pl_micro_pl_d</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">75.1 + 76.1 + 77.1 + 79.1 + 999998</field>
            </record>
            <record id="l10n_pl_micro_pl_e" model="account.report.line">
                <field name="name">E. Podatek dochodowy</field>
                <field name="code">pl_micro_pl_e</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">87</field>
            </record>
            <record id="l10n_pl_micro_pl_f" model="account.report.line">
                <field name="name">F. Zysk/strata netto (A−B+C−D−E) (dla jednostek mikro, o których mowa w art. 3 ust. 1a pkt 1, 3 i 4 oraz ust. 1b ustawy)</field>
                <field name="code">pl_micro_pl_f</field>
                <field name="expression_ids">
                    <record id="l10n_pl_micro_pl_f_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_micro_pl_a.balance - pl_micro_pl_b.balance + pl_micro_pl_c.balance - pl_micro_pl_d.balance - pl_micro_pl_e.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_micro_pl_g" model="account.report.line">
                <field name="name">G. Wynik finansowy netto ogółem (A−B+C−D−E) (dla jednostek mikro, o których mowa w art. 3 ust. 1a pkt 2 ustawy).</field>
                <field name="code">pl_micro_pl_g</field>
                <field name="expression_ids">
                    <record id="l10n_pl_micro_pl_g_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_micro_pl_g_1.balance - pl_micro_pl_g_2.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_micro_pl_g_1" model="account.report.line">
                        <field name="name">I. Nadwyżka przychodów nad kosztami (wartość dodatnia)</field>
                        <field name="code">pl_micro_pl_g_1</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_micro_pl_g_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">pl_micro_pl_a.balance - pl_micro_pl_b.balance + pl_micro_pl_c.balance - pl_micro_pl_d.balance - pl_micro_pl_e.balance</field>
                                <field name="subformula">if_above(PLN(0))</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_pl_micro_pl_g_2" model="account.report.line">
                        <field name="name">II. Nadwyżka kosztów nad przychodami (wartość ujemna)</field>
                        <field name="code">pl_micro_pl_g_2</field>
                        <field name="expression_ids">
                            <record id="l10n_pl_micro_pl_g_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-(pl_micro_pl_a.balance - pl_micro_pl_b.balance + pl_micro_pl_c.balance - pl_micro_pl_d.balance - pl_micro_pl_e.balance)</field>
                                <field name="subformula">if_above(PLN(0))</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
