# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON>il <PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr "<b>Publicación de LinkedIn</b>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr "El ID de la publicación en LinkedIn"

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages data: %r."
msgstr "Ocurrió un error al obtener la información de sus páginas: %r."

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages: %r."
msgstr "Ocurrió un error al obtener sus páginas: %r."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr "ID de la aplicación"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr "Secreto de aplicación"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Imagen del autor"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr ""
"Marque esto si quiere usar su cuenta de desarrollador de LinkedIn en lugar "
"de la proporcionada."

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr "Mostrar la vista previa de LinkedIn"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""
"No se pudo recuperar la publicación. Es posible que haya sido eliminada o "
"que no tenga permiso para verla."

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Me gusta"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr "ID de la cuenta de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr "URN de la cuenta de LinkedIn"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
#, python-format
msgid "LinkedIn Comments"
msgstr "Comentarios de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr "Me gusta en LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr "Vista previa de Linkedin"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr "Nombre Vanity de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "Token de acceso de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr "ID del autor de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr "URN del autor de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr "URL de la imagen del autor de LinkedIn"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "LinkedIn did not provide a valid access token."
msgstr "LinkedIn no proporcionó un token de acceso válido."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr "URL de la publicación de LinkedIn"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr "Cuenta de desarrollador de Linkedin"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr "Tipo de medios"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr "Publicar imagen"

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "Publicaciones"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr "Cuenta de red social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr "Publicación en redes sociales en vivo"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "Redes sociales"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "Publicación en redes sociales"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicación en redes sociales"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "Flujo social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr "Publicación en un flujo social"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr "El token de acceso se usa para realizar la solicitud de la API REST"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"Ocurrió un error en la URL que este servicio solicitó. Contacte al autor de "
"la aplicación."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr "No hay ninguna página vinculada a esta cuenta"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "Ocurrió un problema de autenticación durante su solicitud."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "No está autorizado. Póngase en contacto con su administrador."

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "Desconocido"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr "Use su propia cuenta de LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Se utiliza para hacer comparaciones cuando necesitamos restringir algunas "
"funciones para una red social específica ('facebook', 'twitter', ...)."

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr "Nombre Vanity que se usa para generar un enlace al autor."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr ""
"No pudimos subir su imagen, intente reducir el tamaño y después publíquela "
"de nuevo (error: falló durante el registro de la subida)."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr ""
"No pudimos subir su imagen, intente reducir el tamaño y después publíquela "
"de nuevo."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#, python-format
msgid "Wrong stream type for \"%s\""
msgstr "Tipo de flujo incorrecto para \"%s\""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "No tiene una suscripción activa. Compre una aquí: %s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid "unknown"
msgstr "desconocido"
