<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_financial_report_l10n_tw_pl" model="account.report">
        <field name="name">Profit and Loss</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.tw"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_l10n_tw_pl_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_report_l10n_tw_pl_op" model="account.report.line">
                <field name="name">Operating profit</field>
                <field name="code">OP</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_op_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">OI.balance - OC.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_tw_pl_oi" model="account.report.line">
                        <field name="name">Operating income</field>
                        <field name="code">OI</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_tw_pl_oi_expression" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-411 - 412 - 413 - 414 - 42</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_tw_pl_oc" model="account.report.line">
                        <field name="name">Operating cost</field>
                        <field name="code">OC</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_tw_pl_oc_expression" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">5</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_oe" model="account.report.line">
                <field name="name">Operating expenses</field>
                <field name="code">OE</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_oe_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">61</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_onp" model="account.report.line">
                <field name="name">Operating net profit (net loss)</field>
                <field name="code">ONP</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_onp_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">OP.balance - OE.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_noid" model="account.report.line">
                <field name="name">Non-operating income and depreciation</field>
                <field name="code">NOID</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_noid_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-71 - 72</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_npbt" model="account.report.line">
                <field name="name">Net profit before tax (net loss)</field>
                <field name="code">NBPT</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_npbt_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">ONP.balance + NOID.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_ite" model="account.report.line">
                <field name="name">Income tax expense (benefits)</field>
                <field name="code">ITE</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_ite_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">82</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_npatcbu" model="account.report.line">
                <field name="name">Net profit after tax (net loss) of continuing business units</field>
                <field name="code">NPATCBU</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_npatcbu_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">NBPT.balance - ITE.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_plcu" model="account.report.line">
                <field name="name">Profit and loss of closed units (after tax)</field>
                <field name="code">PLCU</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_plcu_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-84</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="ccount_financial_report_l10n_tw_pl_npatcp" model="account.report.line">
                <field name="name">Net profit after tax (net loss) for the current period</field>
                <field name="code">NPATCP</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="ccount_financial_report_l10n_tw_pl_npatcp_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">NPATCBU.balance + PLCU.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="ccount_financial_report_l10n_tw_pl_ocglcp" model="account.report.line">
                <field name="name">Other comprehensive gains and losses for the current period</field>
                <field name="code">OCGLCP</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level" eval="3"/>
                <field name="expression_ids">
                    <record id="ccount_financial_report_l10n_tw_pl_ocglcp_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-87</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_tw_pl_tcplcp" model="account.report.line">
                <field name="name">Total comprehensive profit and loss for the current period</field>
                <field name="code">TCPLCP</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_tw_pl_tcplcp_expression" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">NPATCP.balance + OCGLCP.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
        </field>
    </record>

</odoo>
