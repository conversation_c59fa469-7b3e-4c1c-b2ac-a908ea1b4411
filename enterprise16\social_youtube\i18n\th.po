# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
# Translators:
# K<PERSON>wu<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>dev <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr "ไอดีเพลย์ลิสต์ 'อัปโหลด' ที่ YouTube API ให้มา ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr "123 วิว •"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"ความคิดเห็น\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"ถูกใจ\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr "<span class=\"fw-bold\">ช่อง YouTube ของคุณ</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""
"<span>สิ่งเหล่านี้จะถูกเก็บไว้นานถึง 30 วันและรีเฟรชบ่อยครั้งเพื่อให้คุณเห็นภาพความเป็นจริงที่แม่นยำ</span>\n"
"                        <span>หากต้องการลบสิ่งเหล่านี้ออกจาก Odoo เพียงลบบัญชีนี้</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr "การเข้าถึงบัญชีของคุณสามารถเพิกถอนได้ตลอดเวลาจาก"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr "โทเคนการเข้าถึงที่จัดทำโดย YouTube API ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr "บัญชี"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr "เกิดข้อผิดพลาด"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr "จุดสิ้นสุดการตรวจสอบสิทธิ์ไม่ได้ให้โทเคนการรีเฟรช กรุณาลองอีกครั้ง"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "รูปภาพผู้เขียน"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "By using our Social YouTube Application, you implicitly agree to the:"
msgstr "ด้วยการใช้แอปพลิเคชันโซเชียล YouTube แสดงว่าคุณเห็นด้วยโดยปริยายต่อ:"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr "ช่อง"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Clear"
msgstr "ล้าง"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""
"ความคิดเห็นถูกทำเครื่องหมายเป็น 'ปิดการใช้งาน' สำหรับวิดีโอนี้ "
"อาจถูกกำหนดให้เป็น 'ส่วนตัว'"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Confirmation"
msgstr "การยืนยัน"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr "มีไอดีของวิดีโอที่ส่งคืนโดย YouTube API"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr "มีไอดีของหมวดหมู่วิดีโอที่ส่งคืนโดย YouTube API"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""
"ไม่สามารถเพิกถอนบัญชีของคุณได้\n"
"ข้อผิดพลาด: %s"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Do you also want to remove the video from your YouTube account?"
msgstr "คุณต้องการลบวิดีโอออกจากบัญชี YouTube ของคุณด้วยหรือไม่?"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Draft Video"
msgstr "ร่างวิดีโอ"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""
"วันหมดอายุของโทเค็นการเข้าถึงที่ได้รับจาก YouTube API ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr "โทเคนการเข้าถึง Google"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr "นโยบายความเป็นส่วนตัว Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr "รีเฟรชโทเคน Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr "ไอดี"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Likes"
msgstr "ถูกใจ"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr "ประเภทมีเดีย"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "No"
msgstr "ไม่"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr "ไอดีลูกค้า OAuth"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr "รหัสลับลูกค้า OAuth"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""
"Odoo จะสูญเสียการเข้าถึงบัญชี YouTube ของคุณ\n"
"                        และลบข้อมูลที่เกี่ยวข้องทั้งหมดออกจากฐานข้อมูลของคุณ"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr "เมื่อโพสต์แล้ว ให้ตั้งค่าวิดีโอเป็นสาธารณะ/ส่วนตัว/ไม่เป็นสาธารณะ"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
#, python-format
msgid "Please select a single YouTube account at a time."
msgstr "โปรดเลือกบัญชี YouTube ครั้งละหนึ่งบัญชี"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr "ส่วนตัว"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Processing..."
msgstr "กำลังดำเนินการ..."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr "สาธารณะ"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Reason:"
msgstr "เหตุผล:"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr "โทเคนการรีเฟรชที่จัดทำโดย YouTube API ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr "เพิกถอน"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr "เพิกถอนและลบ"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoke Account"
msgstr "เพิกถอนบัญชี"

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr "เพิกถอนบัญชี YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr "ขณะนี้การเพิกถอนโทเค็นการเข้าถึงจำกัดเฉพาะบัญชี YouTube เท่านั้น"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Select"
msgstr "เลือก"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr "บัญชีอื่นที่เลือก"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr "บัญชี YouTube ที่เลือก"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""
"เพียงเก็บชื่อไฟล์ของวิดีโอไว้ในขณะที่อัปโหลดวิดีโอไปยัง YouTube โดยตรง"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr "บัญชีโซเชียล"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr "โพสต์โซเชียลไลฟ์"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr "สื่อสังคมออนไลน์"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr "โพสต์โซเชียล"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr "เทมเพลตโพสต์โซเชียล"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr "สตรีมโซเชียล"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr "โพสต์สตรีมโซเชียล"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "ฟิลด์ 'ข้อความ' จำเป็นสำหรับไอดีโพสต์ %s"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr "วิดีโอที่เลือกเกินขนาดสูงสุดที่อนุญาต%s"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr "URL ที่บริการนี้ร้องขอการส่งคืนข้อผิดพลาด โปรดติดต่อผู้เขียนแอป"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr "วิดีโอที่คุณพยายามเผยแพร่ถูกลบออกจาก YouTube แล้ว"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "There is no channel linked with this YouTube account."
msgstr "ไม่มีช่องที่เชื่อมโยงกับบัญชี YouTube นี้"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""
"เพื่อให้บริการแอปพลิเคชันของเรา โปรดทราบว่าเราจัดเก็บข้อมูลต่อไปนี้จากบัญชี "
"YouTube ของคุณ:"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr "วันหมดอายุโทเคน"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "ไม่ได้รับอนุญาต โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid "Unknown"
msgstr "ไม่ทราบ"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr "ไม่อยู่ในรายการ"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Upload Video"
msgstr "อัปโหลดวิดีโอ"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Upload failed. Please try again."
msgstr "การอัปโหลดล้มเหลว กรุณาลองอีกครั้ง"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading %s"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading... %s%%"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr "ใช้บัญชี YouTube ของคุณ"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"ใช้ในการเปรียบเทียบเมื่อเราต้องการจำกัดฟีเจอร์บางอย่างสำหรับสื่อเฉพาะ "
"('facebook', 'twitter', ...)"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "Video"
msgstr "วิดีโอ"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr "คำอธิบายวิดีโอ"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr "ความเป็นส่วนตัวของวิดีโอ"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr "ชื่อวิดีโอ"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Video Upload"
msgstr "อัปโหลดไฟล์"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "Video not found. It could have been removed from Youtube."
msgstr "ไม่พบวิดีโอ อาจถูกลบออกจาก Youtube"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "ยอดเข้าชม"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Yes, delete it"
msgstr "ใช่ ลบออก"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "คุณไม่มีการสมัครสมาชิกที่ใช้งานอยู่ กรุณาซื้อที่นี้: %s"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "You have to upload a video when posting on YouTube."
msgstr "คุณต้องอัปโหลดวิดีโอเมื่อโพสต์บน YouTube"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
msgid "YouTube"
msgstr "YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr "โทเคนการเข้าถึง YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr "ไอดีหมวดหมู่ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr "รหัสช่อง YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr "ไอดีช่อง YouTube ที่จัดทำโดย YouTube API ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "YouTube Comments"
msgstr "ความคิดเห็น YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr "จำนวนความคิดเห็นของ YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr "บัญชีนักพัฒนา YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr "ไม่ถูกใจ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr "ถูกใจ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_ratio
msgid "YouTube Likes Ratio"
msgstr "อัตราส่วนการกดไลค์ของ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr "ไอดีลูกค้า OAuth ของ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr "รหัสลับลูกค้า OAuth ของ YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr "ตัวเลือกของ YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placehdoler"
msgstr "ตัวอย่างข้อความ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr "ดูตัวอย่าง YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr "YouTube Terms of Service (ToS)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr "YouTube รูปภาพภาพตัวอย่าง"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr "URL ภาพขนาดย่อของ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr "ไอดีเพลย์ลิสต์อัปโหลดของ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr "วิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr "คำอธิบายวิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr "ไอดีวิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr "ไอดีวิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr "ชื่อวิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr "URL วิดีโอ YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr "ยอดเข้าชม YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr ""
"YouTube ไม่ได้มอบโทเคนเพื่อการเข้าถึงที่ถูกต้อง หรือโทเคนอาจหมดอายุแล้ว"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid authorization code."
msgstr "YouTube ไม่ได้ให้โค้ดการรับรองที่ถูกต้อง"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr "ชื่อช่องและรูปภาพของคุณ"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "Your video is missing a correct title or description."
msgstr "วิดีโอของคุณไม่มีชื่อหรือคำอธิบายที่ถูกต้อง"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr "ข้อมูลเมตาของวิดีโอของคุณ รวมถึงชื่อและจำนวนการดู (แต่ไม่ใช่วิดีโอ)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""
"เช่น มีส่วนร่วมกับชุมชนทั้งหมดของคุณด้วยแอปเดียว! https://www.odoo.com/trial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr "เช่น Odoo Social Tutorial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr "แผงการเข้าถึงบัญชีแอปบุคคลที่สามของ Google"
