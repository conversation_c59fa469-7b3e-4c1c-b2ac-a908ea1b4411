# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_account_taxcloud
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 08:13+0000\n"
"PO-Revision-Date: 2017-04-28 13:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_account_invoice
msgid "Invoice"
msgstr "Τιμολόγιο"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:34
#, python-format
msgid "The configuration of TaxCloud is in the Accounting app, Settings menu."
msgstr ""

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:34
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.actions.server,name:sale_account_taxcloud.action_sale_order_update_taxes
msgid "Update taxes with Taxcloud"
msgstr ""
