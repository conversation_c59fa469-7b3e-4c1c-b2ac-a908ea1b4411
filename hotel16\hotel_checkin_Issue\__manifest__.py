# -*- coding: utf-8 -*-

{
    "name": "Hotel Check-in",
    "version": "********.0",
    "author": "Your Company",
    "category": "Generic Modules/Hotel Management",
    "summary": "Standalone module for hotel check-in operations",
    "description": """
        Standalone module for Hotel Check-in Management.
        * List of confirmed reservations
        * Quick check-in functionality
        * Restricted access for check-in staff
        * Modern UI dashboard for room status
        
        This module provides a simplified interface for front desk staff to perform check-ins
        without access to the full hotel management system.
    """,
    "depends": ["base", "web", "hotel", "hotel_management"],
    "data": [
        "security/hotel_checkin_security.xml",
        "security/ir.model.access.csv",
        "views/hotel_checkin_view.xml",
        "data/hotel_checkin_data.xml",
    ],
    "assets": {
        "web.assets_backend": [
            "hotel_checkin/static/src/css/hotel_checkin.css",
            "hotel_checkin/static/src/js/hotel_checkin.js",
            "hotel_checkin/static/src/xml/hotel_checkin_templates.xml",
            "hotel_checkin/static/src/scss/hotel_checkin.scss",
        ],
    },
    "demo": [],
    "installable": True,
    "application": True,
    "sequence": 1,
    "license": "LGPL-3",
} 