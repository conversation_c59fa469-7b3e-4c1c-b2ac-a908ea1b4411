# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * voip
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-21 17:08+0000\n"
"PO-Revision-Date: 2017-11-21 17:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jo<PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:207
#, python-format
msgid " Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_nbr
msgid "# of Cases"
msgstr "# Slucajeva"

#. module: voip
#: model:ir.model,name:voip.model_mail_activity
msgid "Activity"
msgstr "Aktivnost"

#. module: voip
#: model:ir.model,name:voip.model_mail_activity_type
msgid "Activity Type"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_always_transfer
msgid "Always Redirect to Handset"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:49
#, python-format
msgid "Backspace"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:35
#, python-format
msgid "CONTACTS"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_call_date
msgid "Call Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_name
msgid "Call Name"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:192
#, python-format
msgid "Call from "
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:182
#: code:addons/voip/models/voip_phonecall.py:224
#, python-format
msgid "Call to "
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:574
#, python-format
msgid "Calling "
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date by Month"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:153
#: model:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
#, python-format
msgid "Cancel"
msgstr "Odustani"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:43
#, python-format
msgid "Cancel the activity"
msgstr ""

#. module: voip
#: selection:voip.phonecall,state:0 selection:voip.phonecall.report,state:0
msgid "Cancelled"
msgstr "Poništeno"

#. module: voip
#: model:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Click to log the summary of a phonecall."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:405
#, python-format
msgid "Click to unblock"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation"
msgstr "Kreacija"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr "Kreiran dana"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:126
#, python-format
msgid "Customer"
msgstr "Kupac"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:390
#, python-format
msgid "Customer unavailable"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_call_date
msgid "Date"
msgstr "Datum"

#. module: voip
#: selection:res.config.settings,mode:0
msgid "Demo"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:5
#, python-format
msgid "Display Dialing Panel"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: voip
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "Don't Reschedule"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_date_deadline
msgid "Due Date"
msgstr "Datum dospijeća"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_duration
msgid "Duration"
msgstr "Trajanje"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall_duration
msgid "Duration in minutes."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:48
#, python-format
msgid "Enter the number..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_type_create_voip_phonecall
msgid "Generates a voip phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall_sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_external_phone
msgid "Handset Extension"
msgstr ""

#. module: voip
#: selection:voip.phonecall,state:0 selection:voip.phonecall.report,state:0
msgid "Held"
msgstr "Odrzan"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_mail_activity_type_create_voip_phonecall
msgid ""
"If set to true, when an activity of this type is scheduled, a voip phonecall"
" is created"
msgstr ""

#. module: voip
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "In 1 Week"
msgstr ""

#. module: voip
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "In 15 Day"
msgstr ""

#. module: voip
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "In 2 Months"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_in_queue
msgid "In Call Queue"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""

#. module: voip
#: selection:voip.phonecall,type:0
msgid "Incoming"
msgstr "Dolazni"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:358
#, python-format
msgid "Incoming call from "
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_is_call_type
msgid "Is it a call type activity?"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator___last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall___last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard___last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report___last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator_write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_activity_id
msgid "Linked Activity"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_mail_message_id
msgid "Linked Chatter Message"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:143
#, python-format
msgid "Log"
msgstr "Log"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_phonecall_id
msgid "Logged Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:148
#, python-format
msgid "Mark as done"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_mobile
msgid "Mobile"
msgstr "Mobilni"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner_sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users_sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings_mode
msgid "Mode"
msgstr "Način"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Month"
msgstr "Mesec"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:32
#, python-format
msgid "NEXT ACTIVITIES"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:136
#, python-format
msgid "Next"
msgstr "Sledeće"

#. module: voip
#: selection:voip.phonecall,state:0 selection:voip.phonecall.report,state:0
msgid "Not Held"
msgstr "Ne Odrzivo"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_note
msgid "Note"
msgstr "Zabilješka"

#. module: voip
#: model:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:152
#, python-format
msgid "One or more parameter is missing. Please check your configuration."
msgstr ""

#. module: voip
#: selection:voip.phonecall,type:0
msgid "Outgoing"
msgstr "Izlazno"

#. module: voip
#: model:ir.ui.view,arch_db:voip.res_user_form
msgid "PBX Configuration"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings_pbx_ip
msgid "PBX Server IP"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_phone
msgid "Phone"
msgstr "Telefon:"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner_sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users_sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr "TelPozivi"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "Phonecalls by user"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:155
#, python-format
msgid "Please check if a phone number is given for the current phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:245
#, python-format
msgid "Please check your configuration.</br> (Reason receives :"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:99
#, python-format
msgid ""
"Problem during the connection. Check if the application is allowed to access"
" your microphone from your browser."
msgstr ""

#. module: voip
#: selection:res.config.settings,mode:0
msgid "Production"
msgstr "Proizvodnja"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:29
#, python-format
msgid "RECENT"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:23
#, python-format
msgid "Refresh the Panel"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_ignore_incoming
msgid "Reject All Incoming Calls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:40
#, python-format
msgid "Remove from the queue"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_user_id
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr "Odgovoran"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_login
msgid "SIP Login / Browser's Extension"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_sip_password
msgid "SIP Password"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:136
#, python-format
msgid "Schedule"
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from CRM app"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_reschedule_option
msgid "Schedule A New Activity"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:22
#, python-format
msgid "Search Phone Calls..."
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr "Pretrazi Telefonske pozive"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:121
#, python-format
msgid "Send mail"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_reschedule_date
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "Specific Date"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:574
#, python-format
msgid "Start Calling"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_start_time
msgid "Start time"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report_state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_state
msgid "Status"
msgstr "Status"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard_summary
msgid "Summary"
msgstr "Sumarno"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings_pbx_ip
msgid "The IP adress of your PBX Server"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings_wsServer
msgid "The URL of your WebSocket"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:403
#, python-format
msgid "The customer is temporary unavailable. Please try later."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:460
#, python-format
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.</br> (Reason "
"received: %s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:157
#, python-format
msgid "The phonecall has no number"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:163
#, python-format
msgid ""
"The server configuration could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall_state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:201
#, python-format
msgid "The websocket uri could be wrong."
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: selection:voip.phonecall,state:0 selection:voip.phonecall.report,state:0
msgid "To Do"
msgstr "Za Uraditi"

#. module: voip
#: selection:voip.phonecall.log.wizard,reschedule_option:0
msgid "Tomorrow"
msgstr "Sutra"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_transfer_choice
msgid "Transfer Choice"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_type
msgid "Type"
msgstr "Tip"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/voip_phonecall.js:130
#, python-format
msgid "Unknown"
msgstr "Nepoznato"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: voip
#: model:ir.ui.view,arch_db:voip.view_voip_user_config
msgid "VOIP Configuration"
msgstr ""

#. module: voip
#: code:addons/voip/wizard/voip_phonecall_transfer_wizard.py:34
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings_wsServer
msgid "WebSocket"
msgstr ""

#. module: voip
#: code:addons/voip/wizard/voip_phonecall_transfer_wizard.py:35
#, python-format
msgid ""
"Wrong configuration for the call. There is no external phone number "
"configured"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:168
#: code:addons/voip/static/src/js/dialing_panel.js:255
#: code:addons/voip/static/src/js/dialing_panel.js:276
#, python-format
msgid "You are already in a call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:94
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:53
#: code:addons/voip/static/src/xml/voip_phonecall.xml:94
#, python-format
msgid "min"
msgstr "min"

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/voip_phonecall.xml:53
#: code:addons/voip/static/src/xml/voip_phonecall.xml:94
#, python-format
msgid "sec"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:244
#, python-format
msgid "the connection cannot be made. "
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
msgid "transfer Option"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard_transfer_number
msgid "transfer To"
msgstr ""

#. module: voip
#: model:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
msgid "transfer the Call"
msgstr ""

#. module: voip
#: selection:voip.phonecall.transfer.wizard,transfer_choice:0
msgid "transfer to another External Phone"
msgstr ""

#. module: voip
#: selection:voip.phonecall.transfer.wizard,transfer_choice:0
msgid "transfer to your external phone"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_configurator
msgid "voip.configurator"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "voip.phonecall"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "voip.phonecall.log.wizard"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_transfer_wizard
msgid "voip.phonecall.transfer.wizard"
msgstr ""
