<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- JVA Approval Request Email Template -->
        <record id="email_template_jva_approval_request" model="mail.template">
            <field name="name">JVA Approval Request</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject">JVA Approval Required: ${object.job_title} - ${object.name}</field>
            <field name="email_from">${(object.requested_by.work_email or user.email)|safe}</field>
            <field name="email_to">${object.env.ref('base.group_system').users.filtered(lambda u: u.has_group('alwasead_job_position_database.group_jva_approver')).mapped('email')|join(',')}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p>Dear General Manager,</p>
    
    <p>A new Job Vacancy Announcement (JVA) form has been submitted for your approval:</p>
    
    <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>JVA Reference:</strong></td>
            <td style="padding: 8px;">${object.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Job Position:</strong></td>
            <td style="padding: 8px;">${object.job_title}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Division:</strong></td>
            <td style="padding: 8px;">${object.division_id.name or 'N/A'}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Department:</strong></td>
            <td style="padding: 8px;">${object.department_id.name or 'N/A'}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Requested Positions:</strong></td>
            <td style="padding: 8px;">${object.requested_positions}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Priority:</strong></td>
            <td style="padding: 8px;">${object.priority.title()}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Requested By:</strong></td>
            <td style="padding: 8px;">${object.requested_by.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Request Date:</strong></td>
            <td style="padding: 8px;">${object.request_date.strftime('%Y-%m-%d %H:%M')}</td>
        </tr>
    </table>
    
    % if object.justification:
    <p><strong>Justification:</strong></p>
    <p>${object.justification}</p>
    % endif
    
    <p>Please review and approve or reject this JVA form in the system.</p>
    
    <p>
        <a href="/web#id=${object.id}&amp;view_type=form&amp;model=hr.jva.form"
           style="background-color: #875A7B; padding: 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
            Review JVA Form
        </a>
    </p>
    
    <p>Best regards,<br/>
    AlWasead HR System</p>
</div>
            </field>
        </record>

        <!-- JVA Approved Email Template -->
        <record id="email_template_jva_approved" model="mail.template">
            <field name="name">JVA Approved</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject">JVA Approved: ${object.job_title} - ${object.name}</field>
            <field name="email_from">${(object.approved_by.work_email or user.email)|safe}</field>
            <field name="email_to">${object.requested_by.work_email}</field>
            <field name="email_cc">${object.env.ref('hr.group_hr_user').users.mapped('email')|join(',')}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p>Dear ${object.requested_by.name},</p>
    
    <p style="color: green; font-weight: bold;">Your Job Vacancy Announcement (JVA) form has been APPROVED!</p>
    
    <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>JVA Reference:</strong></td>
            <td style="padding: 8px;">${object.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Job Position:</strong></td>
            <td style="padding: 8px;">${object.job_title}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Approved Positions:</strong></td>
            <td style="padding: 8px;">${object.requested_positions}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Approved By:</strong></td>
            <td style="padding: 8px;">${object.approved_by.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Approval Date:</strong></td>
            <td style="padding: 8px;">${object.approval_date.strftime('%Y-%m-%d %H:%M')}</td>
        </tr>
    </table>
    
    <p><strong>Next Steps:</strong></p>
    <ul>
        <li>Recruitment process can now begin</li>
        <li>Job advertisements can be created</li>
        <li>Contact HR team to proceed with recruitment</li>
    </ul>
    
    <p>
        <a href="/web#id=${object.id}&amp;view_type=form&amp;model=hr.jva.form"
           style="background-color: #28a745; padding: 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
            View JVA Form
        </a>
    </p>
    
    <p>Best regards,<br/>
    AlWasead HR System</p>
</div>
            </field>
        </record>

        <!-- JVA Rejected Email Template -->
        <record id="email_template_jva_rejected" model="mail.template">
            <field name="name">JVA Rejected</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="subject">JVA Rejected: ${object.job_title} - ${object.name}</field>
            <field name="email_from">${(object.rejected_by.work_email or user.email)|safe}</field>
            <field name="email_to">${object.requested_by.work_email}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p>Dear ${object.requested_by.name},</p>
    
    <p style="color: red; font-weight: bold;">Your Job Vacancy Announcement (JVA) form has been REJECTED.</p>
    
    <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>JVA Reference:</strong></td>
            <td style="padding: 8px;">${object.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Job Position:</strong></td>
            <td style="padding: 8px;">${object.job_title}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Rejected By:</strong></td>
            <td style="padding: 8px;">${object.rejected_by.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Rejection Date:</strong></td>
            <td style="padding: 8px;">${object.rejection_date.strftime('%Y-%m-%d %H:%M')}</td>
        </tr>
    </table>
    
    % if object.rejection_reason:
    <p><strong>Rejection Reason:</strong></p>
    <p style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545;">
        ${object.rejection_reason}
    </p>
    % endif
    
    <p><strong>Next Steps:</strong></p>
    <ul>
        <li>Review the rejection reason</li>
        <li>Make necessary adjustments to the JVA form</li>
        <li>Resubmit for approval when ready</li>
    </ul>
    
    <p>
        <a href="${object.get_base_url()}/web#id=${object.id}&amp;view_type=form&amp;model=hr.jva.form" 
           style="background-color: #dc3545; padding: 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
            View JVA Form
        </a>
    </p>
    
    <p>Best regards,<br/>
    AlWasead HR System</p>
</div>
            </field>
        </record>

        <!-- Staffing Notification Email Template -->
        <record id="email_template_staffing_notification" model="mail.template">
            <field name="name">Staffing Level Notification</field>
            <field name="model_id" ref="hr.model_hr_job"/>
            <field name="subject">Staffing Alert: ${object.name} is Understaffed</field>
            <field name="email_from">${user.email}</field>
            <field name="email_to">${object.department_id.manager_id.work_email}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p>Dear ${object.department_id.manager_id.name},</p>
    
    <p style="color: orange; font-weight: bold;">Staffing Level Alert</p>
    
    <p>The following job position in your department is currently understaffed:</p>
    
    <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Job Position:</strong></td>
            <td style="padding: 8px;">${object.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Department:</strong></td>
            <td style="padding: 8px;">${object.department_id.name}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Required Positions:</strong></td>
            <td style="padding: 8px;">${object.required_positions}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Current Employees:</strong></td>
            <td style="padding: 8px;">${object.current_employees}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #f0f0f0;"><strong>Vacant Positions:</strong></td>
            <td style="padding: 8px;">${object.vacant_positions}</td>
        </tr>
    </table>
    
    <p>Consider creating a JVA form to request approval for recruitment.</p>
    
    <p>
        <a href="${object.get_base_url()}/web#id=${object.id}&amp;view_type=form&amp;model=hr.job" 
           style="background-color: #875A7B; padding: 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
            View Job Position
        </a>
    </p>
    
    <p>Best regards,<br/>
    AlWasead HR System</p>
</div>
            </field>
        </record>

    </data>
</odoo>
