# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_reports
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_employee_exit
msgid "# Departure Employee"
msgstr "# <PERSON>st<PERSON><PERSON>"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_new_employee
msgid "# New Employees"
msgstr "# Neue Mitarbeiter"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Company"
msgstr "Unternehmen"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_id
msgid "Contract"
msgstr "Vertrag"

#. module: hr_contract_reports
#: model:ir.model,name:hr_contract_reports.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Analysebericht über Vertrag und Mitarbeiter"

#. module: hr_contract_reports
#: model:ir.ui.menu,name:hr_contract_reports.menu_report_contract_employee_all
msgid "Contracts"
msgstr "Verträge"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date
msgid "Date"
msgstr "Datum"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_start
msgid "Date First Contract Started"
msgstr "Startdatum des ersten Vertrags"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date_end_contract
msgid "Date Last Contract Ended"
msgstr "Enddatum vom letzten Vertrag "

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Department"
msgstr "Abteilung"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__departure_reason_id
msgid "Departure Reason"
msgstr "Grund für den Austritt"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__age_sum
msgid "Duration Contract"
msgstr "Vertragsdauer"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employee"
msgstr "Mitarbeiter"

#. module: hr_contract_reports
#: model:ir.actions.act_window,name:hr_contract_reports.contract_employee_report_action
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.hr_contract_employee_report_view_tree
msgid "Employees Analysis"
msgstr "Mitarbeiteranalyse"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Last 365 Days"
msgstr "Letzten 365 Tage"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__start_date_months
msgid "Months of first date of this month since 01/01/1970"
msgstr "Monate des ersten Datums dieses Monats seit 01/01/1970"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__end_date_months
msgid "Months of last date of this month since 01/01/1970"
msgstr "Monate des letzten Datums dieses Monats seit 01/01/1970"

#. module: hr_contract_reports
#: model_terms:ir.actions.act_window,help:hr_contract_reports.contract_employee_report_action
msgid "No data to display"
msgstr "Keine Daten zum Anzeigen"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__wage
msgid "Wage"
msgstr "Lohn"
