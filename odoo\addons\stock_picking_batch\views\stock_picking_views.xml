<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_picking_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <field name="batch_id" invisible="1"/>
                <button type="object"
                        name="action_view_batch"
                        class="oe_stat_button"
                        icon="fa-truck"
                        string="Batch"
                        attrs="{'invisible': [('batch_id', '=', False)]}"/>
            </div>
        </field>
    </record>
</odoo>
