# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
# Translators:
# <PERSON> <<PERSON><PERSON><PERSON><PERSON>@hotmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# Серге<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"Идентификатор плейлиста «Загрузки», предоставляемый API YouTube, никогда не "
"должен устанавливаться вручную. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr "123 просмотра •"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr "<span class=\"fw-bold\">Ваш канал на YouTube</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""
"<span>Они хранятся до 30 дней и часто обновляются, чтобы дать вам точное представление о реальности. </span>\n"
"<span>Чтобы удалить их из Odoo, просто удалите эту учетную запись.</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr ""
"Доступ к вашей учетной записи может быть отменен в любое время начиная с "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr ""
"Токен доступа, предоставляемый API YouTube, никогда не должен "
"устанавливаться вручную. "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr "Счёт"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr "Произошла ошибка."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr ""
"Запрос аутентификации не предоставил токен обновления. Пожалуйста, "
"попробуйте еще раз."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Изображение автора"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "By using our Social YouTube Application, you implicitly agree to the:"
msgstr ""
"Используя наше приложение YouTube для социальных сетей, вы безоговорочно "
"соглашаетесь со следующим: "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr "Канал"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Clear"
msgstr "Очистить"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""
"Комментарии для этого видео помечены как «отключенные». Они могли быть "
"установлены как «приватный»."

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Confirmation"
msgstr "Подтверждение"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr "Содержит идентификатор видео, возвращенный API YouTube. "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr "Содержит идентификатор категории видео, возвращенный API YouTube. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""
"Не удалось отозвать вашу учетную запись.\n"
"Ошибка: %s"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr "Создал"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Do you also want to remove the video from your YouTube account?"
msgstr "Желаете ли Вы также удалить видео из своего аккаунта YouTube?"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Draft Video"
msgstr "Черновое видео "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""
"Дата истечения срока действия токена доступа, предоставляемого API YouTube; "
"этот срок никогда не следует устанавливать вручную."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr "Токен доступа Google "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr "Политика конфиденциальности Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr "Токен обновления Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr "Идентификатор"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Лайки"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr "Тип носителя"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "No"
msgstr "Нет"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr "ID клиента OAuth "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr "Секретный ключ клиента OAuth "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""
"Odoo потеряет доступ к вашей учетной записи YouTube\n"
"                        и удалит все связанные с ним данные из вашей базы данных."

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr ""
"После размещения установите видео в качестве общедоступного/частного/вне "
"списка"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
#, python-format
msgid "Please select a single YouTube account at a time."
msgstr "Пожалуйста, выбирайте по одному аккаунту YouTube за раз."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr "Личное"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Processing..."
msgstr "Обработка..."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr "Публичный"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Reason:"
msgstr "Причина:"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"Токен обновления, предоставляемый API YouTube, никогда не должен "
"устанавливаться вручную. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr "Отменить"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr "Отмена и удаление"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoke Account"
msgstr "Отмена учетной записи"

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr "Отозвать аккаунт на YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr ""
"В настоящее время отмена маркеров доступа распространяется только на "
"аккаунты YouTube."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Select"
msgstr "Выбрать"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr "Выбраны другие учетные записи "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr "Выбранные аккаунты YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""
"Просто содержит имя файла видео, поскольку само видео загружается "
"непосредственно на YouTube."

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr "Учетная запись соц. сети"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr "Пост в социальных сетях"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr "Социальные сети"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr "Пост в социальной сети"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr "Шаблон социальных постов"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr "Источник"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr "Пост источника соц. сети"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "Поле \"сообщение\" обязательно для идентификатора поста %s"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr "Выбранное видео превышает максимально допустимый размер %s."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"URL-адрес, запрошенный этой службой, возвратил ошибку. Свяжитесь с автором "
"приложения. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr "Видео, которое вы пытаетесь опубликовать, было удалено с YouTube."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "There is no channel linked with this YouTube account."
msgstr "С этим аккаунтом на YouTube не связано ни одного канала."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""
"Обратите внимание, что для предоставления наших сервисов приложений мы "
"храним следующие данные из вашей учетной записи YouTube: "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr "Срок действия токена"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "Несанкционировано. Обратитесь к своему администратору. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid "Unknown"
msgstr "Неизвестный"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr "Без списка"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Upload Video"
msgstr "Загрузить видео"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Upload failed. Please try again."
msgstr "Загрузка не удалась. Пожалуйста, попробуйте еще раз."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading %s"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading... %s%%"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr "Используйте свой собственный аккаунт YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Используется для сравнения, когда нам нужно ограничить некоторые функции для"
" определенных медиа носителей ('facebook', 'twitter', ...)."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "Video"
msgstr "Видео"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr "Описание видео"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr "Конфиденциальность видео"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr "Заголовок видео"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Video Upload"
msgstr "Загрузка видео"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "Video not found. It could have been removed from Youtube."
msgstr "Видео не найдено. Возможно оно удалено из Youtube."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "Представления"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Yes, delete it"
msgstr "Да, удалить"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""
"У вас нет активных подписок. Пожалуйста, приобретите хотя бы одну здесь: %s"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "You have to upload a video when posting on YouTube."
msgstr "Вы должны загрузить видео при размещении на YouTube."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
msgid "YouTube"
msgstr "YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr "Токен доступа к YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr "Идентификатор категории YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr "Идентификатор канала YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"Идентификатор канала YouTube, предоставляемый API YouTube, никогда не должен"
" устанавливаться вручную."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "YouTube Comments"
msgstr "Комментарии YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr "Количество комментариев на YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr "Учетная запись разработчика YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr "Дизлайки YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr "Лайки YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_ratio
msgid "YouTube Likes Ratio"
msgstr "Соотношение количества лайков на YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr "Идентификатор клиента YouTube OAuth"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr "Секретный ключ клиента YouTube OAuth"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr "Параметры YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placehdoler"
msgstr "YouTube плейсхолдер"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr "Предварительный просмотр на YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr "Условия использования YouTube (ToS)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr "Значок YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr "Url миниатюры YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr "Идентификатор плейлиста загрузки YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr "YouTube видео"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr "Описание видео YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr "Идентификатор видео YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr "Идентификатор видео YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr "Заголовок видео YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr "Url видео YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr "Просмотры YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr ""
"YouTube не предоставил действительный токен доступа либо срок его действия "
"истек."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid authorization code."
msgstr "YouTube не предоставил корректный код авторизации."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr "Название и изображение вашего канала"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "Your video is missing a correct title or description."
msgstr "В вашем видео отсутствует правильный заголовок или описание."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr ""
"Метаданные вашего видео, включая название и количество просмотров (но не "
"само видео)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""
"например Вовлеките все свое сообщество с помощью одного приложения! "
"https://www.odoo.com/trial "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr "например Odoo Social Tutorial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr "панель доступа к учетной записи сторонних приложений Google"
