# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_margin
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-03-14 00:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "Nº facturado en compra"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "Nº facturado en venta"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "#Purchased"
msgstr "#Comprados"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Criterios de análisis"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Precio medio en las facturas de cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_avg_price
msgid "Avg. Price in Vendor Bills "
msgstr "Avg . Precio en facturas de proveedores"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_avg_price
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_avg_price
msgid "Avg. Unit Price"
msgstr "Precio unidad promedio"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Precio de catálogo"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership
msgid "Check if the product is eligible for membership."
msgstr "Compruebe si el producto es elegible para la asociación."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_create_date
msgid "Created on"
msgstr "Creado en"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_taxes_id
msgid "Customer Taxes"
msgstr "Impuestos cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership_date_from
msgid "Date from which membership becomes active."
msgstr "Fecha desde la que el socio está activo."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership_date_to
msgid "Date until which membership remains active."
msgstr "Fecha hasta la que el socio permanece activo."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Draft, Open and Paid"
msgstr "Borrador, abierto y de pago"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_expected_margin
msgid "Expected Margin"
msgstr "Margen previsto"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Margen previsto (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_expected
msgid "Expected Sale"
msgstr "Venta prevista"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Venta prevista - Coste normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Venta prevista - Volumen de negocio"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Margen previsto * 100 / Venta prevista"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_property_account_expense_id
msgid "Expense Account"
msgstr "Cuenta de gastos"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_from_date
msgid "From"
msgstr "Desde"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Información general"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_property_account_income_id
msgid "Income Account"
msgstr "Cuenta de ingresos"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product_invoice_state
msgid "Invoice State"
msgstr "Estado factura"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_date_from
msgid "Margin Date From"
msgstr "Fecha desde del margen"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_date_to
msgid "Margin Date To"
msgstr "Fecha hasta del margen"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Márgenes"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership
msgid "Membership"
msgstr "Socio"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership_date_to
msgid "Membership End Date"
msgstr "Fecha fin de asociación"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership_date_from
msgid "Membership Start Date"
msgstr "Fecha de inicio de la asociación"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_normal_cost
msgid "Normal Cost"
msgstr "Coste normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Coste normal - Coste total"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Márgenes abiertos"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Open and Paid"
msgstr "Abierto y pagado"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Paid"
msgstr "Pagado"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product"
msgstr "Producto"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_email_template_id
msgid "Product Email Template"
msgstr "Plantilla de correo electrónico de producto"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Margen de producto"

#. module: product_margin
#: code:addons/product_margin/wizard/product_margin.py:66
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
#, python-format
msgid "Product Margins"
msgstr "Márgenes de producto"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Categorías de propiedades"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_gap
msgid "Purchase Gap"
msgstr "Diferencia compra"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Compras"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Ventas"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Diferencia ventas"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Precio estándar"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr ""
"Suma de Multiplicación de los precios de coste y cantidad de facturas de "
"proveedores"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr ""
"Suma de la multiplicación del precio de factura y la cantidad de las "
"facturas de cliente."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr ""
"Suma de la multiplicación del precio de la factura y la cantidad de facturas "
"de proveedores"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer Invoices"
msgstr ""
"Suma de la multiplicación del PVP y la cantidad de las facturas de cliente."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Suma de cantidad en facturas de cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Suma de Cantidad de facturas de proveedores"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_property_account_expense_id
msgid ""
"This account will be used for invoices instead of the default one to value "
"expenses for the current product."
msgstr ""
"Esta cuenta se utilizará en facturas, en lugar de la cuenta por defecto, "
"para valorar gastos de este producto."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_property_account_income_id
msgid ""
"This account will be used for invoices instead of the default one to value "
"sales for the current product."
msgstr ""
"Esta cuenta se utilizará en facturas, en lugar de la cuenta por defecto, "
"para valorar las ventas de este producto."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_to_date
msgid "To"
msgstr "Hasta"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Total coste"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_margin
msgid "Total Margin"
msgstr "Margen total"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Tasa de margen total (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Margen total * 100 / Volumen de negocio"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Volumen de negocio"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_margin
msgid "Turnover - Standard price"
msgstr "Volumen de negocio - Precio estándar"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Impuestos de proveedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to "
"an email template."
msgstr ""
"Al validar una factura, un correo electrónico será enviado al cliente basado "
"en esta plantilla. El cliente recibirá un correo electrónico para cada "
"producto vinculado a una plantilla de correo electrónico ."
