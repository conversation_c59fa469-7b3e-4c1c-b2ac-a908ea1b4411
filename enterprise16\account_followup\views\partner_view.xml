<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="res_partner_view_form">
            <field name="name">res.partner.view.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button groups="account.group_account_invoice" type="object" class="oe_stat_button" name="open_action_followup" icon="fa-list" attrs="{'invisible': [('total_due', '=', 0)]}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value"><field name="total_due" widget='monetary' options="{'currency_field': 'currency_id'}"/></span>
                            <span class="o_stat_text">Due</span>
                        </div>
                    </button>
                </div>
            </field>
        </record>
    </data>
</odoo>
