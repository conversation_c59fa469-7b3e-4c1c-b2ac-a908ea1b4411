# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# Lasse L, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-23 08:33+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__project_template_use_documents
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__project_template_use_documents
msgid "Documents"
msgstr "Dokument"

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__documents_allowed_company_id
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__documents_allowed_company_id
msgid "Documents Allowed Company"
msgstr "Företag med tillåtna dokument"

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_3
msgid "Done/Archived"
msgstr "Klar/Arkiverad"

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_2
msgid "In Use"
msgstr "I bruk"

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_3
msgid "Miscellaneous"
msgstr "Diverse"

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_1
msgid "New/Unsorted"
msgstr "Ny/Osorterad"

#. module: documents_project_sale
#: model:ir.model.fields,help:documents_project_sale.field_product_product__template_folder_id
#: model:ir.model.fields,help:documents_project_sale.field_product_template__template_folder_id
msgid ""
"On sales order confirmation, a workspace will be automatically generated for"
" the project based on this template."
msgstr ""
"Vid bekräftelse av försäljningsorder kommer ett arbetsutrymme automatiskt "
"att genereras för projektet baserat på denna mall."

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_2
msgid "Photos"
msgstr "Bilder"

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_1
msgid "Plans"
msgstr "Planeringar"

#. module: documents_project_sale
#: model:ir.model,name:documents_project_sale.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: documents_project_sale
#: model:product.template,name:documents_project_sale.product_1
msgid "Renovation Architect (Workspace Template)"
msgstr "Renoveringsarkitekt (Arbetsytans mall)"

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder
msgid "Renovation Projects"
msgstr "Renoveringsprojekt"

#. module: documents_project_sale
#: model:ir.model,name:documents_project_sale.model_sale_order
msgid "Sales Order"
msgstr "Order"

#. module: documents_project_sale
#: model:documents.facet,name:documents_project_sale.documents_folder_facet_1
msgid "Status"
msgstr "Status"

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template
msgid "Template"
msgstr "Mall"

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__template_folder_id
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__template_folder_id
msgid "Workspace Template"
msgstr "Mall för arbetsutrymme"
