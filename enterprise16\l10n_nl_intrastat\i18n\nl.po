# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_intrastat
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-03 10:31+0000\n"
"PO-Revision-Date: 2021-12-20 18:16+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"

#. module: l10n_nl_intrastat
#: code:addons/l10n_nl_intrastat/models/account_intrastat_report.py:0
#: model_terms:ir.ui.view,arch_db:l10n_nl_intrastat.res_company_form_inherit_l10n_nl
#, python-format
msgid "CBS"
msgstr "CBS"

#. module: l10n_nl_intrastat
#: model:ir.model,name:l10n_nl_intrastat.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_nl_intrastat
#: code:addons/l10n_nl_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Export (CBS)"
msgstr "Exporteer (CBS)"

#. module: l10n_nl_intrastat
#: model:ir.model,name:l10n_nl_intrastat.model_account_intrastat_report
msgid "Intrastat Report"
msgstr "Intrastat raportage"

#. module: l10n_nl_intrastat
#: model:ir.model.fields,field_description:l10n_nl_intrastat.field_res_company__l10n_nl_cbs_reg_number
msgid "Registration Number"
msgstr "Registratienummer"

#. module: l10n_nl_intrastat
#: code:addons/l10n_nl_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid ""
"The transaction codes that have been used are inconsistent with the time "
"period. Before January 2021 the transaction codes for a period should "
"consist of single-digits only. Before January 2022 transactions codes should "
"consist of exclusively single-digits or double-digits. Please ensure all "
"transactions in the specified period utilise the correct transaction codes."
msgstr ""
"De gebruikte transactiecodes komen niet overeen met de tijdsperiode. Vóór "
"januari 2021 dienen de transactiecodes voor een periode alleen uit enkele "
"cijfers te bestaan. Vóór januari 2022 dienen transactiecodes uitsluitend uit "
"enkele of dubbele cijfers te bestaan. Zorg ervoor dat alle transacties in de "
"opgegeven periode de juiste transactiecodes gebruiken."

#. module: l10n_nl_intrastat
#: code:addons/l10n_nl_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid ""
"The transaction codes that have been used are inconsistent with the time "
"period. From the start of January 2022 onwards the transaction codes should "
"consist of two digits only (no single-digit codes). Please ensure all "
"transactions in the specified period utilise the correct transaction codes."
msgstr ""
"De gebruikte transactiecodes komen niet overeen met de tijdsperiode. Vanaf "
"begin januari 2022 dienen de transactiecodes slechts uit twee cijfers te "
"bestaan (geen enkelcijferige codes). Zorg ervoor dat alle transacties in de "
"opgegeven periode de juiste transactiecodes gebruiken."
