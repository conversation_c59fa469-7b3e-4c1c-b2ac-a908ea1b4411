# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_enterprise
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_enterprise
#: model:ir.model.fields,field_description:sale_enterprise.field_sale_report__avg_days_to_confirm
msgid "Average Days To Confirm"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields,help:sale_enterprise.field_sale_report__avg_days_to_confirm
msgid ""
"Average days to confirm a sales order after its creation. Due to a hack "
"needed to calculate this,               every record will show the same "
"average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale_enterprise.view_order_product_search_inherit
msgid "Fully Invoiced"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields,field_description:sale_enterprise.field_sale_report__invoice_status
msgid "Invoice Status"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__no
msgid "Nothing to Invoice"
msgstr ""

#. module: sale_enterprise
#: model_terms:ir.ui.view,arch_db:sale_enterprise.sale_report_view_pivot
msgid "Sales Analysis"
msgstr ""

#. module: sale_enterprise
#: model:ir.model,name:sale_enterprise.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:sale_enterprise.view_order_product_search_inherit
msgid "To Invoice"
msgstr ""

#. module: sale_enterprise
#: model:ir.model.fields.selection,name:sale_enterprise.selection__sale_report__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr ""
