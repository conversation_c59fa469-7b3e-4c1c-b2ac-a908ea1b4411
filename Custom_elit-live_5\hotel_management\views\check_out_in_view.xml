<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="check_out_in_form" model="ir.ui.view">
            <field name="name">Check Out In</field>
            <field name="model">check.out.in</field>
            <field name="arch" type="xml">
                <form string="_form">
                    <sheet>
                        <group>
                            <group>
                                <field name="checkout_time" widget="float_time"/>
                                <field name="checkin_time" widget="float_time"/>
                            </group>
                            <group>
                                <field name="real_checkout_time" widget="float_time"/>
                                <field name="shop_id"/>
                                <field name="journal_id"/>
                                <field name="guarantee_journal_id"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="check_out_in_tree_view" model="ir.ui.view">
            <field name="name">Check Out In</field>
            <field name="model">check.out.in</field>
            <field name="arch" type="xml">
                <tree string="_form">
                    <field name="checkout_time"/>
                    <field name="checkin_time"/>
                    <field name="real_checkout_time"/>
                    <field name="journal_id"/>
                </tree>
            </field>
        </record>

        <record id="check_in_out_action" model="ir.actions.act_window">
            <field name="name">Check In Out Time</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">check.out.in</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="config_root_menu_id" name="Check Out In Time" action="check_in_out_action"
                  parent="hotel.hotel_configuration_menu" sequence="1"/>


        <!--inheriting payment form -->

        <!-- Inherit Form View to Modify it -->
        <record id="account_payment_form_inherit" model="ir.ui.view">
            <field name="name">Account Payment</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='ref']" position="before">
                    <field name="guarantee_amount"/>
                </xpath>

            </field>
        </record>


    </data>
</odoo>