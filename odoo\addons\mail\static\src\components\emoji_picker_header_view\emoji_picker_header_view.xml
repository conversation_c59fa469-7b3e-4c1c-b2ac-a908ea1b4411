<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.EmojiPickerHeaderView" owl="1">
        <div class="o_EmojiPickerHeaderView d-flex bg-100 border-bottom" t-attf-class="{{ className }}" t-ref="root">
            <EmojiCategoryBarView record="emojiPickerHeaderView.emojiCategoryBarView"/>
            <span class="flex-grow-1"/>
            <EmojiPickerHeaderActionListView t-if="emojiPickerHeaderView.actionListView" record="emojiPickerHeaderView.actionListView"/>
        </div>
    </t>

</templates>
