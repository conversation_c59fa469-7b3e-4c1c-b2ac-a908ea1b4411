# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_twitter
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_post_template.py:0
#, python-format
msgid "%s / %s characters to fit in a Tweet"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "11m"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "<b class=\"text-900\">Twitter Account</b>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a tweet\"/>\n"
"                                    <span>Quote Tweet</span>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-retweet me-1\" title=\"Retweet a tweet\"/>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<span class=\"fst-italic\">Empty tweet</span>"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "@twitteraccount ·"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "A retweet already exists"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Authentication failed. Please enter valid credentials."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Can not like / unlike the tweet\n"
"%s."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Cancel"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_res_config_settings__twitter_use_own_account
msgid ""
"Check this if you want to use your personal Twitter Developer Account "
"instead of the provided one."
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Key"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Secret Key"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_uid
msgid "Created by"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_date
msgid "Created on"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__description
msgid "Description"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__display_name
msgid "Display Name"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__has_twitter_accounts
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__has_twitter_accounts
msgid "Display Twitter Preview"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Error"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_twitter_quote.js:0
#, python-format
msgid "Error while sending the data to the server."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to delete the Tweet\n"
"%s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to fetch the conversation id: '%s' using the account %s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to fetch the tweets in the same thread: '%s' using the account %s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to post comment: %s with the account %s."
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_likes
msgid "Favorites of"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__id
msgid "ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__image
msgid "Image"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__is_twitter_post_limit_exceed
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__is_twitter_post_limit_exceed
msgid "Is Twitter Post Limit Exceed"
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_keyword
msgid "Keyword"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account____last_update
msgid "Last Modified on"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_uid
msgid "Last Updated by"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_date
msgid "Last Updated on"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Likes"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"Looks like you've made too many requests. Please wait a few minutes before "
"giving it another try."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_media__media_type
msgid "Media Type"
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_user_mentions
msgid "Mentions"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__name
msgid "Name"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Oops! Couldn't find this tweet on Twitter.com"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Please select a Twitter account for this stream type."
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Post"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "Post Image"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Quote a Tweet"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_link
msgid "Quoted tweet author Link"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_name
msgid "Quoted tweet author Name"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_message
msgid "Quoted tweet message"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_profile_image_url
msgid "Quoted tweet profile image URL"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "RT"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweet_count
msgid "Re-tweets"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Replies from Tweets older than 7 days must be accessed on Twitter.com"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet or Quote"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Retweets"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_searched_keyword
msgid "Search Keyword"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_search
msgid "Search User"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_searched_by_id
msgid "Searched by"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_account
msgid "Social Account"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_live_post
msgid "Social Live Post"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_media
msgid "Social Media"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post
msgid "Social Post"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post_template
msgid "Social Post Template"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream
msgid "Social Stream"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream_post
msgid "Social Stream Post"
msgstr ""

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_twitter_account
msgid "Social Twitter Account"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"The keyword you've typed in does not look valid. Please try again with other"
" words."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "This Tweet has been deleted."
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_follow
msgid "Tweets of"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields.selection,name:social_twitter.selection__social_media__media_type__twitter
#: model:social.media,name:social_twitter.social_media_twitter
msgid "Twitter"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_author_id
msgid "Twitter Author ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_can_retweet
msgid "Twitter Can Retweet"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_comments_count
#, python-format
msgid "Twitter Comments"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_key
msgid "Twitter Consumer Key"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_secret_key
msgid "Twitter Consumer Secret Key"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Twitter Developer Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_id
msgid "Twitter Followed Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_id
msgid "Twitter ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_likes_count
msgid "Twitter Likes"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token
msgid "Twitter OAuth Token"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token_secret
msgid "Twitter OAuth Token Secret"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_post_limit_message
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_post_limit_message
msgid "Twitter Post Limit Message"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_preview
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_preview
msgid "Twitter Preview"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Twitter Profile Image"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_profile_image_url
msgid "Twitter Profile Image URL"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_id_str
msgid "Twitter Quoted Tweet ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweeted_tweet_id_str
msgid "Twitter Retweet ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_screen_name
msgid "Twitter Screen Name"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_tweet_id
msgid "Twitter Tweet ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_user_id
msgid "Twitter User ID"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_user_likes
msgid "Twitter User Likes"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token or it may have expired."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token."
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_live_post__twitter_tweet_id
msgid "Twitter tweet id"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Undo Retweet"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Unknown"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Unknown error"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_use_own_account
msgid "Use your own Twitter Account"
msgstr ""

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_account.py:0
#, python-format
msgid ""
"We could not upload your image, it may be corrupted, it may exceed size "
"limit or API may have send improper response (error: %s)."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "You are not authenticated"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comments.js:0
#, python-format
msgid ""
"You can comment only three times a tweet as it may be considered as spamming"
" by Twitter"
msgstr ""

#. module: social_twitter
#: model:ir.model.constraint,message:social_twitter.constraint_social_stream_post_tweet_uniq
msgid "You can not store two times the same tweet on the same stream!"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"You cannot create a Stream from this Twitter account.\n"
"It may be because it's protected. To solve this, please make sure you follow it before trying again."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"You need to add the following callback URL to your twitter application "
"settings: %s"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_view_form
msgid "e.g. #odoo"
msgstr ""

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comment.js:0
#, python-format
msgid "tweet"
msgstr ""
