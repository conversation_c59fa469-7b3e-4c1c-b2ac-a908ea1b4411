<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="mail_test_ticket_tracking_tpl" model="mail.template">
        <field name="name">Mail Test Full: Tracking Template</field>
        <field name="subject">Test Template</field>
        <field name="partner_to">{{ object.customer_id.id }}</field>
        <field name="body_html" type="html"><p>Hello <t t-out="object.name or ''"></t></p></field>
        <field name="model_id" ref="test_mail.model_mail_test_ticket"/>
        <field name="auto_delete" eval="True"/>
    </record>

    <record id="mail_test_container_tpl" model="mail.template">
        <field name="name">Mail Test: Template</field>
        <field name="subject">Post on {{ object.name }}</field>
        <field name="partner_to">{{ object.customer_id.id }}</field>
        <field name="body_html" type="html"><p>Adding stuff on <t t-out="object.name or ''"></t></p></field>
        <field name="model_id" ref="test_mail.model_mail_test_container"/>
        <field name="auto_delete" eval="True"/>
    </record>

    <template id="mail_test_ticket_tracking_view">
    	<p><span t-out="object.name or ''"/> datetime has been updated to <span t-out="object.datetime or ''"/></p>
    </template>

    <template id="mail_test_ticket_test_template">
        <t t-call="web.html_container">
            <t t-set="o" t-value="res_company"/>
            <t t-call="web.external_layout">
                <div class="page">
                    <p>This is a sample of an external report.</p>
                </div>
            </t>
        </t>
    </template>

    <template id="mail_template_simple_test">
        <p>Hello <t t-out="partner.name"/>, this comes from <t t-out="object.name"/>.</p>
    </template>

</odoo>
