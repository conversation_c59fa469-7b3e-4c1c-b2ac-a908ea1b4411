<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <template id="report_invoice_document"  inherit_id="account.report_invoice_document" primary="True">
            <!-- Global variables -->
            <xpath expr="//h2[1]" position="before">
                <t t-set="extra_edi_vals" t-value="o._l10n_pe_edi_get_extra_report_values()"/>
                <t t-set="spot" t-value="o._l10n_pe_edi_get_spot()"/>
            </xpath>

            <xpath expr="//div[@t-if='not is_html_empty(o.narration)']" position="before">
                <p>
                    <span><b>SON:</b> <t t-esc="extra_edi_vals.get('amount_to_text')"/></span>
                </p>
            </xpath>

            <xpath expr="//h2/span[contains(@t-if, 'out_invoice')]" position="replace">
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'" t-esc="o.l10n_latam_document_type_id.report_name"/>
            </xpath>

            <xpath expr="//h2/span[contains(@t-if, 'out_refund')]" position="replace">
                <span t-if="o.move_type == 'out_refund' and o.state == 'posted'" t-esc="o.l10n_latam_document_type_id.report_name"/>
            </xpath>

            <xpath expr="//h2/span[@t-field='o.name']" position="replace">
                <span t-if="o.name != '/'" t-esc="o.name.replace(' ', '')"/>
            </xpath>
            <!-- QR-code -->
            <xpath expr="//div[@id='qrcode']" position="after">
                    <p t-if="spot" t-esc="spot['spot_message']"/>
                    <p>
                        <img t-if="extra_edi_vals" class="border border-dark rounded" t-att-src="'/report/barcode/?barcode_type=%s&amp;value=%s&amp;width=%s&amp;height=%s' % ('QR', extra_edi_vals['qr_str'], 100, 100)"/>
                    </p>
            </xpath>
        </template>

        <!-- FIXME: Temp fix to allow fetching invoice_documemt in Studio Reports with localisation [Copied from l10n_ar]-->
        <template id="report_invoice" inherit_id="account.report_invoice">
            <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
                <t t-if="o._get_name_invoice_report() == 'l10n_pe_edi.report_invoice_document'"
                    t-call="l10n_pe_edi.report_invoice_document" t-lang="lang"/>
            </xpath>
        </template>

        <template id="report_invoice_with_payments" inherit_id="account.report_invoice_with_payments">
            <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
                <t t-if="o._get_name_invoice_report() == 'l10n_pe_edi.report_invoice_document'"
                    t-call="l10n_pe_edi.report_invoice_document" t-lang="lang"/>
            </xpath>
        </template>
    </data>
</odoo>
