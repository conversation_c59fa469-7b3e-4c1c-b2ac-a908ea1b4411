<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageNotificationPopoverContent" owl="1">
        <div class="o_MessageNotificationPopoverContent m-2" t-attf-class="{{ className }}" t-ref="root">
            <t t-foreach="messageNotificationPopoverContentView.messageView.message.notifications" t-as="notification" t-key="notification.localId">
                <div class="o_MessageNotificationPopoverContent_notification">
                    <i class="o_MessageNotificationPopoverContent_notificationIcon me-2" t-att-class="notification.iconClass" t-att-title="notification.iconTitle" role="img"/>
                    <t t-if="notification.partner">
                        <span class="o_MessageNotificationPopoverContent_notificationPartnerName" t-esc="notification.partner.nameOrDisplayName"/>
                    </t>
                </div>
            </t>
        </div>
    </t>

</templates>
