/* CSS for freezing the calendar header */
.fc-day-header, .fc-head {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}

.fc-scroller {
    height: auto !important;
    overflow-y: auto !important;
}

.fc-head {
    position: sticky;
    top: 0;
    z-index: 20;
}

.fc-body > tr > .fc-widget-content {
    overflow: visible !important;
}

.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 1;
}

.fc-row .fc-highlight-skeleton {
    z-index: 2;
}

/* Fix for resource headers */
.fc-timeline .fc-divider {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}

.fc-time-area .fc-rows .fc-widget-header {
    position: sticky;
    left: 0;
    z-index: 9;
    background-color: white;
}

/* Ensure scrollable content */
#booking_calendar .fc-view {
    overflow: visible;
}

/* Make the content scrollable while keeping header fixed */
.fc-timeline-body {
    overflow-y: auto;
}

.fc-timeline-event-container {
    position: relative;
}

/* Calendar wrapper for scrolling */
#calendar-wrapper {
    max-height: calc(100vh - 250px);
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Ensure the days row stays at the top when scrolling */
.fc-day-header span, .fc-day-header div {
    position: relative;
    z-index: 5;
}

/* Make room labels sticky on the left */
.fc-resource-area .fc-cell-content {
    position: sticky;
    left: 0;
    background: white;
    z-index: 8;
}

/* Ensure resource area stays fixed on the left */
.fc-resource-area {
    position: sticky;
    left: 0;
    background: white;
    z-index: 8;
    border-right: 1px solid #ddd;
}

/* Add shadows for better visual separation */
.fc-resource-area:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 5px;
    background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
    z-index: 1;
}

/* Add shadow effect to indicate scrollable area */
.fc-head:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -5px;
    height: 5px;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
    z-index: 1;
} 