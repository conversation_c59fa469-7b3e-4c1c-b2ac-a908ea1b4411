# -*- coding: utf-8 -*-

from odoo import api, fields, models
from datetime import datetime, timedelta
import calendar
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class MonthlyReservationReport(models.TransientModel):
    _name = 'monthly.reservation.report'
    _description = 'Monthly Reservation Schedule Report'

    month = fields.Selection([
        ('1', 'January'),
        ('2', 'February'),
        ('3', 'March'),
        ('4', 'April'),
        ('5', 'May'),
        ('6', 'June'),
        ('7', 'July'),
        ('8', 'August'),
        ('9', 'September'),
        ('10', 'October'),
        ('11', 'November'),
        ('12', 'December'),
    ], string='Month', required=True, default=lambda self: str(datetime.now().month))
    
    year = fields.Integer(string='Year', required=True, default=lambda self: datetime.now().year)
    
    @api.model
    def get_month_name(self, month_number):
        """Get the month name from month number."""
        if not month_number or not str(month_number).isdigit():
            return ''
        month_int = int(month_number)
        if month_int < 1 or month_int > 12:
            return ''
        
        month_names = {
            1: 'January', 2: 'February', 3: 'March', 4: 'April',
            5: 'May', 6: 'June', 7: 'July', 8: 'August',
            9: 'September', 10: 'October', 11: 'November', 12: 'December'
        }
        return month_names.get(month_int, '')
    
    def print_report(self):
        """Generate the monthly reservation schedule report."""
        _logger.info("Generating monthly reservation schedule report...")
        
        try:
            # Get month and year from the wizard
            month = int(self.month)
            year = int(self.year)
            
            # Get month name and days in month
            month_name = self.get_month_name(month)
            actual_days_in_month = calendar.monthrange(year, month)[1]
            days_in_month = 31  # Always use 31 days
            
            # Date range for the report
            start_date = datetime(year, month, 1)
            end_date = datetime(year, month, actual_days_in_month)
            
            # Format dates for domain filter
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # Map of room.product_id to room.id for efficient lookup
            room_product_map = {}
            
            # Get all rooms first
            rooms = self.env['hotel.room'].search([])
            
            # Create mapping from product_id to room
            for room in rooms:
                if room.product_id:
                    room_product_map[room.product_id.id] = room.id
            
            # Find the check-in and check-out field names in reservation_line
            resv_line_model = self.env['hotel.reservation.line']
            resv_line_fields = resv_line_model.fields_get()
            
            # Check if the expected fields exist in reservation_line
            if 'checkin' not in resv_line_fields or 'checkout' not in resv_line_fields or 'room_number' not in resv_line_fields:
                raise UserError("Missing required fields in hotel.reservation.line model.")
            
            # Get all reservation lines for the date range
            try:
                # Find all reservation lines that have dates overlapping with our month
                resv_line_domain = [
                    ('checkout', '>=', start_date_str),
                    ('checkin', '<=', end_date_str)
                ]
                reservation_lines = resv_line_model.search(resv_line_domain)
                
                # Group reservation lines by room
                room_reservation_map = {}
                for line in reservation_lines:
                    # Get the product_id from the room_number field
                    if not line.room_number or not line.room_number.id:
                        continue
                    
                    product_id = line.room_number.id
                    
                    # Check if this product maps to a room
                    if product_id not in room_product_map:
                        continue
                    
                    room_id = room_product_map[product_id]
                    
                    # Add this reservation line to the room's reservations
                    if room_id not in room_reservation_map:
                        room_reservation_map[room_id] = []
                    
                    reservation = line.line_id  # Get the parent reservation
                    if reservation:
                        room_reservation_map[room_id].append({
                            'reservation': reservation,
                            'line': line
                        })
            
            except Exception as e:
                raise UserError(f"Error searching reservation lines: {e}")
            
            # Prepare room data structure for the report
            rows = []
            
            # Process each room
            for room in rooms:
                # Prepare days data for this room
                days_data = []
                
                # Initialize all days as unoccupied
                for day in range(1, 32):  # Always create 31 days
                    days_data.append({
                        'occupied': 0,
                        'guest': '',
                        'color': '#FFFFFF' if day <= actual_days_in_month else '#F5F5F5',  # Light gray for invalid days
                        'reservation_id': False
                    })
                
                # Get reservations for this room
                room_reservations = room_reservation_map.get(room.id, [])
                
                # Process each reservation for this room
                for res_data in room_reservations:
                    reservation = res_data['reservation']
                    line = res_data['line']
                    
                    # Only process reservations in draft, confirm, or done (checked-in) states
                    if reservation.state not in ['draft', 'confirm', 'done']:
                        continue
                    
                    # Get guest name
                    guest_name = reservation.partner_id.name if hasattr(reservation, 'partner_id') and reservation.partner_id else 'Guest'
                    
                    # Get reservation state and determine the correct status label
                    state = reservation.state
                    
                    # Determine status label for display
                    status_labels = {
                        'draft': 'BOOKING',
                        'confirm': 'CONFIRMED',
                        'done': 'CHECKED-IN',
                    }
                    status_label = status_labels.get(state, 'UNKNOWN')
                    color = self._get_color_for_state(state)
                    
                    try:
                        # Get check-in/check-out dates from the reservation line
                        checkin_date = line.checkin
                        checkout_date = line.checkout
                        
                        # Adjust dates to be within the month
                        if checkin_date < start_date:
                            checkin_date = start_date
                        if checkout_date > end_date:
                            checkout_date = end_date
                        
                        # Mark days as occupied
                        current_date = checkin_date
                        first_day_marked = False  # Track if we've marked the first day
                        
                        while current_date < checkout_date:  # Not including checkout day
                            if current_date.month == month and current_date.year == year:
                                day_index = current_date.day - 1  # 0-based index
                                
                                # Format guest info for tooltip
                                reservation_info = f"{guest_name}\n"  # Start with just the name
                                reservation_info += f"Status: {status_label}\n"
                                reservation_info += f"Check-in: {line.checkin}\nCheck-out: {line.checkout}"
                                
                                # Determine if this is first day of stay in this month
                                is_first_day = not first_day_marked
                                if is_first_day:
                                    first_day_marked = True
                                
                                days_data[day_index] = {
                                    'occupied': 1,
                                    'guest': reservation_info,
                                    'color': color,
                                    'reservation_id': reservation.id,
                                    'is_first_day': is_first_day,
                                    'guest_name': guest_name
                                }
                            current_date += timedelta(days=1)
                    except Exception as e:
                        _logger.error(f"Error processing reservation dates: {e}")
                
                # Add room data to rows
                room_data = {
                    'room_name': room.name,
                    'days': days_data
                }
                rows.append(room_data)
            
            # Prepare data for report
            data = {
                'month_name': month_name,
                'year': year,
                'days_in_month': days_in_month,
                'actual_days_in_month': actual_days_in_month,
                'rows': rows,
                'is_test_data': False
            }
            
            # Return the report action with direct data
            return self.env.ref('hotel_invoice.action_monthly_reservation_schedule_report').report_action(self, data=data)
        
        except Exception as e:
            # Catch any unhandled exceptions
            _logger.error(f"Unhandled exception in print_report: {e}")
            raise UserError(f"Error generating report: {e}")
    
    def _get_color_for_state(self, state):
        """Get the color code for a reservation state."""
        status_colors = {
            'draft': '#FFE4B5',  # Light orange for draft/booking
            'confirm': '#98FB98',  # Light green for confirmed
            'done': '#87CEEB',  # Light blue for checked-in
            'default': '#FFFFFF'  # White for unknown states
        }
        return status_colors.get(state, status_colors['default']) 