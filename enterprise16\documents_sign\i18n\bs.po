# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_sign
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_request__folder_id
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Attachment Folder"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_request__documents_tag_ids
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Attachment Tags"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Kreiraj"

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_ir_attachment
msgid "Document"
msgstr "Dokument"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr "Zadatak"

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""
