<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <record id="sale_planning_admin_read_all_sol" model="ir.rule">
        <field name="name">Sale Planning: planning admin can see every service SOL</field>
        <field name="model_id" ref="sale.model_sale_order_line"/>
        <field name="domain_force">[
            ('company_id', 'in', company_ids),
            ('state', 'in', ['sale', 'done']),
            ('product_id.type', '=', 'service'),
        ]</field>
        <field name="perm_read" eval="True"/>
        <field name="groups" eval="[(4,ref('planning.group_planning_manager'))]"/>
    </record>
</data>
</odoo>
