# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* account_check_printing
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-31 09:57+0000\n"
"PO-Revision-Date: 2018-01-31 09:57+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:58
#, python-format
msgid " : Check Number Sequence"
msgstr " : Cheque Number Sequence"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:69
#, python-format
msgid "A check memo cannot exceed 60 characters."
msgstr "A cheque memo cannot exceed 60 characters."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_margin_top
msgid "Adjust the margins of generated checks to make it fit your printer's settings."
msgstr "Adjust the margins of generated cheques to make it fit your printer's settings."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company_account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings_account_check_printing_layout
msgid "Check Layout"
msgstr "Cheque Layout"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company_account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings_account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "Cheque Left Margin"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_number
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_number
msgid "Check Number"
msgstr "Cheque Number"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "Cheque Printing"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid "Check Printing Payment Method Selected"
msgstr "Cheque Printing Payment Method Selected"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings_account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "Cheque Right Margin"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_sequence_id
msgid "Check Sequence"
msgstr "Cheque Sequence"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company_account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings_account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "Cheque Top Margin"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "Check this option if your pre-printed cheques are not numbered."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "Cheque to print"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "Cheques"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks To Print"
msgstr "Cheques To Print"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_sequence_id
msgid "Checks numbering sequence."
msgstr "Cheques numbering sequence."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:97
#, python-format
msgid "Checks to Print"
msgstr "Cheque to Print"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "Cheques to print"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:89
#, python-format
msgid "In order to print multiple checks at once, they must belong to the same bank journal."
msgstr "In order to print multiple cheques at once, they must belong to the same bank journal."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_bank_journal_form_inherited_check_printing
msgid "Manual Numbering of check"
msgstr "Manual Numbering of cheque"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company_account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings_account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "Multi-Pages Cheque Stub"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_next_check_number
msgid "Next Check Number"
msgstr "Next Cheque Number"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_number
msgid "Number of the check corresponding to this payment. If your pre-printed check are not already numbered, you can manage the numbering in the journal configuration page."
msgstr "Number of the cheque corresponding to this payment. If your pre-printed cheque are not already numbered, you can manage the numbering in the journal configuration page."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:86
#, python-format
msgid "Payments to print as a checks must have 'Check' selected as payment method and not have already been reconciled"
msgstr "Payments to print as a cheques must have 'Cheque' selected as payment method and not have already been reconciled"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Please enter the number of the first pre-printed check that you are about to print on."
msgstr "Please enter the number of the first pre-printed cheque that you are about to print on."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "Print Cheque"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "Print Cheques"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:99
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "Print Pre-numbered Cheques"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_layout
msgid "Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr "Select the format corresponding to the cheque paper you will be printing your cheques on.\n"
"In order to disable the printing feature, select 'None'."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_next_number
msgid "Sequence number of the next printed check."
msgstr "Sequence number of the next printed cheque."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid "Technical feature used to know whether check printing was enabled as payment method."
msgstr "Technical feature used to know whether cheque printing was enabled as payment method."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:26
#, python-format
msgid "The last check number was %s. In order to avoid a check being rejected by the bank, you can only use a greater number."
msgstr "The last cheque number was %s. In order to avoid a cheque being rejected by the bank, you can only use a greater number."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_number
msgid "The selected journal is configured to print check numbers. If your pre-printed check paper already has numbers or if the current numbering is wrong, you can change it in the journal configuration page."
msgstr "The selected journal is configured to print cheque numbers. If your pre-printed cheque paper already has numbers or if the current numbering is wrong, you can change it in the journal configuration page."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:121
#, python-format
msgid "There is no check layout configured.\n"
"Make sure the proper check printing module is installed and its configuration (in company settings > 'Configuration' tab) is correct."
msgstr "There is no cheque layout configured.\n"
"Make sure the proper cheque printing module is installed and its configuration (in company settings > 'Configuration' tab) is correct."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_multi_stub
msgid "This option allows you to print check details (stub) on multiple pages if they don't fit on a single page."
msgstr "This option allows you to print cheque details (stub) on multiple pages if they don't fit on a single page."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company_account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings_account_check_printing_date_label
msgid "This option allows you to print the date label on the check as per CPA. Disable this if your pre-printed check includes the date label."
msgstr "This option allows you to print the date label on the cheque as per CPA. Disable this if your pre-printed cheque includes the date label."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "This will allow to save on payments the number of the corresponding check."
msgstr "This will allow to save on payments the number of the corresponding cheque."

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check in middle"
msgstr "cheque in middle"

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check on bottom"
msgstr "cheque on bottom"

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check on top"
msgstr "cheque on top"

