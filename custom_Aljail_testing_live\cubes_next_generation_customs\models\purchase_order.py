# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.fields import Command


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    original_total_amount = fields.Float(compute="get_original_total_amount", string='الاجمالي قبل الخصم')

    @api.depends('price_unit','product_uom_qty')
    def get_original_total_amount(self):
        for elem in self:
            elem.original_total_amount = 0
            if elem.price_unit and elem.product_qty:
                elem.original_total_amount = elem.price_unit * elem.product_qty

