# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_amazon
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_amazon
#: model:mail.template,body_html:sale_amazon.order_sync_failure
msgid ""
"<div>\n"
"            <p>The synchronization of the Amazon order with reference <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> encountered an error and was not completed.</p>\n"
"            <p>Unless the order is canceled in SellerCentral, no other synchronization will be attempted.</p>\n"
"            <p>To schedule a new synchronization attempt, proceed as follows:\n"
"                <ol>\n"
"                    <li>Enter the Developer Tools.</li>\n"
"                    <li>Open the form of the Amazon Account on which the order was placed.</li>\n"
"                    <li>Navigate to the Order Follow-up tab.</li>\n"
"                    <li>Set a date for <em>Last Orders Sync</em> that is anterior to the last status update of the order.</li>\n"
"                    <li>Save the changes and click on the <em>SYNC ORDERS</em> button.</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>If the problem persists, contact <a href=\"https://www.odoo.com/help/\">Odoo support</a>.</p>\n"
"        </div>\n"
"        "
msgstr ""
"<div>\n"
"            <p>مزامنة طلب أمازون مع المرجع <t t-out=\"ctx.get('amazon_order_ref') or ''\">REF</t> واجهت خطأ وتعذر إكمالها.</p>\n"
"            <p>إلا إذا تم إلغاء الطلب في SellerCentral، لن يتم إجراء أي محاولة مزامنة أخرى.</p>\n"
"            <p>لجدولة محاولة مزامنة أخرى، قم باتباع الخطوات التالية:\n"
"                <ol>\n"
"                    <li>قم بإدخال أدوات المطور.</li>\n"
"                    <li>افتح الاستمارة من حساب أمازون الذي تم الطلب منه.</li>\n"
"                    <li>انتقل إلى علامة تبويب متابعة الطلب.</li>\n"
"                    <li>قم بتعيين تاريخ لـ <em>آخر مزامنة للطلبات</em> التي تسبق تاريخ آخر تحديث لحالة الطلب.</li>\n"
"                    <li>قم بحفظ التغييرات ثم اضغط على زر <em>مزامنة الطلبات</em>.</li>\n"
"                </ol>\n"
"            </p>\n"
"            <p>إذا استمرت المشكلة، تواصل مع <a href=\"https://www.odoo.com/help/\">فريق دعم أودو</a>.</p>\n"
"        </div>\n"
"        "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__api_ref
msgid "API Identifier"
msgstr "معرّف الواجهة البرمجية للتطبيق "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_access_key
msgid "AWS Access Key"
msgstr "مفتاح الوصول إلى خدمات أمازون ويب "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_secret_key
msgid "AWS Secret Key"
msgstr "المفتاح السري لخدمات أمازون ويب "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_session_token
msgid "AWS Session Token"
msgstr "رمز جلسة خدمات أمازون ويب "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__account_id
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Account"
msgstr "الحساب "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Account Name"
msgstr "اسم الحساب"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Active"
msgstr "نشط"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"After validation of the credentials, the marketplaces\n"
"                                                to which this account has access will be\n"
"                                                synchronized and automatically made available."
msgstr ""
"بعد تصديق بيانات الاعتماد، سوف تكون الأسواق\n"
"                                                التي بوسع هذا الحساب الوصول إليها\n"
"                                                متزامنة ومتاحة تلقائياً. "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_account
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Amazon Account"
msgstr "حساب أمازون "

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_account_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Accounts"
msgstr "حسابات أمازون "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,field_description:sale_amazon.field_res_users__amazon_email
msgid "Amazon Email"
msgstr "البريد الإلكتروني لأمازون "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "Amazon Item Ref"
msgstr "مرجع عنصر أمازون "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_location__amazon_location
msgid "Amazon Location"
msgstr "موقع أمازون "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_marketplace
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_form
msgid "Amazon Marketplace"
msgstr "سوق أمازون "

#. module: sale_amazon
#: model:ir.actions.act_window,name:sale_amazon.list_amazon_marketplace_action
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_marketplace_view_tree
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
msgid "Amazon Marketplaces"
msgstr "أسواق أمازون "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_amazon_offer
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order_line__amazon_offer_id
msgid "Amazon Offer"
msgstr "عرض أمازون "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "Amazon Offers"
msgstr "عروض أمازون "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_order_ref
msgid "Amazon Order Ref"
msgstr "مرجع طلب أمازون "

#. module: sale_amazon
#: model:product.template,name:sale_amazon.default_product_product_template
msgid "Amazon Sale"
msgstr "تخفيضات أمازون "

#. module: sale_amazon
#: model:product.template,name:sale_amazon.shipping_product_product_template
msgid "Amazon Shipping"
msgstr "شحن أمازون "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Amazon Sync Pending"
msgstr "مزامنة أمازون معلقة "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_crm_team__amazon_team
msgid "Amazon Team"
msgstr "فريق أمازون "

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Amazon accounts correspond to Amazon Seller Central accounts."
msgstr "حسابات أمازون المقابلة لحسابات Seller Central لدى أمازون. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_marketplace.py:0
#, python-format
msgid "Amazon marketplaces cannot be deleted."
msgstr "لا يمكن حذف أسواق أمازون. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Amazon move : %s"
msgstr "حركة أمازون : %s "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"Since the current carrier doesn't automatically provide a tracking "
"reference, you need to set one manually."
msgstr ""
"يتطلب أمازون وجود مرجع تتبع مع كل عملية توصيل. بما أن شركة الشحن الحالية لا "
"تقدم مرجع تتبع تلقائياً، عليك تعيين واحد يدوياً. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Amazon requires that a tracking reference is provided with each delivery. "
"You need to assign a carrier to this delivery."
msgstr ""
"يتطلب أمازون وجود مرجع تتبع مع كل عملية توصيل. عليك تعيين شركة شحن لعملية "
"التوصيل هذه. "

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AE
msgid "Amazon.ae"
msgstr "Amazon.ae"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_CA
msgid "Amazon.ca"
msgstr "Amazon.ca"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_JP
msgid "Amazon.co.jp"
msgstr "Amazon.co.jp"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_UK
msgid "Amazon.co.uk"
msgstr "Amazon.co.uk"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_US
msgid "Amazon.com"
msgstr "Amazon.com"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_AU
msgid "Amazon.com.au"
msgstr "Amazon.com.au"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BE
msgid "Amazon.com.be"
msgstr "Amazon.com.be"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_BR
msgid "Amazon.com.br"
msgstr "Amazon.com.br"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_MX
msgid "Amazon.com.mx"
msgstr "Amazon.com.mx"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_TR
msgid "Amazon.com.tr"
msgstr "Amazon.com.tr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_DE
msgid "Amazon.de"
msgstr "Amazon.de"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_EG
msgid "Amazon.eg"
msgstr "Amazon.eg"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_ES
msgid "Amazon.es"
msgstr "Amazon.es"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_FR
msgid "Amazon.fr"
msgstr "Amazon.fr"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IN
msgid "Amazon.in"
msgstr "Amazon.in"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_IT
msgid "Amazon.it"
msgstr "Amazon.it"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_NL
msgid "Amazon.nl"
msgstr "Amazon.nl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_PL
msgid "Amazon.pl"
msgstr "Amazon.pl"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SA
msgid "Amazon.sa"
msgstr "Amazon.sa"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SE
msgid "Amazon.se"
msgstr "Amazon.se"

#. module: sale_amazon
#: model:amazon.marketplace,name:sale_amazon.marketplace_SG
msgid "Amazon.sg"
msgstr "Amazon.sg"

#. module: sale_amazon
#: model:mail.template,name:sale_amazon.order_sync_failure
msgid "Amazon: Order Synchronization Failure"
msgstr "أمازون: فشلت المزامنة "

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_orders_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_orders
msgid "Amazon: sync orders"
msgstr "أمازون: مزامنة الطلبات "

#. module: sale_amazon
#: model:ir.actions.server,name:sale_amazon.ir_cron_sync_amazon_pickings_ir_actions_server
#: model:ir.cron,cron_name:sale_amazon.ir_cron_sync_amazon_pickings
msgid "Amazon: sync pickings"
msgstr "أمازون: مزامنة عمليات الانتقاء "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred"
msgstr "حدث خطأ ما "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "An error occurred while linking your account with Amazon."
msgstr "حدث خطأ عند ربط حسابك بأمازون."

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "Available Marketplaces"
msgstr "الأسواق المتاحة "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.authorization_error
msgid "Back to my account"
msgstr "العودة إلى حسابي"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__company_id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__company_id
msgid "Company"
msgstr "الشركة "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "تعذر إنشاء الاتصال بالواجهة البرمجية للتطبيق. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the feed URL."
msgstr "تعذر إنشاء اتصال بعنوان رابط الموجز"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Could not establish the connection to the proxy."
msgstr "تعذر تأسيس الاتصال بالوكيل."

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/controllers/onboarding.py:0
#, python-format
msgid "Could not find Amazon account with id %s"
msgstr "تعذر العثور على حساب أمازون بالمعرف%s"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__create_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Credentials"
msgstr "بيانات الاعتماد"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/res_config_settings.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.res_config_settings_view_form
#, python-format
msgid "Default Products"
msgstr "المنتجات الافتراضية "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__display_name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "Error code: %s; description: %s"
msgstr "رمز الخطأ: %s؛ الوصف: %s "

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__eu-west-1
msgid "Europe"
msgstr "أوروبا"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-west-2
msgid "Far East"
msgstr "الشرق الأقصى"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Amazon"
msgstr "الشحن من قِبَل أمازون "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_order_view_form
msgid "Fulfilled by Merchant"
msgstr "الشحن من قِبَل التاجر "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_sale_order__amazon_channel
msgid "Fulfillment Channel"
msgstr "قناة الاستيفاء "

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fba
msgid "Fulfillment by Amazon"
msgstr "مستوفى من قِبَل أمازون "

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__sale_order__amazon_channel__fbm
msgid "Fulfillment by Merchant"
msgstr "مستوفى من قِبَل التاجر "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"Gift message:\n"
"%s"
msgstr ""
"رسالة الهدية:\n"
"%s "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__id
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active
msgid ""
"If made inactive, this account will no longer be synchronized with Amazon."
msgstr "إذا جعلته غير نشط، لن يكون هذا الحساب متزامناً مع أمازون بعد الآن "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"If the date is set in the past, orders placed on this Amazon Account before the first synchronization of the module might be synchronized with Odoo.\n"
"If the date is set in the future, orders placed on this Amazon Account between the previous and the new date will not be synchronized with Odoo."
msgstr ""
"إذا تم تعيين التاريخ في الماضي، قد تتم مزامنة الطلبات في حساب أمازون هذا قبل المزامنة الأولى للتطبيق مع أودو. \n"
"إذا تم تعيين التاريخ في المستقبل، لن تتم مزامنة الطلبات في حساب أمازون هذا التي تقع بين التاريخ السابق والتاريخ الجديد مع أودو. "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"If this account gained access to new marketplaces,"
"                                         synchronize and add them to the "
"current sync marketplaces"
msgstr ""
"إذا حصل هذا الحساب على صلاحية الوصول إلى أسواق جديدة،"
"                                         قم بمزامنتها وإضافتها إلى الأسواق "
"الحالية المتزامنة "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_search
msgid "Inactive"
msgstr "غير نشط"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_location
msgid "Inventory Locations"
msgstr "مواقع المخزون"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__is_follow_up_displayed
msgid "Is Follow Up Displayed"
msgstr "المتابَعة معروضة "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token
msgid "LWA Access Token"
msgstr "رمز وصول تسجيل الدخول مع أمازون"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__refresh_token
msgid "LWA Refresh Token"
msgstr "تحديث رمز تسجيل الدخول مع أمازون"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace____last_update
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__last_orders_sync
msgid "Last Orders Sync"
msgstr "آخر مزامنة للطلبات "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_uid
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__write_date
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Link with Amazon"
msgstr "الربط مع أمازون"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Link your Amazon account with Odoo to start synchronizing your\n"
"                                Amazon orders."
msgstr ""
"اربط حساب أمازون الخاص بك مع أودو لبدء مزامنة\n"
"                                طلبات أمازون الخاصة بك. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__marketplace_id
msgid "Marketplace"
msgstr "السوق "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Marketplaces"
msgstr "الأسواق "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__name
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__name
msgid "Name"
msgstr "الاسم"

#. module: sale_amazon
#: model:ir.model.fields.selection,name:sale_amazon.selection__amazon_marketplace__region__us-east-1
msgid "North America"
msgstr "أمريكا الشمالية"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_product__offer_count
#: model:ir.model.fields,field_description:sale_amazon.field_product_template__offer_count
msgid "Offer Count"
msgstr "عدد العروض "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: code:addons/sale_amazon/models/product_product.py:0
#: code:addons/sale_amazon/models/product_template.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_product_view_form
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_product_template_view_form
#, python-format
msgid "Offers"
msgstr "العروض "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Only available marketplaces can be selected"
msgstr "يمكن اختيار الأسواق المتاحة فقط "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__order_count
msgid "Order Count"
msgstr "عدد الطلبات "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Order Follow-up"
msgstr "متابعة الطلب "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
#, python-format
msgid "Orders"
msgstr "الطلبات "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_template
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_id
msgid "Product"
msgstr "المنتج"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__product_template_id
msgid "Product Template"
msgstr "قالب المنتج"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/stock_picking.py:0
#, python-format
msgid ""
"Products delivered to Amazon customers must have their respective parts in "
"the same package. Operations related to the product %s were not all "
"confirmed at once."
msgstr ""
"يجب أن يكون للطلبات التي يتم إيصالها إلى أمازون أجزاؤها الخاصة في نفس الطرد."
" لم يتم تأكيد كافة العمليات المتعلقة بالمنتج %s في آن واحد. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__region
msgid "Region"
msgstr "منطقة"

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register your Amazon account"
msgstr "تسجيل حساب أمازون "

#. module: sale_amazon
#: model_terms:ir.actions.act_window,help:sale_amazon.list_amazon_account_action
msgid "Register yours to start synchronizing your orders into Odoo."
msgstr "قم بالتسجيل لبدء مزامنة طلباتك في أودو. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token
msgid "Restricted Data Token"
msgstr "رمز البيانات المقيدة"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__sku
msgid "SKU"
msgstr "وحدة إدارة المخزون "

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_offer_unique_sku
msgid "SKU must be unique for a given account and marketplace."
msgstr "يجب أن تكون وحدة إدارة المخزون فريدة لكل حساب وسوق. "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_crm_team
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__team_id
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__user_id
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"Select the marketplace on which your seller account\n"
"                                                was originally created."
msgstr ""
"قم بتحديد السوق الذي تم إنشاء حساب البائع \n"
"                                                فيه بدايةً. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__seller_central_url
msgid "Seller Central URL"
msgstr "رابط URL للبائع "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__seller_key
msgid "Seller Key"
msgstr "مفتاح البائع"

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__base_marketplace_id
msgid "Sign-up Marketplace"
msgstr "التسجيل في السوق "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__location_id
msgid "Stock Location"
msgstr "موقع المخزون "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Successfully updated the marketplaces available to this account!"
msgstr "تم تحديث الأسواق المتاحة لهذا الحساب بنجاح! "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "Sync Marketplaces"
msgstr "مزامنة الأسواق "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Orders"
msgstr "مزامنة الطلبات "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Sync Pickings"
msgstr "مزامنة عمليات الانتقاء "

#. module: sale_amazon
#: model:mail.template,subject:sale_amazon.order_sync_failure
msgid ""
"Synchronization of Amazon order {{ ctx.get('amazon_order_ref') }} has failed"
msgstr "فشلت مزامنة طلب أمازون {{ ctx.get('amazon_order_ref') }} "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_marketplace__tax_included
msgid "Tax Included"
msgstr "شامل الضريبة"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__region
msgid ""
"The Amazon region of the marketplace. Please refer to the Selling Partner "
"API documentation to find the correct region."
msgstr ""
"منطقة أمازون في السوق. يرجى الرجوع إلى وثائق واجهة برمجة تطبيقات الشريك "
"للعثور على المنطقة الصحيحة. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order_line__amazon_item_ref
msgid "The Amazon-defined item reference."
msgstr "مرجع طلب محدد في أمازون "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__api_ref
msgid "The Amazon-defined marketplace reference."
msgstr "مرجع سوق محدد في أمازون "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_sale_order__amazon_order_ref
msgid "The Amazon-defined order reference."
msgstr "مرجع طلب محدد في أمازون "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__team_id
msgid "The Sales Team assigned to Amazon orders for reporting"
msgstr "فريق المبيعات المسند لطلبات أمازون لإعداد التقارير "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__sku
msgid "The Stock Keeping Unit."
msgstr "وحدة إدارة المخزون "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "فشل الاتصال مع الواجهة البرمجية للتطبيق. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Error code: %s; description: %s"
msgstr ""
"فشل الاتصال مع الواجهة البرمجية للتطبيق. \n"
"رمز الخطأ: %s؛ الوصف: %s "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_res_partner__amazon_email
#: model:ir.model.fields,help:sale_amazon.field_res_users__amazon_email
msgid "The encrypted email of the customer. Does not forward mails."
msgstr ""
"البريد الإلكتروني المشفر للعميل. لا يقوم بإعادة توجيه رسائل ابريد "
"الإلكتروني. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__last_orders_sync
msgid ""
"The last synchronization date for orders placed on this account. Orders "
"whose status has not changed since this date will not be created nor updated"
" in Odoo."
msgstr ""
"تاريخ آخر مزامنة للطلبات في هذا الحساب. لن يتم إشاء أو تحديث الطلبات التي لم"
" تتغير حالاتها منذ ذلك التاريخ في أودو. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__location_id
msgid ""
"The location of the stock managed by Amazon under the Amazon Fulfillment "
"program."
msgstr "موقع المخزون الذي يديره أمازون ضمن برنامج استيفاء أمازون. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__refresh_token
msgid "The long-lived token that can be exchanged for a new access token."
msgstr "الرمز طويل الأمد الذي يمكن استبداله برمز وصول جديد. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__marketplace_id
msgid "The marketplace of this offer."
msgstr "سوق هذا العرض "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__available_marketplace_ids
msgid "The marketplaces this account has access to."
msgstr "الأسواق التي بوسع هذا الحساب الوصول إليها. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__active_marketplace_ids
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__active_marketplace_ids
msgid "The marketplaces this account sells on."
msgstr "الأسواق التي يبيع عليها هذا الحساب. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__aws_credentials_expiry
msgid "The moment at which the AWS credentials become invalid."
msgstr "اللحظة التي تصبح فيها بيانات اعتماد خدمات أمازون ويب غير صالحة. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__restricted_data_token_expiry
msgid "The moment at which the Restricted Data Token becomes invalid."
msgstr "اللحظة التي يصبح فيها رمز البيانات المقيدة غير صالح. "

#. module: sale_amazon
#: model:ir.model.fields,field_description:sale_amazon.field_amazon_account__access_token_expiry
msgid "The moment at which the token becomes invalid."
msgstr "اللحظة التي يصبح فيها الرمز غير صالح. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/sale_order.py:0
#, python-format
msgid ""
"The order has been canceled by the Amazon customer while someproducts have "
"already been delivered. Please create a return for this order to adjust the "
"stock."
msgstr ""
"لقد تم إلغاء هذا الطلب بواسطة عميل أمازون وقد تم توصيل بعض المنتجات بالفعل. "
"يرجى إنشاء طلب إرجاع لهذا الطلب لتعديل المخزون. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__base_marketplace_id
msgid ""
"The original sign-up marketplace of this account. Used for authentication "
"only."
msgstr "تسجيل السوق الأصلي لهذا الحساب. يُستخدم للمصادقة فقط. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_offer__account_id
msgid "The seller account used to manage this product."
msgstr "حساب البائع المُستخدَم لإدارة هذا المنتج. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_access_key
msgid "The short-lived key used to identify the assumed ARN role on AWS."
msgstr ""
"المفتاح قصير الأمد المُستخدَم لتعريف دور رقم المرجع البديل المفترض في خدمات "
"أمازون ويب. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_secret_key
msgid ""
"The short-lived key used to verify the access to the assumed ARN role on "
"AWS."
msgstr ""
"المفتاح قصير الأمد المُستخدَم لتأكيد الوصول إلى دور رقم المرجع البديل "
"المفترض في خدمات أمازون ويب. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__restricted_data_token
msgid ""
"The short-lived token used instead of the LWA Access Token to access "
"restricted data"
msgstr ""
"الرمز قصير الأمد المُستخدَم عوضاً عن رمز وصول تسجيل الدخول مع أمازون للوصول "
"إلى البيانات المقيدة "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__access_token
msgid "The short-lived token used to query Amazon API on behalf of a seller."
msgstr ""
"الرمز قصير الأمد المُستخدَم للاستفسار عن الواجهة البرمجية لأمازون نيابةً عن "
"البائع. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__aws_session_token
msgid ""
"The short-lived token used to query the SP-API with the assumed ARN role on "
"AWS."
msgstr ""
"المفتاح قصير الأمد المُستخدَم للاستفسار عن SP-API مع دور رقم المرجع البديل "
"المفترض في خدمات أمازون ويب. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_account__name
msgid "The user-defined name of the account."
msgstr "اسم محدد للمستخدم للحساب. "

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_amazon_marketplace_unique_api_ref
msgid "There can only exist one marketplace for a given API Identifier."
msgstr "يمكن أن يكون هناك سوق واحد فقط لكل معرّف للواجهة الرمجية للتطبيق. "

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_unique_amazon_order_ref
msgid ""
"There can only exist one sale order for a given Amazon Order Reference."
msgstr "يمكن أن يكون هناك أمر بيع واحد فقط لكل مرجع طلب في أمازون. "

#. module: sale_amazon
#: model:ir.model.constraint,message:sale_amazon.constraint_sale_order_line_unique_amazon_item_ref
msgid ""
"There can only exist one sale order line for a given Amazon Item Reference."
msgstr "يمكن أن يكون هناك بند أمر بيع واحد فقط لكل مرجع عنصر في أمازون. "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid ""
"This action will disconnect your account with Amazon and"
"                                          cannot be undone. Are you sure you"
" want to proceed?"
msgstr ""
"سيقوم هذا الإجراء بفصل حسابك مع أمازون ولا يمكن التراجع عنه. هل أنت متأكد من"
" أنك ترغب في الاستمرار؟ "

#. module: sale_amazon
#: model:ir.model,name:sale_amazon.model_stock_picking
msgid "Transfer"
msgstr "الشحنة"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Unlink account"
msgstr "إلغاء ربط الحساب"

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Update Available Marketplaces"
msgstr "تحديث الأسواق المتاحة "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_offer_view_tree
msgid "View on Seller Central"
msgstr "العرض في Seller Central "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "Warning"
msgstr "تحذير"

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_picking__amazon_sync_pending
msgid "Whether the picking must be notified to Amazon or not."
msgstr "ما إذا كان يجب إرسال إشعار لأمازون بعملية الانتقاء أم لا. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_amazon_marketplace__tax_included
msgid "Whether the price includes the tax amount or not."
msgstr "ما إذا كان السعر شاملاً للضريبة أو لا. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_stock_location__amazon_location
msgid ""
"Whether this location represents the stock of a seller managed by Amazon "
"under the Amazon Fulfillment program or not."
msgstr ""
"ما إذا كان هذا الموقع يمثل مخزون البائع الذي يديره أمازون ضمن برنامج استيفاء"
" أمازون أم لا. "

#. module: sale_amazon
#: model:ir.model.fields,help:sale_amazon.field_crm_team__amazon_team
msgid "Whether this sales team is associated with Amazon orders or not."
msgstr "ما إذا كان فريق المبيعات هذا مرتبطاً بطلبات أمازون أم لا. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid "You first need to authorize the Amazon account %s."
msgstr "عليك أولاً تفويض حساب أمازون %s. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/utils.py:0
#, python-format
msgid ""
"You first need to set the marketplaces to synchronize for the Amazon account"
" %s."
msgstr "عليك أولاً تعيين الأسواق للمزامنة لحساب أمازون %s. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"You reached the maximum number of requests for this operation; please try "
"again later."
msgstr ""
"لقد وصلت إلى الحد الأقصى لعدد الطلبات لهذا المُشغل؛ يرجى المحاولة مجدداً "
"لاحقاً. "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "Your Amazon account is linked with Odoo."
msgstr "حساب أمازون الخاص بك مرتبط بأودو. "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid ""
"[%s] %s\n"
"Condition: %s - %s"
msgstr ""
"[%s] %s\n"
"الحالة: %s - %s"

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Delivery Charges for %s"
msgstr "[%s] رسوم التوصيل لـ %s "

#. module: sale_amazon
#. odoo-python
#: code:addons/sale_amazon/models/amazon_account.py:0
#, python-format
msgid "[%s] Gift Wrapping Charges for %s"
msgstr "[%s] رسوم تغليف الهدايا لـ %s "

#. module: sale_amazon
#: model_terms:ir.ui.view,arch_db:sale_amazon.amazon_account_view_form
msgid "e.g. American Market"
msgstr "مثال: السوق الأمريكي "
