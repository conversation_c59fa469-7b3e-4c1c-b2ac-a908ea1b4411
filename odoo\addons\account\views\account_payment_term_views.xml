<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_payment_term_search" model="ir.ui.view">
            <field name="name">account.payment.term.search</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <search string="Payment Terms">
                    <field name="name" string="Payment Terms"/>
                    <field name="active"/>
                    <separator/>
                    <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="view_payment_term_tree" model="ir.ui.view">
            <field name="name">account.payment.term.tree</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <tree string="Payment Terms">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="view_payment_term_form" model="ir.ui.view">
            <field name="name">account.payment.term.form</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <form string="Payment Terms">
                    <sheet>
                        <field name="active" invisible="1"/>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        <group>
                            <field name="note" placeholder="Payment term explanation for the customer..."/>
                        </group>
                        <label for="display_on_invoice"/>
                        <field name="display_on_invoice"/>
                        <separator string="Terms"/>
                        <p class="text-muted">
                            The last line's computation type should be "Balance" to ensure that the whole amount will be allocated.
                        </p>
                        <field name="line_ids">
                            <tree string="Payment Terms" editable="top" no_open="True">
                                <field name="value" string="Due Type"/>
                                <field name="value_amount" attrs="{'invisible': [('value', '=', 'balance')]}" />
                                <field name="months"/>
                                <field name="days"/>
                                <field name="end_month" widget="boolean_toggle" options="{'autosave': False}"/>
                                <field name="days_after" attrs="{'invisible': [('end_month','=', False)]}"/>
                                <field name="discount_percentage"/>
                                <field name="discount_days"/>
                            </tree>
                        </field>

                        <div class="oe_edit_only">
                            <separator string="Example"/>
                            <field name="example_invalid" invisible="1"/>
                            <div attrs="{'invisible': [('example_invalid', '=', False)]}">
                                The Payment Term must have one Balance line.
                            </div>
                            <div attrs="{'invisible': [('example_invalid', '=', True)]}" class="d-flex" >
                                For any invoice of
                                <span class="mx-1"/> <field name="example_amount" /> <span class="mx-1"/>
                                dated
                                <span class="mx-1"/> <field name="example_date" class="oe_inline" style="color: #704A66; font-weight: bold"/>,
                                the due date(s) and amount(s) will be:
                            </div>
                            <field name="example_preview" attrs="{'invisible': [('example_invalid', '=', True)]}"/>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_account_payment_term_kanban" model="ir.ui.view">
            <field name="name">account.payment.term.kanban</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="note"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div><strong class="o_kanban_record_title"><t t-esc="record.name.value"/></strong></div>
                                <div t-if="!widget.isHtmlEmpty(record.note.raw_value)"><t t-out="record.note.value"/></div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="action_payment_term_form" model="ir.actions.act_window">
            <field name="name">Payment Terms</field>
            <field name="res_model">account.payment.term</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_payment_term_search"/>
        </record>

    </data>
</odoo>
