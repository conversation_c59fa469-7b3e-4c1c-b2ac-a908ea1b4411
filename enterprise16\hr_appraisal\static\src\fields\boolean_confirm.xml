<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="hr_appraisal.BooleanToggleConfirm" owl="1">
        <ConfirmCheckBox
            className="'o_field_boolean o_boolean_toggle form-switch'"
            id="props.id"
            value="props.value or false"
            disabled="isReadonly"
            onChange="(value) => this.onChange(value)"
        />
    </t>
</templates>
