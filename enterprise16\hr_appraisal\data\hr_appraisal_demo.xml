<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.user_demo" model="res.users">
            <field name="groups_id" eval="[(4, ref('hr_appraisal.group_hr_appraisal_user'))]"/>
        </record>

        <record id="hr_appraisal_0" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + timedelta(1)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_1" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=1)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_2" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=-1)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>
        <function model="hr.appraisal" name="action_cancel" eval="[ref('hr_appraisal.hr_appraisal_2')]"/>

        <record id="hr_appraisal_3" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=-12)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
            <field name="manager_feedback_published" eval="True"/>
            <field name="manager_feedback" type="html"><p>The employee is autonomous</p></field>
            <field name="employee_feedback_published" eval="True"/>
            <field name="employee_feedback" type="html"><p>I feel much more comfortable than last year</p></field>
            <field name="assessment_note" model="hr.appraisal.note" eval="obj().search([], order='id desc', limit=1)"/>
        </record>
        <function model="hr.appraisal" name="action_confirm" eval="[ref('hr_appraisal.hr_appraisal_3')]"/>
        <function model="hr.appraisal" name="action_done" eval="[ref('hr_appraisal.hr_appraisal_3')]"/>

        <record id="hr_appraisal_4" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=2)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
            <field name="assessment_note" model="hr.appraisal.note" eval="obj().search([], order='id desc', limit=1)"/>
        </record>
        <function model="hr.appraisal" name="action_confirm" eval="[ref('hr_appraisal.hr_appraisal_4')]"/>
        <function model="hr.appraisal" name="action_done" eval="[ref('hr_appraisal.hr_appraisal_4')]"/>

        <record id="hr_appraisal_4bis" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=1)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
            <field name="manager_feedback_published" eval="True"/>
            <field name="manager_feedback" type="html"><p>The employee is autonomous</p></field>
            <field name="employee_feedback_published" eval="True"/>
            <field name="employee_feedback" type="html"><p>I feel much more comfortable than last year</p></field>
        </record>
        <function model="hr.appraisal" name="action_confirm" eval="[ref('hr_appraisal.hr_appraisal_4bis')]"/>

        <record id="hr_appraisal_5" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_mit"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=3)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_6" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_niv"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=4)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_7" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_stw"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=5)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_8" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_chs"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=6)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_9" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=7)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_10" model="hr.appraisal">
            <field name="employee_id" ref="hr.employee_fpi"/>
            <field name="state">new</field>
            <field name="date_close" eval="(DateTime.now() + relativedelta(months=8)).strftime('%Y-%m-%d')"/>
            <field name="manager_ids" eval="[(4, ref('hr.employee_admin'))]"/>
        </record>

        <record id="hr_appraisal_goal_1" model="hr.appraisal.goal">
            <field name="name">Responsible</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">025</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=8)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_2" model="hr.appraisal.goal">
            <field name="name">Computer Skill</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">000</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=-1)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_3" model="hr.appraisal.goal">
            <field name="name">Similar Tasks</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">000</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=2)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_4" model="hr.appraisal.goal">
            <field name="name">Typing</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">050</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=12)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_5" model="hr.appraisal.goal">
            <field name="name">Trascripts</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">075</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=3)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_6" model="hr.appraisal.goal">
            <field name="name">Trascripts</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">100</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=-1)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_7" model="hr.appraisal.goal">
            <field name="name">Deadlines</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">050</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=-1)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_8" model="hr.appraisal.goal">
            <field name="name">Literacy</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">025</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=7)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="hr_appraisal_goal_9" model="hr.appraisal.goal">
            <field name="name">Confidentiality</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="manager_id" ref="hr.employee_admin"/>
            <field name="progression">075</field>
            <field name="deadline" eval="(DateTime.now() + relativedelta(months=1)).strftime('%Y-%m-%d')"/>
        </record>
    </data>
</odoo>
