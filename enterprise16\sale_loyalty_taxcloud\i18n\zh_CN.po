# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty_taxcloud
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON>ery CHEN <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_loyalty_taxcloud
#: code:addons/sale_loyalty_taxcloud/models/sale_order.py:0
#, python-format
msgid ""
"Any order that has discounts and uses TaxCloud must be invoiced all at once to prevent faulty tax computation with Taxcloud.\n"
"The following orders must be completely invoiced:\n"
"%s"
msgstr ""
"任何有折扣并使用 TaxCloud 的订单都必须一次性开具结算单，以防止使用 Taxcloud 进行错误的税务计算。\n"
"以下订单必须完整开具结算单:\n"
"%s"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_account_move_line__reward_id
msgid "Discount reward"
msgstr "折扣奖励"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "日记账分录"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "日记账项目"

#. module: sale_loyalty_taxcloud
#: code:addons/sale_loyalty_taxcloud/models/sale_order_line.py:0
#, python-format
msgid ""
"Orders with coupons or promotions programs that use TaxCloud for automatic tax computation cannot be modified after having been invoiced.\n"
"To modify this order, you must first cancel or refund all existing invoices."
msgstr ""
"带有使用 TaxCloud 进行自动计税的优惠券或促销方案的订单在开具结算单后无法修改。\n"
"要修改此订单，您必须首先取消或退还所有现有结算单。"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: sale_loyalty_taxcloud
#: model:ir.model,name:sale_loyalty_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_account_move_line__price_taxcloud
#: model:ir.model.fields,field_description:sale_loyalty_taxcloud.field_sale_order_line__price_taxcloud
msgid "Taxcloud Price"
msgstr "Taxcloud 单价"

#. module: sale_loyalty_taxcloud
#: model:ir.model.fields,help:sale_loyalty_taxcloud.field_account_move_line__reward_id
msgid "The loyalty reward that created this line."
msgstr "创建此行的会员奖励"
