<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="report_saleorder_customized" inherit_id="sale.report_saleorder_document">
            <xpath expr="//span[@t-field='line.discount']" position="replace">
                 <span t-esc="'%.2f'%(line.discount)"/>
            </xpath>
            <xpath expr="//tr[hasclass('is-subtotal')]" position="after">
                <tr>
                    <td>Total Discount</td>
                    <td class="text-right">
                        <span t-field="doc.amount_discount"
                              t-options='{"widget": "monetary", "display_currency": doc.pricelist_id.currency_id}'/>
                    </td>
                </tr>
            </xpath>
        </template>

    </data>
</odoo>