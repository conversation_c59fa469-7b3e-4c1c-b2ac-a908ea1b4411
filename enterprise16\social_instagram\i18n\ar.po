# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "منذ 34 ثانية "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"التعليقات \"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"الإعجابات \"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">صفحتي على Instagram</span> "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<span>Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required by Instagram.</span><br/>\n"
"                <span>We don't automatically resize your image to avoid undesired result.</span><br/>\n"
"                <span>More information on:</span>"
msgstr ""
"<span>يجب أن تكون الصور ضمن النسبة 4:5 و 1.91:1 كما هو مطلوب من Instagram.</span><br/>\n"
"                <span>لا نقوم بتغيير حجم صورتك تلقائياً لتجنب النتائج غير المرغوب بها.</span><br/>\n"
"                <span>المزيد من المعلومات على:</span> "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "رمز الوصول "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "An image is required when posting on Instagram."
msgstr "يجب أن تكون هناك صورة عند النشر على Instagram. "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "معرّف التطبيق "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "سر التطبيق "

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "صورة الكاتب "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr ""
"قم بتحديد ذلك إذا كنت ترغب في استخدام حساب مطور Instagram الخاص بك عوضاً عن "
"المقدم إليك. "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Could not find any account to add."
msgstr "لم نتمكن من العثور على أي حساب لإضافته. "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "عرض معاينة Instagram "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"معرف حساب Facebook المقدم من قِبَل الواجهة البرمجية لـ Facebook. يجب ألا يتم تعيين ذلك يدوياً أبداً.\n"
"        حساب Instagram (\"المهني\") مرتبط دائماً بحساب Facebook. "

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
msgid "Instagram"
msgstr "Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "رمز وصول Instagram "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"رمز الوصول إلى Instagram مقدم من الواجهة البرمجية لـ Facebook. يجب ألا يتم تعيين ذلك يدوياً أبداً.\n"
"            يُستخدم لمصادقة الطلبات عند النشر أو قراءة المعلومات من هذا الحساب. "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "معرّف حساب Instagram "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"معرّف حساب Instagram المقدم من قِبَل الواجهة البرمجية لـ Facebook. يجب ألا "
"يتم تعيين ذلك يدوياً أبداً. "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "معرّف تطبيق Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "سر تطبيق Instagram "

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
#, python-format
msgid "Instagram Comments"
msgstr "تعليقات Instagram "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "حساب مطور Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "معرّف حساب Instagram Facebook "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "معرّف كاتب Instagram Facebook "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_id
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_id
msgid "Instagram Image"
msgstr "صور Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "إعجابات Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "معرّف منشور Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "رابط URL لمنشور Instagram "

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "منشورات Instagram "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "معاينة Instagram "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Instagram did not provide a valid access token."
msgstr "لم يقدم Instagram رمز وصول صالح. "

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#, python-format
msgid "Likes"
msgstr "الإعجابات"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "نوع الوسائط الاجتماعية "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "No Instagram accounts linked with your Facebook page"
msgstr "لا توجد حسابات Instagram مرتبطة بصفحتك على Facebook "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Only .jpg/.jpeg images can be posted on Instagram."
msgstr "بإمكانك نشر الصور بصيغة .jpg/.jpeg فقط على Instagram. "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Only the first attached image will be posted on Instagram."
msgstr "فقط أول صورة مرفقة سيتم نشرها على Instagram. "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
#, python-format
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr ""

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "صورة المنشور "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "الحساب الاجتماعي "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "منشور اجتماعي حي "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "مواقع التواصل الاجتماعي"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "منشور اجتماعي "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "قالب المنشور الاجتماعي "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "الوسائط الاجتماعية "

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "منشور الوسائط الاجتماعية "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr ""
"معرّف Facebook لكاتب هذا المنشور على Instagram، يُستخدم لإحضار صورة الملف "
"التعريفي. "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "حدثت مشكلة في المصادقة أثناء طلبك. "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "غير مصرح به. يرجى التواصل مع مديرك. "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "استخدم حساب Instagram الخاص بك "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr "يُستخدم للسماح بالوصول إلى Instagram لاسترجاع صورة المنشور "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"يستخدم لإجراء المقارنات عند الحاجة إلى تقييد بعض الخصائص لموقع تواصل اجتماعي"
" محدد ('facebook'، 'twitter'، ...). "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "لا تملك اشتراكاً سارياً. يرجى شراء واحد من هنا: %s "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Your image appears to be corrupted, please try loading it again."
msgstr "يبدو أن صورتك تالفة. يرجى محاولة تحميلها مجدداً. "

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#, python-format
msgid ""
"Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required "
"by Instagram."
msgstr ""
"يجب أن تكون صورتك ضمن النسبة 4:5 و 1.91:1 كما هو مطلوب من قِبَل Instagram. "

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "https://www.facebook.com/help/instagram/1631821640426723"
msgstr "https://www.facebook.com/help/instagram/1631821640426723"
