<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_social_global_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'social', 'bin_size': False}</field>
    </record>

    <record id="menu_social_global_settings" model="ir.ui.menu">
        <field name="action" ref="action_social_global_settings" />
    </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.social</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <t groups="social.group_social_manager">
                    <div class="app_settings_block" data-string="Social Media" string="Social Media" data-key="social" groups="base.group_no_one">
                        <h2>Demo Mode</h2>
                        <div class="row mt16 o_settings_container">
                            <div class="col-md-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="module_social_demo"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_social_demo" class="oe_inline o_form_label"/>
                                    <div class="text-muted">
                                        Enable this option and load demo data to test the social module.<br/>
                                        This must never be used on a production database!
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2>Developer Accounts</h2>
                        <h3 class="o_setting_tip text-muted">Use your own Developer Accounts on our Social app.
                            Those credentials are provided in the developer section of your professional social media account.</h3>
                        <div class="row mt16 o_settings_container o_social_developer_settings"></div>
                    </div>
                </t>
            </xpath>
        </field>
    </record>
</odoo>
