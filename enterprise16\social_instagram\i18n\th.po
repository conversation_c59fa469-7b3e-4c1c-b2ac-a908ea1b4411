# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# <PERSON><PERSON><PERSON> Thaidev <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON>hip<PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Rasar<PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "34 วินาทีที่แล้ว"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"ความคิดเห็น\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"ถูกใจ\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">หน้าอินสตาแกรมของฉัน</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<span>Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required by Instagram.</span><br/>\n"
"                <span>We don't automatically resize your image to avoid undesired result.</span><br/>\n"
"                <span>More information on:</span>"
msgstr ""
"<span>รูปภาพของคุณจะต้องอยู่ในอัตราส่วน 4:5 และ 1.91:1 ตามที่ Instagram ต้องการ</span><br/>\n"
"                <span>เราจะไม่ปรับขนาดภาพของคุณโดยอัตโนมัติเพื่อหลีกเลี่ยงผลลัพธ์ที่ไม่ต้องการ</span><br/>\n"
"                <span>ข้อมูลเพิ่มเติมเกี่ยวกับ:</span>"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "โทเคนเข้าถึง"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "An image is required when posting on Instagram."
msgstr "ต้องใช้รูปภาพเมื่อโพสต์บน Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "ไอดีแอป"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "รหัสแอป"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "รูปภาพผู้เขียน"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr ""
"เลือกตัวเลือกนี้หากคุณต้องการใช้บัญชีนักพัฒนา Instagram "
"ส่วนตัวของคุณแทนบัญชีที่ให้มา"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Could not find any account to add."
msgstr "ไม่พบบัญชีที่จะเพิ่ม"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "แสดงตัวอย่าง Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"ไอดีบัญชี Facebook ที่ Facebook API ให้มา ไม่ควรตั้งค่าด้วยตนเอง\n"
"        บัญชี Instagram (\"มืออาชีพ\") จะเชื่อมโยงกับบัญชี Facebook เสมอ"

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
msgid "Instagram"
msgstr "Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "โทเคนการเข้าถึง Instagram "

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"โทเคนการเข้าถึง Instagram ที่จัดทำโดย Facebook API ไม่ควรตั้งค่าด้วยตนเอง\n"
"        ใช้สำหรับตรวจสอบคำขอเมื่อโพสต์หรืออ่านข้อมูลจากบัญชีนี้"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "ไอดีบัญชี Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr "ไอดีบัญชี Instagram ที่ Facebook API ให้มา ไม่ควรตั้งค่าด้วยตนเอง"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "Instagram App ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "Instagram App Secret"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
#, python-format
msgid "Instagram Comments"
msgstr "ความคิดเห็นใน Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "บัญชีนักพัฒนา Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "ไอดีผู้ใช้ Instagram Facebook "

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "ไอดีผู้เขียน Instagram Facebook"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_id
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_id
msgid "Instagram Image"
msgstr "รูปภาพ Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "ถูกใจ Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "ไอดีโพสต์ Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "URL โพสต์ Instagram"

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "โพสต์ Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "ตัวอย่าง Instagram"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Instagram did not provide a valid access token."
msgstr "Instagram ไม่ได้ให้โทเคนการเข้าถึงที่ถูกต้อง"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#, python-format
msgid "Likes"
msgstr "ถูกใจ"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "ประเภทมีเดีย"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "No Instagram accounts linked with your Facebook page"
msgstr "ไม่มีบัญชี Instagram ที่เชื่อมโยงกับหน้า Facebook ของคุณ"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Only .jpg/.jpeg images can be posted on Instagram."
msgstr "เฉพาะรูปภาพ .jpg/.jpeg เท่านั้นที่สามารถโพสต์บน Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Only the first attached image will be posted on Instagram."
msgstr "เฉพาะรูปภาพแรกที่แนบมาเท่านั้นที่จะโพสต์บน Instagram"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
#, python-format
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr ""

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "โพสต์รูปภาพ"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "บัญชีโซเชียล"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "โพสต์โซเชียลไลฟ์"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "สื่อสังคมออนไลน์"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "โพสต์โซเชียล"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "เทมเพลตโพสต์โซเชียล"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "สตรีมโซเชียล"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "โพสต์สตรีมโซเชียล"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr "ไอดี Facebook ของผู้เขียนโพสต์ Instagram นี้ ใช้เพื่อดึงรูปโปรไฟล์"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "เกิดปัญหาในการตรวจสอบสิทธิ์ระหว่างที่คุณร้องขอ"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "ไม่ได้รับอนุญาต โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "ใช้บัญชี Instagram ของคุณ"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr "ใช้เพื่ออนุญาตให้เข้าถึง Instagram เพื่อดึงรูปภาพโพสต์"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"ใช้ในการเปรียบเทียบเมื่อเราต้องการจำกัดฟีเจอร์บางอย่างสำหรับสื่อเฉพาะ "
"('facebook', 'twitter', ...)"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "คุณไม่มีการสมัครสมาชิกที่ใช้งานอยู่ กรุณาซื้อที่นี้: %s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Your image appears to be corrupted, please try loading it again."
msgstr "รูปภาพของคุณดูเหมือนจะเสียหาย โปรดลองโหลดอีกครั้ง"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#, python-format
msgid ""
"Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required "
"by Instagram."
msgstr ""
"รูปภาพของคุณจะต้องอยู่ในอัตราส่วน 4:5 และ 1.91:1 ตามที่ Instagram ต้องการ"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "https://www.facebook.com/help/instagram/1631821640426723"
msgstr "https://www.facebook.com/help/instagram/1631821640426723"
