<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="lunch_product_view_search" model="ir.ui.view">
        <field name="name">lunch.product.search</field>
        <field name="model">lunch.product</field>
        <field name="arch" type="xml">
            <search string="Product Search">
                <field name="name" string="Product"/>
                <field name="category_id" string="Category"/>
                <field name="supplier_id"/>
                <field name="description"/>
                <separator/>
                <filter name="available_today" string="Available Today" domain="[('supplier_id.available_today', '=', True)]"/>
                <separator/>
                <filter name="available_on_mon" string="Monday" domain="[('supplier_id.mon', '=', True)]"/>
                <filter name="available_on_tue" string="Tuesday" domain="[('supplier_id.tue', '=', True)]"/>
                <filter name="available_on_wed" string="Wednesday" domain="[('supplier_id.wed', '=', True)]"/>
                <filter name="available_on_thu" string="Thursday" domain="[('supplier_id.thu', '=', True)]"/>
                <filter name="available_on_fri" string="Friday" domain="[('supplier_id.fri', '=', True)]"/>
                <filter name="available_on_sat" string="Saturday" domain="[('supplier_id.sat', '=', True)]"/>
                <filter name="available_on_sun" string="Sunday" domain="[('supplier_id.sun', '=', True)]"/>
                <separator/>
                <filter name="inactive" string="Archived" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_supplier" string="Vendor" context="{'group_by': 'supplier_id'}"/>
                    <filter name="group_by_category" string="Category" context="{'group_by': 'category_id'}"/>
                </group>
                <searchpanel>
                    <field name="category_id" select="multi" string="Categories" icon="fa-cutlery" color="#875A7B" enable_counters="1"/>
                    <field name="supplier_id" select="multi" string="Vendors" icon="fa-truck" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="lunch_product_view_tree" model="ir.ui.view">
        <field name="name">lunch.product.tree</field>
        <field name="model">lunch.product</field>
        <field name="arch" type="xml">
            <tree string="Products Tree">
                <field name="currency_id" invisible="1"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="supplier_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="description"/>
                <field name="price" widget="monetary"/>
            </tree>
        </field>
    </record>

    <record id="lunch_product_view_tree_order" model="ir.ui.view">
        <field name="name">lunch.product.tree.order</field>
        <field name="inherit_id" ref="lunch_product_view_tree"/>
        <field name="model">lunch.product</field>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">lunch_list</attribute>
                <attribute name="create">0</attribute>
            </xpath>
        </field>
    </record>

    <record id="lunch_product_view_form" model="ir.ui.view">
        <field name="name">lunch.product.form</field>
        <field name="model">lunch.product</field>
        <field name="arch" type="xml">
            <form string="Products Form">
                <field name="company_id" invisible="1"/>
                <field name="currency_id" invisible="1"/>
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_title">
                        <label for="name" class="oe-edit-only"/>
                        <h1>
                            <field name='name'/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name='category_id'/>
                            <field name='supplier_id'/>
                            <field name='price' widget="monetary"/>
                        </group>
                        <group>
                            <field name="new_until"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <label for="description"/>
                        <field name='description'/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_lunch_product_kanban_order" model="ir.ui.view">
        <field name="name">lunch.product.kanban</field>
        <field name="model">lunch.product</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <kanban js_class="lunch_kanban" create="0" edit="0" group_create="0" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="supplier_id"/>
                <field name="description"/>
                <field name="currency_id"/>
                <field name="company_id"/>
                <field name="is_new"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                            <field name="image_128" class="o_lunch_image o_kanban_image_fill_left" options="{'placeholder': '/lunch/static/img/lunch.png', 'size': [94, 94]}" widget="image"/>
                            <div class="oe_kanban_details ml8">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div>
                                                    <field class="pe-1" name="is_favorite" widget="boolean_favorite" nolabel="1"/>
                                                    <strong><span t-esc="record.name.value"/></strong>
                                                </div>
                                                <div class="text-odoo">
                                                    <div t-if="record.is_new.raw_value" class="o_lunch_new_product me-1 py-1 fs-6 badge rounded-pill text-bg-success">
                                                        New
                                                    </div>
                                                    <field name="price" widget="monetary"/>
                                                </div>
                                            </div>
                                        </strong>
                                        <span class="o_kanban_record_subtitle"><span t-esc="record.supplier_id.value"/></span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <ul>
                                        <li t-out="record.description.value" class="text-muted"/>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_lunch_product_kanban" model="ir.ui.view">
        <field name="name">lunch.product.kanban</field>
        <field name="model">lunch.product</field>
        <field name="priority">5</field>
        <field name="arch" type="xml">
            <kanban create="1" edit="0" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="supplier_id"/>
                <field name="description"/>
                <field name="currency_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                            <div class="o_kanban_image_fill_left d-none d-md-block"
                                t-attf-style="background-image:url('#{kanban_image('lunch.product', 'image_128', record.id.raw_value)}')"/>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <div>
                                                <div class="float-end">
                                                    <field name="price" widget="monetary"/>
                                                </div>
                                                <strong><span t-esc="record.name.value"/></strong>
                                            </div>
                                        </strong>
                                        <span class="o_kanban_record_subtitle"><span t-esc="record.supplier_id.value"/></span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <ul>
                                        <li t-out="record.description.value" class="text-muted"/>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="lunch_product_action_statbutton" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="res_model">lunch.product</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_group_by_supplier': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new product for lunch
            </p><p>
            A product is defined by its name, category, price and vendor.
            </p>
        </field>
    </record>

    <record id="lunch_product_category_view_tree" model="ir.ui.view">
        <field name="name">Product category Tree</field>
        <field name="model">lunch.product.category</field>
        <field name="arch" type="xml">
            <tree string="Products List">
                <field name='name' string="Product Category"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="lunch_product_category_view_form" model="ir.ui.view">
        <field name="name">Product category Form</field>
        <field name="model">lunch.product.category</field>
        <field name="arch" type="xml">
            <form string="Product Categories Form">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="action" name="%(lunch.lunch_product_action_statbutton)d"
                            context="{'search_default_category_id': active_id,'default_category_id': active_id}"
                            attrs="{'invisible': [('product_count', '=', 0)]}"
                            icon="fa-cutlery">
                            <field string="Products" name="product_count" widget="statinfo"/>
                        </button>
                    </div>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_title">
                        <label for="name" class="oe-edit-only"/>
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <label for="company_id" groups="base.group_multi_company"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </sheet>
            </form>
        </field>
    </record>

     <record id="lunch_product_category_view_kanban" model="ir.ui.view">
        <field name="name">Product category Kanban</field>
        <field name="model">lunch.product.category</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill o_kanban_record">
                            <div class="o_kanban_image_fill_left d-none d-md-block"
                                t-attf-style="background-image:url('#{kanban_image('lunch.product.category', 'image_128', record.id.raw_value)}')"/>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <div class="float-end">
                                            <button class="badge text-bg-primary" type="action"
                                                name="%(lunch.lunch_product_action_statbutton)d"
                                                context="{'search_default_category_id': active_id,'default_category_id': active_id}"
                                                attrs="{'invisible': [('product_count', '=', 0)]}">
                                                <field string="Products" name="product_count" widget="statinfo"/>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <strong><field name="name"/></strong><br/>
                                    <field name="company_id" groups="base.group_multi_company"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="lunch_product_category_view_search" model="ir.ui.view">
        <field name="name">lunch.product.category.search</field>
        <field name="model">lunch.product.category</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="lunch_product_action" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="res_model">lunch.product</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new product for lunch
            </p><p>
            A product is defined by its name, category, price and vendor.
            </p>
        </field>
    </record>

    <record id="lunch_product_action_order" model="ir.actions.act_window">
        <field name="name">Order Your Lunch</field>
        <field name="res_model">lunch.product</field>
        <field name="view_mode">kanban,tree</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('view_lunch_product_kanban_order')}),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('lunch_product_view_tree_order')})
        ]"/>
        <field name="search_view_id" ref="lunch_product_view_search"/>
        <field name="context">{'search_default_available_today': 1}</field>
        <field name="domain">[]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            There is no product available today
            </p><p>
            To see some products, check if your vendors are available today and that you have configured some products
            </p>
        </field>
    </record>

    <record id="lunch_product_category_action" model="ir.actions.act_window">
        <field name="name">Product Categories</field>
        <field name="res_model">lunch.product.category</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="search_view_id" ref="lunch_product_category_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new product category
            </p><p>
            Here you can access all categories for the lunch products.
            </p>
        </field>
    </record>
</odoo>
