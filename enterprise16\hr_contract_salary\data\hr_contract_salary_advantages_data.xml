<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <record id="l10n_be_monthly_benefit" model="hr.contract.salary.advantage.type">
        <field name="name">Monthly Benefit in Kind</field>
        <field name="sequence">5</field>
    </record>

    <record id="l10n_be_monthly_net" model="hr.contract.salary.advantage.type">
        <field name="name">Monthly Advantages in Net</field>
        <field name="sequence">10</field>
    </record>

    <record id="l10n_be_monthly_cash" model="hr.contract.salary.advantage.type">
        <field name="name">Monthly Advantages in Cash</field>
        <field name="sequence">15</field>
    </record>

    <record id="l10n_be_yearly_cash" model="hr.contract.salary.advantage.type">
        <field name="name">Yearly Advantages in Cash</field>
        <field name="sequence">20</field>
        <field name="periodicity">yearly</field>
    </record>

    <record id="l10n_be_non_financial_advantages" model="hr.contract.salary.advantage.type">
        <field name="name">Non Financial Advantages</field>
        <field name="sequence">25</field>
    </record>
    </data>
</odoo>
