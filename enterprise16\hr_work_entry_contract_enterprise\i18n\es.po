# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_enterprise
# 
# Translators:
# <PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_work_entry_contract_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract_enterprise.hr_work_entry_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.hr_menu_contract
msgid "All Contracts"
msgstr "Todos los contratos"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_hr_payroll_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_work_entry_conflicts
msgid "Conflicts"
msgstr "Conflictos"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_hr_payroll_root
msgid "Payroll"
msgstr "Nómina"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_hr_payroll_work_entries_root
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_hr_work_entry_configuration
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract_enterprise.hr_work_entry_gantt
msgid "Work Entries"
msgstr "Entradas de trabajo"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_hr_work_entry_type_view
msgid "Work Entry Types"
msgstr "Tipos de entradas de trabajo"

#. module: hr_work_entry_contract_enterprise
#: model:ir.ui.menu,name:hr_work_entry_contract_enterprise.menu_resource_calendar_view
msgid "Working Times"
msgstr "Tiempos de Trabajo"
