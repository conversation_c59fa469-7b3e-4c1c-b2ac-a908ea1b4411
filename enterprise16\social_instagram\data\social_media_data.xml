<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="social_media_instagram" model="social.media">
            <field name="name">Instagram</field>
            <field name="media_type">instagram</field>
            <field name="media_description">Manage your Instagram Business account and schedule posts</field>
            <field name="image" type="base64" file="social_instagram/static/src/img/instagram.svg"/>
        </record>

        <record id="stream_type_instagram_posts" model="social.stream.type">
            <field name="name">Instagram Posts</field>
            <field name="stream_type">instagram_posts</field>
            <field name="media_id" ref="social_instagram.social_media_instagram"></field>
        </record>
    </data>
</odoo>
