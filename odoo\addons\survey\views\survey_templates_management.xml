<?xml version="1.0" encoding="utf-8"?>
<odoo><data>

    <!-- ============================================================ -->
    <!--                Errors / Corner case management               -->
    <!-- ============================================================ -->

    <!-- Forbidden error messages-->
    <template id="survey_403_page" name="Survey: custom 403 page">
        <t t-call="survey.layout">
            <div id="wrap">
                <div class="container">
                    <h1 class="mt32">403: Forbidden</h1>
                    <p>The page you were looking for could not be authorized.</p>
                    <p>Maybe you were looking for
                        <a t-attf-href="/web#view_type=form&amp;model=survey.survey&amp;id=#{survey.id}&amp;action=survey.action_survey_form">this page</a> ?
                    </p>
                </div>
            </div>
        </t>
    </template>

    <!-- Error: void survey -->
    <template id="survey_void_content" name="Survey: void content">
        <t t-call="survey.layout">
            <t t-if="answer.test_entry" t-call="survey.survey_button_form_view" />
            <div class="wrap">
                <div class="container">
                    <div class="jumbotron mt32">
                        <h1><span t-field="survey.title"/> survey is empty</h1>
                        <p t-if="env.user.has_group('survey.group_survey_user')">
                            Please make sure you have at least one question in your survey. You also need at least one section if you chose the "Page per section" layout.<br />
                            <a t-att-href="'/web#view_type=form&amp;model=survey.survey&amp;id=%s&amp;action=survey.action_survey_form' % survey.id"
                                class="btn btn-secondary"
                                groups="survey.group_survey_manager">Edit in backend</a>
                        </p>
                        <p t-else="">
                            No question yet, come back later.
                        </p>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Error: auth required  -->
    <template id="survey_auth_required" name="Survey: login required">
        <t t-call="survey.layout">
            <div class="wrap">
                <div class="container">
                    <div class="jumbotron mt32">
                        <h1>Login required</h1>
                        <p>This survey is open only to registered people. Please
                            <a t-att-href="redirect_url">log in</a>.
                        </p>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Expired (closed) survey page -->
    <template id="survey_closed_expired" name="Survey: expired">
        <t t-call="survey.layout">
            <div class="wrap">
                <div class="container">
                    <div class="jumbotron mt32">
                        <h1><span t-field="survey.title"/> survey expired</h1>
                        <p>This survey is now closed. Thank you for your interest !</p>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- ============================================================ -->
    <!--                       Tools / Utilities                      -->
    <!-- ============================================================ -->

    <template id="survey_button_form_view" name="Survey: back to form view">
        <div groups="survey.group_survey_manager" t-ignore="true" class="alert alert-info p-2 border-0 rounded-0 d-print-none css_editable_mode_hidden mb-0">
            <div t-ignore="true" class="text-center">
                <a t-attf-href="/web#view_type=form&amp;model=survey.survey&amp;id=#{survey.id}&amp;action=survey.action_survey_form"><span t-if="answer and answer.test_entry">This is a Test Survey. </span><i class="fa fa-fw fa-arrow-right"/>Edit Survey</a>
            </div>
        </div>
    </template>

    <template id="survey_button_retake" name="Survey: retake button">
        <div>
            <t t-if="not answer.scoring_success and not answer.is_session_answer">
                <t t-if="survey.is_attempts_limited">
                    <t t-set="attempts_left" t-value="survey._get_number_of_attempts_lefts(answer.partner_id, answer.email, answer.invite_token)" />
                    <t t-if="attempts_left > 0">
                        <p><span>Number of attempts left</span>: <span t-esc="attempts_left"></span></p>
                        <p><a role="button" class="btn btn-primary btn-lg" t-att-href="'/survey/retry/%s/%s' % (survey.access_token, answer.access_token)">
                        Retry</a></p>
                    </t>
                </t>
                <t t-else="">
                    <p><a role="button" class="btn btn-primary btn-lg" t-att-href="'/survey/retry/%s/%s' % (survey.access_token, answer.access_token)">
                        Take Again</a></p>
                </t>
            </t>
        </div>
    </template>

    <!-- Survey Home page - Session Code
    Used in 'session mode' to give an easy access to the survey through the '/s' route. -->
    <template id="survey_session_code" name="Survey: Access Code page">
        <t t-call="survey.layout">
            <div class="wrap o_survey_wrap pb16 d-flex">
                <div class="container o_survey_quick_access d-flex flex-column">
                    <div class="d-flex flex-grow-1 align-items-center">
                        <div class="w-100 px-4 px-md-0">
                            <div class="text-center mb32">
                                <h3>Enter Session Code</h3>
                            </div>
                            <div class="row">
                                <div class="col-12 col-md-4 offset-md-4 text-center">
                                    <input id="session_code"
                                        type="text" placeholder="e.g. 4812"
                                        autocomplete="off"
                                        t-attf-value="{{session_code if session_code else ''}}"
                                        class="form-control o_survey_question_text_box fw-bold bg-transparent text-primary text-center rounded-0 p-2 w-100"/>
                                </div>
                                <div class="col-12 col-md-4 offset-md-4 text-center o_survey_error text-danger h4 pt-2 px-0" role="alert">
                                    <span t-attf-class="o_survey_session_error_invalid_code {{'d-none' if error != 'survey_wrong' else ''}}">Oops! No survey matches this code.</span>
                                    <span t-attf-class="o_survey_session_error_not_launched {{'d-none' if error != 'survey_session_not_launched' else ''}}">The session did not start yet.</span>
                                </div>
                            </div>
                            <div class="text-center mt32 p-2">
                                <button groups="survey.group_survey_user" type="button" t-attf-class="o_survey_launch_session btn btn-primary {{'d-none' if error != 'survey_session_not_launched' else ''}}" t-att-data-survey-id="survey_id">Launch Session</button>
                                <button type="submit" t-attf-class="btn btn-primary {{'d-none' if error == 'survey_session_not_launched' and request.env.user.has_group('survey.group_survey_user') else ''}}">Join Session</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="survey_navigation" name="Survey: Navigation">
        <div class="d-inline-block">
            <button
                t-if="survey and survey.users_can_go_back"
                t-att-disabled="not can_go_back"
                type="submit" class="btn border-start p-0 shadow-none o_survey_navigation_submit" name="button_submit" value="previous" t-att-data-previous-page-id="previous_page_id">
                <i class="fa fa-chevron-left p-2" />
            </button>
            <t t-set="can_go_forward"
                t-value="survey and survey.questions_layout in ['page_per_question', 'page_per_section'] and answer and answer.state != 'done' and not answer.is_session_answer"/>
            <button
                t-att-disabled="not can_go_forward"
                type="submit" class="btn border-start p-0 shadow-none o_survey_navigation_submit" t-att-value="'next' if not survey_last else 'finish'">
                <i class="fa fa-chevron-right p-2" />
            </button>
        </div>
    </template>

</data>
</odoo>
