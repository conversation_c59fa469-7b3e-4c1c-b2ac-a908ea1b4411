<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="social_post_view_form" model="ir.ui.view">
        <field name="name">social.post.view.form.inherit.social.sale</field>
        <field name="model">social.post</field>
        <field name="inherit_id" ref="social.social_post_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[hasclass('o_social_post_engagement_stat_btn')]" position="before">
                <button class="oe_stat_button" name="action_redirect_to_quotations"  type="object"
                    icon="fa-pencil-square-o" groups="sales_team.group_sale_salesman">
                    <field name="sale_quotation_count" string="Quotations" widget="statinfo"/>
                </button>
                <button class="oe_stat_button" name="action_redirect_to_invoiced"  type="object"
                    icon="fa-usd" groups="sales_team.group_sale_salesman">
                    <field name="sale_invoiced_amount" string="Revenues" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>
    <record id="social_post_view_kanban" model="ir.ui.view">
        <field name="name">social.post.view.kanban.inherit.social.sale</field>
        <field name="model">social.post</field>
        <field name="inherit_id" ref="social.social_post_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='click_count']" position="after">
                <field name="sale_invoiced_amount"/>
                <field name="sale_quotation_count"/>
            </xpath>
            <xpath expr="//div[@id='post-stats']" position="attributes">
                <attribute name="class" add="justify-content-between" separator=" "/>
            </xpath>
            <xpath expr="//div[@id='post-stats']" position="inside">
                <div class="px-2 text-center" groups="sales_team.group_sale_salesman">
                    <small class="pe-1">Quotations:</small> <small class="fw-bold"><t t-esc="record.sale_quotation_count.raw_value"/></small>
                </div>
                <div class="px-2 text-center" groups="sales_team.group_sale_salesman">
                    <small class="pe-1">Revenues:</small> <small class="fw-bold"><t t-esc="record.sale_invoiced_amount.raw_value"/></small>
                </div>
            </xpath>
        </field>
    </record>
</data>
</odoo>
