from odoo import fields, models, api, _


class AccountMoveInherit(models.Model):
    _inherit = 'account.move'

    cubes_analytic_account_id = fields.Many2one(
        'account.analytic.account',
        string='Analytic Account',
        compute='_compute_cubes_analytic_account_id',
        store=True
    )

    @api.depends('partner_id')
    def _compute_cubes_analytic_account_id(self):
        for rec in self:
            sale_orders = self.env['sale.order'].search([
                ('invoice_ids', 'in', [rec.id])  # FIX: Convert rec.id to a list
            ])
            if sale_orders:
                rec.cubes_analytic_account_id = sale_orders[0].cubes_analytic_account_id.id
            else:
                rec.cubes_analytic_account_id = False
