<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="sales_brand_report_temp">
        <t t-call="web.html_container">
            <t t-call="cubes_sales_brand_report.external_sales_brand_report_header">
                <div class="page" style="font-size:12px;color:#000">
                    <style>
                        .header_table{
                        width:100%;
                        }
                        .header_table th{
                        padding:5px;
                        text-align:center;
                        border:1px solid black;
                        background:#c8cace;
                        }
                        .header_table td{
                        padding:5px;
                        text-align:center;
                        border:1px solid black;
                        }
                        .my_table{
                        width:100%;
                        }
                        .my_table th{
                        padding:5px;
                        }
                        .my_table .bottom_my_table th{
                        border-bottom:1px solid #000;
                        }
                        .my_table td{
                        padding:5px;
                        border-bottom:1px solid #efefef;
                        }
                    </style>
                    <div class="row">
                        <center>
                            <h3>Sales Brand Report</h3>
                        </center>
                        <br/>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <table class="header_table">
                                <thead>
                                    <tr>
                                        <th style="width:9%">Product</th>
                                        <t t-foreach="dates" t-as="d">
                                            <th style="width:9%">Qty Delivered (<t t-esc="d" />)</th>
                                        </t>
                                        <th style="width:9%">Total Qty Delivered</th>
                                        <th style="width:9%">On Hand</th>
                                    </tr>
                                </thead>
                                <tbody class="sale_tbody">
                                    <tr t-foreach="products" t-as="product">
                                        <td>
                                            <t t-esc="product['name']"/>
                                        </td>
                                        <t t-set="total_delivered" t-value="0" />
                                        <t t-foreach="product['months_qty_delivered'].split('-')[0].split(',')" t-as="m">
                                            <td t-esc="m" />
                                            <t t-set="total_delivered" t-value="total_delivered+float(m)" />
                                        </t>
                                        <td>
                                            <t t-esc="total_delivered"/>
                                        </td>
                                        <td>
                                            <t t-esc="product['qty_available']"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row mt32 mb32"/>

                </div>
            </t>
        </t>
    </template>
</odoo>