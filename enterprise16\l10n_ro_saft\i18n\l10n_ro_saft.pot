# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ro_saft
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-08 15:20+0000\n"
"PO-Revision-Date: 2023-08-08 15:20+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_type_id
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_type_id
msgid ""
"A 3-digit number defined by ANAF to identify a type of tax in the D.406 "
"export (e.g. 300 for VAT, 150 for withholding taxes on dividends...)"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_code
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_code
msgid ""
"A 6-digit number defined by ANAF to precisely identify taxes in the D.406 "
"export (e.g. 310309 for domestic 19% VAT)"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__a
msgid "A: general commercial companies using the general CoA for businesses"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_539
msgid "Addiction fee for slotmachine gaming features"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_114
msgid ""
"Additional 20% share of investment value according to art. 38 paragraph(13) "
"from the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_128
msgid "Additional offshore Corporate Income Tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_540
msgid "Administrative fees for applying for a license for remote gambling"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_102
msgid ""
"Advance payments for annual income tax due by commercial banks, Romanian "
"legal persons and Romanian bank subsidiaries, foreign legal persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_626
msgid ""
"Advance payments of the tax for some incomes from independent activities, as"
" well as for incomes from other sources provided in art. 114 para. (2) lit. "
"k ^ 1) of Law 227/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_998
msgid ""
"Amounts collected for the state budget, the state insurance budget and the "
"budget of the Single National Health Insurance Fund, currently being "
"distributed"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_344
msgid ""
"Amounts deducted from VAT for state pre-university educational institutions,"
" nurseries, county and local centers of agricultural consulting and support "
"for child protection system"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_954
msgid ""
"Amounts from the recovery of debts arising from undue income related to the "
"state budget"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_959
msgid ""
"Amounts from the recovery of debts arising from undue rights related to the "
"insurance system for accidents at work and occupational diseases"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_958
msgid ""
"Amounts from the recovery of debts arising from undue rights related to the "
"public pension system"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_951
msgid "Amounts of debt collection from undue rights"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_811
msgid ""
"Amounts payable due to special protection and qualification of disabled "
"people"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_990
msgid ""
"Amounts representing income tax and social contributions due by individuals "
"being distributed"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_304
msgid "Annual - value-added tax (VAT)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_537
msgid "Annual duty for obtaining authorization for operating gambling games"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_104
msgid "Annual tax income"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_524
msgid "Authorization duty for operating gambling for TV bingo games"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_523
msgid "Authorization duty for operating gambling for bingo halls"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_521
msgid "Authorization duty for operating gambling for casino activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_520
msgid "Authorization duty for operating gambling for fixed odds betting"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_518
msgid "Authorization duty for operating gambling for lottery games"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_522
msgid "Authorization duty for operating gambling for slot-machines"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_519
msgid "Authorization duty for operating gambling for totalizator"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_790
msgid ""
"Authorization fees for marketing alcohol, alcoholic beverages, tobacco and "
"coffee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_995
msgid ""
"Available from amounts collected from deduction (bank/third party) of "
"amounts owned to debtors"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_997
msgid ""
"Available from amounts collected representing financial loss caused and "
"recovered in terms of art. 10 from Law no. 241/2005"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_947
msgid "Available from cars emission tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_994
msgid ""
"Available social insurance and special funds budgets, ongoing distribution"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__bank
msgid ""
"BANK: banks and financial instituttions using the CoA for financial "
"institutions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_458
msgid "CAS payed by employer/tax payer (art.146 alin. 5^6 from Fiscal Code)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_457
msgid "CASS owed by retired persons who exceed the ceiling of 4000 lei"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_459
msgid "CASS payed by employer/tax payer (art.168 alin. 6^1 from Fiscal Code)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_776
msgid "Car pollution tax, compensated/ refunded"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_bank_statement_line__l10n_ro_is_self_invoice
#: model:ir.model.fields,help:l10n_ro_saft.field_account_move__l10n_ro_is_self_invoice
#: model:ir.model.fields,help:l10n_ro_saft.field_account_payment__l10n_ro_is_self_invoice
msgid ""
"Check this box to indicate that this bill is a self-invoice made to register"
" a VAT event in the absence of an invoice received from a supplier."
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__code
msgid "Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_840
msgid ""
"Collections from redemption of loans granted for arrears coverage toward "
"CONEL and ROMGAZ"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_915
msgid "Collections from retained share, according to the Penal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_923
msgid ""
"Collections from the sale of seized, abandoned goods and other amounts "
"identified with the appropriate seizure law"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_759
msgid ""
"Compensation for all damages caused by the exercise of the right of passage"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_754
msgid ""
"Compensation related to the right of passage (tool-tax) in exchange for the "
"limitations brought to the right of use"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_772
msgid "Consular fees"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_830
msgid "Contribution by tourism undertakings"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_417
msgid "Contribution for Social insurance fund"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_456
msgid ""
"Contribution for amount of medicines consumed exceeding volumes determined "
"by contracts"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_442
msgid ""
"Contribution for vacation and compensation by people which are unable to "
"work due to a work accident or professional illnesses"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_443
msgid ""
"Contribution for vacation and compensation by people which are unable to "
"work due to a work accident or professional illnesses accounted for from the"
" insurance fund for work accidents and professional illnesses"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_440
msgid "Contribution for vacation and compensation by unemployed people"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_439
msgid ""
"Contribution for vacation and compensation from natural or legal persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_941
msgid ""
"Contribution to health services expenses for domestic tobacco production"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_944
msgid ""
"Contribution to health services expenses for imported alcoholic beverages"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_942
msgid "Contribution to health services expenses for imported tobacco products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_943
msgid ""
"Contribution to health services expenses for intern alcoholic beverages "
"products production"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_945
msgid ""
"Contribution to health services expenses from publicity activities on "
"tobacco and alcoholic beverages products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_820
msgid "Contribution to public education"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_414
msgid ""
"Contribution to supplementary pension retained from people included in the "
"social insurance system"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_708
msgid "Contribution to the energy transition fund"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_420
msgid "Contribution to unemployment fund"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_421
msgid "Contribution to unemployment fund by the employer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_422
msgid "Contribution to unemployment fund deducted from the insured persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_107
msgid "Corporate Income Tax exempted"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_924
msgid "Court fines"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_850
msgid "Custom duties from legal persons"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__description
msgid "Description"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_760
msgid "Development tax included in the tariff of electricity and heating"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_751
msgid ""
"Directed income from the flat tax on automotive fuels delivered internally "
"by producers as well as automotive fuels consumed and imported by them"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_781
msgid "Dividends paid to the state budget by central public authorities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_536
msgid "Duty for obtaining license for organizing gambling activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_510
msgid ""
"Duty for organizing and operating gambling activities due until 01.01.2014"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_932
msgid ""
"Encashments from the capitalization of confiscated, abandoned goods and "
"other amounts ascertained together with the confiscation according to the "
"law"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_933
msgid ""
"Encashments from the capitalization of confiscated, abandoned goods and "
"other amounts ascertained together with the confiscation according to the "
"law by the General Directorate for Fiscal Antifraud"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_791
msgid "Environmental stamp"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_200
msgid "Excise"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_252
msgid ""
"Excise duties on fermented beverages other than beer and wine, in which the "
"absolute alcohol content (100%) resulting from the exclusive fermentation of"
" fruit, fruit juices and concentrated fruit juices is less than 50%"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_226
msgid "Excise duties on heated tobacco products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_280
msgid "Excise duty collected from the sale of air conditioners"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_210
msgid ""
"Excise duty collected from the sale of alcohol, distilates and alchoholic "
"berevages, due until 31.12.2006"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_260
msgid "Excise duty collected from the sale of cars from domestic production"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_230
msgid "Excise duty collected from the sale of energy products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_261
msgid ""
"Excise duty for cars which were the subject of leases entered into before "
"1st January 2007"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_211
msgid "Excise duty on beer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_251
msgid ""
"Excise duty on beer / beer base in mixtures with non-alcoholic beverages, in"
" which the share of Plato grades derived from malt, malleable and / or non-"
"malleable cereals is less than 30% of the total number of Plato grades"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_258
msgid ""
"Excise duty on bullet cartridges and other ammunition for hunting and "
"personal use weapons, other than for military or sporting use"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_221
msgid "Excise duty on cigarettes"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_222
msgid "Excise duty on cigaretts and cigarette sheets"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_237
msgid "Excise duty on coal and coke"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_240
msgid "Excise duty on coffee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_233
msgid "Excise duty on diesel and biodiesel"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_270
msgid "Excise duty on electricity"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_259
msgid ""
"Excise duty on engines of more than 100 hp, for yachts and other pleasure "
"craft"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_215
msgid "Excise duty on ethyl alcohol"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_213
msgid "Excise duty on fermented, sparking beverages, other than beer and wine"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_224
msgid "Excise duty on fine-cut smoking tobacco for cigar rolling"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_234
msgid "Excise duty on fuel oil"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_254
msgid "Excise duty on garments made of natural furs"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_253
msgid "Excise duty on gold and / or platinum jewelery, except wedding rings"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_244
msgid "Excise duty on green coffee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_257
msgid ""
"Excise duty on hunting weapons and weapons for personal use, other than for "
"military or sporting use"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_243
msgid "Excise duty on instant coffee including instant coffee blends"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_214
msgid "Excise duty on intermediate products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_238
msgid "Excise duty on lamp oil (kerosene)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_231
msgid "Excise duty on leaded petrol"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_235
msgid "Excise duty on liquefied petroleum gas"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_227
msgid ""
"Excise duty on liquids containing nicotine for inhalation by means of an "
"electronic device such as an electronic cigarette"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_239
msgid "Excise duty on mineral oils due until 31.12.2006"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_236
msgid "Excise duty on natural gas"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_256
msgid ""
"Excise duty on on passenger cars and off-road vehicles with a cylinder "
"capacity of 3,000 cm3 or more"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_275
msgid "Excise duty on other excise goods (energy)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_225
msgid "Excise duty on other smoking tobacco"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_242
msgid "Excise duty on roasted coffee including coffee with substitutes"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_220
msgid "Excise duty on sales of tobacco products"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_223
msgid "Excise duty on smoking tobacco"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_212
msgid "Excise duty on sparkling wines"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_217
msgid "Excise duty on still fermented beverages, other than beer and wine"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_216
msgid "Excise duty on still wines"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_250
msgid "Excise duty on the sales of other products due until 30 September 2013"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_218
msgid ""
"Excise duty on the sales of still fermented beverages other than beer and "
"wine  due until 31.01.2011"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_232
msgid ""
"Excise duty on unleaded petrol and denatured bioethanol used as motor fuel "
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_255
msgid ""
"Excise duty on yachts and other ships and boats with or without motor for "
"pleasure, other than for use in performance sports"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_241
msgid "Excise duty to be refunded/offset from the sale of diesel "
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_290
msgid "Excise duty to be reimbursed"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_283
msgid "Excisescollected when issuing fiscal banners for alcoholic beverages"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_415
msgid "Farmers' contribution to the pension fund and social insurance"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_551
msgid "Fee owed to the Romanian Olympic and Sports Committee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_552
msgid "Fee owed to the Romanian Paralympic Committee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_777
msgid "Fees and other income from environmental protection"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_608
msgid "Financial awards income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_535
msgid "Gambling access fee"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_621
msgid "Gambling income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_517
msgid "Gambling license duty for TV bingo games"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_516
msgid "Gambling license duty for bingo halls"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_514
msgid "Gambling license duty for casino activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_513
msgid "Gambling license duty for fixed odds betting"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_511
msgid "Gambling license duty for lottery games"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_515
msgid "Gambling license duty for slot-machines"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_512
msgid "Gambling license duty for totalizator"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_503
msgid "Gambling social stamp tax"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Generate SAF-T"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_303
msgid "Half-yearly - value-added tax (VAT)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_430
msgid "Health insurance contribution"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_460
msgid "Health insurance contribution by natural persons - regularizations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_441
msgid ""
"Health insurance contribution by retirees for income that exceeds the "
"threshold required by law"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_431
msgid "Health insurance contribution by the employer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_448
msgid ""
"Health insurance contribution by the employer for people who are in medical "
"leave for incapacity of work, due to a work accident or professional "
"illnesses supported by FAAMBP, according to the Law no. 95/2006"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_432
msgid "Health insurance contribution deducted from the insured persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_444
msgid ""
"Health insurance contribution for people from families that are entitled to "
"welfare, according to Law no. 416/2001 regarding the minimum guaranteed "
"income, with subsequent amendments"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_434
msgid "Health insurance contribution for people in ongoing military service"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_438
msgid ""
"Health insurance contribution for people in sick leave, according to the Law"
" no. 95/2006 regarding healthcare reform"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_435
msgid ""
"Health insurance contribution for people who perform custodial sentence or "
"remand"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_433
msgid ""
"Health insurance contribution for people whose duties are paid from the "
"Unemployment Insurance Fund"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_437
msgid ""
"Health insurance contribution for persons in parental leave up to the age of"
" 2 and for a disabled child up to the age of 3"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_436
msgid ""
"Health insurance contribution for sick leaves which is deducted from social "
"insurance contribution by the employer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_955
msgid "Hedge fund"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__i
msgid ""
"I: non-residents and taxpayers with obligation to submit a special VAT "
"return"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__id
msgid "ID"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__ifn
msgid ""
"IFN: non-bank financial institutions using the CoA of BNR Reg. no. 17/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__ifrs
msgid ""
"IFRS: general commercial companies using the CoA for companies applying IFRS"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__insurance
msgid "INSURANCE: insurance companies using the CoA for insurance companies"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"In the Company settings, please set your company VAT number under `Tax ID` "
"if registered for VAT, or your CUI number under `Company Registry`."
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_911
msgid ""
"Income for state social insurance budget from fines and other penalties "
"imposed under legal provisions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_950
msgid "Income from capitalization of public goods"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_961
msgid ""
"Income from commission charged by territorial labor inspectorates due until "
"31 December 2010"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_922
msgid "Income from compensations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_771
msgid "Income from custom benefits tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_957
msgid "Income from expenses recovery incurred in the process of foreclosure"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_913
msgid "Income from fines applied to non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_971
msgid ""
"Income from guarantees granted and paid to credit institutions within <PRIMA"
" CASA> program"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_774
msgid "Income from provision of services"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_952
msgid "Income from recovery of court fees, imputations and compensations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_773
msgid "Income from technical, judicial and extrajudicial expertise"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_765
msgid "Income from the application of the extinctive prescription"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_753
msgid ""
"Income from the recovery of payments in advance for state legal expenses "
"from non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_100
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_600
msgid "Income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_814
msgid "Income tax deducted shares"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_105
msgid ""
"Income tax due by foreign legal persons, other than those from section 1, "
"due by foreign legal persons that carry out activities by means of a "
"permanent office in Romania"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_111
msgid ""
"Income tax exempted according to art. 38 paragraph(1) from the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_112
msgid ""
"Income tax exempted according to art. 38 paragraph(3) from the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_113
msgid ""
"Income tax exempted according to art. 38 paragraph(8), (9) or (11) from the "
"Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_160
msgid ""
"Income tax for Romanian offices of foreign commercial and economic "
"organizations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_622
msgid ""
"Income tax for individuals' income from an association with a taxpayer legal"
" entity, according to Title II of the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_770
msgid "Income tax from higher education institutions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_601
msgid "Income tax from independent services"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_623
msgid ""
"Income tax on natural persons' income from an association with a taxable "
"legal person, according to title III of the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_675
msgid "Income tax related to the Unique Declaration"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_103
msgid ""
"Income tax/ advance payments for annual income tax due by Romanian legal "
"persons, others than those from section 1 and legal persons with "
"headquarters in Romania"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_762
msgid "Incomes from judicial stamp"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_925
msgid "Increases due to revenue not paid on time"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_467
msgid "Individual contribution to social security (OMEF1646/2007)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_825
msgid ""
"Individual contribution to the state budget (pensions for military personel)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_424
msgid ""
"Individual contribution to unemployment fund by people who obtain "
"professional income other than salary"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_462
msgid ""
"Individual health insurance contribution by people with revenue from "
"activities performed under contracts / civil agreements concluded according "
"to the Civil Code as well as agent's contract"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_463
msgid ""
"Individual health insurance contribution by people with revenue from "
"technical, judicial and extrajudicial expertise"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_464
msgid ""
"Individual health insurance contribution by people with revenues from an "
"association with a micro-enterprise, according to the IV^1 Title from the "
"Fiscal Code, which is not a legal person"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_470
msgid ""
"Individual health insurance contribution by people with revenues from "
"granting property"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_468
msgid ""
"Individual health insurance contribution by people with revenues from "
"independent activities or by people with no revenue"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_461
msgid ""
"Individual health insurance contribution by people with revenues from "
"intellectual property rights"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_469
msgid ""
"Individual health insurance contribution by people with revenues from leases"
" of agricultural goods, withholding income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_465
msgid ""
"Individual health insurance contribution by people with revenues, "
"withholding income tax from associations with no legal persons referred in "
"art. 13 letter e) from Tax Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_466
msgid ""
"Individual health insurance contribution by people with revenues, "
"withholding tax on income from agricultural associations referred in art. 71"
" letter d) from Tax Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_447
msgid ""
"Individual health insurance contribution for monastic staff of recognized "
"religions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_445
msgid ""
"Individual health insurance contribution for persons located in detention "
"centers for deportation, and for persons undergoing procedures to establish "
"their identity that are accommodated in special centers"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_446
msgid ""
"Individual health insurance contribution for persons who are undertaking "
"measures provided in art. 105, 113 and 114 from the Penal Code and for "
"persons who are in postponement or interruption of imprisonment sentence"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_449
msgid ""
"Individual health insurance contribution for persons, Romanian citizens who "
"are victims of human trafficking, for a period of maximum 12 months"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_419
msgid ""
"Individual social insurance contribution by people who obtain professional "
"income other than salary"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_452
msgid ""
"Individual social insurance contribution by people with revenue from "
"activities performed under contracts / civil agreements concluded according "
"to the Civil Code as well as agent's contract"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_451
msgid ""
"Individual social insurance contribution by people with revenue from "
"intellectual property"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_453
msgid ""
"Individual social insurance contribution by people with revenue from "
"technical, judicial and extrajudicial expertise"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_480
msgid "Insurance contribution for labour"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_611
msgid "Intellectual property income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_305
msgid "Interest and penalties on late payment of VAT"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_605
msgid "Interest tax"
msgstr ""

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.view_move_form_l10n_ro
msgid "Is self invoice (RO)?"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_bank_statement_line__l10n_ro_is_self_invoice
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_move__l10n_ro_is_self_invoice
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_payment__l10n_ro_is_self_invoice
msgid "Is self invoice?"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_761
msgid "Judicial stamp taxes"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_619
msgid "Leasing agricultural goods income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_606
msgid ""
"Liquidation / dissolution without liquidation of a legal person income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_127
msgid "Micro-enterprise Income Tax exempt"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_121
msgid "Micro-enterprise income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_120
msgid "Micro-enterprises' income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_755
msgid "Mining royalties"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_301
msgid "Monthly - value-added tax (VAT)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_541
msgid ""
"Monthly fee calculated on the total participation fees collected monthly, "
"due by the organizers of online gambling, provided in GEO 77/2009 on the "
"organization and operation of gambling"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma14
msgid ""
"NORMA14: private pension companies using the CoA of ASF Reg. no. 14/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma36
msgid ""
"NORMA36: insurance and/or reinsurance brokerage companies using the CoA of "
"ASF Reg. no. 36/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma39
msgid ""
"NORMA39: leasing and financial companies using the IFRS CoA according to ASF"
" Reg. no. 39/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_710
msgid "Natural gas and oil intern production tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_927
msgid "Non-tax fileing penalties"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_756
msgid "Oil royalties"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_931
msgid "Other direct tax collection"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_926
msgid "Other fines, penalties and confiscations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_970
msgid "Other income"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_953
msgid "Other incomes from capitalization of assets"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_757
msgid "Other revenues from concessions and rentals of Public Institutions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_999
msgid "Other taxes, fees, contributions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_810
msgid "Payments from legal persons for unqualified disabled people"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_780
msgid "Payments from net profit of autonomous administrations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_101
msgid "Payments from net revenue of National Bank of Romania"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_778
msgid ""
"Payments from public institutions' liquidities and self-financed activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_960
msgid "Payments from salary reduction"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_699
msgid "Payroll Taxes (residual)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_602
msgid "Payroll taxes"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_921
msgid "Penalties due to scheduled payments"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_920
msgid ""
"Penalties for failure to file or late filing of the declaration of taxes"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_721
msgid "Penalties not paid on time"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_607
msgid "Pensions income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_504
msgid ""
"Periodically regulated annual gambling authorization fee, according to "
"income"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_502
msgid ""
"Periodically regulated tax on gambling according to income within reporting "
"time"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Bank Account` for your company."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Telephone Number` for your company."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please set the company Tax Accounting Basis in the Accounting Settings."
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_701
msgid "Property tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_302
msgid "Quarterly - value-added tax (VAT)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_455
msgid "Quarterly contribution for Cost-volume / cost-volume-outcome contracts"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_940
msgid "Quarterly contribution for financing health services expenses"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_450
msgid ""
"Quarterly contribution for financing medicine expenses covered from the "
"National Fund of Health Insurance and from the Ministry of Health's budget"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_946
msgid ""
"Quarterly contribution to medications covered from the National Fund of "
"Health Insurance and from the Ministry of Health's budget, outstanding at 1 "
"October 2011"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_986
msgid "Receivables compensation CAS (contributions to the social health fund)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_987
msgid ""
"Receivables compensation CASS (contributions to the social welfare found)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_985
msgid "Receivables compensation SB"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_988
msgid "Receivables compensation SOMAJ (unemployment contributions)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_972
msgid "Receivables recovered by joint liability"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_752
msgid "Refund for advanced state judicial expenses"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_982
msgid "Refund of budgetary financing of previous years - BCAS"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_983
msgid "Refund of budgetary financing of previous years - BSAN"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_984
msgid "Refund of budgetary financing of previous years - BSOM"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_981
msgid "Refund of budgetary financing of previous years - SB"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_916
msgid "Refunds from budget financing of previous years"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_610
msgid "Regularizations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_390
msgid "Reimbursement of the VAT"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_956
msgid "Revenue from the recovery of the State Aids"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_910
msgid ""
"Revenues from fines and other penalties imposed by other specialized "
"institutions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_912
msgid ""
"Revenues from fines and other penalties imposed by the Directorate General "
"for Tax Fraud"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_code
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_code
msgid "Romanian SAF-T Tax Code"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_l10n_ro_saft_tax_type
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_type_id
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_type_id
msgid "Romanian SAF-T Tax Type"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_110
msgid "Romanian fund"
msgstr ""

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "Romanian localization"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_758
msgid ""
"Royalties from concessions contracts, lease and other efficient exploitation"
" of agricultural land contracts"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "SAF-T"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_735
msgid ""
"Share applied to the monthly wages fund, including monthly gainings achieved"
" by individuals (art. 53/OUG no. 102/1999)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_730
msgid ""
"Share of the income realized by the Romanian legal persons provided at art. "
"2, letter a) from the OG no. 47/1998 regarding the establishment and use of "
"the Special Fund of civil aviation"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_991
msgid "Single account for non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_474
msgid ""
"Social health insurance contribution due by individuals who earn income "
"based on sports activity contracts"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_476
msgid ""
"Social health insurance contributions related to the Unique Declaration"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_410
msgid "Social insurance contribution"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_454
msgid ""
"Social insurance contribution by people with revenues from independent "
"activities, agricultural activities and associations without legal persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_411
msgid "Social insurance contribution by the employer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_412
msgid "Social insurance contribution deducted from the insured persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_481
msgid ""
"Social insurance contribution due in case of speccific working conditions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_482
msgid ""
"Social insurance contribution due in case of special working conditions"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_486
msgid ""
"Social insurance contribution for agriculture (art.60 pct. 7 from Fiscal "
"Code)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_413
msgid ""
"Social insurance contribution for people whose duties are paid from the "
"Unemployment Insurance Fund"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_416
msgid ""
"Social insurance contribution for work accidents and professional illnesses "
"caused by the employer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_418
msgid ""
"Social insurance contribution for work accidents and professional illnesses "
"for unemployed people"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_485
msgid ""
"Social insurance contributions due by insured persons on the basis of an "
"insurance contract"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_475
msgid "Social insurance contributions related to the Unique Declaration"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_993
msgid ""
"Social insurance income budget collected in the single account, ongoing "
"distribution"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_740
msgid ""
"Social stamp tax on the value of imported new vehicles with engine capacity "
"of more than 2000 cm3"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_709
msgid "Solidarity contribution"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_538
msgid "Special fee for video lottery"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_779
msgid "Special taxes for cars and vehicles at first registration in Romania"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_130
msgid "Specific Corporate Income Tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_992
msgid ""
"State budget income collected in the single account, ongoing distribution"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "Tax Accounting Basis"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_res_company__l10n_ro_saft_tax_accounting_basis
#: model:ir.model.fields,field_description:l10n_ro_saft.field_res_config_settings__l10n_ro_saft_tax_accounting_basis
msgid "Tax Accounting Basis (RO)"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_725
msgid ""
"Tax and tariffs on the issuing of licenses and operation authorizations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_711
msgid ""
"Tax on additional income obtained as a result of the natural gas prices "
"deregulation"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_161
msgid "Tax on additional income obtained by electricity producer"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_604
msgid "Tax on dividends for natural persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_763
msgid "Tax on financial aassets"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_500
msgid "Tax on gambling"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_501
msgid ""
"Tax on gambling in advance or monthly during gambling license availability"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_615
msgid ""
"Tax on income from accounting, technical, judicial and extrajudicial "
"expertise"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_616
msgid ""
"Tax on income from activities performed under contracts / civil agreements "
"concluded according to the Civil Code and agent contracts"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_613
msgid "Tax on income from agricultural activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_618
msgid ""
"Tax on income from commercial activities - tax on income based on "
"conventions / civil contracts concluded according to the Civil Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_634
msgid "Tax on income from commissions obtained in Romania by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_150
msgid "Tax on income from dividends for legal persons"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_631
msgid "Tax on income from dividends obtained in Romania by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_712
msgid ""
"Tax on income from exploitation activities of natural resources, others than"
" natural gas"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_632
msgid "Tax on income from interests obtained in Romania by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_122
msgid ""
"Tax on income from micro-enterprises created by the association of a natural"
" person with a legal person, not generating a legal person"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_690
msgid "Tax on income from other sources"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_633
msgid "Tax on income from royalties obtained in Romania by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_617
msgid ""
"Tax on income from sale operations - buying foreign currency, other than "
"those with financial instruments traded on authorized markets"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_636
msgid ""
"Tax on income from services provided in Romania and outside Romania by non-"
"residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_162
msgid ""
"Tax on income from the sale of agricultural land located outside the village"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_614
msgid ""
"Tax on income from the sale of assets in a consignment and from activities "
"carried out under an agent contract, commission or commercial mandate"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_641
msgid ""
"Tax on income from the transfer of fiduciary patrimony from the fiduciary to"
" non-resident beneficiary"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_642
msgid ""
"Tax on income from the transfer of securities and operations with derivative"
" financial instruments"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_140
msgid ""
"Tax on income generated in Romania by nonresident - legal persons, collected"
" until 31.01.2014"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_639
msgid ""
"Tax on income obtained by liquidation of a legal person by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_637
msgid ""
"Tax on income obtained by non-residents individuals from financial award "
"granted at contests held in Romania"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_638
msgid "Tax on income obtained from gambling in Romania by non-residents"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_635
msgid ""
"Tax on income obtained in Romania by non-residents from sports and "
"entertainment activities"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_155
msgid "Tax on income of attorneys and public notaries"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_640
msgid ""
"Tax on income representing remunerations received by non-resident legal "
"persons who act as administrator, founder or member of a board of directors "
"of a Romanian legal person"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_624
msgid ""
"Tax on incomes obtained by individuals from an association with a taxpayer "
"legal entity, according to Law no. 170/2016"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_612
msgid ""
"Tax on incomes obtained in Romania by non-residents - individuals, collected"
" up to 31/01/2014"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_625
msgid ""
"Tax on incomes realized by natural persons from independent activity "
"realized on the basis of sports activity contracts."
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_713
msgid "Tax on natural monopoly of the energy and natural gas sectors"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_720
msgid "Tax on nocive activities to human health"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_106
msgid "Tax on profit from natural persons in association"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_930
msgid ""
"Tax on profits earned from illicit commercial activities or from not "
"respecting the laws regarding consumer protection"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_603
msgid "Tax on property and granting property"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_750
msgid "Tax on prospecting, exploration, resource exploitation"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The CUI number for your company (under `Company Registry` in the Company "
"settings) is incorrect."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT number for your company has failed the VIES check."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT number for your company is incorrect."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT numbers for the following partners failed the VIES check:"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_res_company__l10n_ro_saft_tax_accounting_basis
#: model:ir.model.fields,help:l10n_ro_saft.field_res_config_settings__l10n_ro_saft_tax_accounting_basis
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "The accounting regulations and Chart of Accounts used by this company"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.constraint,message:l10n_ro_saft.constraint_l10n_ro_saft_tax_type_code_unique
msgid "The code of the tax type must be unique !"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_472
msgid ""
"The contribution for the social health insurance due by the persons who "
"realize incomes obtained from the association with a legal person, taxpayer,"
" according to Law no. 170/2016 regarding the specific tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_423
msgid "The employer's contribution to the Guarantee Fund for salary payment"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The following products have no `Internal Reference`:"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The following taxes are missing the \"Romanian SAF-T Tax Type\" and/or "
"\"Romanian SAF-T Tax Code\" field(s):"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The follwing products have duplicated `Internal Reference`, please make them"
" unique:"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_471
msgid ""
"The individual contribution for the social health insurance due by the "
"persons with incomes obtained from an association with a taxpayer legal "
"person, according to title II of the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_484
msgid ""
"The insurative contribution for labour, due by employers who carry out the "
"activity in the field of constructions according to art. 60 point 5 of Law "
"no. 227/2015 on the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The intrastat code isn't set on the follwing products:"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_473
msgid ""
"The social insurance contribution due by the natural persons who realize "
"incomes based on the sports activity contracts"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_483
msgid ""
"The social insurance contribution, due by the natural persons who carry out "
"their activity in the field of constructions according to art. 60 point 5 of"
" Law no. 227/2015 on the Fiscal Code"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partner addresses are missing the city:"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partner addresses are missing the country:"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partners have a VAT prefix that differs from their country:"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"These partners have missing or invalid CUI numbers in `Company Registry`. "
"Example of a valid CUI: ********"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"These partners have missing or invalid VAT numbers. Example of a valid VAT: "
"RO********"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_000
msgid ""
"Transactions not relevant to be reported for taxes and import operations"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_609
msgid "Transfer of ownership of securities income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_620
msgid "Transfer of real estates from the personal property income tax"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_307
msgid "VAT to be paid as a result of adjustments"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_300
msgid "Value-added tax (VAT)"
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"While preparing the data for the SAF-T export, we noticed the following "
"missing or incorrect data."
msgstr ""

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "XML"
msgstr ""
