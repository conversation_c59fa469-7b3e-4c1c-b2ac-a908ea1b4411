// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_AttachmentViewer {
    z-index: -1;
}

.o_AttachmentViewer_buttonNavigation {
    width: 40px;
    height: 40px;
}

.o_AttachmentViewer_buttonNavigationNextIcon {
    margin: 1px 0 0 1px; // not correctly centered for some reasons
}

.o_AttachmentViewer_buttonNavigationPreviousIcon {
    margin: 1px 1px 0 0; // not correctly centered for some reasons
}

.o_AttachmentViewer_header {
    height: $o-navbar-height;
}

.o_AttachmentViewer_main {
    z-index: -1;
    padding: ($o-navbar-height * 1.125) 0;
}

.o_AttachmentViewer_name {
    min-width: 0;
}

.o_AttachmentViewer_zoomer {
    padding: ($o-navbar-height * 1.125) 0;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_AttachmentViewer {
    outline: none;
}

.o_AttachmentViewer_headerItemButton:hover {
    background-color: rgba($white, 0.1);
    color: lighten($gray-400, 15%);
}

.o_AttachmentViewer_toolbarButton {
    background-color: var(--AttachmentViewer_toolbarButton-background-color, #{$o-gray-800});
    color: #fff;

    &:hover {
        filter: brightness(1.3);
    }
}

.o_AttachmentViewer_view {
    background-color: #000000;
    box-shadow: 0 0 40px #000000;
    outline: none;

    &.o_AttachmentViewer_isText {
        background: $white;
    }
}
