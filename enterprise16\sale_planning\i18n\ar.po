# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_planning
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        as\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        كـ\n"
"                    </span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Planned</span>"
msgstr "<span class=\"o_stat_text\">مخطط له</span> "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">To plan</span>"
msgstr "<span class=\"o_stat_text\">للتخطيط</span> "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span>Hours</span>"
msgstr "<span>الساعات</span> "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
msgid "<strong>Sales Order Item  — </strong>"
msgstr "<strong>عنصر أمر البيع  — </strong> "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__allocated_hours
msgid "Allocated Time"
msgstr "الوقت المخصص "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Billable"
msgstr "قابل للفوترة "

#. module: sale_planning
#: model:ir.ui.menu,name:sale_planning.sale_planning_menu_schedule_by_sale_order
msgid "By Sales Order"
msgstr "بواسطة أمر البيع "

#. module: sale_planning
#: model:product.template,name:sale_planning.developer_product_product_template
msgid "Developer (Plan services)"
msgstr "مطوِّر (تخطيط الخدمات) "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__end_datetime
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_tester
msgid "Functional Tester"
msgstr "أداه الاختبار الوظيفية "

#. module: sale_planning
#: model:product.template,name:sale_planning.technical_maintainance_product_product_template
msgid "IT Technical Maintenance (Plan services)"
msgstr "الصيانة الفنية لتقنية المعلومات (تخطيط الخدمات) "

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_technician
msgid "IT Technician"
msgstr "فني تقنية المعلومات "

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,help:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_planning.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order. Only employees with this role will "
"automatically be assigned shifts for Sales Orders containing this service."
msgstr ""
"إذا كان مفعلاً، سيتم إنشاء نوبة عمل تلقائياً للدور المحدد عند تأكيد أمر "
"البيع. وحدهم الموظفون الذين يشغلون هذا المنصب سيتم تعيينهم في نوبات العمل "
"تلقائياً لأوامر البيع التي تحتوي على هذه الخدمة. "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "My Sales Orders"
msgstr "أوامر البيع الخاصة بي "

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "No shifts found. Let's create one!"
msgstr "لم يتم العثور على أي نوبات عمل. فلنقم بإنشائها! "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Non Billable"
msgstr "غير قابلة للفوترة "

#. module: sale_planning
#: model:ir.model.constraint,message:sale_planning.constraint_planning_slot_check_datetimes_set_or_plannable_slot
msgid ""
"Only slots linked to a sale order with a plannable service can be "
"unscheduled."
msgstr "وحدها الخانات المرتبطة بخدمة قابلة للتخطيط يمكن إلغاء جدولتها. "

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.planning_action_orders_planned
msgid "Orders Planned"
msgstr "الطلبات المخطط لها "

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.developer_product_product_template
msgid "Our developer will help you to add new features to your Software."
msgstr "سوف يساعدك مطورنا على إضافة خصائص جديدة إلى برنامجك. "

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Plan Orders"
msgstr "تخطيط الأوامر "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_enabled
msgid "Plan Services"
msgstr "تخطيط الخدمات "

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid ""
"Plannable services should be a service product, product\n"
"%s."
msgstr ""
"يجب أن تكون الخدمات القابلة للتخطيط منتج خدمة، منتج \n"
"%s. "

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid "Plannable services should use an UoM within the %s category."
msgstr "يجب أن تستخدم الخدمات القابلة للتخطيط وحدة قياس ضمن فئة %s. "

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "تحليل التخطيط التقرير"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_first_sale_line_id
msgid "Planning First Sale Line"
msgstr "تخطيط أول بند بيع "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_planned
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_planned
msgid "Planning Hours Planned"
msgstr "ساعات التخطيط المخطط لها "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_to_plan
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_to_plan
msgid "Planning Hours To Plan"
msgstr "ساعات التخطيط لتخطيطها "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_initial_date
msgid "Planning Initial Date"
msgstr "تاريخ التخطيط المبدأي "

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_role
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_role_id
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_role_id
msgid "Planning Role"
msgstr "التخطيط للدور "

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_slot
msgid "Planning Shift"
msgstr "التخطيط لنوبة العمل "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_slot_ids
msgid "Planning Slot"
msgstr "خانة التخطيط "

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_product_template
msgid "Product"
msgstr "المنتج"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__role_product_ids
msgid "Role Product"
msgstr "دور المنتج"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_order_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_line_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_planning.period_report_template
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.resource_sale_planning
msgid "Sales Order Item"
msgstr "عنصر أمر المبيعات"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""
"عنصر أمر البيع الذي سيتم إجراء هذه الجلسة من أجله. عندما يتم التخطيط لأوامر "
"البيع تلقائياً، يتم اعتبار الساعات المتبقية من عنصر أمر البيع، بالإضافة إلى "
"الدور المحدد في الخدمة. "

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "Schedule by Sales Order"
msgstr "الجدولة حسب أمر البيع "

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"قم بجدولة مواردك البشرية والمادية عبر الأدوار والمشاريع وأوامر البيع. "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Service"
msgstr "الخدمة"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_role__product_ids
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__role_product_ids
msgid "Services"
msgstr "الخدمات"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/controllers/main.py:0
#, python-format
msgid "Shift"
msgstr "نوبة العمل "

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__start_datetime
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.technical_maintainance_product_product_template
msgid "Take a rest. We'll do our best."
msgstr "استرخِ، سنفعل ما بوسعنا. "

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "The sales orders have successfully been assigned."
msgstr "لقد تم إسناد أوامر البيع بنجاح. "

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "There are no sales orders to assign or no employees are available."
msgstr "لا توجد أوامر بيع لإسنادها أو لا يوجد موظفون متاحون. "

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
#, python-format
msgid ""
"This Sale Order Item doesn't have a target value of planned hours. Planned "
"hours :"
msgstr ""
"ليس لعنصر أمر البيع هذا قيمة مستهدفة للساعات المخطط لها. الساعات المخطط لها:"
" "

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/planning_gantt_controller.js:0
#, python-format
msgid ""
"This resource is not available for this shift during the selected period."
msgstr "هذا المورد غير متاح لنوبة العمل هذه ضمن الفترة المحددة. "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_view_form_in_gantt_inherit_sale_planning
msgid "Unschedule"
msgstr "إلغاء الجدولة "

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
#, python-format
msgid "View Planning"
msgstr "عرض المخطط "

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "View Shifts"
msgstr "عرض نوبات العمل "

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_planner
msgid "Work Planner"
msgstr "أداة تخطيط العمل "

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "e.g. Consultant"
msgstr "مثال: استشاري "

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order_line.py:0
#, python-format
msgid "remaining"
msgstr "متبقي "
