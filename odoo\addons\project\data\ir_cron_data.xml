<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="ir_cron_rating_project" model="ir.cron">
        <field name="name">Project: Send rating</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="state">code</field>
        <field name="code">model._send_rating_all()</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>

    <record id="ir_cron_recurring_tasks" model="ir.cron">
        <field name="name">Project: Create Recurring Tasks</field>
        <field name="model_id" ref="project.model_project_task_recurrence"/>
        <field name="state">code</field>
        <field name="code">model._cron_create_recurring_tasks()</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="nextcall" eval="(DateTime.now().replace(hour=3, minute=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
    </record>
</odoo>
