<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="document_tax_totals_x1" inherit_id="account.document_tax_totals">
        <xpath expr="tr" position="before">
            <tr class="border-black o_total">
                <td><strong>Original Amount</strong></td>
                <td class="text-end">
                    <span t-esc="tax_totals['formatted_original_amount_total']"/>
                </td>
            </tr>
            <tr class="border-black o_total" t-if="tax_totals['total_discount'] > 0">
                <td><strong>Discount</strong></td>
                <td class="text-end">
                    <span t-esc="tax_totals['formatted_discount_amount_total']"/>
                </td>
            </tr>
        </xpath>
    </template>

    <template id="report_saleorder_document_inherit_x1" inherit_id="sale.report_saleorder_document">
        <xpath expr="//th[@name='th_priceunit']" position="after">
            <th name="th_price_unit_after_discount"
                t-if="display_discount"
                class="text-right">سعر الوحدة بعد الخصم</th>
        </xpath>
        <xpath expr="//th[@name='th_description']" position="after">
            <th name="th_barcode"

                class="text-center">Barcode</th>
        </xpath>

        <xpath expr="//th[@name='th_taxes']" position="after">
            <th name="th_original_total_amount" t-if="display_discount" class="text-right">الاجمالي قبل الخصم</th>
        </xpath>
        <xpath expr="//td[@name='td_priceunit']" position="after">
            <td name="td_price_unit_after_discount" t-if="display_discount" class="text-end">
                <span t-field="line.price_unit_after_discount"/>
            </td>
        </xpath>
          <xpath expr="//td[@name='td_name']" position="after">
            <td   class="text-end">
                <span t-field="line.price_unit_after_discount"/>
            </td>
        </xpath>
        <xpath expr="//td[@name='td_taxes']" position="after">
            <td name="td_original_total_amount" t-if="display_discount" class="text-end">
                <span t-field="line.original_total_amount"/>
            </td>
        </xpath>
    </template>

</odoo>