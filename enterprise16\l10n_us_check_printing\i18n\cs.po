# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_us_check_printing
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-28 12:15+0000\n"
"PO-Revision-Date: 2015-10-26 16:26+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Czech (http://www.transifex.com/odoo/odoo-9/language/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Balance Due"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Check Amount:"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_method_code
msgid "Code"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__company_id
msgid "Company"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__company_id
msgid "Company related to this journal"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__create_date
msgid "Created on"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,partner_type:0
msgid "Customer"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Description"
msgstr "Popis"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__destination_account_id
msgid "Destination Account"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__writeoff_account_id
msgid "Difference Account"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__display_name
msgid "Display Name"
msgstr "Zobrazovaný název"

#. module: l10n_us_check_printing
#: selection:account.payment,state:0
msgid "Draft"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Due Date"
msgstr "Splatnost"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__has_invoices
msgid "Has Invoices"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__hide_payment_method
msgid "Hide Payment Method"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__id
msgid "ID"
msgstr "ID"

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,payment_type:0
msgid "Internal Transfer"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Invoice Amount"
msgstr "Fakturovaná částka"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__invoice_ids
msgid "Invoices"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__reconciled_invoice_ids
msgid "Invoices whose journal items have been reconciled with this payment's."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__move_name
msgid "Journal Entry Name"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__writeoff_label
msgid "Journal Item Label"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment____last_update
msgid "Last Modified on"
msgstr "Naposled upraveno"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting "
"a transaction on a card saved by the customer when buying or subscribing "
"online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch "
"deposit to submit to your bank. When encoding the bank statement in Odoo, "
"you are suggested to reconcile the transaction with the batch deposit.To "
"enable batch deposit, module account_batch_payment must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit "
"to your bank. To enable sepa credit transfer, module account_sepa must be "
"installed "
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__communication
msgid "Memo"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__move_line_ids
msgid "Move Line"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__move_reconciled
msgid "Move Reconciled"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__multi
msgid "Multi"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__name
msgid "Name"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__partner_id
msgid "Partner"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__partner_type
msgid "Partner Type"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Payment"
msgstr "Platba"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__amount
msgid "Payment Amount"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_date
msgid "Payment Date"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_difference
msgid "Payment Difference"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_difference_handling
msgid "Payment Difference Handling"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__journal_id
msgid "Payment Journal"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_method_id
msgid "Payment Method Type"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_reference
msgid "Payment Reference"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_transaction_id
msgid "Payment Transaction"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_type
msgid "Payment Type"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model,name:l10n_us_check_printing.model_account_payment
msgid "Payments"
msgstr "Platby"

#. module: l10n_us_check_printing
#: selection:account.payment,state:0
msgid "Posted"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_bottom
msgid "Print Check (Bottom)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_middle
msgid "Print Check (Middle)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_top
msgid "Print Check (Top)"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,payment_type:0
msgid "Receive Money"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,state:0
msgid "Reconciled"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__reconciled_invoice_ids
msgid "Reconciled Invoices"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file "
"name, etc."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__payment_token_id
msgid "Saved payment token"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,payment_type:0
msgid "Send Money"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,state:0
msgid "Sent"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__state
msgid "Status"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__invoice_ids
msgid ""
"Technical field containing the invoices for which the payment has been "
"generated.\n"
"                                                                                                                                                                       This "
"does not especially correspond to the invoices reconciled with the payment,\n"
"                                                                                                                                                                       as "
"it can have been generated first, and reconciled later"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically "
"set when the statement line is reconciled then stored to set the same number "
"again if the line is cancelled, set to draft and re-processed again."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__has_invoices
msgid "Technical field used for usability purposes"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__destination_journal_id
msgid "Transfer To"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_unread
msgid "Unread Messages"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_check
msgid "VOID"
msgstr ""

#. module: l10n_us_check_printing
#: selection:account.payment,partner_type:0
msgid "Vendor"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,help:l10n_us_check_printing.field_account_payment__website_message_ids
msgid "Website communication history"
msgstr ""
