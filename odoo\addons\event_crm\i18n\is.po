# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_crm
# 
# Translators:
# jonasyngvi, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-04 09:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Icelandic (https://app.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_count
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_count
msgid "# Leads"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_count
msgid "# Registrations"
msgstr ""

#. module: event_crm
#: code:addons/event_crm/models/event_registration.py:0
#, python-format
msgid "(updated)"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_view_form
msgid "<span class=\"o_stat_text\"> Attendees</span>"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:event_crm.event_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__active
msgid "Active"
msgstr "Virk"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Archived"
msgstr "Vistuð"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__confirm
msgid "Attendees are confirmed"
msgstr ""

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__create
msgid "Attendees are created"
msgstr ""

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__done
msgid "Attendees attended"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Automatically add these tags to the created leads."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Automatically assign the created leads to this Sales Team."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_user_id
msgid "Automatically assign the created leads to this Salesperson."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__company_id
msgid "Company"
msgstr "Fyrirtæki"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_count
msgid "Counter for the registrations linked to this lead"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_basis
msgid "Create"
msgstr "Búa til"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Create a Lead Generation Rule"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_ids
msgid "Created Leads"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_date
msgid "Created on"
msgstr "Búið til þann"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Creation Type"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_trigger
msgid ""
"Creation: at attendee creation;\n"
"Confirmation: when attendee is confirmed, manually or automatically;\n"
"Attended: when attendance is confirmed and registration set to done;"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_type
msgid "Default lead type when this rule is applied."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__display_name
msgid "Display Name"
msgstr "Birtingarnafn"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_event
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_id
msgid "Event"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_type_ids
msgid "Event Categories"
msgstr ""

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_lead_rule
msgid "Event Lead Rules"
msgstr ""

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_registration
msgid "Event Registration"
msgstr ""

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_registration_action_from_lead
msgid "Event registrations"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_id
msgid "Event triggering the rule that created this lead"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Event:"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_registration_filter
msgid "Filter the attendees that will or not generate leads."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_type_ids
msgid ""
"Filter the attendees to include those of this specific event category. If "
"not set, no event category restriction will be applied."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_id
msgid ""
"Filter the attendees to include those of this specific event. If not set, no"
" event restriction will be applied."
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "For any of these Events"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__id
msgid "ID"
msgstr "Auðkenni (ID)"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "If the Attendees meet these Conditions"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule____last_update
msgid "Last Modified on"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__lead
msgid "Lead"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Creation Type"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Default Values"
msgstr ""

#. module: event_crm
#: model:ir.ui.menu,name:event_crm.event_lead_rule_menu
msgid "Lead Generation"
msgstr ""

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_lead_rule_action
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Generation Rule"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Generation Rules"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_type
msgid "Lead Type"
msgstr ""

#. module: event_crm
#: model:ir.model,name:event_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_event
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_registration
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_ids
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_ids
msgid "Leads"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_event__lead_ids
msgid "Leads generated from this event"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Name"
msgstr "Nafn"

#. module: event_crm
#: code:addons/event_crm/models/event_lead_rule.py:0
#, python-format
msgid "New registrations"
msgstr ""

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_event
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_registration
msgid "No leads found"
msgstr ""

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_registration_action_from_lead
msgid "No registration found"
msgstr ""

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__opportunity
msgid "Opportunity"
msgstr ""

#. module: event_crm
#: code:addons/event_crm/models/event_registration.py:0
#: code:addons/event_crm/models/event_registration.py:0
#, python-format
msgid "Participants"
msgstr ""

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__attendee
msgid "Per Attendee"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_basis
msgid ""
"Per Attendee : A Lead is created for each Attendee (B2C).\n"
"Per Order : A single Lead is created per Ticket Batch/Sale Order (B2B)"
msgstr ""

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__order
msgid "Per Order"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_lead_rule_id
msgid "Registration Rule"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Registration Rule:"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_registration_filter
msgid "Registrations Domain"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_ids
msgid "Registrations triggering the rule that created this lead"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__company_id
msgid ""
"Restrict the trigger of this rule to events belonging to a specific company.\n"
"If not set, no company restriction will be applied."
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__name
msgid "Rule Name"
msgstr ""

#. module: event_crm
#: model:event.lead.rule,name:event_crm.event_lead_rule_0
msgid "Rule on @example.com"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_lead_rule_id
msgid "Rule that created this lead"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Sales Team"
msgstr "Söluteymi"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_user_id
msgid "Salesperson"
msgstr "Sölufulltrúa"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Search Lead Generation Rules"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_id
msgid "Source Event"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_ids
msgid "Source Registrations"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Tags"
msgstr "Merki"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Those automatically create leads when attendees register."
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Trigger Type"
msgstr ""

#. module: event_crm
#: code:addons/event_crm/models/event_registration.py:0
#: code:addons/event_crm/models/event_registration.py:0
#, python-format
msgid "Updated registrations"
msgstr ""

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_trigger
msgid "When"
msgstr ""

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "e.g. B2B Fairs"
msgstr ""
