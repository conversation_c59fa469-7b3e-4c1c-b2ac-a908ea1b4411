<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_order_form_inherit_sale_planning" model="ir.ui.view">
        <field name="name">sale.order.form.sale.planning</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_invoice']" position="before">
                <field name="id" invisible="1"/>
                <field name="planning_first_sale_line_id" invisible="1"/>
                <field name="planning_initial_date" invisible="1"/>
                <button name="action_view_planning" type="object" icon="fa-tasks"
                    attrs="{'invisible': ['|', ('planning_hours_to_plan', '=', 0), ('state', 'in', ['draft', 'sent', 'cancel', 'done'])]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="planning_hours_to_plan" class="mr4" widget="statinfo" nolabel="1" options="{'digits': [false, 0]}"/>
                            <span>Hours</span>
                        </span>
                        <span class="o_stat_text">To plan</span>
                    </div>
                </button>
                <button name="%(planning.planning_action_schedule_by_resource)d"
                    type="action"
                    context="{'search_default_sale_order_id': id,
                              'default_sale_line_id': planning_first_sale_line_id,
                              'search_default_group_by_role': 1,
                              'search_default_group_by_resource': 2,
                              'initialDate': planning_initial_date,
                              'planning_gantt_active_sale_order_id': id}"
                    class="oe_stat_button"
                    icon="fa-tasks"
                    attrs="{'invisible': ['|', ('planning_hours_planned', '=', 0), ('state', 'in', ['draft', 'sent'])]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="planning_hours_planned" class="mr4" widget="statinfo" nolabel="1" options="{'digits': [false, 0]}"/>
                            <span>Hours</span>
                        </span>
                        <span class="o_stat_text">Planned</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

</odoo>
