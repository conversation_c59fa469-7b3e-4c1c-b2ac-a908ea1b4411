<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_invoice_filter" model="ir.ui.view">
        <field name="name">account.invoice.select.account_check_printing</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search/group/filter[@name='status']" position="after">
                <filter name="preferred_payment_method" context="{'group_by': 'preferred_payment_method_id'}" groups="account.group_account_invoice,account.group_account_readonly"/>
            </xpath>
        </field>
    </record>
</odoo>
