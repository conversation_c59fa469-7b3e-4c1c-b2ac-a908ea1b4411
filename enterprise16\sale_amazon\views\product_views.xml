<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="amazon_product_template_view_form" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="action_view_offers"
                        class="oe_stat_button"
                        icon="fa-amazon"
                        type="object"
                        groups="sales_team.group_sale_manager"
                        attrs="{'invisible': [('offer_count', '=', 0)]}">
                    <field name="offer_count" widget="statinfo" string="Offers"/>
                </button>
            </div>
        </field>
    </record>

    <record id="amazon_product_product_view_form" model="ir.ui.view">
        <field name="name">product.product.form.inherit</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="action_view_offers"
                        class="oe_stat_button"
                        icon="fa-amazon"
                        type="object"
                        groups="sales_team.group_sale_manager"
                        attrs="{'invisible': [('offer_count', '=', 0)]}">
                    <field name="offer_count" widget="statinfo" string="Offers"/>
                </button>
            </div>
        </field>
    </record>

</odoo>
