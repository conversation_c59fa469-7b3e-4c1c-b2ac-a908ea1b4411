# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_th_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-12 07:23+0000\n"
"PO-Revision-Date: 2023-12-12 07:23+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_th_reports
#: model:ir.model,name:l10n_th_reports.model_l10n_th_pnd_report_handler
msgid "Abstract Tax Report PND Handler"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Branch Number"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "CSV"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "City"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Company Information"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Contact Name"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "From %s to %s"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Invoice Date"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Invoice/Bill Date"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "No."
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "PND3"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "PND53"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Purchase Tax Report"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Purchase Tax Report (xlsx)"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Reference"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Sales Tax Report"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Sales Tax Report (xlsx)"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "State"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Street"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Street2"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Tax ID"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Tax Invoice No."
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Tax Rate"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Tax Report"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Tax Type"
msgstr ""

#. module: l10n_th_reports
#: model:ir.model,name:l10n_th_reports.model_l10n_th_pnd3_report_handler
msgid "Thai Tax Report (PND3) Custom Handler"
msgstr ""

#. module: l10n_th_reports
#: model:ir.model,name:l10n_th_reports.model_l10n_th_pnd53_report_handler
msgid "Thai Tax Report (PND53) Custom Handler"
msgstr ""

#. module: l10n_th_reports
#: model:ir.model,name:l10n_th_reports.model_l10n_th_tax_report_handler
msgid "Thai Tax Report Custom Handler"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Title"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Total Amount"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Total Excluding VAT Amount"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_vat.py:0
#, python-format
msgid "Vat Amount"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "WHT Amount"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "WHT Condition"
msgstr ""

#. module: l10n_th_reports
#. odoo-python
#: code:addons/l10n_th_reports/models/tax_report_pnd.py:0
#, python-format
msgid "Zip"
msgstr ""
