# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:41+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid "%(project_name)s's Documents"
msgstr "%(project_name)s's documenten"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Een set van condities en acties welke beschikbaar is voor alle bijlagen die "
"aan deze condities voldoen"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_ask_for_validation
msgid "Ask for Validation"
msgstr "Vragen om bevestiging"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Bijlage"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Categoriseer en deel je documenten met je klanten"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_facet
msgid "Category"
msgstr "Categorie"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Aanmaken"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Taak aanmaken"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Standaard labels"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_deprecate
msgid "Deprecate"
msgstr "Afkeuren"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_deprecated
msgid "Deprecated"
msgstr "Vervallen"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
#: model:ir.model.fields,field_description:documents_project.field_ir_attachment__document_ids
msgid "Document"
msgstr "Document"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
#, python-format
msgid "Documents"
msgstr "Documenten"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Documenten &amp; Analytics"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_folder
msgid "Documents Workspace"
msgstr "Documenten werkruimte"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_draft
msgid "Draft"
msgstr "Concept"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__is_shared
msgid "Is Shared"
msgstr "Is gedeeld"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_mark_as_draft
msgid "Mark As Draft"
msgstr "Markeer als concept"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "Merged Workspace"
msgstr "Samengevoegde werkruimte"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "New task from Documents"
msgstr "Nieuwe taak van documenten"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__document_count
msgid "Number of documents in Project"
msgstr "Aantal documenten in project"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_count
msgid "Number of documents in Task"
msgstr "Aantal documenten in taak"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_documents_folder__project_ids
msgid "Project"
msgstr "Project"

#. module: documents_project
#: model:documents.folder,name:documents_project.documents_project_folder
msgid "Projects"
msgstr "Projecten"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_ids
#: model:ir.model.fields,field_description:documents_project.field_project_task__shared_document_ids
msgid "Shared Documents"
msgstr "Gedeelde documenten"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_count
msgid "Shared Documents Count"
msgstr "Aantal gedeelde documenten"

#. module: documents_project
#: model:documents.facet,name:documents_project.documents_project_status
msgid "Status"
msgstr "Status"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Taak"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "Task created from document"
msgstr "Taak gemaakt op basis van document"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr ""
"De werkruimte \"%s\" is vereist voor de projecttoepassing en kan niet worden"
" verwijderd."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"The \"%s\" workspace should either be in the \"%s\" company like this "
"project or be open to all companies."
msgstr ""
"De werkruimte \"%s\" moet zich in het bedrijf \"%s\" bevinden zoals dit "
"project, of moet openstaan voor alle bedrijven."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the \"%s\" project to "
"which it is linked. Please update the company of the \"%s\" project, or "
"leave the company of this workspace empty."
msgstr ""
"Deze werkruimte moet in hetzelfde bedrijf blijven als het \"%s\"-project "
"waaraan deze is gekoppeld. Werk het bedrijf van het \"%s\"-project bij, of "
"laat het bedrijf van deze werkruimte leeg."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Deze werkruimte moet in hetzelfde bedrijf blijven als de volgende projecten waaraan deze is gekoppeld:\n"
"%s\n"
"\n"
"Werk het bedrijf van die projecten bij, of laat het bedrijf van deze werkruimte leeg."

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_to_validate
msgid "To Validate"
msgstr "Goedkeuren"

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Upload <span class=\"fw-normal\">een bestand of </span>sleep <span "
"class=\"fw-normal\">het hier.</span>"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__project_use_documents
msgid "Use Documents"
msgstr "Documenten gebruiken"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_validate
msgid "Validate"
msgstr "Bevestigen"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_validated
msgid "Validated"
msgstr "Goedgekeurd"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,field_description:documents_project.field_project_task__documents_folder_id
msgid "Workspace"
msgstr "Werkruimte"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Workspace in which all of the documents of this project will be categorized."
" All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Werkruimte waarin alle documenten van dit project worden gecategoriseerd. "
"Alle bijlagen van je taken worden ook automatisch toegevoegd als documenten "
"in deze werkruimte."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%s\" company:\n"
"%s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%s\" workspace blank."
msgstr ""
"Je kunt het bedrijf van dit project niet wijzigen, omdat de werkruimte is gekoppeld aan de andere volgende projecten die zich nog in het bedrijf \"%s\" bevinden:\n"
"%s\n"
"\n"
"Werk het bedrijf van alle projecten bij zodat ze in hetzelfde bedrijf blijven als hun werkruimte, of laat het bedrijf van de \"%s\" werkruimte leeg."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "You cannot set a company on the Projects workspace."
msgstr "Je kunt geen bedrijf instellen in de werkruimte Projecten."

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.share_page
msgid "documents shared"
msgstr "documenten gedeeld"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "new %s from %s"
msgstr "nieuw %s van %s"
