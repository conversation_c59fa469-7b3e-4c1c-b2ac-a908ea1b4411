# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _, tools
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class BioTimePunch(models.Model):
    _name = 'biotime.punch'
    _description = 'Biotime Raw Punch'
    _order = 'punch_datetime DESC'

    name = fields.Char(string="Reference", compute='_compute_name', store=True)
    employee_id = fields.Many2one('hr.employee', string="Employee")
    biotime_employee_id = fields.Many2one('biotime.employee', string="Biotime Employee")
    biotime_terminal_id = fields.Many2one('biotime.terminal', string="Terminal")
    
    emp_code = fields.Char(string="Employee Code")
    terminal_sn = fields.Char(string="Terminal SN")
    punch_time = fields.Char(string="Original Punch Time")
    punch_datetime = fields.Datetime(string="Punch Datetime")
    punch_state = fields.Selection([
        ('0', 'Check In'),
        ('1', 'Check Out'),
        ('2', 'Break Out'),
        ('3', 'Break In'),
        ('4', 'Overtime In'),
        ('5', 'Overtime Out'),
        ('unknown', 'Unknown')
    ], string="Punch State", default='unknown')
    
    verify_type = fields.Char(string="Verify Type")
    work_code = fields.Char(string="Work Code")
    
    attendance_id = fields.Many2one('hr.attendance', string="Attendance Record")
    processed = fields.Boolean(string="Processed", default=False, compute="_compute_processed", store=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('processed', 'Processed'),
        ('error', 'Error'),
        ('manual', 'Manually Processed')
    ], string="Status", default='draft')
    
    notes = fields.Text(string="Notes")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id)
    
    # For statistics
    needs_attention = fields.Boolean(string="Needs Attention", compute="_compute_needs_attention", store=True)
    
    @api.depends('state', 'punch_state', 'processed')
    def _compute_needs_attention(self):
        for record in self:
            record.needs_attention = (
                record.state in ['error', 'draft'] or 
                (record.punch_state == 'unknown' and not record.processed)
            )
    
    @api.depends('attendance_id', 'state')
    def _compute_processed(self):
        for record in self:
            # Only mark as processed if there's an actual attendance record
            # or it was manually marked as processed
            if record.attendance_id or record.state == 'manual':
                record.processed = True
            else:
                record.processed = False
    
    @api.depends('biotime_employee_id', 'punch_datetime')
    def _compute_name(self):
        for record in self:
            datetime_str = record.punch_datetime.strftime('%Y-%m-%d %H:%M:%S') if record.punch_datetime else ''
            emp_name = record.biotime_employee_id.name or record.emp_code or 'Unknown'
            record.name = f"{emp_name} - {datetime_str}"
    
    def action_mark_check_in(self):
        self.ensure_one()
        self.punch_state = '0'  # Check In
        result = self._process_punch()
        if result:
            self.state = 'manual'
        return True
        
    def action_mark_check_out(self):
        self.ensure_one()
        self.punch_state = '1'  # Check Out
        result = self._process_punch()
        if result:
            self.state = 'manual'
        return True
    
    def _process_punch(self):
        """Process this punch to create/update attendance"""
        self.ensure_one()
        if not self.employee_id:
            self.state = 'error'
            self.notes = "No employee assigned"
            return False
            
        if self.punch_state == '0':  # Check In
            # Look for existing open attendance
            existing_attendance = self.env['hr.attendance'].sudo().search([
                ('employee_id', '=', self.employee_id.id),
                ('check_in', '!=', False),
                ('check_out', '=', False)
            ], limit=1)
            
            if existing_attendance:
                # Already has an open attendance - log this unusual case
                self.state = 'error'
                self.notes = f"Employee already has an open attendance record (ID: {existing_attendance.id})"
                return False
            else:
                # Create new attendance with check-in
                attendance = self.env['hr.attendance'].sudo().create({
                    'employee_id': self.employee_id.id,
                    'check_in': self.punch_datetime,
                })
                self.attendance_id = attendance.id
                self.state = 'processed'
                return True
                
        elif self.punch_state == '1':  # Check Out
            # Look for an open attendance to close
            open_attendance = self.env['hr.attendance'].sudo().search([
                ('employee_id', '=', self.employee_id.id),
                ('check_in', '!=', False),
                ('check_out', '=', False)
            ], limit=1, order='check_in ASC')
            
            if open_attendance:
                # Close the attendance
                open_attendance.check_out = self.punch_datetime
                self.attendance_id = open_attendance.id
                self.state = 'processed'
                return True
            else:
                # No open attendance found - check if this is a night shift checkout
                # Get the biotime config
                biotime_config = False
                if self.biotime_terminal_id and self.biotime_terminal_id.biotime_id:
                    biotime_config = self.biotime_terminal_id.biotime_id
                
                if biotime_config and biotime_config.handle_night_shifts:
                    # Check for a recent check-in from the previous day
                    max_hours = biotime_config.night_shift_hours
                    max_days = biotime_config.night_shift_span_days
                    
                    # Calculate the earliest valid check-in time for a night shift
                    earliest_valid_time = self.punch_datetime - timedelta(hours=max_hours, days=max_days)
                    
                    # Look for check-ins in the valid time range
                    night_shift_attendance = self.env['hr.attendance'].sudo().search([
                        ('employee_id', '=', self.employee_id.id),
                        ('check_in', '>=', earliest_valid_time),
                        ('check_in', '<=', self.punch_datetime),
                        ('check_out', '=', False)
                    ], limit=1, order='check_in DESC')
                    
                    if night_shift_attendance:
                        # This is likely a night shift checkout
                        night_shift_attendance.check_out = self.punch_datetime
                        self.attendance_id = night_shift_attendance.id
                        self.state = 'processed'
                        
                        # Log this as a night shift detection
                        duration_hours = (self.punch_datetime - night_shift_attendance.check_in).total_seconds() / 3600
                        self.message_post(
                            body=_("Detected as night shift checkout. Shift duration: %.2f hours.") % duration_hours
                        )
                        return True
                
                # If we reach here, it's a genuine error - no open attendance found
                self.state = 'error'
                self.notes = "No open attendance record found to check out"
                return False
        else:
            # Not a check-in or check-out punch - can't process
            self.state = 'error'
            self.notes = f"Cannot process punch with state '{self.punch_state}'"
            return False
        
        return False

    # Batch processing methods
    def action_auto_assign_chronological(self):
        """Auto-assign punches based on chronology for a single employee"""
        # Get all unprocessed punches for this employee in order
        employee = self.employee_id
        if not employee:
            return False

        # Get all unprocessed punches for this employee
        all_punches = self.search([
            ('employee_id', '=', employee.id),
            ('processed', '=', False),
            ('state', 'in', ['draft', 'error']),
        ], order='punch_datetime ASC')

        if not all_punches:
            return False

        # Find the last state of the employee
        last_attendance = self.env['hr.attendance'].search([
            ('employee_id', '=', employee.id),
        ], limit=1, order='check_in DESC')

        # Determine the next expected punch type
        next_punch_state = '0'  # Default to check-in
        if last_attendance and not last_attendance.check_out:
            next_punch_state = '1'  # If last attendance is open, expect check-out
        
        for punch in all_punches:
            punch.punch_state = next_punch_state
            result = punch._process_punch()
            if result:
                # Toggle the expected next state
                next_punch_state = '0' if next_punch_state == '1' else '1'

        return True

    def _reprocess_unprocessed_punches(self):
        """Attempt to reprocess any punches that are not processed but have valid state"""
        unprocessed = self.search([
            ('state', 'in', ['draft', 'error']),
            ('punch_state', 'in', ['0', '1']),  # Only check-in and check-out
            ('employee_id', '!=', False),
        ])
        
        for punch in unprocessed:
            punch._process_punch()
        
        return True

    @api.model
    def action_auto_process_unknown_punches(self):
        """Auto-process unknown state punches in batch using configuration rules"""
        # First try to reprocess any valid but unprocessed punches
        self._reprocess_unprocessed_punches()
        
        # Get all biotime configurations to check their rules
        biotime_configs = self.env['biotime.config'].search([('active', '=', True)])
        
        # Process by employee to maintain proper order
        employees = self.env['hr.employee'].search([])
        
        for employee in employees:
            # Get unprocessed punches with unknown state for this employee
            unprocessed = self.search([
                ('employee_id', '=', employee.id),
                ('state', 'in', ['draft', 'error']),
            ], order='punch_datetime')
            
            if not unprocessed:
                continue
                
            # Get all check-ins without check-outs
            open_checkins = self.search([
                ('employee_id', '=', employee.id),
                ('punch_state', '=', '0'),  # Check-in
                ('processed', '=', True),
                ('attendance_id.check_out', '=', False)
            ], order='punch_datetime')
            
            # If we have open check-ins, look for potential check-outs among unprocessed punches
            for checkin in open_checkins:
                attendance = checkin.attendance_id
                if not attendance or attendance.check_out:
                    continue
                    
                # Look for unprocessed punches after this check-in that could be check-outs
                potential_checkouts = unprocessed.filtered(
                    lambda p: p.punch_datetime > checkin.punch_datetime and p.punch_state == 'unknown'
                )
                
                if potential_checkouts:
                    # Use the first one as check-out
                    checkout = potential_checkouts[0]
                    checkout.punch_state = '1'  # Mark as check-out
                    checkout._process_punch()
                    
                    # Remove from unprocessed list
                    unprocessed = unprocessed - checkout
                    
                else:
                    # No potential check-out found, check the configuration rules
                    # Get the biotime config for this employee (via terminal of the check-in)
                    biotime_config = checkin.biotime_terminal_id.biotime_id if checkin.biotime_terminal_id else False
                    
                    # If no config found or rule is to ignore, continue
                    if not biotime_config or biotime_config.missing_checkout_handling == 'ignore':
                        continue
                        
                    # Apply the missing check-out rule
                    if biotime_config.missing_checkout_handling == 'auto_close':
                        # Get end of day based on check-in date
                        checkout_time = self._get_employee_work_end_time(
                            employee, 
                            checkin.punch_datetime, 
                            biotime_config
                        )
                        # Update the attendance
                        attendance.write({'check_out': checkout_time})
                        # Create a note about this automatic action
                        checkin.message_post(
                            body=_("Automatically added check-out at end of shift (%s) due to missing check-out punch.") 
                                % checkout_time.strftime('%Y-%m-%d %H:%M:%S')
                        )
                    
                    elif biotime_config.missing_checkout_handling == 'next_checkin':
                        # Look for the next check-in for this employee
                        next_checkin = self.search([
                            ('employee_id', '=', employee.id),
                            ('punch_state', '=', '0'),  # Check-in
                            ('punch_datetime', '>', checkin.punch_datetime)
                        ], order='punch_datetime', limit=1)
                        
                        if next_checkin:
                            # Use the next check-in time as check-out time
                            attendance.write({'check_out': next_checkin.punch_datetime})
                            # Create a note about this automatic action
                            checkin.message_post(
                                body=_("Automatically added check-out at next check-in time (%s) due to missing check-out punch.") 
                                    % next_checkin.punch_datetime.strftime('%Y-%m-%d %H:%M:%S')
                            )
                    
                    elif biotime_config.missing_checkout_handling == 'default_hours':
                        # Calculate work hours based on employee contract or default settings
                        hours_duration = self._get_work_duration(employee, checkin.punch_datetime, biotime_config)
                        checkout_time = checkin.punch_datetime + timedelta(hours=hours_duration)
                        # Update the attendance
                        attendance.write({'check_out': checkout_time})
                        # Create a note about this automatic action
                        checkin.message_post(
                            body=_("Automatically added check-out after calculated work duration (%s hours) due to missing check-out punch.") 
                                % hours_duration
                        )
            
            # Process remaining unprocessed punches
            # Group by day for better organization
            punches_by_day = {}
            for punch in unprocessed:
                day_key = punch.punch_datetime.strftime('%Y-%m-%d')
                if day_key not in punches_by_day:
                    punches_by_day[day_key] = []
                punches_by_day[day_key].append(punch)
            
            # Get the Biotime config for this employee
            first_punch = next(iter(unprocessed), None)
            biotime_config = first_punch.biotime_terminal_id.biotime_id if first_punch and first_punch.biotime_terminal_id else False
            
            # Handle night shifts if enabled
            if biotime_config and biotime_config.handle_night_shifts:
                # Special processing for night shifts - combine consecutive days if needed
                sorted_days = sorted(punches_by_day.keys())
                
                for i in range(len(sorted_days) - 1):
                    current_day = sorted_days[i]
                    next_day = sorted_days[i+1]
                    
                    # Check if these are consecutive days
                    current_date = datetime.strptime(current_day, '%Y-%m-%d').date()
                    next_date = datetime.strptime(next_day, '%Y-%m-%d').date()
                    
                    if (next_date - current_date).days <= biotime_config.night_shift_span_days:
                        # Get the last punch of current day and first punch of next day
                        current_day_punches = sorted(punches_by_day[current_day], key=lambda p: p.punch_datetime)
                        next_day_punches = sorted(punches_by_day[next_day], key=lambda p: p.punch_datetime)
                        
                        if current_day_punches and next_day_punches:
                            last_current_punch = current_day_punches[-1]
                            first_next_punch = next_day_punches[0]
                            
                            # Calculate time between the punches
                            time_diff = (first_next_punch.punch_datetime - last_current_punch.punch_datetime).total_seconds() / 3600
                            
                            # If within night shift duration, mark them as check-in/check-out
                            if time_diff <= biotime_config.night_shift_hours:
                                # Mark the last punch of current day as check-in
                                last_current_punch.punch_state = '0'
                                last_current_punch._process_punch()
                                
                                # Mark the first punch of next day as check-out
                                first_next_punch.punch_state = '1'
                                first_next_punch._process_punch()
                                
                                # Log this as a night shift detection
                                first_next_punch.message_post(
                                    body=_("Automatically paired with previous day punch as part of night shift. Duration: %.2f hours.") % time_diff
                                )
                                
                                # Remove these punches from processing lists
                                punches_by_day[current_day].remove(last_current_punch)
                                punches_by_day[next_day].remove(first_next_punch)
            
            # Process each day's punches
            for day, day_punches in punches_by_day.items():
                if not day_punches:
                    continue
                    
                # Sort by datetime
                sorted_punches = sorted(day_punches, key=lambda p: p.punch_datetime)
                
                # Get the Biotime config for this employee (via terminal of the first punch)
                first_punch = sorted_punches[0] if sorted_punches else False
                biotime_config = first_punch.biotime_terminal_id.biotime_id if first_punch and first_punch.biotime_terminal_id else False
                
                if not biotime_config:
                    # No config found, use default behavior (alternate in/out)
                    self._process_punches_default(sorted_punches)
                    continue
                
                # Check if the first punch should be a check-in or might need one created before it
                first_punch = sorted_punches[0] if sorted_punches else False
                if first_punch and first_punch.punch_state == 'unknown':
                    # Look for previous attendance records for this employee on this day
                    day_start = datetime.strptime(day + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
                    day_end = datetime.strptime(day + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    
                    prev_attendance = self.env['hr.attendance'].search([
                        ('employee_id', '=', employee.id),
                        ('check_in', '>=', day_start),
                        ('check_in', '<=', day_end)
                    ], limit=1)
                    
                    if not prev_attendance:
                        # No attendance found, this should likely be a check-in
                        # Check the configuration for missing check-ins
                        if biotime_config.missing_checkin_handling == 'auto_open':
                            # Set this punch as check-in, using its actual time
                            first_punch.punch_state = '0'
                            first_punch._process_punch()
                            first_punch.message_post(
                                body=_("Automatically set as check-in based on configuration rules.")
                            )
                        elif biotime_config.missing_checkin_handling == 'start_of_day':
                            # Get start of day based on config
                            default_start = day_start.replace(
                                hour=int(biotime_config.default_work_hours_from),
                                minute=int((biotime_config.default_work_hours_from % 1) * 60)
                            )
                            
                            # Create a new attendance with default start time
                            attendance = self.env['hr.attendance'].create({
                                'employee_id': employee.id,
                                'check_in': default_start,
                            })
                            
                            # Then set this punch as check-out
                            first_punch.punch_state = '1'
                            first_punch.attendance_id = attendance.id
                            first_punch._process_punch()
                            
                            # Create a note about this automatic action
                            first_punch.message_post(
                                body=_("Created artificial check-in at start of day (%s) and marked this punch as check-out.") 
                                    % default_start.strftime('%Y-%m-%d %H:%M:%S')
                            )
                
                # After handling the first punch specially, process the rest with default logic
                # but skip the first one if we already processed it
                remaining_punches = sorted_punches[1:] if first_punch and first_punch.state == 'processed' else sorted_punches
                if remaining_punches:
                    self._process_punches_default(remaining_punches)
        
        return True
        
    def _process_punches_default(self, sorted_punches):
        """Process punches with default in/out alternating behavior"""
        # If even number of punches, pair them as in/out
        if len(sorted_punches) % 2 == 0:
            for i in range(0, len(sorted_punches), 2):
                if i < len(sorted_punches):
                    sorted_punches[i].punch_state = '0'  # Check In
                    sorted_punches[i]._process_punch()
                    
                if i+1 < len(sorted_punches):
                    sorted_punches[i+1].punch_state = '1'  # Check Out
                    sorted_punches[i+1]._process_punch()
        # If odd number, leave the last one unprocessed
        else:
            for i in range(0, len(sorted_punches)-1, 2):
                if i < len(sorted_punches):
                    sorted_punches[i].punch_state = '0'  # Check In
                    sorted_punches[i]._process_punch()
                    
                if i+1 < len(sorted_punches):
                    sorted_punches[i+1].punch_state = '1'  # Check Out
                    sorted_punches[i+1]._process_punch()

    @api.model
    def get_problematic_punch_stats(self):
        """Get statistics on problematic punches for notification"""
        # Get counts of problematic punches
        today = fields.Date.today()
        yesterday = today - timedelta(days=1)
        
        domain = ['|', '|', 
            ('state', '=', 'error'), 
            ('state', '=', 'draft'), 
            '&', ('punch_state', '=', 'unknown'), ('processed', '=', False)
        ]
        
        # Today's issues
        today_domain = domain + [
            ('punch_datetime', '>=', fields.Datetime.to_string(datetime.combine(today, datetime.min.time()))),
            ('punch_datetime', '<', fields.Datetime.to_string(datetime.combine(today + timedelta(days=1), datetime.min.time())))
        ]
        today_count = self.search_count(today_domain)
        
        # Yesterday's issues
        yesterday_domain = domain + [
            ('punch_datetime', '>=', fields.Datetime.to_string(datetime.combine(yesterday, datetime.min.time()))),
            ('punch_datetime', '<', fields.Datetime.to_string(datetime.combine(today, datetime.min.time())))
        ]
        yesterday_count = self.search_count(yesterday_domain)
        
        # Group by employee to find top problematic employees
        employee_stats = {}
        problematic_punches = self.search(domain + [
            ('punch_datetime', '>=', fields.Datetime.to_string(datetime.combine(today - timedelta(days=7), datetime.min.time())))
        ])
        
        for punch in problematic_punches:
            emp_name = punch.employee_id.name or punch.biotime_employee_id.name or punch.emp_code or 'Unknown'
            if emp_name not in employee_stats:
                employee_stats[emp_name] = 0
            employee_stats[emp_name] += 1
        
        # Sort by count
        sorted_employees = sorted(employee_stats.items(), key=lambda x: x[1], reverse=True)
        top_employees = sorted_employees[:5] if sorted_employees else []
        
        return {
            'today_count': today_count,
            'yesterday_count': yesterday_count,
            'total_count': self.search_count(domain),
            'top_employees': top_employees
        }
    
    @api.model
    def action_notify_administrators(self):
        """Notify administrators about problematic punches"""
        stats = self.get_problematic_punch_stats()
        
        if not stats['today_count'] and not stats['yesterday_count']:
            return  # No issues to report
            
        # Get administrators to notify
        admin_users = self.env.ref('hr.group_hr_manager').users
        
        if not admin_users:
            return
            
        # Prepare message
        message = f"""
<p>Biotime Attendance Issues Summary:</p>
<ul>
    <li><strong>Today:</strong> {stats['today_count']} issues</li>
    <li><strong>Yesterday:</strong> {stats['yesterday_count']} issues</li>
    <li><strong>Total open issues:</strong> {stats['total_count']} issues</li>
</ul>
"""
        
        if stats['top_employees']:
            message += "<p><strong>Top employees with issues:</strong></p><ul>"
            for employee, count in stats['top_employees']:
                message += f"<li>{employee}: {count} issues</li>"
            message += "</ul>"
            
        message += "<p>Please review and fix these issues in the Attendance Issues menu.</p>"
        
        # Send notification
        self.env['mail.message'].create({
            'subject': f"Biotime Attendance Issues: {stats['today_count']} new issues today",
            'body': message,
            'model': 'biotime.punch',
            'res_id': 0,  # Generic record
            'message_type': 'notification',
            'partner_ids': [(4, user.partner_id.id) for user in admin_users],
            'subtype_id': self.env.ref('mail.mt_comment').id,
        })

    def _get_employee_work_end_time(self, employee, reference_datetime, biotime_config):
        """
        Get the end time of employee's work schedule for a given date
        Falls back to default hours if no contract schedule found
        """
        end_time = None

        # Try to get the end time from employee contract if enabled
        if biotime_config.use_employee_contracts:
            # Get the employee's active contract
            contract = self.env['hr.contract'].sudo().search([
                ('employee_id', '=', employee.id),
                ('state', '=', 'open'),
                ('date_start', '<=', reference_datetime.date()),
                '|',
                ('date_end', '=', False),
                ('date_end', '>=', reference_datetime.date())
            ], limit=1)
            
            # If contract found, try to get the work schedule
            if contract and contract.resource_calendar_id:
                # Get the day of week (0 = Monday, 6 = Sunday)
                dow = reference_datetime.weekday()
                
                # Find work hours for this day
                attendances = contract.resource_calendar_id.attendance_ids.filtered(
                    lambda a: int(a.dayofweek) == dow
                )
                
                if attendances:
                    # Get the latest end time for this day
                    latest_attendance = max(attendances, key=lambda a: a.hour_to)
                    hour = int(latest_attendance.hour_to)
                    minute = int((latest_attendance.hour_to % 1) * 60)
                    
                    # Create the end time
                    end_time = reference_datetime.replace(
                        hour=hour, 
                        minute=minute, 
                        second=0
                    )
                    
                    # If we're past the end time, use the default
                    if reference_datetime > end_time:
                        end_time = None
        
        # Fall back to default settings if no contract or end time found
        if not end_time:
            end_time = reference_datetime.replace(
                hour=int(biotime_config.default_work_hours_to),
                minute=int((biotime_config.default_work_hours_to % 1) * 60),
                second=0
            )
            
        return end_time
        
    def _get_work_duration(self, employee, reference_datetime, biotime_config):
        """
        Calculate work duration for an employee based on their contract or defaults
        Returns duration in hours
        """
        # Default duration from config
        default_duration = biotime_config.default_work_hours_to - biotime_config.default_work_hours_from
        
        # Try to get duration from employee contract if enabled
        if biotime_config.use_employee_contracts:
            # Get the employee's active contract
            contract = self.env['hr.contract'].sudo().search([
                ('employee_id', '=', employee.id),
                ('state', '=', 'open'),
                ('date_start', '<=', reference_datetime.date()),
                '|',
                ('date_end', '=', False),
                ('date_end', '>=', reference_datetime.date())
            ], limit=1)
            
            # If contract found, try to get the work schedule
            if contract and contract.resource_calendar_id:
                # Get the day of week (0 = Monday, 6 = Sunday)
                dow = reference_datetime.weekday()
                
                # Find work hours for this day
                attendances = contract.resource_calendar_id.attendance_ids.filtered(
                    lambda a: int(a.dayofweek) == dow
                )
                
                if attendances:
                    # Calculate total hours for this day
                    total_hours = sum(a.hour_to - a.hour_from for a in attendances)
                    return total_hours
        
        # Fall back to default duration if no contract found
        return default_duration 