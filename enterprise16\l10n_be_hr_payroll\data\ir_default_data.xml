<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Default advantage values -->
    <function model="ir.default" name="set" eval="('hr.contract', 'commission_on_target', 0.0)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'fuel_card', 150.0)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'representation_fees', 150.0)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'internet', 38.0)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'mobile', 30.0)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'meal_voucher_amount', 7.45)"/>
    <function model="ir.default" name="set" eval="('hr.contract', 'eco_checks', 250.0)"/>
    </data>
</odoo>
