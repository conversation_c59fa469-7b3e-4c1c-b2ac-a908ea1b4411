# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account_followup
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"The letter will be sent using the IAP service from Odoo.&#10;Make sure you have enough credits on your account or proceed to a recharge.\"/>\n"
"                        <br/>"
msgstr ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Avviso\" title=\"La lettera verrà inviata attraverso il servizio IAP di Odoo.&amp;#10;Controllare di avere credito sufficiente sul conto o effettuare una ricarica.\"/>\n"
"                        <br/>"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "By post"
msgstr "Per posta ordinaria"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Criterio sollecito"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Resoconto sollecito"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Invia una lettera"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "Sending this document by post will cost"
msgstr "La spedizione del documento via posta ordinaria costerà"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail
msgid "Snailmail"
msgstr "Posta ordinaria"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail_cost
msgid "Stamps"
msgstr "Francobolli"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Procedura guidata per l'inoltro manuale di promemoria ai clienti"

#. module: snailmail_account_followup
#. odoo-python
#: code:addons/snailmail_account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a letter by post, but no follow-up contact has any "
"address set"
msgstr ""
"Stai cercando di inviare una lettera tramite posta ma  non è stato indicato "
"nessun indirizzo per il contatto di follow-up"
