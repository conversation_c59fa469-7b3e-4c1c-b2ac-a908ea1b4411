<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id='ir_cron_update_ocr_status' model='ir.cron'>
            <field name='name'>Recruitment OCR: Update All Status</field>
            <field name='model_id' ref='model_hr_applicant'/>
            <field name='state'>code</field>
            <field name='code'>model.check_all_status()</field>
            <field name='interval_number'>1</field>
            <field name='interval_type'>days</field>
            <field name='numbercall'>-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True" />
        </record>

        <record id='ir_cron_ocr_parse' model='ir.cron'>
            <field name='name'>Recruitment OCR: Parse CV</field>
            <field name='model_id' ref='model_hr_applicant'/>
            <field name='state'>code</field>
            <field name='code'>model._cron_parse()</field>
            <field name='interval_number'>1</field>
            <field name='interval_type'>days</field>
            <field name='numbercall'>-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True" />
            <field name='priority' eval="1"/>
        </record>

        <record id='ir_cron_ocr_validate' model='ir.cron'>
            <field name='name'>Recruitment OCR: Validate CV</field>
            <field name='model_id' ref='model_hr_applicant'/>
            <field name='state'>code</field>
            <field name='code'>model._cron_validate()</field>
            <field name='interval_number'>1</field>
            <field name='interval_type'>days</field>
            <field name='numbercall'>-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True" />
        </record>
    </data>
</odoo>
