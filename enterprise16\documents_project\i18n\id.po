# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:41+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Abe <PERSON>, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid "%(project_name)s's Documents"
msgstr "Dokumen %(project_name)s"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Rangkaian ketentuan dan tindakan yang akan tersedia untuk semua lampiran "
"yang cocok dengan ketentuan"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_ask_for_validation
msgid "Ask for Validation"
msgstr "Minta Validasi"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Lampiran"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Kategorikan dan bagikan dokumen Anda dengan pelanggan Anda"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_facet
msgid "Category"
msgstr "Kategori"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Buat"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Buat Task"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Tag Default"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_deprecate
msgid "Deprecate"
msgstr "Usang"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_deprecated
msgid "Deprecated"
msgstr "Usang"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
#: model:ir.model.fields,field_description:documents_project.field_ir_attachment__document_ids
msgid "Document"
msgstr "Dokumen"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
#, python-format
msgid "Documents"
msgstr "Dokumen"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Dokumen &amp; Analitik"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_folder
msgid "Documents Workspace"
msgstr "Ruang Kerja Dokumen"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_draft
msgid "Draft"
msgstr "Draft"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__is_shared
msgid "Is Shared"
msgstr "Apakah Dibagi"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_mark_as_draft
msgid "Mark As Draft"
msgstr "Tandai Sebagai Draf"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "Merged Workspace"
msgstr "Ruang Kerja Digabung"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "New task from Documents"
msgstr "Task baru dari Dokumen"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__document_count
msgid "Number of documents in Project"
msgstr "Jumlah dokumen di Projec"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_count
msgid "Number of documents in Task"
msgstr "Jumlah dokumen di Task"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_documents_folder__project_ids
msgid "Project"
msgstr "Proyek"

#. module: documents_project
#: model:documents.folder,name:documents_project.documents_project_folder
msgid "Projects"
msgstr "Proyek"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_ids
#: model:ir.model.fields,field_description:documents_project.field_project_task__shared_document_ids
msgid "Shared Documents"
msgstr "Dokumen yang Dibagi"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_count
msgid "Shared Documents Count"
msgstr "Jumlah Dokumen yang Dibagi"

#. module: documents_project
#: model:documents.facet,name:documents_project.documents_project_status
msgid "Status"
msgstr "Status"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Kegiatan"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "Task created from document"
msgstr ""

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr ""
"Ruang kerja \"%s\" dibutuhkan oleh aplikasi Project dan tidak dapat dihapus."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"The \"%s\" workspace should either be in the \"%s\" company like this "
"project or be open to all companies."
msgstr ""
"Ruang kerja \"%s\" harus berada di perusahaan \"%s\" seperti project ini "
"atau terbuka untuk semua perusahaan."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the \"%s\" project to "
"which it is linked. Please update the company of the \"%s\" project, or "
"leave the company of this workspace empty."
msgstr ""
"Ruang kerja ini harus berada di perusahaan yang sama dengan project \"%s\" "
"yang mana ruang kerja ini di-link. Mohon update perusahaan project \"%s\", "
"atau tinggalkan perusahaan ruang kerja ini kosong."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Ruang kerja ini harus berada di perusahaan yang sama dengan project berikut yang mana ruang kerja ini di-link:\n"
"%s\n"
"\n"
"Mohon update perusahaan project-project tersebut, atau tinggalkan perusahaan ruang kerja ini kosong."

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_to_validate
msgid "To Validate"
msgstr "Untuk Divalidasi"

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Unggah <span class=\"fw-normal\">file atau </span>tarik <span class=\"fw-"
"normal\">ke sini.</span>"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__project_use_documents
msgid "Use Documents"
msgstr "Gunakan Dokumen"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_validate
msgid "Validate"
msgstr "Validasi"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_validated
msgid "Validated"
msgstr "Sudah Divalidasi"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,field_description:documents_project.field_project_task__documents_folder_id
msgid "Workspace"
msgstr "Ruang Kerja"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Workspace in which all of the documents of this project will be categorized."
" All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Ruang kerja di mana semua dokumen project ini akan dikategorasikan. Semua "
"lampiran task Anda akan secara otomatis ditambahkan sebagai dokumen di ruang"
" kerja ini juga."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%s\" company:\n"
"%s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%s\" workspace blank."
msgstr ""
"Anda tidak dapat mengganti perusahaan project ini, karena ruang kerja di-link ke project-project berikut yang masih ada di perusahaan \"%s\":\n"
"%s\n"
"\n"
"Mohon update perusahaan semua project supaya mereka tetap di perusahaan yang sama dengan ruang kerja mereka, atau biarkan kosong ruang kerja perusahaan \"%s\"."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "You cannot set a company on the Projects workspace."
msgstr ""

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.share_page
msgid "documents shared"
msgstr "dokumen dibagi"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "new %s from %s"
msgstr "%s baru dari %s"
