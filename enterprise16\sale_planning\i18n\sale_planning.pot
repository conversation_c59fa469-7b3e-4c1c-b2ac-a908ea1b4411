# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_planning
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2023-04-14 06:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid ""
"<span attrs=\"{'invisible': [('planning_enabled', '=', False)]}\" class=\"oe_inline me-2\">\n"
"                        as\n"
"                    </span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Planned</span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">To plan</span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span>Hours</span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
msgid "<strong>Sales Order Item  — </strong>"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__allocated_hours
msgid "Allocated Time"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Billable"
msgstr ""

#. module: sale_planning
#: model:ir.ui.menu,name:sale_planning.sale_planning_menu_schedule_by_sale_order
msgid "By Sales Order"
msgstr ""

#. module: sale_planning
#: model:product.template,name:sale_planning.developer_product_product_template
msgid "Developer (Plan services)"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__end_datetime
msgid "End Date"
msgstr ""

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_tester
msgid "Functional Tester"
msgstr ""

#. module: sale_planning
#: model:product.template,name:sale_planning.technical_maintainance_product_product_template
msgid "IT Technical Maintenance (Plan services)"
msgstr ""

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_technician
msgid "IT Technician"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,help:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_planning.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order. Only employees with this role will "
"automatically be assigned shifts for Sales Orders containing this service."
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "My Sales Orders"
msgstr ""

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "No shifts found. Let's create one!"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Non Billable"
msgstr ""

#. module: sale_planning
#: model:ir.model.constraint,message:sale_planning.constraint_planning_slot_check_datetimes_set_or_plannable_slot
msgid ""
"Only slots linked to a sale order with a plannable service can be "
"unscheduled."
msgstr ""

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.planning_action_orders_planned
msgid "Orders Planned"
msgstr ""

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.developer_product_product_template
msgid "Our developer will help you to add new features to your Software."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/views/sale_planning_calendar/sale_planning_calendar_controller.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#: code:addons/sale_planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Plan Orders"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_enabled
msgid "Plan Services"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid ""
"Plannable services should be a service product, product\n"
"%s."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
#, python-format
msgid "Plannable services should use an UoM within the %s category."
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_first_sale_line_id
msgid "Planning First Sale Line"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_planned
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_planned
msgid "Planning Hours Planned"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_hours_to_plan
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_hours_to_plan
msgid "Planning Hours To Plan"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order__planning_initial_date
msgid "Planning Initial Date"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_role
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_role_id
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_role_id
msgid "Planning Role"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_slot
msgid "Planning Shift"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_sale_order_line__planning_slot_ids
msgid "Planning Slot"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_product_template
msgid "Product"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__role_product_ids
msgid "Role Product"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_order_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Sales Order"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_line_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_planning.period_report_template
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.resource_sale_planning
msgid "Sales Order Item"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "Schedule by Sales Order"
msgstr ""

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Service"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_role__product_ids
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__role_product_ids
msgid "Services"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/controllers/main.py:0
#, python-format
msgid "Shift"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__start_datetime
msgid "Start Date"
msgstr ""

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.technical_maintainance_product_product_template
msgid "Take a rest. We'll do our best."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "The sales orders have successfully been assigned."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/sale_planning_mixin.js:0
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "There are no sales orders to assign or no employees are available."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
#, python-format
msgid ""
"This Sale Order Item doesn't have a target value of planned hours. Planned "
"hours :"
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/js/backend/planning_gantt_controller.js:0
#, python-format
msgid ""
"This resource is not available for this shift during the selected period."
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_view_form_in_gantt_inherit_sale_planning
msgid "Unschedule"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
#, python-format
msgid "View Planning"
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/hooks.js:0
#, python-format
msgid "View Shifts"
msgstr ""

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_planner
msgid "Work Planner"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "e.g. Consultant"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order_line.py:0
#, python-format
msgid "remaining"
msgstr ""
