<odoo noupdate="1">
    <record id="test_access_right_fake_ticket" model="ir.rule">
            <field name="name">Fake Ticket: portal or following</field>
            <field name="model_id" ref="model_test_access_right_ticket"/>
            <field name="domain_force">[
                '|',
                    ('message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
                    ('message_partner_ids', 'in', [user.partner_id.id])
            ]</field>
            <field name="groups" eval="[Command.link(ref('base.group_portal'))]"/>
    </record>
</odoo>
