<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="cfdibalance">
            <BCE:Balanza
                xmlns:BCE="http://www.sat.gob.mx/esquemas/ContabilidadE/1_3/BalanzaComprobacion"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                xsi:schemaLocation="http://www.sat.gob.mx/esquemas/ContabilidadE/1_3/BalanzaComprobacion http://www.sat.gob.mx/esquemas/ContabilidadE/1_3/BalanzaComprobacion/BalanzaComprobacion_1_3.xsd"
                Version="1.3"
                t-att-RFC="vat"
                t-att-Mes="month"
                t-att-Anio="year"
                t-att-TipoEnvio="type">
                <t t-foreach="accounts" t-as="account">
                    <BCE:Ctas
                        t-att-Debe="account.get('debit')"
                        t-att-NumCta="account.get('number')"
                        t-att-Haber="account.get('credit')"
                        t-att-SaldoFin="account.get('end')"
                        t-att-SaldoIni="account.get('initial')"/>
                </t>
            </BCE:Balanza>
       </template>
    </data>
</odoo>
