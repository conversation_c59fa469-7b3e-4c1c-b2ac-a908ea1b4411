# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_mass_mailing
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "&amp;times;"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid ""
"<strong>Newsletter Popup!</strong> The newsletter popup snippet effect is "
"active on this page. Click"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:15
#, python-format
msgid "Add a Newsletter Subscribe Button"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:53
#, python-format
msgid "Add a Newsletter Subscribe Popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Change Newsletter"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.unsubscribe
msgid "Choose your mailing subscriptions."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social_left
msgid "Contact"
msgstr "Kontakt"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.res_config_settings_view_form
msgid "Encourage to subscribe to mailing lists with a popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Here To Edit Dialog Content"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_mail_mass_mailing_list
msgid "Mailing List"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.unsubscribed
msgid "Mailing Subscriptions"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:21
#, python-format
msgid "Newsletter"
msgstr "Novine"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.view_mail_mass_mailing_list_form
msgid "Popup Content"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Subscribe"
msgstr "Pretplati se"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks for your subscription!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.res_config_settings_view_form
msgid ""
"This adds a new Newsletter Popup snippet to drag & drop on on your website "
"pages. It triggers a popup when visitors land on the page. This popup "
"prompts them to enter their email address to join a mailing list."
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:83
#, python-format
msgid "Type Here ..."
msgstr ""

#. module: website_mass_mailing
#: model:res.groups,name:website_mass_mailing.group_website_popup_on_exit
msgid "Use subscription pop up on the website"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_res_config_settings_group_website_popup_on_exit
msgid "Website Popup"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mail_mass_mailing_list_popup_content
msgid "Website Popup Content"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mail_mass_mailing_list_popup_redirect_url
msgid "Website Popup Redirect URL"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.unsubscribe
msgid "You are not subscribed to any of our mailing list."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.unsubscribed
msgid "You have been successfully <strong>unsubscribed</strong>!"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:168
#, python-format
msgid "Your changes have been saved."
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:171
#, python-format
msgid "Your changes have not been saved, try again later."
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "your email..."
msgstr ""
