# -*- coding:utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID
import xlsxwriter
from io import BytesIO
import base64
import logging
import os
_logger = logging.getLogger(__name__)

import time
from datetime import datetime
from dateutil import relativedelta
from odoo.exceptions import ValidationError

class AnalyticREport(models.TransientModel):
    _name = 'analytic.wizard.report'

    partner_id = fields.Many2one('res.partner', string="Customer")
    date_from = fields.Date(string="Date From", required=True, default=time.strftime('%Y-%m-01'))
    date_to = fields.Date(string="Date To", required=True,
                          default=str(datetime.now() + relativedelta.relativedelta(months=+1, day=1, days=-1))[:10])
    #equity_saving = fields.Float(default='11587.**********')
    #warranty = fields.Float(default='246.************')
    #rental = fields.Float(deafult='9077.***********')
    #salary = fields.Float(deafult='3265.***********')

    service_margin = fields.Float(default = '.15')

    gentextfile = fields.Binary('Click On Save As Button To Download File', readonly=True)
    analytics_ids = fields.Many2many('account.analytic.account', string='Analytic Accounts', required=True)

    def print_analytic_report_excel(self):
        if self.date_from > self.date_to:
            raise ValidationError('Start Date must be less than End Date')

        xls_filename = 'analytic_report.xlsx'
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        from_date = self.date_from
        to_date = self.date_to
        
        # STEP 1: Find company-wide indirect costs
        company_indirect_costs = {
            'equity_saving': 0,
            'warranty': 0,
            'rental': 0,
            'salary': 0
        }
        
        # Find equity saving cost (without partner filter)
        equity_saving_entry = self.env['account.move'].search([
            ('indirect_cost', '=', 'equity_saving_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ], limit=1)
        
        if equity_saving_entry:
            equity_saving_line = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', 'in', equity_saving_entry.ids)
            ], limit=1)
            if equity_saving_line:
                company_indirect_costs['equity_saving'] = equity_saving_line.debit
        
        # Find warranty cost (without partner filter)
        warranty_entry = self.env['account.move'].search([
            ('indirect_cost', '=', 'warranty_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ], limit=1)
        
        if warranty_entry:
            warranty_line = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', 'in', warranty_entry.ids)
            ], limit=1)
            if warranty_line:
                company_indirect_costs['warranty'] = warranty_line.debit
        
        # Find rental cost (without partner filter)
        rental_entry = self.env['account.move'].search([
            ('indirect_cost', '=', 'rental_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ], limit=1)
        
        if rental_entry:
            rental_line = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', 'in', rental_entry.ids)
            ], limit=1)
            if rental_line:
                company_indirect_costs['rental'] = rental_line.debit
        
        # Find salary cost (without partner filter)
        salary_entry = self.env['account.move'].search([
            ('indirect_cost', '=', 'salary_account'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to)
        ], limit=1)
        
        if salary_entry:
            salary_line = self.env['account.move.line'].search([
                ('debit', '>', 0),
                ('move_id', 'in', salary_entry.ids)
            ], limit=1)
            if salary_line:
                company_indirect_costs['salary'] = salary_line.debit
        
        # STEP 2: Calculate company-wide total direct costs based on ALL partners
        company_total_direct_costs = 0
        partner_direct_costs = {}
        
        # Always get ALL partners for company-wide calculations
        All_analytic_accounts = self.env['account.analytic.account'].search([])
        all_partners = set(All_analytic_accounts.mapped('partner_id'))
        
        # Calculate each partner's direct costs and total company direct costs (for ALL partners)
        for partner in all_partners:
            if not partner:
                continue
                
            analytic_accounts = self.env['account.analytic.account'].search([
                ('partner_id', '=', partner.id)
            ])
            
            partner_total = 0
            for analytic in analytic_accounts:
                lines = self.env['account.analytic.line'].search([
                    ('account_id', '=', analytic.id),
                    ('date', '>=', self.date_from), 
                    ('date', '<=', self.date_to),
                    ('general_account_id.account_type', '=', 'income')
                ])
                
                for line in lines:
                    partner_total += line.amount
            
            partner_direct_costs[partner.id] = partner_total
            company_total_direct_costs += partner_total
        
        # Get partners to include in the report
        if self.partner_id:
            report_partners = self.partner_id
        else:
            report_partners = all_partners
            
        # STEP 3: Process each partner that should be in the report
        for partner in report_partners:
            if not partner:
                continue
                
            sheet = workbook.add_worksheet(partner.name)
            format1 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            format2 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            date_style = workbook.add_format({'text_wrap': True, 'num_format': 'dd-mm-yyyy', 'bottom': True, 'right': True, 'left': True, 'top': True})
            format2.set_align('center')
            format2.set_align('vcenter')
            format2.set_color('white')
            format2.set_bg_color('blue')
            
            # Create number format for 3 decimal places
            number_format = workbook.add_format({'align': 'center', 'border': True, 'num_format': '#,##0.000'})
            
            header_row_style = workbook.add_format(
                { 'align': 'center', 'border': True})

            header2_row_style = workbook.add_format(
                {'bg_color': '#ffff00', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header2_row_style.set_align('center')
            header2_row_style.set_align('vcenter')
            header3_row_style =  workbook.add_format(
                {'bg_color': '#92d050', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header3_row_style.set_align('center')
            header3_row_style.set_align('vcenter')
            header4_row_style = workbook.add_format(
                {'bg_color': '#fac090', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header4_row_style.set_align('center')
            header4_row_style.set_align('vcenter')
            header5_row_style = workbook.add_format(
                { 'bold': True, 'align': 'center', 'border': True,'text_wrap': True})
            header5_row_style.set_align('center')
            header5_row_style.set_align('vcenter')

            header6_row_style = workbook.add_format(
                { 'font_size': 12,'align': 'center', 'border': True, 'text_wrap': True})
            header6_row_style.set_align('center')
            header6_row_style.set_align('vcenter')
            
            # Add number format to headers that will contain numbers
            header_number_format = workbook.add_format(
                {'bg_color': '#ffff00', 'color': '#000000', 'bold': True, 'align': 'center', 
                 'border': True, 'num_format': '#,##0.000'})
            header3_number_format = workbook.add_format(
                {'bg_color': '#92d050', 'color': '#000000', 'bold': True, 'align': 'center', 
                 'border': True, 'num_format': '#,##0.000'})
            header4_number_format = workbook.add_format(
                {'bg_color': '#fac090', 'color': '#000000', 'bold': True, 'align': 'center', 
                 'border': True, 'num_format': '#,##0.000'})
            header5_number_format = workbook.add_format(
                {'bold': True, 'align': 'center', 'border': True, 'text_wrap': True, 
                 'num_format': '#,##0.000'})

            sheet.set_column(0, 1, 20)
            sheet.set_column(1, 2, 15)
            sheet.set_column(2, 3, 15)
            sheet.set_column(3, 4, 15)
            sheet.set_column(4, 5, 15)
            sheet.set_column(5, 6, 15)
            sheet.set_column(6, 7, 15)
            sheet.set_column(7, 8, 15)
            sheet.set_column(8, 9, 15)
            sheet.set_column(9, 10, 15)
            sheet.set_column(11, 12, 15)
            sheet.set_column(13, 14, 15)
            sheet.set_column(15, 16, 15)

            sheet.insert_image('B2', '../static/images/logo.png')
            sheet.merge_range('B7:I7', 'المطالبات الشهرية - ' + partner.name, header6_row_style)
            sheet.merge_range('B8:B8', 'Date ', format2)
            sheet.merge_range('B8:I8', str(' From  ' + str(from_date) + ' To  ' + str(to_date)), header6_row_style)
            sheet.merge_range('B11:E11','التكلفة المباشرة', header2_row_style)
            sheet.merge_range('A12:A13', 'اسم المركز', header5_row_style)
            sheet.merge_range('B12:B13', 'المواد السلعية', header2_row_style)
            sheet.merge_range('C12:C13', 'مواد وادوات التنظيف', header2_row_style)
            sheet.merge_range('D12:D13', 'المصروفات  التشغيلية', header2_row_style)
            sheet.merge_range('E12:E13', 'الإجمالي ', header3_row_style)
            sheet.merge_range('F11:J11', 'التكلفة غير المباشرة', header2_row_style)
            sheet.merge_range('F12:F13', 'مصروفات توفير نقدية', header4_row_style)
            sheet.merge_range('G12:G13', 'م.التأمين', header4_row_style)
            sheet.merge_range('H12:H13', 'م.ايجارات', header4_row_style)
            sheet.merge_range('I12:I13', 'م.مرتبات', header4_row_style)
            sheet.merge_range('J12:J13', 'اجمالي', header3_row_style)
            sheet.merge_range('K11:K13', 'اجمالي التكلفة  المباشرة + غير المباشرة للمركز', header5_row_style)
            sheet.merge_range('L11:L13','هامش الخدمة', header5_row_style)
            sheet.merge_range('M11:M13', 'خدمات نقل قمامة ', header5_row_style)
            sheet.merge_range('N11:N13', 'إجمالي تكلفة المركز', header5_row_style)

            col = 0
            line_row = 12

            # STEP 4: Calculate partner's share of indirect costs
            partner_share = 0
            if company_total_direct_costs > 0 and partner.id in partner_direct_costs:
                partner_share = partner_direct_costs[partner.id] / company_total_direct_costs
            
            # Calculate partner's portion of each indirect cost
            partner_equity_saving = company_indirect_costs['equity_saving'] * partner_share
            partner_warranty = company_indirect_costs['warranty'] * partner_share
            partner_rental = company_indirect_costs['rental'] * partner_share
            partner_salary = company_indirect_costs['salary'] * partner_share

            data = []
            analytic_ids = self.env['account.analytic.account'].search([('partner_id','=',partner.id)])
            total_balance = 0
            total_goods_balance = 0
            total_cleaning_balance = 0
            total_other_balance = 0
            total_cleaning_garbage_amount = 0

            # Calculate total direct costs for this partner
            for analytic in analytic_ids:
                lines = self.env['account.analytic.line'].search(
                    [('account_id', '=', analytic.id),
                     ('date', '>=', self.date_from), ('date', '<=', self.date_to)], order="id desc")
                for line in lines:
                    if line.general_account_id.account_type == 'income':
                        total_balance += line.amount
                        total_goods_balance += line.goods_balance
                        total_cleaning_balance += line.cleaning_balance
                        total_other_balance += line.other_balance

            # Process each analytic account
            for analytic in analytic_ids:
                account_row = line_row
                account_col = col
                line_row += 1
                balance = 0
                goods_balance = 0
                cleaning_balance =0
                other_balance = 0
                cleaning_garbage_amount = 0

                # calculate garbage amount (this remains specific to each analytic account)
                account_moves_garbage = self.env['account.move'].search([
                    ('indirect_cost','=','garbage_account'),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('analytic_account_custom' , '=' , analytic.id)
                ] , limit=1 )
                if account_moves_garbage :
                    cleaning_garbage_amount_line = self.env['account.move.line'].search([
                        ('move_id','in', account_moves_garbage.ids),
                    ],limit=1)
                    if cleaning_garbage_amount_line:
                        cleaning_garbage_amount = cleaning_garbage_amount_line.debit
                total_cleaning_garbage_amount = total_cleaning_garbage_amount + cleaning_garbage_amount

                # Calculate direct costs for this analytic account
                lines = self.env['account.analytic.line'].search(
                    [('account_id', '=', analytic.id),
                     ('date', '>=', self.date_from), ('date', '<=', self.date_to)], order="id desc")
                for reco in lines :
                    if reco.general_account_id.account_type == 'income':
                        balance += reco.amount
                        goods_balance += reco.goods_balance
                        cleaning_balance += reco.cleaning_balance
                        other_balance += reco.other_balance

                # STEP 5: Calculate analytic account's share of partner's indirect costs
                analytic_share = 0
                if total_balance > 0:
                    analytic_share = balance / total_balance
                
                # Calculate this analytic account's share of partner's indirect costs
                analytic_equity_saving = partner_equity_saving * analytic_share
                analytic_warranty = partner_warranty * analytic_share
                analytic_rental = partner_rental * analytic_share  
                analytic_salary = partner_salary * analytic_share
                total_analytic_indirect = analytic_equity_saving + analytic_warranty + analytic_rental + analytic_salary

                # Write data to Excel with number format for numeric cells
                sheet.write(line_row, col , analytic.name, header_row_style)
                sheet.write(line_row, col + 1, goods_balance, number_format)
                sheet.write(line_row, col + 2, cleaning_balance, number_format)
                sheet.write(line_row, col + 3, other_balance, number_format)
                sheet.write(line_row, col + 4, balance, number_format)
                sheet.write(line_row, col + 5, analytic_equity_saving, number_format)
                sheet.write(line_row, col + 6, analytic_warranty, number_format)
                sheet.write(line_row, col + 7, analytic_rental, number_format)
                sheet.write(line_row, col + 8, analytic_salary, number_format)
                sheet.write(line_row, col + 9, total_analytic_indirect, number_format)
                sheet.write(line_row, col + 10, balance + total_analytic_indirect, number_format)
                sheet.write(line_row, col + 11, (balance + total_analytic_indirect) * self.service_margin, number_format)
                sheet.write(line_row, col + 12, cleaning_garbage_amount, number_format)
                sheet.write(line_row, col + 13, balance + total_analytic_indirect + (balance + total_analytic_indirect) * self.service_margin + cleaning_garbage_amount, number_format)

            # Write partner totals row with number format
            line_row+=1
            sheet.write(line_row, col,'اجمالي المطالبات الداخلية', header_row_style)
            sheet.write(line_row, col + 1, total_goods_balance, number_format)
            sheet.write(line_row, col + 2, total_cleaning_balance, number_format)
            sheet.write(line_row, col + 3, total_other_balance, number_format)
            sheet.write(line_row, col + 4, total_balance, number_format)
            sheet.write(line_row, col + 5, partner_equity_saving, number_format)
            sheet.write(line_row, col + 6, partner_warranty, number_format)
            sheet.write(line_row, col + 7, partner_rental, number_format)
            sheet.write(line_row, col + 8, partner_salary, number_format)
            total_partner_indirect = partner_equity_saving + partner_warranty + partner_rental + partner_salary
            sheet.write(line_row, col + 9, total_partner_indirect, number_format)
            sheet.write(line_row, col + 10, total_balance + total_partner_indirect, number_format)
            sheet.write(line_row, col + 11, (total_balance + total_partner_indirect) * self.service_margin, number_format)
            sheet.write(line_row, col + 12, total_cleaning_garbage_amount, number_format)
            sheet.write(line_row, col + 13, total_balance + total_partner_indirect + (total_balance + total_partner_indirect) * self.service_margin + total_cleaning_garbage_amount, number_format)
            
        # Add a summary sheet showing all partners
        if not self.partner_id and len(all_partners) > 1:
            summary_sheet = workbook.add_worksheet('ملخص الاجماليات')
            summary_sheet.set_column(0, 0, 30)
            summary_sheet.set_column(1, 10, 15)
            
            # Create percentage format with 5 decimal places
            percentage_format = workbook.add_format({'align': 'center', 'border': True, 'num_format': '#,##0.00000'})
            
            # Headers with Arabic translation
            summary_sheet.write(0, 0, 'اسم العميل', header5_row_style)
            summary_sheet.write(0, 1, 'التكلفة المباشرة', header5_row_style)
            summary_sheet.write(0, 2, 'النسبة من الإجمالي', header5_row_style)
            summary_sheet.write(0, 3, 'مصروفات توفير نقدية', header4_row_style)
            summary_sheet.write(0, 4, 'م.التأمين', header4_row_style)
            summary_sheet.write(0, 5, 'م.ايجارات', header4_row_style)
            summary_sheet.write(0, 6, 'م.مرتبات', header4_row_style)
            summary_sheet.write(0, 7, 'إجمالي غير المباشرة', header3_row_style)
            summary_sheet.write(0, 8, 'هامش الخدمة', header5_row_style)
            summary_sheet.write(0, 9, 'خدمات نقل قمامة', header5_row_style)
            summary_sheet.write(0, 10, 'الإجمالي الكلي', header3_row_style)
            
            # Partner data
            row = 1
            for partner in all_partners:
                if not partner:
                    continue
                    
                partner_direct = partner_direct_costs.get(partner.id, 0)
                share = 0
                if company_total_direct_costs > 0:
                    share = partner_direct / company_total_direct_costs
                
                partner_equity = company_indirect_costs['equity_saving'] * share
                partner_warranty = company_indirect_costs['warranty'] * share
                partner_rental = company_indirect_costs['rental'] * share
                partner_salary = company_indirect_costs['salary'] * share
                partner_indirect = partner_equity + partner_warranty + partner_rental + partner_salary
                
                # Get partner's garbage costs
                partner_garbage = 0
                partner_analytics = self.env['account.analytic.account'].search([('partner_id', '=', partner.id)])
                for analytic in partner_analytics:
                    garbage_entry = self.env['account.move'].search([
                        ('indirect_cost', '=', 'garbage_account'),
                        ('date', '>=', self.date_from),
                        ('date', '<=', self.date_to),
                        ('analytic_account_custom', '=', analytic.id)
                    ], limit=1)
                    
                    if garbage_entry:
                        garbage_line = self.env['account.move.line'].search([
                            ('move_id', 'in', garbage_entry.ids),
                            ('debit', '>', 0)
                        ], limit=1)
                        if garbage_line:
                            partner_garbage += garbage_line.debit
                
                service_margin = (partner_direct + partner_indirect) * self.service_margin
                grand_total = partner_direct + partner_indirect + service_margin + partner_garbage
                
                summary_sheet.write(row, 0, partner.name)
                summary_sheet.write(row, 1, partner_direct, number_format)
                summary_sheet.write(row, 2, share, percentage_format)
                summary_sheet.write(row, 3, partner_equity, number_format)
                summary_sheet.write(row, 4, partner_warranty, number_format)
                summary_sheet.write(row, 5, partner_rental, number_format)
                summary_sheet.write(row, 6, partner_salary, number_format)
                summary_sheet.write(row, 7, partner_indirect, number_format)
                summary_sheet.write(row, 8, service_margin, number_format)
                summary_sheet.write(row, 9, partner_garbage, number_format)
                summary_sheet.write(row, 10, grand_total, number_format)
                
                row += 1
            
            # Total row
            summary_sheet.write(row, 0, 'الإجمالي', header5_row_style)
            summary_sheet.write(row, 1, company_total_direct_costs, number_format)
            summary_sheet.write(row, 2, 1.0 if company_total_direct_costs > 0 else 0, percentage_format)
            summary_sheet.write(row, 3, company_indirect_costs['equity_saving'], number_format)
            summary_sheet.write(row, 4, company_indirect_costs['warranty'], number_format)
            summary_sheet.write(row, 5, company_indirect_costs['rental'], number_format)
            summary_sheet.write(row, 6, company_indirect_costs['salary'], number_format)
            total_indirect = sum(company_indirect_costs.values())
            summary_sheet.write(row, 7, total_indirect, number_format)
            
            # Calculate company total garbage costs
            company_garbage = 0
            garbage_entries = self.env['account.move'].search([
                ('indirect_cost', '=', 'garbage_account'),
                ('date', '>=', self.date_from),
                ('date', '<=', self.date_to)
            ])
            for entry in garbage_entries:
                garbage_line = self.env['account.move.line'].search([
                    ('move_id', '=', entry.id),
                    ('debit', '>', 0)
                ], limit=1)
                if garbage_line:
                    company_garbage += garbage_line.debit
            
            company_service_margin = (company_total_direct_costs + total_indirect) * self.service_margin
            company_grand_total = company_total_direct_costs + total_indirect + company_service_margin + company_garbage
            
            summary_sheet.write(row, 8, company_service_margin, number_format)
            summary_sheet.write(row, 9, company_garbage, number_format)
            summary_sheet.write(row, 10, company_grand_total, number_format)

        workbook.close()
        output.seek(0)
        self.write({'gentextfile': base64.encodebytes(output.getvalue())})
        return {
            'type': 'ir.actions.act_url',
            'name': 'Test Report',
            'url': '/web/content/analytic.wizard.report/{}/gentextfile/المطالبات الشهرية_{}_{}.xlsx?download=true'.format(
                self.id,
                self.date_from,
                self.date_to
            ),
            'target': 'new'
        }

