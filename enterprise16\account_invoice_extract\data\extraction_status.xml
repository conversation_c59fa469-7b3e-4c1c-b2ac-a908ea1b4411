<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_move_form_inherit_ocr" model="ir.ui.view">
        <field name="name">invoice.move.form.inherit.ocr</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_manual_send_for_digitization" class="oe_highlight" string="Send For Digitization" type="object"
                attrs="{'invisible': [('extract_can_show_send_button', '=', False)]}" data-hotkey="x" />
                <button name="action_reload_ai_data" string="Reload AI Data" type="object"
                attrs="{'invisible':['|', '|',
                       ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                       ('state', '!=', 'draft'),
                       ('extract_state', 'not in', ['waiting_validation', 'validation_to_send'])]}" />
            </xpath>
            <xpath expr="//header" position='after'>
                <field name="extract_state" attrs="{'invisible':True}"/>
                <field name="extract_word_ids" attrs="{'invisible':True}"/>
                <field name="extract_attachment_id" attrs="{'invisible':True}"/>
                <field name="extract_can_show_resend_button" attrs="{'invisible':True}"/>
                <field name="extract_can_show_send_button" attrs="{'invisible':True}"/>
                <field name="extract_can_show_banners" attrs="{'invisible':True}"/>
                <div role="status" class="alert alert-danger mb8 text-center"
                     attrs="{'invisible':['|',
                            ('extract_can_show_banners', '=', False),
                            ('extract_state', 'not in', ['not_enough_credit'])]}">
                    You don't have enough credit to extract data from your invoice.
                    <button type="object" name="buy_credits" class="btn btn-link">
                        <i class="fa fa-fw o_button_icon fa-arrow-right"/>
                        Buy credits
                    </button>
                    <button type="object" name="action_manual_send_for_digitization" class="btn btn-link">
                        <i class="fa fa-fw o_button_icon fa-arrow-right"/>
                        Resend For Digitization
                    </button>
                </div>
                <div role="status" class="alert alert-info mb8 text-center" name="waiting_extraction"
                     attrs="{'invisible':['|',
                            ('extract_can_show_banners', '=', False),
                            ('extract_state', 'not in', ['waiting_upload', 'waiting_extraction'])]}">
                    All fields will be automated by Artificial Intelligence, it might take 5 seconds.
                    <button type="object" name="check_status" class="btn btn-link">
                        <i class="fa fa-fw o_button_icon fa-arrow-right"/>
                        Refresh
                    </button>
                </div>
                <field name="extract_status_code" invisible="1" options="{'format': false}"/>
                <div role="status" class="alert alert-info mb8 text-center" name="extraction_status"
                     attrs="{'invisible':['|',
                            ('extract_can_show_banners', '=', False),
                            ('extract_state', 'not in', ['extract_not_ready'])]}">
                    The data extraction is not finished yet. The extraction usually takes between 5 and 10 seconds.
                    <button type="object" name="check_status" class="btn btn-link">
                        <i class="fa fa-fw o_button_icon fa-arrow-right"/>
                        Refresh
                    </button>
                </div>
                <div role="status" class="alert alert-danger mb8 text-center"
                     attrs="{'invisible':['|',
                            ('extract_can_show_banners', '=', False),
                            ('extract_state', 'not in', ['error_status'])]}">
                    <field name="extract_error_message" class="oe_inline" style="width:auto !important;"/>
                    <button type="object" name="action_manual_send_for_digitization" class="btn btn-link">
                        <i class="fa fa-fw o_button_icon fa-arrow-right"/>
                        Resend For Digitization
                    </button>
                </div>
            </xpath>
            <xpath expr="//field[@name='message_ids']" position='replace'>
                <field name="message_main_attachment_id" invisible="1"/>
                <field name="message_ids" options="{'post_refresh': 'always'}"/>
            </xpath>
            <xpath expr="//page[@id='other_tab']//group[@name='accounting_info_group']" position="after">
                <group string="Extraction Information"
                       name="extraction_info_group"
                       attrs="{'invisible': ['|', ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')), ('extract_state', 'in', ('no_extract_requested', 'not_enough_credit'))]}"
                       groups="base.group_no_one">
                    <field name="extract_remote_id"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="model_account_send_for_digitalization" model="ir.actions.server">
            <field name="name">Send Bills for digitization</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    if all(rec.is_invoice() for rec in records):
                        action = records.action_manual_send_for_digitization()
                    else:
                        raise UserError("This action is only possible for invoices")
            </field>
    </record>

</odoo>
