<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Paperformat -->
    <record id="paperformat_stock_report" model="report.paperformat">
        <field name="name">Inventory Ageing A4</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">297</field>
        <field name="page_width">210</field>
        <field name="orientation">Landscape</field>
        <field name="margin_top">05</field>
        <field name="margin_bottom">15</field>
        <field name="margin_left">7</field>
        <field name="margin_right">7</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">05</field>
        <field name="dpi">90</field>
    </record>

    <!-- PDF / Excel Actions -->
    <record id="sales_brand_report_xlsx" model="ir.actions.report">
        <field name="name">Sales Brand Report</field>
        <field name="model">sales.brand.report.wizard</field>
        <field name="report_type">xlsx</field>
        <field name="report_name">cubes_sales_brand_report.sales_brand_report_xls.xlsx</field>
        <field name="report_file">cubes_sales_brand_report.sales_brand_report_xls.xlsx</field>
    </record>

    <record id="print_sales_brand_report" model="ir.actions.report">
            <field name="name">Sales Brand Report</field>
            <field name="model">sales.brand.report.wizard</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">cubes_sales_brand_report.sales_brand_report_temp</field>
            <field name="report_file">cubes_sales_brand_report.sales_brand_report_temp</field>
            <field name="binding_model_id" ref="model_sales_brand_report_wizard"/>
            <field name="binding_type">report</field>
<!--            <field name="paperformat_id" ref="cubes_sales_brand_report.paperformat_stock_report"/>-->
        </record>

    <!-- Customer Headers / Footers -->
    <template id="external_sales_brand_report_layout">
        <div class="header">
        </div>

        <div class="article o_report_layout_standard">
            <t t-raw="0"/>
        </div>

        <div class="footer">
            <div class="text-center">
                <div class="text-right" style="border-top: 1px solid white;">
                    <ul class="list-inline">
                        <li>Page:</li>
                        <li>
                            <span class="page"/>
                        </li>
                        <li>/</li>
                        <li>
                            <span class="topage"/>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </template>

    <template id="external_sales_brand_report_header">
        <!-- Multicompany -->
        <t t-if="not o and doc">
            <t t-set="o" t-value="doc"/>
        </t>

        <t t-if="o and 'company_id' in o">
            <t t-set="company" t-value="o.company_id.sudo()"/>
        </t>
        <t t-if="not o or not 'company_id' in o">
            <t t-set="company" t-value="res_company"/>
        </t>
        <t t-call="cubes_sales_brand_report.external_sales_brand_report_layout">
            <t t-raw="0"/>
        </t>
    </template>

</odoo>