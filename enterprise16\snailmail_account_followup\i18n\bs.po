# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * snailmail_account_reports_followup
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('letters_qty', '&gt;', 1)]}\"> Sending this "
"document will cost </span>"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Cancel"
msgstr "Otkaži"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__snailmail_cost
msgid "Credits"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Kriterijum praćenja"

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model,name:snailmail_account_reports_followup.model_followup_send
msgid "Followup Send"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__id
msgid "ID"
msgstr "ID"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__letter_ids
msgid "Letter"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_followup_send__letters_qty
msgid "Letters Qty"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.actions.act_window,name:snailmail_account_reports_followup.followup_send
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Send Follow-Ups"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,field_description:snailmail_account_reports_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Pošalji pismo"

#. module: snailmail_account_reports_followup
#. openerp-web
#: code:addons/snailmail_account_reports_followup/static/src/xml/account_reports_followup_template.xml:7
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
#, python-format
msgid "Send by Post"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "Sending these"
msgstr ""

#. module: snailmail_account_reports_followup
#: model:ir.model.fields,help:snailmail_account_reports_followup.field_account_followup_followup_line__send_letter
msgid "When processing, it will send a letter by Post"
msgstr ""

#. module: snailmail_account_reports_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_reports_followup.followup_send_wizard_form
msgid "documents will cost approximatively"
msgstr ""
