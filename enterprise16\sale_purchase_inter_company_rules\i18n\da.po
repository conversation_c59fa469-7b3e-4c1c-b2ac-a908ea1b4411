# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_inter_company_rules
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-07 10:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_generated
msgid "Auto Generated Purchase Order"
msgstr "Automatisk genereret indkøb"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_generated
msgid "Auto Generated Sales Order"
msgstr "Auto genereret salgsordre"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__auto_validation
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__auto_validation
msgid "Automatic Validation"
msgstr "Automatisk validering"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr ""

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""
"Konfigure korrekt varehus for virksomheden(%s) fra Menu: "
"Indstillinger/Brugere/Virksomheder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__warehouse_id
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""
"Standard værdi angivet på Købs(Salgs)ordre, som vil blive oprettet baseret "
"på Salgs(købs)ordre til denne virksomhed"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                using "
"warehouse %(warehouse)s when a company confirms a %(event_type)s for "
"%(company)s."
msgstr ""
"Generer en %(validation)s %(generated_object)s ud fra varehus %(warehouse)s "
"når en virksomhed bekræfer en %(event_type)s for %(company)s."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                when a company"
" confirms a %(event_type)s for %(company)s."
msgstr ""
"Generer en %(validation)s %(generated_object)s når en virksomhed bekræfter "
"en %(event_type)s for %(company)s."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"Inter company user of company %(name)s doesn't have enough access rights"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""
"Intern virksomhed bruger af virksomhed %s har ikke tilstrækkelige "
"adgangsrettigheder"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid "Provide at least one user for inter company relation for %(name)s"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Provide one user for intercompany relation for %(name)s "
msgstr ""

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Indkøbsordre"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Rule"
msgstr "Regel"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Salgsordre"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "Salgsordrelinje"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""
"Vælg typen for at opsætte inter-virksomhed regler i den valgte virksomhed."

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_purchase_order_id
msgid "Source Purchase Order"
msgstr "Indkøbskilde"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_sale_order_id
msgid "Source Sales Order"
msgstr "Salgsordrekilde"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__purchase
msgid "Synchronize Purchase Order"
msgstr "Synkroniser Købsordre"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale
msgid "Synchronize Sales Order"
msgstr "Synkroniser Salgsordre"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale_purchase
msgid "Synchronize Sales and Purchase Order"
msgstr "Synkroniser Salgs- og Købsordre"

#. module: sale_purchase_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr "Brug varehus"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__warehouse_id
msgid "Warehouse"
msgstr "Lagerstyring"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "Lager for indkøb"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different than purchase price list currency.\n"
"The currency of the SO is obtained from the pricelist of the company partner.\n"
"\n"
"(SO currency: %(so_currency)s, Pricelist: %(pricelist)s, Partner: %(partner)s (ID: %(id)s))"
msgstr ""

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "draft"
msgstr "kladde"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase order"
msgstr "købsordre"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase/sales order"
msgstr "indkøbs/salgsordre"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales order"
msgstr "salgsordre"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales/purchase order"
msgstr "salgs/indkøbsordre"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "validated"
msgstr "valideret"
