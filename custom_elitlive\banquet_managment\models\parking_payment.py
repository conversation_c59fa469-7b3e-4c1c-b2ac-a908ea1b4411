from datetime import datetime

from odoo import api, fields, models,_

class ParkingPayment(models.Model):
    _name = 'reservation.parking.payment'

    name = fields.Char(
        default=lambda rec: ('New')
    )

    partner_id = fields.Many2one(
        comodel_name='res.partner',
        string='Customer'
    )
    amount = fields.Float(
        string='Amount'
    )
    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Journal',
        required=True
    )
    guarantee_journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Guarantee Journal',
        required=True
    )
    payment_type = fields.Selection([
        ('outbound', 'Send'),
        ('inbound', 'Received'),
    ], default='inbound')

    guarantee_amount = fields.Float(
        string='Guarantee Amount'
    )

    date = fields.Date(
        string='Date',
        default= datetime.now().date()
    )

    state = fields.Selection([
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancel'),
    ], default='draft')

    reservation_id = fields.Many2one(
        comodel_name='hotel.reservation'
    )

    def action_draft(self):
        self.state = 'draft'

    def action_posted(self):
        self.state = 'posted'

    def action_cancel(self):
        self.state = 'cancel'

    @api.model
    def create(self, vals_list):
        res = super().create(vals_list)
        sequence = self.env['ir.sequence'].next_by_code('reservation.parking.payment') or _('New')
        if sequence:
            res.name = f'{res.journal_id.code}/{sequence}'
        return res


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    reservation_id = fields.Many2one(
        comodel_name='hotel.reservation'
    )
