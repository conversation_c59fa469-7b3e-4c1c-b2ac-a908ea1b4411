<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="bi_partner_ledger_report_form_partner_aged_ledger" model="ir.ui.view">
        <field name="name">bi.partner.ledger.report.form.partner.aged.ledger</field>
        <field name="model">bi.account.aged.partner.balance</field>
        <field name="arch" type="xml">
            <form string="Report Options">
                <separator string="Aged Partner Balance"/>
                <group col="4">
                    <field name="date_from"/>
                    <field name="partner_ids" readonly="0"/>
                    <field name="period_length"/>
                    <newline/>
                    <field name="result_selection" widget="radio"/>
                    <field name="target_move" widget="radio"/>
                </group>
                <field name="journal_ids" required="0" invisible="1"/>
                <footer>
                    <button name="print_report_aged_partner" string="Print" type="object" default_focus="1"
                            class="oe_highlight"/>
                    <button name="print_report_aged_partner" string="Print Excel" type="object" class="oe_highlight"
                            context="{'report_type':'excel'}"/>
                    <button string="Cancel" class="btn btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_bi_partner_ledger_report_account_aged_balance_view" model="ir.actions.act_window">
        <field name="name">Aged Partner Balance</field>
        <field name="res_model">bi.account.aged.partner.balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="bi_partner_ledger_report_form_partner_aged_ledger"/>
        <field name="context">{}</field>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_bi_partner_ledger_report_aged_partner_balance"
              name="Aged Partner Balance"
              action="action_bi_partner_ledger_report_account_aged_balance_view"
              parent="bi_menu_finance_report_partner_ledger_report"/>

</odoo>
