<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="advanced_web_domain_widget.DomainFieldBits" owl="1">
        <div t-att-class="{ o_inline_mode: !props.editInDialog }">
            <t t-if="getResModel(props)">
                <DomainSelectorBits
                    resModel="getResModel(props)"
                    value="displayedDomain || '[]'"
                    readonly="props.readonly or props.editInDialog"
                    update.bind="update"
                    isDebugMode="!!env.debug"
                    debugValue="props.value"
                    className="props.readonly ? 'o_read_mode' : 'o_edit_mode'"
                />
                <div class="o_field_domain_panel">
                    <t t-if="state.recordCount !== null">
                        <i class="fa fa-arrow-right" role="img" aria-label="Domain" title="Domain" />
                        <t t-if="state.isValid and isValidDomain">
                            <button class="btn btn-sm btn-secondary o_domain_show_selection_button" type="button" t-on-click.stop="onButtonClick">
                                <t t-esc="state.recordCount" /> record(s)
                            </button>
                        </t>
                        <t t-else="">
                            <span class="text-warning" role="alert">
                                <i class="fa fa-exclamation-triangle" role="img" aria-label="Warning" title="Warning" /> Invalid domain
                            </span>
                        </t>
                        <t t-if="!!env.debug and !props.readonly">
                            <button
                                class="btn btn-sm btn-icon fa fa-refresh o_refresh_count"
                                role="img"
                                aria-label="Refresh"
                                title="Refresh"
                                t-on-click="() => this.loadCount(props)"
                            />
                        </t>
                    </t>
                    <t t-else="">
                        <i class="fa fa-circle-o-notch fa-spin" role="img" aria-label="Loading" title="Loading" />
                    </t>

                    <t t-if="props.editInDialog and !props.readonly">
                        <button class="btn btn-sm btn-primary o_field_domain_dialog_button" t-on-click.prevent="onEditDialogBtnClick">Edit Domain</button>
                    </t>
                </div>
            </t>
            <t t-else="">
                <div>Select a model to add a filter.</div>
            </t>
        </div>
    </t>

</templates>
