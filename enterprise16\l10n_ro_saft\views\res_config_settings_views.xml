<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_form_inherit_l10n_ro_saft" model="ir.ui.view">
        <field name="name">res.config.settings.form.inherit.l10n.ro.saft</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='invoicing_settings']" position="after">
                <div attrs="{'invisible': [('country_code', '!=', 'RO')]}">
                    <div id="l10n_ro_saft_title">
                        <h2>Romanian localization</h2>
                    </div> 
                    <div id="l10n_ro_saft_section" class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="l10n_ro_saft_tax_accounting_basis" string="Tax Accounting Basis"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                <div class="text-muted">
                                    The accounting regulations and Chart of Accounts used by this company
                                </div>
                                <div class="content-group">
                                    <div class="row mt16">
                                        <field name="l10n_ro_saft_tax_accounting_basis"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
