.. image:: https://img.shields.io/badge/licence-AGPL--3-blue.svg
    :target: https://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3

Advanced VAT Invoice
====================
This module allow us to print vat invoice report and simplified tax invoice
report in arabic language and also this module have an option for generating
qr code and display invoice reports while scanning the QR code.

License
-------
General Public License, Version 3 (AGPL v3).
https://www.gnu.org/licenses/agpl-3.0-standalone.html

Configuration
=============
* Select QR code generation method from Configuration --> Settings --> QR Code

Company
-------
* `Cybrosys Techno Solutions <https://cybrosys.com/>`__

Credits
-------
Developer: (V16) Athira P S ,
           (V17) Gayathri V
Contact: <EMAIL>

Contacts
--------
* Mail Contact : <EMAIL>
* Website : https://cybrosys.com

Bug Tracker
-----------
Bugs are tracked on GitHub Issues. In case of trouble, please check there if your issue has already been reported.

Maintainer
==========
.. image:: https://cybrosys.com/images/logo.png
   :target: https://cybrosys.com

This module is maintained by Cybrosys Technologies.

For support and more information, please visit `Our Website <https://cybrosys.com/>`__

Further information
===================
HTML Description: `<static/description/index.html>`__
