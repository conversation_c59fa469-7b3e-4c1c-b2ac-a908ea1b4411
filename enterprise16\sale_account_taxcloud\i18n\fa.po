# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_taxcloud
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Go to Settings."
msgstr "به تنظیمات برو"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "آیا TaxCloud پیکربندی شده است"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"لطفاً مشخصات کاربری TaxCloud خود را برای مقایسه‌ی خودکار نرخ مالیات وارد "
"کنید."

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "فاکتور پیش پرداخت فروش"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "سفارش فروش"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "سطر سفارش‌فروش"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Tax"
msgstr "مالیات"

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "قادر به بازیابی مالیات‌ها از TaxCloud نیست:"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Update taxes"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Use TaxCloud API"
msgstr "از TaxCloud "

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"برای تعیین لزوم هشدار دادن یا ندادن به کاربر برای پیکربندی TaxCloud "
"مورداستفاده قرار می‌گیرد."
