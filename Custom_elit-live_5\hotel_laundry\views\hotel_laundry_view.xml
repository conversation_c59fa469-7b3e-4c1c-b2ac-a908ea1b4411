<?xml version="1.0"?>
<odoo>
    <data>
        <!--view creation for hotel loundry, which will come in configuration part-->
        <record model="ir.ui.view" id="hotel_laundry_form_view">
            <field name="name">hotel.laundry.form</field>
            <field name="model">hotel.laundry</field>
            <field name="arch" type="xml">
                <form string="Hotel Laundry">

                    <header>
                        <button name="confirm" string="Confirm" states="draft,edit" type="object"
                                groups="hotel_laundry.group_laundry_manager"/>
                        <button name="update_record" string="Update" states="confirmed" type="object"
                                groups="hotel_laundry.group_laundry_manager"/>
                        <button name="cancel_supplier" string="Cancel" states="edit,confirmed" type="object"
                                groups="hotel_laundry.group_laundry_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed"/>
                    </header>
                    <sheet>
                        <group colspan="4" col="4">
                            <group colspan="2" col="2">
                                <field name="partner_id" context="{'res_partner_search_mode': 'supplier'}"/>
                                <field name="company_id"/>
                            </group>
                        </group>
                            <separator colspan="4" string="Laundry Service Info"/>
                            <notebook colspan="4">
                                <page string="Hotel Laundry Service">
                                        <field name="laundry_service_ids" nolabel="1">
                                            <form string="Hotel Laundry Service">
                                                <group colspan="4">
                                                    <group colspan="4" col="4">
                                                        <!-- <field name="name"/> -->
                                                        <field name="laundry_services_id"
                                                               domain="[('type','=','service'),('categ_id','=',category_id)]"
                                                               context="{'default_type': 'service', 'default_categ_id':category_id}"/>
                                                        <field name="supplier_id" invisible="1"/>
                                                        <field name="category_id" invisible="1"/>
                                                    </group>
                                                    <separator colspan="4" string="Lanundry Service Items Info"/>
                                                    <notebook >
                                                        <page string="Laundry Service Items">
                                                                <field name="laundry_services_items_ids" nolabel="1">
                                                                    <form string="Laundry Service Items">
                                                                        <group>
                                                                            <field name="name" invisible="1"/>
                                                                            <field name="item_id"
                                                                                   domain="[('type','=','service'),('categ_id','=',category_id1)]"
                                                                                   context="{'default_type': 'service', 'default_categ_id':category_id1}"/>
                                                                            <field name="cost_price"/>
                                                                            <field name="sale_price"/>
                                                                            <field name="category_id1" invisible="1"/>
                                                                        </group>
                                                                    </form>
                                                                    <tree string="Laundry Service Items">
                                                                        <field name="item_id"/>
                                                                        <field name="cost_price"/>
                                                                        <field name="sale_price"/>
                                                                    </tree>
                                                                </field>
                                                        </page>
                                                    </notebook>
                                                </group>
                                            </form>
                                            <tree string="Hotel Lanudry Service">
                                                <field name="laundry_services_id"/>
                                            </tree>
                                        </field>
                                </page>
                            </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="hotel_laundry_tree_view">
            <field name="name">hotel.laundry.tree</field>
            <field name="model">hotel.laundry</field>
            <field name="arch" type="xml">
                <tree string="Hotel Laundry">
                    <field name="partner_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="view_hotel_laundry_kanban" model="ir.ui.view">
            <field name="name">hotel.laundry.kanban</field>
            <field name="model">hotel.laundry</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="partner_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
										<strong class="o_kanban_record_title">
											<field name="partner_id"/>
										</strong>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="hotel_laundry_view">
            <field name="name">Hotel Laundry</field>
            <field name="res_model">hotel.laundry</field>
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>


        <menuitem name="Laundry Service"
                  id="laundry_service_submenu"
                  action="hotel_laundry_view"
                  parent="hotel.hotel_configuration_menu"/>

        <!-- View creation for laundry management which will show main menu to manage service product -->

        <record model="ir.ui.view" id="laundry_management_form_view1">
            <field name="name">laundry.management.form.view1</field>
            <field name="model">laundry.management</field>
            <field name="arch" type="xml">
                <form string="Laundry Service Configuration" version="7.0">
                    <header>
                        <button name="confirm" string="Confirm" states="draft" type="object" icon="fa-check"/>
                        <button name="cancel_service" string="Cancel" states="draft" type="object" icon="fa-times"/>
                        <button name="send_to_laundry" string="Send to Laundry" states="confirmed" type="object"/>
                        <button name="laundry_returned" string="Laundry Return" states="sent_to_laundry" type="object"
                                icon="fa-forward" context="{'laundry_flag': True}"/>
                        <button name="customer_return" string="Customer Return" type="object"
                                attrs="{'invisible': ['|',('request_type','!=','from_room'),('state','!=','laundry_returned')]}"/>
                        <button name="done_internal" string="Done" type="object"
                                attrs="{'invisible': ['|',('request_type','!=','internal'),('state','!=','laundry_returned')]}"/>
                        <button name="done_from_room" string="Done" states="customer_returned" type="object"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,done"/>
                    </header>
                    <sheet>
                        <group colspan="4" col="4">
                            <field name="name"/>
                            <field name="user_id"/>
                            <field name="date_order"/>
                            <field name="deadline_date"/>
                            <field name="shop_id"/>
                            <field name="company_id"/>
                            <field name="pricelist_id"/>
                        </group>
                        <newline/>
                        <separator colspan="4" string="Room Info"/>
                        <group colspan="4" col="4">
                            <field name="request_type"/>
                            <field name="service_type"></field>
                            <field name="is_chargable"></field>
                            <!-- <field name="room_number" attrs="{'invisible': [('request_type','!=','from_room')]}"
                            domain="[('state','=','sellable')]"
                            select="1"/> -->
                            <field name="room_number" attrs="{'invisible': [('request_type','!=','from_room')]}"
                                   select="1"/>
                            <field name="partner_id" attrs="{'invisible': [('request_type','!=','from_room')]}"/>
                        </group>
                        <newline/>
                        <separator string="Supplier Info"/>
                        <group>
                            <field name="supplier_id"/>
                            <label for="supplier_id_temp" string=" " colspan="2"/>
                            <field name="supplier_id_temp" invisible="1"/>
                        </group>
                        
                            <separator string="Laundry Service Product Info"/>
                            <notebook>
                                <page string="Laundry Service Product">
                                        <field name="laundry_service_product_ids" nolabel="1"
                                               context="{'default_supplier_id': supplier_id}">
                                            <form string="Laundry Service Product">
                                                <group colspan="4">
                                                    <group colspan="4" col="4">
                                                        <field name="laundry_services_id"/>
                                                        <field name="pricelist_id"/>
                                                        <field name="tax_id" widget="many2many_tags"
                                                               domain="[('type_tax_use','&lt;&gt;','purchase')]"/>
                                                        <!-- 													<field name="cost_tax_id" widget="many2many_tags" domain="[('parent_id','=',False),('type_tax_use','&lt;&gt;','sale')]"/> -->
                                                        <!-- 													<field name="cost_rate" /> -->
                                                        <field name="sales_rate"/>
                                                        <!-- 													<field name="cost_subtotal" /> -->
                                                        <field name="sale_subtotal"/>
                                                        <field name="supplier_id" invisible="1"/>
                                                    </group>
                                                    <newline/>
                                                    <separator string="Service Product Line Info"/>
                                                    
                                                        <field name="laundry_service_product_line_ids" nolabel="1"/>
                                                </group>
                                            </form>
                                            <tree string="Laundry Service Product">
                                                <field name="laundry_services_id"/>
                                                <field name="tax_id" widget="many2many_tags"/>
                                                <!-- 												<field name="cost_tax_id"/> -->
                                                <!-- 												<field name="cost_rate" /> -->
                                                <field name="sales_rate"/>
                                                <!-- 												<field name="cost_subtotal"  sum="Total Of Cost Subtotals"/> -->
                                                <field name="sale_subtotal" sum="Total Of Sale Subtotals"/>
                                            </tree>
                                        </field>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="amount_subtotal" widget='monetary'
                                               options="{'currency_field': 'currency_id'}"/>
                                        <field name="amount_tax" widget='monetary'
                                               options="{'currency_field': 'currency_id'}"/>
                                        <div class="oe_subtotal_footer_separator oe_inline">
                                            <label for="amount_total"/>
                                        </div>
                                        <field name="amount_total" nolabel="1" class="oe_subtotal_footer_separator"
                                               widget='monetary' options="{'currency_field': 'currency_id'}"/>
                                </page>
                                <!--<page string="Picking Info">
                                <separator colspan="4" string="Picking"/>
                                     <field colspan="4" name="invoice_ids" nolabel="1" widget="many2many"/>
                                </page>-->
                            </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="laundry_management_tree_view1">
            <field name="name">laundry.management.tree.view</field>
            <field name="model">laundry.management</field>
            <field name="arch" type="xml">
                <tree string="Laundry Service Configuration">
                    <field name="name"/>
                    <field name="user_id"/>
                    <field name="date_order"/>
                    <field name="deadline_date"/>
                    <field name="request_type"/>
                    <field name="room_number"/>
                    <field name="partner_id"/>
                    <field name="supplier_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>
        <record model="ir.actions.act_window" id="laundry_management_view">
            <field name="name">Laundry Service Configuration</field>
            <field name="res_model">laundry.management</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>

        <menuitem name="Laundry Management" sequence="75"
                  id="laundry_management_menu"
                  parent="hotel.hotel_management_menu"/>
        <menuitem name="Laundry Service Request"
                  id="laundry_service_request"
                  action="laundry_management_view"
                  parent="laundry_management_menu"/>

        <record model="ir.ui.view" id="laundry_service_product_line_form_view">
            <field name="name">laundry.service.product.line.form.view</field>
            <field name="model">laundry.service.product.line</field>
            <field name="arch" type="xml">
                <form string="laundry Service Product Line" version="7.0">
                    <sheet>
                        <group colspan="4">
                            <field name="item_id" invisible="1"/>
                            <field name="item_id_ref"/>
                            <field name="qty"/>
                            <field name="qty_uom"/>
                            <!-- 							<field name="cost_price" /> -->
                            <field name="sales_price"/>
                            <!-- 							<field name="cost_subtotal" /> -->
                            <field name="sale_subtotal"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="laundry_service_product_line_form_view123">
            <field name="name">hotel.laundry.services.form.view</field>
            <field name="model">hotel.laundry.services</field>
            <field name="arch" type="xml">
                <form string="laundry Service" version="7.0">
                    <sheet>
                        <group colspan="4" col="4">
                            <field name="name" invisible="1"/>
                            <!-- <field name="hotel_laundry_service_id"/> -->
                            <field name="supplier_id"/>
                            <field name="laundry_services_id"
                                   context="{'default_categ_id': category_id, 'search_default_categ_id': category_id,'default_type': 'service', 'search_default_type': 'service'}"/>
                            <field name="category_id" invisible="1"/>
                        </group>
                        <field name="laundry_services_items_ids"/>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="laundry_service_product_line_form_view12345">
            <field name="name">hotel.laundry.services.form.view</field>
            <field name="model">hotel.laundry.services.items</field>
            <field name="arch" type="xml">
                <form string="laundry Services Item" version="7.0">
                    <sheet>
                        <group colspan="4" col="4">
                            <field name="name"/>
                            <!-- <field name="hotel_laundry_service_id"/> -->
                            <field name="item_id"
                                   context="{'default_categ_id': category_id1, 'search_default_categ_id': category_id1}"/>
                            <field name="cost_price"/>
                            <field name="sale_price"/>
                            <field name="category_id1"/>
                        </group>

                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="laundry_service_product_line_tree_view">
            <field name="name">laundry.service.product.line.tree.view</field>
            <field name="model">laundry.service.product.line</field>
            <field name="arch" type="xml">
                <tree string="laundry Service Product Line">
                    <field name="item_id_ref"/>
                    <field name="qty"/>
                    <field name="qty_uom"/>
                    <!-- 						<field name="cost_price" invisible="1"/> -->
                    <field name="sales_price"/>
                    <!-- 						<field name="cost_subtotal" invisible="1" sum="Total Of Cost Subtotal"/> -->
                    <field name="sale_subtotal" sum="Total Of Sale Subtotal"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="view_hotel_folio_form_inherit_laundry">
            <field name="name">hotel.folio.inherit laundry</field>
            <field name="model">hotel.folio</field>
            <field name="inherit_id" ref="hotel_management.hotel_folio_form_inherit_state"/>
            <field name="arch" type="xml">
                <field name="food_lines" position="after">
                    <separator string="Laundry Lines"/>
                    <field name="laundry_line_ids" colspan="4" string="Laundry Line" nolabel="1" readonly="1">
                        <form string="Laundry Line">
                            <notebook>
                                <page string="Laundry Line">
                                    <separator string="Automatic Declaration" colspan="4"/>
                                    <group colspan="4" col="6">
                                        <field name="product_uom_qty"
                                               context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id,  'uom':product_uom}"
                                        />
                                        <field name="company_id" invisible="1"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom" options='{"no_open": True}'/>
                                        <field name="product_id"
                                               context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom}"

                                        />
                                    </group>
                                    <separator string="Manual Description"
                                               colspan="4"/>
                                    <field name="name" colspan="4" select="2"/>
                                    <field name="price_unit" select="2"/>
                                    <field name="discount"/>
                                    <newline/>
                                    <field name="tax_id" widget="many2many_tags" colspan="4"/>
                                    <separator string="States" colspan="4"/>
                                    <field name="state" select="2"/>
                                    <!-- 											<field name="invoiced" select="2"/> -->
                                </page>
                                <page string="Extra Info">
                                    <field name="source_origin"/>
                                    <!-- 											<field name="product_uos"  /> -->
                                    <!-- 											<field name="address_allotment_id" select="2"/> -->
                                </page>
                                <page string="History">
                                    <separator string="Invoice Lines" colspan="4"/>
                                    <field name="invoice_lines" colspan="4" nolabel="1"/>
                                </page>
                            </notebook>
                        </form>
                        <tree string="Laundry Line">
                            <field name="state" invisible="1"/>
                            <field name="product_id"/>
                            <field name="name"/>
                            <field name="source_origin"/>
                            <field name="product_uom_qty"/>
                            <field name="tax_id" widget="many2many_tags"/>
                            <field name="price_unit"/>
                            <field name="price_subtotal"/>
                        </tree>
                    </field>
                </field>
            </field>
        </record>


    </data>
</odoo>
