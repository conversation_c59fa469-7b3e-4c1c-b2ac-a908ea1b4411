<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="helpdesk_sla_report_analysis_view_search_timesheet" model="ir.ui.view">
        <field name="name">helpdesk.sla.report.analysis.search.timesheet</field>
        <field name="model">helpdesk.sla.report.analysis</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_sla_report_analysis_view_search"/>
        <field name="arch" type="xml">
            <filter name="my_ticket" position="after">
                <filter string="My Team's Tickets" name="my_team_ticket" domain="[('user_id.employee_parent_id.user_id', '=', uid)]"/>
                <filter string="My Department" name="my_department" domain="['|', ('employee_id.member_of_department', '=', True), ('user_id', '=', uid)]"/>
            </filter>
            <filter name="team" position="after">
                <filter string="Department" name="department" context="{'group_by':'department_id'}"/>
                <filter string="Manager" name="manager" context="{'group_by': 'manager_id'}"/>
            </filter>
        </field>
    </record>
</odoo>
