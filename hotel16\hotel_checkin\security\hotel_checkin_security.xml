<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Create a dedicated category for hotel management -->
        <record id="module_category_hotel_management" model="ir.module.category">
            <field name="name">Hotel Management</field>
            <field name="description">Helps you manage your hotel operations</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Create a security group for check-in only users -->
        <record id="group_hotel_checkin_user" model="res.groups">
            <field name="name">Hotel Check-in User</field>
            <field name="category_id" ref="hotel_checkin.module_category_hotel_management"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Users who can only check in guests but cannot create or modify reservations</field>
        </record>
        
        <!-- Menu access rules will be added directly in the view XML -->
    </data>
</odoo> 