<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.social.push.notifications</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="website.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='website_marketing_automation']" position="after">
                <div class="col-md-6 o_setting_box">
                    <div class="o_setting_left_pane">
                        <field name="firebase_enable_push_notifications"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="firebase_enable_push_notifications" class="oe_inline o_form_label"/>
                        <div class="text-muted">
                            Send push notifications and configure this website's notifications permission request
                        </div>
                        <div class="content-group" attrs="{'invisible': [('firebase_enable_push_notifications', '=', False)]}">
                            <div class="mt16">
                                <label for="notification_request_title" string="Title" class="o_form_label"/>
                                <field name="notification_request_title" class="w-100"
                                    placeholder="Want to discover new versions?"/>
                            </div>
                            <div class="mt16">
                                <label for="notification_request_body" string="Body" class="o_form_label align-top"/>
                                <field name="notification_request_body" class="w-100"
                                    placeholder="Enable push notifications to be notified about new features."/>
                            </div>
                            <div class="mt16">
                                <label for="notification_request_delay" string="Delay" class="o_form_label pe-2"/>
                                <field name="notification_request_delay" class="oe_inline"/>
                                <span class="ps-2">seconds</span>
                            </div>
                            <div class="mt16">
                                <label for="notification_request_icon" string="Icon" class="o_form_label pe-2"/>
                                <field name="notification_request_icon" class="oe_inline"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 o_setting_box"
                    attrs="{'invisible': [('firebase_enable_push_notifications', '=', False)]}"
                    groups="base.group_no_one">
                    <div class="o_setting_left_pane">
                        <field name="firebase_use_own_account"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="firebase_use_own_account" class="oe_inline o_form_label"/>
                        <div class="text-muted">
                            Use your own Firebase Account for this website's push notifications
                        </div>
                        <div class="content-group" attrs="{'invisible': [('firebase_use_own_account', '=', False)]}">
                            <div class="mt16">
                                <label for="firebase_project_id" string="Project ID" class="o_form_label"/>
                                <field name="firebase_project_id" class="w-100" placeholder='e.g. "my-project-id"'/>
                            </div>
                            <div class="mt16">
                                <label for="firebase_web_api_key" string="Web API Key" class="o_form_label"/>
                                <field name="firebase_web_api_key" class="w-100" placeholder='e.g. "BIzbSyXhhsFHEgphW55CSg5cV7h7c_S-AuTMKc9"'/>
                            </div>
                            <div class="mt16">
                                <label for="firebase_push_certificate_key" string="Push Certificate Key ID" class="o_form_label"/>
                                <field name="firebase_push_certificate_key" class="w-100"
                                     placeholder='e.g. "CCSc77KP_LX8dTAogFakOoJ_VqNP15u0-43psDJe__a9B..."'/>
                            </div>
                            <div class="mt16">
                                <label for="firebase_sender_id" string="Sender ID" class="o_form_label"/>
                                <field name="firebase_sender_id" class="w-100" placeholder='e.g. "280765099157"'/>
                            </div>
                            <div class="mt16">
                                <label for="firebase_admin_key_file" string="Key File" class="o_form_label pe-2"/>
                                <field name="firebase_admin_key_file"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
