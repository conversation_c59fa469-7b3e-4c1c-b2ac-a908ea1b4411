# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.disallowed_expenses_main_template
msgid ""
"<span>There are multiple disallowed expenses rates in this period</span>"
msgstr ""

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_account
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__account_ids
msgid "Account"
msgstr "帳戶"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Active"
msgstr "啟用"

#. module: account_disallowed_expenses
#: model_terms:ir.actions.act_window,help:account_disallowed_expenses.action_account_disallowed_expenses_category_list
msgid "Add a Disallowed Expenses Category"
msgstr "新增不允許費用類別"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__category_id
msgid "Category"
msgstr "類別"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Category Name"
msgstr "類別名稱"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__code
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Code"
msgstr "代號"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__company_id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "公司"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "創立者"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "建立於"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__current_rate
msgid "Current Rate"
msgstr "目前比率"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__rate
msgid "Disallowed %"
msgstr "不允許 %"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_disallowed_amount
msgid "Disallowed Amount"
msgstr "不允許金額"

#. module: account_disallowed_expenses
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_report_de
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_form
msgid "Disallowed Expenses"
msgstr "已拒絕費用"

#. module: account_disallowed_expenses
#: model:ir.actions.act_window,name:account_disallowed_expenses.action_account_disallowed_expenses_category_list
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_disallowed_expenses_category_list
msgid "Disallowed Expenses Categories"
msgstr "已拒絕費用分類"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_category
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_account__disallowed_expenses_category_id
msgid "Disallowed Expenses Category"
msgstr "不允許費用類別"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_report_handler
msgid "Disallowed Expenses Custom Handler"
msgstr "不允許費用自訂處理程式"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_rate
msgid "Disallowed Expenses Rate"
msgstr "不允許費用比率"

#. module: account_disallowed_expenses
#: model:account.report,name:account_disallowed_expenses.disallowed_expenses_report
#: model:ir.actions.client,name:account_disallowed_expenses.action_account_report_de
msgid "Disallowed Expenses Report"
msgstr "不允許費用報告"

#. module: account_disallowed_expenses
#: model:ir.model.constraint,message:account_disallowed_expenses.constraint_account_disallowed_expenses_category_unique_code_in_country
msgid "Disallowed expenses category code should be unique in each company."
msgstr "不允許費用類別代碼應在每個公司內獨一無二。"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__display_name
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "General Ledger"
msgstr "總分類帳"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__id
msgid "ID"
msgstr "ID"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Journal Items"
msgstr "日記帳項目"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category____last_update
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__name
msgid "Name"
msgstr "名稱"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#, python-format
msgid "No Rate"
msgstr "無匯率"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_rate
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__rate_ids
msgid "Rate"
msgstr "比率"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Rates"
msgstr "比率"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Related Account(s)"
msgstr "相關賬戶"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Set Rates"
msgstr "設定匯率"

#. module: account_disallowed_expenses
#: model:ir.model.fields,help:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Set active to false to hide the category without removing it."
msgstr "將 active 設為假 (false) 可隱藏類別而不作刪除。"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "開始日期"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Total"
msgstr "總計"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_total_amount
msgid "Total Amount"
msgstr "金額總計"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. 1201"
msgstr "例：1201"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. Non-Deductible Tax"
msgstr "例：不可抵扣稅款"
