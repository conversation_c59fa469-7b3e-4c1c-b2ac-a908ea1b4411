<odoo>
    <data>

        <record id="user_login_history" model="ir.ui.view">
            <field name="name">مسجل دخول المستخدم</field>
            <field name="model">res.users.loging_history</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="user_id"/>
                    <field name="login_date"/>
                    <field name="logout_date"/>
                </tree>
            </field>
        </record>

        <record id="user_login_log_act_window" model="ir.actions.act_window">
            <field name="name">User Login Log</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.users.loging_history</field>
            <field name="view_mode">tree,form</field>
        </record>


    </data>
</odoo>