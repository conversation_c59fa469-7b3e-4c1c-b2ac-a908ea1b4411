# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# KeyVillage, 2023
# <PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# Ивай<PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# aleks<PERSON><PERSON> i<PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "# на задачи"

#. module: industry_fsm
#: model:ir.actions.report,print_report_name:industry_fsm.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "<b>Send your task report</b> to your customer."
msgstr ""

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.planned_date_end, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br><br>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br><br>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "<i class=\"fa fa-check me-1\"/>Sign"
msgstr ""

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_task_report
msgid ""
"<p>\n"
"                Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br><br>\n"
"                Please find attached the worksheet for our onsite operation. <br><br>\n"
"                Feel free to contact us if you have any questions.<br><br>\n"
"                Best regards,<br><br>\n"
"            </p>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<span class=\"o_stat_text\">Worksheet</span>\n"
"                            <span class=\"o_stat_text\">Complete</span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong class=\"me-2\">Total</strong>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>Клиент: </strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Worker: </strong>"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__active
msgid "Active"
msgstr "Активно"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_config_activity_type
msgid "Activity Types"
msgstr "Тип дейности"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "Администратор"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid ""
"Analyze the progress of your tasks and the performance of your workers."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__ancestor_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Ancestor Task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Archived"
msgstr "Архивирано"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__user_ids
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Assignees"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr "Дата на възлагане"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_avg
msgid "Average Rating"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
msgid "Blocked"
msgstr "Блокиран"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "По проект"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "По потребители"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Cancel"
msgstr "Отказ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Close"
msgstr "Затвори"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Closed"
msgstr "Затворено"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Closed Last 30 Days"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Closed Last 7 Days"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__is_closed
msgid "Closing Stage"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__comment
msgid "Comments"
msgstr "Коментари"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "Фирми"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_project_project_filter_inherit_industry_fsm
msgid "Company"
msgstr "Фирма"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "Конфигурация "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Confirm"
msgstr "Потвърждение"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: note that the duration "
"has automatically been rounded to 15 minutes.</i>"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Create"
msgstr "Създай"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Create Date"
msgstr "Задайте дата"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.open_create_project_fsm
msgid "Create a Project"
msgstr "Създайте проект"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Create reports to be signed off by your customers"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Custom Worksheets"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
#, python-format
msgid "Customer"
msgstr "Клиент"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Customer Preview"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_customer_ratings
msgid "Customer Ratings"
msgstr "Клиентски оценявания"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Date"
msgstr "Дата"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Days Spent"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "Краен срок"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Description"
msgstr "Описание"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Discard"
msgstr "Отхвърлете"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_enabled_conditions_count
msgid "Display Enabled Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_primary
msgid "Display Mark As Done Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_secondary
msgid "Display Mark As Done Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_satisfied_conditions_count
msgid "Display Satisfied Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_send_report_primary
msgid "Display Send Report Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_send_report_secondary
msgid "Display Send Report Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_sign_report_primary
msgid "Display Sign Report Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_sign_report_secondary
msgid "Display Sign Report Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Dissatisfied"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Do you want to stop the running timers?"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_effective
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Effective Hours"
msgstr "Ефективни часове"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_task_report
msgid "Email sent when clicking on \"send report\" in a task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_end
msgid "End date"
msgstr "Крайна дата"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "Крайна дата"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_project.py:0
#: code:addons/industry_fsm/models/res_company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#, python-format
msgid "Field Service"
msgstr ""

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Field Service: Intervention Scheduled"
msgstr ""

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_task_report
msgid "Field Service: Task Report"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
msgid "Find here your itinerary for today's tasks."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "Find here your upcoming tasks for the next few days."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__is_closed
msgid "Folded in Kanban stages are closing stages."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Followed Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Future"
msgstr "Бъдеще"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Give it a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Go back to your Field Service <b>task</b>."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Group By"
msgstr "Групиране по"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Hours Spent"
msgstr "Прекарани часове"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__id
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "In Progress"
msgstr "В процес"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Initially Planned Hours"
msgstr "Първоначално планирани часове"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_reached
msgid "Is Milestone Reached"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "Kanban State"
msgstr "Състояние Канбан"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard____last_update
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line____last_update
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm____last_update
msgid "Last Modified on"
msgstr "Последна промяна на"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "Последна фаза на актуализация"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Late Tasks"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will be closed.</i>"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__line_ids
msgid "Line"
msgstr "Ред"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "Маркирайте като готово"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_ir_ui_menu
msgid "Menu"
msgstr "Меню"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_id
msgid "Milestone"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_deadline
msgid "Milestone Deadline"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_milestones
msgid "Milestones"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Department's Projects"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Department's Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "My Favorite Projects"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Favorites Projects"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Projects"
msgstr "Моите проекти"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Tasks"
msgstr "Моите задачи"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Team's Projects"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Team's Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Navigate To"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "No Rating"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid "No tasks found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Not Starred"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Okay"
msgstr "Окей"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Open your <b>worksheet</b> in order to fill it in with the details of your "
"intervention."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__overtime
msgid "Overtime"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__parent_id
msgid "Parent Task"
msgstr "Основна задача"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Past"
msgstr "Отминал"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__personal_stage_type_ids
msgid "Personal Stage"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "Планиран"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_planned
msgid "Planned Hours"
msgstr "Планирани часове"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "Планиране"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planning_overlap
msgid "Planning Overlap"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "Приоритет"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "Развитие"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__project_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Project"
msgstr "Проект"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "Проекти"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Rated tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_text
msgid "Rating Last Text"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Rating Value (/5)"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
msgid "Ready"
msgstr "Готово"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Remaining Hours"
msgstr "Оставащи часове"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "Отчитане"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Satisfied"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Save time by automatically generating a <b>signature</b>."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Schedule your tasks and assign them to your workers."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Search planning"
msgstr ""

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Select the <b>customer</b> for your task."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Send Report"
msgstr "Изпратете отчет"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Send report"
msgstr ""

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_intervention_details
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "Настройки"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign"
msgstr "Подпишете"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Sign Report"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign Task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Signature"
msgstr "Подпис"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Липсва подпис."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr "Подписано от"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid ""
"Some employees are still running their timer for this task. Are you sure you"
" want to continue?"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Stage"
msgstr "Стадий"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "Стадии"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Starred"
msgstr "Отбелязано със звездичка"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Start Date"
msgstr "Начална Дата"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_begin
msgid "Start date"
msgstr "Начална дата"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Stop the <b>timer</b> when you are done."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_subtasks
msgid "Sub-tasks"
msgstr "Подзадачи"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__tag_ids
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "Маркери"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__task_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "Задача"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__fsm_done
msgid "Task Done"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.report,name:industry_fsm.task_custom_report
msgid "Task Report"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_type
msgid "Task Stage"
msgstr "Етап на задача"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_industry_fsm_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard
msgid "Task stop running timers confirmation wizard"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard_line
msgid "Task stop running timers confirmation wizard line"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_tasks_action_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Tasks"
msgstr "Задачи"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "Анализ на задачи"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks Soon in Overtime"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Tasks in Conflict"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "The worksheet has been signed"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "There are no reports to send."
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr "Време"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "Изразходвано време"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material Invoicing"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "График "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Timesheets"
msgstr "Графици"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "To Do"
msgstr "За извършване"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "To Schedule"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid ""
"To get things done, plan activities and use the task status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Today"
msgstr "Днес"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Unassigned"
msgstr "Неназначени"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Unread Messages"
msgstr "Непрочетени съобщения"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "Потребител"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Validate the <b>signature</b>."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__wizard_id
msgid "Wizard"
msgstr "Помощник"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Worker"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
msgid "Working Days to Assign"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Working Days to Close"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Worksheet"
msgstr "Работен график"

#. module: industry_fsm
#: model:mail.template,report_name:industry_fsm.mail_template_data_task_report
msgid ""
"Worksheet {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr ""

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.fsm_form_view_comment
msgid "comment"
msgstr "коментар"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm_inherited
msgid "e.g. Boiler replacement"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM ?"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.fsm_customer_ratings_server_action
msgid "project.project.fsm"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "working days to assign"
msgstr ""

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_task_report
msgid "{{ object.name }} Report"
msgstr ""
