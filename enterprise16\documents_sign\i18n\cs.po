# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_sign
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# ka<PERSON><PERSON><PERSON>chus<PERSON> <karolina.schus<PERSON><PERSON>@vdp.sk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Soubor podmínek a akcí, které budou k dispozici všem přílohám, které "
"odpovídají podmínkám"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Vytvořit"

#. module: documents_sign
#: code:addons/documents_sign/models/workflow.py:0
#, python-format
msgid "New templates"
msgstr "Nové šablony"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_direct
msgid "PDF to Sign"
msgstr "PDF k podpisu"

#. module: documents_sign
#: model:documents.workflow.rule,name:documents_sign.documents_sign_rule_sign_directly
msgid "Sign"
msgstr "e-podpis"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_new
msgid "Signature PDF Template"
msgstr "Šablona podpisu PDF"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr "Vyžádání podpisu"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr "Podpisová šablona"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Signed Document Tags"
msgstr "Podepsané značky dokumentu"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Signed Document Workspace"
msgstr "Podepsaný pracovní prostor dokumentu"
