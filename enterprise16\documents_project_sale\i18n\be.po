# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project_sale
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-23 08:33+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Belarusian (https://app.transifex.com/odoo/teams/41243/be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__project_template_use_documents
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__project_template_use_documents
msgid "Documents"
msgstr ""

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__documents_allowed_company_id
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__documents_allowed_company_id
msgid "Documents Allowed Company"
msgstr ""

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_3
msgid "Done/Archived"
msgstr ""

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_2
msgid "In Use"
msgstr ""

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_3
msgid "Miscellaneous"
msgstr "Рознае"

#. module: documents_project_sale
#: model:documents.tag,name:documents_project_sale.documents_folder_facet_1_tag_1
msgid "New/Unsorted"
msgstr ""

#. module: documents_project_sale
#: model:ir.model.fields,help:documents_project_sale.field_product_product__template_folder_id
#: model:ir.model.fields,help:documents_project_sale.field_product_template__template_folder_id
msgid ""
"On sales order confirmation, a workspace will be automatically generated for"
" the project based on this template."
msgstr ""

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_2
msgid "Photos"
msgstr ""

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template_child_1
msgid "Plans"
msgstr ""

#. module: documents_project_sale
#: model:ir.model,name:documents_project_sale.model_product_template
msgid "Product"
msgstr ""

#. module: documents_project_sale
#: model:product.template,name:documents_project_sale.product_1
msgid "Renovation Architect (Workspace Template)"
msgstr ""

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder
msgid "Renovation Projects"
msgstr ""

#. module: documents_project_sale
#: model:ir.model,name:documents_project_sale.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: documents_project_sale
#: model:documents.facet,name:documents_project_sale.documents_folder_facet_1
msgid "Status"
msgstr "Статус"

#. module: documents_project_sale
#: model:documents.folder,name:documents_project_sale.documents_folder_template
msgid "Template"
msgstr ""

#. module: documents_project_sale
#: model:ir.model.fields,field_description:documents_project_sale.field_product_product__template_folder_id
#: model:ir.model.fields,field_description:documents_project_sale.field_product_template__template_folder_id
msgid "Workspace Template"
msgstr ""
