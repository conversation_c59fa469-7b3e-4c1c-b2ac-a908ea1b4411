<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="demo_disallowed_expenses_category_1201" model="account.disallowed.expenses.category">
            <field name="name">Non-deductible taxes</field>
            <field name="code">1201</field>
        </record>
        <record id="demo_disallowed_expenses_category_1202" model="account.disallowed.expenses.category">
            <field name="name">Regional taxes, duties and fees</field>
            <field name="code">1202</field>
        </record>
        <record id="demo_disallowed_expenses_category_1203" model="account.disallowed.expenses.category">
            <field name="name">Fines, penalties and confiscations of any kind</field>
            <field name="code">1203</field>
        </record>
        <record id="demo_disallowed_expenses_category_1204" model="account.disallowed.expenses.category">
            <field name="name">Non-deductible pensions, capital, employer contributions and premiums</field>
            <field name="code">1204</field>
        </record>
        <record id="demo_disallowed_expenses_category_1205" model="account.disallowed.expenses.category">
            <field name="name">Car expenses and capital losses on non-deductible motor vehicles</field>
            <field name="code">1205</field>
        </record>
        <record id="demo_disallowed_expenses_category_1234" model="account.disallowed.expenses.category">
            <field name="name">Non-deductible mobility allowances</field>
            <field name="code">1234</field>
        </record>
        <record id="demo_disallowed_expenses_category_1206" model="account.disallowed.expenses.category">
            <field name="name">Car expenses up to a portion of the benefit in kind</field>
            <field name="code">1206</field>
        </record>
        <record id="demo_disallowed_expenses_category_1235" model="account.disallowed.expenses.category">
            <field name="name">Expenses of the mobility allowance up to a proportion of the benefit kind</field>
            <field name="code">1235</field>
        </record>
        <record id="demo_disallowed_expenses_category_1207" model="account.disallowed.expenses.category">
            <field name="name">Hospitality expenses and non-deductible business gifts</field>
            <field name="code">1207</field>
        </record>
        <record id="demo_disallowed_expenses_category_1208" model="account.disallowed.expenses.category">
            <field name="name">Non-deductible restaurant expenses</field>
            <field name="code">1208</field>
        </record>
        <record id="demo_disallowed_expenses_category_1209" model="account.disallowed.expenses.category">
            <field name="name">Non-specific professional clothing expenses</field>
            <field name="code">1209</field>
        </record>
        <record id="demo_disallowed_expenses_category_1210" model="account.disallowed.expenses.category">
            <field name="name">Exaggerated interests</field>
            <field name="code">1210</field>
        </record>
        <record id="demo_disallowed_expenses_category_1211" model="account.disallowed.expenses.category">
            <field name="name">Interest related to a portion of certain borrowings</field>
            <field name="code">1211</field>
        </record>
        <record id="demo_disallowed_expenses_category_1212" model="account.disallowed.expenses.category">
            <field name="name">Abnormal or voluntary benefits</field>
            <field name="code">1212</field>
        </record>
        <record id="demo_disallowed_expenses_category_1214" model="account.disallowed.expenses.category">
            <field name="name">Social benefits</field>
            <field name="code">1214</field>
        </record>
        <record id="demo_disallowed_expenses_category_1215" model="account.disallowed.expenses.category">
            <field name="name">Benefits of meal vouchers, sports/culture vouchers or eco-vouchers</field>
            <field name="code">1215</field>
        </record>
        <record id="demo_disallowed_expenses_category_1216" model="account.disallowed.expenses.category">
            <field name="name">Liberalities</field>
            <field name="code">1216</field>
        </record>
        <record id="demo_disallowed_expenses_category_1217" model="account.disallowed.expenses.category">
            <field name="name">Reductions in value and losses on shares or parts</field>
            <field name="code">1217</field>
        </record>
        <record id="demo_disallowed_expenses_category_1218" model="account.disallowed.expenses.category">
            <field name="name">Resumptions of previous exemptions</field>
            <field name="code">1218</field>
        </record>
        <record id="demo_disallowed_expenses_category_1233" model="account.disallowed.expenses.category">
            <field name="name">Employee participation and beneficiary bonuses</field>
            <field name="code">1233</field>
        </record>
        <record id="demo_disallowed_expenses_category_1220" model="account.disallowed.expenses.category">
            <field name="name">Compensation for missing coupon</field>
            <field name="code">1220</field>
        </record>
        <record id="demo_disallowed_expenses_category_1232" model="account.disallowed.expenses.category">
            <field name="name">Expenses of works approved tax shelter</field>
            <field name="code">1232</field>
        </record>
        <record id="demo_disallowed_expenses_category_1222" model="account.disallowed.expenses.category">
            <field name="name">Bonuses, subsidies in capital and in regional interest</field>
            <field name="code">1222</field>
        </record>
        <record id="demo_disallowed_expenses_category_1223" model="account.disallowed.expenses.category">
            <field name="name">Non-deductible payments to certain States</field>
            <field name="code">1223</field>
        </record>
        <record id="demo_disallowed_expenses_category_1225" model="account.disallowed.expenses.category">
            <field name="name">Unjustified expenses</field>
            <field name="code">1225</field>
        </record>
        <record id="demo_disallowed_expenses_category_1230" model="account.disallowed.expenses.category">
            <field name="name">Reversal of deduction for innovation income in the event of staggering of historical costs</field>
            <field name="code">1230</field>
        </record>
        <record id="demo_disallowed_expenses_category_1231" model="account.disallowed.expenses.category">
            <field name="name">Resumption of deduction for innovation income following non-reinvestment in qualifying expenses</field>
            <field name="code">1231</field>
        </record>
        <record id="demo_disallowed_expenses_category_1226" model="account.disallowed.expenses.category">
            <field name="name">Positive corrections in application under Diamond Scheme. Positive difference between the gross profit determined on a flat-rate basis and the gross profit determined on an accounting basis</field>
            <field name="code">1226</field>
        </record>
        <record id="demo_disallowed_expenses_category_1227" model="account.disallowed.expenses.category">
            <field name="name">Write-down on inventory and non-deductible costs</field>
            <field name="code">1227</field>
        </record>
        <record id="demo_disallowed_expenses_category_1228" model="account.disallowed.expenses.category">
            <field name="name">Positive difference between the reference income for a corporate executive and the income of the highest corporate executive</field>
            <field name="code">1228</field>
        </record>
        <record id="demo_disallowed_expenses_category_1229" model="account.disallowed.expenses.category">
            <field name="name">Correction based on the minimum taxable income from diamond trading </field>
            <field name="code">1229</field>
        </record>
        <record id="demo_disallowed_expenses_category_1239" model="account.disallowed.expenses.category">
            <field name="name">Other disallowed expenses</field>
            <field name="code">1239</field>
        </record>
    </data>
</odoo>
