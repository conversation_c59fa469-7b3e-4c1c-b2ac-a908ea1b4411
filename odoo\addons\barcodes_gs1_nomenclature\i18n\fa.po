# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes_gs1_nomenclature
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:50+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__alpha
msgid "Alpha-Numeric Name"
msgstr "نام الفا عددی"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid ""
"Alternative regex delimiter for the FNC1. The separator must not match the "
"begin/end of any related rules pattern."
msgstr ""
"جداکننده regex جایگزین برای FNC1. جداکننده نباید با شروع/پایان هیچ الگوی "
"قوانین مرتبط مطابقت داشته باشد."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__associated_uom_id
msgid "Associated Uom"
msgstr "مرتبط Uom"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_nomenclature
msgid "Barcode Nomenclature"
msgstr "نامگذاری بارکد"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_rule
msgid "Barcode Rule"
msgstr "قانون بارکد"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__use_date
msgid "Best before Date"
msgstr "بهترین تاریخ مصرف"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__date
msgid "Date"
msgstr "تاریخ"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid "Decimal"
msgstr "عددی"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location_dest
msgid "Destination location"
msgstr "مکان مقصد"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid "Encoding"
msgstr "رمزگذاری"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__expiration_date
msgid "Expiration Date"
msgstr "تاریخ انقضا"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid "FNC1 Separator"
msgstr "جداکننده FNC1"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid "GS1 Content Type"
msgstr "نوع محتوای GS1"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__encoding__gs1-128
msgid "GS1-128"
msgstr "GS1-128"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid ""
"If True, use the last digit of AI to determine where the first decimal is"
msgstr ""
"اگر درست است، از آخرین رقم هوش مصنوعی برای تعیین اینکه اولین اعشار کجاست "
"استفاده کنید"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: can't be formated as date"
msgstr "بارکد نامعتبر: نمی توان آن را به عنوان تاریخ قالب بندی کرد"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: the check digit is incorrect"
msgstr "بارکد نامعتبر: رقم بررسی نادرست است"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid "Is GS1 Nomenclature"
msgstr "نامگذاری GS1 است"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location
msgid "Location"
msgstr "مکان"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "سری ساخت"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__measure
msgid "Measure"
msgstr "ندازه"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__identifier
msgid "Numeric Identifier"
msgstr "شناسه عددی"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__pack_date
msgid "Pack Date"
msgstr "تاریخ بسته بندی"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package
msgid "Package"
msgstr "بسته"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package_type
msgid "Package Type"
msgstr "نوع بسته‌بندی"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__quantity
msgid "Quantity"
msgstr "تعداد"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid "The FNC1 Separator Alternative is not a valid Regex: "
msgstr "جایگزین جداکننده FNC1 یک Regex معتبر نیست:"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid ""
"The GS1 content type defines what kind of data the rule will process the "
"barcode as:        * Date: the barcode will be converted into a Odoo "
"datetime;        * Measure: the barcode's value is related to a specific "
"UoM;        * Numeric Identifier: fixed length barcode following a specific "
"encoding;        * Alpha-Numeric Name: variable length barcode."
msgstr ""
"نوع محتوای GS1 تعیین می‌کند که قاعده بارکد را به چه نوع داده‌ای پردازش خواهد کرد:تاریخ: بارکد به یک تاریخ و زمان در Odoo تبدیل خواهد شد؛\n"
"اندازه: مقدار بارکد مربوط به یک واحد اندازه‌گیری خاص است؛\n"
"شناسه عددی: بارکد با طول ثابت که از یک کدگذاری خاص پیروی می‌کند؛\n"
"نام الفا-عدد: بارکد با طول متغیر."

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid "The rule pattern \"%s\" is not a valid Regex: "
msgstr "الگوی قاعده \"%s\" یک عبارت باقاعده معتبر نیست:"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid ""
"The rule pattern \"%s\" is not valid, it needs two groups:\n"
"\t- A first one for the Application Identifier (usually 2 to 4 digits);\n"
"\t- A second one to catch the value."
msgstr ""
"الگوی قاعده \"%s\" نامعتبر است و به دو گروه نیاز دارد:\n"
"\n"
"\tگروه اول برای شناسه برنامه (معمولاً 2 تا 4 رقم)؛\n"
"\tگروه دوم برای دریافت مقدار."

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid ""
"There is something wrong with the barcode rule \"%s\" pattern.\n"
"If this rule uses decimal, check it can't get sometime else than a digit as last char for the Application Identifier.\n"
"Check also the possible matched values can only be digits, otherwise the value can't be casted as a measure."
msgstr ""
"مشکلی در الگوی قانون بارکد \"%s\" وجود دارد.\n"
"اگر این قانون از اعداد اعشاری استفاده می‌کند، بررسی کنید که نمی‌تواند غیر از یک رقم به عنوان آخرین کاراکتر برای شناسه برنامه دریافت کند.\n"
"همچنین بررسی کنید که مقادیر مطابقت یافته تنها می‌توانند ارقام باشند، در غیر این صورت مقدار نمی‌تواند به عنوان یک اندازه تبدیل شود."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid ""
"This Nomenclature use the GS1 specification, only GS1-128 encoding rules is "
"accepted is this kind of nomenclature."
msgstr ""
"این نامگذاری از مشخصات GS1 استفاده می کند، تنها قوانین رمزگذاری GS1-128 "
"پذیرفته شده است که این نوع نامگذاری است."

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be parsed by any barcode rules."
msgstr "این بارکد با هیچ قانون بارکد قابل تجزیه نیست."

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be partially or fully parsed."
msgstr "این بارکد را نمی توان به طور جزئی یا کامل تجزیه کرد."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"این قانون فقط در صورتی اعمال می شود که بارکد با کدگذاری مشخص شده کدگذاری شده"
" باشد"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__type
msgid "Type"
msgstr "نوع"
