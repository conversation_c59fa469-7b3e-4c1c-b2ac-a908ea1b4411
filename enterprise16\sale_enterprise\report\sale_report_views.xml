<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="sale_report_view_pivot" model="ir.ui.view">
        <field name="name">sale.report.pivot</field>
        <field name="model">sale.report</field>
        <field name="arch" type="xml">
            <pivot string="Sales Analysis" display_quantity="1" sample="1">
                <field name="categ_id" type="row"/>
                <field name="order_id" type="measure"/>
                <field name="price_subtotal" type="measure"/>
                <field name="price_total" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="sale_report_view_tree" model="ir.ui.view">
        <field name="name">sale.report.view.tree.inherit.sale.enterprise</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.sale_report_view_tree"/>
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="invoice_status" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_order_product_search_inherit" model="ir.ui.view">
        <field name="name">view.order.product.search.inherit</field>
        <field name="mode">primary</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.view_order_product_search"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="before">
                <filter name="to_invoice" string="To Invoice" domain="[('invoice_status', '=', 'to invoice')]"/>
                <filter name="fully_invoiced" string="Fully Invoiced" domain="[('invoice_status', '=', 'invoiced')]"/>
            </field>
        </field>
    </record>

</odoo>
