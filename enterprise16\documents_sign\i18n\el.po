# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_sign
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2019-01-16 08:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_ir_attachment
msgid "Attachment"
msgstr "Συνημμένο"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Δημιουργία"

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_request__documents_tag_ids
msgid "Document Tags"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_request__folder_id
msgid "Document Workspace"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents_sign
#: model:documents.workflow.rule,name:documents_sign.documents_sign_rule_sign_request
#: model:documents.workflow.rule,name:documents_sign.documents_sign_rule_sign_request_finance
msgid "Request a Signature"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Signed Document Tags"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Signed Document Workspace"
msgstr ""

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr "Εργασία"

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor Credit Note"
msgstr "Πιστωτικό Προμηθευτή"

#. module: documents_sign
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""
