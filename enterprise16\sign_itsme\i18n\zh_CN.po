# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign_itsme
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>EN <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 10:53+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid ""
"<small>Name: The signatory has provided this identity through itsme®</small>"
msgstr "<small>名称：签名人通过 itsme® 提供身份验证</small>"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid "Confirm your identity"
msgstr "确认您的身份"

#. module: sign_itsme
#: model:sign.item.role,name:sign_itsme.sign_item_role_itsme_customer
msgid "Customer (identified with itsme®)"
msgstr "客户 ( 用 itsme® 身份验证 )"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "额外的身份验证步骤"

#. module: sign_itsme
#: model:ir.model.fields,help:sign_itsme.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr "强制签名人使用第二种身份验证方法进行身份验证"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Go Back"
msgstr "反回"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid "Identification refused"
msgstr "身份认证被拒"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Identify with itsme"
msgstr "用 itsme 身份验证"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid "Name"
msgstr "名称"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/xml/templates.xml:0
#, python-format
msgid "Please confirm your identity to finalize your signature."
msgstr "请确认您的身份以完成您的签字。"

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/models/sign_request_item.py:0
#, python-format
msgid "Sign request item is not validated yet."
msgstr "签名请求项目尚未验证。"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_item_role
msgid "Signature Item Party"
msgstr "签字项方"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request_item
msgid "Signature Request Item"
msgstr "签字请求项"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid ""
"The itsme® identification data could not be forwarded to Odoo, the signature"
" could not be saved."
msgstr "itsme®身份验证数据无法转发给Odoo，签字无法保存。"

#. module: sign_itsme
#: model:ir.model.fields.selection,name:sign_itsme.selection__sign_item_role__auth_method__itsme
msgid "Via itsme®"
msgstr "通过itsme®"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/js/document_signable.js:0
#, python-format
msgid ""
"You have rejected the identification request or took too long to process it."
" You can try again to finalize your signature."
msgstr "您拒绝了身份验证请求，或身份验证处理时间过长。您可以再次尝试签字。"

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/controllers/main.py:0
#, python-format
msgid "itsme® IAP service could not be found."
msgstr "无法找到 itsme® IAP 服务"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_birthdate
msgid "itsme® Signer's Birthdate"
msgstr "itsme® 签字人出生日期"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_name
msgid "itsme® Signer's Name"
msgstr "itsme® 签字人名字"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_validation_hash
msgid "itsme® Validation Token"
msgstr "itsme® 验证令牌"
