# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bulk_sales_price_change
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-14 18:00+0000\n"
"PO-Revision-Date: 2024-04-14 18:00+0000\n"
"Last-Translator: \n"
"Language-Team: Arabic\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__adjustment_type
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__adjustment_type
msgid "Adjustment Type"
msgstr "نوع التعديل"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__adjustment_value
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__adjustment_value
msgid "Adjustment Value"
msgstr "قيمة التعديل"

#. module: bulk_sales_price_change
#: model:ir.actions.act_window,name:bulk_sales_price_change.action_bulk_price_update_wizard
#: model:ir.ui.menu,name:bulk_sales_price_change.menu_bulk_price_root
msgid "Bulk Price Update"
msgstr "تحديث الأسعار بالجملة"

#. module: bulk_sales_price_change
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_update_wizard_form
msgid "Are you sure you want to update these prices?"
msgstr "هل أنت متأكد من تحديث هذه الأسعار؟"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history_line__price_difference
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_preview_line__price_difference
msgid "Difference"
msgstr "الفرق"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_history__adjustment_type__fixed
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__adjustment_type__fixed
msgid "Fixed Amount"
msgstr "مبلغ ثابت"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_history__operation_type__increase
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__operation_type__increase
msgid "Increase"
msgstr "زيادة"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_history__operation_type__decrease
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__operation_type__decrease
msgid "Decrease"
msgstr "تخفيض"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history_line__new_price
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_preview_line__new_price
msgid "New Price"
msgstr "السعر الجديد"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history_line__old_price
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_preview_line__old_price
msgid "Current Price"
msgstr "السعر الحالي"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__operation_type
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__operation_type
msgid "Operation Type"
msgstr "نوع العملية"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_history__adjustment_type__percentage
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__adjustment_type__percentage
msgid "Percentage"
msgstr "نسبة مئوية"

#. module: bulk_sales_price_change
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_update_wizard_form
msgid "Preview"
msgstr "معاينة"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__line_ids
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_history_form
msgid "Price Update Lines"
msgstr "بنود تحديث الأسعار"

#. module: bulk_sales_price_change
#: model:ir.actions.act_window,name:bulk_sales_price_change.action_bulk_price_history
#: model:ir.ui.menu,name:bulk_sales_price_change.menu_bulk_price_history
msgid "Price Update History"
msgstr "سجل تحديث الأسعار"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history_line__product_id
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_preview_line__product_id
msgid "Product"
msgstr "المنتج"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__category_ids
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__category_ids
msgid "Product Categories"
msgstr "فئات المنتجات"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__tag_ids
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__tag_ids
msgid "Product Tags"
msgstr "علامات المنتج"

#. module: bulk_sales_price_change
#: model:ir.model.fields,help:bulk_sales_price_change.field_bulk_price_update_wizard__adjustment_value
msgid "For percentage: enter value between 0-100. For fixed amount: enter the amount to adjust."
msgstr "للنسبة المئوية: أدخل قيمة بين 0-100. للمبلغ الثابت: أدخل المبلغ المراد تعديله."

#. module: bulk_sales_price_change
#: model:ir.model.fields,help:bulk_sales_price_change.field_bulk_price_update_wizard__category_ids
msgid "Select categories to filter products. Leave empty to apply to all categories."
msgstr "حدد الفئات لتصفية المنتجات. اتركه فارغاً للتطبيق على جميع الفئات."

#. module: bulk_sales_price_change
#: model:ir.model.fields,help:bulk_sales_price_change.field_bulk_price_update_wizard__tag_ids
msgid "Select tags to filter products. Leave empty to apply to all tagged products."
msgstr "حدد العلامات لتصفية المنتجات. اتركه فارغاً للتطبيق على جميع المنتجات المعلمة."

#. module: bulk_sales_price_change
#: model:res.groups,name:bulk_sales_price_change.group_bulk_price_update
msgid "Bulk Price Update"
msgstr "تحديث الأسعار بالجملة"

#. module: bulk_sales_price_change
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_update_wizard_form
msgid "Apply Changes"
msgstr "تطبيق التغييرات"

#. module: bulk_sales_price_change
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_update_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. module: bulk_sales_price_change
#: model:ir.ui.menu,name:bulk_sales_price_change.menu_bulk_price_update_wizard
msgid "Update Prices"
msgstr "تحديث الأسعار"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_history__total_products
msgid "Total Products Updated"
msgstr "إجمالي المنتجات المحدثة"

#. module: bulk_sales_price_change
#: code:addons/bulk_sales_price_change/wizards/bulk_price_update.py:0
#, python-format
msgid "No products found matching the selected criteria."
msgstr "لم يتم العثور على منتجات تطابق المعايير المحددة."

#. module: bulk_sales_price_change
#: code:addons/bulk_sales_price_change/wizards/bulk_price_update.py:0
#, python-format
msgid "Success"
msgstr "نجاح"

#. module: bulk_sales_price_change
#: code:addons/bulk_sales_price_change/wizards/bulk_price_update.py:0
#, python-format
msgid "Prices have been updated successfully!"
msgstr "تم تحديث الأسعار بنجاح!"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__rounding_method
msgid "Rounding Method"
msgstr "طريقة التقريب"

#. module: bulk_sales_price_change
#: model:ir.model.fields,field_description:bulk_sales_price_change.field_bulk_price_update_wizard__decimal_places
msgid "Decimal Places"
msgstr "المنازل العشرية"

#. module: bulk_sales_price_change
#: model:ir.model.fields,help:bulk_sales_price_change.field_bulk_price_update_wizard__decimal_places
msgid "Number of decimal places to round to"
msgstr "عدد المنازل العشرية للتقريب إليها"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__rounding_method__no_round
msgid "No Rounding"
msgstr "بدون تقريب"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__rounding_method__round_up
msgid "Round Up"
msgstr "تقريب لأعلى"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__rounding_method__round_down
msgid "Round Down"
msgstr "تقريب لأسفل"

#. module: bulk_sales_price_change
#: model:ir.model.fields.selection,name:bulk_sales_price_change.selection__bulk_price_update_wizard__rounding_method__round_half_up
msgid "Round to Nearest (0.5 → Up)"
msgstr "تقريب لأقرب رقم (0.5 → لأعلى)"

#. module: bulk_sales_price_change
#: model_terms:ir.ui.view,arch_db:bulk_sales_price_change.view_bulk_price_update_wizard_form
msgid "Rounding Options"
msgstr "خيارات التقريب" 