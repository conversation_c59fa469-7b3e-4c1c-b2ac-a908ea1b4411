from odoo import models, fields, api ,exceptions
from datetime import datetime

class SaleOrder(models.Model):
    _inherit = 'sale.order'



    def _get_order_lines_to_report(self):
        down_payment_lines = self.order_line.filtered(lambda line:
            line.is_downpayment
            and not line.display_type
            and not line._get_downpayment_state()
        )


        def show_line(line):
            if not line.is_downpayment:
                return True
            elif line.display_type and down_payment_lines:
                return True  # Only show the down payment section if down payments were posted
            elif line in down_payment_lines:
                return True  # Only show posted down payments
            else:
                return False
        lines=self.order_line.filtered(lambda l:not l.product_id.is_discount_product)


        return lines.filtered(show_line)

