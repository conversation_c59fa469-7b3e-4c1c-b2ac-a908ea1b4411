# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr "事例の数"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "%(number of missed calls)s missed calls"
msgstr "%(不在着信数) の不在着信"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "1 missed call"
msgstr "不在着信1"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "2 missed calls"
msgstr "不在着信2"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"A hardware error occured while trying to access audio recording device. "
"Please make sure your drivers are up to date and try again."
msgstr "オーディオ録音デバイスへのアクセス中にハードウェアエラーが発生しました。ドライバが最新であることを確認し、再度お試し下さい。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Accept"
msgstr "受理"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr "活動"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Add to Call Queue"
msgstr "コールキューに追加"

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr "通話キューに追加"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occured involving the audio recording device "
"(%(errorName)s):</br>%(errorMessage)s"
msgstr "音声録音デバイスでエラーが発生しました (%(errorName)s):</br>%(errorMessage)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"An error occurred during the instantiation of the User Agent:</br></br> "
"%(error message)s"
msgstr "ユーザエージェントのインスタンス生成中にエラーが発生しました:</br></br> %(エラーメッセージ)"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Are you sure that you want to close this website? There's a call ongoing."
msgstr "このウェブサイトを閉じてもよろしいですか?進行中の電話があります。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr "尋ねる"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr "アバター"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr "バックスペース"

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr "通話"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr "呼び出し日"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr "通話名"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call duration: %(min)smin %(sec)ssec"
msgstr "通話時間: %(min)s分 %(sec)s秒"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr "他のデバイスから電話する"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Call rejected (reason: \"%(reasonPhrase)s\")"
msgstr "着信拒否 (理由: \"%(reasonPhrase)s\")"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr "%sへの呼び出し"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/voip_service.js:0
#, python-format
msgid "Calling %s"
msgstr "%sを呼び出す"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr "呼び出し日"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr "アクティビティをキャンセルする"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr "取消済"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please grant it and try again. Otherwise, make sure this website"
" runs over HTTPS and that your browser is not set to deny access to media "
"devices."
msgstr ""
"音声録音デバイスにアクセスできません。マイクへのアクセスを拒否している場合は、許可してからもう一度お試しください。そうでない場合は、このウェブサイトがHTTPSで実行され、ブラウザがメディアデバイスへのアクセスを拒否するよう設定されていないことを確認して下さい。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone must be used\n"
"        "
msgstr ""
"モバイルアプリケーションを使用する際に電話をかける方法を選択してください：\n"
"- VoIP： 常にOdooソフトフォンを使用\n"
"- デバイス電話：常にデバイス電話を使用\n"
"- 確認：ソフトフォンかデバイス電話のどちらを使用するか常に確認"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr "クリックしてブロックを解除します"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr "閉じる"

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Connecting..."
msgstr "接続しています..."

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr "連絡先"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Contacts"
msgstr "連絡先"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr "作成日"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr "顧客"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr "日付"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr "デモ"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr "デバイスの電話"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__direction
msgid "Direction"
msgstr "方向"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr "ディスプレイダイヤルパネル"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
msgid "Display Name"
msgstr "表示名"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr "ドキュメント"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr "スケジュールしない"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr "期日"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr "経過時間"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr "分単位の期間。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Edit"
msgstr "編集"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "End Call"
msgstr "通話終了"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Enter number or name"
msgstr "番号または名前を入力してください"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr "番号を入力してください..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr "外部デバイス番号"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Failed to start the user agent. The URL of the websocket server may be "
"wrong. Please have an administrator verify the websocket server URL in the "
"General Settings."
msgstr ""
"ユーザエージェントの起動に失敗しました。ウェブソケットサーバのURLが間違っている可能性があります。管理者が一般設定よりウェブソケットサーバのURLを確認して下さい。"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr "電話のリストを表示するときの順序を示します。"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr "グループ化"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr "電話を切りますが、通話をキューに入れます"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr "保留"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr "携帯電話での通話方法"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr "有効にすると、Odooで着信が自動的に拒否されます。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__should_call_from_another_device
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr ""
"有効にすると、Odooで電話をかける場合に「外部デバイス番号」に電話が転送されます。このオプションを使用すると、Odooで電話をかけても、他のデバイス（例：デスクホンなど）から対応することができます。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__external_device_number
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr "\"別のデバイスから電話をかける\" オプションが有効な場合、Odooでかけた電話はこの電話番号に転送されます。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr "1週間後"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr "15日後"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr "2ヶ月後"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr "通話キュー内"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr "求めて:"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr "通話をフォローアップするために、別の通話、会議のリクエストをトリガーできます。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__incoming
msgid "Incoming"
msgstr "着信"

#. module: voip
#. odoo-python
#. odoo-javascript
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr "%sからの着信"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr "%s(%s)からの着信"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Incoming call from..."
msgstr "からの着信..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "コールキューの有無"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr "キーパッド"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr "最後に見た電話"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr "リンクされたアクティビティ"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr "リンクされたおしゃべりメッセージ"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr "リンクされたVoIP電話"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr "電話の概要をログに記録する"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr "記録された電話"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Make a call using:"
msgstr "以下を使用して電話をかける:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr "完了とする"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr "不在"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr "%sからの不在着信"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr "携帯電話"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr "消毒された携帯電話番号"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mobile:"
msgstr "携帯電話:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr "ミュート"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr "私の電話"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Next activities"
msgstr "次の活動"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr "音声録音デバイスがありません。このアプリケーションを使用するにはマイクが必要です。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr "未保留"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr "ノート"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""
"Odooを使用すると、インバウンドコールをその場で記録して、\n"
"顧客との通信の履歴を追跡したり、\n"
"別のチームメンバーに通知したりできます。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__direction__outgoing
msgid "Outgoing"
msgstr "送信待ち"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr "PBXサーバIP"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr "PBXまたはWebsocketアドレスがありません。設定を確認してください。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr "保留"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
#, python-format
msgid "Phone"
msgstr "電話"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr "消毒された電話番号"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/components/activity/activity.xml:0
#, python-format
msgid "Phone:"
msgstr "電話:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr "電話の詳細"

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr "通話"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr "マイクの使用を承諾してください。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr "後でもう一度試してください。問題が解決しない場合は、管理者に設定を確認してもらいましょう。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr "製造"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Recent"
msgstr "最近の"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr "パネルを更新する"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr "登録が拒否されました: %(statusCode)s %(reasonPhrase)s."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject"
msgstr "拒否"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject call"
msgstr "通話を拒否する"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr "着信を拒否"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr "拒否済"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr "%sからの着信を拒否しました"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Remember ?"
msgstr "記録しますか?"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/call_queue_switch_view.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr "コールキューから削除"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr "キューから削除します"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr "担当者"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr "リンギング..."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from your database"
msgstr "データベースから電話をスケジュールして発信する"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr "新しいアクティビティをスケジュールする"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr "活動をスケジュール　"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Next"
msgstr "次のスケジュール"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search"
msgstr "検索"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr "通話の検索"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr "メール送信"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr "付番"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone"
" number set: %(record_names)s"
msgstr "電話番号が設定されていないため、コールキューに追加できないドキュメントがあります。: %(record_names)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr "日付指定"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr "始まる時間"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr "ステータス"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr "サマリ"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Take call"
msgstr "電話をかける"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP address of your PBX Server"
msgstr "PBXサーバのIPアドレス"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr "ウェブソケットのURL"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr "WebSocket 接続が失われ、再確立できませんでした。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The connection cannot be made.</br> Please check your configuration."
msgstr "接続できません。</br>設定を確認してください。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr ""
"このエラーはトランスポート層から発生している可能性があります。管理者は一般設定でウェブソケットサーバーのURLを確認して下さい。問題が解決しない場合は、サーバーの問題である可能性があります。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.</br> (Reason "
"received: %(reasonPhrase)s)"
msgstr "番号が正しくないか、ユーザー認証が間違っているか、接続できない可能性があります。設定を確認して下さい: %(reasonPhrase)s)"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_secret
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr "PBXサーバーでの登録に使用するパスワードです。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The person you try to contact is currently unavailable."
msgstr "連絡しようとした相手は現在不在です。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr "電話番号はありません"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/models/registerer.js:0
#, python-format
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr ""
"サーバの認証に失敗しました。正しいサーバ(一般設定のPBXサーバーIP)に到達していること、およびユーザ設定の認証情報が正しいことを管理者に確認してもらって下さい。"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""
"通話が作成されると、ステータスはToDoに設定されます。\n"
"通話が終了すると、ステータスは保留に設定されます。\n"
"通話が適用されなくなった場合は、ステータスを'キャンセル済み'に設定できます。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__voip_username
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr "PBXサーバーへの登録に使用するユーザー名（通常は内線番号）です。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr "サーバへのウェブソケット接続が切断されました。接続を再確立を試みています..."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr "未処理"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr "明日"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr "運送"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr "未割当"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr "ユーザ設定"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr "VOIP電話"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr "VOIP電話ログウィザード"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr "ユーザーレポートによるVoIP電話"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr "VOIPキューサポート"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
#, python-format
msgid "VoIP"
msgstr "VoIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr "VoIP設定"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr "VoIP環境"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr "VoIP secret"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr "VoIP ユーザー名 / 内線番号"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Voip"
msgstr "VoIP"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr "ウェブソケット"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr "あなたはすでに電話中です"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid ""
"You must allow the access to the microphone on your device. Otherwise, the "
"VoIP call receiver will not hear you."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr "お使いのブラウザはWebRTCをサポートできませんでした。構成を確認してください。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr "資格情報が正しく設定されていません。管理者に連絡してください。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr "最小"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr "秒"
