<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.cron" id="web_hotel_reservation_draft_deletion">
            <field name="name">Hotel Reservation Draft</field>
            <field name="model_id" ref="hotel_management.model_hotel_reservation"/>
            <field name="state">code</field>
            <field name="code">model.hotel_reservation_web_unlink()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="active" eval="True"/>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>
        <record model="ir.cron" id="web_hotel_reservation_draft_scheduler">
            <field name="name">Hotel Reservation Scheduler</field>
            <field name="model_id" ref="hotel_management.model_hotel_reservation"/>
            <field name="state">code</field>
            <field name="code">model.state_scheduler()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active" eval="True"/>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>