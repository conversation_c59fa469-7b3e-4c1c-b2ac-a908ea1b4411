# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# abc <PERSON>f <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:37+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_kanban_inherit_industry_fsm_report
msgid ""
"<span class=\"fa fa-pencil me-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"
msgstr ""
"<span class=\"fa fa-pencil me-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "<span class=\"o_label ms-2\">Worksheets</span>"
msgstr "<span class=\"o_label ms-2\">Çalışma Sayfaları</span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_gantt_fsm_worksheet
msgid "<strong>Template — </strong>"
msgstr "<strong>Şablon — </strong>"

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet Template"
msgstr "Çalışma Sayfası Şablonuna Göre"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_color
msgid "Color"
msgstr "Renk"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_2
msgid "Comments"
msgstr "Yorumlar"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Create reports to be signed off by your customers"
msgstr "Müşterileriniz tarafından imzalanacak raporlar oluşturun"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_template_id
msgid ""
"Create templates for each type of intervention you have and customize their "
"content with your own custom fields."
msgstr ""
"Sahip olduğunuz her müdahale türü için şablonlar oluşturun ve içeriklerini "
"kendi özel alanlarınızla özelleştirin."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_2_ir_ui_view_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_3_ir_ui_view_3
msgid "Created on"
msgstr "Oluşturulma"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "Customize worksheet templates for each type of intervention.<br>"
msgstr "Her müdahale türü için çalışma sayfası şablonlarını özelleştirin.<br>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Date"
msgstr "Tarih"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.x_project_task_worksheet_template_2_ir_model_1
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
msgid "Default Worksheet"
msgstr "Ön tanımlı çalışma sayfası "

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Description of the Intervention"
msgstr "Müdahalenin Tanımı"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_1
msgid "Device Installation and Maintenance"
msgstr "Cihaz Kurulumu ve Bakımı"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "Dropdown menu"
msgstr "Aşağı Açılır Menü"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "Görevlerin Analizi"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid ""
"I hereby certify that this device meets the requirements of an acceptable "
"device at the time of testing."
msgstr ""
"Bu cihazın test sırasında kabul edilebilir bir cihazın gereksinimlerini "
"karşıladığını onaylarım."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Intervention Type"
msgstr "Müdahale Türü"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Manufacturer"
msgstr "Üretici"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Model"
msgstr "Model"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "No tasks found. Let's create one!"
msgstr "Görev bulunamadı. Hadi bir tane oluşturalım!"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr "Çalışma sayfası şablonu bulunamadı. Hadi bir tane oluşturalım!"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Planning by Worksheet Template"
msgstr "Çalışma Sayfası Şablonuna Göre Planlama"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr "Proje"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Schedule your tasks and assign them to your workers."
msgstr "Görevlerinizi planlayın ve çalışanlarınıza atayın."

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.action_fsm_task_send_report
msgid "Send Report"
msgstr "Rapor Gönder"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Serial Number"
msgstr "Seri Numarası"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
msgid "Task"
msgstr "Görev"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Görev Tekrarı"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr "Görev Çalışma Sayfası Özel Raporu"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Worker Signature"
msgstr "İşçi İmzası"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr "Çalışma Sayfası"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_count
msgid "Worksheet Count"
msgstr "Çalışma Sayfası Sayısı"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_project_task_user_fsm__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_view_tree
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_map_view_inherit_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_project_task_user_fsm_view_search
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_project_filter_inherit_industry_fsm_report
msgid "Worksheet Template"
msgstr "Çalışma Sayfası Şablonu"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr "Çalışma Sayfası Şablonları"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.x_project_task_worksheet_template_2_ir_actions_act_window_1
#: model:ir.actions.act_window,name:industry_fsm_report.x_project_task_worksheet_template_3_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Çalışma Sayfası"
