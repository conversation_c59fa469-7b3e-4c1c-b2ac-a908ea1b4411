<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View for Reconciliation Report -->
    <record id="view_biotime_reconciliation_report_tree" model="ir.ui.view">
        <field name="name">biotime.reconciliation.report.tree</field>
        <field name="model">biotime.reconciliation.report</field>
        <field name="arch" type="xml">
            <tree string="Attendance Reconciliation" decoration-danger="status in ('biotime_only', 'missing_checkout', 'missing_checkin')" decoration-warning="status=='time_mismatch'" decoration-success="status=='matched'" export_xlsx="1" create="false" delete="false" edit="false" default_order="date desc, employee_id">
                <field name="employee_id"/>
                <field name="date"/>
                <field name="biotime_check_in"/>
                <field name="biotime_check_out"/>
                <field name="odoo_check_in"/>
                <field name="odoo_check_out"/>
                <field name="status"/>
                <field name="discrepancy_minutes" widget="float_time"/>
                <field name="attendance_id" optional="hide"/>
                <field name="biotime_punch_in_id" optional="hide"/>
                <field name="biotime_punch_out_id" optional="hide"/>
                <button name="action_fix_record" string="Fix" type="object" icon="fa-wrench" attrs="{'invisible': [('status', '=', 'matched')]}"/>
            </tree>
        </field>
    </record>

    <!-- Search View for Reconciliation Report -->
    <record id="view_biotime_reconciliation_report_search" model="ir.ui.view">
        <field name="name">biotime.reconciliation.report.search</field>
        <field name="model">biotime.reconciliation.report</field>
        <field name="arch" type="xml">
            <search string="Search Attendance Reconciliation">
                <field name="employee_id"/>
                <field name="date"/>
                <filter string="Today" name="today" domain="[('date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="Yesterday" name="yesterday" domain="[('date', '=', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last 7 Days" name="last_week" domain="[('date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter string="Discrepancies Only" name="discrepancies" domain="[('status', '!=', 'matched')]"/>
                <filter string="In Biotime Only" name="biotime_only" domain="[('status', '=', 'biotime_only')]"/>
                <filter string="In Odoo Only" name="odoo_only" domain="[('status', '=', 'odoo_only')]"/>
                <filter string="Missing Check-out" name="missing_checkout" domain="[('status', '=', 'missing_checkout')]"/>
                <filter string="Time Mismatch" name="time_mismatch" domain="[('status', '=', 'time_mismatch')]"/>
                <filter string="Matched Records" name="matched" domain="[('status', '=', 'matched')]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Date" name="date" context="{'group_by': 'date'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Form View for Reconciliation Report -->
    <record id="view_biotime_reconciliation_report_form" model="ir.ui.view">
        <field name="name">biotime.reconciliation.report.form</field>
        <field name="model">biotime.reconciliation.report</field>
        <field name="arch" type="xml">
            <form string="Attendance Reconciliation" create="false" edit="false" delete="false">
                <header>
                    <button name="action_fix_record" string="Fix Discrepancy" type="object" class="oe_highlight" attrs="{'invisible': [('status', '=', 'matched')]}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="employee_id" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="date" readonly="1"/>
                        </h2>
                        <h3>
                            <field name="status" readonly="1" widget="badge" 
                                decoration-danger="status in ('biotime_only', 'missing_checkout', 'missing_checkin')" 
                                decoration-warning="status=='time_mismatch'" 
                                decoration-success="status=='matched'"/>
                        </h3>
                    </div>
                    <group>
                        <group string="Biotime Data">
                            <field name="biotime_check_in"/>
                            <field name="biotime_check_out"/>
                            <field name="biotime_punch_in_id"/>
                            <field name="biotime_punch_out_id"/>
                        </group>
                        <group string="Odoo Attendance">
                            <field name="odoo_check_in"/>
                            <field name="odoo_check_out"/>
                            <field name="attendance_id"/>
                            <field name="discrepancy_minutes" widget="float_time"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for Reconciliation Report -->
    <record id="action_biotime_reconciliation_report" model="ir.actions.act_window">
        <field name="name">Attendance Reconciliation</field>
        <field name="res_model">biotime.reconciliation.report</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_discrepancies': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                All attendance records are synchronized!
            </p>
            <p>
                This report shows discrepancies between Biotime and Odoo attendance records.
                Use this to identify and fix missing or incorrect attendance data.
            </p>
        </field>
    </record>

    <!-- Report Actions -->
    <record id="action_biotime_reconciliation_refresh" model="ir.actions.server">
        <field name="name">Refresh Reconciliation Data</field>
        <field name="model_id" ref="model_biotime_reconciliation_report"/>
        <field name="binding_model_id" ref="model_biotime_reconciliation_report"/>
        <field name="binding_view_types">list,form</field>
        <field name="state">code</field>
        <field name="code">
            action = model.action_refresh_report()
        </field>
    </record>

    <record id="action_biotime_reconciliation_export" model="ir.actions.server">
        <field name="name">Export Discrepancies</field>
        <field name="model_id" ref="model_biotime_reconciliation_report"/>
        <field name="binding_model_id" ref="model_biotime_reconciliation_report"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            action = model.action_export_discrepancies()
        </field>
    </record>

    <!-- Menu Entry -->
    <menuitem id="menu_biotime_reconciliation_report" 
              name="Attendance Reconciliation" 
              parent="hr_attendance.menu_hr_attendance_root" 
              action="action_biotime_reconciliation_report" 
              sequence="6"/>
</odoo> 