# -*- coding: utf-8 -*-

from odoo import models, fields, api ,_
from collections import defaultdict


class account_analytic_line(models.Model):
    _inherit = 'account.analytic.line'

    cubes_type = fields.Selection(string='Type',
                                  selection=[('goods', 'سلعي'), ('cleaning', 'تنظيف'),
                                             ('service', 'خدمي'), ('Other', 'أخرى'),
                                             ('hypothetical', 'افتراضي')],
                                  default='hypothetical', related='product_id.cubes_type', store=True)

    goods_balance = fields.Monetary(
        compute='_compute_cubes_type',
        string='goods',store=True
    )

    cleaning_balance = fields.Monetary(
        compute='_compute_cubes_type',
        string='Cleaning',store=True
    )

    other_balance = fields.Monetary(
        compute='_compute_cubes_type',
        string='Others',store=True
    )

    @api.depends('cubes_type')
    def _compute_cubes_type(self):
        for account in self:
           if account.general_account_id.account_type != 'income':
               account.goods_balance = 0
               account.cleaning_balance = 0
               account.other_balance = 0
           elif account.cubes_type == 'goods' :
               account.goods_balance = account.amount
               account.cleaning_balance = 0
               account.other_balance = 0
           elif account.cubes_type == 'cleaning':
               account.cleaning_balance = account.amount
               account.goods_balance = 0
               account.other_balance = 0
           else:
               account.other_balance = account.amount
               account.goods_balance = 0
               account.cleaning_balance = 0


class account_analytic_account(models.Model):
    _inherit = 'account.analytic.account'

    @api.depends('line_ids.amount')
    def _compute_cubes_type_good(self):
        Curr = self.env['res.currency']
        analytic_line_obj = self.env['account.analytic.line']
        domain = [
            ('account_id', 'in', self.ids),
            ('company_id', 'in', [False] + self.env.companies.ids),
            ('cubes_type' ,'=' ,'goods' )
        ]
        if self._context.get('from_date', False):
            domain.append(('date', '>=', self._context['from_date']))
        if self._context.get('to_date', False):
            domain.append(('date', '<=', self._context['to_date']))

        user_currency = self.env.company.currency_id
        credit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '>=', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_credit = defaultdict(float)
        for l in credit_groups:
            data_credit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        debit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '<', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_debit = defaultdict(float)
        for l in debit_groups:
            data_debit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        for account in self:
            debit = abs(data_debit.get(account.id, 0.0))
            credit = data_credit.get(account.id, 0.0)
            account.goods_balance = credit - debit

    @api.depends('line_ids.amount')
    def _compute_cubes_type_cleaning(self):
        Curr = self.env['res.currency']
        analytic_line_obj = self.env['account.analytic.line']
        domain = [
            ('account_id', 'in', self.ids),
            ('company_id', 'in', [False] + self.env.companies.ids),
            ('cubes_type' ,'=' ,'cleaning' )
        ]
        if self._context.get('from_date', False):
            domain.append(('date', '>=', self._context['from_date']))
        if self._context.get('to_date', False):
            domain.append(('date', '<=', self._context['to_date']))

        user_currency = self.env.company.currency_id
        credit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '>=', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_credit = defaultdict(float)
        for l in credit_groups:
            data_credit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        debit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '<', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_debit = defaultdict(float)
        for l in debit_groups:
            data_debit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        for account in self:
            debit = abs(data_debit.get(account.id, 0.0))
            credit = data_credit.get(account.id, 0.0)
            account.cleaning_balance = credit - debit

    @api.depends('line_ids.amount')
    def _compute_cubes_type_other(self):
        Curr = self.env['res.currency']
        analytic_line_obj = self.env['account.analytic.line']
        domain = [
            ('account_id', 'in', self.ids),
            ('company_id', 'in', [False] + self.env.companies.ids),
            ('cubes_type', 'not in', ['cleaning','goods'])
        ]
        if self._context.get('from_date', False):
            domain.append(('date', '>=', self._context['from_date']))
        if self._context.get('to_date', False):
            domain.append(('date', '<=', self._context['to_date']))

        user_currency = self.env.company.currency_id
        credit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '>=', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_credit = defaultdict(float)
        for l in credit_groups:
            data_credit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        debit_groups = analytic_line_obj.read_group(
            domain=domain + [('amount', '<', 0.0)],
            fields=['account_id', 'currency_id', 'amount'],
            groupby=['account_id', 'currency_id'],
            lazy=False,
        )
        data_debit = defaultdict(float)
        for l in debit_groups:
            data_debit[l['account_id'][0]] += Curr.browse(l['currency_id'][0])._convert(
                l['amount'], user_currency, self.env.company, fields.Date.today())

        for account in self:
            debit = abs(data_debit.get(account.id, 0.0))
            credit = data_credit.get(account.id, 0.0)
            account.other_balance = credit - debit


class account_account(models.Model):
    _inherit = 'account.move'

    indirect_cost = fields.Selection(string='Indirect Cost Type',
                                  selection=[('garbage_account', 'خدمات نقل قمامة'), ('equity_saving_account', 'توفير سيولة'),
                                             ('warranty_account', 'تامين'), ('rental_account', 'ايجارات'),
                                             ('salary_account', 'مرتبات')])

    analytic_account_custom = fields.Many2one('account.analytic.account',string='Analytic Account')
    partner_id_custom = fields.Many2one('res.partner', string='Customer')