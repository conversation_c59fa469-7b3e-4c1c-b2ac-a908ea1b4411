# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_multiple_barcodes
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2022-08-10 11:04+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Poedit 3.1\n"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_product__barcode_ids
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_template__barcode_ids
msgid "Additional Barcodes"
msgstr "Dodatkowe Kody Kreskowe"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__name
msgid "Barcode"
msgstr "Kod Kreskowy"

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Cancel"
msgstr "Anuluj"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__create_uid
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__create_uid
msgid "Created by"
msgstr "Utworzony przez"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__create_date
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__create_date
msgid "Created on"
msgstr "Utworzony w dniu"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__display_name
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__display_name
msgid "Display Name"
msgstr "Nazwa Wyświetlana"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__id
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__id
msgid "ID"
msgstr "ID"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard____last_update
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi____last_update
msgid "Last Modified on"
msgstr "Zmodyfikowany w dniu"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__write_uid
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__write_uid
msgid "Last Updated by"
msgstr "Zaktualizowano przez"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__write_date
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__write_date
msgid "Last Updated on"
msgstr "Zaktualizowany w dniu"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__name
msgid "New Barcode"
msgstr "Nowy Kod Kreskowy"

#. module: product_multiple_barcodes
#: model:ir.model.constraint,message:product_multiple_barcodes.constraint_product_product_barcode_uniq
msgid "No error"
msgstr "Brak błędu"

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_product
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__product_id
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_barcode_multi_view_search
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_variant_barcode_multi_view_search
msgid "Product"
msgstr "Produkt"

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_barcode_multi
msgid "Product Barcode Multi"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_template
msgid "Product Template"
msgstr "Szablon produktu"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__remember_previous_barcode
msgid "Remember previous barcode in \"Additional Barcodes\" field"
msgstr "Zapamiętaj poprzedni kod kreskowy w polu \"Dodatkowe Kody Kreskowe\""

#. module: product_multiple_barcodes
#: code:addons/product_multiple_barcodes/models/product_product.py:0
#, python-format
msgid ""
"The following barcode(s): {0} was found in other active products.\n"
"Note that product barcodes should not repeat themselves both in \"Barcode\" field and \"Additional Barcodes\" field."
msgstr ""
"Następujące kody kreskowe: {0} zostały znalezione w innych aktywnych produktach.\n"
"Należy pamiętać, że kody kreskowe produktów nie powinny powtarzać się zarówno w polu \"Kod Kreskowy\", jak i w polu \"Dodatkowe Kody Kreskowe\"."

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_product_view_form_multiply_barcode
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_template_view_form_multiply_barcode
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Update Barcode"
msgstr "Aktualizuj Kod Kreskowy"

#. module: product_multiple_barcodes
#: model:ir.actions.act_window,name:product_multiple_barcodes.action_multiply_barcode_wizard
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Update Product Barcode"
msgstr "Aktualizuj Kod Kreskowy Produktu"

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_multiply_barcode_wizard
msgid "Update Product Multiply Barcode Wizard"
msgstr "Kreator aktualizacji wielu kodów kreskowych produktów"

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid ""
"Use this popup in case you would like to update barcode of current product. It also allows you to record automatically previous barcode into \"Additional "
"Barcodes\" field,\n"
"                        so it will still be searchable and also in this case you will be 100% sure that this barcode will not be used by other products also"
msgstr ""
"Użyj tego okienka w przypadku, gdy chcesz zaktualizować kod kreskowy bieżącego produktu. Pozwala ono również na automatyczne zapisanie poprzedniego kodu "
"kreskowego w polu \"Dodatkowe Kody Kreskowe\",\n"
"                        dzięki czemu nadal będzie można go wyszukiwać, a także w tym przypadku będziesz miał 100% pewności, że ten kod kreskowy nie będzie "
"używany przez inne produkty"

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "or"
msgstr "lub"
