# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_taxcloud
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Language-Team: Belarusian (https://app.transifex.com/odoo/teams/41243/be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Go to Settings."
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr ""

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Tax"
msgstr ""

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Update taxes"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Use TaxCloud API"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
