# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_no_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-09 14:30+0000\n"
"PO-Revision-Date: 2023-02-09 14:30+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_FA
msgid "A. Fixed Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_title
msgid "ACTIVE"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active
msgid "ACTIVE TOTAL"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_account_recceivable
msgid "Account Receivable"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_CurA
msgid "B. Current Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report,name:l10n_no_reports.account_financial_report_NO_balancesheet
#: model:ir.actions.client,name:l10n_no_reports.account_financial_html_report_action_5
#: model:ir.ui.menu,name:l10n_no_reports.account_financial_html_report_menu_5
msgid "Balance Sheet"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_bonds
msgid "Bonds"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_equity
msgid "C. Equity"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_change_in_value_of_marketable_investments
msgid "Change in Value of Marketable Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_changes_in_balance_value
msgid "Changes in Balance Value"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_convertible_debt
msgid "Convertible Debt"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_cost_of_materials
msgid "Cost of Materials"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_liabilities
msgid "D. Liabilities"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_debenture_loan
msgid "Debenture Loan"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_debt_to_financial_institutions
msgid "Debt to Financial Institutions"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_deferred_tax
msgid "Deferred Tax"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_Deferred_Tax_Assets
msgid "Deferred Tax Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_depreciation
msgid "Depreciation"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_equities_and_investments
msgid "Equities and Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_equities_and_investments_in_group_companies
msgid "Equities and Investments in Group Companies"
msgstr ""

#. module: l10n_no_reports
#: model:ir.model,name:l10n_no_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_Goodwill
msgid "Goodwill"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Allowances_for_liability
msgid "I. Allowances for liability"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_Intangible_Assets
msgid "I. Intangible Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Inv
msgid "I. Inventories"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_equity_paid_in_capital
msgid "I. Paid-in Capital"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Deb
msgid "II. Debitors"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_Fixed_Assets
msgid "II. Fixed Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Other_longterm_liabilities
msgid "II. Other longterm liabilities"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_retained_earnings
msgid "II. Retained Earnings"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Current_liability
msgid "III. Current liability"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_Fixed_Financial_Assets
msgid "III. Fixed Financial Assets"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_sti
msgid "III. Short-Term Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_CASH
msgid "IV. Cash and deposits"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_income_from_other_investments
msgid "Income from Other Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_increase_in_value_of_marketable_investments
msgid "Increase in Value of Marketable Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_interest_income_from_group_companies
msgid "Interest Income from Group Companies"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_interest_to_group_companies
msgid "Interest to Group Companies"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_investment_in_associated_companies
msgid "Investment in Associated Companies"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_investment_in_group_companies_etc
msgid "Investment in Group Companies etc"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_investment_in_subsidiaries
msgid "Investment in Subsidiaries"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_land
msgid "Land, Building and Other Real Estate"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_Licences
msgid "Licences"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_loan_to_associated_companies
msgid "Loan to Associated Companies and Joint Venture Activities"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_loan_to_group_companies
msgid "Loan to Group Companies"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_machinery
msgid "Machinery and Equipment"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_marketable_bonds
msgid "Marketable Bonds"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_marketable_shares
msgid "Marketable Shares"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_net_profit_loss_for_the_year
msgid "Net profit/Loss for the Year"
msgstr ""

#. module: l10n_no_reports
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "No KID number, please set one up for company %s"
msgstr ""

#. module: l10n_no_reports
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"No number for the Register of Legal Entities, please set one up for company "
"%s"
msgstr ""

#. module: l10n_no_reports
#: model_terms:ir.ui.view,arch_db:l10n_no_reports.no_evat_template
msgid "Odoo SA"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_operating_costs
msgid "Operating Costs"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_operating_movable_property_furniture_tools_other
msgid "Operating Movable Property, Furniture, Tools, Other"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_operating_profit
msgid "Operating Profit"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_operating_revenue
msgid "Operating revenues"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_ordinary_result
msgid "Ordinary Result"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_ordinary_result_before_taxes
msgid "Ordinary Result Before Taxes"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_financial_expense
msgid "Other Financial Expense"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_financial_income
msgid "Other Financial Income"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_long_term_liabilities
msgid "Other Long Term Liabilities"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_other_marketable_financial_instruments
msgid "Other Marketable Financial Instruments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_operating_expense
msgid "Other Operating Expense"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_operating_income
msgid "Other Operating Income"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_equity_other_paid_in_capital
msgid "Other Paid-in Capital"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_provisions
msgid "Other Provisions"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_other_receivables
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_other_receivables_2
msgid "Other Receivables"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_other_short_term_liabilities
msgid "Other Short-Term Liabilities"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive_title
msgid "Passive"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive
msgid "Passive TOTAL"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_other_payment_to_be_received_by_owner
msgid "Payment to be Received by Owner"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_pension_liability
msgid "Pension Liability"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_personal_expense
msgid "Personal Expense"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report,name:l10n_no_reports.account_financial_report_NO_profitandloss
#: model:ir.actions.client,name:l10n_no_reports.account_financial_html_report_action_4
#: model:ir.ui.menu,name:l10n_no_reports.account_financial_html_report_menu_4
msgid "Profit and Loss"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_reduction_in_value_of_marketable_investments
msgid "Reduction in Value of Marketable Investments"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_RD
msgid "Research and Development"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive_reserves
msgid "Reserves"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive_share_premium_retained_equity
msgid "Retained Equity"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_sales_revenue
msgid "Sales Revenue"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive_share_capital
msgid "Share Capital"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_passive_share_premium_reserve
msgid "Share Premium Reserve"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_share_of_profit_of_subsidiaries_and_associated_companies
msgid "Share of Profit of Subsidiaries and Associated Companies"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_active_ships
msgid "Ships, Rigs, Aeroplans etc"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_short_term_convertible_loans
msgid "Short-Term Convertible Loans"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_short_term_debenture_loans
msgid "Short-Term Debenture Loans"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_short_term_debt_to_financial_institutions
msgid "Short-Term Debt to Financial Institutions"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_tax_payable
msgid "Tax Payable"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_tax_on_extraordinary_charges
msgid "Tax on Extraordinary Charges"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_tax_on_ordinary_result
msgid "Tax on Ordinary Result"
msgstr ""

#. module: l10n_no_reports
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"The chosen date interval is invalid, it should be from the start of one "
"month to the end of the other month"
msgstr ""

#. module: l10n_no_reports
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"The chosen dates do not correspond to a valid time interval for the tax "
"return XML"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_trade_creditors
msgid "Trade Creditors"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_value_added_tax
msgid "Value Added Tax"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_write-downs
msgid "Write-Downs"
msgstr ""

#. module: l10n_no_reports
#: model:account.financial.html.report.line,name:l10n_no_reports.account_financial_report_NO_write_downs_of_short_terms_investments
msgid "Write-Downs of Short-Terms Investments"
msgstr ""

#. module: l10n_no_reports
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#: code:addons/l10n_no_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "XML"
msgstr ""
