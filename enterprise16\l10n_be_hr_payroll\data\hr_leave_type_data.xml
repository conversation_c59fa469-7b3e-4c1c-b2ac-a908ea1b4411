<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="holiday_type_small_unemployment" model="hr.leave.type">
        <field name="name">Small Unemployment</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">yes</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_small_unemployment"></field>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_maternity" model="hr.leave.type">
        <field name="name">Maternity Time Off</field>
        <field name="requires_allocation">no</field>
        <field name="leave_validation_type">no_validation</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_maternity"></field>
        <field name="icon_id" ref="hr_holidays.icon_11"/>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_unpredictable" model="hr.leave.type">
        <field name="name">Unpredictable Reason</field>
        <field name="requires_allocation">no</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_unpredictable"></field>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_training" model="hr.leave.type">
        <field name="name">Training Time Off</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">yes</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_training_time_off"></field>
        <field name="icon_id" ref="hr_holidays.icon_26"/>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_extra_legal" model="hr.leave.type">
        <field name="name">Extra Legal Time Off</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">yes</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_extra_legal"></field>
        <field name="icon_id" ref="hr_holidays.icon_9"/>
        <field name="company_id" eval="False"/>
        <field name="sequence">6</field>
    </record>

    <record id="holiday_type_recovery" model="hr.leave.type">
        <field name="name">Recovery Bank Holiday</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">yes</field>
        <field name="request_unit">day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_recovery"></field>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_european" model="hr.leave.type">
        <field name="name">European Time Off</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">yes</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_european"></field>
        <field name="icon_id" ref="hr_holidays.icon_14"/>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_credit_time" model="hr.leave.type">
        <field name="name">Credit Time</field>
        <field name="requires_allocation">no</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" eval="ref('hr_holidays.mt_leave')"/>
        <field name="work_entry_type_id" ref="work_entry_type_credit_time"></field>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_status_work_accident" model="hr.leave.type">
        <field name="name">Work Accident Time Off</field>
        <field name="requires_allocation">no</field>
        <field name="color_name">red</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_work_accident"/>
        <field name="company_id" eval="False"/>
    </record>

    <record id="holiday_type_strike" model="hr.leave.type">
        <field name="name">Strike</field>
        <field name="requires_allocation">no</field>
        <field name="request_unit">half_day</field>
        <field name="color_name">lavender</field>
        <field name="leave_notif_subtype_id" ref="hr_holidays.mt_leave"/>
        <field name="work_entry_type_id" ref="work_entry_type_strike"></field>
        <field name="company_id" eval="False"/>
    </record>
</odoo>
