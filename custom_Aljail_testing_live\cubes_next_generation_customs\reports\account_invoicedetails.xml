<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="report_invoice_document_inherit_x1" inherit_id="account.report_invoice_document">
        <xpath expr="//table[@name='invoice_line_table']" position="before">
                        <t t-set="i" t-value="1"/>
        </xpath>
        <xpath expr="//table[@name='invoice_line_table']/tbody/t[2]" position="after">
            <t t-set="total_qty" t-value="0"/>

        </xpath>
        <xpath expr="//th[@name='th_priceunit']" position="after">
            <th name="th_price_unit_after_discount" t-if='display_discount'
                class="text-right">سعر الوحدة بعد الخصم</th>
        </xpath>
         <xpath expr="//th[@name='th_description']" position="after">
            <th name="th_barcode"
                class="text-center">Barcode</th>
        </xpath>
         <xpath expr="//th[@name='th_description']" position="before">
            <th name="th_barcode"
                class="text-center">Seq.</th>
        </xpath>
        <xpath expr="//th[@name='th_taxes']" position="replace">
            <th name="th_original_total_amount" t-if='display_discount'  class="text-right">الاجمالي قبل الخصم</th>
        </xpath>
         <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]/tr/t/td[1]" position="after">
            <td name="td_barcode" class="text-end">
                <span t-field="line.barcode"/>
            </td>
        </xpath>
        <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]/tr/t/td[6]" position="replace">
        </xpath>


        <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]/tr/t/td[4]" position="after">
            <td name="td_price_unit_after_discount" t-if='display_discount'  class="text-end">
                <span t-field="line.price_unit_after_discount"/>
            </td>
            <t t-set="total_qty" t-value="total_qty + line.quantity"/>
        </xpath>
        <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]/tr/t/td[hasclass('o_price_total')]" position="before">
            <td name="td_original_total_amount" t-if='display_discount' class="text-end">
                <span t-field="line.original_total_amount"/>
            </td>

        </xpath>
        <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]" position="after">
                        <tr>
                            <td></td>
                        <td></td>
                         <td></td>
                        <td class="text-end">
                            <span t-esc="total_qty"/>
                        </td>
                        <td class="text-end"></td>
                        <td class="text-end"></td>
                        <td class="text-end"></td>
                        <td></td>
                    </tr>
                    </xpath>
        <xpath expr="//table[@name='invoice_line_table']/tbody/t[2]" position="replace">
            <t t-set="lines" t-value="o.invoice_line_ids.filtered(lambda l:not l.product_id.is_discount_product).sorted(key=lambda l: (-l.sequence, l.date, l.move_name, -l.id), reverse=True)"/>

        </xpath>
        <xpath expr="//div[@name='customer_code']" position="after">
            <div class="col-auto col-3 mw-100 mb-2"  name="customer_code">
                                <strong>الرصيد الحالي لعميل:</strong>
                                <p class="m-0" t-field="o.partner_balance"/>
                            </div>
        </xpath>
         <xpath expr="//table[@name='invoice_line_table']/tbody/t[4]/tr/t/td[1]" position="before">
            <td name="td_barcode" class="text-end">
                <span t-esc="i"/>
                <t t-set="i" t-value="i+1"/>
            </td>
        </xpath>

          <xpath expr="//table[@name='invoice_line_table']" position="attributes">
              <attribute name="class">table-bordered</attribute>
                <attribute name="width">100%</attribute>
        </xpath>

    </template>

</odoo>