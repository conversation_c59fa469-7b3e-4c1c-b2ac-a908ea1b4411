<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_company_form" model="ir.ui.view">
        <field name="name">view.company.form.inherit.user</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_info']" position="inside">
                <group>
                    <field name="footer_img" widget="image" class="oe_avatar" />
                    <field name="header_img" widget="image" class="oe_avatar" />
                </group>
            </xpath>
        </field>
    </record>


    <template id="header_layout_standard_inv_custom_test">
        <t t-set="company" t-value="user.company_id"/>
        <div class="header" style="height: 100px;margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
            <style>
                    * {
                    font-family: 'Tajawal', sans-serif !important;
                    font-weight: 900 !important;
                    font-size: 1.1rem !important;
                    }
                </style>
            <table class="table table-borderless" style="width:100%; font-size:0.9rem;margin-top:10px;padding:1pt;">
                <tbody>
                    <tr>
                        <td width="100%" style="margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
                            <img class="img" t-attf-src="data:image/png;base64, {{company.logo}}" style="margin-top:10px;max-height:220px;width:100%; margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;" alt="Logo"/>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer" style="height: 100px;margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
            <table style="border:None;width:100%;padding:0;color:black;margin-top:-26px;font-size:0.9rem" class="table table-sm">
                <tr style="border:None;">
                    <td width="100%" style="margin:0; padding:0; border:none;">
                        <img class="img" t-attf-src="data:image/png;base64, {{company.footer_img}}" style="margin:0;max-height:190px;width:100%;object-fit:contain;display:block;" alt="Logo"/>
                    </td>
                </tr>
            </table>
        </div>

        <div class="article" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id"
             t-att-data-oe-lang="o and o.env.context.get('lang')">
            <style>
                    * {
                    font-family: 'Tajawal', sans-serif !important;
                    font-weight: 900 !important;
                    font-size: 1.1rem !important;
                    }
                </style>
            <t t-out="0"/>
        </div>
    </template>


    <template id="header_layout_standard_inv_custom_header2">
        <t t-set="company" t-value="user.company_id"/>
        <div class="header" style="height: 100px;margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
            <style>
                    * {
                    font-family: 'Tajawal', sans-serif !important;
                    font-weight: 900 !important;
                    font-size: 0.9rem !important;
                    }
                </style>
            <table class="table table-borderless" style="width:100%; font-size:0.9rem;margin-top:10px;padding:1pt;">
                <tbody>
                    <tr>
                        <td width="100%" style="margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
                            <img class="img" t-attf-src="data:image/png;base64, {{company.header_img}}" style="margin-top:10px;max-height:220px;width:100%; margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;" alt="Logo"/>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer" style="height: 100px;margin-left: 0; margin-right: 0; padding-left: 0;padding-right: 0;">
            <style>
                    * {
                    font-family: 'Tajawal', sans-serif !important;
                    font-weight: 900 !important;
                    font-size: 0.9rem !important;
                    }
                </style>
            <table style="border:None;width:100%;padding:0;color:black;margin-top:-26px;font-size:0.9rem" class="table table-sm">
                <tr style="border:None;">
                    <td width="100%" style="margin:0; padding:0; border:none;">
                        <img class="img" t-attf-src="data:image/png;base64, {{company.footer_img}}" style="margin:0;max-height:170px;width:100%;object-fit:contain;display:block;" alt="Logo"/>
                    </td>
                </tr>
            </table>
        </div>

        <div class="article" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <style>
                    * {
                    font-family: 'Tajawal', sans-serif !important;
                    font-weight: 900 !important;
                    font-size: 0.9rem !important;
                    }
                </style>
            <t t-out="0"/>
        </div>
    </template>



    <!--Template for VAT invoice report-->
    <template id="invoice_hotel_template_id">
        <t t-call="web.html_container">
            <div class="page">
                <t t-foreach="docs" t-as="o">

                    <t t-call="hotel_invoice.header_layout_standard_inv_custom_test">
                        <div jsname="WjL7X" jsslot="" style="margin-top:0px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #D9BF83;color: white;border: none;display: flex;align-items: center;justify-content: space-between;                                   gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                                                                  cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <i class="google-material-icons" style="font-size: 20px;"> </i>
                                        <span style="color:black;">بطاقة حجز اقامة</span>
                                    </div>
                                    <div style="flex-grow: 1; text-align: center;">
                                        <span style="color:black;">
                                            <t t-if="o.state == 'draft'">
                                                فاتورة مبدئية
                                            </t>
                                            <t t-if="o.state == 'confirm'">
                                                فاتورة نهائية
                                            </t>
                                        </span>
                                    </div>
                                    <div style="width: 28px;"></div> <!-- Spacer to balance the layout -->
                                </div>
                            </div>
                        </div>

                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right; display: flex; justify-content: space-between;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true" style="display: flex; width: 100%;">
                                <div style="background-color: #FFFFFF;color: white;border: border: 2pt solid #D9BF83;display: flex;align-items: center;margin-top:1px;                                  gap: 2px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                                   cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;                                   width: 48%; justify-content: center; margin-left: auto;">
                                    <span t-esc="o.reservation_no" style="color:black;"/>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">رقم الحجز</span>
                                </div>
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;margin-top:-49px;                              gap: 2px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                              cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;                              width: 48%; justify-content: center; margin-right: auto;">
                                    <span style="color:black;">
                                        <t t-esc="o.date_order.strftime('%Y-%m-%d')" />
                                        <span style="margin: 0 10px;">|</span>
                                        <t t-esc="o.date_order.strftime('%H:%M:%S')" />
                                    </span>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">تاريخ الحجز</span>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <span style="color:black;">السيد</span>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span t-esc="o.partner_id.name" style="color:black;"/>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <span t-esc="o.partner_id.mobile" style="color:black;"/>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;"> رقم الهاتف</span>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <span t-esc="o.gust_no" style="color:black;"/>

                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;"> عدد الاشخاص</span>

                                </div>
                            </div>
                        </div>

                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <span t-esc="line.room_number.name" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;"> رقم الشاليه</span>
                                </div>
                            </div>
                        </div>

                        <br/>
                        <br/>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right; display: flex; justify-content: space-between;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true" style="display: flex; width: 100%;">
                                <div style="background-color: #D9BF83;color: white;border: border: 2pt solid #D9BF83;display: flex;align-items: center;margin-top:1px;                                  gap: 2px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                                  cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;                                  width: 48%; justify-content: center; margin-left: auto;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <span style="color:black;">
                                            <t t-esc="line.checkin.strftime('%Y-%m-%d')" />
                                            <span style="margin: 0 10px;">|</span>
                                            15:00:00
                                        </span>
                                    </t>
                                    <span style="color:black;"> تاريخ الدخول</span>
                                    <i class="google-material-icons" style="font-size: 20px;color:#FFFFFF">⮜</i>
                                </div>
                                <div style="background-color: #D9BF83;color: white;border: none;display: flex;align-items: center;margin-top:-49px;                              gap: 2px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                              cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;                              width: 48%; justify-content: center; margin-right: auto;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <span style="color:black;">
                                            <t t-esc="line.real_checkout_time.strftime('%Y-%m-%d')" />
                                            <span style="margin: 0 10px;">|</span>
                                            12:00:00
                                        </span>
                                    </t>
                                    <span style="color:black;">تاريخ الخروج</span>
                                    <i class="google-material-icons" style="font-size: 20px;color:#FFFFFF">⮞</i>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <span t-esc="line.price" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">قيمة حجز اليوم</span>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <span t-esc="line.number_of_days" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">مدة الحجز</span>
                                </div>
                            </div>
                        </div>

                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                   <t t-foreach="o.reservation_line" t-as="line">
                                        <span t-esc="line.sub_total1" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">قيمة الحجز الكلية</span>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <t t-set="discount_day" t-value="line.free_days + line.discount_by_day"/>
                                        <t t-set="discount" t-value="discount_day * line.price"/>
                                        <span t-esc="discount" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">قيمة التخفيض</span>
                                </div>
                            </div>
                        </div>
                        <div jsname="WjL7X" jsslot="" style="margin-top:4px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                             cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-foreach="o.reservation_line" t-as="line">
                                        <t t-set="discount_days" t-value="line.free_days + line.discount_by_day"/>
                                        <span t-esc="discount_days" style="color:black;"/>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">عدد ايام التخفيض</span>
                                </div>
                            </div>
                        </div>

                        <br/>
                        <br/>
                        <div jsname="WjL7X" jsslot="" style="margin-top:20px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #FFFFFF;color: white;border: none;display: flex;align-items: center;                             gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                              cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-if="o.is_guarrantte_amount">
                                        <span t-field="o.guarrante_amount" style="color:black;"/>
                                    </t>
                                    <t t-else="">
                                        <span style="color:black;">0</span>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83"> *</i>
                                    <span style="color:black;">قيمة الضمان</span>
                                </div>
                            </div>
                        </div>
                        <!-- Advance payment section -->
                        <div jsname="WjL7X" jsslot="" style="margin-top:5px;text-align:right;">
                            <div class="VfPpkd-dgl2Hf-ppHlrf-sM5MNb" data-is-touch-wrapper="true">
                                <div style="background-color: #D9BF83;color: white;border: none;display: flex;align-items: center;                                gap: 8px;padding: 12px 20px;border-radius: 24px;font-size: 18px;font-weight: bold;                                 cursor: pointer;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);transition: background-color 0.3s ease;">
                                    <t t-if="o.is_auto_payment">
                                        <t t-if="o.is_guarrantte_amount">
                                            <t t-set="total_amount" t-value="0"/>
                                            <t t-foreach="o.reservation_line" t-as="line">
                                                <t t-set="total_amount" t-value="total_amount + line.sub_total1"/>
                                            </t>
                                            <span t-esc="total_amount + o.guarrante_amount" style="color:black;"/>
                                        </t>
                                        <t t-else="">
                                            <t t-set="total_amount" t-value="0"/>
                                            <t t-foreach="o.reservation_line" t-as="line">
                                                <t t-set="total_amount" t-value="total_amount + line.sub_total1"/>
                                            </t>
                                            <span t-esc="total_amount" style="color:black;"/>
                                        </t>
                                    </t>
                                    <t t-else="">
                                        <span style="color:black;">0</span>
                                    </t>
                                    <i class="google-material-icons" style="font-size: 20px;color:#D9BF83;">* </i>
                                    <span style="color:black;">المبلغ المستلم</span>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </div>

            <div class="page">
                <t t-call="hotel_invoice.header_layout_standard_inv_custom_header2">
                  <div style="border: 1px solid #D9BF83; width: 100%; margin-top: 0; height: auto; font-size: 0.8rem; background-color: #FFFFFF; padding: 15px;">
                        <div style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: space-between; gap: 8px;">
                            <!-- Left Column -->
                            <div style="width: 100%; text-align: right; direction: rtl;margin-left: auto;">
                                <ul style="padding-right: 5px; list-style-type: disc;font-size: 12px;line-height: 1.5;">
                                    <li>يتكون المنتجع من الشاليهات السكنية والمطعم المطل على حوض السباحة المفتوح وحديقة للألعاب الأطفال و محل بقالة يحتوي على المستلزمات الضرورية اليومية وصالة بلياردو وتنس طاولة وصالة PlayStation.</li>
                                    <li>تعتبر قيمة الحجز المدفوع نظير الإقامة في الشاليه واستخدام الحوض والألعاب الأطفال الخارجية ولا تشمل المطعم أو المحل أو أي نشاطات أخرى.</li>
                                    <li>يتكون الشاليه من حجرتين نوم وصالة معيشة وحمام ومطبخ حديث مجهز بمكيف هواء وحديقة خاصة، وهو مصمم ليسع 5 أشخاص، وفي كل الأحوال يجب ألا يزيد عدد المقيمين بالشاليه الواحد عن سبعة أفراد.</li>
                                    <li>السعر اليومي يغطي خمسة أشخاص من كتيب العائلة، ويمكن إضافة شخصين كحد أقصى بدفع قيمة إضافية حيث لا يحمل الشاليه الواحد أكثر من سبعة أشخاص.</li>
                                    <li>يتطلب دفع مبلغ إضافي بقيمة 50 دينار عن كل يوم على أي شخص إضافي (السادس والسابع) بشرط أن يكون أحد أفراد الأسرة أو والدين الزوج أو الزوجة و100 دينار عن كل يوم على الأشخاص غير المسجلين بالكتيب من الأسرة أو العاملات. (هذا بالنسبة للمقيمين).</li>
                                    <li>يسمح باستقبال الزوار داخل المنتجع على أن يدفع 50 دينار على الزائر (المبيت غير مسموح).</li>
                                    <li>بالنسبة للشاليهات التي تحتوي على حوض السباحة الخاص فهي مجهزة بسياج زجاجي للحماية به بوابة وقفل لسلامة الأطفال، كما يحتوي المطبخ على غسالة ملابس.</li>
                                    <li>جميع الشاليهات بها غرفتين: غرفة بسرير زوجي + غرفة بسريرين فرديين (يمكن إضافة سرائر إضافية بالتواصل مع الإدارة) + حمام + مطبخ متكامل + صالة معيشة + حديقة صغيرة + شواية + Wifi &amp; TV.</li>
                                    <li>يجب الاستحمام قبل استعمال أحواض السباحة الخاصة والحوض المشترك.</li>
                                </ul>
                                <ul style="padding-right: 5px; list-style-type: disc;font-size: 12px;line-height: 1.5;">
                                    <li>لن يسمح بالدخول للمنتجع إلا بعد إحضار بطاقة الحجز وكتيب العائلة وإيصال الضمان الذي سيبقيان في عهدة الإدارة كوديعة إلى حين استلام الشاليه والمغادرة.</li>
                                    <li>التقيد بزمن الدخول (الاستقبال) والمحدد بالساعة (الثالثة ظهراً) وموعد المغادرة كحد أقصى (الثانية عشرة صباحاً) وسيتم خصم مبلغ 100 دينار عن كل ساعة تأخير من الضمان المقدم.</li>
                                    <li>يوجد بالمنتجع موقف للسيارات ومخصص لسيارة واحدة لكل شاليه.</li>
                                    <li>كل محتويات الشاليه عهدة عند النزيل وسيتم خصم قيمة الأشياء المفقودة أو المكسورة من الضمان المقدم قبل المغادرة وفقاً للجدول المبين داخل الشاليه.</li>
                                    <li>إدارة المنتجع غير مسؤولة عن ضياع الأشياء الثمينة.</li>
                                    <li>التقيد باللباس المناسب داخل المطعم وأثناء السباحة بالحوض أو الشاطئ.</li>
                                    <li>احترام خصوصيات وثقافة النزلاء الآخرين.</li>
                                    <li>يمنع إدخال الأسلحة والمشروبات الكحولية والألعاب النارية وأي محروقات أخرى، ويعتبر الحجز ملغياً في حالة ثبوت ذلك دون إرجاع قيمة الحجز.</li>
                                    <li>اتباع تعليمات المشرفين وفريق الإنقاذ أثناء السباحة بالبحر أو بحوض السباحة.</li>
                                    <li>يعتبر الحجز ملغياً دون إرجاع قيمة الحجز في حالة الاعتداء بالقول أو الفعل على الموظفين العاملين أو النزلاء.</li>
                                    <li>لا يسمح بالتنازل عن الحجز إلى نزيل آخر في حالة تعذر الإقامة لصاحب كتيب العائلة المسجل بالحجز.</li>
                                    <li>لا يسمح بنقل المحتويات الخاصة بكل شاليه إلى شاليه آخر.</li>
                                    <li>يمنع منعاً باتاً التدخين داخل الشاليه.</li>
                                    <li>لا يتحمل المنتجع أي مسؤولية عن أي أعطال طارئة قد تحصل بأحد مرافق المنتجع.</li>
                                </ul>
                            </div>


                        </div>
                    </div>

                </t>
            </div>
        </t>
    </template>

    <record id="view_hotel_reservation_form1" model="ir.ui.view">
        <field name="name">hotel.reservation.form</field>
        <field name="model">hotel.reservation</field>
        <field name="inherit_id" ref="hotel_management.view_hotel_reservation_form1"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <field name="real_checkout_time"/>
            </xpath>
            <xpath expr="//field[@name='childs']" position="after">
                <field name="gust_no" invisible="1"/>
            </xpath>
        </field>
    </record>


</odoo>
