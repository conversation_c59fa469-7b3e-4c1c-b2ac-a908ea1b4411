<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="view_company_form_inherited" model="ir.ui.view">
        <field name="name">res.company</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='currency_id']" position="before">
                <field name="is_print_analytic_account"/>
            </xpath>
        </field>
    </record>

    <template id="report_saleorder_document_inherit" inherit_id="sale.report_saleorder_document">
        <xpath expr="//t[@t-set='address']" position="before">
            <t t-if="doc.company_id.is_print_analytic_account and doc.cubes_analytic_account_id">
                <p>
                    <strong>Analytic Account:</strong>
                    <t t-out="doc.cubes_analytic_account_id.name"/>
                </p>
            </t>
        </xpath>
    </template>

    <template id="report_stock_picking_document_inherit" inherit_id="stock.report_delivery_document">
        <xpath expr="//t[@t-set='address']" position="before">
            <t t-if="o.company_id.is_print_analytic_account and o.cubes_analytic_account_id">
                <p>
                    <strong>Analytic Account:</strong>
                    <t t-out="o.cubes_analytic_account_id.name"/>
                </p>
            </t>
        </xpath>
    </template>

    <template id="report_account_move_document_inherit" inherit_id="account.report_invoice_document">
        <xpath expr="//div[hasclass('row')]" position="before">
            <t t-if="o.company_id.is_print_analytic_account and o.cubes_analytic_account_id">
                <p>
                    <strong>Analytic Account:</strong>
                    <t t-out="o.cubes_analytic_account_id.name"/>
                </p>
            </t>
        </xpath>
    </template>

</odoo>