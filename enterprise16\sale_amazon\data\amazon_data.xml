<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Supported Amazon marketplaces -->
    <!-- North America -->
    <record id="marketplace_BR" model="amazon.marketplace">
        <field name="name">Amazon.com.br</field>
        <field name="api_ref">A2Q3Y263D00KWC</field>
        <field name="region">us-east-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.com.br</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_CA" model="amazon.marketplace">
        <field name="name">Amazon.ca</field>
        <field name="api_ref">A2EUQ1WTGCTBG2</field>
        <field name="region">us-east-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.ca</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_MX" model="amazon.marketplace">
        <field name="name">Amazon.com.mx</field>
        <field name="api_ref">A1AM78C64UM0Y8</field>
        <field name="region">us-east-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.com.mx</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_US" model="amazon.marketplace">
        <field name="name">Amazon.com</field>
        <field name="api_ref">ATVPDKIKX0DER</field>
        <field name="region">us-east-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.com</field>
        <field name="tax_included">False</field>
    </record>
    <!-- Europe -->
    <record id="marketplace_AE" model="amazon.marketplace">
        <field name="name">Amazon.ae</field>
        <field name="api_ref">A2VIGQ35RCS4UG</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.ae</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_BE" model="amazon.marketplace">
        <field name="name">Amazon.com.be</field>
        <field name="api_ref">AMEN7PMS3EDWL</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.com.be</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_DE" model="amazon.marketplace">
        <field name="name">Amazon.de</field>
        <field name="api_ref">A1PA6795UKMFR9</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral-europe.amazon.com</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_EG" model="amazon.marketplace">
        <field name="name">Amazon.eg</field>
        <field name="api_ref">ARBP9OOSHTCHU</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.eg</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_ES" model="amazon.marketplace">
        <field name="name">Amazon.es</field>
        <field name="api_ref">A1RKKUPIHCS9HS</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral-europe.amazon.com</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_FR" model="amazon.marketplace">
        <field name="name">Amazon.fr</field>
        <field name="api_ref">A13V1IB3VIYZZH</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral-europe.amazon.com</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_IN" model="amazon.marketplace">
        <field name="name">Amazon.in</field>
        <field name="api_ref">A21TJRUUN4KGV</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.in</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_IT" model="amazon.marketplace">
        <field name="name">Amazon.it</field>
        <field name="api_ref">APJ6JRA9NG5V4</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral-europe.amazon.com</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_NL" model="amazon.marketplace">
        <field name="name">Amazon.nl</field>
        <field name="api_ref">A1805IZSGTT6HS</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.nl</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_PL" model="amazon.marketplace">
        <field name="name">Amazon.pl</field>
        <field name="api_ref">A1C3SOZRARQ6R3</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.pl</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_SA" model="amazon.marketplace">
        <field name="name">Amazon.sa</field>
        <field name="api_ref">A17E79C6D8DWNP</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.sa</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_SE" model="amazon.marketplace">
        <field name="name">Amazon.se</field>
        <field name="api_ref">A2NODRKZP88ZB9</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.se</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_TR" model="amazon.marketplace">
        <field name="name">Amazon.com.tr</field>
        <field name="api_ref">A33AVAJ2PDY3EV</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral.amazon.com.tr</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_UK" model="amazon.marketplace">
        <field name="name">Amazon.co.uk</field>
        <field name="api_ref">A1F83G8C2ARO7P</field>
        <field name="region">eu-west-1</field>
        <field name="seller_central_url">https://sellercentral-europe.amazon.com</field>
        <field name="tax_included">True</field>
    </record>
    <!-- Far East -->
    <record id="marketplace_AU" model="amazon.marketplace">
        <field name="name">Amazon.com.au</field>
        <field name="api_ref">A39IBJ37TRP1C6</field>
        <field name="region">us-west-2</field>
        <field name="seller_central_url">https://sellercentral.amazon.com.au</field>
        <field name="tax_included">True</field>
    </record>
    <record id="marketplace_JP" model="amazon.marketplace">
        <field name="name">Amazon.co.jp</field>
        <field name="api_ref">A1VC38T7YXB528</field>
        <field name="region">us-west-2</field>
        <field name="seller_central_url">https://sellercentral.amazon.co.jp</field>
        <field name="tax_included">False</field>
    </record>
    <record id="marketplace_SG" model="amazon.marketplace">
        <field name="name">Amazon.sg</field>
        <field name="api_ref">A19VAU5U5O7RUS</field>
        <field name="region">us-west-2</field>
        <field name="seller_central_url">https://sellercentral.amazon.sg</field>
        <field name="tax_included">True</field>
    </record>

    <!-- Default products -->
    <record id="default_product" model="product.product">
        <field name="name">Amazon Sale</field>
        <field name="type">consu</field>
        <field name="list_price">0.0</field>
        <field name="sale_ok">False</field>
        <field name="purchase_ok">False</field>
    </record>
    <record id="shipping_product" model="product.product">
        <field name="name">Amazon Shipping</field>
        <field name="type">service</field>
        <field name="list_price">0.0</field>
        <field name="sale_ok">False</field>
        <field name="purchase_ok">False</field>
    </record>
    <function model="product.product" name="_configure_for_amazon">
        <value eval="[ref('sale_amazon.default_product'), ref('sale_amazon.shipping_product')]"/>
    </function>

</odoo>
