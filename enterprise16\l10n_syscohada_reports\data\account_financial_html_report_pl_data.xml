<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_financial_report_l10n_pcg_cdr" model="account.report">
        <field name="name">Profit and Loss (SYSCOHADA)</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_l10n_pcg_cdr_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_report_line_01_syscohada_cdr_tot" model="account.report.line">
                <field name="name">INCOME AND EXPENSES</field>
                <field name="code">SYSCOHADA_CDR_TOT</field>
                <field name="children_ids">
                    <record id="account_financial_report_line_01_syscohada_cdr_char_vent" model="account.report.line">
                        <field name="name">TA | Ventes de marchandises</field>
                        <field name="code">SYSCOHADA_Ventes_de_marchandises</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_vent_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-701</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_char_acht" model="account.report.line">
                        <field name="name">RA | Purchases of goods</field>
                        <field name="code">SYSCOHADA_Achats_de_marchandises</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_acht_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-601</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_char_stck" model="account.report.line">
                        <field name="name">RB | Change in inventory of goods</field>
                        <field name="code">SYSCOHADA_Variation_de_stocks_mar</field>
                        <field name="hierarchy_level" eval="5"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_stck_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6031</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_marg_comr" model="account.report.line">
                        <field name="name">XA | TRADE MARGIN (Sum of TA to RB)</field>
                        <field name="code">SYSCOHADA_Marge_commerciale </field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_marg_comr_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Ventes_de_marchandises.balance - SYSCOHADA_Achats_de_marchandises.balance + SYSCOHADA_Variation_de_stocks_mar.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_fbr" model="account.report.line">
                                <field name="name">TB | Sales of manufactured products</field>
                                <field name="code">SYSCOHADA_Ventes_prod_fab</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_01_syscohada_cdr_char_fbr_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-703 - 704 - 702</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_01_syscohada_cdr_char_tc" model="account.report.line">
                                <field name="name">TC | Work, services sold</field>
                                <field name="code">SYSCOHADA_Travaux_services_vn</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_01_syscohada_cdr_char_tc_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-705 - 706</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_01_syscohada_cdr_char_pa" model="account.report.line">
                                <field name="name">TD | Accessory Products</field>
                                <field name="code">SYSCOHADA_Produits_accessoires</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_01_syscohada_cdr_char_pa_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-707</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_char_exp" model="account.report.line">
                        <field name="name">XB | SALES REVENUES (TA + TB + TC + TD)</field>
                        <field name="code">SYSCOHADA_Chiffres_affaires</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_exp_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Ventes_de_marchandises.balance + SYSCOHADA_Ventes_prod_fab.balance + SYSCOHADA_Travaux_services_vn.balance + SYSCOHADA_Produits_accessoires.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_syscohada_cdrc_te" model="account.report.line">
                                <field name="name">TE | Stored production (or destocking)</field>
                                <field name="code">SYSCOHADA_Produc_stck</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc_te_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-73</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc_tf" model="account.report.line">
                                <field name="name">TF | Capitalized production</field>
                                <field name="code">SYSCOHADA_Produc_immo</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc_tf_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-72</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc_tg" model="account.report.line">
                                <field name="name">TG | Operating Grants</field>
                                <field name="code">SYSCOHADA_Sub_exp</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc_tg_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-71</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc_th" model="account.report.line">
                                <field name="name">TH | Other products</field>
                                <field name="code">SYSCOHADA_Autres_prod</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc_th_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-75</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc5" model="account.report.line">
                                <field name="name">TI | Operating expense transfers</field>
                                <field name="code">SYSCOHADA_Transf_chrg_exp</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-781</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc8" model="account.report.line">
                                <field name="name">RC | Purchases of raw materials and related supplies</field>
                                <field name="code">SYSCOHADA_Achats_mat_prem</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc8_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-602</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc9" model="account.report.line">
                                <field name="name">RD | Change in inventory of raw materials and related supplies</field>
                                <field name="code">SYSCOHADA_Var_stocks_mp</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc9_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-6032</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc10" model="account.report.line">
                                <field name="name">RE | Other Purchases</field>
                                <field name="code">SYSCOHADA_Autres_achats</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc10_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-608 - 605 - 604</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc11" model="account.report.line">
                                <field name="name">RF | Change in inventory of other supplies</field>
                                <field name="code">SYSCOHADA_Var_stocks_autres_app</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc11_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-6033</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc12" model="account.report.line">
                                <field name="name">RG | Transport</field>
                                <field name="code">SYSCOHADA_Transports</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc12_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-61</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc13" model="account.report.line">
                                <field name="name">RH | External Services</field>
                                <field name="code">SYSCOHADA_Services_ext</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc13_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-63 - 62</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc14" model="account.report.line">
                                <field name="name">RI | Taxes and Fees</field>
                                <field name="code">SYSCOHADA_impots_et_taxes</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc14_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-64</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc15" model="account.report.line">
                                <field name="name">RJ | Other expenses</field>
                                <field name="code">SYSCOHADA_Autres_charges</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc15_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-65</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_char_fin" model="account.report.line">
                        <field name="name">XC | ADDED VALUE (XB+RA+RB) + (Sum of TE to RJ)</field>
                        <field name="code">SYSCOHADA_Val_ajoutee</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_fin_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Chiffres_affaires.balance + SYSCOHADA_Achats_de_marchandises.balance + SYSCOHADA_Variation_de_stocks_mar.balance + (SYSCOHADA_Produc_stck.balance + SYSCOHADA_Produc_immo.balance + SYSCOHADA_Sub_exp.balance + SYSCOHADA_Autres_prod.balance + SYSCOHADA_Transf_chrg_exp.balance + SYSCOHADA_Achats_mat_prem.balance + SYSCOHADA_Var_stocks_mp.balance + SYSCOHADA_Autres_achats.balance + SYSCOHADA_Var_stocks_autres_app.balance + SYSCOHADA_Transports.balance + SYSCOHADA_Services_ext.balance + SYSCOHADA_impots_et_taxes.balance + SYSCOHADA_Autres_charges.balance)</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_syscohada_cdrc17" model="account.report.line">
                                <field name="name">RK | Personnel expenses</field>
                                <field name="code">SYSCOHADA_Charges_pers</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc17_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-66</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_syscohada_cdrc18" model="account.report.line">
                        <field name="name">XD | GROSS OPERATING SURPLUS (XC+RK)</field>
                        <field name="code">SYSCOHADA_EBE</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_syscohada_cdrc18_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Val_ajoutee.balance + SYSCOHADA_Charges_pers.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_syscohada_rpam" model="account.report.line">
                                <field name="name">TJ | Reversal of depreciation, provisions and impairment</field>
                                <field name="code">SYSCOHADA_Reprise_amr_prov_dep</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_rpam_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-799 - 791 - 798</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_dap" model="account.report.line">
                                <field name="name">RL | Depreciation, amortization, provisions and impairment</field>
                                <field name="code">SYSCOHADA_Dot_am_prov_dep</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_dap_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-681 - 691</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_syscohada_cdrc19" model="account.report.line">
                        <field name="name">XE | OPERATING INCOME (XD+TJ+RL)</field>
                        <field name="code">SYSCOHADA_Resultat_exp</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_syscohada_cdrc19_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_EBE.balance + SYSCOHADA_Reprise_amr_prov_dep.balance + SYSCOHADA_Dot_am_prov_dep.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_syscohada_rvfn" model="account.report.line">
                                <field name="name">TK | Financial and Similar Income</field>
                                <field name="code">SYSCOHADA_Revenu_fin_ass</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_rvfn_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-77</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_rpad" model="account.report.line">
                                <field name="name">TL | Reversals of provisions and financial depreciation</field>
                                <field name="code">SYSCOHADA_Reprise_prov_dep_fin</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_rpad_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-797</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_trfcgf" model="account.report.line">
                                <field name="name">TM | Financial expense transfers</field>
                                <field name="code">SYSCOHADA_Transferts_charges_fin</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_trfcgf_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-787</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_ff" model="account.report.line">
                                <field name="name">RM | Financial expenses and similar charges</field>
                                <field name="code">SYSCOHADA_Frais_fin_chrg_ass</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_ff_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-67</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_dpdf" model="account.report.line">
                                <field name="name">RN | Allocation to provisions and financial depreciation</field>
                                <field name="code">SYSCOHADA_Dot_prov_dep_fin</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_dpdf_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-697</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_syscohada_cdrc20" model="account.report.line">
                        <field name="name">XF | FINANCIAL RESULT (Sum of TK to RN)</field>
                        <field name="code">SYSCOHADA_Resultat_fin</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_syscohada_cdrc20_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Revenu_fin_ass.balance + SYSCOHADA_Reprise_prov_dep_fin.balance + SYSCOHADA_Transferts_charges_fin.balance + SYSCOHADA_Frais_fin_chrg_ass.balance + SYSCOHADA_Dot_prov_dep_fin.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr_char_exc" model="account.report.line">
                        <field name="name">XG | INCOME FROM ORDINARY ACTIVITIES (XE+XF)</field>
                        <field name="code">SYSCOHADA_Resultat_AO</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr_char_exc_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Resultat_fin.balance + SYSCOHADA_Resultat_exp.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_syscohada_cdrc21" model="account.report.line">
                                <field name="name">TN | Proceeds from disposal of fixed assets</field>
                                <field name="code">SYSCOHADA_Prod_cess_immo</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc21_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-82</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc22" model="account.report.line">
                                <field name="name">TO | Other O.A.S products</field>
                                <field name="code">SYSCOHADA_Autres_prod_HAO</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc22_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-84 - 86 - 88</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_cdrc23" model="account.report.line">
                                <field name="name">RO | Book value of fixed asset disposals</field>
                                <field name="code">SYSCOHADA_Val_comp_cess_immo</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_cdrc23_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-81</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_syscohada_rp" model="account.report.line">
                                <field name="name">RP | Other expenses O.A.S</field>
                                <field name="code">SYSCOHADA_Autres_chrg_HAO</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_syscohada_rp_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-85 - 83</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr24" model="account.report.line">
                        <field name="name">XH | INCOME OUTSIDE OF ORDINARY ACTIVITIES (Sum of TN to RP)</field>
                        <field name="code">SYSCOHADA_Resultat_HAO</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr24_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Prod_cess_immo.balance + SYSCOHADA_Autres_prod_HAO.balance + SYSCOHADA_Val_comp_cess_immo.balance + SYSCOHADA_Autres_chrg_HAO.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_line_01_syscohada_prct" model="account.report.line">
                                <field name="name">RQ | Employee Share Ownership</field>
                                <field name="code">SYSCOHADA_Part_trav</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_01_syscohada_prct_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-87</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_line_01_syscohada_ir" model="account.report.line">
                                <field name="name">RS | Income taxes</field>
                                <field name="code">SYSCOHADA_Impots_resultat</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_line_01_syscohada_ir_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-89</field>
                                        <field name="date_scope">normal</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_line_01_syscohada_cdr25" model="account.report.line">
                        <field name="name">XI | NET INCOME (XG+XH+RQ+RS)</field>
                        <field name="code">SYSCOHADA_XI</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_line_01_syscohada_cdr25_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SYSCOHADA_Resultat_AO.balance + SYSCOHADA_Resultat_HAO.balance + SYSCOHADA_Part_trav.balance + SYSCOHADA_Impots_resultat.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
    <record id="action_account_report_sypnl" model="ir.actions.client">
        <field name="name">Profit and Loss</field>
        <field name="tag">account_report</field>
        <field name="context" eval="{'report_id': ref('account_financial_report_l10n_pcg_cdr')}"/>
    </record>
    <record id="account_financial_report_line_03_1_6_syscohada_bilan_passif" model="account.report.line">
        <field name="action_id" ref="action_account_report_sypnl"/>
    </record>
</odoo>
