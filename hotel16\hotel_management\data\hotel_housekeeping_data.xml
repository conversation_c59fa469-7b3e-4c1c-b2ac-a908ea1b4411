<?xml version="1.0" encoding="utf-8"?>
<openerp>
    <data noupdate="1">

      <record id="consume_product_location_id1" model="stock.location">
            <field name="name">Consume Product</field>
            <field name="usage">view</field>
            <field name="active">TRUE</field>
            <field name="location_id">3</field>
            <field name="scrap_location">FALSE</field>
            <field name="company_id">1</field>
            <!--<field name="chained_location_type">none</field>-->
            <!--<field name="chained_auto_packing">manual</field>-->
			<field name="company_id" eval="False"/>
        </record>
		
		<record id="banq_management_category" model="product.category">
            <field name="name">Foods</field>
            <field name="ismenutype">True</field>
        </record>
		
    </data>
</openerp>

