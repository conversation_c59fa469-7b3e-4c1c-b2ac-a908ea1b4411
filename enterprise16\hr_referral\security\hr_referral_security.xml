<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="group_hr_recruitment_referral_user" model="res.groups">
        <field name="name">User : Referral only</field>
        <field name="category_id" ref="base.module_category_human_resources_recruitment"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_hr_referral_reward_responsible_user" model="res.groups">
        <field name="name">Referral Reward Responsible User</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('hr_referral.group_hr_recruitment_referral_user'))]"/>
    </record>

    <record id="hr_recruitment.group_hr_recruitment_user" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('hr_referral.group_hr_recruitment_referral_user'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
    </record>

    <data noupdate="1">

    <record id="hr_referral_reward_responsible_user_rule" model="ir.rule">
        <field name="name">Referral Rewards Responsible User</field>
        <field name="model_id" ref="model_hr_referral_reward"/>
        <field name="domain_force">[('gift_manager_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('hr_referral.group_hr_referral_reward_responsible_user'))]"/>
    </record>

    <record id="hr_referral_reward_manager_user_rule" model="ir.rule">
        <field name="name">Manager Referral Rewards</field>
        <field name="model_id" ref="model_hr_referral_reward"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
    </record>

    <record id="hr_referral_points_emp_rule" model="ir.rule">
        <field name="name">Employee Referral Points</field>
        <field name="model_id" ref="model_hr_referral_points"/>
        <field name="domain_force">[('ref_user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="hr_referral_points_manager_rule" model="ir.rule">
        <field name="name">Manager Referral Points</field>
        <field name="model_id" ref="model_hr_referral_points"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
    </record>

    <record id="hr_applicant_referral_user_rule" model="ir.rule">
        <field name="name">User Referral Applicants</field>
        <field name="model_id" ref="model_hr_applicant"/>
        <field name="domain_force">[('ref_user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('hr_referral.group_hr_recruitment_referral_user'))]"/>
    </record>

    <record id="hr_applicant_officer_rule" model="ir.rule">
        <field name="name">Officer Applicants</field>
        <field name="model_id" ref="model_hr_applicant"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <record model="ir.rule" id="hr_referral_alert_comp_rule">
        <field name="name">Referral Alerts multi-company</field>
        <field name="model_id" ref="model_hr_referral_alert"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record model="ir.rule" id="hr_referral_reward_comp_rule">
        <field name="name">Referral Reward multi-company</field>
        <field name="model_id" ref="model_hr_referral_reward"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record model="ir.rule" id="hr_hr_referral_onboarding_comp_rule">
        <field name="name">Referral Onboarding multi-company</field>
        <field name="model_id" ref="model_hr_referral_onboarding"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>
    </data>
</odoo>
