# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_timesheet
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON><PERSON><PERSON><PERSON>skiSAMSAIT <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_count
msgid "# Tickets"
msgstr "# Tickets"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_team_view_form_inherit_helpdesk_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Erfasst</span>"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__analytic_account_id
msgid "Analytic Account"
msgstr "Kostenstelle"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Kostenstellenbuchung"

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Confirm Time Spent"
msgstr "Aufgewendete Zeit bestätigen"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Kundendiensttickets in Aufgaben umwandeln"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Projektaufgaben in Tickets umwandeln"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_create_timesheet
msgid "Create Timesheet from ticket"
msgstr "Zeiterfassungbogen aus Ticket erstellen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Days Spent"
msgstr "Aufgewendete Tage"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Delete"
msgstr "Löschen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__department_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__department_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
msgid "Department"
msgstr "Abteilung"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Beschreiben Sie Ihre Aktivität ..."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description"
msgstr "Beschreibung"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description of the ticket..."
msgstr "Ticket-Beschreibung"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Discard"
msgstr "Verwerfen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__display_task
msgid "Display Task"
msgstr "Aufgabe anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer
msgid "Display Timer"
msgstr "Timer anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_pause
msgid "Display Timer Pause"
msgstr "Timer-Pause anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_resume
msgid "Display Timer Resume"
msgstr "Timer-Fortsetzung anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "Primären Start des Timers anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr "Sekundären Start des Timers anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_stop
msgid "Display Timer Stop"
msgstr "Timer-Stopp anzeigen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr "Zeiterfassung anzeigen"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr "Dauer"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__employee_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__employee_id
msgid "Employee"
msgstr "Mitarbeiter"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Zeit in Tagen erfassen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__has_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__has_helpdesk_team
msgid "Has Helpdesk Teams"
msgstr "Hat Kundendienstteams"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__helpdesk_team
msgid "Helpdesk Team"
msgstr "Kundendienstteam"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__helpdesk_ticket_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_timesheets_analysis_report__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "Kundendienstticket"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__total_hours_spent
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__total_hours_spent
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_cohort_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_graph_main_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_pivot_main_inherit_helpdesk_timesheet
msgid "Hours Spent"
msgstr "Aufgewendete Stunden"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__is_timer_running
msgid "Is Timer Running"
msgstr "Läuft ein Timer"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__manager_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__employee_parent_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
msgid "Manager"
msgstr "Manager"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Department"
msgstr "Meine Abteilung"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Team's Ratings"
msgstr "Bewertungen meines Teams"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
msgid "My Team's Tickets"
msgstr "Tickets meines Teams"

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "New"
msgstr "Neu"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause"
msgstr "Pause"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause timer"
msgstr "Timer pausieren"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project"
msgstr "Projekt"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid ""
"Project to which the timesheets of this helpdesk team's tickets will be "
"linked."
msgstr ""
"Projekt, mit dem die Zeiterfassungsbögen für die Tickets dieses "
"Kundendienstteams verknüpft werden sollen."

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Record a new activity"
msgstr "Neue Aktivität erfassen"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume"
msgstr "Fortsetzen"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume timer"
msgstr "Timer fortsetzen"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLA-Statusanalyse"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save"
msgstr "Speichern"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save time"
msgstr "Zeit speichern"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start"
msgstr "Start"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start timer"
msgstr "Timer starten"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop"
msgstr "Stopp"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop timer"
msgstr "Timer stoppen"

#. module: helpdesk_timesheet
#: model:project.project,label_tasks:helpdesk_timesheet.helpdesk_team3_projet
msgid "Tasks"
msgstr "Aufgaben"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_search_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_tree_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_report_search_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.my_timesheet_view_form_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_table
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_view_form_helpdesk
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Ticketanalyse"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
msgid "Ticket for which we are creating a sales order"
msgstr "Ticket, für das wir einen Verkaufsauftrag erstellen"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "Ticket:"
msgstr "Ticket:"

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/project.py:0
#: code:addons/helpdesk_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_ids
#, python-format
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__time_spent
msgid "Time"
msgstr "Zeit"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
msgid ""
"Time spent on this ticket. By default, your timesheets will be linked to the sales order item of your ticket.\n"
"Remove the sales order item to make your timesheet entries non billable."
msgstr ""
"Für dieses Ticket aufgewendete Zeit. Standardmäßig sind Ihre Zeiterfassungsbögen mit der Verkaufsauftragsposition Ihres Tickets verknüpft.\n"
"Entfernen Sie die Verkaufsauftragsposition, um Ihre Zeiterfassungsbögen nichtabrechenbar zu machen."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_pause
msgid "Timer Last Pause"
msgstr "Timer zuletzt pausiert"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_start
msgid "Timer Start"
msgstr "Timer-Start"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr "Zeiterfassungsaktivitäten"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Codierungseinheit für Zeiterfassung"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr "Zeiterfassung im Team aktiviert"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
#: model:ir.actions.report,name:helpdesk_timesheet.timesheet_report_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
#: model:res.groups,name:helpdesk_timesheet.group_use_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "Timesheets"
msgstr "Zeiterfassung"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Zeiterfassungsanalysebericht"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Summe erf. Zeit"

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Erfassen Sie täglich Ihre Arbeitsstunden nach Projekten und berechnen Sie "
"sie an Ihre Kunden."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__user_timer_id
msgid "User Timer"
msgstr "Timer des Benutzers"

#. module: helpdesk_timesheet
#: model:project.project,name:helpdesk_timesheet.helpdesk_team3_projet
msgid "VIP Support"
msgstr "VIP-Support"

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/analytic.py:0
#, python-format
msgid ""
"You cannot link a timesheet entry to a task and a ticket at the same time."
msgstr ""
"Sie können keinen Zeiterfassungseintrag gleichzeitig mit einer Aufgabe und "
"einem Ticket verbinden."

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "for the"
msgstr "für das"
