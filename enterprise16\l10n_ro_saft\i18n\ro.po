# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ro_saft
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-07 10:21+0000\n"
"PO-Revision-Date: 2023-08-07 10:21+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_type_id
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_type_id
msgid ""
"A 3-digit number defined by ANAF to identify a type of tax in the D.406 "
"export (e.g. 300 for VAT, 150 for withholding taxes on dividends...)"
msgstr ""
"Un număr din 3 cifre definit de ANAF pentru a identifica un tip de impozit "
"în exportul D.406 (de ex. 300 pentru TVA, 150 pentru impozite reținute la "
"sursă pe dividende...)"

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_code
#: model:ir.model.fields,help:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_code
msgid ""
"A 6-digit number defined by ANAF to precisely identify taxes in the D.406 "
"export (e.g. 310309 for domestic 19% VAT)"
msgstr ""
"Un număr din 6 cifre definit de ANAF pentru a identifica cu precizie taxele "
"în exportul D.406 (de ex. 310309 pentru TVA internă de 19%)"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__a
msgid "A: general commercial companies using the general CoA for businesses"
msgstr ""
"A: societățile comerciale generale care aplică planul de conturi pentru "
"societăți generale"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_report
msgid "Accounting Report"
msgstr "Raport Contabil"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_539
msgid "Addiction fee for slotmachine gaming features"
msgstr "Taxa de viciu pentru jocurile de noroc caracteristice slotmachine"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_114
msgid ""
"Additional 20% share of investment value according to art. 38 paragraph(13) "
"from the Fiscal Code"
msgstr ""
"Cota suplimentara de 20% din valoarea investitiilor prevazute la art.38 "
"alin(13) din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_128
msgid "Additional offshore Corporate Income Tax"
msgstr "Impozit asupra veniturilor suplimentare offshore"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_540
msgid "Administrative fees for applying for a license for remote gambling"
msgstr ""
"Taxe administrative pentru solicitarea licenței pentru jocurile de noroc la "
"distanță"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_102
msgid ""
"Advance payments for annual income tax due by commercial banks, Romanian "
"legal persons and Romanian bank subsidiaries, foreign legal persons"
msgstr ""
"Plati anticipate in contul impozitului pe profit anual datorat de bancile "
"comerciale,persosne juridice romane si sucursalele din Romania ale bancilor,"
" persoane juridice straine"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_626
msgid ""
"Advance payments of the tax for some incomes from independent activities, as"
" well as for incomes from other sources provided in art. 114 para. (2) lit. "
"k ^ 1) of Law 227/2015"
msgstr ""
"Plăți anticipate ale impozitului pentru unele venituri din activități "
"independente, precum și pentru venituri din alte surse prevăzute la art. 114"
" alin.(2) lit. k ^ 1) din Legea 227/2015"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_998
msgid ""
"Amounts collected for the state budget, the state insurance budget and the "
"budget of the Single National Health Insurance Fund, currently being "
"distributed"
msgstr ""
"Sume încasate pentru bugetul de stat, bugetul asigurarilor de stat și "
"bugetul Fondului național unic de asigurări sociale de sănătate, în curs de "
"distribuire"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_344
msgid ""
"Amounts deducted from VAT for state pre-university educational institutions,"
" nurseries, county and local centers of agricultural consulting and support "
"for child protection system"
msgstr ""
"Sume defalcate din tva pentru institutiile de invatamant preuniversitar de "
"stat, crese, centre judetene si locale de consultante agricola precum si "
"pentru sustinerea sistemului de protectie a copilului"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_954
msgid ""
"Amounts from the recovery of debts arising from undue income related to the "
"state budget"
msgstr ""
"Sume din recuperarea debitelor provenite din drepturile necuvenite aferente "
"bugetului de stat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_959
msgid ""
"Amounts from the recovery of debts arising from undue rights related to the "
"insurance system for accidents at work and occupational diseases"
msgstr ""
"Sume din recuperarea debitelor provenite din drepturile necuvenite aferente "
"sistemului de asigurare pentru accidente de munca și boli profesionale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_958
msgid ""
"Amounts from the recovery of debts arising from undue rights related to the "
"public pension system"
msgstr ""
"Sume din recuperarea debitelor provenite din drepturile necuvenite aferente "
"sistemului public de pensii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_951
msgid "Amounts of debt collection from undue rights"
msgstr "Sume din recuperarea debitelor provenite din drepturi necuvenite"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_811
msgid ""
"Amounts payable due to special protection and qualification of disabled "
"people"
msgstr ""
"Sume datorate privind protectia speciala si incadrarea in munca a "
"persoanelor cu handicap"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_990
msgid ""
"Amounts representing income tax and social contributions due by individuals "
"being distributed"
msgstr ""
"Sume reprezentând impozit pe venit și contribuții sociale datorate de "
"persoanele fizice în curs de distribuire"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_304
msgid "Annual - value-added tax (VAT)"
msgstr "Taxa pe valoarea adaugata- anuala"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_537
msgid "Annual duty for obtaining authorization for operating gambling games"
msgstr ""
"Taxa anuală pentru obţinerea autorizaţiei de exploatare a jocurilor de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_104
msgid "Annual tax income"
msgstr "Impozit pe profit anual"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_524
msgid "Authorization duty for operating gambling for TV bingo games"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru "
"jocurile bingo organizate prin intermediul sistemelor reţelelor TV"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_523
msgid "Authorization duty for operating gambling for bingo halls"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru "
"jocurile bingo desfăşurate în săli de joc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_521
msgid "Authorization duty for operating gambling for casino activities"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru jocuri "
"de noroc caracteristice activităţii cazinourilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_520
msgid "Authorization duty for operating gambling for fixed odds betting"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru pariuri"
" în cotă fixă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_518
msgid "Authorization duty for operating gambling for lottery games"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru "
"jocurile loto"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_522
msgid "Authorization duty for operating gambling for slot-machines"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru jocuri "
"tip slot-machine"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_519
msgid "Authorization duty for operating gambling for totalizator"
msgstr ""
"Taxe aferente autorizaţiei de exploatare a jocurilor de noroc pentru pariuri"
" mutuale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_790
msgid ""
"Authorization fees for marketing alcohol, alcoholic beverages, tobacco and "
"coffee"
msgstr ""
"Taxe de autorizare pentru comercializarea alcoolului, bauturilor alcoolice, "
"produselor din tutun si a cafelei"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_995
msgid ""
"Available from amounts collected from deduction (bank/third party) of "
"amounts owned to debtors"
msgstr ""
"Disponibil din sume încasate din înfiinţarea popririi (bancă/terţ) asupra "
"sumelor ce se cuvin debitorilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_997
msgid ""
"Available from amounts collected representing financial loss caused and "
"recovered in terms of art. 10 from Law no. 241/2005"
msgstr ""
"Disponibil din sume incasate reprezentand prejudiciul cauzat si recuperat in"
" conditiile art.10 din Legea nr. 241/2005"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_947
msgid "Available from cars emission tax"
msgstr ""
"Disponibil din taxe pentru emisiile poluante provenite de la autovehicule"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_994
msgid ""
"Available social insurance and special funds budgets, ongoing distribution"
msgstr ""
"Disponibil al bugetelor asigurarilor sociale si fondurilor speciale, in curs"
" de distribuire"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__bank
msgid ""
"BANK: banks and financial instituttions using the CoA for financial "
"institutions"
msgstr ""
"BANK: instituțiile de credit și instituțiile financiare non-bancare care "
"aplică planul de conturi pentru bănci și instituții financiar-monetare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_458
msgid "CAS payed by employer/tax payer (art.146 alin. 5^6 from Fiscal Code)"
msgstr ""
"CAS suportat de angajator/plătitorul de venit (art.146 alin. 5^6 din CF)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_457
msgid "CASS owed by retired persons who exceed the ceiling of 4000 lei"
msgstr "CASS datorată de pensionari care depășesc plafonul de 4000 lei"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_459
msgid "CASS payed by employer/tax payer (art.168 alin. 6^1 from Fiscal Code)"
msgstr ""
"CASS suportat de angajator/plătitorul de venit (art.168 alin. 6^1 din CF)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_776
msgid "Car pollution tax, compensated/ refunded"
msgstr "Taxa de poluare pentru autovehicule, de compensat/restituit"

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_account_bank_statement_line__l10n_ro_is_self_invoice
#: model:ir.model.fields,help:l10n_ro_saft.field_account_move__l10n_ro_is_self_invoice
#: model:ir.model.fields,help:l10n_ro_saft.field_account_payment__l10n_ro_is_self_invoice
msgid ""
"Check this box to indicate that this bill is a self-invoice made to register"
" a VAT event in the absence of an invoice received from a supplier."
msgstr ""
"Bifați această casetă pentru a indica că această factură este o auto-factură"
" făcută pentru a înregistra un eveniment de TVA în absența unei facturi "
"primite de la un furnizor."

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__code
msgid "Code"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_840
msgid ""
"Collections from redemption of loans granted for arrears coverage toward "
"CONEL and ROMGAZ"
msgstr ""
"Incasari din rambursarea imprumuturilor acordate pentru acoperirea "
"arieratelor catre CONEL si ROMGAZ"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_915
msgid "Collections from retained share, according to the Penal Code"
msgstr "Incasari din cota retinuta, conform Codului penal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_923
msgid ""
"Collections from the sale of seized, abandoned goods and other amounts "
"identified with the appropriate seizure law"
msgstr ""
"Incasari din valorificarea bunurilor confiscate, abandonate si alte sume "
"constatate odata cu confiscarea potrivit legii"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_759
msgid ""
"Compensation for all damages caused by the exercise of the right of passage"
msgstr ""
"Despăgubiri acordate pentru toate pagubele cauzate prin exercitarea "
"dreptului de trecere"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_754
msgid ""
"Compensation related to the right of passage (tool-tax) in exchange for the "
"limitations brought to the right of use"
msgstr ""
"Compensații aferente dreptului de trecere în schimbul limitărilor aduse "
"dreptului de folosință"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_772
msgid "Consular fees"
msgstr "Taxe consulare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_830
msgid "Contribution by tourism undertakings"
msgstr "Contributia agentilor economici din turism"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_417
msgid "Contribution for Social insurance fund"
msgstr "Contributia pentru asigurari sociale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_456
msgid ""
"Contribution for amount of medicines consumed exceeding volumes determined "
"by contracts"
msgstr ""
"Contribuţie datorată pentru volume de medicamente consumate care depăşesc "
"volumele stabilite prin contracte"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_442
msgid ""
"Contribution for vacation and compensation by people which are unable to "
"work due to a work accident or professional illnesses"
msgstr ""
"Contributia pentru concedii si indemnizatii datorate de persoanele aflate in"
" incapacitate de munca din cauza de accident de munca sau boala profesionala"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_443
msgid ""
"Contribution for vacation and compensation by people which are unable to "
"work due to a work accident or professional illnesses accounted for from the"
" insurance fund for work accidents and professional illnesses"
msgstr ""
"Contributia pentru concedii si indemnizatii datorate de persoanele aflate in"
" incapacitate de munca din cauza de accident de munca sau boala profesionala"
" suportata din fondul de asigurare pentru accidente de munca si boli "
"profesionale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_440
msgid "Contribution for vacation and compensation by unemployed people"
msgstr ""
"Contributii pt. concedii si indemnizatii datorate de persoanele aflate in "
"somaj"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_439
msgid ""
"Contribution for vacation and compensation from natural or legal persons"
msgstr ""
"Contributii pentru concedii si indemnizatii de la persoane juridice sau "
"fizice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_941
msgid ""
"Contribution to health services expenses for domestic tobacco production"
msgstr ""
"Contributia pentru finantarea unor cheltuieli de sanatate pentru produsele "
"din tutun din productia interna"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_944
msgid ""
"Contribution to health services expenses for imported alcoholic beverages"
msgstr ""
"Contributia pentru finantarea unor cheltuieli de sanatate pentru bauturile "
"alcoolice din achizitii intracomunitare/ import"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_942
msgid "Contribution to health services expenses for imported tobacco products"
msgstr ""
"Contributia pentru finantarea unor cheltuieli de sanatate pentru produsele "
"din tutun din achizitii intracomunitare/import"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_943
msgid ""
"Contribution to health services expenses for intern alcoholic beverages "
"products production"
msgstr ""
"Contributia pentru finantarea unor cheltuieli de sanatate pentru bauturile "
"alcoolice din productia interna"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_945
msgid ""
"Contribution to health services expenses from publicity activities on "
"tobacco and alcoholic beverages products"
msgstr ""
"Contributia pentru finantarea unor cheltuieli de sanatate din activitati "
"publicitare la produsele din tutun si bauturi alcoolice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_820
msgid "Contribution to public education"
msgstr "Contributia pentru invatamantul de stat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_414
msgid ""
"Contribution to supplementary pension retained from people included in the "
"social insurance system"
msgstr ""
"Contributia pentru pensia suplimentara retinuta de la persoanele cuprinse in"
" sistemul de asigurari sociale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_708
msgid "Contribution to the energy transition fund"
msgstr "Contributie la Fondul de Tranzitie Energetica"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_420
msgid "Contribution to unemployment fund"
msgstr "Contributia de asigurari pentru somaj"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_421
msgid "Contribution to unemployment fund by the employer"
msgstr "Contributia de asigurari pentru somaj datorata de angajator"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_422
msgid "Contribution to unemployment fund deducted from the insured persons"
msgstr ""
"Contributia individuala de asigurari pentru somaj retinuta de la asigurati"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_107
msgid "Corporate Income Tax exempted"
msgstr "Impozit pe profit scutit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_924
msgid "Court fines"
msgstr "Amenzi judiciare"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_850
msgid "Custom duties from legal persons"
msgstr "Taxe vamale de la persoane juridic"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__description
msgid "Description"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_760
msgid "Development tax included in the tariff of electricity and heating"
msgstr "Taxa de dezvoltare cuprinsa in tariful energiei electrice si termice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_751
msgid ""
"Directed income from the flat tax on automotive fuels delivered internally "
"by producers as well as automotive fuels consumed and imported by them"
msgstr ""
"Venituri cu destinatie speciala din cota unica asupra carburantilor auto "
"livrati la intern de producatori precum si pentru carburantii auto consumati"
" de acestia si asupra carburantilor auto importati"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_781
msgid "Dividends paid to the state budget by central public authorities"
msgstr ""
"Dividende de virat la bugetul de stat de către autorităţile publice centrale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_536
msgid "Duty for obtaining license for organizing gambling activities"
msgstr "Taxa pentru obţinerea licenţei de organizare a jocurilor de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_510
msgid ""
"Duty for organizing and operating gambling activities due until 01.01.2014"
msgstr ""
"Taxe pentru organizarea şi exploatarea jocurilor de noroc, datorate pana la "
"01.01.2014"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_932
msgid ""
"Encashments from the capitalization of confiscated, abandoned goods and "
"other amounts ascertained together with the confiscation according to the "
"law"
msgstr ""
"Încasări din valorificarea bunurilor confiscate, abandonate și alte sume "
"constatate odată cu confiscarea potrivit legii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_933
msgid ""
"Encashments from the capitalization of confiscated, abandoned goods and "
"other amounts ascertained together with the confiscation according to the "
"law by the General Directorate for Fiscal Antifraud"
msgstr ""
"Încasări din valorificarea bunurilor confiscate, abandonate și alte sume "
"constatate odata cu confiscarea potrivit legii de către Direcția Generală "
"Antifraudă Fiscală"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_791
msgid "Environmental stamp"
msgstr "Timbru de mediu"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_200
msgid "Excise"
msgstr "Accize"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_252
msgid ""
"Excise duties on fermented beverages other than beer and wine, in which the "
"absolute alcohol content (100%) resulting from the exclusive fermentation of"
" fruit, fruit juices and concentrated fruit juices is less than 50%"
msgstr ""
"Accize pentru băuturile fermentate, altele decât bere şi vinuri, la care "
"ponderea de alcool absolut (100%) provenită din fermentarea exclusivă a "
"fructelor, sucurilor de fructe şi sucurilor concentrate de fructe este mai "
"mică de 50%"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_226
msgid "Excise duties on heated tobacco products"
msgstr "Accize pentru produse din tutun încălzit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_280
msgid "Excise duty collected from the sale of air conditioners"
msgstr "Accize incasate din vanzarea  aparatelor de aer conditionat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_210
msgid ""
"Excise duty collected from the sale of alcohol, distilates and alchoholic "
"berevages, due until 31.12.2006"
msgstr ""
"Accize incasate din vanzarea de alcool, distilate si bauturi alcoolice, "
"datorate pana la 31.12.2006"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_260
msgid "Excise duty collected from the sale of cars from domestic production"
msgstr "Accize incasate din vanzarea autoturismelor din productia interna"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_230
msgid "Excise duty collected from the sale of energy products"
msgstr "Accize incasate din vanzarea produselor energetice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_261
msgid ""
"Excise duty for cars which were the subject of leases entered into before "
"1st January 2007"
msgstr ""
"Accize pentru autoturismele care au făcut obiectul unor contracte de leasing"
" iniţiate înainte de 1 ianuarie 2007"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_211
msgid "Excise duty on beer"
msgstr "Accize pentru bere"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_251
msgid ""
"Excise duty on beer / beer base in mixtures with non-alcoholic beverages, in"
" which the share of Plato grades derived from malt, malleable and / or non-"
"malleable cereals is less than 30% of the total number of Plato grades"
msgstr ""
"Accize pentru berea/baza de bere din amestecul cu băuturi nealcoolice, la "
"care ponderea gradelor Plato provenite din malţ, cereale malţificabile "
"şi/sau nemalţificabile este mai mică de 30% din numărul total de grade Plato"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_258
msgid ""
"Excise duty on bullet cartridges and other ammunition for hunting and "
"personal use weapons, other than for military or sporting use"
msgstr ""
"Accize pentru cartuşe cu glonţ şi alte tipuri de muniţie pentru armele de "
"vânătoare şi arme de uz personal, altele decât cele de uz militar sau de uz "
"sportiv"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_221
msgid "Excise duty on cigarettes"
msgstr "Accize pentru tigarete"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_222
msgid "Excise duty on cigaretts and cigarette sheets"
msgstr "Accize pentru tigari si tigati foi"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_237
msgid "Excise duty on coal and coke"
msgstr "Accize pentru carbune si cocs"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_240
msgid "Excise duty on coffee"
msgstr "Accize pentru cafea"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_233
msgid "Excise duty on diesel and biodiesel"
msgstr "Accize pentru motorină şi biodiesel"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_270
msgid "Excise duty on electricity"
msgstr "Accize pentru energie electrica"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_259
msgid ""
"Excise duty on engines of more than 100 hp, for yachts and other pleasure "
"craft"
msgstr ""
"Accize pentru motoare cu capacitate de peste 100 CP, destinate iahturilor şi"
" altor nave şi ambarcaţiuni pentru agrement"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_215
msgid "Excise duty on ethyl alcohol"
msgstr "Accize pentru alcool etilic"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_213
msgid "Excise duty on fermented, sparking beverages, other than beer and wine"
msgstr ""
"Accize pentru bauturi fermentate, spumoase, altele decat bere si vinuri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_224
msgid "Excise duty on fine-cut smoking tobacco for cigar rolling"
msgstr "Accize pentru tutun de fumat fin tăiat, destinat rulării în ţigarete"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_234
msgid "Excise duty on fuel oil"
msgstr "Accize pentru pacura"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_254
msgid "Excise duty on garments made of natural furs"
msgstr "Accize pentru confecţii din blănuri naturale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_253
msgid "Excise duty on gold and / or platinum jewelery, except wedding rings"
msgstr ""
"Accize pentru bijuterii din aur şi/sau din platină, cu excepţia verighetelor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_244
msgid "Excise duty on green coffee"
msgstr "Accize pentru cafea verde"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_257
msgid ""
"Excise duty on hunting weapons and weapons for personal use, other than for "
"military or sporting use"
msgstr ""
"Accize pentru arme de vânătoare şi arme de uz personal, altele decât cele de"
" uz militar sau de uz sportiv"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_243
msgid "Excise duty on instant coffee including instant coffee blends"
msgstr "Accize pentru cafea solubilă, inclusiv amestecuri cu cafea solubilă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_214
msgid "Excise duty on intermediate products"
msgstr "Accize pentru produse intermediare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_238
msgid "Excise duty on lamp oil (kerosene)"
msgstr "Accize pentru petrol lampant (kerosen)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_231
msgid "Excise duty on leaded petrol"
msgstr "Accize pentru benzina cu plumb"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_235
msgid "Excise duty on liquefied petroleum gas"
msgstr "Accize pentru gaz petrolier lichefiat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_227
msgid ""
"Excise duty on liquids containing nicotine for inhalation by means of an "
"electronic device such as an electronic cigarette"
msgstr ""
"Accize pentru lichidele cu conținut de nicotină destinate inhalării cu "
"ajutorul unui dispozitiv electronic tip țigaretă electronică"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_239
msgid "Excise duty on mineral oils due until 31.12.2006"
msgstr "Accize din vanzarea uleiurilor minerale datorate pana la 31.12.2006"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_236
msgid "Excise duty on natural gas"
msgstr "Accize pentru gaz natural"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_256
msgid ""
"Excise duty on on passenger cars and off-road vehicles with a cylinder "
"capacity of 3,000 cm3 or more"
msgstr ""
"Accize pentru autoturisme şi autoturisme de teren a căror capacitate "
"cilindrică este mai mare sau egală cu 3.000 cm3"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_275
msgid "Excise duty on other excise goods (energy)"
msgstr "Accize pentru alte produse  accizabile (energetice)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_225
msgid "Excise duty on other smoking tobacco"
msgstr "Accize pentru alte tutunuri de fumat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_242
msgid "Excise duty on roasted coffee including coffee with substitutes"
msgstr "Accize pentru cafea prajită, inclusiv cafea cu înlocuitori"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_220
msgid "Excise duty on sales of tobacco products"
msgstr "Accize din vanzarea produselor din tutun"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_223
msgid "Excise duty on smoking tobacco"
msgstr "Accize pentru tutun de fumat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_212
msgid "Excise duty on sparkling wines"
msgstr "Accize pentru vinuri spumoase"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_217
msgid "Excise duty on still fermented beverages, other than beer and wine"
msgstr ""
"Accize pentru băuturi fermentate liniştite, altele decât bere şi vinuri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_216
msgid "Excise duty on still wines"
msgstr "Accize pentru vinuri liniştite"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_250
msgid "Excise duty on the sales of other products due until 30 September 2013"
msgstr ""
"Accize incasate din vanzarea altor produse datorate pana la data de 30 "
"septembrie 2013"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_218
msgid ""
"Excise duty on the sales of still fermented beverages other than beer and "
"wine  due until 31.01.2011"
msgstr ""
"Accize din vanzarea de bauturi fermentate linistite, altele decât bere si "
"vinuri, datorate pana la data de 31.01.2011"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_232
msgid ""
"Excise duty on unleaded petrol and denatured bioethanol used as motor fuel "
msgstr ""
"Accize pentru benzină fără plumb şi bioetanol denaturat şi utilizat drept "
"combustibil pentru motor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_255
msgid ""
"Excise duty on yachts and other ships and boats with or without motor for "
"pleasure, other than for use in performance sports"
msgstr ""
"Accize pentru iahturi şi alte nave şi ambarcaţiuni cu sau fără motor pentru "
"agrement, cu excepţia celor destinate utilizării în sportul de performanţă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_241
msgid "Excise duty to be refunded/offset from the sale of diesel "
msgstr "Accize de restituit/de compensat din vanzarea de motorina"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_290
msgid "Excise duty to be reimbursed"
msgstr "Accize de rambursat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_283
msgid "Excisescollected when issuing fiscal banners for alcoholic beverages"
msgstr ""
"Accize incasate la eliberarea banderolelor fiscale pentru bauturi alcoolice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_415
msgid "Farmers' contribution to the pension fund and social insurance"
msgstr ""
"Contributia la fondul de pensii si asigurari sociale ale agricultorilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_551
msgid "Fee owed to the Romanian Olympic and Sports Committee"
msgstr "Taxa datorata Comitetului Olimpic si Sportiv Roman"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_552
msgid "Fee owed to the Romanian Paralympic Committee"
msgstr "Taxa datorata Comitetului Paralimpic Roman"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_777
msgid "Fees and other income from environmental protection"
msgstr "Taxe si alte venituri din protectia mediului"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_608
msgid "Financial awards income tax"
msgstr "Impozit pe veniturile din premii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_535
msgid "Gambling access fee"
msgstr "Taxa de acces pentru jocurile de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_621
msgid "Gambling income tax"
msgstr "Impozit pe veniturile din jocuri de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_517
msgid "Gambling license duty for TV bingo games"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru jocurile "
"bingo organizate prin intermediul sistemelor reţelelor TV"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_516
msgid "Gambling license duty for bingo halls"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru jocurile "
"bingo desfăşurate în săli de joc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_514
msgid "Gambling license duty for casino activities"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru jocuri de "
"noroc caracteristice activităţii cazinourilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_513
msgid "Gambling license duty for fixed odds betting"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru pariuri în "
"cotă fixă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_511
msgid "Gambling license duty for lottery games"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru jocurile "
"loto"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_515
msgid "Gambling license duty for slot-machines"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru jocuri tip "
"slot-machine"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_512
msgid "Gambling license duty for totalizator"
msgstr ""
"Taxe aferente licenţei de organizare a jocurilor de noroc pentru pariuri "
"mutuale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_503
msgid "Gambling social stamp tax"
msgstr "Taxa de timbru social asupra jocurilor de noroc"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "Manipulare personalizată a Cartii Mari"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Generate SAF-T"
msgstr "Generați SAF-T"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_303
msgid "Half-yearly - value-added tax (VAT)"
msgstr "Taxa pe valoarea adaugata- semestrial"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_430
msgid "Health insurance contribution"
msgstr "Contributia pentru asigurari de sanatate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_460
msgid "Health insurance contribution by natural persons - regularizations"
msgstr ""
"Contributia de asigurari sociale de sanatate datorata de persoane fizice - "
"Regularizari"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_441
msgid ""
"Health insurance contribution by retirees for income that exceeds the "
"threshold required by law"
msgstr ""
"Contributia pentru asigurari sociale de sanatate datorate de pensionari "
"pentru partea de venit care depaseste plafonul prevazut de lege"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_431
msgid "Health insurance contribution by the employer"
msgstr "Contributia pentru asigurari de sanatate datorata de angajator"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_448
msgid ""
"Health insurance contribution by the employer for people who are in medical "
"leave for incapacity of work, due to a work accident or professional "
"illnesses supported by FAAMBP, according to the Law no. 95/2006"
msgstr ""
"Contribuţia de asigurări sociale de sănătate datorată de angajator pentru "
"persoanele care se află în concediu medical pentru incapacitate de muncă, "
"urmare unui accident de muncă sau boală profesională suportată din FAAMBP, "
"conform Legii nr.95/2006"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_432
msgid "Health insurance contribution deducted from the insured persons"
msgstr "Contributia pentru asigurari de sanatate retinuta de la asigurati"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_444
msgid ""
"Health insurance contribution for people from families that are entitled to "
"welfare, according to Law no. 416/2001 regarding the minimum guaranteed "
"income, with subsequent amendments"
msgstr ""
"Contributia de asigurari sociale de sanatate datorata pentru persoanele care"
" fac parte dintr-o familie care are dreptul la ajutor social, potrivit Legii"
" nr. 416/2001 privind venitul minim garantat, cu modificarile si "
"completarile ulterioare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_434
msgid "Health insurance contribution for people in ongoing military service"
msgstr ""
"Contributia pentru asigurari de sanatate datorata pentru persoanele care "
"satisfac stagiul militar in termen"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_438
msgid ""
"Health insurance contribution for people in sick leave, according to the Law"
" no. 95/2006 regarding healthcare reform"
msgstr ""
"Contributia de asigurari sociale de sanatate, datorata pentru persoanele "
"care se afla in concediu medical, conform Legii nr.95/2006 privind reforma "
"in domeniul sanatatii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_435
msgid ""
"Health insurance contribution for people who perform custodial sentence or "
"remand"
msgstr ""
"Contributia pentru asigurari de sanatate datorata pentru persoanele care "
"executa pedepse privative de libertate sau aflate in arest preventiv"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_433
msgid ""
"Health insurance contribution for people whose duties are paid from the "
"Unemployment Insurance Fund"
msgstr ""
"Contributia pentru asigurari de sanatate datorata pentru persoane pentru "
"care plata drepturilor se suporta din bugetul asigurarilor pentru somaj"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_437
msgid ""
"Health insurance contribution for persons in parental leave up to the age of"
" 2 and for a disabled child up to the age of 3"
msgstr ""
"Contributia de asigurari de sanatate pentru persoanele aflate in concediu "
"pentru cresterea copilului pana la implinirea varstei de 2 ani si in cazul "
"copilului cu handicap pana la implinirea varstei de 3 ani"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_436
msgid ""
"Health insurance contribution for sick leaves which is deducted from social "
"insurance contribution by the employer"
msgstr ""
"Contributia pentru asigurari de sanatate calculata pentru concediile "
"medicale care se deduce din contributia de asigurari sociale datorata de "
"angajator"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_955
msgid "Hedge fund"
msgstr "Fond de risc"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__i
msgid ""
"I: non-residents and taxpayers with obligation to submit a special VAT "
"return"
msgstr ""
"I: nerezidenți/Contribuabilii care au obligaţia să depună decontul special "
"de taxă pe valoarea adăugată"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__id
msgid "ID"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__ifn
msgid ""
"IFN: non-bank financial institutions using the CoA of BNR Reg. no. 17/2015"
msgstr ""
"IFN: instituțiile financiare non-bancare care aplică planul de conturi "
"conform Reglementării BNR nr.17/2015"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__ifrs
msgid ""
"IFRS: general commercial companies using the CoA for companies applying IFRS"
msgstr ""
"IFRS: societățile comerciale generale care aplică prevederile OMFP 2844/2016"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__insurance
msgid "INSURANCE: insurance companies using the CoA for insurance companies"
msgstr ""
"INSURANCE: societățile de asigurări care aplică planul de conturi pentru "
"societăți de asigurări"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"In the Company settings, please set your company VAT number under `Tax ID` "
"if registered for VAT, or your CUI number under `Company Registry`."
msgstr "În setările companiei, vă rugăm să setați numărul dvs. de TVA al "
"companiei la „Cod fiscal” dacă sunteți înregistrat pentru TVA sau numărul dvs. "
"CUI în „Registrul companiei”."

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_911
msgid ""
"Income for state social insurance budget from fines and other penalties "
"imposed under legal provisions"
msgstr ""
"Venituri la bugetul asigurarilor sociale de stat, din amenzi si alte "
"sanctiuni aplicate potrivit dispozitiile legale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_950
msgid "Income from capitalization of public goods"
msgstr "Venituri din valorificarea unor bunuri ale institutiilor publice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_961
msgid ""
"Income from commission charged by territorial labor inspectorates due until "
"31 December 2010"
msgstr ""
"Venituri din comisionul perceput de catre inspectoratele teritoriale de "
"munca, datorat pana la data de 31 decembrie 2010"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_922
msgid "Income from compensations"
msgstr "Venituri din despagubiri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_771
msgid "Income from custom benefits tax"
msgstr "Venituri din taxe pentru prestatii vamale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_957
msgid "Income from expenses recovery incurred in the process of foreclosure"
msgstr ""
"Venituri din recuperarea cheltuielilor efectuate in cursul procesului de "
"executare silita"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_913
msgid "Income from fines applied to non-residents"
msgstr "Venituri din amenzi aplicate persoanelor nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_971
msgid ""
"Income from guarantees granted and paid to credit institutions within <PRIMA"
" CASA> program"
msgstr ""
"Venituri rezultate din garantiile acordate si platite institutiilor de "
"credit in cadrul programului <PRIMA CASA>"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_774
msgid "Income from provision of services"
msgstr "Venituri din prestari servicii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_952
msgid "Income from recovery of court fees, imputations and compensations"
msgstr ""
"Venituri din recuperarea cheltuielilor de judecata, imputatii si despagubiri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_773
msgid "Income from technical, judicial and extrajudicial expertise"
msgstr "Venituri din expertiza tehnica, judiciara si extrajudiciara"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_765
msgid "Income from the application of the extinctive prescription"
msgstr "Venituri din aplicarea prescriptiei extinctive"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_753
msgid ""
"Income from the recovery of payments in advance for state legal expenses "
"from non-residents"
msgstr ""
"Venituri din recuperarea cheltuielilor judiciare avansate de stat de la "
"persoanele nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_100
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_600
msgid "Income tax"
msgstr "Impozit pe venit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_814
msgid "Income tax deducted shares"
msgstr "Cote defalcate din impozitul pe venit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_105
msgid ""
"Income tax due by foreign legal persons, other than those from section 1, "
"due by foreign legal persons that carry out activities by means of a "
"permanent office in Romania"
msgstr ""
"Impozit pe profit datorat de persoane juridice străine, altele decât cele de"
" la pct.1, datorate de către persoanele juridice străine care desfăşoară "
"activitate prin intermediul unui sediu permanent în România"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_111
msgid ""
"Income tax exempted according to art. 38 paragraph(1) from the Fiscal Code"
msgstr "Impozit pe profit scutit conform art.38 alin(1) din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_112
msgid ""
"Income tax exempted according to art. 38 paragraph(3) from the Fiscal Code"
msgstr "Impozit pe profit scutit conform art.38 alin(3) din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_113
msgid ""
"Income tax exempted according to art. 38 paragraph(8), (9) or (11) from the "
"Fiscal Code"
msgstr ""
"Impozit pe profit scutit conform art.38 alin(8), (9) sau (11) din Codul "
"fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_160
msgid ""
"Income tax for Romanian offices of foreign commercial and economic "
"organizations"
msgstr ""
"Impozitul pe venitul reprezentantelor din Romania ale societatilor "
"comerciale si organizatiilor economice straine"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_622
msgid ""
"Income tax for individuals' income from an association with a taxpayer legal"
" entity, according to Title II of the Fiscal Code"
msgstr ""
"Impozit pe veniturile realizate de persoanele fizice dintr-o asociere cu o "
"persoană juridică contribuabil, potrivit titlului II din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_770
msgid "Income tax from higher education institutions"
msgstr "Taxa din veniturile institutiilor de invatamant superior"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_601
msgid "Income tax from independent services"
msgstr "Impozit pe veniturile din activitati independente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_623
msgid ""
"Income tax on natural persons' income from an association with a taxable "
"legal person, according to title III of the Fiscal Code"
msgstr ""
"Impozit pe veniturile realizate de persoanele fizice dintr-o asociere cu o "
"persoană juridică contribuabil, potrivit titlului III din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_675
msgid "Income tax related to the Unique Declaration"
msgstr "Impozit pe venit aferent declarației unice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_103
msgid ""
"Income tax/ advance payments for annual income tax due by Romanian legal "
"persons, others than those from section 1 and legal persons with "
"headquarters in Romania"
msgstr ""
"Impozit pe profit /plăţi anticipate în contul impozitului pe profit anual "
"datorat /datorate de persoane juridice române, altele decât cele de la "
"pct.1, precum şi de persoanele juridice cu sediul social în România"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_762
msgid "Incomes from judicial stamp"
msgstr "Venituri din timbru judiciar"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_925
msgid "Increases due to revenue not paid on time"
msgstr "Majorari de intarziere pentru venituri nevarsate la termen"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_467
msgid "Individual contribution to social security (OMEF1646/2007)"
msgstr "Contributii individuale asigurari sociale (OMEF1646/2007)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_825
msgid ""
"Individual contribution to the state budget (pensions for military personel)"
msgstr "Contribuție individuală la bugetul de stat (pensie militari)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_424
msgid ""
"Individual contribution to unemployment fund by people who obtain "
"professional income other than salary"
msgstr ""
"Contribuţia individuală de asigurări pentru şomaj datorată de persoanele "
"care realizează venituri de natură profesională, altele decât cele de natură"
" salarială"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_462
msgid ""
"Individual health insurance contribution by people with revenue from "
"activities performed under contracts / civil agreements concluded according "
"to the Civil Code as well as agent's contract"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venituri din activităţi desfăşurate în baza "
"contractelor/convenţiilor civile încheiate potrivit Codului civil, precum şi"
" a contractelor de agent"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_463
msgid ""
"Individual health insurance contribution by people with revenue from "
"technical, judicial and extrajudicial expertise"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate care realizează "
"venituri din activitatea de expertiză contabilă şi tehnică, judiciară şi "
"extrajudiciară"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_464
msgid ""
"Individual health insurance contribution by people with revenues from an "
"association with a micro-enterprise, according to the IV^1 Title from the "
"Fiscal Code, which is not a legal person"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venitul obţinut dintr-o asociere cu o  "
"microintreprindere, potrivit titlului IV^1 din Codul Fiscal, care nu "
"generează o persoană juridică"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_470
msgid ""
"Individual health insurance contribution by people with revenues from "
"granting property"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venituri din cedarea folosintei bunurilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_468
msgid ""
"Individual health insurance contribution by people with revenues from "
"independent activities or by people with no revenue"
msgstr ""
"Contributia de asigurari sociale de sanatate datorate de persoanele care "
"realizeaza venituri din activati independente si persoanele care nu "
"realizeaza venituri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_461
msgid ""
"Individual health insurance contribution by people with revenues from "
"intellectual property rights"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venituri din drepturi de proprietate intelectuală"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_469
msgid ""
"Individual health insurance contribution by people with revenues from leases"
" of agricultural goods, withholding income tax"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venituri din arendarea bunurilor agricole, în "
"regim de reţinere la sursă a impozitului pe venit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_465
msgid ""
"Individual health insurance contribution by people with revenues, "
"withholding income tax from associations with no legal persons referred in "
"art. 13 letter e) from Tax Code"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate  datorată de  "
"persoanele care realizează venituri, în regim de reţinere la sursă a "
"impozitului pe venit, din asocierile fără personalitate juridică prevăzute "
"la art. 13 lit. e) din Codul Fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_466
msgid ""
"Individual health insurance contribution by people with revenues, "
"withholding tax on income from agricultural associations referred in art. 71"
" letter d) from Tax Code"
msgstr ""
"Contribuţia individuală de asigurări sociale de sănătate  datorată de  "
"persoanele care realizează venituri, în regim de reţinere la sursă a "
"impozitului pe venit, din activităţile agricole prevăzute la art. 71 lit. d)"
" din Codul Fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_447
msgid ""
"Individual health insurance contribution for monastic staff of recognized "
"religions"
msgstr ""
"Contribuţia individuală de sănătate datorată pentru personalul monahal al "
"cultelor recunoscute"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_445
msgid ""
"Individual health insurance contribution for persons located in detention "
"centers for deportation, and for persons undergoing procedures to establish "
"their identity that are accommodated in special centers"
msgstr ""
"Contribuţia individuală de sănătate datorată pentru persoanele aflate în "
"centrele de cazare în vederea returnării ori expulzării, precum şi pentru "
"persoanele care se află în timpul procedurilor necesare stabilirii "
"identităţii şi sunt cazaţi în centrele special amenajate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_446
msgid ""
"Individual health insurance contribution for persons who are undertaking "
"measures provided in art. 105, 113 and 114 from the Penal Code and for "
"persons who are in postponement or interruption of imprisonment sentence"
msgstr ""
"Contribuţia individuală de sănătate datorată pentru persoanele care se află "
"în executarea măsurilor prevăzute la art.105, 113, 114 din Codul penal, "
"precum şi pentru persoanele care se află în perioada de amânare sau "
"întrerupere a executării pedepsei privative de libertate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_449
msgid ""
"Individual health insurance contribution for persons, Romanian citizens who "
"are victims of human trafficking, for a period of maximum 12 months"
msgstr ""
"Contribuţia individuală de sănătate datorată pentru persoanele, cetăţeni "
"români, care sunt victime ale traficului de persoane, pentru o perioadă de "
"cel mult 12 luni"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_419
msgid ""
"Individual social insurance contribution by people who obtain professional "
"income other than salary"
msgstr ""
"Contribuţia individuală de asigurări sociale datorată de persoanele care "
"realizează venituri de natură profesională, altele decât cele de natură "
"salarială"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_452
msgid ""
"Individual social insurance contribution by people with revenue from "
"activities performed under contracts / civil agreements concluded according "
"to the Civil Code as well as agent's contract"
msgstr ""
"Contribuţia individuală de asigurări sociale datorată de persoanele care "
"realizează venituri din activităţi desfăşurate în baza "
"contractelor/convenţiilor civile încheiate potrivit Codului civil, precum şi"
" a contractelor de agent"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_451
msgid ""
"Individual social insurance contribution by people with revenue from "
"intellectual property"
msgstr ""
"Contribuţia individuală de asigurări sociale datorată de persoanele care "
"realizează venituri din drepturi de proprietate intelectuală "

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_453
msgid ""
"Individual social insurance contribution by people with revenue from "
"technical, judicial and extrajudicial expertise"
msgstr ""
"Contribuţia individuală de asigurări sociale datorată de persoanele care "
"realizează venituri din activitatea de expertiză contabilă şi tehnică, "
"judiciară şi extrajudiciară"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_480
msgid "Insurance contribution for labour"
msgstr "Contribuție asiguratorie pentru muncă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_611
msgid "Intellectual property income tax"
msgstr "Impozit pe veniturile din drepturi de proprietate intelectuala"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_305
msgid "Interest and penalties on late payment of VAT"
msgstr "Dobanzi si penalitati de intarziere aferente T.V.A."

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_605
msgid "Interest tax"
msgstr "Impozit pe veniturile din dobanzi"

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.view_move_form_l10n_ro
msgid "Is self invoice (RO)?"
msgstr "Este autofactura (RO)?"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_bank_statement_line__l10n_ro_is_self_invoice
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_move__l10n_ro_is_self_invoice
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_payment__l10n_ro_is_self_invoice
msgid "Is self invoice?"
msgstr "Este autofactura?"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_move
msgid "Journal Entry"
msgstr "Notă contabilă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_761
msgid "Judicial stamp taxes"
msgstr "Taxe judiciare de timbru"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_l10n_ro_saft_tax_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_619
msgid "Leasing agricultural goods income tax"
msgstr "Impozit pe veniturile din arendarea bunurilor agricole"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_606
msgid ""
"Liquidation / dissolution without liquidation of a legal person income tax"
msgstr ""
"Impozite pe veniturile din lichidare/dizolvare fara lichidare a unei "
"persoane juridice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_127
msgid "Micro-enterprise Income Tax exempt"
msgstr "Impozit pe veniturile microintreprinderilor scutit"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_121
msgid "Micro-enterprise income tax"
msgstr "Impozit pe veniturile microintreprinderi"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_120
msgid "Micro-enterprises' income tax"
msgstr "Impozit pe veniturile microintreprinderilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_755
msgid "Mining royalties"
msgstr "Redevente miniere"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_301
msgid "Monthly - value-added tax (VAT)"
msgstr "Taxa pe valoarea adaugata- lunar"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_541
msgid ""
"Monthly fee calculated on the total participation fees collected monthly, "
"due by the organizers of online gambling, provided in GEO 77/2009 on the "
"organization and operation of gambling"
msgstr ""
"Taxa lunară calculată la totalul taxelor de participare încasate lunar, "
"datorată de organizatorii de jocuri de noroc online, prevazută în OUG "
"77/2009 privind organizarea și exploatarea jocurilor de noroc"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma14
msgid ""
"NORMA14: private pension companies using the CoA of ASF Reg. no. 14/2015"
msgstr ""
"NORMA14: societățile din domeniul pensiilor private care aplică IFRS (Norma "
"14/2015) a Autorității de Supraveghere Financiară (ASF)"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma36
msgid ""
"NORMA36: insurance and/or reinsurance brokerage companies using the CoA of "
"ASF Reg. no. 36/2015"
msgstr ""
"NORMA36: societățile de brokeraj de asigurări și/sau reasigurări care aplică"
" reglementarea nr. 36/2015 a Autorității de Supraveghere Financiară (ASF)"

#. module: l10n_ro_saft
#: model:ir.model.fields.selection,name:l10n_ro_saft.selection__res_company__l10n_ro_saft_tax_accounting_basis__norma39
msgid ""
"NORMA39: leasing and financial companies using the IFRS CoA according to ASF"
" Reg. no. 39/2015"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_710
msgid "Natural gas and oil intern production tax"
msgstr "Impozit la titeiul si la gazele naturale din productia interna"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_927
msgid "Non-tax fileing penalties"
msgstr "Penalități de nedeclarare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_756
msgid "Oil royalties"
msgstr "Redevente petroliere"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_931
msgid "Other direct tax collection"
msgstr "Alte incasari din impozite directe"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_926
msgid "Other fines, penalties and confiscations"
msgstr "Alte amenzi, penalitati si confiscari"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_970
msgid "Other income"
msgstr "Alte venituri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_953
msgid "Other incomes from capitalization of assets"
msgstr "Alte venituri din valorificarea unor bunuri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_757
msgid "Other revenues from concessions and rentals of Public Institutions"
msgstr ""
"Alte venituri din concesiuni si inchirieri de catre Institutii publice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_999
msgid "Other taxes, fees, contributions"
msgstr "Alte impozite, taxe, contribuții"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_810
msgid "Payments from legal persons for unqualified disabled people"
msgstr ""
"Varsaminte de la persoanele juridice, pentru persoanele cu handicap "
"neincadrate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_780
msgid "Payments from net profit of autonomous administrations"
msgstr "Varsaminte din profitul net al regiilor autonome"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_101
msgid "Payments from net revenue of National Bank of Romania"
msgstr "Varsaminte din veniturile nete ale Bancii Nationale a Romaniei"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_778
msgid ""
"Payments from public institutions' liquidities and self-financed activities"
msgstr ""
"Varsaminte din disponibilitatile institutiilor publice si activitatilor "
"autofinantat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_960
msgid "Payments from salary reduction"
msgstr "Vărsăminte din reducerea drepturilor salariale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_699
msgid "Payroll Taxes (residual)"
msgstr "Impozit pe salarii (restante)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_602
msgid "Payroll taxes"
msgstr "Impozit pe veniturile din salarii si asimilate salariilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_921
msgid "Penalties due to scheduled payments"
msgstr "Penalitati datorate in cazul esalonarilor la plata"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_920
msgid ""
"Penalties for failure to file or late filing of the declaration of taxes"
msgstr ""
"Penalitati pentru nedepunerea sau depunerea cu intarziere a declaratiei de "
"impozite si taxe"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_721
msgid "Penalties not paid on time"
msgstr "Penalitati neachitare la termen"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_607
msgid "Pensions income tax"
msgstr "Impozit pe veniturile din pensii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_504
msgid ""
"Periodically regulated annual gambling authorization fee, according to "
"income"
msgstr ""
"Taxa anuală de autorizare a jocurilor de noroc, regularizată periodic, în "
"funcţie de realizări"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_502
msgid ""
"Periodically regulated tax on gambling according to income within reporting "
"time"
msgstr ""
"Taxa pe jocuri de noroc regularizata periodic in functie de realizari, "
"datorata     pentru perioada de raportare"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Bank Account` for your company."
msgstr "Vă rugăm să definiți un `Cont bancar` pentru compania dumneavoastră."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Telephone Number` for your company."
msgstr ""
"Vă rugăm să definiți un `Număr de telefon` pentru compania dumneavoastră."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please set the company Tax Accounting Basis in the Accounting Settings."
msgstr ""
"Vă rugăm să setați baza de contabilitate fiscală a companiei în Setările de "
"contabilitate."

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_701
msgid "Property tax"
msgstr "Impozit pe construcţii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_302
msgid "Quarterly - value-added tax (VAT)"
msgstr "Taxa pe valoarea adaugata- trimestrial"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_455
msgid "Quarterly contribution for Cost-volume / cost-volume-outcome contracts"
msgstr ""
"Contribuţie trimestrială datorată pentru  contractele cost-volum/cost-volum-"
"rezultat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_940
msgid "Quarterly contribution for financing health services expenses"
msgstr ""
"Contribuţia trimestriala pentru finanţarea unor cheltuieli în domeniul "
"sănătăţii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_450
msgid ""
"Quarterly contribution for financing medicine expenses covered from the "
"National Fund of Health Insurance and from the Ministry of Health's budget"
msgstr ""
"Contribuţie trimestriala datorata pentru medicamentele suportate din Fondul "
"naţional unic de asigurări sociale de sănătate şi din bugetul Ministerului "
"Sănătăţii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_946
msgid ""
"Quarterly contribution to medications covered from the National Fund of "
"Health Insurance and from the Ministry of Health's budget, outstanding at 1 "
"October 2011"
msgstr ""
"Contribuţie trimestriala datorata pentru medicamentele suportate din Fondul "
"naţional unic de asigurări sociale de sănătate şi din bugetul Ministerului "
"Sănătăţii, aflate în sold, la data de 1 octombrie 2011 şi neachitate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_986
msgid "Receivables compensation CAS (contributions to the social health fund)"
msgstr "Venituri din compensarea creantelor din despagubiri CAS"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_987
msgid ""
"Receivables compensation CASS (contributions to the social welfare found)"
msgstr "Venituri din compensarea creantelor din despagubiri CASS"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_985
msgid "Receivables compensation SB"
msgstr "Venituri din compensarea creantelor din despagubiri BS"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_988
msgid "Receivables compensation SOMAJ (unemployment contributions)"
msgstr "Venituri din compensarea creantelor din despagubiri SOMAJ"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_972
msgid "Receivables recovered by joint liability"
msgstr "Creante recuperate in urma atragerii raspunderii solidare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_752
msgid "Refund for advanced state judicial expenses"
msgstr "Venituri din recuperarea cheltuielilor judiciare avansate de stat"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_982
msgid "Refund of budgetary financing of previous years - BCAS"
msgstr "Restituiri fonduri din finantarea bugetara a anilor precedenti - BCAS"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_983
msgid "Refund of budgetary financing of previous years - BSAN"
msgstr "Restituiri fonduri din finantarea bugetara a anilor precedenti - BSAN"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_984
msgid "Refund of budgetary financing of previous years - BSOM"
msgstr "Restituiri fonduri din finantarea bugetara a anilor precedenti - BSOM"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_981
msgid "Refund of budgetary financing of previous years - SB"
msgstr "Restituiri fonduri din finantarea bugetara a anilor precedenti - BS"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_916
msgid "Refunds from budget financing of previous years"
msgstr "Restituiri de fonduri din finantarea bugetara a anilor precedenti"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_610
msgid "Regularizations"
msgstr "Regularizari"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_390
msgid "Reimbursement of the VAT"
msgstr "Taxa pe valoarea adaugata rambursare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_956
msgid "Revenue from the recovery of the State Aids"
msgstr "Venituri din ajutoare de stat recuperate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_910
msgid ""
"Revenues from fines and other penalties imposed by other specialized "
"institutions"
msgstr ""
"Venituri din amenzi si alte sanctiuni aplicate de catre alte institutii de "
"specialitate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_912
msgid ""
"Revenues from fines and other penalties imposed by the Directorate General "
"for Tax Fraud"
msgstr ""
"Venituri din amenzi si alte sanctiuni aplicate de Directia Generala "
"Antifrauda Fiscala"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_code
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_code
msgid "Romanian SAF-T Tax Code"
msgstr "Cod Fiscal SAF-T Roman"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_l10n_ro_saft_tax_type
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax__l10n_ro_saft_tax_type_id
#: model:ir.model.fields,field_description:l10n_ro_saft.field_account_tax_template__l10n_ro_saft_tax_type_id
msgid "Romanian SAF-T Tax Type"
msgstr "Tipul fiscal SAF-T românesc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_110
msgid "Romanian fund"
msgstr "Fondul Romania"

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "Romanian localization"
msgstr "Localizare romaneasca"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_758
msgid ""
"Royalties from concessions contracts, lease and other efficient exploitation"
" of agricultural land contracts"
msgstr ""
"Redevenţe rezultate din contractele de concesiune, arendă şi alte contracte "
"de exploatare eficientă a terenurilor cu destinaţie agricolă"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "SAF-T"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_735
msgid ""
"Share applied to the monthly wages fund, including monthly gainings achieved"
" by individuals (art. 53/OUG no. 102/1999)"
msgstr ""
"Cota aplicata fondului de salarii realizat lunar, inclusiv asupra "
"castigurilor realizate lunar de colaboratori persoane fizice (art.53/OUG "
"nr.102/1999)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_730
msgid ""
"Share of the income realized by the Romanian legal persons provided at art. "
"2, letter a) from the OG no. 47/1998 regarding the establishment and use of "
"the Special Fund of civil aviation"
msgstr ""
"Cota din veniturile realizate de persoanele juridice romane care presteaza "
"activitatile prevazute la art.2 lit.a) din OG nr.47/1998 privind "
"constituirea si utilizarea Fondului special al aviatiei civile"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_991
msgid "Single account for non-residents"
msgstr "Cont unic nerezidenti"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_474
msgid ""
"Social health insurance contribution due by individuals who earn income "
"based on sports activity contracts"
msgstr ""
"Contribuția de asigurări sociale de sănătate datorată de persoanele fizice "
"care realizează venituri în baza contractelor de activitate sportivă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_476
msgid ""
"Social health insurance contributions related to the Unique Declaration"
msgstr ""
"Contribuții de asigurări sociale de sănătate aferente declarației unice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_410
msgid "Social insurance contribution"
msgstr "Contributia de asigurari sociale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_454
msgid ""
"Social insurance contribution by people with revenues from independent "
"activities, agricultural activities and associations without legal persons"
msgstr ""
"Contributii de asigurari sociale de stat datorate de persoane care "
"realizeaza venituri din activitati independente, activitati agricole si "
"asocieri fara personalitate juridica"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_411
msgid "Social insurance contribution by the employer"
msgstr "Contributia de asigurari sociale datorata de angajator"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_412
msgid "Social insurance contribution deducted from the insured persons"
msgstr "Contributia individuala de asigurari sociale retinuta de la asigurati"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_481
msgid ""
"Social insurance contribution due in case of speccific working conditions"
msgstr ""
"Contribuția de asigurări sociale datorată în cazul condițiilor deosebite de "
"muncă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_482
msgid ""
"Social insurance contribution due in case of special working conditions"
msgstr ""
"Contribuția de asigurări sociale datorată în cazul condițiilor speciale de "
"muncă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_486
msgid ""
"Social insurance contribution for agriculture (art.60 pct. 7 from Fiscal "
"Code)"
msgstr "CAS agricultură (art.60 pct. 7 din CF)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_413
msgid ""
"Social insurance contribution for people whose duties are paid from the "
"Unemployment Insurance Fund"
msgstr ""
"Contributia de asigurari sociale datorata pentru persoane pentru care plata "
"drepturilor se suporta din bugetul asigurarilor pentru somaj"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_416
msgid ""
"Social insurance contribution for work accidents and professional illnesses "
"caused by the employer"
msgstr ""
"Contributia de asigurare pentru accidente de munca si boli profesionale "
"datorata de angajator"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_418
msgid ""
"Social insurance contribution for work accidents and professional illnesses "
"for unemployed people"
msgstr ""
"Contributia de asigurare pt.accidente de munca si boli profesionale pt. "
"someri"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_485
msgid ""
"Social insurance contributions due by insured persons on the basis of an "
"insurance contract"
msgstr ""
"Contribuții de asigurări sociale datorate de persoane asigurate pe baza de "
"contract de asigurare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_475
msgid "Social insurance contributions related to the Unique Declaration"
msgstr "Contribuții de asigurări sociale aferente declarației unice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_993
msgid ""
"Social insurance income budget collected in the single account, ongoing "
"distribution"
msgstr ""
"Venituri ale bugetelor asigurarilor sociale de stat incasate in contul unic,"
" in curs de distribuire"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_740
msgid ""
"Social stamp tax on the value of imported new vehicles with engine capacity "
"of more than 2000 cm3"
msgstr ""
"Taxa de timbru social asupra valorii autovehiculelor noi din import, cu "
"capacitate cilindrica de minimum 2000cm3"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_709
msgid "Solidarity contribution"
msgstr "Contributia de solidaritate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_538
msgid "Special fee for video lottery"
msgstr "Taxa specială pentru videoloterie"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_779
msgid "Special taxes for cars and vehicles at first registration in Romania"
msgstr ""
"Taxe speciale pentru autoturisme si autovehicule la prima inmatriculare in "
"Romania"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_130
msgid "Specific Corporate Income Tax"
msgstr "Impozit specific"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_992
msgid ""
"State budget income collected in the single account, ongoing distribution"
msgstr ""
"Venituri ale bugetului de stat incasate in contul unic, in curs de "
"distribuire"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_tax
msgid "Tax"
msgstr "Taxă"

#. module: l10n_ro_saft
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "Tax Accounting Basis"
msgstr "Baza contabilă fiscală"

#. module: l10n_ro_saft
#: model:ir.model.fields,field_description:l10n_ro_saft.field_res_company__l10n_ro_saft_tax_accounting_basis
#: model:ir.model.fields,field_description:l10n_ro_saft.field_res_config_settings__l10n_ro_saft_tax_accounting_basis
msgid "Tax Accounting Basis (RO)"
msgstr "Baza contabilă fiscală (RO)"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_725
msgid ""
"Tax and tariffs on the issuing of licenses and operation authorizations"
msgstr ""
"Taxe si tarife pentru eliberarea de licente si autorizatii de functionare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_711
msgid ""
"Tax on additional income obtained as a result of the natural gas prices "
"deregulation"
msgstr ""
"Impozit asupra veniturilor suplimentare obtinute ca urmare a dereglementarii"
" preturilor din sectorul gazelor naturale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_161
msgid "Tax on additional income obtained by electricity producer"
msgstr ""
"Impozit pe venitul suplimentar realizat de producătorii de energie electrică"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_604
msgid "Tax on dividends for natural persons"
msgstr "Impozit pe veniturile din dividende distribuite persoanelor fizice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_763
msgid "Tax on financial aassets"
msgstr "Taxa pe activele financiare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_500
msgid "Tax on gambling"
msgstr "Taxa jocuri de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_501
msgid ""
"Tax on gambling in advance or monthly during gambling license availability"
msgstr ""
"Taxa pe jocuri de noroc datorata anticipat sau lunar pe perioada de "
"valabilitate a licentei de exploatare a jocurilor de noroc"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_615
msgid ""
"Tax on income from accounting, technical, judicial and extrajudicial "
"expertise"
msgstr ""
"Impozit pe veniturile din activitatea de expertiza contabila si tehnica "
"judiciara si extrajudiciara"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_616
msgid ""
"Tax on income from activities performed under contracts / civil agreements "
"concluded according to the Civil Code and agent contracts"
msgstr ""
"Impozit pe veniturile din activităţi desfăşurate în baza contractelor/ "
"convenţiilor civile încheiate potrivit Codului civil, precum şi a "
"contractelor de agent"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_613
msgid "Tax on income from agricultural activities"
msgstr "Impozit aferent veniturilor din activitati agricole"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_618
msgid ""
"Tax on income from commercial activities - tax on income based on "
"conventions / civil contracts concluded according to the Civil Code"
msgstr ""
"Impozit pe veniturile din activitati comerciale - impozit pe veniturile "
"realizate in baza unor conventii/ contracte civile incheiate in conditiile "
"codului civil"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_634
msgid "Tax on income from commissions obtained in Romania by non-residents"
msgstr ""
"Impozit pe veniturile din comisioane obţinute din România de persoane "
"nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_150
msgid "Tax on income from dividends for legal persons"
msgstr "Impozit pe veniturile din dividende distribuite persoanelor juridice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_631
msgid "Tax on income from dividends obtained in Romania by non-residents"
msgstr ""
"Impozit pe veniturile din dividende obţinute din România de persoane "
"nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_712
msgid ""
"Tax on income from exploitation activities of natural resources, others than"
" natural gas"
msgstr ""
"Impozit pe veniturile din activitatile de exploatare a resurselor naturale, "
"altele decat gazele naturale"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_632
msgid "Tax on income from interests obtained in Romania by non-residents"
msgstr ""
"Impozit pe veniturile din dobânzi obţinute din România de persoane "
"nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_122
msgid ""
"Tax on income from micro-enterprises created by the association of a natural"
" person with a legal person, not generating a legal person"
msgstr ""
"Impozit pe veniturile persoanelor fizice dintr-o asociere cu o persoană "
"juridică microîntreprindere, care nu generează o persoană juridică"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_690
msgid "Tax on income from other sources"
msgstr "Impozit pe veniturile din alte surse"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_633
msgid "Tax on income from royalties obtained in Romania by non-residents"
msgstr ""
"Impozit pe veniturile din redevenţe obţinute din România de persoane "
"nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_617
msgid ""
"Tax on income from sale operations - buying foreign currency, other than "
"those with financial instruments traded on authorized markets"
msgstr ""
"Impozit pe castingul din operatiuni de vanzare - cumparare de valute la "
"termen, altele decat cele cu instrumente financiare tranzactionate pe piete "
"autorizate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_636
msgid ""
"Tax on income from services provided in Romania and outside Romania by non-"
"residents"
msgstr ""
"Impozit pe veniturile din servicii prestate în România şi în afara României "
"de persoane nerezident"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_162
msgid ""
"Tax on income from the sale of agricultural land located outside the village"
msgstr ""
"Impozit pe veniturile din instrainarea prin vanzare a terenurilor agricole "
"situate in extravilan"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_614
msgid ""
"Tax on income from the sale of assets in a consignment and from activities "
"carried out under an agent contract, commission or commercial mandate"
msgstr ""
"Impozit pe veniturile obtinute din valorificarea de bunuri in regim de "
"consignatie si din activitatile desfasurate pe baza unui contract de agent, "
"comision sau mandat comercial"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_641
msgid ""
"Tax on income from the transfer of fiduciary patrimony from the fiduciary to"
" non-resident beneficiary"
msgstr ""
"Impozit pe veniturile realizate din transferul masei patrimoniale fiduciare "
"de la fiduciar la beneficiarul nerezident"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_642
msgid ""
"Tax on income from the transfer of securities and operations with derivative"
" financial instruments"
msgstr ""
"Impozit pe veniturile din transferul titlurilor de valoare si din operatiuni"
" cu instrumente financiare derivate"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_140
msgid ""
"Tax on income generated in Romania by nonresident - legal persons, collected"
" until 31.01.2014"
msgstr ""
"Impozit pe veniturile obtinute din Romania de nerezidenti - persoane "
"juridice, incasate pana la 31.01.2014"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_639
msgid ""
"Tax on income obtained by liquidation of a legal person by non-residents"
msgstr ""
"Impozit pe venituri din lichidarea unei persoane juridice române realizate "
"de persoane nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_637
msgid ""
"Tax on income obtained by non-residents individuals from financial award "
"granted at contests held in Romania"
msgstr ""
"Impozit pe veniturile obţinute de persoane fizice nerezidente din premii "
"acordate la concursuri organizate în România"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_638
msgid "Tax on income obtained from gambling in Romania by non-residents"
msgstr ""
"Impozit pe veniturile obţinute la jocuri de noroc practicate în România de "
"persoane fizice nerezidente"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_635
msgid ""
"Tax on income obtained in Romania by non-residents from sports and "
"entertainment activities"
msgstr ""
"Impozit pe veniturile obţinute din România de persoane nerezidente din "
"activităţi sportive şi de divertisment"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_155
msgid "Tax on income of attorneys and public notaries"
msgstr "Impozitul pe onorariul avocatilor si notarilor publici"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_640
msgid ""
"Tax on income representing remunerations received by non-resident legal "
"persons who act as administrator, founder or member of a board of directors "
"of a Romanian legal person"
msgstr ""
"Impozit pe veniturile reprezentând remuneraţii primite de persoane juridice "
"nerezidente care au calitatea de administrator, fondator sau membru al "
"consiliului de administraţie al unei persoane juridice române"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_624
msgid ""
"Tax on incomes obtained by individuals from an association with a taxpayer "
"legal entity, according to Law no. 170/2016"
msgstr ""
"Impozit pe veniturile obținute de persoanele fizice dintr-o asociere cu o "
"persoană juridică contribuabil, potrivit Legii nr.170/2016"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_612
msgid ""
"Tax on incomes obtained in Romania by non-residents - individuals, collected"
" up to 31/01/2014"
msgstr ""
"Impozit pe veniturile obtinute din Romania de nerezidenti - persoane fizice,"
" incasate pana la 31.01.2014"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_625
msgid ""
"Tax on incomes realized by natural persons from independent activity "
"realized on the basis of sports activity contracts."
msgstr ""
"Impozit pe veniturile realizate de persoanele fizice din activități "
"independente realizate în baza contractelor de activitate sportivă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_713
msgid "Tax on natural monopoly of the energy and natural gas sectors"
msgstr ""
"Impozit pe monopolul natural din sectorul energiei electrice si al gazului "
"natural"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_720
msgid "Tax on nocive activities to human health"
msgstr "Taxa asupra activitatilor daunatoare sanatatii"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_106
msgid "Tax on profit from natural persons in association"
msgstr "Impozit pe profit din asociere datorat de persoane fizice"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_930
msgid ""
"Tax on profits earned from illicit commercial activities or from not "
"respecting the laws regarding consumer protection"
msgstr ""
"Impozit pe profitul obtinut din activitati comerciale ilicite sau din "
"nerespectarea legii privind protectia consumatorilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_603
msgid "Tax on property and granting property"
msgstr "Impozit pe veniturile din cedarea folosintei bunurilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_750
msgid "Tax on prospecting, exploration, resource exploitation"
msgstr ""
"Taxa pe activitatea de prospectiune, explorare, exploatare a resurselor "
"minerale"

#. module: l10n_ro_saft
#: model:ir.model,name:l10n_ro_saft.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Sabloane pentru Taxe"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The CUI number for your company (under `Company Registry` in the Company "
"settings) is incorrect."
msgstr "Numărul CUI pentru compania dvs. (sub „Registrul companiei” în setările companiei) este incorect."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT number for your company has failed the VIES check."
msgstr "Numărul de TVA al companiei dvs. a eșuat verificarea VIES."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT number for your company is incorrect."
msgstr "Numărul de TVA al companiei dvs. este incorect."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The VAT numbers for the following partners failed the VIES check:"
msgstr ""
"Numerele de TVA pentru următorii parteneri nu au trecut la verificarea VIES:"

#. module: l10n_ro_saft
#: model:ir.model.fields,help:l10n_ro_saft.field_res_company__l10n_ro_saft_tax_accounting_basis
#: model:ir.model.fields,help:l10n_ro_saft.field_res_config_settings__l10n_ro_saft_tax_accounting_basis
#: model_terms:ir.ui.view,arch_db:l10n_ro_saft.res_config_settings_form_inherit_l10n_ro_saft
msgid "The accounting regulations and Chart of Accounts used by this company"
msgstr ""
"Reglementările contabile și Planul de conturi utilizate de această companie"

#. module: l10n_ro_saft
#: model:ir.model.constraint,message:l10n_ro_saft.constraint_l10n_ro_saft_tax_type_code_unique
msgid "The code of the tax type must be unique !"
msgstr ""

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_472
msgid ""
"The contribution for the social health insurance due by the persons who "
"realize incomes obtained from the association with a legal person, taxpayer,"
" according to Law no. 170/2016 regarding the specific tax"
msgstr ""
"Contribuția de asigurări sociale de sănătate datorată de persoanele care "
"realizează venituri obținute din asocierea cu o persoană juridică, "
"contribuabil, potrivit Legii nr. 170/2016 privind impozitul specific"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_423
msgid "The employer's contribution to the Guarantee Fund for salary payment"
msgstr ""
"Contributia angajatorilor la Fondul de garantare pentru plata creantelor "
"salariale"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "The following products have no `Internal Reference`:"
msgstr "Următoarele produse nu au `Referință internă`:"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The following taxes are missing the \"Romanian SAF-T Tax Type\" and/or "
"\"Romanian SAF-T Tax Code\" field(s):"
msgstr ""
"Următoarele taxe lipsesc câmpurile „Tipul fiscal SAF-T românesc” și/sau „Cod"
" fiscal SAF-T românesc”:"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The follwing products have duplicated `Internal Reference`, please make them"
" unique:"
msgstr ""
"Următoarele produse au duplicat „Referință internă”, vă rugăm să le faceți "
"unice:"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_471
msgid ""
"The individual contribution for the social health insurance due by the "
"persons with incomes obtained from an association with a taxpayer legal "
"person, according to title II of the Fiscal Code"
msgstr ""
"Contribuția individuală de asigurări sociale de sănătate datorată de "
"persoanele care realizează venituri obținute dintr-o asociere cu o persoană "
"juridică contribuabil, potrivit titlului II din Codul fiscal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_484
msgid ""
"The insurative contribution for labour, due by employers who carry out the "
"activity in the field of constructions according to art. 60 point 5 of Law "
"no. 227/2015 on the Fiscal Code"
msgstr ""
"Contribuția asiguratorie pentru muncă, datorată de angajatori și care "
"desfăsoară activitatea în domeniul construcțiilor conform art. 60 pct. 5 din"
" Legea nr.227/2015 privind Codul fiscal"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The intrastat code isn't set on the follwing products:"
msgstr ""
"Codul intrastat nu este setat pentru următoarele produse:"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_473
msgid ""
"The social insurance contribution due by the natural persons who realize "
"incomes based on the sports activity contracts"
msgstr ""
"Contribuția de asigurări sociale datorată de persoanele fizice care "
"realizează venituri în baza contractelor de activitate sportivă"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_483
msgid ""
"The social insurance contribution, due by the natural persons who carry out "
"their activity in the field of constructions according to art. 60 point 5 of"
" Law no. 227/2015 on the Fiscal Code"
msgstr ""
"Contribuția de asigurări sociale, datorată de către persoanele fizice și "
"care își desfăsoară activitatea în domeniul construcțiilor conform art. 60 "
"pct. 5 din Legea nr. 227/2015 privind Codul fiscal"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partner addresses are missing the city:"
msgstr "Aceste adrese ale partenerilor lipsesc orașul:"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partner addresses are missing the country:"
msgstr "Aceste adrese ale partenerilor lipsesc țara:"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "These partners have a VAT prefix that differs from their country:"
msgstr "Acești parteneri au un prefix de TVA care diferă de țara lor:"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"These partners have missing or invalid CUI numbers in `Company Registry`. "
"Example of a valid CUI: ********"
msgstr ""
"Acești parteneri au numere CUI lipsă sau invalide în `Registrul companiei`. "
"Exemplu de CUI valid: ********"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"These partners have missing or invalid VAT numbers. Example of a valid VAT: "
"RO********"
msgstr ""
"Acești parteneri au numere de TVA lipsă sau nevalide. Exemplu de TVA "
"valabil: RO********"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_000
msgid ""
"Transactions not relevant to be reported for taxes and import operations"
msgstr ""
"Tranzacții care nu sunt relevante pentru a fi raportate pentru taxe și "
"operațiuni de import"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_609
msgid "Transfer of ownership of securities income tax"
msgstr ""
"Impozit pe castigul din transferul dreptului de proprietate asupra "
"titlurilor de valoare"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_620
msgid "Transfer of real estates from the personal property income tax"
msgstr ""
"Impozit pe veniturile din transferal proprietarilor imobiliare din "
"patrimonial personal"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_307
msgid "VAT to be paid as a result of adjustments"
msgstr "TVA de plata la bugetul de stat, rezultata ca urmare a ajustarilor"

#. module: l10n_ro_saft
#: model:l10n_ro_saft.tax.type,description:l10n_ro_saft.l10n_ro_saft_tax_type_300
msgid "Value-added tax (VAT)"
msgstr "Taxa pe valoarea adaugata"

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"While preparing the data for the SAF-T export, we noticed the following "
"missing or incorrect data."
msgstr ""
"În timpul pregătirii datelor pentru exportul SAF-T, am observat următoarele "
"date lipsă sau incorecte."

#. module: l10n_ro_saft
#. odoo-python
#: code:addons/l10n_ro_saft/models/account_general_ledger.py:0
#, python-format
msgid "XML"
msgstr ""
