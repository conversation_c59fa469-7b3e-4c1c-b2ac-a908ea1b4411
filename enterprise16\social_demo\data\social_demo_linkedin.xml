<?xml version='1.0' encoding='utf-8'?>
<odoo>
<data noupdate="1">
    <record id="social_stream_linkedin_page" model="social.stream">
        <field name="name">LinkedIn Posts: My Company</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="stream_type_id" ref="social_linkedin.stream_type_linkedin_company_post" />
        <field name="media_id" ref="social_linkedin.social_media_linkedin" />
        <field name="account_id" ref="social_account_linkedin" />
    </record>

    <record id="social_live_post_linkedin_1" model="social.live.post">
        <field name="state">posted</field>
        <field name="linkedin_post_id">1</field>
        <field name="post_id" ref="social_post_global" />
        <field name="account_id" ref="social_account_linkedin" />
    </record>

    <record id="social_live_post_linkedin_2" model="social.live.post">
        <field name="state">posted</field>
        <field name="linkedin_post_id">2</field>
        <field name="post_id" ref="social_post_global_2" />
        <field name="account_id" ref="social_account_linkedin" />
    </record>

    <record id="social_live_post_linkedin_3" model="social.live.post">
        <field name="state">posted</field>
        <field name="linkedin_post_id">3</field>
        <field name="post_id" ref="social_post_global_3" />
        <field name="account_id" ref="social_account_linkedin" />
    </record>

    <record id="social_stream_post_linkedin_1" model="social.stream.post">
        <field name="stream_id" ref="social_stream_linkedin_page" />
        <field name="linkedin_post_urn">1</field>
        <field name="linkedin_comments_count">94</field>
        <field name="linkedin_likes_count">4584</field>
        <field name="author_name">My Company Page</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Our new product has been released 🎉 Check it out!</field>
        <field name="published_date" eval="time.strftime('%Y-01-05 08:00:00')"/>
        <field name="linkedin_author_urn">XX:XX:XX</field>
        <field name="linkedin_post_urn">XX:XX:XX</field>
    </record>

    <record id="social_stream_post_linkedin_2" model="social.stream.post">
        <field name="stream_id" ref="social_stream_linkedin_page" />
        <field name="linkedin_post_urn">2</field>
        <field name="linkedin_comments_count">87</field>
        <field name="linkedin_likes_count">7489</field>
        <field name="author_name">My Company Page</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Our company wishes a happy new year to everyone on LinkedIn!</field>
        <field name="published_date" eval="time.strftime('%Y-01-01 00:01:00')"/>
        <field name="linkedin_author_urn">XX:XX:XX</field>
        <field name="linkedin_post_urn">XX:XX:XX</field>
    </record>

    <record id="social_stream_post_linkedin_3" model="social.stream.post">
        <field name="stream_id" ref="social_stream_linkedin_page" />
        <field name="linkedin_comments_count">77</field>
        <field name="linkedin_likes_count">1325</field>
        <field name="author_name">My Company Page</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Get 20% out of your purchases on https://mycompany.com/shop
Better run to our website while it lasts 🏃</field>
        <field name="published_date" eval="time.strftime('%Y-01-07 08:00:00')"/>
        <field name="linkedin_author_urn">XX:XX:XX</field>
        <field name="linkedin_post_urn">XX:XX:XX</field>
    </record>

    <record id="social_stream_post_linkedin_4" model="social.stream.post">
        <field name="stream_id" ref="social_stream_linkedin_page" />
        <field name="linkedin_comments_count">84</field>
        <field name="linkedin_likes_count">1454</field>
        <field name="author_name">My Company Page</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.users/%s/image_128' % ref('base.user_admin')" />
        <field name="message">To celebrate the new year, we have a gift for you: Learn how to build your own chair!

https://www.youtube.com/watch?v=kmt-oVAB6hU</field>
        <field name="published_date" eval="time.strftime('%Y-01-10 08:00:00')"/>
        <field name="linkedin_author_urn">XX:XX:XX</field>
        <field name="linkedin_post_urn">XX:XX:XX</field>
    </record>

    <record id="social_stream_post_linkedin_image_1" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4')"></field>
        <field name="stream_post_id" ref="social_stream_post_linkedin_3" />
    </record>

    <record id="social_stream_post_linkedin_image_2" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4b')"></field>
        <field name="stream_post_id" ref="social_stream_post_linkedin_3" />
    </record>

    <record id="social_stream_post_linkedin_image_3" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4c')"></field>
        <field name="stream_post_id" ref="social_stream_post_linkedin_3" />
    </record>

    <record id="social_stream_post_linkedin_image_4" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4d')"></field>
        <field name="stream_post_id" ref="social_stream_post_linkedin_3" />
    </record>
</data>
</odoo>
