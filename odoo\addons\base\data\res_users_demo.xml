<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="partner_demo" model="res.partner">
            <field name="name"><PERSON></field>
            <field name="company_id" ref="main_company"/>
            <field name="company_name">YourCompany</field>
            <field name="street">3575  Buena Vista Avenue</field>
            <field name="city">Eugene</field>
            <field name="state_id"  model="res.country.state" search="[('code','ilike','OR')]"/>
            <field name="zip">97401</field>
            <field name="country_id" ref="us"/>
            <field name="tz">Europe/Brussels</field>
            <field name="email"><EMAIL></field>
            <field name="phone">(*************</field>
        </record>

        <record id="main_partner" model="res.partner">
            <field name="name">YourCompany</field>
            <field name="company_name">YourCompany</field>
            <field name="street">250 Executive Park Blvd, Suite 3400</field>
            <field name="city">San Francisco</field>
            <field name="zip">94134</field>
            <field name='country_id' ref='base.us'/>
            <field name='state_id' ref='state_us_5'/>
            <field name="phone">******-555-5556</field>
            <field name="email"><EMAIL></field>
            <field name="website">www.example.com</field>
        </record>

        <record id="user_demo" model="res.users">
            <field name="partner_id" ref="base.partner_demo"/>
            <field name="login">demo</field>
            <field name="password">demo</field>
            <field name="signature" type="html"><span>-- <br/>+Mr Demo</span></field>
            <field name="company_id" ref="main_company"/>
            <field name="groups_id" eval="[Command.set([ref('base.group_user'), ref('base.group_partner_manager'), ref('base.group_allow_export')])]"/>
            <field name="image_1920" type="base64" file="base/static/img/user_demo-image.jpg"/>
        </record>

        <record model="res.partner" id="base.partner_root">
            <field name="tz">Europe/Brussels</field>
        </record>

        <record model="res.partner" id="base.partner_admin">
            <field name="name">Mitchell Admin</field>
            <field name="company_name">YourCompany</field>
            <field name="street">215 Vine St</field>
            <field name="city">Scranton</field>
            <field name="zip">18503</field>
            <field name='country_id' ref='base.us'/>
            <field name='state_id' ref='state_us_39'/>
            <field name="phone">******-555-5555</field>
            <field name="email"><EMAIL></field>
            <field name="tz">Europe/Brussels</field>
            <field name="image_1920" type="base64" file="base/static/img/partner_root-image.jpg"/>
        </record>

        <record id="base.user_admin" model="res.users">
            <field name="signature" type="html"><span>-- <br/>Mitchell Admin</span></field>
        </record>

        <!-- Portal : partner and user -->
        <record id="partner_demo_portal" model="res.partner">
            <field name="name">Joel Willis</field>
            <field name="email"><EMAIL></field>
            <field name="street">858 Lynn Street</field>
            <field name="city">Bayonne</field>
            <field model="res.country.state" name="state_id" search="[('code','ilike','NJ')]"/>
            <field name="zip">07002</field>
            <field name="country_id" ref="base.us"/>
            <field name="company_name">YourCompany</field>
            <field name="image_1920" type="base64" file="base/static/img/partner_demo_portal.jpg"/>
            <field name="phone">(*************</field>
        </record>
        <record id="demo_user0" model="res.users" context="{'no_reset_password': True}">
            <field name="partner_id" ref="partner_demo_portal"/>
            <field name="login">portal</field>
            <field name="password">portal</field>
            <field name="signature"><![CDATA[<span>-- <br/>Mr Demo Portal</span>]]></field>
            <field name="groups_id" eval="[Command.clear()]"/><!-- Avoid auto-including this user in any default group -->
        </record>

        <record id="base.group_portal" model="res.groups"><!-- Add the demo user to the portal (and therefore to the portal member group) -->
            <field name="users" eval="[Command.link(ref('demo_user0'))]"/>
        </record>
    </data>
</odoo>
