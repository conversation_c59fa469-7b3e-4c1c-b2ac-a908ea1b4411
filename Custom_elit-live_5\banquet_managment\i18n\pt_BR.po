# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* banquet_managment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 08:39+0000\n"
"PO-Revision-Date: 2020-08-12 08:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "(Compute)"
msgstr "(Calcular)"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "(Update History)"
msgstr "(Histórico de atualização)"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__address
msgid "Address"
msgstr "Endereço"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__adult
msgid "Adult Persons"
msgstr "Pessoas adultas"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__agent_id
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__agent_id
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__via__agent
#: model:ir.model.fields.selection,name:banquet_managment.selection__crm_lead__via__agent
msgid "Agent"
msgstr "Agente"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_all
msgid "All Banquet Reservation"
msgstr "Todas as reservas do banquete"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_cancel
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_cancel
msgid "All Cancelled Banquet Reservation"
msgstr "Todas as reservas do banquete Cancelar"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_cancel
msgid "All Cancelled Reservation"
msgstr "Todas as reservas do Cancela"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_confirm
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_confirm
msgid "All Confirm Banquet Reservation"
msgstr "Todas as reservas do banquete confirmado"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_confirm
msgid "All Confirm Reservation"
msgstr "Todas as reservas do confirmado"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_done
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_done
msgid "All Done Banquet Reservation"
msgstr "Todas as reservas do banquete Concluídos"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_draft
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_draft
msgid "All Draft Banquet Reservation"
msgstr "Todos os projectos de reserva do banquete "

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_all
msgid "All Reservation"
msgstr "Todos Reservation"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__approve
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__approve
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Approved"
msgstr "Aprovado"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__banquet_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__banquet_id
msgid "Banquet"
msgstr "Banquet"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__banq_bool
#: model:ir.ui.menu,name:banquet_managment.main_menu_banquet_booking
msgid "Banquet Booking"
msgstr "Reservas do banquete"

#. module: banquet_managment
#: model:ir.ui.menu,name:banquet_managment.menu_banquet_config
msgid "Banquet Configuration"
msgstr "Configuração do banquete"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Hall"
msgstr "Salão de banquetes"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.crm_case_form_view_leads_inherit_tour
#: model_terms:ir.ui.view,arch_db:banquet_managment.crm_case_form_view_oppor_inherit_tour
msgid "Banquet History"
msgstr "História do banquete"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__banquet_id
msgid "Banquet Id"
msgstr "Id do banquete"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Information"
msgstr "Informações de Banquete "

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_quotation_view
#: model:ir.model,name:banquet_managment.model_banquet_quotation
#: model:ir.ui.menu,name:banquet_managment.menu_banquet_quotation_form
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_tree_view
msgid "Banquet Quotation"
msgstr "Cotação de banquete"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__banquets_ids
msgid "Banquet Quotation History"
msgstr "Histórico de cotação de banquete"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__name
msgid "Banquet Quotation No."
msgstr "N º de cotação do banquete"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.open_banquet_reservation_form_tree
msgid "Banquet Reservation"
msgstr "Reserva de banquete"

#. module: banquet_managment
#: model:ir.ui.menu,name:banquet_managment.menu_theme_plan_tree
msgid "Banquet Theme"
msgstr "Tema do banquete"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Type"
msgstr "Tipo de banquete"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Basic Info"
msgstr "Informação básica"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__board_toread
msgid "Board to Read"
msgstr "Placa para leitura"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Booking Details"
msgstr "Detalhes da Reserva"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__booking_id
msgid "Booking Ref"
msgstr "Ref. de Reserva"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__banquet_id
msgid "Booking Ref."
msgstr "Ref. de Reserva"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Cancel Reservation"
msgstr "Cancelar Reservation"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__category_id
msgid "Category"
msgstr "Categoria"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__child
msgid "Child"
msgstr "Criança"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__code
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__code
msgid "Code"
msgstr "Código"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__agent_comm
msgid "Commision"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__company_id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__company_id
msgid "Company"
msgstr "Companhia"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__confirm
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__confirm
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Confirm"
msgstr "confirmar"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__contact_name
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__contact_name
msgid "Contact Name"
msgstr "Nome de contato"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__cost_price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__cost_price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__cost_price_unit
msgid "Cost Price"
msgstr "Preço de custo"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__cost_price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__cost_price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__cost_price_subtotal
msgid "Cost Subtotal"
msgstr "Custo Subtotal"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Create Banquet Booking"
msgstr "Criar reservas do banquete"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Create Folio"
msgstr "Criar Folio"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Create Journal Entry"
msgstr "Criar a entrada de diário"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__create_uid
msgid "Created by"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__create_date
msgid "Created on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__current_date
msgid "Creation Date"
msgstr "Data de criação"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__currency_id
msgid "Currency"
msgstr "moeda"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_recv_acc
msgid "Deposit Account"
msgstr "Conta de Depósito"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_cost
msgid "Deposit Cost"
msgstr "Depósito custo"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.act_deposit_journal_entry
msgid "Deposit Journal Entry"
msgstr "Depósito de entrada de diário"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_deposit_policy_tree
#: model:ir.model,name:banquet_managment.model_deposit_payment_policy
#: model:ir.ui.menu,name:banquet_managment.menu_deposit_policy
msgid "Deposit Payment Policy"
msgstr "Política de pagamento de depósito"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__deposit_policy__percentage
#: model:ir.model.fields.selection,name:banquet_managment.selection__hotel_reservation__deposit_policy__percentage
msgid "Deposit Percentage"
msgstr "Percentagem de Depósito"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__deposit_policy
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_policy
#: model:ir.ui.menu,name:banquet_managment.menu_deposit_policy_tree
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_deposit_payment_policy_tree
msgid "Deposit Policy"
msgstr "Política de Depósito"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_deposit_journal_entry_wizard
msgid "Deposit_journal_entry Detail Wizard"
msgstr "Assistente de detalhes de Deposit_journal_entry"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__name
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__name
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__name
msgid "Description"
msgstr "Descrição"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__via__direct
#: model:ir.model.fields.selection,name:banquet_managment.selection__crm_lead__via__direct
msgid "Direct"
msgstr "Direto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__discount
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__discount
msgid "Discount (%)"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__display_name
msgid "Display Name"
msgstr "Desconto (% )"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__done
msgid "Done"
msgstr "Feito"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__draft
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__draft
msgid "Draft"
msgstr "Projecto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__email_id
msgid "Email Id"
msgstr "ID do e-mail"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__current_date
msgid "Enquiry Date"
msgstr "Data do inquérito"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Event Information"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Food Details"
msgstr "Informações sobre o evento"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__food_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__food_items_id
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__food_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__other_items_ids
msgid "Food Items"
msgstr "Itens alimentares"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_food_items
msgid "Food Items Details"
msgstr "Detalhes de itens de comida"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Food List"
msgstr "Lista de alimentos"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__start_date
msgid "From Date"
msgstr "De data"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__ref_id
#: model_terms:ir.ui.view,arch_db:banquet_managment.itinerary_lead_history_tree_view
msgid "History"
msgstr "História"

#. module: banquet_managment
#: model:res.groups,name:banquet_managment.group_banquet_user
msgid "Hotel Management / Banquet User"
msgstr "Gestão Hoteleira / Usuário do banquete"

#. module: banquet_managment
#: model:res.groups,name:banquet_managment.group_banquet_manager
msgid "Hotel Management/ Banquet Manager"
msgstr "Gestão Hoteleira / Gerente do banquete"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__id
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__id
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__id
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__id
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__id
msgid "ID"
msgstr "ID"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "ID Details"
msgstr "Detalhes do ID"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__invoiced
msgid "Invoiced"
msgstr "Faturado"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_room__deposit_bool
msgid "Is Deposit Applicable"
msgstr "Depósito é aplicável"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Item Deatails"
msgstr "Detalhes do item"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__journal_id
msgid "Journal"
msgstr "entrada"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_food_items____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_other_items____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan____last_update
msgid "Last Modified on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__update_date
msgid "Last Updated Date"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__write_uid
msgid "Last Updated by"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__write_date
msgid "Last Updated on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__lead
msgid "Lead"
msgstr "Vantagem"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__lead_sequence
msgid "Lead Number"
msgstr "Número de Vantagem"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_crm_lead2opportunity_partner
msgid "Lead To Opportunity Partner"
msgstr "Prospecto para Oportunidade de Parceiro"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__min_dep_amount
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__min_amount
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__min_dep_amount
msgid "Minimum Deposit Amount"
msgstr "Valor do depósito mínimo"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__mobile
msgid "Mobile Number"
msgstr "Número de telemóvel"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__name
msgid "Name"
msgstr "Nome"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__deposit_policy__no_deposit
#: model:ir.model.fields.selection,name:banquet_managment.selection__hotel_reservation__deposit_policy__no_deposit
msgid "No Deposit"
msgstr "Sem Depósito"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__number_of_days
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__number_of_days
msgid "Number Of Days"
msgstr "Número de dias"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__number_of_rooms
msgid "Number Of Rooms"
msgstr "Número de quartos"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Order List"
msgstr "Lista de ordem"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Other Item List"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__other_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__other_items_id
msgid "Other Items"
msgstr "Outros Itens"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_other_items
msgid "Other Items Details"
msgstr "Detalhes dos outros itens "

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Other Services"
msgstr "Outros Serviços"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__payment_date
msgid "Payment Date"
msgstr "Data de Pagamento"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__percentage
msgid "Percentage"
msgstr "Porcentagem"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__percentage
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__percentage
msgid "Percentage/Deposit Amount"
msgstr "Porcentagem/Valor do depósito"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Percentage/Deposit Amt"
msgstr "Porcentagem/Valor do depósito"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__name
msgid "Policy Name"
msgstr "Nome da política"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_deposit_payment_policy_policy_name_uniq
msgid "Policy Name must be unique for selected shop !"
msgstr "Nome da política deve ser exclusivo para a loja selecionada!"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__checkout_date
msgid "Prefer End Date"
msgstr "Data final preferido"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__checkin_date
msgid "Prefer start Date"
msgstr "Data de início preferido"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_banquet_quotation_check_in_out_dates
msgid "Prefer start Date Should be lesser than the Prefer End Date!"
msgstr "Data de início preferido deve ser menor do que a data de término preferido!"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pricelist_id
msgid "Pricelist"
msgstr "Lista de Preços"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_id
msgid "Product"
msgstr "Produto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__pur_tax_ids
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__pur_tax_ids
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__pur_tax_ids
msgid "Purchase Taxes"
msgstr "Impostos de compra"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_tax_amt
msgid "Purchase Taxes "
msgstr "Impostos de compra"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_total_amt
msgid "Purchase Total Amount"
msgstr "Valor Total da compra"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_untax_amt
msgid "Purchase Untaxed Amount"
msgstr "Valor da compra não-tributadas"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_uom_qty
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_uom_qty
msgid "Quantity (UoM)"
msgstr "Quantidade (UoM)"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__name
msgid "Quotation No."
msgstr "Cotação n º."

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Refuse"
msgstr "Recusou"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__refused
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__refused
msgid "Refused"
msgstr "Recusou"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_open_hotel_reservation_form_tree
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_open_hotel_reservation_form_tree11
#: model:ir.model,name:banquet_managment.model_hotel_reservation
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_reservation_graph
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_reservation_tree
msgid "Reservation"
msgstr "Reserva"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_hotel_reservation_line
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Reservation Line"
msgstr "Linha de Reserva"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__total_tax
msgid "Reservation Tax"
msgstr "Reserva Impostos"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__room_ids
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Room Details"
msgstr "Detalhes do quarto"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Room Type"
msgstr "Tipo de quarto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_tax_amt
msgid "Sale Taxes "
msgstr "Impostos de venda"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_total_amt
msgid "Sale Total Amount"
msgstr "Valor Total da venda"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_untax_amt
msgid "Sale Untaxed Amount"
msgstr "Valor da venda sem imposto"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_seating_plan_theme_name_uniq
msgid "Seating Name must be unique !"
msgstr "Nome dos lugares deve ser exclusivo!"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_seating_plan_tree
#: model:ir.model,name:banquet_managment.model_seating_plan
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__seating_id
#: model:ir.ui.menu,name:banquet_managment.menu_seating_plan_tree
msgid "Seating Plan"
msgstr "Plano de lugares"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__send_to
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Send To Customer"
msgstr "Enviar para o Cliente"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__send_to
msgid "Sent To Customer"
msgstr "Enviar para o Cliente"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__service_cost
msgid "Service Cost"
msgstr "Custo do Serviço"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_banquet_quotation__number_of_days
msgid ""
"Shall be computed based on check out policy configured for selected shop."
msgstr "Devem ser calculados com base no política de check-out configurado para a loja selecionada."

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__shop_id
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__shop_id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__shop_id
msgid "Shop"
msgstr "Loja"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__purches_bol
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__purches_bol
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__purches_bol
msgid "Show Purchase Tax"
msgstr "Mostrar Imposto de Compras"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_deposit_payment_policy_start_date_uniq
msgid "Start Date must be unique for selected shop !"
msgstr "Data de início deve ser exclusiva para a loja selecionada!"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__state
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__state
msgid "Status"
msgstr "Status"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__sub_total
msgid "Sub Total"
msgstr "Subtotal"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Tax On Product"
msgstr "Imposto sobre o produto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__tax_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__tax_id
msgid "Taxes"
msgstr "Impostos"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_hotel_reservation__total_tax
msgid "The amount without tax."
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__theme_id
msgid "Theme"
msgstr "Tema"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__name
msgid "Theme Name"
msgstr "Nome do tema"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_theme_plan_theme_name_uniq
msgid "Theme Name must be unique !"
msgstr "Nome do tema deve ser exclusivo!"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_theme_plan_tree
#: model:ir.model,name:banquet_managment.model_theme_plan
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_theme_plan_tree
msgid "Theme Plan"
msgstr "Plano do tema"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__total_cost1
msgid "Total Reservation cost"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__price_unit
msgid "Unit Price"
msgstr "Preço unitário"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__untaxed_amt
msgid "Untaxed Amount"
msgstr "Valor da venda  imposto"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_uom
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_uom
msgid "UoM"
msgstr "UoM"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_crm_lead
msgid "User Modification"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__via
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__via
msgid "Via"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Visa Journal Entry"
msgstr "Visa entrada de diário"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_food_items__product_id
msgid ""
"Will list out all food items that belong to company of selected shop. \n"
" It also shows global product as well."
msgstr "rá listar todos os itens alimentares que pertencem à empresa de loja selecionada.\n"
"Também mostra o produto global."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_banquet_quotation__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user."
msgstr "Mostrará a lista de loja que pertencem as empresas permitidas do usuário conectado."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_deposit_payment_policy__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assign a shop to configure shop-wise deposit  policy."
msgstr "Mostrará a lista de lojas que pertencem as empresas permitidas do usuário conectado.\n"
"  -Atribuir uma loja para configurar a política de depósito de acordo com a loja."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_crm_lead__shop_id
msgid "Will show only open leads for the selected shop."
msgstr ""

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_banquet_quotation_lead_history
msgid "itinerary lead history"
msgstr "história lead de itinerário"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_hotel_room
msgid "room Inherit "
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_seating_plan_tree
msgid "seating Plan"
msgstr "Plano de lugares"
