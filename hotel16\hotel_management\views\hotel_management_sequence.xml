<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
        
        <record id="seq_hotel_housekeeping111" model="ir.sequence">
            <field name="name">Hotel Housekeeping Ref</field>
            <field name="code">hotel.housekeeping1</field>
            <field name="prefix">HK</field>
            <field name="padding">5</field>
			<field name="company_id" eval="False"/>
        </record>
        
        
        <record id="seq_rr_housekeeping_repair1111" model="ir.sequence">
            <field name="name">rr housekeeping Repair</field>
            <field name="code">rr.housekeeping.repair</field>
            <field name="prefix">RPR</field>
            <field name="padding">5</field>
			<field name="company_id" eval="False"/>
        </record>
        
         <record id="seq_rr_housekeeping_replace1111" model="ir.sequence">
            <field name="name">rr housekeeping Repalace</field>
            <field name="code">rr.housekeeping.replace</field>
            <field name="prefix">RPC</field>
            <field name="padding">5</field>
			<field name="company_id" eval="False"/>
        </record>
        
        <record model="ir.sequence" id="seq_hotel_reservation_new">
			<field name="name">Hotel Reservations</field>
			<field name="code">hotel.reservation</field>
			<field name="prefix">HR/</field>
			<field name="padding">5</field>
			<field name="company_id" eval="False"/>
	    </record>
	    
	    <record model="ir.sequence" id="seq_hotel_reservation1234">
			<field name="name">Hotel Restaurant Order</field>
			<field name="code">hotel.restaurant.order</field>
			<field name="prefix">TO/</field>
			<field name="padding">5</field>
			<field name="company_id" eval="False"/>
	   </record>
	   
	   <record model="ir.sequence" id="seq_hotel_table1234">
			<field name="name">Hotel Restaurant Reservation</field>
			<field name="code">hotel.restaurant.reservation</field>
			<field name="prefix">TBR/</field>
			<field name="padding">5</field>
			<field name="company_id" eval="False"/>
	  </record>
	
	  <record model="ir.sequence" id="hotel_restaurant_seq_hotel_resorder1">
		<field name="name">Hotel Reservation Order</field>
		<field name="code">hotel.reservation.order</field>
		<field name="prefix">TBRO/</field>
		<field name="padding">5</field>
		<field name="company_id" eval="False"/>
	  </record>
        
       <record model="ir.sequence" id="seq_issue_slip_seq">
	        <field name="name">Issue Slip</field>
	        <field name="code">issue.material.details</field>
	        <field name="prefix">ISS/</field>
	        <field name="padding">6</field>
			<field name="company_id" eval="False"/>
       </record>

       <record id="seq_agent_commission_invoice_new" model="ir.sequence">
            <field name="name">Agent Commission Invoice</field>
            <field name="code">agent.commission.invoice</field>
            <field name="prefix">ACI</field>
            <field name="padding">6</field>
			<field name="company_id" eval="False"/>
		</record>
		
	  <record model="ir.sequence" id="seq_hotel_reservation11">
			<field name="name">Hotel Order</field>
			<field name="code">hotel.restaurant.order</field>
			<field name="prefix">TO/</field>
			<field name="padding">5</field>
	  </record>	  
	  <record model="ir.sequence" id="seq_hotel_table1">
		<field name="name">Hotel Reservation</field>
		<field name="code">hotel.restaurant.reservation</field>
		<field name="prefix">TBR/</field>
		<field name="padding">5</field>
	 </record>
	 
	<record model="ir.sequence" id="seq_hotel_resorder145">
        <field name="name">Hotel Reservation New</field>
        <field name="code">hotel.reservation.order12</field>
        <field name="prefix">KOT/</field>
        <field name="padding">5</field>
    </record>
    
     <record model="ir.sequence" id="seq_hotel_resorder_bot">
        <field name="name">Hotel Reservation BOT</field>
        <field name="code">hotel.reservation.botorder</field>
        <field name="prefix">BOT/</field>
        <field name="padding">5</field>
		<field name="company_id" eval="False"/>
     </record>

	
</data>
</odoo>