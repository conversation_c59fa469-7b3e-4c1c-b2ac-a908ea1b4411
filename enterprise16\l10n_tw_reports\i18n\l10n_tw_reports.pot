# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tw_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-27 05:54+0000\n"
"PO-Revision-Date: 2022-06-27 05:54+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_1
msgid "1. Interest income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oc_1
msgid "1. Sales cost"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oe_1
msgid "1. Sales expenses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oi_1
msgid "1. Sales income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_1
msgid "1. Unrealized gains and losses on available-for-sale financial assets"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_10
msgid "10. Gains and losses on disposal of property, plant and equipment"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_11
msgid "11. Impairment loss"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_12
msgid "12. Impairment of reversal benefits"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_13
msgid "13. Others"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_2
msgid ""
"2. Hedging gains and losses that are part of effective hedging in cash flow "
"hedging"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oc_2
msgid "2. Labor cost"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oi_2
msgid "2. Labor income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oe_2
msgid "2. Management costs"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_2
msgid "2. Rental income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_3
msgid ""
"3. Exchange differencs on translation of financial statements of foreign "
"operating agencies"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oc_3
msgid "3. Other costs"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oi_3
msgid "3. Other incomes"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oe_3
msgid "3. R&D expenses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_3
msgid "3. Royalty income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_4
msgid "4. Dividend income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oe_4
msgid "4. Other expenses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_4
msgid "4. Unrealized revaluation appreciation (Note 1)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_5
msgid "5. Interest costs"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_5
msgid ""
"5. Share of other comprehensive profit or loss recognised using the equity "
"method"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ocglcp_6
msgid "6. Income tax related to other comprehensive profit or loss (Note 2)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_6
msgid ""
"6. Net gain or loss on financial assets (liabilities) at fair value through "
"profit or loss"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_7
msgid "7. Investment gains and losses recognised using the equity method"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_8
msgid "8. Exchange gains and losses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid_9
msgid "9. Disposal of investment gains and losses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oi
msgid "Operating income"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oc
msgid "Operating cost"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_op
msgid "Operating profit"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_oe
msgid "Operating expenses"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_onp
msgid "Operating net profit (net loss)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_noid
msgid "Non-operating income and depreciation"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_npbt
msgid "Net profit before tax (net loss)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_ite
msgid "Income tax expense (benefits)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_npatcbu
msgid "Net profit after tax (net loss) of continuing business units"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_plcu
msgid "Profit and loss of closed units (after tax)"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.ccount_financial_report_l10n_tw_pl_npatcp
msgid "Net profit after tax (net loss) for the current period"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.ccount_financial_report_l10n_tw_pl_ocglcp
msgid "Other comprehensive gains and losses for the current period"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_l10n_tw_pl_tcplcp
msgid "Total comprehensive profit and loss for the current period"
msgstr ""

#. module: l10n_tw_reports
#: model:account.financial.html.report,name:l10n_tw_reports.account_financial_report_l10n_tw_pl
#: model:ir.actions.client,name:l10n_tw_reports.account_financial_html_report_action_4
#: model:ir.ui.menu,name:l10n_tw_reports.account_financial_html_report_menu_4
msgid "Profit and Loss"
msgstr ""

#. module: l10n_tw_reports
#: model:ir.ui.menu,name:l10n_tw_reports.account_reports_tw_statements_menu
msgid "Taiwan"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_total_assets0_l10n_tw
msgid "ASSETS"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_bank_view0_l10n_tw
msgid "Bank and Cash Accounts"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_current_assets0_l10n_tw
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_current_assets_view0_l10n_tw
msgid "Current Assets"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_current_liabilities0_l10n_tw
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_current_liabilities1_l10n_tw
msgid "Current Liabilities"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_current_year_earnings_line_2_l10n_tw
msgid "Current Year Allocated Earnings"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_current_year_earnings_line_1_l10n_tw
msgid "Current Year Earnings"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_current_year_earnings0_l10n_tw
msgid "Current Year Unallocated Earnings"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_equity0_l10n_tw
msgid "EQUITY"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_liabilities_view0_l10n_tw
msgid "LIABILITIES"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_libailities_and_equity
msgid "LIABILITIES + EQUITY"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_current_liabilities_payable_l10n_tw
msgid "Payables"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_fixed_assets_view0_l10n_tw
msgid "Plus Fixed Assets"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_non_current_assets_view0_l10n_tw
msgid "Plus Non-current Assets"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_non_current_liabilities0_l10n_tw
msgid "Plus Non-current Liabilities"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_prepayements0_l10n_tw
msgid "Prepayments"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_previous_year_earnings0_l10n_tw
msgid "Previous Years Unallocated Earnings"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report,name:l10n_tw_reports.account_financial_report_l10n_tw_pl
msgid "Profit and Loss"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_report_receivable0_l10n_tw
msgid "Receivables"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_retained_earnings0_l10n_tw
msgid "Retained Earnings"
msgstr ""

#. module: l10n_tw_reports
#: model:account.report.line,name:l10n_tw_reports.account_financial_unaffected_earnings0_l10n_tw
msgid "Unallocated Earnings"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Total %s"
msgstr "Totalt %s"
