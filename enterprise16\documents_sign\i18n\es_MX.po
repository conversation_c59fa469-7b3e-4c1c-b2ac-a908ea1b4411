# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_sign
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Un conjunto de condiciones y acciones que estarán disponibles para todos los"
" archivos adjuntos que coincidan con las condiciones"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Crear"

#. module: documents_sign
#: code:addons/documents_sign/models/workflow.py:0
#, python-format
msgid "New templates"
msgstr "Nuevas plantillas"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_direct
msgid "PDF to Sign"
msgstr "PDF para firma"

#. module: documents_sign
#: model:documents.workflow.rule,name:documents_sign.documents_sign_rule_sign_directly
msgid "Sign"
msgstr "Firmar"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_new
msgid "Signature PDF Template"
msgstr "Plantilla de PDF para firma"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr "Solicitud de firma"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr "Plantilla de firma"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Signed Document Tags"
msgstr "Etiquetas de documentos firmados"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Signed Document Workspace"
msgstr "Espacio de trabajo de documentos firmados"
