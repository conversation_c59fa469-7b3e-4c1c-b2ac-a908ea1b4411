<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="social_stream_view_form" model="ir.ui.view">
        <field name="name">social.stream.view.form.inherit.twitter</field>
        <field name="model">social.stream</field>
        <field name="inherit_id" ref="social.social_stream_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='stream_type_id']" position="after">
                <field name="twitter_followed_account_search"
                    attrs="{'required': [('id', '=', False), ('stream_type_type', 'in', ('twitter_follow', 'twitter_likes'))], 'invisible': ['|', ('stream_type_type', 'not in', ('twitter_follow', 'twitter_likes')), '|', ('account_id', '=', False), ('id', '!=', False)]}"
                    widget="twitter_users_autocomplete" />
                <field name="twitter_followed_account_id" invisible="1" />
                <field name="twitter_searched_keyword" placeholder="e.g. #odoo"
                    attrs="{'required': [('stream_type_type', '=', 'twitter_keyword')], 'invisible': ['|', ('stream_type_type', '!=', 'twitter_keyword'), ('account_id', '=', False)], 'readonly': [('id', '!=', False)]}" />
            </xpath>
        </field>
    </record>
</data>
</odoo>
