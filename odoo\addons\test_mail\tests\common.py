# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.addons.mail.tests.common import MailCommon
from odoo.tests.common import TransactionCase


class TestMailCommon(MailCommon):
    """ Main entry point for functional tests. Kept to ease backward
    compatibility. """


class TestRecipients(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super(TestRecipients, cls).setUpClass()
        Partner = cls.env['res.partner'].with_context({
            'mail_create_nolog': True,
            'mail_create_nosubscribe': True,
            'mail_notrack': True,
            'no_reset_password': True,
        })
        cls.partner_1 = Partner.create({
            'name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'country_id': cls.env.ref('base.be').id,
            'mobile': '**********',
            'phone': False,
        })
        cls.partner_2 = Partner.create({
            'name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'country_id': cls.env.ref('base.be').id,
            'mobile': '+32 456 22 11 00',
            'phone': False,
        })
