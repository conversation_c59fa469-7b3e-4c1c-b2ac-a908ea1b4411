<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_picking_wave_tree" model="ir.ui.view">
        <field name="name">stock.picking.wave.tree</field>
        <field name="model">stock.picking.batch</field>
        <field name="priority">25</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="stock_picking_batch.stock_picking_batch_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
        </field>
    </record>
    <record id="stock_picking_wave_kanban" model="ir.ui.view">
        <field name="name">stock.picking.wave.kanban</field>
        <field name="model">stock.picking.batch</field>
        <field name="priority">25</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="stock_picking_batch.stock_picking_batch_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
        </field>
    </record>
    <record id="action_picking_tree_wave" model="ir.actions.act_window">
        <field name="name">Wave Transfers</field>
        <field name="res_model">stock.picking.batch</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="context">{'search_default_draft': True, 'search_default_in_progress': True}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('stock_picking_batch.stock_picking_wave_tree')}),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('stock_picking_batch.stock_picking_wave_kanban')})]"/>
        <field name="domain">[('is_wave', '=', True)]</field>
        <field name="search_view_id" ref="stock_picking_batch_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new wave transfer
                </p><p>
                    The goal of the wave transfer is to group operations from different transfer
                    together in order to increase their efficiency.
                    It may also be useful to assign jobs (one person = one batch) or
                    help the timing management of operations (tasks to be done at 1pm).
            </p>
        </field>
    </record>

    <record id="stock_picking_type_kanban_batch" model="ir.ui.view">
        <field name="name">picking.type.kanban.batch</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.stock_picking_type_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='picking_type_backorder_count']" position="after">
                <div t-if="record.count_picking_batch.raw_value > 0" class="row">
                    <div class="col-12">
                        <a class="oe_kanban_stock_picking_type_list" name="get_action_picking_tree_batch" type="object">
                            <field name="count_picking_batch"/>
                            Batches
                        </a>
                    </div>
                </div>
                <div t-if="record.count_picking_wave.raw_value > 0" class="row">
                    <div class="col-12">
                        <a class="oe_kanban_stock_picking_type_list" name="get_action_picking_tree_wave" type="object">
                            <field name="count_picking_wave"/>
                            Waves
                        </a>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
    <menuitem
        action="action_picking_tree_wave"
        id="stock_picking_wave_menu"
        parent="stock.menu_stock_warehouse_mgmt"
        groups="stock.group_stock_picking_wave"
        sequence="23"/>

</odoo>
