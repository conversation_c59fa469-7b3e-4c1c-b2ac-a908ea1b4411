# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_ebay
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON> Villalobos <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" If you want to set quantity to 0, the Out Of Stock option should be enabled"
" and the listing duration should set to Good 'Til Canceled"
msgstr ""
"Si desea establecer la cantidad a 0, debe activar la opción \"Sin "
"existencias\" y definir la duración de la lista a \"Válido hasta "
"cancelación\". "

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
" You need to have at least 2 variations selected for a multi-variations listing.\n"
" Or if you try to delete a variation, you cannot do it by unselecting it. Setting the quantity to 0 is the safest method to make a variation unavailable."
msgstr ""
"Se deben seleccionar al menos 2 variantes para tener una lista multivariante.\n"
"O, si intenta eliminar una variación, no es suficiente con desmarcarla. El método más seguro para eliminar una variación es poner la cantidad en 0. "

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_10
msgid "10 Days"
msgstr "10 días"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_3
msgid "3 Days"
msgstr "3 días"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_30
msgid "30 Days (only for fixed price)"
msgstr "30 días (solo para el precio fijo)"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_5
msgid "5 Days"
msgstr "5 días"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__days_7
msgid "7 Days"
msgstr "7 días"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr "<span class=\"o_form_label\">Equipo de ventas</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Storage</span>"
msgstr "<span class=\"o_form_label\">Almacenamiento</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Synchronization</span>"
msgstr "<span class=\"o_form_label\">Sincronización</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Account</span>"
msgstr "<span class=\"o_form_label\">Cuenta de eBay</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">eBay Marketplace Account Deletion/Closure "
"Notifications</span>"
msgstr ""
"<span class=\"o_form_label\">Notificaciones de eliminación/cierre de cuentas"
" de eBay Marketplace</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "<span class=\"o_form_label\">eBay Options</span>"
msgstr "<span class=\"o_form_label\">Opciones de eBay</span>"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Activar otras divisas"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Add other countries"
msgstr "Añadir otros países"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"All the quantities must be greater than 0 or you need to enable the Out Of "
"Stock option."
msgstr ""
"Todas las cantidades deben ser mayores a 0 o debe activar la opción de Sin "
"existencias. "

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_best_offer
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_best_offer
msgid "Allow Best Offer"
msgstr "Aceptar la mejor oferta"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__chinese
msgid "Auction"
msgstr "Subasta"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_buy_it_now_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_buy_it_now_price
msgid "Buy It Now Price"
msgstr "Precio de compra actual"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Cancel"
msgstr "Cancelar"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_id
msgid "Category"
msgstr "Categoría"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_category_2_id
msgid "Category 2 (Optional)"
msgstr "Categoría 2 (Opcional)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_id
msgid "Category ID"
msgstr "ID de categoría"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_parent_id
msgid "Category Parent ID"
msgstr "ID de categoría padre"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__category_type
msgid "Category Type"
msgstr "Tipo de categoría"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__code
msgid "Code"
msgstr "Código"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Configure The eBay Integrator Now"
msgstr " Configurar el integrador de eBay ahora"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_country_all_form
msgid "Countries"
msgstr "Países"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Countries & Currencies"
msgstr "Países y divisas"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_country
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_country
msgid "Country"
msgstr "País"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__create_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__create_date
msgid "Created on"
msgstr "Creado en"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_res_currency
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Currency"
msgstr "Divisa"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__store
msgid "Custom Store Category"
msgstr "Categoría de tienda personalizada"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Describe the product characteristics..."
msgstr "Describa las características del producto... "

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_template_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_template_id
msgid "Description Template"
msgstr "Plantilla de descripción"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_dev_id
msgid "Developer Key"
msgstr "Clave de desarrollador"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__display_name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_duration
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_duration
msgid "Duration"
msgstr "Duración"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay Synchronisation could not confirm because of the following error:\n"
"%s"
msgstr ""
"No se pudo confirmar la sincronización con eBay debido al siguiente error:\n"
"%s"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid ""
"Ebay could not synchronize order:\n"
"%s"
msgstr ""
"Ebay no pudo sincronizar el pedido:\n"
"%s"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_orders_sync
msgid "Ebay: get new orders"
msgstr "Ebay: obtener nuevos pedidos"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_orders_recovery
msgid "Ebay: orders recovery"
msgstr "Ebay: recuperación de pedidos"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_stock_sync_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_stock_sync
msgid "Ebay: synchronise stock"
msgstr "Ebay: sincronizar las existencias"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.ir_cron_sale_ebay_categories_ir_actions_server
#: model:ir.cron,cron_name:sale_ebay.ir_cron_sale_ebay_categories
msgid "Ebay: update categories"
msgstr "Ebay: Actualizar categorías"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "End Item's Listing"
msgstr "Fin de la lista de artículos"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
" No Variant Set To Be Listed On eBay."
msgstr ""
"Se encontró un error.\n"
"No hay ningún conjunto de variantes por listar en eBay."

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Error Encountered.\n"
"'%s'"
msgstr ""
"Se encontró un error.\n"
"'%s'"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Fixed Price"
msgstr "Precio fijo"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_type__fixedpriceitem
msgid "Fixed price"
msgstr "Precio fijo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__full_name
msgid "Full Name"
msgstr "Nombre completo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_gallery_plus
msgid "Gallery Plus"
msgstr "Gallery Plus"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Generate Token"
msgstr "Generar token"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__product_template__ebay_listing_duration__gtc
msgid "Good 'Til Cancelled (only for fixed price)"
msgstr "Válido hasta cancelación (solo para precio fijo) "

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__id
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__id
msgid "ID"
msgstr "ID"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,help:sale_ebay.field_res_currency__ebay_available
msgid "If activated, can be used for eBay."
msgstr "Si se activa, puede utilizarse para eBay."

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Import eBay config data and sync transactions"
msgstr "Importar datos de configuración y sincronizar transacciones de eBay"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"Impossible to revise a listing into a multi-variations listing.\n"
" Create a new listing."
msgstr ""
"Es imposible transformar una publicación en una publicación con múltiples variantes.\n"
"Crear publicación nueva."

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_item_condition_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_item_condition_id
msgid "Item Condition"
msgstr "Estado del artículo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy____last_update
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_uid
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__write_date
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__write_date
msgid "Last Updated on"
msgstr "Ultima actualización realizada"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_last_sync
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_last_sync
msgid "Last update"
msgstr "Última actualización"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__leaf_category
msgid "Leaf Category"
msgstr "Categoría básica"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link Existing Listing"
msgstr "Enlace a lista vigente"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Link With Existing eBay Listing"
msgstr "Enlazar a una lista vigente de eBay"

#. module: sale_ebay
#: model:ir.actions.act_window,name:sale_ebay.action_ebay_link_listing
msgid "Link with Existing eBay Listing"
msgstr "Enlazar a una lista vigente de eBay"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.ebay_link_listing_view
msgid "Link with eBay Listing"
msgstr "Enlazar a una lista de eBay"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "List Item on eBay"
msgstr " Publicar artículo en eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_type
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_type
msgid "Listing Type"
msgstr " Tipo de publicación"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_location
msgid "Location"
msgstr "Ubicación"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_account_deletion_endpoint
msgid "Marketplace account deletion notification endpoint"
msgstr "Última notificación de eliminación de cuentas de Marketplace"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode"
msgstr "Modo"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Mode and credentials"
msgstr " Modo y credenciales"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_category__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_item_condition__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__name
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__name
msgid "Name"
msgstr "Nombre"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid "No Business Policies"
msgstr " Sin políticas empresariales"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__ebay_category__category_type__ebay
msgid "Official eBay Category"
msgstr "Categoría oficial de eBay"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "One parameter is missing."
msgstr "Falta un parámetro."

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "Or the condition is not compatible with the category."
msgstr "O la condición no es compatible con la categoría."

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_out_of_stock
msgid "Out Of Stock"
msgstr " Sin existencias"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_payment_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_payment_policy_id
msgid "Payment Policy"
msgstr " Política de pago"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"Please provide your ebay production keys before enabling the account "
"deletion notifications."
msgstr ""
"Indique sus claves de producción de ebay antes de activar las notificaciones"
" de eliminación de cuentas."

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Policies"
msgstr "Políticas"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_id
msgid "Policy ID"
msgstr "ID de la política"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_private_listing
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_private_listing
msgid "Private Listing"
msgstr "Anuncio privado"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_template
msgid "Product"
msgstr "Producto"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "Product created from eBay transaction %s"
msgstr "El producto se creó a partir de una transacción de eBay %s"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__prod
msgid "Production"
msgstr "Producción"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_app_id
msgid "Production App Key"
msgstr "Production App Key"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_cert_id
msgid "Production Cert Key"
msgstr "Production Cert Key"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_prod_token
msgid "Production Token"
msgstr "Token de producción"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_use
msgid "Publish On eBay"
msgstr "Publicar en eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity
msgid "Quantity On eBay"
msgstr "Cantidad en eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_quantity_sold
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_quantity_sold
msgid "Quantity Sold"
msgstr " Cantidad vendida"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Relist Item"
msgstr "Volver a publicar el artículo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_return_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_return_policy_id
msgid "Return Policy"
msgstr "Política de devoluciones"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Revise Item"
msgstr "Modificar artículo"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_sale_order
msgid "Sales Order"
msgstr "Orden de venta"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team"
msgstr "Equipo de ventas"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sales Team to manage eBay sales"
msgstr "Equipo de ventas que administra las ventas de eBay"

#. module: sale_ebay
#: model:ir.model.fields.selection,name:sale_ebay.selection__res_config_settings__ebay_domain__sand
msgid "Sandbox"
msgstr "Entorno de pruebas"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_app_id
msgid "Sandbox App Key"
msgstr "Clave de aplicación del entorno de prueba"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_cert_id
msgid "Sandbox Cert Key"
msgstr "Clave de certificado del entorno de prueba "

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sandbox_token
msgid "Sandbox Token"
msgstr "Token del entorno de prueba"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Sell on eBay"
msgstr " Vender en eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_seller_shipping_policy_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_seller_shipping_policy_id
msgid "Shipping Policy"
msgstr "Política de entrega"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_start_date
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_start_date
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_price
msgid "Starting Price for Auction"
msgstr " Precio inicial de subasta"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Storage location of your products"
msgstr "Ubicación del almacenamiento de sus productos"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_id
msgid "Store Category (Optional)"
msgstr "Categoría de tienda (opcional)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_store_category_2_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_store_category_2_id
msgid "Store Category 2 (Optional)"
msgstr "Categoría 2 de tienda (opcional)"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_subtitle
msgid "Subtitle"
msgstr "Subtítulo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__short_summary
msgid "Summary"
msgstr "Resumen"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "Sync now"
msgstr "Sincronizar ahora"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Chose The Following Delivery Method :\n"
msgstr "El comprador eligió el siguiente método de entrega:\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_order.py:0
#, python-format
msgid "The Buyer Posted :\n"
msgstr "El comprador publicó:\n"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_config_settings.py:0
#, python-format
msgid ""
"The python 'cryptography' module is not installed on your server.\n"
"It is necessary to support eBay account deletion notifications, please contact your system administrator to install it."
msgstr ""
"No se ha instalado el módulo python 'cryptography' en su servidor.\n"
"Es necesario para admitir las notificaciones de eliminación de cuentas de eBay, póngase en contacto con el administrador del sistema para instalarlo."

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_subtitle
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_subtitle
msgid ""
"The subtitle is restricted to 55 characters. Fees can be claimed by eBay for"
" this feature"
msgstr ""
"El subtítulo está limitado a 55 caracteres, es posible que eBay cobre "
"tarifas adicionales por esta función"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_title
msgid "The title is restricted to 80 characters"
msgstr "El título está limitado a 80 caracteres"

#. module: sale_ebay
#: model:ir.model.fields,help:sale_ebay.field_product_product__ebay_category_2_id
#: model:ir.model.fields,help:sale_ebay.field_product_template__ebay_category_2_id
msgid ""
"The use of a secondary category is not allowed on every eBay sites. Fees can"
" be claimed by eBay for this feature"
msgstr ""
"No se permite el uso de una categoría secundaria en algunos sitios de eBay, "
"es posible que se cobre una tarifa adicional por esta función"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"There is no last synchronization date in your System Parameters. Create a "
"System Parameter record with the key \"ebay_last_sync\" and the value set to"
" the date of the oldest order you wish to synchronize in the format \"YYYY-"
"MM-DD\"."
msgstr ""
"No hay una fecha de última sincronización en sus Parámetros del sistema. "
"Establezca un registro de Parámetros del sistema con la clave "
"\"ebay_last_sync\" y el valor definido como la fecha del pedido más antiguo "
"que desea sincronizar con el formato \"AAAA-MM-DD\"."

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid ""
"This function should not be called with a range of more than 30 days, as "
"eBay does not handle longer timespans. Instead use synchronize_orders which "
"split in as many calls as needed."
msgstr ""
"Esta función no debe ser ejecutada en un rango de mas de 30 días, pues eBay "
"no maneja periodos más largos. En su lugar, utilice synchronize_orders que "
"lo divide en tantas llamadas como necesite. "

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/res_partner.py:0
#, python-format
msgid ""
"This is an automated notification as a deletion request has been received from eBay concerning the account \"%s (%s)\". The account has been anonymised already and his portal access revoked (if they had any).\n"
"\n"
"However, personal information might remain in linked documents, please review them according to laws that apply."
msgstr ""
"Esta es una notificación automática, dado que se ha recibido una solicitud de eliminación de eBay relativa a la cuenta \"%s (%s)\". La cuenta ya se ha convertido en anónima y se ha cancelado su acceso al portal (si es que lo tenía).\n"
"\n"
"Sin embargo, la información personal puede permanecer en los documentos vinculados, revíselos de acuerdo con las leyes correspondientes."

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_title
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_title
msgid "Title"
msgstr "Título"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_policy__policy_type
msgid "Type"
msgstr "Tipo"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_sync_stock
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_sync_stock
msgid "Use Stock Quantity"
msgstr "Utilizar la cantidad de existencias"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_use
msgid "Use eBay"
msgstr "Usar eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_country__ebay_available
#: model:ir.model.fields,field_description:sale_ebay.field_res_currency__ebay_available
msgid "Use on eBay"
msgstr "Usar en eBay"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "Variants"
msgstr "Variantes"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_verification_token
msgid "Verification Token"
msgstr "Token de verificación"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_zip_code
msgid "Zip"
msgstr "C.P."

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay"
msgstr "eBay"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_category
msgid "eBay Category"
msgstr "Categoría de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_description
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_description
#: model_terms:ir.ui.view,arch_db:sale_ebay.product_template_sale_ebay_form_view
msgid "eBay Description"
msgstr "Descripción de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_domain
msgid "eBay Environment"
msgstr "Entorno de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_fixed_price
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_fixed_price
msgid "eBay Fixed Price"
msgstr "Precio fijo de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_site__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_id
msgid "eBay ID"
msgstr "ID de eBay"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_item_condition
msgid "eBay Item Condition"
msgstr "Estado del artículo de eBay"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_link_listing
msgid "eBay Link Listing"
msgstr "Enlace a la lista de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_ebay_link_listing__ebay_id
msgid "eBay Listing ID"
msgstr "ID de la lista de eBay"

#. module: sale_ebay
#: model:mail.activity.type,summary:sale_ebay.ebay_GDPR_notification
msgid "eBay Odoo connector notification - account deletion"
msgstr "Notificación del conector de eBay de Odoo: eliminación de la cuenta"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_policy
msgid "eBay Policy"
msgstr "Políticas de eBay"

#. module: sale_ebay
#: model:ir.model,name:sale_ebay.model_ebay_site
msgid "eBay Site"
msgstr "Sitio de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_listing_status
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_listing_status
msgid "eBay Status"
msgstr "Estado de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_partner__ebay_id
#: model:ir.model.fields,field_description:sale_ebay.field_res_users__ebay_id
msgid "eBay User ID"
msgstr "ID del usuario de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_variant_url
msgid "eBay Variant URL"
msgstr "URL de la variante de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_site
msgid "eBay Website"
msgstr "Sitio web de eBay"

#. module: sale_ebay
#: model:mail.activity.type,name:sale_ebay.ebay_GDPR_notification
msgid "eBay connector: account deletion notification"
msgstr "Conector de eBay: notificación de eliminación de cuenta"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay documentation"
msgstr "documentación de eBay"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/sale_ebay.py:0
#, python-format
msgid ""
"eBay error: Impossible to synchronize the categories. \n"
"'%s'"
msgstr ""
"Error de eBay: No se pueden sincronizar las categorías. \n"
"'%s'"

#. module: sale_ebay
#. odoo-python
#: code:addons/sale_ebay/models/product.py:0
#, python-format
msgid "eBay is unreachable. Please try again later."
msgstr "No se puede acceder a eBay. Inténtelo más tarde."

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "eBay parameters"
msgstr "Parámetros de eBay"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid ""
"eBay requires supporting customer account deletion/closure notifications.\n"
"                                Please follow the"
msgstr ""
"eBay solicita habilitar las notificaciones de eliminación/cierre de las cuentas de los clientes.\n"
"Siga la"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_product_product__ebay_url
#: model:ir.model.fields,field_description:sale_ebay.field_product_template__ebay_url
msgid "eBay url"
msgstr "URL de eBay"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_end_items_listings
msgid "eBay: End product listings"
msgstr "eBay: Fin de la lista de productos"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_list_items
msgid "eBay: List products"
msgstr "eBay: Publicar los productos en la lista"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_relist_items
msgid "eBay: Relist products"
msgstr "eBay: Volver a publicar los productos en la lista"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_revise_items
msgid "eBay: Revise products"
msgstr "eBay: Modificar productos"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_unlink_items_listings
msgid "eBay: Unlink product listings"
msgstr "eBay: Desvincular las listas de productos"

#. module: sale_ebay
#: model:ir.actions.server,name:sale_ebay.action_ebay_update_carrier
msgid "eBay: Update carrier information"
msgstr "eBay: Actualizar la información del transportista"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_currency
msgid "ebay Currency"
msgstr "Divisa de eBay"

#. module: sale_ebay
#: model:ir.model.fields,field_description:sale_ebay.field_res_config_settings__ebay_sales_team
msgid "ebay Sales Team"
msgstr "Equipo de ventas de eBay"

#. module: sale_ebay
#: model_terms:ir.ui.view,arch_db:sale_ebay.res_config_settings_view_form
msgid "to setup this mechanism."
msgstr "para activar este mecanismo."

#. module: sale_ebay
#: code:addons/sale_ebay/tools/ebaysdk.py:0
#, python-format
msgid ""
"An unexpected error occured from eBay.\n"
"Please check your credentials and try again later."
msgstr ""
"Ocurrió un error inesperado de eBay.\n"
"Verifique sus credenciales y vuelva a intentarlo más tarde."
