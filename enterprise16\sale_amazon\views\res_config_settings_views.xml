<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="list_amazon_account_action" model="ir.actions.act_window">
        <field name="name">Amazon Accounts</field>
        <field name="res_model">amazon.account</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'active_test': False, 'search_default_filter_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Register your Amazon account
            </p><p>
            Amazon accounts correspond to Amazon Seller Central accounts.
        </p><p>
            Register yours to start synchronizing your orders into Odoo.
        </p>
        </field>
    </record>

    <record id="list_amazon_marketplace_action" model="ir.actions.act_window">
        <field name="name">Amazon Marketplaces</field>
        <field name="res_model">amazon.marketplace</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.sale.amazon</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='amazon_connector']" position="inside">
                <div class="mt16">
                    <div>
                        <button name="%(sale_amazon.list_amazon_account_action)d"
                                string="Amazon Accounts"
                                icon="fa-arrow-right"
                                type="action"
                                class="btn-link"/>
                    </div>
                    <div>
                        <button name="%(sale_amazon.list_amazon_marketplace_action)d"
                                string="Amazon Marketplaces"
                                icon="fa-arrow-right"
                                type="action"
                                class="btn-link"
                                groups="base.group_no_one"/>
                    </div>
                    <div>
                        <button name="action_view_default_amazon_products"
                                string="Default Products"
                                icon="fa-arrow-right"
                                type="object"
                                class="btn-link"
                                groups="base.group_no_one"/>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
