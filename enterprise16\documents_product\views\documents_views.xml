<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.documents</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="70"/>
            <field name="inherit_id" ref="documents.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('o_documents_block')]" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
                <xpath expr="//div[hasclass('o_documents_block')]" position="inside">
                    <div class="row mt16 o_settings_container">
                        <div class="col-xs-12 col-md-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="documents_product_settings"/>
                            </div>
                            <div class="o_setting_right_pane o_documents_right_pane">
                                <label for="documents_product_settings"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                <div class="row">
                                    <div class="text-muted col-md-12">
                                        Centralize files attached to products
                                    </div>
                                </div>
                                <div class="content-group" attrs="{'invisible' : [('documents_product_settings', '=', False)]}">
                                    <div class="row mt16">
                                        <label class="o_form_label col-lg-3" for="product_folder" string="Workspace"/>
                                        <field name="product_folder" attrs="{'required' : [('documents_product_settings', '=', True)]}"/>
                                    </div>
                                    <div class="row">
                                        <label class="o_form_label col-lg-3" for="product_tags" string="Default Tags"/>
                                       <field name="product_tags" widget="many2many_tags" domain="[('folder_id','=', product_folder)]"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
    </record>

</odoo>
