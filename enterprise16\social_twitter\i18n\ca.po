# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_twitter
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> Bochaca <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# jabiri7, 2022
# <PERSON><PERSON> <PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_post_template.py:0
#, python-format
msgid "%s / %s characters to fit in a Tweet"
msgstr "%s / %s caràcters per a ajustar-se a una piulada"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "11m"
msgstr "11 m"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "<b class=\"text-900\">Twitter Account</b>"
msgstr "<b class=\"text-900\">compte de Twitter</b>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a tweet\"/>\n"
"                                    <span>Quote Tweet</span>"
msgstr ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a tweet\"/>\n"
"                                    <span>Tweet de cita</span>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-retweet me-1\" title=\"Retweet a tweet\"/>"
msgstr "<i class=\"fa fa-retweet me-1\" title=\"Retweet a tweet\"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<span class=\"fst-italic\">Empty tweet</span>"
msgstr "<span class=\"fst-italic\">tuit buit</span>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "@twitteraccount ·"
msgstr "·twitteraccount ·"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "A retweet already exists"
msgstr "Ja existeix un retweet"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Authentication failed. Please enter valid credentials."
msgstr "Ha fallat l'autenticació. Si us plau, introduïu credencials vàlides."

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Imatge de l'autor"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Can not like / unlike the tweet\n"
"%s."
msgstr ""
"No podeu marcar la piulada com m'agrada/no m'agrada\n"
"%s."

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancel·lar"

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_res_config_settings__twitter_use_own_account
msgid ""
"Check this if you want to use your personal Twitter Developer Account "
"instead of the provided one."
msgstr ""
"Marqueu aquesta opció si voleu utilitzar el vostre compte personal per a "
"desenvolupadors de Twitter en lloc del compte indicat."

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Key"
msgstr "Clau del consumidor"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Secret Key"
msgstr "Clau secreta del consumidor"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_date
msgid "Created on"
msgstr "Creat el"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__description
msgid "Description"
msgstr "Descripció"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__has_twitter_accounts
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__has_twitter_accounts
msgid "Display Twitter Preview"
msgstr "Visualitza la previsualització del Twitter"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_twitter_quote.js:0
#, python-format
msgid "Error while sending the data to the server."
msgstr "S'ha produït un error en enviar les dades al servidor."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to delete the Tweet\n"
"%s."
msgstr ""
"No ha pogut esborrar-se la piulada\n"
"%s."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to fetch the conversation id: '%s' using the account %s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to fetch the tweets in the same thread: '%s' using the account %s."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to post comment: %s with the account %s."
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_likes
msgid "Favorites of"
msgstr "Preferits de"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__id
msgid "ID"
msgstr "ID"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__image
msgid "Image"
msgstr "Imatge"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__is_twitter_post_limit_exceed
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__is_twitter_post_limit_exceed
msgid "Is Twitter Post Limit Exceed"
msgstr "S'ha excedit el límit de publicacions de Twitter"

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_keyword
msgid "Keyword"
msgstr "Paraula clau"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Likes"
msgstr "M'agrada"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"Looks like you've made too many requests. Please wait a few minutes before "
"giving it another try."
msgstr ""
"Sembla que has fet massa peticions. Espereu uns minuts abans de tornar-ho a "
"provar."

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_media__media_type
msgid "Media Type"
msgstr "Tipus de contingut multimèdia"

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_user_mentions
msgid "Mentions"
msgstr "Mencions"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__name
msgid "Name"
msgstr "Nom"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Oops! Couldn't find this tweet on Twitter.com"
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Please select a Twitter account for this stream type."
msgstr ""
"Seleccioneu un compte de Twitter per a aquest tipus de transmissió en viu."

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Post"
msgstr "Publicar"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "Post Image"
msgstr "Publica la imatge"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Quote a Tweet"
msgstr "Cita una piulada"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_link
msgid "Quoted tweet author Link"
msgstr "Enllaç a l'autor del tweet citat"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_name
msgid "Quoted tweet author Name"
msgstr "Nom de l'autor del tweet citat"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_message
msgid "Quoted tweet message"
msgstr "Missatge de Twitter citat"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_profile_image_url
msgid "Quoted tweet profile image URL"
msgstr "URL de la imatge del perfil de tweet citat"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "RT"
msgstr "RT"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweet_count
msgid "Re-tweets"
msgstr "Re-tweets"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Replies from Tweets older than 7 days must be accessed on Twitter.com"
msgstr ""

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet"
msgstr "Retweet"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet or Quote"
msgstr "Retoca o cita"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Retweets"
msgstr "Retweets"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_searched_keyword
msgid "Search Keyword"
msgstr "Buscar paraula clau"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_search
msgid "Search User"
msgstr "Cerca a l'usuari"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_searched_by_id
msgid "Searched by"
msgstr "Cercat per"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_account
msgid "Social Account"
msgstr "Compte social"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_live_post
msgid "Social Live Post"
msgstr "Publicació social en directe"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_media
msgid "Social Media"
msgstr "Mitjans de comunicació"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post
msgid "Social Post"
msgstr "Correu social"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicació social"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream
msgid "Social Stream"
msgstr "Flux social"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream_post
msgid "Social Stream Post"
msgstr "Publicació de la transmissió en viu social"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_twitter_account
msgid "Social Twitter Account"
msgstr "Compte social de Twitter"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"The keyword you've typed in does not look valid. Please try again with other"
" words."
msgstr ""
"La paraula clau que heu escrit no sembla vàlida. Torneu-ho a provar amb "
"altres paraules."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"L'URL sol·licitat per aquest servei ha retornat un error. Contacteu amb "
"l'autor de l'aplicació."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "This Tweet has been deleted."
msgstr ""

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_follow
msgid "Tweets of"
msgstr "Tweets de"

#. module: social_twitter
#: model:ir.model.fields.selection,name:social_twitter.selection__social_media__media_type__twitter
#: model:social.media,name:social_twitter.social_media_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_author_id
msgid "Twitter Author ID"
msgstr "ID de l'autor de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_can_retweet
msgid "Twitter Can Retweet"
msgstr "El Twitter pot retuitejar"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_comments_count
#, python-format
msgid "Twitter Comments"
msgstr "Comentaris de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_key
msgid "Twitter Consumer Key"
msgstr "Clau de consumidor de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_secret_key
msgid "Twitter Consumer Secret Key"
msgstr "Clau secreta del consumidor de Twitter"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Twitter Developer Account"
msgstr "Compte de desenvolupador de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_id
msgid "Twitter Followed Account"
msgstr "Compte seguit del Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_id
msgid "Twitter ID"
msgstr "Identificador de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_likes_count
msgid "Twitter Likes"
msgstr "A Twitter li agrada"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token
msgid "Twitter OAuth Token"
msgstr "Twitter OAuth Token"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token_secret
msgid "Twitter OAuth Token Secret"
msgstr "Twitter OAuth Token Secret"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_post_limit_message
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_post_limit_message
msgid "Twitter Post Limit Message"
msgstr "Límit de missatges de publicació de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_preview
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_preview
msgid "Twitter Preview"
msgstr "Vista prèvia de Twitter"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Twitter Profile Image"
msgstr "Imatge del perfil de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_profile_image_url
msgid "Twitter Profile Image URL"
msgstr "URL de la imatge del perfil de Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_id_str
msgid "Twitter Quoted Tweet ID"
msgstr "ID de Twitter citat"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweeted_tweet_id_str
msgid "Twitter Retweet ID"
msgstr "ID del retweet del Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_screen_name
msgid "Twitter Screen Name"
msgstr "Nom de la pantalla del Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_tweet_id
msgid "Twitter Tweet ID"
msgstr "Identificador del Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_user_id
msgid "Twitter User ID"
msgstr "ID de l'usuari del Twitter"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_user_likes
msgid "Twitter User Likes"
msgstr "Gustos dels usuaris de Twitter"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token or it may have expired."
msgstr ""
"Twitter no ha proporcionat un token d'accés vàlid o pot haver caducat."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token."
msgstr "Twitter no ha proporcionat un token d'accés vàlid."

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_live_post__twitter_tweet_id
msgid "Twitter tweet id"
msgstr "ID del tuit en Twitter"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "No autoritzat. Si us plau, contacteu amb l'administrador."

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Undo Retweet"
msgstr "Desfés el retoc"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Unknown"
msgstr "Desconegut"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Unknown error"
msgstr "Error desconegut"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_use_own_account
msgid "Use your own Twitter Account"
msgstr "Utilitza el teu propi compte de Twitter"

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Utilitzat per fer comparacions quan necessitem restringir algunes "
"característiques a un contingut multimèdia específic ('facebook', 'twitter',"
" ...)."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_account.py:0
#, python-format
msgid ""
"We could not upload your image, it may be corrupted, it may exceed size "
"limit or API may have send improper response (error: %s)."
msgstr ""

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "You are not authenticated"
msgstr "No esteu autenticat"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comments.js:0
#, python-format
msgid ""
"You can comment only three times a tweet as it may be considered as spamming"
" by Twitter"
msgstr ""
"Podeu comentar només tres vegades un tuit ja que es pot considerar brossa "
"per Twitter"

#. module: social_twitter
#: model:ir.model.constraint,message:social_twitter.constraint_social_stream_post_tweet_uniq
msgid "You can not store two times the same tweet on the same stream!"
msgstr "No podeu emmagatzemar dues vegades el mateix tuit al mateix flux!"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"You cannot create a Stream from this Twitter account.\n"
"It may be because it's protected. To solve this, please make sure you follow it before trying again."
msgstr ""
"No podeu crear un flux des d'aquest compte de Twitter.\n"
"Pot ser perquè està protegit. Per resoldre això, si us plau, asseguri's de seguir-ho abans de tornar-ho a provar."

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "No tens una subscripció activa. Si us plau, compri un aquí:%s"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"You need to add the following callback URL to your twitter application "
"settings: %s"
msgstr ""
"Heu d'afegir aquest URL del callback als paràmetres de l'aplicació Twitter: "
"%s"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_view_form
msgid "e.g. #odoo"
msgstr "p. ex. odoo"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comment.js:0
#, python-format
msgid "tweet"
msgstr "tweet"
