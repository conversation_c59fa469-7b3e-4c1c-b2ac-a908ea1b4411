# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_rs_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-01 08:51+0000\n"
"PO-Revision-Date: 2022-12-01 08:51+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_1
msgid "1. Debts convertible into equity"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_1
msgid "1. Domestic trade receivables - parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_1
msgid "1. Financial expenses incurred with parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_1
msgid "1. Financial income from parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_1
msgid "1. Forest and plantations"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_1
msgid "1. Goods sold to domestic parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_1
msgid "1. Investment in development"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_1
msgid "1. Land"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_1_prev_year
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_1_rs_0418_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_1_rs_0422_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_1
msgid "1. Loss from previous years"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_1
msgid "1. Materials, spare parts, tools and small inventory"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_1
msgid "1. Participation in equity of subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_1
msgid ""
"1. Products sold and services provided to domestic parent companies and "
"subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_1
msgid "1. Provisions for costs incurred during the warranty period"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_1
msgid "1. Receivables from parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1_balance_account_codes
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1_prev_year
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1_rs_0418_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1_rs_0422_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_1
msgid "1. Retained earnings from previous years"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_1
msgid "1. Share capital"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_1
msgid ""
"1. Short-term loans and investments in parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_1
msgid "1. Short-term loans from parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_1
msgid "1. Trade payables - domestic parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_10_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_10
msgid "10. Provisions for long-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_2
msgid "2. Buildings"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_2
msgid ""
"2. Concessions, patents, licenses, trademarks, service marks, software and "
"similar rights"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_2
msgid "2. Financial expenses incurred with other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_2
msgid "2. Financial income from other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_2
msgid "2. Foreign trade receivables - parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_2
msgid "2. Goods sold to foreign parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_2
msgid "2. Liabilities to parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_2
msgid "2. Livestock"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_2_1064
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_2_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_2_rs_0419_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_2_rs_0423_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_2
msgid "2. Loss for the current year"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_2
msgid "2. Participation in equity of associates and joint ventures"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_2
msgid ""
"2. Products sold and services provided to foreign parent companies and "
"subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_2
msgid "2. Provisions for the recovery of natural resources"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_2
msgid "2. Receivables from other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_2_1064
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_2_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_2_rs_0419_1_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_2_rs_0423_1_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_2
msgid "2. Retained earnings for the current year"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_2
msgid "2. Short-term loans and investments in other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_2
msgid "2. Short-term loans from other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_2
msgid "2. Stakes in limited liability companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_2
msgid "2. Trade payables - foreign parent company and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_2_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_2
msgid "2. Work and services in progress"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_3
msgid "3. Biological resources in preparation"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_3
msgid "3. Domestic trade receivables - other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_3
msgid "3. Finished products"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_3
msgid "3. Goods sold to other associated companies on the domestic market"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_3
msgid "3. Goodwill"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_3
msgid "3. Liabilities to other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_3
msgid "3. Participating interests"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_3
msgid ""
"3. Participation in equity in other legal entities and other securities for "
"sale"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_3
msgid "3. Plant and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_3
msgid ""
"3. Products sold and services provided to other associated companies on the "
"domestic market"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_3
msgid "3. Provisions for restructuring costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_3
msgid "3. Receivables from credit sales"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_3
msgid "3. Share in losses of associated companies and joint ventures"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_3
msgid "3. Share in the profits of associated companies and joint ventures"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_3_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_3
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_3
msgid "3. Short-term loans - domestic"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_3_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_3
msgid "3. Trade payables - other domestic associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_4
msgid "4. Advances for biological resources"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_4
msgid "4. Foreign trade receivables - other associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_4
msgid "4. Goods"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_4
msgid "4. Goods sold to other associated companies on the foreign market"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_4
msgid "4. Investment immovables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_4
msgid "4. Liabilities for long-term securities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_4
msgid "4. Long-term investments in parent companies and subsidiaries"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_4
msgid "4. Other financial expenses"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_4
msgid "4. Other financial income"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_4
msgid "4. Other intangible assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_4
msgid ""
"4. Products sold and services provided to other associated companies on the "
"foreign market"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_4
msgid "4. Provisions for compensations and other employment benefits"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_4
msgid "4. Receivables from sales made under financial leasing contracts"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_4_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_4
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_4
msgid "4. Short-term loans - foreign"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_4
msgid "4. State owned capital"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_4_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_4
msgid "4. Trade payables - other foreign associated companies"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_5
msgid "5. Goods sold to domestic customers"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_5
msgid "5. In-process intangible assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_5
msgid ""
"5. Liabilities for permanent assets and assets of discontinued operations "
"held for sale"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_5
msgid "5. Long-term investments in other associated legal entities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_5
msgid "5. Long-term loans - domestic"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_5
msgid "5. Other immovables, plant and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_5
msgid "5. Other short-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_5
msgid "5. Permanent assets held for sale"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_5
msgid "5. Products sold and services provided to domestic customers"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_5
msgid "5. Provisions for litigation expenses"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_5
msgid "5. Provisions of natural assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_5
msgid "5. Receivables on sureties"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_5
msgid "5. Socially owned capital"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_5
msgid "5. Trade payables - domestic"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_5_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_5
msgid "5. Trade receivables - domestic"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_6
msgid "6. Advances for intangible assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_6
msgid "6. Advances paid for inventories and services"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_6
msgid "6. Contested and doubtful receivables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_6
msgid "6. Goods sold to foreign customers"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_6
msgid "6. Immovables, plant and equipment under construction"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_6
msgid "6. Long-term investments - domestic"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_6
msgid "6. Long-term loans - foreign"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_6
msgid "6. Other long-term provisions"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_6
msgid "6. Other short-term financial liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_6
msgid "6. Products sold and services provided to foreign customers"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_6
msgid "6. Provisions for short–term investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_6
msgid "6. Stakes in cooperatives"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_6
msgid "6. Trade payables - foreign"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_6_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_6
msgid "6. Trade receivables - foreign"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_7
msgid "7. Financial leasing liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_7
msgid "7. Investments in third-party immovables, plant and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_7
msgid "7. Long-term investments - foreign"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_7
msgid "7. Other long-term receivables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_7
msgid "7. Other operating liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_7
msgid "7. Other receivables from sales"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_7
msgid "7. Provisions for acquisition of intangible assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_7_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_7
msgid "7. Share premium"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_8
msgid "8. Advances for immovables, plant and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_8
msgid "8. Other capital"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_8
msgid "8. Other long-term liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_8
msgid "8. Provisions for long-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_8
msgid "8. Provisions for trade receivables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_8_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_8
msgid "8. Securities held to maturity"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_9_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_9
msgid "9. Other long-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_9_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_9
msgid "9. Provisions for property, plant and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A
msgid "A. Equity (I + II - III + IV + V + VI - VII + VIII + IX - X)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_off_balance_A_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_off_balance_A
msgid "A. Off-balance sheet assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A
msgid "A. Operating income (I + II + III + IV)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_A_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_A
msgid "A. Subscribed capital unpaid"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets
msgid "Assets (A + B + C + D)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B
msgid "B. Long-term provisions and liabilities (I + II)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_off_balance_B_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_off_balance_B
msgid "B. Off-balance sheet liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B
msgid ""
"B. Operating expenses (I - II - III + IV + V + VI + VII + VIII + IX + X + "
"XI)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B
msgid "B. Permanent assets (I + II + III + IV + V)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.column,name:l10n_rs_reports.account_financial_report_rs_BS_column
#: model:account.report.column,name:l10n_rs_reports.account_financial_report_rs_PnL_column
msgid "Balance"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report,name:l10n_rs_reports.account_financial_report_rs_BS
msgid "Balance Sheet"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_C_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_C
msgid "C. Deferred tax assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_C_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_C
msgid "C. Deferred tax liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C
msgid "C. Financial income (I + II + III)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D
msgid "D. Current assets (I + II + III + IV + V + VI + VII + VIII + IX)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D
msgid "D. Financial expenses (I + II + III)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D
msgid "D. Short-term liabilities (I + II + III + IV + V + VI + VII)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_E_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_E
msgid ""
"E. Income on value adjustment of other assets carried at fair value through "
"profit and loss account"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_E_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_E
msgid ""
"E. Loss above equity (A.III + A.VII + A.X - A.IX - A.VIII - A.VI - A.V - "
"A.IV - A.II - A.I)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities
msgid "Equity and liabilities (A + B + C + D - E)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_F_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_F
msgid ""
"F. Expenses on value adjustment of other assets carried at fair value "
"through profit and loss account"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_G_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_G
msgid "G. Other income"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_H_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_H
msgid "H. Other expenses"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_I
msgid "I. Capital"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_I
msgid "I. Cost of goods sold"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_I
msgid ""
"I. Financial expenses incurred with associated companies and other financial"
" expenses (1042 + 1043 + 1044 + 1045)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_I
msgid ""
"I. Financial income from associated companies and other financial income "
"(1034 + 1035 + 1036 + 1037)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_I
msgid "I. Income from goods sold"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_I
msgid "I. Intangible assets"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_I
msgid "I. Inventories"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_I
msgid "I. Long-term provisions"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_I_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_I_balance_unbound
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_I
msgid ""
"I. Net profit from discontinued operations, effects of changes in accounting"
" policies and corrections of errors from previous periods"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_I
msgid "I. Short-term financial liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_I_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_I
msgid "I. Tax expenses for the period"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_II
msgid "II. Deferred tax expenses of a period"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_II
msgid "II. Immovables, plants and equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_II
msgid "II. Income from interest (from third parties)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_II
msgid "II. Income from products sold and services provided"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_II
msgid "II. Interest expenses (to third parties)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_B_II
msgid "II. Long-term liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_II
msgid "II. Prepayments, deposits and guarantees"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_II
msgid "II. Receivables from sales"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_II
msgid "II. Revenue from undertaking for own purposes"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_II_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_II
msgid "II. Subscribed capital unpaid"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_III
msgid "III. Biological resources"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_III
msgid "III. Deferred tax income of a period"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_III
msgid "III. Income from premiums, subsidies, grants, donations and similar"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_III
msgid ""
"III. Increase in inventories of work in progress and finished products and "
"unfinished services"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_D_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_D_III
msgid ""
"III. Negative effects on exchange rate and effects of foreign currency "
"clause (to third parties)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_III
msgid "III. Operating liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_C_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_C_III
msgid ""
"III. Positive effects on exchange rate and effects of foreign currency "
"clause (to third parties)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_III
msgid "III. Receivables from specific business operations"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_III_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_III
msgid "III. Treasury shares"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_IV
msgid ""
"IV. Decrease in inventories of work in progress and finished products and "
"unfinished services"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_IV
msgid "IV. Long-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_A_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_A_IV
msgid "IV. Other operating income"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_IV
msgid "IV. Other receivables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_IV
msgid "IV. Other short-term liabilities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_IV
msgid "IV. Personal indemnities paid to employer"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_IV_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_IV
msgid "IV. Reserves"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_IX_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_IX
msgid "IX. Accrued expenses"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_IX_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_IX
msgid "IX. Depreciation costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_IX_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_IX
msgid "IX. Participation without control rights"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_J_balance
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_J_balance_unbound
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_J
msgid ""
"J. Net loss from discontinued operations, effects of changes in accounting "
"policies and corrections of errors from previous periods"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_off_balance
msgid "Off-balance items"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_operating_result_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_operating_result
msgid "Operating profit/loss (A - B)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_P_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_P
msgid "P. Net profit/loss (profit before tax - tax on profit)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report,name:l10n_rs_reports.account_financial_report_rs_PnL
#: model:ir.actions.client,name:l10n_rs_reports.action_account_report_rs_PnL
msgid "Profit and Loss"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_results_before_tax_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_results_before_tax
msgid ""
"Profit before tax (Operating + Financing + Other regular and Irregular "
"operations)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_financial_result_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_financial_result
msgid "Profit from financing (C - D)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_other_result_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_other_result
msgid ""
"Profit from other regular operations and irregular operations (E - F + G - H"
" + I - J)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_tax_profit
msgid "Tax on profit (I + II - III + IV)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_V_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_V
msgid "V. Financial assets at fair value through profit and loss account"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_V_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_V
msgid "V. Liabilities for value added tax"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_B_V
msgid "V. Long-term financial receivables"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_V_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_V
msgid "V. Raw material costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_V_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_V
msgid ""
"V. Revaluation reserves from intangible assets, immovables, plants and "
"equipment"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_VI_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_VI
msgid "VI. Fuel and energy costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_VI_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_VI
msgid "VI. Liabilities for other taxes, contributions and other duties"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VI
msgid "VI. Short-term financial investments"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VI_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VI
msgid ""
"VI. Unrealized profits from securities and other elements of other "
"comprehensive income (credit balance accounts of group 33 except 330)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VII
msgid "VII. Cash and cash equivalents"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_VII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_D_VII
msgid "VII. Deferred expenses"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_VII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_VII
msgid "VII. Salaries, wages and other personal indemnities"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VII
msgid ""
"VII. Unrealized losses from securities and other elements of other "
"comprehensive income (debit balance accounts of group 33 except 330)"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_VIII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_VIII
msgid "VIII. Production services costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_VIII
msgid "VIII. Retained earnings"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VIII_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_assets_D_VIII
msgid "VIII. Value added tax"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_X_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_X
msgid "X. Long-term provision costs"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_BS_equity_liabilities_A_X
msgid "X. Loss"
msgstr ""

#. module: l10n_rs_reports
#: model:account.report.expression,report_line_name:l10n_rs_reports.account_financial_report_rs_PnL_B_XI_balance
#: model:account.report.line,name:l10n_rs_reports.account_financial_report_rs_PnL_B_XI
msgid "XI. Intangible costs"
msgstr ""
