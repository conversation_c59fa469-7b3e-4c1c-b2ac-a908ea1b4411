<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="portal_my_home_menu_sign" name="Portal layout : sign menu entries" inherit_id="portal.portal_breadcrumbs" priority="60">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'signatures' or my_sign_item" t-attf-class="breadcrumb-item #{'active ' if not sign_requests else ''}">
                <a t-if="my_sign_item" t-attf-href="/my/signatures?{{ keep_query() }}">Signatures</a>
                <t t-else="">Signatures</t>
            </li>
            <li t-if="my_sign_item" class="breadcrumb-item active">
                <span t-field="my_sign_item.reference"/>
            </li>
        </xpath>
    </template>

    <template id="portal_my_home_sign" name="Show Signatures" customize_show="True" inherit_id="portal.portal_my_home" priority="60">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="title">Signatures</t>
                <t t-set="url" t-value="'/my/signatures'"/>
                <t t-set="placeholder_count" t-value="'sign_count'"/>
            </t>
        </xpath>
    </template>

    <template id="sign_portal_my_requests" name="My Signatures">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>
            <t t-call="portal.portal_searchbar">
                <t t-set="title">Signatures</t>
            </t>
                <div t-if="not grouped_signatures" class="alert alert-warning mt8" role="alert">
                    There are no signatures request.
                </div>
            <t t-if="grouped_signatures" t-call="portal.portal_table">
                <thead>
                    <tr>
                        <th>Document</th>
                        <th class="text-center">Signature Date</th>
                        <th class="text-end">Status</th>
                    </tr>
                </thead>
                <t t-foreach="grouped_signatures" t-as="signature_requests">
                    <tbody>
                        <tr t-if="not groupby =='none'" class="table-light">
                            <th t-if="groupby != 'none'" colspan="3">
                                <span t-field="signature_requests[0].sudo().state"/>
                            </th>
                        </tr>
                    </tbody>
                    <tbody>
                        <tr t-foreach="signature_requests" t-as="sign_request_item">
                            <td>
                                <a t-attf-href="/my/signature/#{sign_request_item.id}?{{ keep_query() }}">
                                    <t t-esc="sign_request_item.reference"/>
                                </a>
                            </td>
                            <td class="text-center">
                                <span t-field="sign_request_item.signing_date"/>
                            </td>
                            <td class="text-end">
                                <span class="badge rounded-pill text-bg-info"
                                      t-field="sign_request_item.sudo().sign_request_id.state"/>
                            </td>
                        </tr>
                    </tbody>
                </t>
            </t>
        </t>
    </template>
    <template id="sign_portal_my_request" name="My Signature">
        <t t-call="portal.portal_layout">
            <t t-set="o_portal_fullwidth_alert" groups="sign.access_sign_request_item_all">
                <t t-call="portal.portal_back_in_edit_mode">
                    <t t-set="backend_url" t-value="'/web#model=sign.request&amp;id=%s&amp;view_type=form' % (sign.request.id)"/>
                </t>
            </t>
            <t t-call="portal.portal_record_layout">
                <t t-set="card_header">
                    <div class="row g-0">
                        <div class="col-md">
                            <h5 class="mb-1 mb-md-0">
                                <span t-field="my_sign_item.reference"/>
                            </h5>
                        </div>
                        <div class="col-md text-md-end">
                            <span t-field="my_sign_item.sign_request_id.state" class=" badge rounded-pill text-bg-info"
                                  title="Current status of the signature request"/>
                        </div>
                    </div>
                </t>
                <t t-set="card_body">
                    <div class="o_sign_portal">
                        <div class="o_sign_button mb-4 text-center align-items-center">
                            <a t-att-href="url" class="btn btn-primary btn-block">
                                <div class="o_sign_button_content" t-if="my_sign_item.state == 'completed'">View Document</div>
                                <div class="o_sign_button_content" t-else="">Sign</div>
                            </a>
                        </div>
                        <div class="row mb-4" t-if="my_sign_item.partner_id">
                            <div class="col-12 col-md-6 pb-2">
                                <span class="o_portal_category">Summary</span>
                                <div>
                                    <strong>Creation Date:</strong>
                                    <span t-field="my_sign_item.create_date" t-options="{'widget': 'date'}"/>
                                </div>
                                <div>
                                    <t t-foreach="my_sign_item.sign_request_id.request_item_ids" t-as="sign">
                                        <div t-if="sign.state == 'sent'" class="_status clearfix" t-att-data-id="sign.id">
                                            <b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b>
                                            <t t-if="sign.role_id"><t t-esc="' - ' + sign.role_id.name"/></t> - <em>Waiting Signature</em>
                                            <em t-if="not sign.is_mail_sent"><br/>(the email access has not been sent)</em>
                                        </div>
                                    </t>
                                    <t t-foreach="my_sign_item.sign_request_id.request_item_ids" t-as="sign">
                                        <div t-if="sign.state == 'canceled'" class="_status clearfix" t-att-data-id="sign.id">
                                           <b> <t t-esc="sign.partner_id.name"/></b><t t-if="sign.role_id">
                                            <t t-esc="' - ' + sign.role_id.name"/></t> - <em>Canceled</em>
                                        </div>
                                    </t>
                                    <t t-foreach="my_sign_item.sign_request_id.request_item_ids" t-as="sign">
                                        <div t-if="sign.state == 'refused'" class="_status clearfix" t-att-data-id="sign.id">
                                           <b> <t t-esc="sign.partner_id.name"/></b><t t-if="sign.role_id">
                                            <t t-esc="' - ' + sign.role_id.name"/></t> - <em>Refused on <span t-field="sign.signing_date"></span></em>
                                        </div>
                                    </t>
                                    <t t-foreach="my_sign_item.sign_request_id.request_item_ids" t-as="sign">
                                        <div t-if="sign.state == 'completed'" class="_status clearfix"><b>
                                            <t t-esc="sign.partner_id.name"/></b><t t-if="sign.role_id">
                                            <t t-esc="' - ' + sign.role_id.name"/></t> - <em>Signed on <span t-field="sign.signing_date"></span></em>
                                        </div>
                                    </t>
                                </div>
                            </div>
                            <div class="coll-12 col-md-6 pb-2" t-if="my_sign_item.partner_id">
                                <span class="o_portal_category">Your Information</span>
                                <div class="row">
                                    <div class="col flex-grow-0 pe-3">
                                        <img class="rounded-circle mt-1 o_portal_contact_img"
                                             t-att-src="image_data_uri(my_sign_item.partner_id.avatar_1024)" alt="Contact"/>
                                    </div>
                                    <div class="col ps-md-0">
                                        <div t-field="my_sign_item.partner_id"
                                             t-options="{'widget': 'contact', 'fields': ['name', 'email', 'phone']}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
            <div class="mt32">
                <h4>
                    <strong>Message and communication history</strong>
                </h4>
                <t t-call="portal.message_thread">
                    <t t-set="object" t-value="my_sign_item.sign_request_id"/>
                    <t t-set="token" t-value="my_sign_item.sign_request_id.access_token"/>
                    <t t-set="pid" t-value="pid"/>
                    <t t-set="hash" t-value="hash"/>
                </t>
            </div>
        </t>
    </template>

</odoo>
