<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">

    <!-- User input 1 -->
    <!-- page 1 -->
    <record id="survey_vendor_certification_answer_1_p1_q1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_1_choice_2"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q2_1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_1"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q2_2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_3"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q2_3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_4"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q3_1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_1"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q3_2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_3"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q3_3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_4"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q4" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_4_choice_2"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p1_q5" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_1_question_5"/>
        <field name="answer_type">text_box</field>
        <field name="value_text_box">I think it misses a product but I don't know what</field>
    </record>
    <!-- page 2 -->
    <record id="survey_vendor_certification_answer_1_p2_q1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_2_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_1_choice_4"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p2_q2_1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_1"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p2_q2_2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_2"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p2_q2_3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_4"/>
    </record>
    <record id="survey_vendor_certification_answer_1_p2_q3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_2_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_3_choice_3"/>
    </record>
    <!-- page 3 -->
    <record id="survey_vendor_certification_answer_1_p3_q1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_1"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">30</field>
    </record>
    <record id="survey_vendor_certification_answer_1_p3_q2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_2"/>
        <field name="answer_type">date</field>
        <field name="value_date">2020-01-08</field>
    </record>
    <record id="survey_vendor_certification_answer_1_p3_q3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_3"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-07 00:00:01</field>
    </record>
    <record id="survey_vendor_certification_answer_1_p3_q4" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_4"/>
        <field name="answer_type">date</field>
        <field name="value_date">2020-01-01</field>
    </record>
    <record id="survey_vendor_certification_answer_1_p3_q5" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_5"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-01 01:00:01</field>
    </record>
    <record id="survey_vendor_certification_answer_1_p3_q6" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_vendor_certification_answer_1"/>
        <field name="question_id" ref="vendor_certification_page_3_question_6"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">1000</field>
    </record>

    <!-- User input 2 -->
    <!-- page 1 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_1_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q2_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_3"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q3_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q3_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_3"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_4_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p1_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_1_question_5"/>
        <field name="skipped" eval="True"/>
    </record>
    <!-- page 2 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p2_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_2_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_1_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p2_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p2_q2_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p2_q2_3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p2_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_2_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_3_choice_4"/>
    </record>
    <!-- page 3 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_1"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">30</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_2"/>
        <field name="answer_type">date</field>
        <field name="value_date">2020-01-09</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_3"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-07 00:00:01</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_4"/>
        <field name="skipped" eval="True"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_5"/>
        <field name="skipped" eval="True"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_2_p3_q6">
        <field name="user_input_id" ref="survey_vendor_certification_answer_2"/>
        <field name="question_id" ref="vendor_certification_page_3_question_6"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">0</field>
    </record>

    <!-- User input 3 -->
    <!-- page 1 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_1_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q2_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q3_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q3_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_4_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p1_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_1_question_5"/>
        <field name="skipped" eval="True"/>
    </record>
    <!-- page 2 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p2_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_2_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_1_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p2_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p2_q2_2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p2_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_2_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_3_choice_2"/>
    </record>
    <!-- page 3 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_1"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">30</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_2"/>
        <field name="answer_type">date</field>
        <field name="value_date">2020-01-08</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_3"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-06 23:59:59</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_4"/>
        <field name="skipped" eval="True"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_5"/>
        <field name="skipped" eval="True"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_3_p3_q6">
        <field name="user_input_id" ref="survey_vendor_certification_answer_3"/>
        <field name="question_id" ref="vendor_certification_page_3_question_6"/>
        <field name="skipped" eval="True"/>
    </record>

    <!-- User input 4 -->
    <!-- page 1 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p1_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_1_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_1_choice_1"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p1_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_1_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_2_choice_3"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p1_q3_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_1_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_3_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p1_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_1_question_4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_1_question_4_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p1_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_1_question_5"/>
        <field name="skipped" eval="True"/>
    </record>
    <!-- page 2 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p2_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_2_question_1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_1_choice_2"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p2_q2_1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_2_question_2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_2_choice_4"/>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p2_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_2_question_3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="vendor_certification_page_2_question_3_choice_5"/>
    </record>
    <!-- page 3 -->
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q1">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_1"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">2</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q2">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_2"/>
        <field name="answer_type">date</field>
        <field name="value_date">2020-01-08</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q3">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_3"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-07 00:00:01</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q4">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_4"/>
        <field name="answer_type">date</field>
        <field name="value_date">2019-12-31</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q5">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_5"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime">2021-01-01 13:00:01</field>
    </record>
    <record model="survey.user_input.line" id="survey_vendor_certification_answer_4_p3_q6">
        <field name="user_input_id" ref="survey_vendor_certification_answer_4"/>
        <field name="question_id" ref="vendor_certification_page_3_question_6"/>
        <field name="skipped" eval="True"/>
    </record>

</data></odoo>
