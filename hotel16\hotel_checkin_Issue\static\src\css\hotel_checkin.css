/* Hotel Check-in Custom Styles */

.o_kanban_view.o_hotel_checkin_kanban {
    background-color: #1abc9c; /* Teal background color */
    padding: 16px;
}

.o_hotel_checkin_kanban .o_kanban_record {
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0;
    overflow: hidden;
    margin-bottom: 16px;
}

.o_hotel_checkin_kanban .villa_header {
    padding: 12px 16px;
    font-weight: bold;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.o_hotel_checkin_kanban .villa_content {
    padding: 12px 16px;
}

.o_hotel_checkin_kanban .villa_occupied {
    background-color: #ffcdd2; /* Light red for occupied */
    border: 1px solid #e57373;
}

.o_hotel_checkin_kanban .villa_available {
    background-color: #dcedc8; /* Light green for available */
    border: 1px solid #aed581;
}

.o_hotel_checkin_kanban .status_indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 8px;
}

.o_hotel_checkin_kanban .status_occupied {
    background-color: #f44336; /* Red */
}

.o_hotel_checkin_kanban .status_available {
    background-color: #4caf50; /* Green */
}

.o_hotel_checkin_kanban .guest_name {
    font-weight: bold;
    margin-bottom: 8px;
}

.o_hotel_checkin_kanban .price_tag {
    color: #ff9800;
    font-weight: bold;
}

.o_hotel_checkin_kanban .detail_row {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.o_hotel_checkin_kanban .detail_row i {
    margin-right: 8px;
    color: #757575;
}

.o_hotel_checkin_kanban .premium_tag {
    background-color: #ffd54f;
    color: #795548;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-block;
    margin-top: 6px;
}

/* Header styling */
.o_hotel_checkin_header {
    background-color: #16a085;
    color: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.o_hotel_checkin_header h2 {
    margin: 0;
    font-size: 24px;
}

.o_hotel_checkin_search {
    margin-top: 12px;
    position: relative;
}

.o_hotel_checkin_search input {
    width: 100%;
    padding: 8px 12px;
    border-radius: 20px;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.o_hotel_checkin_search i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

/* Hotel Check-in CSS */
.hotel-checkin-dashboard {
    padding: 20px;
}

.hotel-checkin-card {
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.hotel-checkin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hotel-checkin-card .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.hotel-checkin-card .status-confirmed {
    background-color: #28a745;
}

.hotel-checkin-card .status-done {
    background-color: #17a2b8;
}

.hotel-checkin-card .status-cancel {
    background-color: #dc3545;
}

.hotel-checkin-card .status-draft {
    background-color: #6c757d;
}

.hotel-checkin-card .card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

.hotel-checkin-card .btn-add-service {
    background-color: #17a2b8;
    color: white;
}

.hotel-checkin-card .btn-view-folio {
    background-color: #6c757d;
    color: white;
}

/* Service wizard styling */
.hotel-service-wizard {
    max-width: 500px;
    margin: 0 auto;
}

.hotel-service-wizard .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.hotel-service-wizard .form-group {
    margin-bottom: 1rem;
}

.hotel-service-wizard .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.hotel-service-wizard .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
} 