API USER MANUAL

BioTime 8.5

Version:	1.0
Date:	July 2021

Contents

Get System User Auth Token	4
Get JWT Auth Token	4
Get General auth token	5
Get Staff Auth Token	7
Staff JWT auth token	7
Get staff General Auth Token	7
Use Auth Token	7
Demo 1	7
Demo 2 (with Third Party Tool - Postman)	8
Device API	8
Get Device List API	8
Get Device Object Info API	10
Employee API	11
Get Employee List API	11
Get Employee Object Info API	14
Create Employee API	15
Update Employee API	18
Delete Employee API	19
Department API	20
Get Department List API	20
Get Department Object Info API	21
Create Department API	21
Update Department API	22
Delete Department API	23
Area API	23
Get Area List API	23
Get Area Object Info API	24
Create Area API	24
Update Area API	25
Delete Area API	26
Position API	26
Get Position List API	26
Get Position Object Info API	27
Create Position API	28
Update Position API	28
Delete Position API	29
Transaction API	30
Get Transaction List API	30

API Description
BioTime 8.5 API is dedicated to the platform data connection for the third-party system. The third-party system can read and set business data flexibly, which effectively reduces the complexity of the third-party system business integration and provides convenient, fast standard connection mode and data structure.

Get System User Auth Token
There are two kinds of auth token for software system user: JWT auth token and general auth token.
Get JWT Auth Token
Request Instruction

HTTPS Method
POST
URI
/jwt-api-token-auth/
Content Type
application/json
Response Type
JSON
Parameter Description
username: string, is required, the username to login the system. password: string, is required, the password to login the system.

Request Herders
{
"Content-Type":"application/json"
}


Request Body
{
"username":"username", "password":"pwd"
}


How to Get Token
Get token via browser, input the following link in the browser: http://serverIP:serverPort/api/docs/#jwt-api-token-auth
serverIP:	BioTime 8.5 server or computer IP, such as: *************
serverPort:  The server port of BioTime 8.5, such as: 8090




Get token via third-party, such as Postman.




Get General auth token
Request Instruction
Same as getting JWT Token

How to Get Token
Get token via browser, input the following link in the browser: http://serverIP:serverPort/api/docs/#api-token-auth
serverIP:	BioTime 8.5 server or computer IP, such as: *************
serverPort:  The server port of BioTime 8.5, such as: 8090

Get token via third-party API development tool, such as Postman.



Get Staff Auth Token
Staff JWT auth token
URL: http://serverIP:serverPort/staff-jwt-api-token-auth/ serverIP:	BioTime 8.5 server or computer IP serverPort: The server port of BioTime 8.5
The way to get token is the same as JWT token above.


Get staff General Auth Token
URL: http://serverIP:serverPort/staff-api-token-auth/ serverIP:	BioTime 8.5 server or computer IP serverPort: The server port of BioTime 8.5
The way to get token is the same as JWT token above.



Use Auth Token
Demo 1
Request URL
http://serverIP:serverPort/iclock/api/terminals/






Request Herders
JWT Token
{
"Content-Type":"application/json", "Authorization":"JWT ey	oQi98"
}
General Token
{
"Content-Type":"application/json", "Authorization":"Token
ae600ca0f1d0aeed8af3f93c8530a69c714752b7"
}











Response Content
{
"count": 0,
"next": null, "previous": null, "msg": "", "code": 0, "results": [],
"data": []
}


Demo 2 (with Third Party Tool - Postman)
(1) Use General Token (same way to use JWT token)


Device API
Get Device List API

HTTP Method
GET
URI
/iclock/api/terminals/
Data Type
application/json




Response Type
JSON


Request Address
http://serverIP:serverPort/iclock/api/terminals/?sn=**&page=**&limit=**&ali as=**&area=**
sn, page, page_size, alias , area are optional filter field, use & as connector
While the request address without any filter field, you will get all devices list.


















Response JSON Format
Example:
Request Address: http://*************:8090/iclock/api/terminals/?sn=ACEZ185060382 Response:
{
"count": 1,
"next": null, "previous": null, "msg": "", "code": 0,
"data": [
{
"id": 1,
"sn": "ACEZ185060382",
"ip_address": "127.0.0.1",
"alias": "ACEZ185060382_name", "terminal_name": null,
"fw_ver": null, "push_ver": "", "state": 1,
"terminal_tz": 8, "area": {
"id": 1,
"area_code": "1",
"area_name": "Not Authorized"
},






"last_activity": null, "user_count": null, "fp_count": null, "face_count": null, "palm_count": null, "transaction_count": null, "push_time": null, "transfer_time": "00:00;14:05",
"transfer_interval": 10, "is_attendance": true, "area_name": "Not Authorized"
}
]
}

4.2 Get Device Object Info

API
HTTP Method
GET
URI
/iclock/api/terminals/ <id>/
Data Type
application/json
Response Type
JSON
Request Address
http://serverIP:serverPort/iclock/api/terminals/device id/





Response JSON Format
Example:
Request Address: http://*************:8090/iclock/api/terminals/1/ Response:
{
"id": 1,
"sn": "ACEZ185060382",
"ip_address": "127.0.0.1",
"alias": "ACEZ185060382_name",






"terminal_name": null, "fw_ver": null, "push_ver": "",
"state": 1,
"terminal_tz": 8, "area": {
"id": 1,
"area_code": "1",
"area_name": "Not Authorized"
},
"last_activity": null, "user_count": null, "fp_count": null, "face_count": null, "palm_count": null, "transaction_count": null, "push_time": null, "transfer_time": "00:00;14:05",
"transfer_interval": 10, "is_attendance": true, "area_name": "Not Authorized"
}

Employee API
Get Employee List API


HTTP Method
GET
URI
/personnel/api/employees/
Data Type
application/json
Response Type
JSON






Request Address
http://serverIP:serverPort/personnel/api/employee/?emp_code=**&page=** &first_name=**&last_name=**&department=**&app_status=**

page, page_size, emp_code, first_name, last_name, department, app_status are optional filter fields.




















Response JSON Format
Example:
Request Address: http://*************:8090/personnel/api/employee/?emp_code=employee1 Response:
{
"count": 1,
"next": null, "previous": null, "msg": "", "code": 0,
"data": [
{
"id": 5,
"emp_code": "employee1", "first_name": "emp1_first_name", "last_name": "emp1_last_name", "nickname": "", "device_password": "",
"card_no": "", "department": {
"id": 1,
"dept_code": "1", "dept_name": "Department"
},
"dept_name": "Department", "position": null, "position_name": null, "hire_date": "2019-04-02",



"gender": "", "birthday": null, "verify_mode": null, "emp_type": null, "contact_tel": "",
"office_tel": "",
"mobile": "",
"national": "",
"city": "",
"address": "",
"postcode": "",
"email": "",
"enroll_sn": "",
"ssn": "",
"religion": "", "enable_att": false, "enable_overtime": false, "enable_holiday": false, "dev_privilege": null, "self_password":
"pbkdf2_sha256$36000$XIJACl8JIXWA$a4hztrK8RSMjBgd9sWL3lTMQS3O9M z+QwaGU/RBiXRU=",
"flow_role": [], "area": [
{
"id": 1,
"area_code": "1",
"area_name": "Not Authorized"
}
],
"area_name": "Not Authorized", "app_status": 0,




Get Employee Object Info API

HTTP Method
GET
URI
/personnel/api/employees/ <id>/
Data Type
application/json
Response Type
JSON
Request Address
http://serverIP:serverPort/personnel/api/employees/employee ID/












Response JSON Format
Example:
Request Address: http://*************:8090/personnel/api/employees/6/ Response:
{
"emp_code": "employee333", "first_name": "emp3_first_name", "last_name": "emp3_last_name", "nickname": null, "device_password": null, "card_no": null,
"department": 1, "position": null, "hire_date": "2019-04-02", "gender": null, "birthday": null, "verify_mode": -1, "emp_type": null,
"contact_tel": null,






"office_tel": null, "mobile": null, "national": null, "city": null, "address": null, "postcode": null, "email": null, "enroll_sn": null, "ssn": null, "religion": null, "enable_att": true,
"enable_overtime": false, "enable_holiday": true, "dev_privilege": 1,
"self_password": "pbkdf2_sha256$36000$XBVRNMOywdNI$P4W7MIZVa3Ho2VBdU2SewS/pb8 GavBYSGBXM/bgL+N0=",
"flow_role": [], "area": [
1
],
"app_status": 0,
"app_role": 1
}
5.3 Create Employee API


HTTP Method
POST
URI
/personnel/api/employees/
Data Type
application/json
Response Type
JSON
Request Parameter
Reference:  http://serverIP:serverPort/api/personnel_docs/#employees-create

























Response JSON Format
Request Address: http://*************:8090/personnel/api/employees/ Request Body:
{
"emp_code": "employee333", "first_name": "emp3_first_name", "last_name": "emp3_last_name", "area": [1],
"department": 1
}
Response Data:
{
"id": 6,
"emp_code": "employee333", "first_name": "emp3_first_name", "last_name": "emp3_last_name", "nickname": null, "device_password": null, "card_no": null,
"department": { "id": 1,
"dept_code": "1", "dept_name": "Department"
},
"dept_name": "Department", "position": null, "position_name": null, "hire_date": "2019-04-02", "gender": null,
"birthday": null,
"verify_mode": -1,



"emp_type": null, "contact_tel": null, "office_tel": null, "mobile": null, "national": null, "city": null, "address": null, "postcode": null, "email": null, "enroll_sn": null, "ssn": null, "religion": null, "enable_att": true,
"enable_overtime": false, "enable_holiday": true, "dev_privilege": 0,
"self_password": "pbkdf2_sha256$36000$XBVRNMOywdNI$P4W7MIZVa3Ho2VBdU2SewS/pb8 GavBYSGBXM/bgL+N0=",
"flow_role": [], "area": [
{
"id": 1,
"area_code": "1",
"area_name": "Not Authorized"
}
],
"area_name": "Not Authorized", "app_status": 0,
"app_role": 1
}

Update Employee API

HTTP Method
PATCH
URI
/personnel/api/employees/ <id>/
Data Type
application/json
Response Type
JSON
Request Parameter
Reference:
http://serverIP:serverPort/api/personnel_docs/#employees-update
















Response JSON Format
Request Address: http://*************:8090/personnel/api/employees/6/ Resquest Body:
{
"first_name": "emp3_first_name_update", "last_name": "emp3_last_name_update",
}
Response Data:
{
"id": 6,
"emp_code": "employee333", "first_name": "emp3_first_name_update", "last_name": "emp3_last_name_update", "nickname": null,
"device_password": null, "card_no": null, "department": 1, "position": null, "hire_date": "2019-04-02", "gender": null, "birthday": null, "verify_mode": -1,
"emp_type": null,






"contact_tel": null, "office_tel": null, "mobile": null, "national": null, "city": null, "address": null, "postcode": null, "email": null, "enroll_sn": null, "ssn": null, "religion": null, "enable_att": true,
"enable_overtime": false, "enable_holiday": true, "dev_privilege": 1,
"self_password": "pbkdf2_sha256$36000$XBVRNMOywdNI$P4W7MIZVa3Ho2VBdU2SewS/pb8 GavBYSGBXM/bgL+N0=",
"flow_role": [], "area": [
1
],
"app_status": 0,
"app_role": 1
}
}

5.5 Delete Employee AP

I
HTTP Method
DELETE
URI
/personnel/api/employees/<id>/
Data Type
application/json

KTECO MIDDLE EAST






Response Type
JSON
Request Address
http://127.0.0.1:81/personnel/api/employees/6/


Department API
Get Department List API

HTTP Method
GET
URI
/personnel/api/departments/
Data Type
application/json
Response Type
JSON

Request Address
http://serverIP:serverPort/personnel/api/departments/?dept_code=**&page
=**&dept_code=**&dept_name=**&parent_dept=**
page, page_size , dept_code, dept_name, parent_dept are optional filter fields.










Response JSON Format
Request address:
http://*************:8090/personnel/api/departments/?dept_code=1

Response JSON format:
{
"count": 1,
"next": null, "previous": null, "msg": "", "code": 0,
"data": [
{
"id": 1,
"dept_code": "1", "dept_name": "Department", "parent_dept": null, "parent_dept_name": null
}
]
}



Get Department Object Info API

HTTP Method
GET
URI
/personnel/api/departments/<id>/
Data Type
application/json
Response Type
JSON





Response JSON Format
Request Address:
http://*************:8090/personnel/api/departments/1/

Request Body:
{
"id": 1,
"dept_code": "1", "dept_name": "Department", "parent_dept": null, "parent_dept_name": null
}



6.3 Create Department AP



I
HTTP Method
POST
URI
/personnel/api/departments/
Data Type
application/json
Response Type
JSON

Request Parameter
Reference: http://serverIP:serverPort/api/personnel_docs/#departments-create



Response JSON Format
Request Address
http://*************:8090/personnel/api/departments/

Request Body
{
"dept_code": "222", "dept_name": "Department222", "parent_dept": 1






Update Department API

HTTP Method
PATCH
URI
/personnel/api/departments/ <id>/
Data Type
application/json
Response Type
JSON

Request Parameter
Reference: http://serverIP:serverPort/api/personnel_docs/#departments-update








Response JSON Format
Request Address
http://*************:8090/personnel/api/departments/2/

Resquest Body
{
"dept_name": "Department222_update"
}

Response Data
{
"id": 2,
"dept_code": "222",
"dept_name": "Department222_update", "parent_dept": 1
}
}



Delete Department API

HTTP Method
DELETE
URI
/personnel/api/departments/<id>/
Data Type
application/json
Response Type
JSON


Response JSON Format
Request Address: http://19.2168.218.8:8090/personnel/api/departments/2/ Resquest Body:
{}

Area API
Get Area List API


HTTP Method
GET
URI
/personnel/api/areas/
Data Type
application/json
Response Type
JSON

Request Address
http://serverIP:serverPort//personnel/api/areas/?area_code=**&page=**&are a_code=**&area_name=**&parent_area=**
page, page_size, area_code, area_name, parent_area are optional filter fields.





Response JSON Format
Request address:
http://*************:8090/personnel/api/areas/?area_code=2/

Response JSON format:
{
"id": 2,
"area_code": "2", "area_name": "Primero", "parent_area": {
"id": 1,
"area_code": "1",
"area_name": "Not Authorized",






Get Area Object Info API



HTTP Method
GET




URI
/personnel/api/areas/<id>/




Data Type
application/json




Response Type
JSON











Response JSON Format
Request address:
http://*************:8090/personnel/api/areas/2/

Response JSON format:
{
"id": 2,
"area_code": "2", "area_name": "Primero", "parent_area": {
"id": 1,
"area_code": "1",
"area_name": "Not Authorized", "parent_area": null
},
"parent_area_name": "Not Authorized"
}







7.3 Create Area API






HTTP Method
POST




URI
/personnel/api/areas/




Data Type
application/json




Response Type
JSON




Z


KTECO MIDDLE EAST








Request Parameter
Reference:  http://serverIP:serverPort/api/personnel_docs/#areas-create


Request Address


http://*************:8090/personnel/api/areas/


Request Body


{


"area_code": "222",


"area_name": "Department222",


"parent_area": 1
Response JSON Format
}


Response Data


{


"id": 2,


"area_code": "222",


"area_name": "Department222",


"parent_area": 1


}

7.4 Update Area API


HTTP Method
PATCH
URI
/personnel/api/areas/ <id>/
Data Type
application/json
Response Type
JSON
Request Parameter
Reference:  http://serverIP:serverPort/api/personnel_docs/#areas-update






Request Address


http://*************:8090/personnel/api/areas/2/


Resquest Body


{


"area_name": "Area222_update"


}
Response JSON Format

Response Data


{


"id": 2,


"area_code": "222",


"area_name": "area222_update",


"parent_area": 1


}


}



7.5 Delete Area API


HTTP Method
DELETE
URI
/personnel/api/areas/<id>/
Data Type
application/json
Response Type
JSON


Response JSON Format
Request Address: http://19.2168.218.8:8090/personnel/api/areas/2/ Resquest Body:
{}


Position API
Get Position List API


HTTP Method
GET






URI
/personnel/api/positions/




Data Type
application/json




Response Type
JSON





Request Address
http://serverIP:serverPort/personnel/api/positions/?position_code=**&page=
**&position_code=**&position_name=**&parent_position=**
page, page_size, position_code, position_name, parent_position are optional filter fields.














Response JSON Format
Request address:
http://*************:8090/personnel/api/positions/?position_code=1

Response JSON format:
{
"count": 1,
"next": null, "previous": null, "msg": "", "code": 0,
"data": [
{
"id": 1,
"position_code": "1", "position_name": "Department", "parent_position": null, "parent_position_name": null
}
]
}





8.2 Get Position Object In

fo API




HTTP Method
GET




URI
/personnel/api/positions/<id>/




Data Type
application/json




Response Type
JSON





Response JSON Format
Request Address:
http://*************:8090/personnel/api/positions/1/

Request Body:



Z

KTECO MIDDLE EAST










{
"id": 1,
"position_code": "1", "position_name": "Position", "parent_position": null, "parent_position_name": null
}



8.3 Create Position API


HTTP Method
POST
URI
/personnel/api/positions/
Data Type
application/json
Response Type
JSON
Request Parameter
Reference:  http://serverIP:serverPort/api/personnel_docs/#positions-create








Response JSON Format
Request Address
http://*************:8090/personnel/api/positions/

Request Body
{
"position_code": "222", "position_name": "Position222", "parent_position": 1
}

Response Data
{
"id": 2,
"position_code": "222", "position_name": "Position222", "parent_position": 1
}


Update Position API


HTTP Method
PATCH




URI
/personnel/api/positions/ <id>/
Data Type
application/json
Response Type
JSON
Request Parameter
Reference:  http://serverIP:serverPort/api/personnel_docs/#positions-update


Request Address


http://*************:8090/personnel/api/positions/2/


Resquest Body


{


"position_name": "Position222_update"


}
Response JSON Format

Response Data


{


"id": 2,


"position_code": "222",


"position_name": "Position222_update",


"parent_position": 1


}


}

8.5 Delete Position API


HTTP Method
DELETE
URI
/personnel/api/positions/<id>/
Data Type
application/json
Response Type
JSON


Response JSON Format
Request Address: http://19.2168.218.8:8090/personnel/api/positions/2/ Resquest Body:
{}






Transaction API
Get Transaction List API

HTTP Method
GET

URI
/iclock/api/transctions/
Data Type
application/json
Response Type
JSON


Request Address
http://serverIP:serverPort/iclock/api/transactions/?emp_code=**&start_time
=**&end_time=**
page , page_size, emp_code, terminal_sn, start_time, end_time are optional filter fields.











Response JSON Format
Request Address:
http://*************:8090/iclock/api/transactions/?emp_code=100001&start
_time=2019-03-01 00:00:00&end_time=2019-04-1 00:00:00

Response JSON format:
{
"count": 7,
"next": null, "previous": null, "msg": "", "code": 0,
"data": [
{
"id": 1,
"emp_code": "100001",
"punch_time": "2019-03-04 09:50:00",
"punch_state": "0",
"verify_type": 1, "work_code": null, "terminal_sn": "", "terminal_alias": null, "area_alias": null, "longitude": null,










For any clarifications regarding the API User Manual, please contact us through e-<NAME_EMAIL>.










ZKTeco Middle East

Office 1207, Floor 112, Arenco Tower, Media City, Sheikh Zayed Road, Dubai, U.A.E. Tel: +971 4 3927649
Fax: +971 4 3792752
E-mail: <EMAIL> www.zkteco.me

## Night Shift Handling

The Biotime module now includes comprehensive support for night shifts, allowing for accurate attendance tracking across midnight boundaries.

### Configuration

In the Biotime Configuration:
- Enable "Handle Night Shifts" to activate night shift processing
- Set "Night Shift Hours" to define the maximum duration of a night shift (e.g., 12 hours)
- Set "Night Shift Span Days" to define how many days a night shift can span (default: 1)

### How It Works

1. **Manual Processing**: When processing a check-out punch, the system checks if it belongs to a night shift by looking for open attendances from the previous day.

2. **Auto-Processing**: During the automatic processing of unknown punches, the system:
   - Groups punches by day
   - Checks consecutive days for potential night shifts
   - Identifies night shifts by analyzing time gaps between the last punch of one day and the first punch of the next day
   - Automatically pairs these punches as check-in/check-out if they fall within the configured night shift duration

3. **Logging**: The system adds detailed logs when night shifts are detected, showing the duration and processing logic.

### Benefits

- Accurate attendance tracking for employees working across midnight
- Elimination of "missing check-out" errors for legitimate night shifts
- Proper calculation of working hours without manual intervention

## Scalability Improvements

The Biotime module has been optimized to handle large deployments with many employees and terminals efficiently.

### Batch Processing

- **Terminal Processing**: Terminals are processed in batches (default: 5 terminals per batch) to avoid timeouts and memory issues.
- **Pagination**: API requests use pagination with configurable page sizes to retrieve data in manageable chunks.
- **Employee Caching**: Employee data is cached during processing to reduce database queries.

### Configuration Options

- **Page Size**: Customize the number of records retrieved per API request via the `page_size` parameter.
- **Batch Size**: Adjust the number of terminals processed in each batch based on server capacity.

### Error Handling

- Improved error handling for API timeouts and connection issues.
- Detailed logging of progress and errors during data synchronization.
- Graceful recovery from partial failures during terminal processing.

### Synchronization Optimization

- **Last Sync Tracking**: The system tracks the last successful sync time to avoid retrieving already processed data.
- **Duplicate Detection**: Checks for existing punch records to prevent duplication.
- **Time-Based Filtering**: API requests include date filters to limit data retrieval to new records only.

These optimizations ensure the module performs efficiently even with hundreds of terminals and thousands of employees.

## Handling Unknown Punch Statuses

The Biotime module includes intelligent processing for punches with unknown status:

### Auto-Assignment Logic

- **Contextual Analysis**: The system intelligently determines if an unknown punch should be treated as a check-in or check-out by examining:
  - The previous punch's status and timestamp
  - The time elapsed between punches
  - Whether the punch occurs in a new day

### Scheduled Processing

- A scheduled task runs periodically to re-process unknown punches.
- Unknown punches are automatically resolved based on surrounding punch data.
- Administrators receive notifications about persistent unknown status punches that require attention.

### Manual Management

- The punch list view includes filters to quickly identify unprocessed and unknown status punches.
- Administrators can manually assign the correct status when automatic assignment is insufficient.

### Benefits

- Reduction in manual data entry and correction
- Improved accuracy of attendance records
- Proactive notification of attendance anomalies
- Robust handling of irregular punch patterns

## Attendance Record Validation

The module includes enhanced validation to ensure data integrity between punches and attendance records:

### Punch Processing Controls

- **Validation Before Processing**: Punches are only marked as "Processed" when:
  - A valid attendance record is successfully created
  - The punch data has been properly paired with a matching check-in/check-out
  - All required conditions for attendance record creation are met

### Preventing False Positives

- The system implements strict controls to prevent punches from being incorrectly marked as processed.
- A punch remains in "Unprocessed" or "Unknown" state until it can be properly matched and verified.
- Automatic system checks ensure that processed punches always have corresponding attendance records.

### Audit Trail

- Each punch maintains a link to its corresponding attendance record when processed.
- Administrators can trace the relationship between punches and attendance records.
- The system logs processing attempts to help troubleshoot any attendance record creation issues.

This validation ensures that attendance data remains accurate and that reporting based on processed punches correctly reflects actual attendance.

## Data Synchronization Optimizations

The Biotime module includes several optimizations to ensure efficient data retrieval and prevent duplicate data pulls:

### Intelligent Sync Mechanism

- **Last Sync Tracking**: The system stores the timestamp of the last successful synchronization.
- **Incremental Data Pulls**: Only data newer than the last sync time is retrieved during regular pulls.
- **API-Level Filtering**: Date range parameters are passed to the Biotime API to limit the data returned.

### Duplicate Prevention

- **Existing Record Check**: Before creating new punch records, the system checks if a punch with the same terminal ID and timestamp already exists.
- **Transaction ID Verification**: Punches are identified by their unique transaction ID to prevent duplication.
- **Conflict Resolution**: If duplicate detection occurs, the system prioritizes existing records and logs the event.

### Scalability Enhancements

- **Batch Processing**: For environments with numerous terminals, the system processes them in smaller batches to prevent timeout issues.
- **Pagination Control**: API requests implement customizable page sizes to optimize data retrieval.
- **Employee Data Caching**: Frequently accessed employee data is cached during processing to reduce database queries.
- **Timeout Management**: Configurable timeouts prevent long-running operations from affecting system performance.

These optimizations ensure the system performs reliably in environments of all sizes, from small deployments to large enterprises with many terminals and employees.

## Employee Contract Integration

The Biotime module now integrates with Odoo's employee contract system to support multiple work schedules:

### Work Schedule Sources

- **Default Fixed Hours**: Basic configuration with fixed start/end times
- **Employee Contracts**: Advanced configuration that uses work schedules from each employee's contract

### How Contract Integration Works

1. **Enabling the Feature**: The "Use Employee Contracts" option tells the system to look for employee contracts when processing attendance.

2. **Contract Lookup Process**:
   - The system searches for the employee's active contract during the relevant date
   - If found, it extracts the work schedule for the specific day of the week
   - The work schedule determines expected start/end times and shift duration

3. **Smart Fallback System**:
   - If no contract is found, the system uses the default hours
   - If the contract doesn't define a schedule for that day, default hours are used
   - If the employee is working outside their scheduled hours, the system adapts accordingly

### Benefits

- **Support for Multiple Shifts**: Companies with varying shift patterns can now properly process attendance
- **Accuracy**: Work hours are based on each employee's actual assigned schedule
- **Flexibility**: The fallback mechanism ensures attendance processing works even when contract data is incomplete

This integration is particularly valuable for organizations with:
- Multiple shifts across different departments
- Employees with varying work schedules
- Specific departments with non-standard work hours

