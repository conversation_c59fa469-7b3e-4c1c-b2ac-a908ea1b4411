<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">

    <record id="survey_answer_1" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_feedback" />
        <field name="partner_id" ref="base.partner_demo"/>
        <field name="email"><EMAIL></field>
        <field name="state">done</field>
    </record>
    <record id="survey_answer_2" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_feedback" />
        <field name="partner_id" ref="base.res_partner_address_7"/>
        <field name="email"><EMAIL></field>
        <field name="state">done</field>
    </record>
    <record id="survey_answer_3" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_feedback" />
        <field name="partner_id" eval="False"/>
        <field name="email"><PERSON><PERSON> &lt;<EMAIL>&gt;</field>
        <field name="state">done</field>
    </record>
    <record id="survey_answer_4" model="survey.user_input">
        <field name="survey_id" ref="survey.survey_feedback" />
        <field name="partner_id" eval="False"/>
        <field name="email">Martin Tamarre &lt;<EMAIL>&gt;</field>
        <field name="state">in_progress</field>
    </record>

</data></odoo>
