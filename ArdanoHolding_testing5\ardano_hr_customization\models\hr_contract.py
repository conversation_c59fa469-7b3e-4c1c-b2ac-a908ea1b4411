from odoo import api, models, fields
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta


class HrPayslipInputType(models.Model):
    _inherit = 'hr.payslip.input.type'
    active = fields.Boolean(default=True)


class HrContract(models.Model):
    _inherit = "hr.contract"

    @api.model
    def _get_view(self, view_id=None, view_type='form', **options):
        arch, view = super()._get_view(view_id, view_type, **options)
        can_edit = self.env.user.has_group('ardano_hr_customization.hr_wage_can_edit')
        if view_type == 'form' and not can_edit:
            for node in arch.xpath("//field[@name='wage']"):
                node.set('readonly', "True")
        return arch, view


    location_bonus = fields.Boolean(string='Location Bonus', default=False, copy=False)
    location_bonus_percentage = fields.Integer(string='Location Percentage(%)', copy=False)
    # location_bonus_amount = fields.Integer(string='Location Amount', compute='_calc_location_bonus_amount')
    job_bonus = fields.Boolean(string='Job Bonus', default=False, copy=False)
    job_bonus_percentage = fields.Float(string='Job Percentage(%)', copy=False,compute='_calc_job_bonus_percentage')
    # job_bonus_amount = fields.Integer(string='Job Amount', compute='_calc_job_bonus_amount')
    general_manager_bonus = fields.Boolean(string='General Manager Bonus', default=False, copy=False)
    general_manager_bonus_percentage = fields.Integer(string='General Manager Percentage(%)', copy=False)
    # general_manager_bonus_amount = fields.Integer(string='General Manager Amount',
    #                                               compute='_calc_general_manager_bonus_amount')
    date_start = fields.Date('Start Date', required=True, default=fields.Date.today, tracking=True, index=True)
    work_start_date = fields.Date('Work Start Date', required=True, default=fields.Date.today, tracking=True,
                                  index=True)
    registration_number = fields.Char('Registration Number', copy=False, related='employee_id.registration_number',)
    hourly_wage = fields.Monetary(compute="_compute_hourly_wage", copy=False,store=True)
    contract_class_id = fields.Many2one('contract.classification', string="Contract Classification")


    def _check_contract_date_end(self):
        contract_ids = self.search([('date_end', '!=', False),
                                    ('state', '=', 'open')])
        for contract in contract_ids:
            if not isinstance(contract.date_end, bool):
                today = datetime.now().date()
                date_end_before = contract.date_end - timedelta(days=15)
                if date_end_before == today:
                    for user in self.env['res.users'].search(
                            [('groups_id', 'in', self.env.ref('hr_contract.group_hr_contract_manager').ids)]):
                        template_id = self.env.ref(
                            'ardano_hr_customization.email_template_date_remainder')
                        if template_id:
                            values = template_id.sudo().generate_email(contract.id,
                                                                       ['subject', 'body_html', 'email_from',
                                                                        'email_to',
                                                                        'partner_to', 'email_cc', 'reply_to',
                                                                        'scheduled_date'])
                            values['email_to'] = user.partner_id.email or ''
                            values['author_id'] = self.env.user.partner_id.id
                            values['subject'] = "Remainder about contract expire date on " + str(contract.date_end)
                            mail_mail_obj = self.env['mail.mail']
                            msg_id = mail_mail_obj.sudo().create(values)
                            if msg_id:
                                msg_id.sudo().send()

                        self.env['mail.activity'].create({
                            'res_id': contract.id,
                            'res_model_id': self.env['ir.model']._get('hr.contract').id,
                            'activity_type_id': 4,
                            'summary': "Dear Mr %s, "
                                       "This contract is going to expired at %s" % (user.name, contract.date_end),
                            'user_id': user.id,
                        })
                    else:
                        pass
        return True

    # @api.depends('location_bonus', 'location_bonus_percentage', 'wage')
    # def _calc_location_bonus_amount(self):
    #     for rec in self:
    #         rec.location_bonus_amount = int(rec.wage * rec.location_bonus_percentage / 100) if rec.location_bonus else 0

    # @api.depends('job_bonus', 'job_bonus_percentage', 'wage')
    # def _calc_job_bonus_amount(self):
    #     for rec in self:
    #         rec.job_bonus_amount = int(rec.wage * rec.job_bonus_percentage / 100) if rec.job_bonus else 0


    @api.depends('job_id', 'job_id.hourly_wage','wage_type')
    def _compute_hourly_wage(self):
        for rec in self:
            if rec.job_id.hourly_wage and rec.wage_type == 'hourly':
                rec.hourly_wage = rec.job_id.hourly_wage
              
            else:
                rec.hourly_wage = 0


    @api.depends('employee_id','employee_id.bonus_id','job_bonus')
    def _calc_job_bonus_percentage(self):
        for rec in self:
            if rec.employee_id.bonus_id and rec.job_bonus == True:
                rec.job_bonus_percentage = rec.employee_id.bonus_id.amount_percentage *100
              
            else:
                rec.job_bonus_percentage = 0



    @api.constrains('general_manager_bonus_percentage')
    def _check_general_manager_bonus_percentage(self):
        for rec in self:
            if rec.general_manager_bonus_percentage > 25:
                raise ValidationError("General manager bonus percentage not exceed 25%")

    # @api.depends('general_manager_bonus', 'general_manager_bonus_percentage', 'wage')
    # def _calc_general_manager_bonus_amount(self):
    #     for rec in self:
    #         rec.general_manager_bonus_amount = int(
    #             rec.wage * rec.general_manager_bonus_percentage / 100) if rec.general_manager_bonus else 0
