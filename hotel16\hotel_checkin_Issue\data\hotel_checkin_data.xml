<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Service Category Base -->
        <record id="hotel_service_category_base" model="product.category">
            <field name="name">General Services</field>
            <field name="isservicetype" eval="True"/>
        </record>
        
        <!-- Service Category -->
        <record id="hotel_service_type_general" model="hotel.service_type">
            <field name="name">General Services</field>
            <field name="isservicetype" eval="True"/>
            <field name="company_id" ref="base.main_company"/>
            <field name="ser_id" ref="hotel_service_category_base"/>
        </record>
        
        <!-- Demo Service Products -->
        <record id="hotel_service_room_cleaning" model="product.product">
            <field name="name">Room Cleaning Service</field>
            <field name="type">service</field>
            <field name="detailed_type">service</field>
            <field name="isservice" eval="True"/>
            <field name="categ_id" ref="hotel_service_type_general"/>
            <field name="list_price">25.00</field>
            <field name="standard_price">15.00</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description">Special room cleaning service</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="hotel_service_laundry" model="product.product">
            <field name="name">Laundry Service</field>
            <field name="type">service</field>
            <field name="detailed_type">service</field>
            <field name="isservice" eval="True"/>
            <field name="categ_id" ref="hotel_service_type_general"/>
            <field name="list_price">15.00</field>
            <field name="standard_price">10.00</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description">Laundry service for guest clothes</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="hotel_service_breakfast" model="product.product">
            <field name="name">Breakfast Service</field>
            <field name="type">service</field>
            <field name="detailed_type">service</field>
            <field name="isservice" eval="True"/>
            <field name="categ_id" ref="hotel_service_type_general"/>
            <field name="list_price">20.00</field>
            <field name="standard_price">12.00</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description">Breakfast delivery to room</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>
    </data>
</odoo> 