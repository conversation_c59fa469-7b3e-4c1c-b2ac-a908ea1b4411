<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <template id="report_layout" inherit_id="web.report_layout">
        <xpath expr="//head" position="inside">
            <t t-if="env and env.context.get('snailmail_layout')" t-call-assets="snailmail_account_followup.followup_report_assets_snailmail"/>
        </xpath>
    </template>
    <template id="minimal_layout" inherit_id="web.minimal_layout">
        <xpath expr="//head" position="inside">
            <t t-if="env and env.context.get('snailmail_layout')" t-call-assets="snailmail_account_followup.followup_report_assets_snailmail"/>
        </xpath>
    </template>
</data>
</odoo>
