# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_be_coda
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-28 12:14+0000\n"
"PO-Revision-Date: 2015-09-08 05:23+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bosnian (http://www.transifex.com/odoo/odoo-9/language/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:135
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.account_bank_statement_import_coda
msgid "<i class=\"fa fa-download mr4\"/>Import Sample Template"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__accounting_date
msgid "Accounting Date"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__all_lines_reconciled
msgid "All Lines Reconciled"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Izvod banke"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__data_file
msgid "Bank Statement File"
msgstr ""

#. module: l10n_be_coda
#: model:ir.actions.act_window,name:l10n_be_coda.action_account_bank_statement_line_coda
msgid "Bank Statement Lines"
msgstr "Stavke izvoda iz banke"

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Bank Transaction"
msgstr "Bankovne transakcije"

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.account_bank_statement_import_coda
msgid "Belgium Coded Statement of Account (.CODA)"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_coda_form
msgid "CODA Notes"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_form
msgid "CODA Statement Line"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:39
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:151
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:155
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:112
#, python-format
msgid ""
"CODA parsing error on movement data record 2.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:118
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__is_difference_zero
msgid "Check if difference is zero."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__date_done
msgid "Closed On"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:216
#, python-format
msgid "Communication"
msgstr "Komunikacija"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__company_id
msgid "Company"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__company_id
msgid "Company related to this journal"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__balance_end
msgid "Computed Balance"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:196
#, python-format
msgid "Counter Party"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:206
#, python-format
msgid "Counter Party Account"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:211
#, python-format
msgid "Counter Party Address"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__create_date
msgid "Created on"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Credit"
msgstr "Potraživanje"

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Credit Transactions."
msgstr "Potražne transakcije"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__date
msgid "Date"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Debit"
msgstr "Dugovanje"

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Debit Transactions."
msgstr "Dugovne transakcije"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__difference
msgid "Difference"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__balance_end_real
msgid "Ending Balance"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__cashbox_end_id
msgid "Ending Cashbox"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__move_line_ids
msgid "Entry lines"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:39
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:54
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:59
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:61
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:112
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:118
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:135
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:151
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:155
#, python-format
msgid "Error"
msgstr "Greška"

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Extended Filters..."
msgstr "Napredni filteri..."

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__reference
msgid "External Reference"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__filename
msgid "Filename"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:54
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:59
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement_import__data_file
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__id
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__id
msgid "ID"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement "
"reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally "
"be booked is already closed."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__is_difference_zero
msgid "Is zero"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__journal_id
msgid "Journal"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement_import__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__move_line_count
msgid "Move Line Count"
msgstr ""

#. module: l10n_be_coda
#: selection:account.bank.statement,state:0
msgid "New"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_form
msgid "Notes"
msgstr "Bilješke"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Partner"
msgstr "Partner"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__name
msgid "Reference"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__user_id
msgid "Responsible"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Search Bank Transactions"
msgstr "Pretraži izvode banke"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__balance_start
msgid "Starting Balance"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__cashbox_start_id
msgid "Starting Cashbox"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Statement"
msgstr "Izvod"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_account_bank_statement_line_coda_tree
msgid "Statement lines"
msgstr "Retci izvoda"

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__state
msgid "Status"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__journal_type
msgid "Technical field used for usability purposes"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__total_entry_encoding
msgid "Total of transaction lines."
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__total_entry_encoding
msgid "Transactions Subtotal"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__journal_type
msgid "Type"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_unread
msgid "Unread Messages"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:61
#, python-format
msgid "Unsupported bank account structure "
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""

#. module: l10n_be_coda
#: selection:account.bank.statement,state:0
msgid "Validated"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr ""
