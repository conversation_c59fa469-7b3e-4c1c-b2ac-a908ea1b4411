# Translation of Odoo Server.
# This file contains the translation of the following modules:
#     * l10n_pe_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-26 19:51+0000\n"
"PO-Revision-Date: 2023-09-26 19:51+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__active
msgid "Active"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_res_country__l10n_pe_agreement_code
msgid "Agreement Code"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_res_country__l10n_pe_agreement_code
msgid "Agreement code defined by SUNAT to avoid double taxation."
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_amount_total
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_amount_total
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_amount_total
msgid "Amount Total"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_base_igv
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_base_igv
msgid "BASE IGV"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_base_igv
msgid "Base IGV"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_base_ivap
msgid "Base IVAP"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__code
msgid "Code"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_res_country__l10n_pe_code
msgid "Code PE"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_res_country
msgid "Country"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_res_country__l10n_pe_code
msgid "Country code to be used on purchase reports."
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__create_date
msgid "Created on"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_customer
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_customer
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_customer
msgid "Customer"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_customer_vat
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_customer_vat
msgid "Customer VAT"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_dua_invoice_id
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_move__l10n_pe_dua_invoice_id
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_payment__l10n_pe_dua_invoice_id
msgid "DUA Invoice"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_dua_invoice_id
#: model:ir.model.fields,help:l10n_pe_reports.field_account_move__l10n_pe_dua_invoice_id
#: model:ir.model.fields,help:l10n_pe_reports.field_account_payment__l10n_pe_dua_invoice_id
msgid "DUA invoice that accredits the tax credit on the importation of goods."
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_date
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_date
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_invoice_date
msgid "Date"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_detraction_number
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_move__l10n_pe_detraction_number
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_payment__l10n_pe_detraction_number
msgid "Detraction"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_detraction_date
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_move__l10n_pe_detraction_date
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_payment__l10n_pe_detraction_date
msgid "Detraction Date"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_doc_type
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_doc_type
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_document_type
msgid "Doc. Type"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_base_exo
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_base_exo
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_exo
msgid "EXO"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_base_exp
msgid "EXP"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_base_free
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_free
msgid "FREE"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_icbper
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_vat_icbper
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_icbper
msgid "ICBPER"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__id
msgid "ID"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_tax_igv
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_tax_igv
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_igv
msgid "IGV"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_vat_igv_g_ng
msgid "IGV GyNG"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_vat_igv_ng
msgid "IGV NG"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_base_ina
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_base_ina
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_ina
msgid "INA"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_tax_isc
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_tax_isc
msgid "ISC"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_14_1_tax_ivap
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_tax_ivap
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_tax_ivap
msgid "IVAP"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_move.py:0
#, python-format
msgid ""
"If a detraction value is set (date or number), both values must be filled."
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_ple_reports.py:0
#, python-format
msgid ""
"In order to generate the PLE reports, please update l10n_pe module to update"
" the required data."
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_detraction_date
#: model:ir.model.fields,help:l10n_pe_reports.field_account_move__l10n_pe_detraction_date
#: model:ir.model.fields,help:l10n_pe_reports.field_account_payment__l10n_pe_detraction_date
msgid "Indicate the date of issuance of the detraction deposit certificate"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_detraction_number
#: model:ir.model.fields,help:l10n_pe_reports.field_account_move__l10n_pe_detraction_number
#: model:ir.model.fields,help:l10n_pe_reports.field_account_payment__l10n_pe_detraction_number
msgid "Indicate the number of issuance of the detraction deposit certificate"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_service_modality
#: model:ir.model.fields,help:l10n_pe_reports.field_account_move__l10n_pe_service_modality
#: model:ir.model.fields,help:l10n_pe_reports.field_account_payment__l10n_pe_service_modality
msgid ""
"Indicate the service modality, fill this field if the invoice is for a "
"service. This will be used on 8.2 report."
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_l10n_pe_ple_usage__name
msgid "Name"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#, python-format
msgid "Only Peruvian company can generate PLE 5.3 report."
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_vat_other
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_other_taxes
msgid "Other Taxes"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#, python-format
msgid "PLE 5.1"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#, python-format
msgid "PLE 5.3"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#, python-format
msgid "PLE 6.1"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_l10n_pe_tax_ple_report_handler
msgid "PLE Generic Report"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_l10n_pe_tax_ple_8_1_report_handler
msgid "PLE Purchase Report 8.1 (Now RCE 8.4)"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_l10n_pe_tax_ple_8_2_report_handler
msgid "PLE Purchase Report 8.2 (Now RCE 8.5)"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_l10n_pe_tax_ple_14_1_report_handler
msgid "PLE Sales Report 14.1 (Now RVIE 14.2)"
msgstr ""

#. module: l10n_pe_reports
#: model_terms:ir.ui.view,arch_db:l10n_pe_reports.view_invoice_form
msgid "Peru"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.line,name:l10n_pe_reports.ple_81_line
msgid "RCE 8.4"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.line,name:l10n_pe_reports.ple_82_line
msgid "RCE 8.5"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.line,name:l10n_pe_reports.ple_14_1_line
msgid "RVIE 14.4"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_service_modality
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_move__l10n_pe_service_modality
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_payment__l10n_pe_service_modality
msgid "Service Modality"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields.selection,name:l10n_pe_reports.selection__account_move__l10n_pe_service_modality__1
msgid "Service provided entirely in Peru"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields.selection,name:l10n_pe_reports.selection__account_move__l10n_pe_service_modality__3
msgid "Service provided exclusively abroad"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields.selection,name:l10n_pe_reports.selection__account_move__l10n_pe_service_modality__2
msgid "Service provided partly in Peru and part abroad"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model,name:l10n_pe_reports.model_l10n_pe_ple_usage
msgid ""
"Service that is reflected in the declared invoice and must be classified "
"according to table 31, used on purchase report 8.2"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_usage_type_id
#: model:ir.model.fields,help:l10n_pe_reports.field_account_move__l10n_pe_usage_type_id
#: model:ir.model.fields,help:l10n_pe_reports.field_account_payment__l10n_pe_usage_type_id
msgid ""
"Service that is reflected in the declared invoice and must be classified "
"according to table 31: Type of Usage."
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#: code:addons/l10n_pe_reports/models/account_general_ledger.py:0
#: code:addons/l10n_pe_reports/models/account_ple_reports.py:0
#: code:addons/l10n_pe_reports/models/account_ple_reports.py:0
#, python-format
msgid "TXT"
msgstr ""

#. module: l10n_pe_reports
#. odoo-python
#: code:addons/l10n_pe_reports/models/account_ple_sales_14_1.py:0
#, python-format
msgid ""
"The state in the next documents is posted/cancelled but not stamped/cancelled in the SUNAT:\n"
"\n"
"%s"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_bank_statement_line__l10n_pe_usage_type_id
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_move__l10n_pe_usage_type_id
#: model:ir.model.fields,field_description:l10n_pe_reports.field_account_payment__l10n_pe_usage_type_id
msgid "Usage Type"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report,name:l10n_pe_reports.tax_report_ple_purchase_8_1
msgid "VAT Report (RCE Purchase 8.4)"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report,name:l10n_pe_reports.tax_report_ple_purchase_8_2
msgid "VAT Report (RCE Purchase 8.5)"
msgstr ""

#. module: l10n_pe_reports
#: model:account.report,name:l10n_pe_reports.tax_report_ple_sales_14_1
msgid "VAT Report (RVIE Sales 14.4)"
msgstr ""

#. module: l10n_pe_reports
#: model:ir.model.fields,help:l10n_pe_reports.field_l10n_pe_ple_usage__code
msgid "Value to be used in the purchase report."
msgstr ""

#. module: l10n_pe_reports
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_1_base_withholdings
#: model:account.report.column,name:l10n_pe_reports.tax_report_ple_8_2_withholdings
msgid "Withholdings"
msgstr ""
