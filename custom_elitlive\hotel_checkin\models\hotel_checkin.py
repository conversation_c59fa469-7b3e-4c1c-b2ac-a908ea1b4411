# -*- coding: utf-8 -*-
from dateutil.relativedelta import relativedelta

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta, date


class ResUsers(models.Model):
    _inherit = 'res.users'

    is_hotel_checkin_user = fields.Boolean(
        string='Hotel Check-in User',
        help="If checked, the user will be added to the Hotel Check-in User group"
    )

    @api.onchange('is_hotel_checkin_user')
    def _onchange_is_hotel_checkin_user(self):
        """Update user groups when the checkbox is toggled"""
        hotel_checkin_group = self.env.ref('hotel_checkin.group_hotel_checkin_user')
        if self.is_hotel_checkin_user:
            self.groups_id = [(4, hotel_checkin_group.id)]
        else:
            self.groups_id = [(3, hotel_checkin_group.id)]
    
class HotelReservation(models.Model):
    _inherit = "hotel.reservation"

    # Override the default search to only show relevant reservations
    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Override search to show only confirmed and done reservations for check-in users"""
        if self.env.user.has_group('hotel_checkin.group_hotel_checkin_user'):
            if not any(arg[0] == 'state' for arg in (args or [])):
                args = (args or []) + ['|', ('state', '=', 'confirm'), ('state', '=', 'done')]
            # Add condition to exclude checked out reservations
            args = args + [('is_checked_out', '=', False)]
        return super().search(args, offset=offset, limit=limit, order=order, count=count)
    
    # Make all fields computed and non-stored to always read from the original reservation
    is_premium = fields.Boolean(
        string='Premium Suite',
        compute='_compute_is_premium',
    )
    
    # Add folio_id field to link to the hotel.folio
    folio_id = fields.Many2one(
        'hotel.folio',
        string='Folio',
        compute='_compute_folio_id',
    )
    
    # Add is_checked_out field to track whether a reservation has been checked out
    is_checked_out = fields.Boolean(
        string='Is Checked Out',
        default=False,
        help="Indicates whether the guest has been checked out"
    )
    
    # Add real_checkout_time field to store the actual checkout time
    real_checkout_time = fields.Datetime(
        string='Real Checkout Time',
        readonly=True,
        help="The actual time when the guest checked out"
    )
    
    real_checkout_display = fields.Char(
        string='Real Checkout Display',
        compute='_compute_real_checkout_display',
    )

    
    @api.depends('name')
    def _compute_folio_id(self):
        """Get the folio associated with this reservation"""
        for record in self:
            # Look for folio with this reservation
            folio = self.env['hotel.folio'].search([('reservation_id', '=', record.id)], limit=1)
            if not folio:
                # Try to find by other means if the direct relation doesn't work
                record.folio_id = False
            else:
                record.folio_id = folio.id
    
    def action_add_service(self):
        """
        Open wizard to add a service to the folio
        """
        self.ensure_one()
        if not self.folio_id:
            raise UserError(_("No folio found for this reservation."))
            
        return {
            'name': _('Add Service'),
            'type': 'ir.actions.act_window',
            'res_model': 'hotel.service.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_reservation_id': self.id,
                'default_folio_id': self.folio_id.id,
            },
        }
    
    price = fields.Float(
        string='Price per Night',
        compute='_compute_price',
    )

    checkin_date = fields.Datetime(
        string='Check-in Date',
        compute='_compute_dates',
    )

    checkout_date = fields.Datetime(
        string='Check-out Date',
        compute='_compute_dates',
    )
    
    checkin_date_display = fields.Date(
        string='Check-in Date (Display)',
        compute='_compute_display_dates',
    )

    checkout_date_display = fields.Date(
        string='Check-out Date (Display)',
        compute='_compute_display_dates',
    )
    
    # Add fields to display the full datetime
    checkin_datetime_display = fields.Char(
        string='Check-in Date & Time',
        compute='_compute_datetime_display',
    )
    
    checkout_datetime_display = fields.Char(
        string='Check-out Date & Time',
        compute='_compute_datetime_display',
    )
    
    stay_days = fields.Integer(
        string='Days of Stay',
        compute='_compute_stay_days',
    )

    is_late_checkout = fields.Boolean(
        string='Late Checkout',
        compute='_compute_is_late_checkout',
        search='_search_is_late_checkout',
    )

    room_count = fields.Integer(
        string='Number of Rooms',
        compute='_compute_room_count',
    )
    
    room_names = fields.Char(
        string='Room Names',
        compute='_compute_room_names',
    )
    is_amount_of_guarrantee = fields.Char(
        compute='_compute_dates',
    )
    guarantee_amount = fields.Float(
        compute='_compute_dates',
    )
    is_auto_payments = fields.Char(
    )

    is_today = fields.Boolean(compute='_compute_time_filters', store=True)
    is_this_week = fields.Boolean(compute='_compute_time_filters', store=True)
    is_this_month = fields.Boolean(compute='_compute_time_filters', store=True)
    is_this_year = fields.Boolean(compute='_compute_time_filters', store=True)

    @api.depends('date_order')
    def _compute_time_filters(self):
        today = fields.Date.today()

        # Week range
        start_week = today - timedelta(days=today.isoweekday() - 1)  # Monday
        end_week = start_week + timedelta(days=7)  # Next Monday (exclusive)

        # Month range
        start_month = today.replace(day=1)
        end_month = start_month + relativedelta(months=1)

        # Year range
        start_year = today.replace(month=1, day=1)
        end_year = today.replace(month=12, day=31)

        for record in self:
            reservation_line = record.reservation_line.search([('line_id', '=', record.id)], limit=1)
            if reservation_line and reservation_line.checkin:
                checkin_date = reservation_line.checkin.date()
                record.is_today = checkin_date == today
                record.is_this_week = start_week <= checkin_date < end_week
                record.is_this_month = start_month <= checkin_date < end_month
                record.is_this_year = start_year <= checkin_date <= end_year
            else:
                record.is_today = False
                record.is_this_week = False
                record.is_this_month = False
                record.is_this_year = False

    @api.depends('reservation_line')
    def _compute_room_count(self):
        """Compute the number of rooms in the reservation"""
        for record in self:
            record.room_count = len(record.reservation_line)
            
    @api.depends('reservation_line.room_number')
    def _compute_room_names(self):
        """Compute a formatted string of room names for display in kanban view"""
        for record in self:
            room_names = []
            for line in record.reservation_line:
                if line.room_number and line.room_number.name:
                    room_names.append(line.room_number.name)
            
            if room_names:
                record.room_names = ", ".join(room_names)
            else:
                record.room_names = "No rooms assigned"

    @api.depends('checkin_date', 'checkout_date')
    def _compute_display_dates(self):
        """Compute date-only versions of the datetime fields for display"""
        for record in self:
            record.checkin_date_display = record.checkin_date.date() if record.checkin_date else False
            record.checkout_date_display = record.checkout_date.date() if record.checkout_date else False
    
    @api.depends('checkin_date', 'checkout_date')
    def _compute_datetime_display(self):
        """Compute formatted datetime strings for display"""
        time_record = self.env['check.out.in'].search([('shop_id', '=', self.env.company.id)], limit=1)

        for record in self:
            if record.checkin_date:
                # Format: "2023-05-15 14:30:00" to "15/05/2023 14:30"
                checkin_dt = record.reservation_line.checkin
                # record.checkin_datetime_display = checkin_dt.strftime('%d/%m/%Y %H:%M')
                record.checkin_datetime_display = record.reservation_line.checkin.replace(hour=int(time_record.checkin_time),minute=0, second=0, microsecond=0, tzinfo=None)
            else:
                record.checkin_datetime_display = False
                
            if record.checkout_date:
                # Format: "2023-05-17 12:00:00" to "17/05/2023 12:00"
                checkout_dt = record.checkout_date
                record.checkout_datetime_display = record.checkout_date.replace(hour=int(time_record.checkin_time),minute=0, second=0, microsecond=0, tzinfo=None)
            else:
                record.checkout_datetime_display = False
    
    @api.depends('checkin_date', 'checkout_date')
    def _compute_stay_days(self):
        """Compute the number of days of stay"""
        for record in self:
            if record.checkin_date and record.checkout_date:
                # Convert to date objects to ignore time
                checkin = record.checkin_date.date()
                checkout = record.checkout_date.date()
                # Calculate the difference in days
                delta = checkout - checkin
                if record.reservation_line:
                    record.stay_days =  record.reservation_line.number_of_days
            else:
                record.stay_days = 0

    @api.depends('reservation_line.room_number.categ_id', 'state')
    def _compute_is_premium(self):
        """Compute whether the room is premium based on its category"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                room = record.reservation_line[0].room_number
                record.is_premium = room and room.categ_id.name.lower().find('premium') >= 0
            else:
                record.is_premium = False
    
    @api.depends('reservation_line.price', 'state')
    def _compute_price(self):
        """Compute the price per night based on the reservation lines"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                record.price = record.reservation_line[0].price
            else:
                record.price = 0.0

    @api.depends('reservation_line.checkin', 'reservation_line.checkout', 'state')
    def _compute_dates(self):
        """Compute check-in and check-out dates from reservation lines"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                record.checkin_date = record.reservation_line[0].checkin
                record.checkout_date = record.reservation_line[0].checkout
                record.real_checkout_time = record.reservation_line[0].real_checkout_time
                record.is_amount_of_guarrantee = 'True' if record.is_guarrantte_amount else 'False'
                record.is_auto_payments = 'True' if record.is_auto_payment else 'False'
                record.guarantee_amount = record.guarrante_amount
                record.note = record.note
            else:
                record.checkin_date = False
                record.checkout_date = False
                record.real_checkout_time = False
                record.is_amount_of_guarrantee = 'False'

    @api.depends('checkout_date', 'state')
    def _compute_is_late_checkout(self):
        """Compute whether the reservation is past its checkout time"""
        now = fields.Datetime.now()
        for record in self:
            if record.state == 'done' and record.checkout_date:
                record.is_late_checkout = now > record.checkout_date
            else:
                record.is_late_checkout = False

    @api.model
    def _search_is_late_checkout(self, operator, value):
        """
        Search method for is_late_checkout field
        This makes the computed field searchable in views
        """
        now = fields.Datetime.now()
        domain = [
            ('state', '=', 'done'),
            ('reservation_line.checkout', '<', now)
        ]
        reservations = self.search(domain)
        if (operator == '=' and value) or (operator == '!=' and not value):
            return [('id', 'in', reservations.ids)]
        return [('id', 'not in', reservations.ids)]
    
    def action_checkin(self):
        """
        This method is a wrapper around the 'done' method from hotel_management
        It's used to provide a clearer name for the check-in action in our simplified interface
        """
        self.ensure_one()
        if self.state != 'confirm':
            raise UserError(_("Only confirmed reservations can be checked in."))
        return self.done()

    def action_checkout(self):
        """
        Complete checkout process:
        1. Confirm folio if not already confirmed
        2. Create invoice
        3. Confirm invoice
        4. Checkout guest
        """
        self.ensure_one()
        if not self.folio_id:
            raise UserError(_("No folio found for this reservation."))
        
        # Step 1: Confirm folio if not already confirmed
        if self.folio_id.state == 'draft':
            self.folio_id.action_confirm()
        
        # Step 2: Create invoice if not already created
        if not self.folio_id.invoice_ids:
            # In Odoo 16, we use _create_invoices instead of action_invoice_create
            if self.folio_id.order_id:
                # Create invoice
                moves = self.folio_id.order_id._create_invoices()
                if moves:
                    # Mark folio as in progress
                    self.folio_id.write({'state': 'progress'})
                    
                    # Step 3: Confirm invoice
                    for move in moves:
                        move.action_post()
        
        # Step 4: Checkout guest - update folio state to check_out
        self.folio_id.write({'state': 'check_out'})
        
        # Update reservation state, mark as checked out, and set real checkout time
        self.write({
            'state': 'done',
            'is_checked_out': True,
            'real_checkout_time': fields.Datetime.now()
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Guest has been successfully checked out.'),
                'sticky': False,
                'type': 'success',
                'next': {
                    'type': 'ir.actions.act_window_close'
                }
            }
        }

    @api.depends('real_checkout_time')
    def _compute_real_checkout_display(self):
        """Compute formatted string for real checkout time display"""
        time_record = self.env['check.out.in'].search([('shop_id', '=', self.env.company.id)], limit=1)
        for record in self:
            if record.real_checkout_time:
                # Format: "2023-05-17 14:45:00" to "17/05/2023 14:45"
                checkout_dt = record.real_checkout_time
                record.real_checkout_display = record.reservation_line[0].real_checkout_time.replace(hour=int((time_record.real_checkout_time)), minute=0, second=0, microsecond=0)
            else:
                record.real_checkout_display = False


class HotelCheckin(models.TransientModel):
    """
    This is a transient model that will be used to display a simplified check-in wizard
    It's not actually stored in the database, just used for the UI
    """
    _name = "hotel.checkin.wizard"
    _description = "Hotel Check-in Wizard"
    
    reservation_id = fields.Many2one('hotel.reservation', string='Reservation', required=True,
                                    domain="[('state', '=', 'confirm')]")
    guest_name = fields.Char(related='reservation_id.partner_id.name', string='Guest Name', readonly=True)
    reservation_no = fields.Char(related='reservation_id.reservation_no', string='Reservation No', readonly=True)
    
    def action_checkin(self):
        """
        Process the check-in for the selected reservation
        """
        if not self.reservation_id:
            raise UserError(_("Please select a reservation to check in."))
        
        if self.reservation_id.state != 'confirm':
            raise UserError(_("Only confirmed reservations can be checked in."))
        
        return self.reservation_id.done()


class HotelCheckinTools(models.TransientModel):
    """
    Utility model for hotel check-in administration
    """
    _name = "hotel.checkin.tools"
    _description = "Hotel Check-in Administration Tools"
    
    user_ids = fields.Many2many('res.users', string='Users', 
                               help="Select users to add to the Hotel Check-in User group")
    
    def action_assign_users_to_checkin_group(self):
        """
        Assign selected users to the Hotel Check-in User group
        """
        checkin_group = self.env.ref('hotel_checkin.group_hotel_checkin_user')
        for user in self.user_ids:
            user.write({'groups_id': [(4, checkin_group.id)]})
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s users have been assigned to the Hotel Check-in User group') % len(self.user_ids),
                'sticky': False,
                'type': 'success',
            }
        }


class HotelServiceWizard(models.TransientModel):
    """
    Wizard for adding services to a folio
    """
    _name = 'hotel.service.wizard'
    _description = 'Add Service to Folio'

    reservation_id = fields.Many2one('hotel.reservation', string='Reservation', 
                                    domain="[('state', '=', 'done')]")
    folio_id = fields.Many2one('hotel.folio', string='Folio', required=True)
    product_id = fields.Many2one('product.product', string='Product', domain= lambda rec: [('id', 'in', rec._filter_products())])
    service_id = fields.Many2one('hotel.services', string='Service')

    product_uom_qty = fields.Float(string='Quantity', default=1.0, required=True)
    product_uom = fields.Many2one('uom.uom', string='Unit of Measure', required=True)
    name = fields.Text(string='Description')
    price_unit = fields.Float(string='Unit Price', required=True)
    discount = fields.Float(string='Discount (%)')
    tax_id = fields.Many2many('account.tax', string='Taxes')


    def _filter_products(self):
        product_ids = self.service_id.search([('categ_id.isservicetype', '=', True)]).service_id
        if product_ids:
            return product_ids.ids
        else:
            return []

    @api.onchange('reservation_id')
    def _onchange_reservation_id(self):
        if self.reservation_id and self.reservation_id.folio_id:
            self.folio_id = self.reservation_id.folio_id

    @api.onchange('product_id')
    def _onchange_product_id(self):
        if self.product_id:
            self.name = self.product_id.display_name
            self.product_uom = self.product_id.uom_id
            self.price_unit = self.product_id.list_price
            self.tax_id = self.product_id.taxes_id

    def action_add_service(self):
        """
        Add the service to the folio's service lines
        """
        self.ensure_one()
        # Create service line
        service_line_vals = {
            'folio_id': self.folio_id.id,
            'order_id': self.folio_id.order_id.id,
            'product_id': self.product_id.id,
            'name': self.name or self.product_id.display_name,
            'product_uom_qty': self.product_uom_qty,
            'product_uom': self.product_uom.id,
            'price_unit': self.price_unit,
            'discount': self.discount,
            'tax_id': [(6, 0, self.tax_id.ids)],
            'order_partner_id': self.folio_id.partner_id.id,
        }
        self.env['hotel_service.line'].create(service_line_vals)
        return {'type': 'ir.actions.act_window_close'}


class HotelCheckoutWizard(models.TransientModel):
    """
    Wizard for checking out guests
    """
    _name = 'hotel.checkout.wizard'
    _description = 'Hotel Checkout Wizard'

    reservation_id = fields.Many2one('hotel.reservation', string='Reservation', required=True,
                                    domain="[('state', '=', 'done')]")
    guest_name = fields.Char(related='reservation_id.partner_id.name', string='Guest Name', readonly=True)
    reservation_no = fields.Char(related='reservation_id.reservation_no', string='Reservation No', readonly=True)
    folio_id = fields.Many2one(related='reservation_id.folio_id', string='Folio', readonly=True)
    total_amount = fields.Float(string='Total Amount', compute='_compute_total_amount')
    create_invoice = fields.Boolean(string='Create Invoice', default=True)
    real_checkout_time = fields.Datetime(string='Real Checkout Time', default=fields.Datetime.now)
    
    @api.depends('folio_id')
    def _compute_total_amount(self):
        for record in self:
            if record.folio_id:
                record.total_amount = record.folio_id.amount_total
            else:
                record.total_amount = 0.0
    
    def action_checkout(self):
        """
        Process checkout through the wizard
        """
        self.ensure_one()
        if not self.reservation_id:
            raise UserError(_("Please select a reservation."))
        
        # Update the real checkout time on the reservation
        self.reservation_id.write({
            'real_checkout_time': self.real_checkout_time
        })
        
        # Call the checkout action on the reservation
        return self.reservation_id.action_checkout() 