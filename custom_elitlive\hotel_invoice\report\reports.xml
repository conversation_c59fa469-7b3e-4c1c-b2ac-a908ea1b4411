<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- VAT Invoice Report -->
    <record model="report.paperformat" id="paperformat_style_formatt_report">
        <field name="name">paperformat.pdc.payment</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">40</field>
        <field name="margin_right">5</field>
        <field name="margin_bottom">27</field>
        <field name="margin_left">5</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">30</field>
        <field name="dpi">90</field>
    </record>

    <!-- A3 Paper Format for Monthly Reservation Schedule -->
    <record model="report.paperformat" id="paperformat_monthly_reservation_schedule">
        <field name="name">A3 Landscape</field>
        <field name="default" eval="False"/>
        <field name="format">A3</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Landscape</field>
        <field name="margin_top">20</field>
        <field name="margin_right">5</field>
        <field name="margin_bottom">10</field>
        <field name="margin_left">5</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">15</field>
        <field name="dpi">90</field>
    </record>

    <record id="action_invoice_hotel_report" model="ir.actions.report">
        <field name="name">Hotel Invoice</field>
        <field name="model">hotel.reservation</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hotel_invoice.invoice_hotel_template_id</field>
        <field name="report_file">hotel_invoice.invoice_hotel_template_id</field>
        <field name="binding_model_id" ref="model_hotel_reservation"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Monthly Reservation Schedule Report -->
    <record id="action_monthly_reservation_schedule_report" model="ir.actions.report">
        <field name="name">Monthly Reservation Schedule</field>
        <field name="model">monthly.reservation.report</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hotel_invoice.monthly_reservation_schedule_template</field>
        <field name="report_file">hotel_invoice.monthly_reservation_schedule_template</field>
        <field name="paperformat_id" ref="hotel_invoice.paperformat_monthly_reservation_schedule"/>
        <field name="print_report_name">'Monthly Reservation Schedule'</field>
        <field name="binding_model_id" ref="model_monthly_reservation_report"/>
        <field name="binding_type">report</field>
    </record>

</odoo>
