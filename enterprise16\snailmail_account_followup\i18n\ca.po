# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account_followup
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# eriiikgt, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"The letter will be sent using the IAP service from Odoo.&#10;Make sure you have enough credits on your account or proceed to a recharge.\"/>\n"
"                        <br/>"
msgstr ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"The letter will be sent using the IAP service from Odoo.#10;Make sure you have enough credits on your account or proceed to a recharge.\"/>\n"
"                        <br/>"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "By post"
msgstr "Per publicació"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Criteri de seguiment"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Informe de seguiment"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Enviar una carta"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "Sending this document by post will cost"
msgstr "L'enviament d'aquest document per missatge costarà"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail
msgid "Snailmail"
msgstr "Correu postal"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail_cost
msgid "Stamps"
msgstr "Segells"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Assistent per a enviar recordatoris manuals als clients"

#. module: snailmail_account_followup
#. odoo-python
#: code:addons/snailmail_account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a letter by post, but no follow-up contact has any "
"address set"
msgstr ""
"Esteu intentant enviar una carta per correu, però cap contacte de seguiment "
"té cap adreça definida"
