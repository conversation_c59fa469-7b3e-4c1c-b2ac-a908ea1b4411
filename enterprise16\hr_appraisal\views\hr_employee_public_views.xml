<?xml version="1.0" ?>
<odoo>
    <record id="hr_employee_public_view_form" model="ir.ui.view">
        <field name="name">hr.employee.public.view.form.inherit.appraisal</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
        <xpath expr="//header" position="inside">
            <field name="parent_user_id" invisible="1"/>
            <button name="action_send_appraisal_request"
                string="Request Appraisal"
                type="object"
                data-hotkey="g"
                groups="!hr_appraisal.group_hr_appraisal_user"
                class="btn btn-primary" attrs="{'invisible': &quot;[('parent_user_id', '!=', uid)]&quot;}"/>
            <button name="action_send_appraisal_request"
                string="Request Appraisal"
                type="object"
                data-hotkey="g"
                groups="hr_appraisal.group_hr_appraisal_user"
                class="btn btn-primary"/>
        </xpath>
        </field>
    </record>
</odoo>
