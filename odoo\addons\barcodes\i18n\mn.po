# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# Munkhbaatar Gombosuren, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Munkhbaatar Gombosuren, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*' ?"
msgstr ""
" '*' Зураасан кодны Regex хэв маягт тохирохгүй байна. Таны хүссэн зүйл '.*' "
"үү?"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""
"<i>Зураасан кодын нэр төрөл</i> нь зураасан кодууд нь яаж танигдаж ангилагдах талаар тодорхойлдог.\n"
"                                Зураасан кодыг сканнердахад таарах<i>эхний</i> дүрэмд\n"
"                                холбогдоно. Зураасан кодын хэвийг regulare expression-р тодоройлдог.\n"
"                                Зураасан кодын эхлэл угтвар хэсгийг нь regulare expression-р илэрхийлдэг."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr ""
"Зураасан кодын задлах дүрэм нь борлуулалтын цэг дээр зураасан кодыг хэрхэн "
"задлан унших арга, дүрмийг тодорхойлдог."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "Шинэ зураасан кодны задлах дүрэм бүртгэх"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "Ерөнхий нэр"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "Үргэлж"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr "Зураасан кодын энэ дүрэмийн хувьд дотоод танилт"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "Зураасан кодын энэ дүрэмийн хувьд дотоод танилт"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "Дурын"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr "Зураасан кодны үзэгдэл танигч"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "Зураасан кодын дүрэм"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "Зураасан код задлах дүрэм"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "Зураасан кодын хэв загвар"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "Зураасан кодын дүрэм"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "Зураасан код уншигдсан"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Barcode: "
msgstr "Зураасан код:"

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "EAN-13-с UPC-A-руу"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Кодчлол"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "ID"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature____last_update
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "Хэзээ ч үгүй"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "Задлах дүрэм"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the \n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, \n"
"                                the barcode field on the associated records <i>must</i> show these digits as \n"
"                                zeroes."
msgstr ""
"Хэв нь мөн жин, үнэ гэх мэт тоон утгуудыг оруулахад хэрэглэгдэж болно.\n"
"                                Тэдгээр нь  <code>{NNN}</code> гэж тэмдэглэгддэг бөгөөд\n"
"                                N-үүд нь тооны оронгуудыг илэрхийлдэг. Аравтын бутархай  \n"
"                                мөн хэрэглэгдэж болдог <code>{NNNDD}</code> гэж илэрхийлэгддэг. \n"
"                                D-үүд нь бутархайн оронг илэрхийлдэг. Энэ тохиолдлолд \n"
"                                эдгээр тоог харуулах талбарууд нь тоонуудыг тэгээр харуулах<i>ёстой</i> "

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "Дүрмийн нэр"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "Дүрэм"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "Хүснэгт"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "Зураасан кодыг таних хэв"

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"The barcode pattern %(pattern)s does not lead to a valid regular expression."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "Зураасан кодын дүрмийн жагсаалт"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr "Таарсан хэв нь энэ зураасан код руу давхар холбогдоно."

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Энэ дүрэм нь зураасан код нь зөвхөн заасан кодчилолоор кодлогдсон байвал "
"хэрэглэгдэнэ."

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "Төрөл"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""
"UPC кодууд нь EAN код руу урд нь тэг нэмэх замаар хөрвүлэгдэнэ. Энэ тохиргоо"
" нь UPC/EAN зураасан кодууд автоматаар хөрвүүлэгдэхийг тодорхойлдог."

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "UPC-A -с EAN-13-руу"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "UPC/EAN хөрвүүлэлт"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "Барааны нэр төрөл"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Unknown barcode command"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""
"Дүрмүүдэд эрэмбэ дараалал тогтооно. Ингэснээр бага эрэмбэтэй дүрэм түрүүлж "
"шалгагдана"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Сүүлд уншуулсан зураасан кодын утга."
