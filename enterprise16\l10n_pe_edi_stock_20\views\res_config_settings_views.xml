<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_form_inherit_l10n_pe_edi" model="ir.ui.view">
        <field name="name">res.config.settings.form.inherit.l10n.pe.edi</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="l10n_pe_edi.res_config_settings_form_inherit_l10n_pe_edi"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='invoicing_peruvian_settings']" position="inside">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_left_pane"/>
                    <div class="o_setting_right_pane">
                        <span class="o_form_label mt16">Sunat Delivery Guide API</span>
                        <div class="text-muted">
                            Configure SUNAT API credentials for delivery guide. Please be aware that
                            the test environment is not supported for delivery guide.
                        </div>
                        <div class="content-group">
                            <div class="row mt32">
                                <label for="l10n_pe_edi_stock_client_id" class="col-md-5 o_light_label"/>
                                <field name="l10n_pe_edi_stock_client_id"/>
                                <label for="l10n_pe_edi_stock_client_secret" class="col-md-5 o_light_label"/>
                                <field name="l10n_pe_edi_stock_client_secret" password="True"/>
                                <label for="l10n_pe_edi_stock_client_username" class="col-md-5 o_light_label"/>
                                <field name="l10n_pe_edi_stock_client_username"/>
                                <label for="l10n_pe_edi_stock_client_password" class="col-md-5 o_light_label"/>
                                <field name="l10n_pe_edi_stock_client_password" password="True"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
