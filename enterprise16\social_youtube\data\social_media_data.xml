<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="social_media_youtube" model="social.media">
            <field name="name">YouTube</field>
            <field name="media_type">youtube</field>
            <field name="media_description">Manage your YouTube videos and schedule video uploads</field>
            <field name="image" type="base64" file="social_youtube/static/src/img/youtube.svg"/>
        </record>

        <record id="stream_type_youtube_channel_videos" model="social.stream.type">
            <field name="name">Channel</field>
            <field name="stream_type">youtube_channel_videos</field>
            <field name="media_id" ref="social_media_youtube"></field>
        </record>
    </data>
</odoo>
