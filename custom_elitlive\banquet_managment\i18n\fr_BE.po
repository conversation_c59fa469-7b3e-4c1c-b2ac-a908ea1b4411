# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* banquet_managment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 08:15+0000\n"
"PO-Revision-Date: 2020-08-12 08:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "(Compute)"
msgstr "(Calculer)"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "(Update History)"
msgstr "Mise ê jour"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__address
msgid "Address"
msgstr "Adresse"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__adult
msgid "Adult Persons"
msgstr "Adultes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__agent_id
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__agent_id
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__via__agent
#: model:ir.model.fields.selection,name:banquet_managment.selection__crm_lead__via__agent
msgid "Agent"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_all
msgid "All Banquet Reservation"
msgstr "Toutes les réservations de banquets"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_cancel
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_cancel
msgid "All Cancelled Banquet Reservation"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_cancel
msgid "All Cancelled Reservation"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_confirm
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_confirm
msgid "All Confirm Banquet Reservation"
msgstr "Toutes les réservations de banquets à confirmer"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_confirm
msgid "All Confirm Reservation"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_done
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_done
msgid "All Done Banquet Reservation"
msgstr "Toutes les réservations de banquets effectuées"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_reservation_tree_draft
#: model:ir.ui.menu,name:banquet_managment.menu_action_banquet_reservation_tree_draft
msgid "All Draft Banquet Reservation"
msgstr "Tous les brouillons de réservation de banquets"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_action_hotel_reservation_tree_all
msgid "All Reservation"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__approve
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__approve
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Approved"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__banquet_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__banquet_id
msgid "Banquet"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__banq_bool
#: model:ir.ui.menu,name:banquet_managment.main_menu_banquet_booking
msgid "Banquet Booking"
msgstr "Réservation de banquet"

#. module: banquet_managment
#: model:ir.ui.menu,name:banquet_managment.menu_banquet_config
msgid "Banquet Configuration"
msgstr "Configuration du banquet"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Hall"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.crm_case_form_view_leads_inherit_tour
#: model_terms:ir.ui.view,arch_db:banquet_managment.crm_case_form_view_oppor_inherit_tour
msgid "Banquet History"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__banquet_id
msgid "Banquet Id"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Information"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_banquet_quotation_view
#: model:ir.model,name:banquet_managment.model_banquet_quotation
#: model:ir.ui.menu,name:banquet_managment.menu_banquet_quotation_form
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_tree_view
msgid "Banquet Quotation"
msgstr "Devis du banquet"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__banquets_ids
msgid "Banquet Quotation History"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__name
msgid "Banquet Quotation No."
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.open_banquet_reservation_form_tree
msgid "Banquet Reservation"
msgstr "Réservation de banquet"

#. module: banquet_managment
#: model:ir.ui.menu,name:banquet_managment.menu_theme_plan_tree
msgid "Banquet Theme"
msgstr "thème de banquet"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Banquet Type"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Basic Info"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__board_toread
msgid "Board to Read"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Booking Details"
msgstr "Détails de réservation"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__booking_id
msgid "Booking Ref"
msgstr "RÀf. de rÀservation"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__banquet_id
msgid "Booking Ref."
msgstr "Réf. de réservation"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Cancel"
msgstr "Annuler"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Cancel Reservation"
msgstr "Annuler Réservation"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__category_id
msgid "Category"
msgstr "Catégorie"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__child
msgid "Child"
msgstr "Enfant"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__code
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__code
msgid "Code"
msgstr "Code"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__agent_comm
msgid "Commision"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__company_id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__company_id
msgid "Company"
msgstr "Société"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__confirm
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__confirm
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Confirm"
msgstr "confirmer"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__contact_name
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__contact_name
msgid "Contact Name"
msgstr "Contact"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__cost_price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__cost_price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__cost_price_unit
msgid "Cost Price"
msgstr "Prix de revient"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__cost_price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__cost_price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__cost_price_subtotal
msgid "Cost Subtotal"
msgstr "Sous total prix de revient"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Create Banquet Booking"
msgstr "Créer une réservation de banquet"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Create Folio"
msgstr "Créer Fiche de réservation"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Create Journal Entry"
msgstr "Créer Journal"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__create_uid
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__create_uid
msgid "Created by"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__create_date
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__create_date
msgid "Created on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__current_date
msgid "Creation Date"
msgstr "Créer Date"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__currency_id
msgid "Currency"
msgstr "devise"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__partner_id
msgid "Customer"
msgstr "client"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_recv_acc
msgid "Deposit Account"
msgstr "Compte d'arrhes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_cost
msgid "Deposit Cost"
msgstr ""

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.act_deposit_journal_entry
msgid "Deposit Journal Entry"
msgstr "Journal d'arrhes"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_deposit_policy_tree
#: model:ir.model,name:banquet_managment.model_deposit_payment_policy
#: model:ir.ui.menu,name:banquet_managment.menu_deposit_policy
msgid "Deposit Payment Policy"
msgstr "Condition de payement"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__deposit_policy__percentage
#: model:ir.model.fields.selection,name:banquet_managment.selection__hotel_reservation__deposit_policy__percentage
msgid "Deposit Percentage"
msgstr "Pourcentage d'arrhes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__deposit_policy
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__deposit_policy
#: model:ir.ui.menu,name:banquet_managment.menu_deposit_policy_tree
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_deposit_payment_policy_tree
msgid "Deposit Policy"
msgstr "Condition d'arrhes"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_deposit_journal_entry_wizard
msgid "Deposit_journal_entry Detail Wizard"
msgstr "Arrhe_journal_détail_Assistant"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__name
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__name
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__name
msgid "Description"
msgstr "Description"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__via__direct
#: model:ir.model.fields.selection,name:banquet_managment.selection__crm_lead__via__direct
msgid "Direct"
msgstr "Directe"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__discount
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__discount
msgid "Discount (%)"
msgstr "Remise(%)"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__display_name
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__display_name
msgid "Display Name"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__done
msgid "Done"
msgstr "Effectué"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__draft
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__email_id
msgid "Email Id"
msgstr "Mail"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__current_date
msgid "Enquiry Date"
msgstr "Date"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Event Information"
msgstr "Information évènement"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Food Details"
msgstr "Commandes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__food_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__food_items_id
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__food_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__other_items_ids
msgid "Food Items"
msgstr "Articles"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_food_items
msgid "Food Items Details"
msgstr "Détails articles"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Food List"
msgstr "Liste commandes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__start_date
msgid "From Date"
msgstr "A partir de"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__ref_id
#: model_terms:ir.ui.view,arch_db:banquet_managment.itinerary_lead_history_tree_view
msgid "History"
msgstr "Historique"

#. module: banquet_managment
#: model:res.groups,name:banquet_managment.group_banquet_user
msgid "Hotel Management / Banquet User"
msgstr "Gestion d'hotel / Employé"

#. module: banquet_managment
#: model:res.groups,name:banquet_managment.group_banquet_manager
msgid "Hotel Management/ Banquet Manager"
msgstr "Gestion d'hotel / Manageur"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__id
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__id
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__id
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__id
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__id
msgid "ID"
msgstr "ID"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "ID Details"
msgstr "ID Détails"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__invoiced
msgid "Invoiced"
msgstr "Facturé"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_room__deposit_bool
msgid "Is Deposit Applicable"
msgstr "Arrhes"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Item Deatails"
msgstr "Details articles"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__journal_id
msgid "Journal"
msgstr "Journal"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_food_items____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_other_items____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan____last_update
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan____last_update
msgid "Last Modified on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__update_date
msgid "Last Updated Date"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__write_uid
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__write_uid
msgid "Last Updated by"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__write_date
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__write_date
msgid "Last Updated on"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__lead
msgid "Lead"
msgstr "Piste"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__lead_sequence
msgid "Lead Number"
msgstr "Nombre Piste"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_crm_lead2opportunity_partner
msgid "Lead To Opportunity Partner"
msgstr "Partenaire Piste vers Opportunité"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__min_dep_amount
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__min_amount
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__min_dep_amount
msgid "Minimum Deposit Amount"
msgstr "Montant minimum des arrhes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__mobile
msgid "Mobile Number"
msgstr "Mobile"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_seating_plan__name
msgid "Name"
msgstr "Nom"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__deposit_policy__no_deposit
#: model:ir.model.fields.selection,name:banquet_managment.selection__hotel_reservation__deposit_policy__no_deposit
msgid "No Deposit"
msgstr "Pas d'arrhes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__number_of_days
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__number_of_days
msgid "Number Of Days"
msgstr "Nombre Jours"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__number_of_rooms
msgid "Number Of Rooms"
msgstr "Nombre Chambre"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Order List"
msgstr "Liste de commande"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Other Item List"
msgstr "Autres Liste articles"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__other_items_ids
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__other_items_id
msgid "Other Items"
msgstr "Autres articles"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_other_items
msgid "Other Items Details"
msgstr "Autres détails d'articles"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Other Services"
msgstr "Autres services"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__payment_date
msgid "Payment Date"
msgstr "Date de paiement"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__percentage
msgid "Percentage"
msgstr "Pourcentage"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__percentage
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__percentage
msgid "Percentage/Deposit Amount"
msgstr "Pourcentage /Montant d'arrhe"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
msgid "Percentage/Deposit Amt"
msgstr "Percentage/Deposit Amt"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__name
msgid "Policy Name"
msgstr "Nom Stratégie"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_deposit_payment_policy_policy_name_uniq
msgid "Policy Name must be unique for selected shop !"
msgstr "Nom de la stratégie doit être unique pour le magasin sélectionné !"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__checkout_date
msgid "Prefer End Date"
msgstr "Date de fin"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__checkin_date
msgid "Prefer start Date"
msgstr "Date de début"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_banquet_quotation_check_in_out_dates
msgid "Prefer start Date Should be lesser than the Prefer End Date!"
msgstr "Date de début doit être antérieure à la date de fin"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pricelist_id
msgid "Pricelist"
msgstr "Tarifs"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_id
msgid "Product"
msgstr "Produit"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__pur_tax_ids
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__pur_tax_ids
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__pur_tax_ids
msgid "Purchase Taxes"
msgstr "Taxes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_tax_amt
msgid "Purchase Taxes "
msgstr "Taxes"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_total_amt
msgid "Purchase Total Amount"
msgstr "Montant total"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__pur_untax_amt
msgid "Purchase Untaxed Amount"
msgstr "Montant hors taxe"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_uom_qty
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_uom_qty
msgid "Quantity (UoM)"
msgstr "Quantité (UOM)"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__name
msgid "Quotation No."
msgstr "No Devis"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Refuse"
msgstr "Refusé"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__refused
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__refused
msgid "Refused"
msgstr "Refusé"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_open_hotel_reservation_form_tree
#: model:ir.actions.act_window,name:banquet_managment.hotel_management_open_hotel_reservation_form_tree11
#: model:ir.model,name:banquet_managment.model_hotel_reservation
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_form
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_reservation_graph
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_banquet_reservation_tree
msgid "Reservation"
msgstr "Réservation"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_hotel_reservation_line
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Reservation Line"
msgstr "Ligne réservation"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__total_tax
msgid "Reservation Tax"
msgstr "réservation Tax"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__room_ids
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Room Details"
msgstr "Descriptions chambre"

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Room Type"
msgstr "Type chambre"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_tax_amt
msgid "Sale Taxes "
msgstr "Taxes de vente"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_total_amt
msgid "Sale Total Amount"
msgstr "Montant total de vente"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__sale_untax_amt
msgid "Sale Untaxed Amount"
msgstr "Montant de vente hors taxe"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_seating_plan_theme_name_uniq
msgid "Seating Name must be unique !"
msgstr "Nom d'une place assise doit être unique !"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_seating_plan_tree
#: model:ir.model,name:banquet_managment.model_seating_plan
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__seating_id
#: model:ir.ui.menu,name:banquet_managment.menu_seating_plan_tree
msgid "Seating Plan"
msgstr "Plan de table"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation_lead_history__state__send_to
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Send To Customer"
msgstr "Envoyer au client"

#. module: banquet_managment
#: model:ir.model.fields.selection,name:banquet_managment.selection__banquet_quotation__state__send_to
msgid "Sent To Customer"
msgstr "Envoyer au client"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_journal_entry_wizard__service_cost
msgid "Service Cost"
msgstr "Coût du Service"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_banquet_quotation__number_of_days
msgid ""
"Shall be computed based on check out policy configured for selected shop."
msgstr "Doit être calculer sur la base des conditions de départ pour la boutique sélectionnée."

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__shop_id
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__shop_id
#: model:ir.model.fields,field_description:banquet_managment.field_deposit_payment_policy__shop_id
msgid "Shop"
msgstr "Boutique"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__purches_bol
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__purches_bol
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__purches_bol
msgid "Show Purchase Tax"
msgstr "Afficher la taxe d'achat"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_deposit_payment_policy_start_date_uniq
msgid "Start Date must be unique for selected shop !"
msgstr "Date début doit être unique"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__state
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation_lead_history__state
msgid "Status"
msgstr "Statut"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation_line__sub_total
msgid "Sub Total"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__price_subtotal
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__price_subtotal
msgid "Subtotal"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.banquet_quotation_form_view
msgid "Tax On Product"
msgstr "Taxe sur les produits"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__tax_id
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__tax_id
msgid "Taxes"
msgstr "Taxes"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_hotel_reservation__total_tax
msgid "The amount without tax."
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__theme_id
msgid "Theme"
msgstr "Thème"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_theme_plan__name
msgid "Theme Name"
msgstr "Nom thème"

#. module: banquet_managment
#: model:ir.model.constraint,message:banquet_managment.constraint_theme_plan_theme_name_uniq
msgid "Theme Name must be unique !"
msgstr "Le nom du thème doit être unique"

#. module: banquet_managment
#: model:ir.actions.act_window,name:banquet_managment.action_theme_plan_tree
#: model:ir.model,name:banquet_managment.model_theme_plan
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_theme_plan_tree
msgid "Theme Plan"
msgstr "Plan thème"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__total_cost1
msgid "Total Reservation cost"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__price_unit
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__price_unit
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_hotel_reservation__untaxed_amt
msgid "Untaxed Amount"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_food_items__product_uom
#: model:ir.model.fields,field_description:banquet_managment.field_other_items__product_uom
msgid "UoM"
msgstr "UoM"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_crm_lead
msgid "User Modification"
msgstr ""

#. module: banquet_managment
#: model:ir.model.fields,field_description:banquet_managment.field_banquet_quotation__via
#: model:ir.model.fields,field_description:banquet_managment.field_crm_lead__via
msgid "Via"
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.deposit_journal_entry_wizard
msgid "Visa Journal Entry"
msgstr "Journal des visas"

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_food_items__product_id
msgid ""
"Will list out all food items that belong to company of selected shop. \n"
" It also shows global product as well."
msgstr "Affichera tous les articles de la boutique sélectionnée. \n"
" Il affiche également le produit global."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_banquet_quotation__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user."
msgstr "Affichera la liste des boutiques de l'utilisateur connecté."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_deposit_payment_policy__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assign a shop to configure shop-wise deposit  policy."
msgstr "Affichera la liste des boutiques de l'utilisateur connecté. \n"
"   -Assigner une boutique pour configurer la stratégie d'arrhes."

#. module: banquet_managment
#: model:ir.model.fields,help:banquet_managment.field_crm_lead__shop_id
msgid "Will show only open leads for the selected shop."
msgstr "N'affichera que les pistes ouvertes pour la boutique sélectionné."

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_banquet_quotation_lead_history
msgid "itinerary lead history"
msgstr "Historique d'Itinéraire"

#. module: banquet_managment
#: model:ir.model,name:banquet_managment.model_hotel_room
msgid "room Inherit "
msgstr ""

#. module: banquet_managment
#: model_terms:ir.ui.view,arch_db:banquet_managment.view_seating_plan_tree
msgid "seating Plan"
msgstr "Plan de table"
