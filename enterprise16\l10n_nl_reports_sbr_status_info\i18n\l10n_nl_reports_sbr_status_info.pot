# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_reports_sbr_statusinformatieservice
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-15 15:41+0000\n"
"PO-Revision-Date: 2024-04-15 15:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid "%s status retrieval failed"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid "%s submission failed"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid "%s submission succeeded"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_icp_icp_wizard__password
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_tax_report_wizard__password
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_res_company__l10n_nl_reports_sbr_password
msgid "Certificate or private key password"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model,name:l10n_nl_reports_sbr_statusinformatieservice.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_icp_icp_wizard__company_id
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__company_id
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_tax_report_wizard__company_id
msgid "Company"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__create_date
msgid "Created on"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__id
msgid "ID"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__is_test
msgid "Is it a test?"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__is_done
msgid "Is the cycle finished?"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model,name:l10n_nl_reports_sbr_statusinformatieservice.model_l10n_nl_reports_sbr_tax_report_wizard
msgid "L10n NL Tax Report for SBR Wizard"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__kenmerk
msgid "Message Exchange ID"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__report_name
msgid "Name of the submitted report"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.actions.server,name:l10n_nl_reports_sbr_statusinformatieservice.cron_l10n_nl_reports_status_process_ir_actions_server
#: model:ir.cron,cron_name:l10n_nl_reports_sbr_statusinformatieservice.cron_l10n_nl_reports_status_process
msgid "Process statusses of the unfinished submitted SBR reports"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_status_service__closing_entry_id
msgid "Related closing entry"
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model.fields,help:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_icp_icp_wizard__password
#: model:ir.model.fields,help:l10n_nl_reports_sbr_statusinformatieservice.field_l10n_nl_reports_sbr_tax_report_wizard__password
msgid "The password is not needed for just printing the XBRL file."
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid ""
"The status retrieval for the %s with discussion id '%s' failed with the "
"error:<br/><br/><i>%s</i><br/><br/>Try submitting your report again."
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid ""
"The submission for the %s with discussion id '%s' failed with the "
"error:<br/><br/><i>%s</i><br/><i>%s</i><br/><br/>Try submitting your report "
"again."
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_statusinformatieservice/models/l10n_nl_reports_sbr_status_service.py:0
#, python-format
msgid ""
"The submission for the %s with discussion id '%s' was successfully received "
"by Digipoort."
msgstr ""

#. module: l10n_nl_reports_sbr_statusinformatieservice
#: model:ir.model,name:l10n_nl_reports_sbr_statusinformatieservice.model_l10n_nl_reports_sbr_status_service
msgid "l10n_nl_reports_sbr.status.service"
msgstr ""
