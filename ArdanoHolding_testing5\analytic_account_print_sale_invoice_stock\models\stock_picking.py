from odoo import fields, models, api, _


class StockPickingInherit(models.Model):
    _inherit = 'stock.picking'

    cubes_analytic_account_id = fields.Many2one(
        'account.analytic.account',
        string='Analytic Account',
        compute='_compute_cubes_analytic_account_id',
        store=True
    )

    @api.depends('partner_id')
    def _compute_cubes_analytic_account_id(self):
        for rec in self:
            if rec.sale_id:
                rec.cubes_analytic_account_id = rec.sale_id.cubes_analytic_account_id.id
            else:
                rec.cubes_analytic_account_id = False
