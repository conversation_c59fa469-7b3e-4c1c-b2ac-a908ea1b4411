# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <ka<PERSON><PERSON><EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# G<PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 07:59+0000\n"
"PO-Revision-Date: 2016-08-05 12:55+0000\n"
"Last-Translator: <PERSON>sidis <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'In Payment' status is used when payments have been registered for the entirety of the invoice in a journal configured to post entries at bank reconciliation only, and some of them haven't been reconciled with a bank statement line yet.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__code_digits
msgid "# of Digits"
msgstr "# ψηφίων"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr ""
"${object.company_id.name|safe} Τιμολόγιο (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} Απόδειξη Πληρωμής (Ref ${object.name or 'n/a' })"

#. module: account
#: code:addons/account/models/account.py:944
#, python-format
msgid "%s (Copy)"
msgstr "%s (Αντίγραφο)"

#. module: account
#: code:addons/account/models/account.py:247
#: code:addons/account/models/account.py:251
#: code:addons/account/models/account.py:253
#: code:addons/account/models/account.py:604
#: code:addons/account/models/account.py:605
#, python-format
msgid "%s (copy)"
msgstr "%s (αντίγραφο)"

#. module: account
#: code:addons/account/models/account.py:695
#, python-format
msgid "%s Sequence"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>στις</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_chatter
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> Συμψηφισμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Προβολή μερικώς συμψηφισμένων εγγραφών"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 Ημέρες"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_2months
msgid "2 Months"
msgstr "2 μήνες"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "Πίστωση 30 Ημερών"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% προκαταβολή στο τέλος του επόμενου μήνα"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_45days
msgid "45 Days"
msgstr "45 ημέρες"

#. module: account
#: code:addons/account/models/account.py:693
#, python-format
msgid ": Refund"
msgstr ": Επιστροφή"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}<br/><br/>\n"
"        Thank you for your payment.\n"
"        Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting\n"
"        to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any question.\n"
"        <br/><br/>\n"
"        Best regards,<br/>\n"
"        % if user and user.signature:\n"
"        ${user.signature | safe}\n"
"        % endif\n"
"    </p>\n"
"</div>\n"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}\n"
"        % if object.partner_id.parent_id:\n"
"            (${object.partner_id.parent_id.name})\n"
"        % endif\n"
"        <br/><br/>\n"
"        Here is your \n"
"        % if object.number:\n"
"            invoice <strong>${object.number}</strong>\n"
"        % else:\n"
"            invoice\n"
"        %endif\n"
"        % if object.origin:\n"
"            (with reference: ${object.origin})\n"
"        % endif\n"
"        amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        from ${object.company_id.name}.\n"
"        % if object.state=='paid':\n"
"            This invoice is already paid.\n"
"        % else:\n"
"            Please remit payment at your earliest convenience.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any question.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "<em>Draft Invoice</em>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_move_line_view_kanban
#: model_terms:ir.ui.view,arch_db:account.view_account_move_kanban
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "Λήψη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-fw fa-comments\"/><b>Send message</b>"
msgstr "Αποστολή μηνύματος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Configure Email Servers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Εκτύπωση"

#. module: account
#: code:addons/account/models/account_invoice.py:714
#, python-format
msgid ""
"<p>You can control the invoice from your vendor based on what you purchased "
"or received.</p>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                        <strong>Email mass mailing</strong> on\n"
"                                        <span>the selected records</span>\n"
"                                    </span>\n"
"                                    <span>Followers of the document and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-clock-o\""
" aria-label=\"Opened\" title=\"Opened\" role=\"img\"/><span class=\"d-none d"
"-md-inline\"> Waiting for Payment</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\" aria-label=\"Paid\" title=\"Paid\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Paid</span></span>"
msgstr "Πληρωμένο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-warning\"><i class=\"fa fa-fw fa-"
"remove\" aria-label=\"Cancelled\" title=\"Cancelled\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Cancelled</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state','!=','draft'), ('type','!=','in_invoice')]}\">Draft Bill</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('sequence_number_next_prefix','=',False)]}\">- First Number:</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state','!=','draft'), ('type','!=','in_refund')]}\">Draft Credit Note</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_invoice')]}\">Bill </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_refund')]}\">Credit Note </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"['|',('state','=','draft'), ('type','!=','out_refund')]}\">Credit "
"Note</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('match_amount', '!=', "
"'between')]}\">and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"<span class=\"o_form_label\">All selected journal entries will be validated "
"and posted. You won't be able to modify them afterwards.</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Incoterm</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Default Taxes</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Localization</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Main Currency</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Rounding Method</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "<span class=\"o_form_label\">of the month</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Τιμολογήθηκαν</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid ""
"<span class=\"text-muted\">Only journals not yet linked to a bank account "
"are proposed</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                    <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span name=\"button_import_placeholder\"/> Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">New</span>"
msgstr "Νέο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">View</span>"
msgstr "Προβολή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in General Ledger</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Τελευταία Δήλωση</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> Από </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> Σε </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Amount Paid</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Balance</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Description</span>"
msgstr "Περιγραφή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Disc.(%)</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Import Bills</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Date</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Number</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>Ημερολογιακές Καταχωρήσεις</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Νέος Λογαριασμός</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Νέο Τιμολόγιο</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Nέο</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>Λειτουργίες</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Original Amount</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Quantity</span>"
msgstr "Ποσότητα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Αναφορά</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Source Document</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Taxes</span>"
msgstr "Φόροι"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Unit Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Προβολή</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">Scan me with your banking "
"app.</strong><br/><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">The SEPA QR Code informations are not set "
"correctly.</strong><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<strong class=\"text-muted\">Your Contact</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Οφειλόμενο Ποσό</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Company:</strong>"
msgstr "<strong>Εταιρεία:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Κωδικός Πελάτη:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Customer: </strong>"
msgstr "<strong>Πελάτης: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>Περιγραφή:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Ημερ. Λήξης:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Καταχωρήσεις Ταξινομημένες κατά:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>Λήξη Λογιστικής Χρήσης</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Ημερομηνία Τιμολογίου:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Ημερολόγιο:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Memo: </strong>"
msgstr "<strong>Υπόμνημα: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Ποσό Πληρωμής: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Ημερ. Πληρωμής: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Μέθοδος Πληρωμής: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Αναφορά:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Προέλευση:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr " <strong>Μερικό σύνολο</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Κινήσεις :</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Σύνολο</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Τύπος: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Προμηθευτής: </strong>"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"Η ταμειακή μηχανή σας επιτρέπει να διαχειριστείτε τις καταχωρήσεις με μετρητά\n"
"                σε ημερολόγια μετρητών. Αυτή η λειτουργία παρέχει έναν εύκολο τρόπο παρακολούθησης\n"
"                των πληρωμών μετρητών σε καθημερινή βάση. Μπορείτε να εισάγετε τα κέρματα που  \n"
"                βρίσκονται στο κουτί μετρητών σας και στη συνέχεια να καταχωρήσετε τις εγγραφές όταν \n"
"                μετρητά μπαίνουν ή βγαίνουν από το κουτί.   "

#. module: account
#: code:addons/account/models/account_invoice.py:1938
#, python-format
msgid "A Payment Term should have only one line of type Balance."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:861
#, python-format
msgid "A bank account can belong to only one journal."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"Μια δήλωση τραπεζικού λογαριασμού είναι μια σύνοψη όλων των χρηματοοικονομικών\n"
"                συναλλαγών που συμβαίνουν σε μια δεδομένη χρονική περίοδο σε έναν τραπεζικό \n"
"                λογαριασμό. Εσείς θα πρέπει περιοδικά να λαμβάνετε αυτήν από την τράπεζά σας."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"Μια καταχώριση ημερολογίου αποτελείται από διάφορες εγγραφές ημερολογίου, "
"κάθε μία από τις οποίες είναι είτε χρεωστική είτε πιστωτική."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"Ένα ημερολόγιο χρησιμοποιείται για την καταγραφή όλων των λογιστικών δεδομένων\n"
"                που σχετίζονται με την καθημερινή επιχειρηματική δραστηριότητα."

#. module: account
#: code:addons/account/models/account_move.py:315
#, python-format
msgid ""
"A payment journal entry generated in a journal configured to post entries "
"only when payments are reconciled with a bank statement cannot be manually "
"posted. Those will be posted automatically after performing the bank "
"reconciliation."
msgstr ""

#. module: account
#: code:addons/account/models/reconciliation_widget.py:692
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Ένας συμψηφισμός πρέπει να περιλαμβάνει τουλάχιστον 2 κινήσεις."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"Μια στρογγυλοποίηση ανά γραμμή συνιστάται εάν οι τιμές σας "
"συμπεριλαμβάνονται στο φόρο. Με αυτό τον τρόπο, το άθροισμα των υποσύνολων "
"γραμμών ισούται με το σύνολο με τους φόρους."

#. module: account
#: code:addons/account/models/account_bank_statement.py:537
#: code:addons/account/models/account_bank_statement.py:540
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Η επιλεγμένη γραμμή κίνησης ήταν ήδη συμψηφισμένη."

#. module: account
#: code:addons/account/models/account_bank_statement.py:548
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""
"Μια επιλεγμένη γραμμή δήλωσης είχε ήδη συμψηφιστεί με μια κίνηση του "
"λογαριασμού."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only one time on same taxes."
msgstr ""

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "Ορισμός προειδοποίησης σε συνεργάτη (Τιμολόγηση)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_warning
msgid "Access warning"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:546
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:548
#: code:addons/account/static/src/xml/account_reconciliation.xml:181
#: code:addons/account/static/src/xml/account_reconciliation.xml:284
#: code:addons/account/static/src/xml/account_reconciliation.xml:309
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__account_id
#: model:ir.model.fields,field_description:account.field_account_invoice__account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__account_id
#: model:ir.model.fields,field_description:account.field_account_move__dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__account_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Λογαριασμός"

#. module: account
#: code:addons/account/models/account_move.py:905
#, python-format
msgid ""
"Account %s (%s) does not allow reconciliation. First change the "
"configuration of this account to allow it."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Αναφορά Κοινού Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template__currency_id
msgid "Account Currency"
msgstr "Νόμισμα Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_dest_id
msgid "Account Destination"
msgstr "Λογαριασμός Προορισμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Εγγραφή Λογαριασμού"

#. module: account
#: model:ir.model,name:account.model_account_group
#: model_terms:ir.ui.view,arch_db:account.view_account_group_form
#: model_terms:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Ομάδα Λογαριασμού"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Ομάδες Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__company_partner_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__partner_id
msgid "Account Holder"
msgstr "Κάτοχος λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_holder_name
msgid "Account Holder Name"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank__journal_id
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Ημερολόγιο Λογαριασμού"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "Account Journal Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__account_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Χαρτογράφηση λογαριασμών"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "Αντιλογισμός Κίνησης Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_acc_number
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_number
msgid "Account Number"
msgstr "Αριθμός Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_payable_id
msgid "Account Payable"
msgstr "Λογαριασμός Πληρωτέος"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Εκτύπωση Ημερολογίου Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Ιδιότητες Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_receivable_id
msgid "Account Receivable"
msgstr "Λογαριασμός Εισπρακτέος"

#. module: account
#: model:ir.model,name:account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_src_id
msgid "Account Source"
msgstr "Πηγή Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "Στατιστικά Λογαριασμού"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Ετικέτα Λογαριασμού"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Ετικέτες Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Φόρος Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Πρότυπο Λογαριασμού Φόρων"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr "Λογαριασμός  TaxCloud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Πρότυπο Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Πρότυπο Λογαριασμού για Αποτίμηση Αποθέματος"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Πρότυπα Λογαριασμών"

#. module: account
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__related_acc_type
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_account_type_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Τύπος Λογαριασμού"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__user_type_id
#: model:ir.model.fields,help:account.field_account_move_line__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Ο Τύπος Λογαριασμού έχει πληροφοριακή χρησιμότητα. Παράγει νομικές αναφορές "
"εξειδικευμένες για την χώρα, θέτει τους κανόνες για το κλείσιμο ενός "
"οικονομικού έτους και επίσης παράγει εναρκτήριες εγγραφές."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
msgid "Account Types"
msgstr "Τύποι Λογαριασμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__type_control_ids
msgid "Account Types Allowed"
msgstr "Επιτρεπόμενοι Τύποι Λογαριασμών"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Μη συμψηφιστικός λογαριασμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Ομάδα λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Ομάδες λογαριασμού"

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_holder_name
msgid ""
"Account holder name, in case it is different than the name of the Account "
"Holder"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_src_id
msgid "Account on Product"
msgstr "Λογαριασμός στο Είδος"

#. module: account
#: model:ir.model,name:account.model_report_account_report_invoice_with_payments
msgid "Account report with payment lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__tag_ids
msgid "Account tag"
msgstr "Ετικέτα λογαριασμού"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""
"Λογαριασμός που θα οριστεί στις γραμμές φόρου τιμολόγησης για πιστωτικά "
"σημειώματα. Αφήστε κενό για να χρησιμοποιήσετε το λογαριασμό εξόδων."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__account_id
#: model:ir.model.fields,help:account.field_account_tax_template__account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""
"Λογαριασμός που θα οριστεί στις γραμμές φόρου τιμολόγησης για τιμολόγια. "
"Αφήστε κενό για να χρησιμοποιήσετε το λογαριασμό εξόδων."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template__refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""
"Λογαριασμός που θα οριστεί στις γραμμές φόρου τιμολόγησης για επιστροφές. "
"Αφήστε κενό για να χρησιμοποιήσετε το λογαριασμό εξόδων."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_dest_id
msgid "Account to Use Instead"
msgstr "Εναλλακτικός Λογαριασμός για Χρήση"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_account_id
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr ""
"Λογαριασμός που χρησιμοποιείται ως αντίστοιχο για την καταχώρηση "
"ημερολογίου, για εφαρμόσιμους φόρους βάσει των πληρωμών."

#. module: account
#: model:ir.actions.server,name:account.ir_cron_reverse_entry_ir_actions_server
#: model:ir.cron,cron_name:account.ir_cron_reverse_entry
#: model:ir.cron,name:account.ir_cron_reverse_entry
msgid "Account; Reverse entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_accountant
#: model:ir.ui.menu,name:account.account_account_menu
#: model:ir.ui.menu,name:account.menu_finance_entries
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_form_inherit_account
msgid "Accounting"
msgstr "Λογιστική"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "Επιλογές Εφαρμογής Λογιστικής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__accounting_date
#: model:ir.model.fields,field_description:account.field_account_invoice__date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__date
msgid "Accounting Date"
msgstr "Ημερ. Λογιστικής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Εντυπα Λογιστικής"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Λογιστικές Εγγραφές"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Overview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "Οι ρυθμίσεις που σχετίζονται με λογιστική διαχειρίζονται σε "

#. module: account
#: selection:account.account.tag,applicability:0
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Λογαριασμοί"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__account_control_ids
msgid "Accounts Allowed"
msgstr "Επιτρεπόμενοι Λογαριασμοί"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Χαρτογράφηση Λογαριασμών"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Accounts Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Mapping of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,field_description:account.field_account_invoice__message_needaction
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "Ενέργειες"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Ενεργοποίηση 'Aλλων Nομισμάτων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__active
#: model:ir.model.fields,field_description:account.field_account_incoterms__active
#: model:ir.model.fields,field_description:account.field_account_journal__active
#: model:ir.model.fields,field_description:account.field_account_payment_term__active
#: model:ir.model.fields,field_description:account.field_account_tax__active
#: model:ir.model.fields,field_description:account.field_account_tax_template__active
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Σε Ισχύ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__active_domain
msgid "Active domain"
msgstr "Ενεργός τομέας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_ids
#: model:ir.model.fields,field_description:account.field_account_payment__activity_ids
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_state
#: model:ir.model.fields,field_description:account.field_account_payment__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "Προσθήκη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "Προσθήκη Πιστωτικού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__add_sign
msgid "Add Sign"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Add a bank"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid "Add a journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a line"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid "Add a new account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.account_tag_action
msgid "Add a new tag"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Add a payment QR code to your invoices"
msgstr ""

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "Προσθέστε μια γραμμή στρογγυλοποίησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__has_second_line
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Προσθήκη δεύτερης γραμμής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a section"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Add an EPC QR code to your invoices so that your customers can pay instantly"
" with their mobile banking application. EPC QR codes are used by many "
"European banks to process SEPA payments."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "Προσθήκη εσωτερικής σημείωσης..."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Add contacts to notify..."
msgstr "Προσθήκη επαφών για ενημέρωση..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__partner_ids
msgid "Additional Contacts"
msgstr "Πρόσθετες επαφές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__comment
msgid "Additional Information"
msgstr "Επιπλέον Πληροφορίες"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "Πρόσθετες σημειώσεις..."

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "Adjustment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__tax_id
msgid "Adjustment Tax"
msgstr "Προσαρμογή Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__adjustment_type
msgid "Adjustment Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Ρυθμίσεις για προχωρημένους"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Ρυθμίσεις για προχωρημένους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Επηρεάζουν με βάση τους μεταγενέστερους φόρους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Επηρεάζει τους μεταγενέστερους φόρους"

#. module: account
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
msgid "Aged Partner Balance"
msgstr "Ενηλικίωση Υπολοίπων Συνεργατών"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_id
msgid "Alias"
msgstr "Ψευδώνυμο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_name
msgid "Alias Name for Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_domain
msgid "Alias domain"
msgstr "Ψευδώνυμο τομέα"

#. module: account
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Entries"
msgstr "Όλες οι Εγγραφές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Όλες οι Γραμμές Συμψηφίστηκαν"

#. module: account
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Posted Entries"
msgstr "Όλες οι Καταχωρημένες Εγγραφές"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:49
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Όλα τα τιμολόγια και οι πληρωμές έχουν αντιστοιχιστεί, τα υπόλοιπα των "
"λογαριασμών σας είναι καθαρά."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"Όλες οι νέες ημερολογιακές καταχωρήσεις που δημιουργήθηκαν αυτόματα "
"εντάσσονται στην κατάσταση 'Μη Καταχωρημένη', αλλά μπορείτε να ορίσετε την "
"επιλογή να προσπεράσετε αυτή την κατάσταση στο σχετικό ημερολόγιο. Σε αυτή "
"την περίπτωση, θα φέρονται ως ημερολογιακές καταχωρήσεις αυτόματα "
"δημιουργημένες από το σύστημα στην επικύρωση εγγράφου (τιμολόγια, τραπεζικές"
" καταθέσεις...) και θα δημιουργούνται στην κατάσταση 'Καταχωρημένη'."

#. module: account
#: code:addons/account/models/account_bank_statement.py:243
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""
"Όλες οι γραμμές καταχωρήσεων των λογαριασμών πρέπει να επεξεργαστούν "
"προκειμένου να κλείσει η κατάσταση."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__update_posted
msgid "Allow Cancelling Entries"
msgstr "Επιτρέπεται η Ακύρωση Εγγραφών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Επιτρέψτε αντιστοίχηση Τιμολογίων & Πληρωμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr "Επιτρέψτε το Περιθώριο Είδους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__reconcile
msgid "Allow Reconciliation"
msgstr "Να Επιτραπεί Συμφωνία"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr "Επιτρέψτε την εκτύπωση επιταγών και τις καταθέσεις"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "Επιτρέψτε την διαχείριση στρογγυλοποίησης μετρητών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "Επιτρέψτε να υπολογιστούν οι φόροι κατά την είσπραξη."

#. module: account
#: model:res.groups,name:account.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows to tag analytic entries and to manage analytic distributions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Σας επιτρέπει να χρησιμοποιείτε αναλυτική λογιστική."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:208
#: code:addons/account/static/src/xml/account_reconciliation.xml:291
#: code:addons/account/static/src/xml/account_reconciliation.xml:308
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount
#: model:ir.model.fields,field_description:account.field_account_move__amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount
#: model:ir.model.fields,field_description:account.field_account_tax__amount
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount
#: model:ir.model.fields,field_description:account.field_cash_box_in__amount
#: model:ir.model.fields,field_description:account.field_cash_box_out__amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__amount
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Ποσό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_total
msgid "Amount (with Taxes)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_subtotal
msgid "Amount (without Taxes)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_currency
msgid "Amount Currency"
msgstr "Ποσό Σχετ. Νομίσματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount_rounding
msgid "Amount Delta"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "Οφειλόμενο Ποσό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "Ποσό Οφειλόμενο στο Νόμισμα της Εταιρείας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "Ποσό Οφειλόμενο στο Νόμισμα του Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount
msgid "Amount Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount_param
msgid "Amount Matching %"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_max
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_max
msgid "Amount Max Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_min
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_min
msgid "Amount Min Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_nature
msgid "Amount Nature"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Paid"
msgstr "Ποσό που Καταβλήθηκε"

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Paid/Received"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Received"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_subtotal_signed
msgid "Amount Signed"
msgstr "Υπογεγραμμένο Ποσό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount_total
msgid "Amount Total"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_type
msgid "Amount Type"
msgstr "Τύπος Ποσού"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "Ποσό που αφορά αυτή η αντιστοίχιση. Θεωρείται ότι είναι πάντα θετικό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount_currency
msgid "Amount in Currency"
msgstr "Ποσό σε νόμισμα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Τύπος Ποσού"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "Ποσό:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only one time on same accounts."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"Ένας λογαριασμός είναι μέρος ενός συνολικού ποσού κατάθεσης επιτρέποντας "
"στην εταιρεία να εγγράψει όλα τα είδη χρεώσεων και πιστώσεων. Οι εταιρείες "
"παρουσιάζουν τους ετήσιους λογαριασμούς τους σε δύο κύρια μέρη: το φύλλο "
"κίνησης και το αντίγραφο εισοδημάτων (λογαριασμός κερδών και ζημιών). Οι "
"ετήσιοι λογαριασμοί μιας εταιρείας γίνονται απαιτητοί από τον νόμο να "
"αποκαλύπτουν μερικές πληροφορίες."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"Ένας τύπος λογαριασμού χρησιμοποιείται για να προσδιορίσει πως ένας λογαριασμός χρησιμοποιείται\n"
"                σε κάθε ημερολόγιο. Η μέθοδος αναβολής ενός τύπου λογαριασμού καθορίζει\n"
"                τη διαδικασία για το ετήσιο κλείσιμο. Αναφορές όπως Ισολογισμός\n"
"                 και η αναφορά Κερδών και Ζημιών χρησιμοποιούν την κατηγορία \n"
"                (κέρδος / ζημία ή ισολογισμός).  "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Αναλυτικοί"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#, python-format
msgid "Analytic Acc."
msgstr "Αναλυτική Λογ."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Αναλυτικός Λογαριασμός"

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_group_menu
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Account Groups"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Αναλυτική Λογιστική"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model_terms:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "Λογαριασμοί Αναλυτικής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__analytic
msgid "Analytic Cost"
msgstr "Αναλυτικό Κόστος"

#. module: account
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Items"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Γραμμή Αναλυτικής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Αναλυτικές Γραμμές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_tags
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Tags"
msgstr "Ετικέτες Αναλυτικής"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#, python-format
msgid "Analytic Tags."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__account_analytic_id
msgid "Analytic account"
msgstr "Αναλυτικός Λογαριασμός"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Αναλυτικές γραμμές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Αναλύσεις"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Οι απαντήσεις δεν πηγαίνουν στο αρχικό νήμα συζήτησης. Αυτό έχει ως επίπτωση"
" την δημιουργία νέου message-id."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_exigible
msgid "Appears in VAT report"
msgstr "Εμφανίζεται στη έκθεση ΦΠΑ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__applicability
msgid "Applicability"
msgstr "Εφαρμοσιμότητα"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr ""

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_invoice_layout_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Apply"
msgstr "Εφαρμογή"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Αυτόματη εφαρμογή αυτής της χρηματοοικονομικής θέσης"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""
"Εφαρμογή μόνο αν η χώρα παράδοσης ή τιμολόγησης ταιριάζουν με αυτή την "
"ομάδα."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "Εφαρμογή μόνο εάν η χώρα αποστολής ή η χώρα τιμολόγησης ταιριάζουν."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__vat_required
msgid "Apply only if partner has a VAT number."
msgstr "Εφαρμογή μόνο εάν ο συνεργάτης έχει αριθμό ΑΦΜ."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""
"Εφαρμόστε τους σωστούς συντελεστές ΦΠΑ για ψηφιακά προϊόντα που πωλούνται "
"στην ΕΕ"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "Απρίλιος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "Αίτημα για Πιστωτικό Τιμολόγιο"

#. module: account
#: selection:account.account.type,internal_group:0
msgid "Asset"
msgstr "Πάγιo"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Assets"
msgstr "Πάγια"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_asset
msgid "Assets Management"
msgstr "Διαχείριση Παγίων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__account_ids
msgid "Associated Account Templates"
msgstr "Συσχετισμένα Πρότυπα Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Τουλάχιστον Ένα Εισερχόμενο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Τουλάχιστον Ένα Εξερχόμενο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Attach a file"
msgstr "Επισύναψη αρχείου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_invoice__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_payment__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__attachment_ids
msgid "Attachments"
msgstr "Συνημμένα"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Τα συνημμένα συνδέονται με ένα έγγραφο μέσω του model / res_id και στο "
"μήνυμα μέσα από αυτό το πεδίο."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "Αύγουστος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_id
msgid "Author"
msgstr "Συντάκτης"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Συντάκτης του μηνύματος. Εάν δεν ρυθμιστεί, email από αυτές τις email "
"διευθύνσεις που δεν ταιριάζουν σε κανένα συνεργάτη."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_avatar
msgid "Author's avatar"
msgstr "Άβαταρ Συντάκτη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-Complete"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__vendor_bill_id
msgid "Auto-complete from a past bill."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "Αυτόματη Ανίχνευση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__auto_reconcile
msgid "Auto-validate"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Autocomplete Vendor Bills (OCR + AI)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_invoice_extract
msgid "Automate Bill Processing"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr ""
"Αυτοματοποιήστε τις καταχωρημένες εισπράξεις εσόδων για πολυετείς συμβάσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "Αυτοματοποιημένες Εγγραφές"

#. module: account
#: code:addons/account/models/company.py:430
#, python-format
msgid "Automatic Balancing Line"
msgstr "Αυτόματη Γραμμή Εξισορρόπησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Αυτόματες Τιμές Νομίσματος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "Αυτόματη Εισαγωγή"

#. module: account
#: code:addons/account/models/account_move.py:390
#, python-format
msgid "Automatic reversal of: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_average
msgid "Average Price"
msgstr "Μέση Τιμή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_price_average
msgid "Average Price in Currency"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:419
#, python-format
msgid "BILL"
msgstr "BILL"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "Κακός Οφειλέτης"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line__balance
msgid "Balance"
msgstr "Υπόλοιπο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__balance_cash_basis
msgid "Balance Cash Basis"
msgstr "Υπόλοιπο Ταμειακής Βάσης"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr ""
"Υπόλοιπο όπως υπολογίστηκε με βάση το υπόλοιπο εκ μεταφοράς και τις γραμμές "
"συναλλαγής"

#. module: account
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:351
#: model:ir.model.fields,field_description:account.field_account_journal__bank_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_id
#: model:ir.model.fields,field_description:account.field_res_partner__bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users__bank_account_count
#, python-format
msgid "Bank"
msgstr "Τράπεζα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Τράπεζες &amp; Μετρητά"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice__partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal__bank_account_id
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Bank Account"
msgstr "Τραπεζικός Λογαριασμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Όνομα Τραπεζικού Λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_number
msgid "Bank Account Number"
msgstr "Αριθμός Τραπεζικού Λογαριασμού"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Αριθμός τραπεζικού λογαριασμού στον οποίο θα καταβληθεί το τιμολόγιο. Ένας "
"τραπεζικός λογαριασμός της εταιρείας εάν πρόκειται για τιμολόγιο πελάτη ή "
"πιστωτικό σημείωμα προμηθευτή, διαφορετικά ένας αριθμός τραπεζικού "
"λογαριασμού συνεργάτη."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Bank Accounts"
msgstr "Τραπεζικοί Λογαριασμοί"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_statements_source
msgid "Bank Feeds"
msgstr "Tραπεζικές Tροφοδοσίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_bic
msgid "Bank Identifier Code"
msgstr "Κωδικός Αναγνώρισης Τράπεζας"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "Bank Interface - Συγχρονίστε τις τροφοδοσίες τράπεζας σας αυτόματα"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__bank_journal_ids
msgid "Bank Journals"
msgstr "Ημερολόγια Τράπεζας"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "Λειτουργίες Τράπεζας"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Συμφωνία Τραπεζικών Λογαριασμών Μετακίνηση Προεπιλογών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Συμφωνία Τραπεζικών Λογαριασμών πρoκαθορισμός Μετακίνησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Κατάσταση Κίνησης Τραπεζικού Λογαριασμού"

#. module: account
#: code:addons/account/models/account_bank_statement.py:602
#, python-format
msgid "Bank Statement %s"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Bank Statement Cashbox"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Bank Statement Closing Balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Γραμμή Κίνησης Τραπεζικού Λογαριασμού"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Γραμμές Τραπεζικής Δήλωσης"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Καταστάσεις Τραπεζικών Λογαριασμών"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__bank_account_id
msgid "Bank account that was used in this transaction."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_type
msgid ""
"Bank account type: Normal or IBAN. Inferred from the bank account number."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "Τράπεζα και Μετρητά"

#. module: account
#: model:ir.model,name:account.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Η γραμμή τραπεζικής δήλωσης συμψηφίστηκε με αυτή την καταχώρηση"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "Δηλώσεις Τράπεζας"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:36
#, python-format
msgid "Bank: Balance"
msgstr "Υπόλοιπο Τράπεζας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__base
msgid "Base"
msgstr "Βάση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_base_amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Βασικό Ποσό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:19
#, python-format
msgid "Based on Customer"
msgstr ""

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "Με Βάση το Τιμολόγιο"

#. module: account
#: code:addons/account/models/company.py:19
#, python-format
msgid "Based on Invoice Number"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template__tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"Με βάση το τιμολόγιο: ο φόρος οφείλεται μόλις επικυρωθεί το τιμολόγιο.\n"
"Με βάση την πληρωμή: ο φόρος οφείλεται μόλις παραληφθεί η πληρωμή του τιμολογίου."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "Με Βάση την Πληρωμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__belongs_to_company
msgid "Belong to the user's current company"
msgstr "Ανήκουν στην τρέχουσα εταιρεία του χρήστη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "Χρέωση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "Ημερ. Χρέωσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "Γραμμές Χρέωσης"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Χρεώσεις"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "Υπεύθυνος Χρεώσεων"

#. module: account
#: model:ir.actions.server,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Λογαριασμοί"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Ανάλυση Λογαριασμών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Pay"
msgstr "Λογαριασμοί για Πληρωμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Validate"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:178
#, python-format
msgid "Bills to pay"
msgstr "Λογαριασμοί για πληρωμή"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "Μήνυμα Αποκλεισμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type__include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "Φέρτε το Υπόλοιπο των Λογαριασμών προς τα Εμπρός"

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Αναζήτηση διαθέσιμων χωρών"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_budget
msgid "Budget Management"
msgstr "Διαχείριση Προϋπολογισμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__name
msgid "Button Label"
msgstr "Ετικέτα Κουμπιού"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Κατά Χώρα"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "Με Πιστωτικό Τιμολόγιο"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Ανά Είδος"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Κατά Κατηγορία Είδους"

#. module: account
#: model:ir.filters,name:account.filter_invoice_report_salespersons
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "Με πωλητές "

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"Αποεπιλέγοντας το ενεργό πεδίο, μπορείτε να αποκρύψετε μια χρηματοοικονομική"
" θέση χωρίς να την διαγράψετε."

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""
"Αποεπιλέγοντας το ενεργό πεδίο, μπορείτε να αποκρύψετε έναν διεθνή εμπορικό "
"όρο  χωρίς να τον διαγράψετε."

#. module: account
#: code:addons/account/models/chart_template.py:398
#: code:addons/account/models/chart_template.py:422
#, python-format
msgid "CABA"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "Εισαγωγή CAMT"

#. module: account
#: model:account.incoterms,name:account.incoterm_CIP
msgid "CARRIAGE AND INSURANCE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CPT
msgid "CARRIAGE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CFR
msgid "COST AND FREIGHT"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CIF
msgid "COST, INSURANCE AND FREIGHT"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "Εισαγωγή CSV"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__visible
msgid "Can be Visible?"
msgstr "Να είναι ορατό;"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "Ακύρωση: Δημιουργήστε πιστωτικό και συμψηφίστε "

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "Ακυρωμένο Τιμολόγιο"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"Δεν είναι δυνατή η δημιουργία πιστωτικού για το τιμολόγιο που έχει ήδη "
"συμψηφιστεί, το τιμολόγιο θα πρέπει πρώτα να απο-συμψηφιστεί, τότε μόνο "
"εσείς μπορείτε να προσθέσετε πιστωτικό σημείωμα για αυτό το τιμολόγιο."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr ""
"Δεν είναι δυνατή η δημιουργία πιστωτικού για ένα προσχέδιο / ακυρωμένο "
"τιμολόγιο."

#. module: account
#: code:addons/account/models/account_move.py:349
#, python-format
msgid "Cannot create moves for different companies."
msgstr "Αδύνατη η δημιουργία κινήσεων για διαφορετικές εταιρίες."

#. module: account
#: code:addons/account/models/account_move.py:381
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr ""
"Δεν είναι δυνατόν να δημιουργηθεί μη ισοσταθμισμένη ημερολογιακή εγγραφή."

#. module: account
#: code:addons/account/models/account_invoice.py:772
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Αδύνατη η εύρεση πίνακα λογαριασμών για την εταιρία. Πρέπει να τον ρυθμίσετε. \n"
"Παρακαλώ μεταβείτε στις Ρυθμίσεις Λογαριασμού."

#. module: account
#: code:addons/account/models/account.py:116
#: code:addons/account/models/chart_template.py:151
#, python-format
msgid "Cannot generate an unused account code."
msgstr "Αδύνατη η δημιουργία ενός μη χρησιμοποιημένου κωδικού λογιαριασμού."

#. module: account
#: code:addons/account/models/account.py:763
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""
"Δεν είναι δυνατή η δημιουργία ενός μη χρησιμοποιημένου κώδικα ημερολογίου. "
"Παρακαλώ συμπληρώστε το πεδίο 'Σύντομος Κωδικός'."

#. module: account
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:349
#, python-format
msgid "Cash"
msgstr "Μετρητά"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr "Ταμειακή Βάση"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "Ημερολόγιο Ταμειακής Βάσης"

#. module: account
#: code:addons/account/models/chart_template.py:422
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "Ημερολόγιο Φόρων Ταμειακής Βάσης"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "Cash Box In"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "Cash Box Out"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:212
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "Έλεγχος Μετρητών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "Λειτουργίες Μετρητών"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Ταμειακές Μηχανές"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr "Στρογγυλοποίηση Μετρητών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__cash_rounding_id
msgid "Cash Rounding Method"
msgstr "Μέθοδος Στρογγυλοποίησης Μετρητών"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Στρογγυλοποίησεις Μετρητών"

#. module: account
#: code:addons/account/models/account_bank_statement.py:187
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "Διαφορά μετρητών που παρατηρήθηκε κατά την καταμέτρηση (%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:34
#, python-format
msgid "Cash: Balance"
msgstr "Ταμείο: Υπόλοιπο"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Γραμμή Ταμείου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__cashbox_id
msgid "Cashbox"
msgstr "Κουτί μετρητών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Γραμμές Ταμείων Μετρητών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Κατηγορία του Λογαριασμού Δαπανών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Κατηγορία Λογαριασμού Εσόδων"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__writeoff_label
#: model:ir.model.fields,help:account.field_account_payment__writeoff_label
#: model:ir.model.fields,help:account.field_account_register_payments__writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr ""
"Αλλάξτε την ετικέτα του αντιγράφου που θα κρατήσει τη διαφορά πληρωμής"

#. module: account
#: code:addons/account/controllers/portal.py:103
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Η αλλαγή του αριθμού ΑΦΜ δεν επιτρέπεται μετά την έκδοση τιμολογίων για το "
"λογαριασμό σας. Επικοινωνήστε μαζί μας άμεσα για αυτή τη διαδικασία."

#. module: account
#: code:addons/account/controllers/portal.py:109
#, python-format
msgid ""
"Changing your company name is not allowed once invoices have been issued for"
" your account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:106
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Η αλλαγή του ονόματός σας δεν επιτρέπεται μετά την έκδοση τιμολογίων για το "
"λογαριασμό σας. Επικοινωνήστε μαζί μας άμεσα για αυτή τη διαδικασία."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__channel_ids
msgid "Channels"
msgstr "Κανάλια"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company__chart_template_id
msgid "Chart Template"
msgstr "Πρότυπο Σχεδίου"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Πρότυπα Πίνακα"

#. module: account
#: code:addons/account/models/company.py:337
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
#, python-format
msgid "Chart of Accounts"
msgstr "Λογιστικό Σχέδιο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Πρότυπα Λογιστικών Σχεδίων"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Πρότυπα Λογιστικών Σχεδίων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Chart of account set."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Λογιστικό Σχέδιο"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Έλεγχος Υπολοίπου Κλεισίματος"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__is_difference_zero
msgid "Check if difference is zero."
msgstr "Ελέγξτε εάν η διαφορά είναι μηδενική."

#. module: account
#: model:ir.model.fields,help:account.field_account_account__reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"Επιλέξτε αυτό το πλαίσιο αν αυτός ο λογαριασμός επιτρέπει την αντιστοίχιση "
"τιμολογίων και πληρωμών με εγγραφές ημερολογίου."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Επιλέξτε αυτό το πλαίσιο αν δεν θέλετε να μοιραστείτε την ίδια ακολουθία για"
" τιμολόγια και πιστωτικά σημειώματα που γίνονται από αυτό το ημερολόγιο"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Ενεργοποιήστε εάν θέλετε να επιτρέπετε την ακύρωση των εγγραφών που "
"σχετίζονται με αυτό το ημερολόγιο ή την ακύρωση του τιμολογίου που "
"σχετίζεται με τις εγγραφές."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__price_include
#: model:ir.model.fields,help:account.field_account_tax_template__price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr "Επιλέξτε το αν η τιμή τιμολόγησης είδους περιέχει αυτό το φόρο."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Επιλέξτε αυτό αν επιθυμείτε ο χρήστης να συμψηφίζει εγγραφές στο λογαριασμό "
"αυτό."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "Με Επιταγές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__child_ids
msgid "Child Messages"
msgstr "Θυγατρικά μηνύματα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__children_tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Φόροι Θυγατρικών"

#. module: account
#: code:addons/account/models/chart_template.py:358
#, python-format
msgid "Choose Accounting Template"
msgstr "Επιλέξτε Πρότυπο Λογιστικής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Choose a default sales tax for your products."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:137
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund__filter_refund
msgid ""
"Choose how you want to credit this invoice. You cannot Modify and Cancel if "
"the invoice is already reconciled"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:19
#, python-format
msgid "Click to <b>send the invoice by email.</b>"
msgstr "Κάντε κλικ για να <b>στείλετε το τιμολόγιο με email.</b>"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:23
#, python-format
msgid "Click to <b>send the invoice.</b>"
msgstr "Κάντε κλικ για να <b>αποστείλετε το τιμολόγιο.</b>"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:14
#, python-format
msgid ""
"Click to <b>validate your invoice.</b> A reference will be assigned to this "
"invoice and you will not be able to modify it anymore."
msgstr ""
"Κάντε κλικ για να  <b>επικυρώσετε το τιμολόγιό σας. </b> Θα γίνει αναφορά σε"
" αυτό το τιμολόγιο και δεν θα μπορείτε να το τροποποιήσετε πια"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "Click to Rename"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:343
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:66
#, python-format
msgid "Close statement"
msgstr "Κλείσιμο Δήλωσης"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
msgid "Closed"
msgstr "Κλειστό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date_done
msgid "Closed On"
msgstr "Έκλεισε στις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account__code
#: model:ir.model.fields,field_description:account.field_account_account_template__code
#: model:ir.model.fields,field_description:account.field_account_analytic_line__code
#: model:ir.model.fields,field_description:account.field_account_incoterms__code
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_method__code
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_code
msgid "Code"
msgstr "Κωδικός"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__code_prefix
msgid "Code Prefix"
msgstr "Πρόθεμα Κωδικού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__coin_value
msgid "Coin/Bill Value"
msgstr "Αξία Νομίσματος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Collect information and produce statistics on the trade in goods in Europe "
"with intrastat."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__color
#: model:ir.model.fields,field_description:account.field_account_journal__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Επιχειρηματική Οντότητα"

#. module: account
#: code:addons/account/models/account_invoice.py:534
#, python-format
msgid "Commercial partner and vendor account owners must be identical."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Common Journal Report"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Κοινή Αναφορά"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_reference_type
msgid "Communication"
msgstr "Επικοινωνία"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Εταιρείες που παραπέμπουν στο συνεργάτη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__company_id
#: model:ir.model.fields,field_description:account.field_account_common_report__company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__company_id
#: model:ir.model.fields,field_description:account.field_account_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_move__company_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,field_description:account.field_account_payment__company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__company_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__company_id
#: model:ir.model.fields,field_description:account.field_account_tax__company_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Εταιρία"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__company_currency_id
msgid "Company Currency"
msgstr "Νόμισμα Εταιρίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Η εταιρία έχει πίνακα λογαριασμών"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,help:account.field_account_journal__company_id
#: model:ir.model.fields,help:account.field_account_move__company_id
#: model:ir.model.fields,help:account.field_account_payment__company_id
msgid "Company related to this journal"
msgstr "Η εταιρία που σχετίζεται με το ημερολόγιο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Ολοκληρωμένο σετ Φόρων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composer_id
msgid "Composer"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composition_mode
msgid "Composition mode"
msgstr "Λειτουργία σύνθεσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr ""
"Υπολογίστε τους φορολογικούς συντελεστές με βάση τους ταχυδρομικούς κώδικες "
"των ΗΠΑ"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""
"Υπολογίζει τους αντιστοίχους λογαριασμούς αυτής της εγγραφής του ημερολογίου"
" για αυτήν την καταχώριση του ημερολογίου. Αυτό μπορεί να χρειαστεί στις "
"αναφορές."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end
msgid "Computed Balance"
msgstr "Υπολογιζόμενο Υπόλοιπο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Conditions on Bank Statement Line"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Configure"
msgstr "Παραμετροποίηση"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/section_and_note_fields_backend.js:102
#, python-format
msgid "Configure a product"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_invoice_layout
msgid "Configure your document layout"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Επιβεβαίωση"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "Επιβεβαίωση Πρόχειρων Τιμολογίων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "Επιβεβαίωση Τιμολογίων"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "Επιβεβαίωση Πληρωμών"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "Επιβεβαίωση των επιλεγμένων τιμολογίων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Επιβεβαιώθηκε"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"Επιβεβαιώνοντας αυτό, θα δημιουργηθεί αυτόματα μια καταχώριση ημερολογίου με"
" τη διαφορά στο λογαριασμό κερδών / ζημιών που έχει οριστεί στο ημερολόγιο "
"ταμείου."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:54
#, python-format
msgid "Congrats, you're all done!"
msgstr "Συγχαρητήρια, είστε σε όλα έτοιμοι!"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "Congratulations! You are all set."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_partner
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Contact"
msgstr "Επαφή"

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Contains"
msgstr "Περιέχει"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""
"Περιέχει τη λογική που μοιράζεται μεταξύ των μοντέλων που επιτρέπει την "
"καταγραφή των πληρωμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__body
msgid "Contents"
msgstr "Περιεχόμενα"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contracts_count
#: model:ir.model.fields,field_description:account.field_res_users__contracts_count
msgid "Contracts Count"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Έλεγχος-Πρόσβαση"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:103
#, python-format
msgid ""
"Correction of <a href=# data-oe-model=account.invoice data-oe-"
"id=%d>%s</a><br>Reason: %s"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Κόστος Πωληθέντων"

#. module: account
#: code:addons/account/models/chart_template.py:204
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__counterpart
msgid "Counterpart"
msgstr "Ομόλογος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_id
msgid "Counterpart Account"
msgstr "Αντίστοιχος Λογαριασμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Counterpart Values"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_id
msgid "Country"
msgstr "Χώρα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_group_id
msgid "Country Group"
msgstr "Ομάδα Κράτους"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "Create"
msgstr "Δημιουργία"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_or_link_option
msgid "Create Or Link Option"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:292
#, python-format
msgid "Create a Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Create a bank account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Create a credit note"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Create a customer invoice"
msgstr ""

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "Δημιουργήστε ένα προσχέδιο πιστωτικού "

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid "Create a journal entry"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Create a new cash log"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_form
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_template_form
msgid "Create a new fiscal position"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid "Create a new incoterm"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_purchasable
msgid "Create a new purchasable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Create a new reconciliation model"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_sellable
msgid "Create a new sellable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_tax_form
msgid "Create a new tax"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Create a vendor credit note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Δημιουργία και καταχώρηση κίνησης"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:281
#, python-format
msgid "Create cash statement"
msgstr "Δημιουργία δήλωσης μετρητών"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:267
#, python-format
msgid "Create invoice/bill"
msgstr "Δημιουργία τιμολογίου / λογαριασμού"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#, python-format
msgid "Create model"
msgstr "Δημιουργία μοντέλου"

#. module: account
#: selection:account.setup.bank.manual.config,create_or_link_option:0
msgid "Create new journal"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "Δημιουργήστε την πρώτη στρογγυλοποίηση μετρητών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_move__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments__create_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_date
#: model:ir.model.fields,field_description:account.field_account_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_account_type__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_date
#: model:ir.model.fields,field_description:account.field_account_common_report__create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_group__create_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_move__create_date
#: model:ir.model.fields,field_description:account.field_account_move_line__create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_payment__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__create_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_date
#: model:ir.model.fields,field_description:account.field_account_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Credit"
msgstr "Πίστωση"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Πιστωτική Κάρτα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit_cash_basis
msgid "Credit Cash Basis"
msgstr "Πίστωση Ταμειακής Βάσης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__filter_refund
msgid "Credit Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__credit_move_id
msgid "Credit Move"
msgstr "Κίνηση Πίστωσης"

#. module: account
#: code:addons/account/models/account_invoice.py:498
#: code:addons/account/models/account_invoice.py:1358
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
#, python-format
msgid "Credit Note"
msgstr "Πιστωτικό Τιμολόγιο"

#. module: account
#: code:addons/account/models/account_invoice.py:499
#, python-format
msgid "Credit Note - %s"
msgstr "Πιστωτικό Τιμολόγιο - %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "Πιστωτικό Τιμολόγιο Αγορών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__date_invoice
msgid "Credit Note Date"
msgstr "Ημερ. Πιστωτικού Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Ακολουθία Καταχώρησης Πιστωτικού Τιμολογίου"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Credit Notes"
msgstr "Πιστωτικά"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Πιστωτικά: Επόμενος Αριθμός"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__credit_account_id
msgid "Credit account"
msgstr "Λογαριασμός Πίστωσης"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr ""
"Εγγραφές πιστωτικού ημερολογίου που αντιστοιχίζονται με αυτή την εγγραφή "
"ημερολογίου."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Νομίσματα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__currency_id
#: model:ir.model.fields,field_description:account.field_account_journal__currency_id
#: model:ir.model.fields,field_description:account.field_account_move__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__currency_id
#: model:ir.model.fields,field_description:account.field_account_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__currency_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_id
#: model:ir.model.fields,field_description:account.field_res_partner__currency_id
#: model:ir.model.fields,field_description:account.field_res_users__currency_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Νόμισμα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_rate
msgid "Currency Rate"
msgstr "Ισοτιμία"

#. module: account
#: code:addons/account/models/account_move.py:1419
#: code:addons/account/models/account_move.py:1431
#, python-format
msgid "Currency exchange rate difference"
msgstr "Διαφορά συναλλαγματικής ισοτιμίας"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
msgid "Current Assets"
msgstr "Κυκλοφορούν Ενεργητικό"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Βραχυπρόθεσμες Υποχρεώσεις"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Έσοδα Τρέχουσας Χρήσης"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Ο τρέχων χρήστης έχει μια ειδοποίηση με αστέρι συνδεδεμένη με αυτό το μήνυμα"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "Πελάτης"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:769
#, python-format
msgid "Customer Credit Note"
msgstr "Πιστωτικό Πελάτη"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "Τιμολόγιο Πελάτη"

#. module: account
#: code:addons/account/models/chart_template.py:418
#, python-format
msgid "Customer Invoices"
msgstr "Τιμολόγια Πελάτη"

#. module: account
#: code:addons/account/models/account_payment.py:767
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Customer Payment"
msgstr "Πληρωμή Πελατών"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Όροι Πληρωμής Πελάτη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer Payments"
msgstr "Συναλλαγές Πελατών"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__access_url
msgid "Customer Portal URL"
msgstr "Διεύθυνση URL Πύλης Πελατών"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__taxes_id
msgid "Customer Taxes"
msgstr "Φόροι Πελάτη"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Customers"
msgstr "Πελάτες"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize"
msgstr "Προσαρμογή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize the look of your invoices."
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAF
msgid "DELIVERED AT FRONTIER"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAP
msgid "DELIVERED AT PLACE"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAT
msgid "DELIVERED AT TERMINAL"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDP
msgid "DELIVERED DUTY PAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDU
msgid "DELIVERED DUTY UNPAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DEQ
msgid "DELIVERED EX QUAY"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DES
msgid "DELIVERED EX SHIP"
msgstr ""

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "ΚΑΤΩ"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:289
#: code:addons/account/static/src/xml/account_reconciliation.xml:304
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__date
#: model:ir.model.fields,field_description:account.field_account_move__date
#: model:ir.model.fields,field_description:account.field_account_move_line__date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__date
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "Ημερομηνία"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Ημερομηνία κατά την οποία αναρτήθηκε η αρχική καταχώρηση της λογιστικής, "
"αυτής της εταιρείας."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""
"Ημερομηνία από την οποία γίνεται η διαχείριση της λογιστικής στην Odoo. "
"Είναι η ημερομηνία της εγγραφής έναρξης."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__reverse_date
msgid "Date of the reverse accounting entry."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "Ημερομηνία:"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Ημερομηνίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__day_of_the_month
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Day of the month"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__day_of_the_month
msgid ""
"Day of the month on which the invoice must come to its term. If zero or "
"negative, this value will be ignored, and no specific day will be set. If "
"greater than the last day of a month, this number will instead select the "
"last day of this month."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:51
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Αγαπητέ/η Κύριε/Κυρία,\n"
"\n"
"οι εγγραφές μας δείχνουν ότι μερικές πληρωμές του λογαριασμού σας είναι ακόμη ανοικτές. Παρακαλώ βρείτε τις λεπτομέρειες παρακάτω.\n"
"Εάν το ποσό έχει ήδη πληρωθεί, παρακαλώ αγνοήστε αυτή την ειδοποίηση. Εάν όχι, παρακαλώ εμβάστε μας το συνολικό ποσό που αναφέρεται παρακάτω.\n"
"Εάν έχετε οποιεσδήποτε απορίες για τον λογαριασμό σας, παρακαλώ επικοινωνήστε μαζί μας.\n"
"\n"
"Ευχαριστούμε εκ των προτέρων για την συνεργασία σας.\n"
"Με εκτίμηση,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Debit"
msgstr "Χρέωση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit_cash_basis
msgid "Debit Cash Basis"
msgstr "Χρέωση Ταμειακής Βάσης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__debit_move_id
msgid "Debit Move"
msgstr "Χρεωστική Κίνηση"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__debit_account_id
msgid "Debit account"
msgstr "Λογαριασμός Χρέωσης"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr ""
"Στοιχεία χρεωστικού ημερολογίου που αντιστοιχίζονται με αυτό το στοιχείο "
"ημερολογίου."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "Δεκέμβριος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Ειδική Ακολουθία Πιστωτικών Σημειωμάτων"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_reference_type
msgid "Default Communication Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_credit_account_id
msgid "Default Credit Account"
msgstr "Προεπιλεγμένος Λογαριασμός Πίστωσης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_debit_account_id
msgid "Default Debit Account"
msgstr "Προεπιλεγμένος Λογαριασμός Χρέωσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default Incoterm of your company"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Προεπιλεγμένος Φόρος Αγορών"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__invoice_reference_type
msgid "Default Reference Type on Invoices."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_sale_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Προεπιλεγμένος Φόρος Πωλήσεων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default Sending Options"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Προεπιλεγμένοι Φόροι"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__incoterm_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__incoterm_id
msgid "Default incoterm"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default payment communication on customer invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "Προκαθορισμένοι φόροι που εφαρμόζονται στις τοπικές συναλλαγές"

#. module: account
#: model:ir.model.fields,help:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,help:account.field_product_template__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__taxes_id
#: model:ir.model.fields,help:account.field_product_template__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "Διαχείριση Αναβαλλόμενων Εσόδων"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid "Define a new account type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr ""
"Ορίστε το μικρότερο κέρμα του νομίσματος που χρησιμοποιείται για την πληρωμή"
" με μετρητά."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Define your fiscal years opening &amp; closing dates."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr ""
"Ορίζει το μικρότερο κέρμα του νομίσματος που μπορεί να χρησιμοποιηθεί για "
"την πληρωμή με μετρητά."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Ορισμός"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__trust
#: model:ir.model.fields,field_description:account.field_res_users__trust
msgid "Degree of trust you have in this debtor"
msgstr "Βαθμός εμπιστοσύνης για τον οφειλέτη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete
msgid "Delete Emails"
msgstr "Διαγραφή Emails"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete_message
msgid "Delete Message Copy"
msgstr "Διαγραφή Αντίγραφου Μηνύματος"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr "Διαγραφή απεσταλμένων emails (μόνο από μαζική αποστολή)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__deprecated
msgid "Deprecated"
msgstr "Ακυρωμένος"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
msgid "Depreciation"
msgstr "Αποσβέσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "Περιγράψτε για ποιο λόγο αφαιρείτε χρήματα από την ταμειακή μηχανή:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:307
#: model:ir.model.fields,field_description:account.field_account_account_type__note
#: model:ir.model.fields,field_description:account.field_account_invoice_line__name
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
#, python-format
msgid "Description"
msgstr "Περιγραφή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__note
msgid "Description on the Invoice"
msgstr "Περιγραφή του Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_account_id
msgid "Destination Account"
msgstr "Λογαριασμός Προορισμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__auto_apply
msgid "Detect Automatically"
msgstr "Εντόπισε Αυτόματα"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_template__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group. 'adjustment' is "
"used to perform tax adjustment."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__difference
msgid "Difference"
msgstr "Διαφορά"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__writeoff_account_id
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_account_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__writeoff_account_id
msgid "Difference Account"
msgstr "Λογαριασμός Διαφοράς"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr ""
"Διαφορά μεταξύ του υπολογιζόμενου υπολοίπου λήξης και του καθορισμένου "
"υπολοίπου λήξης."

#. module: account
#: model:ir.model,name:account.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__group_products_in_bills
msgid ""
"Disable this option to use a simplified versions of vendor bills, where "
"products are hidden."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Disable to have a simplified view of vendor bills, without the products."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Disc (%)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__discount
msgid "Discount (%)"
msgstr "Έκπτωση (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_account__display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag__display_name
#: model:ir.model.fields,field_description:account.field_account_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_account_type__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__display_name
#: model:ir.model.fields,field_description:account.field_account_common_report__display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_group__display_name
#: model:ir.model.fields,field_description:account.field_account_incoterms__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_send__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_move__display_name
#: model:ir.model.fields,field_description:account.field_account_move_line__display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__display_name
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments__display_name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__display_name
#: model:ir.model.fields,field_description:account.field_account_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__qr_code
#: model:ir.model.fields,field_description:account.field_res_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__display_type
msgid "Display Type"
msgstr "Τύπος Οθόνης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Εμφάνιση στα τιμολόγια"

#. module: account
#: code:addons/account/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Να μην κρατηθεί αντίγραφο του email στο ιστορικό επικοινωνίας εγγράφων (μόνο"
" για μαζική αποστολή)"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "Λήψη"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "Προσχέδιο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Draft Credit Note"
msgstr "Προσχέδιο Πιστωτικού"

#. module: account
#: code:addons/account/models/account_invoice.py:496
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Πρόχειρο Τιμολόγιο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Πρόχειρα Τιμολόγια"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Πρόχειρες δηλώσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due"
msgstr "Οφειλή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__residual
msgid "Due Amount"
msgstr "Οφειλόμενο Ποσό"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:39
#: code:addons/account/static/src/xml/account_reconciliation.xml:290
#: model:ir.model.fields,field_description:account.field_account_invoice__date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report__date_due
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Due Date"
msgstr "Ημερ. Λήξης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Ημερ. Υπολογισμού Οφειλών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Τύπος Λήξης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__date_maturity
msgid "Due date"
msgstr "Ημερ. Λήξης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due the"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1285
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""
"Αναγνωρίστηκε διπλότυπη αναφορά προμηθευτή. Ίσως καταχωρήσατε δύο φορές τον "
"ίδιο λογαριασμό / πιστωτικό σημείωμα του προμηθευτή."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr "Δυναμικές Εκθέσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "ΕΕ Ψηφιακών Προϊόντων ΦΠΑ"

#. module: account
#: model:account.incoterms,name:account.incoterm_EXW
msgid "EX WORKS"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:396
#: code:addons/account/models/chart_template.py:411
#: code:addons/account/models/chart_template.py:421
#, python-format
msgid "EXCH"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:965
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Είτε περάσετε και τις δύο χρεωστική είτε πιστωτική είτε όχι."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_email
msgid "Email"
msgstr "Email"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email Alias"
msgstr "Ψευδώνυμο Email"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Η διεύθυνση ηλεκτρονικού ταχυδρομείου του αποστολέα. Αυτό το πεδίο είναι "
"ορίζεται όταν δεν ταυτοποιηθεί ο συνεργάτης και έχει αντικαθιστά το πεδίο "
"author_id στην συζήτηση."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_email
msgid "Email by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email your Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_to
#: model:ir.model.fields,field_description:account.field_account_common_report__date_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_to
msgid "End Date"
msgstr "Ημερ. Λήξης"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "Τέλος του Επόμενου Μήνα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end_real
msgid "Ending Balance"
msgstr "Τελικό Υπόλοιπο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_end_id
msgid "Ending Cashbox"
msgstr "Τερματισμός του Ταμείου"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Καταχωρήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Καταχωρήσεις Ταξινομημένες κατά"

#. module: account
#: code:addons/account/models/account_move.py:903
#, python-format
msgid "Entries are not from the same account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Καταχωρήσεις για Αναθεώρηση"

#. module: account
#: code:addons/account/models/account_analytic_line.py:47
#, python-format
msgid "Entries: "
msgstr "Εγγραφές: "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_id
msgid "Entry Sequence"
msgstr "Ιεράρχηση Εγγραφών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_ids
msgid "Entry lines"
msgstr "Γραμμές εγγραφής"

#. module: account
#: selection:account.account.type,internal_group:0
#: model:account.account.type,name:account.data_account_type_equity
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Equity"
msgstr "Κεφάλαιο"

#. module: account
#: code:addons/account/models/res_config_settings.py:146
#, python-format
msgid "Error!"
msgstr "Λάθος!"

#. module: account
#: code:addons/account/models/chart_template.py:421
#, python-format
msgid "Exchange Difference"
msgstr "Συναλλαγματικές Διαφορές"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Ημερολόγιο Συναλλαγματικών Κερδών / Ζημιών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__exchange_move_id
msgid "Exchange Move"
msgstr "Κίνηση Συναλλάγματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Αναμονή Λογιστικού Σχεδίου"

#. module: account
#: selection:account.account.type,internal_group:0
msgid "Expense"
msgstr "Έξοδα"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_expense_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Λογαριασμός Εξόδων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Λογαριασμός Εξόδων στο Πρότυπο Είδους"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Expenses"
msgstr "Έξοδα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__reference
msgid "External Reference"
msgstr "Εξωτερική Αναφορά"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:320
#: code:addons/account/static/src/xml/account_reconciliation.xml:335
#, python-format
msgid "External link"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FAS
msgid "FREE ALONGSIDE SHIP"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FCA
msgid "FREE CARRIER"
msgstr "ΔΩΡΕΑΝ ΜΕΤΑΦΟΡΕΣ"

#. module: account
#: model:account.incoterms,name:account.incoterm_FOB
msgid "FREE ON BOARD"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__starred_partner_ids
msgid "Favorited By"
msgstr "Αγαπημένο από"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Αγαπημένα"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "Φεβρουάριος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__state_ids
msgid "Federal States"
msgstr "Ομόσπονδα Κράτη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__attachment_ids
msgid "Files"
msgstr "Αρχεία"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "Συμπληρώστε την φόρμα εάν βάλετε χρήματα στην ταμειακή μηχανή:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:147
#, python-format
msgid "Filter..."
msgstr "Φίλτρο..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__general_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "ΧρΟικ. Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Φορολογικές Πληροφορίες"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "Δημοσιονομική Ελληνικοποίηση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__position_id
msgid "Fiscal Mapping"
msgstr "Οικονομική Απεικόνιση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Φορολογικές Περίοδοι"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__position_id
#: model:ir.model.fields,field_description:account.field_account_invoice__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_position_id
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Φορολογική Θέση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__name
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Πρότυπο Φορολογικής Θέσης"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Φορολογικές Θέσεις"

#. module: account
#: code:addons/account/models/company.py:310
#: model:ir.model,name:account.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#, python-format
msgid "Fiscal Year"
msgstr "Λογιστική Χρήση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_fiscal_year
msgid "Fiscal Years"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_day
msgid "Fiscal year last day."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_month
msgid "Fiscal year last month."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Τελευταία Ημέρα Οικονομικού Έτους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Τελευταίος Μήνας Οικονομικού Έτους"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "Σταθερό"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "Σταθερό Ποσό"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Πάγια"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""
"Το προκαθορισμένο ποσό θα ισχύσει ως χρέωση εάν είναι αρνητικό, και ως "
"πίστωση εάν είναι θετικό."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Επίπεδα Παρακολούθησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__outbound_payment_method_ids
msgid "For Outgoing Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "Για ποσοστό εισάγετε μια τιμή από 0 εώς 100."

#. module: account
#: sql_constraint:account.invoice.line:0
msgid ""
"Forbidden unit price, account and quantity on non-accountable invoice line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Force the second tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_tax_included
msgid "Force the tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""
"Αναγκάζει όλες τις κινήσεις αυτού του λογαριασμού να έχουν το αντίστοιχο "
"νόμισμα."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr "Αναγκάζει όλες τις κινήσεις να έχουν δευτερεύον νόμισμα."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:231
#: code:addons/account/report/account_journal.py:101
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Το περιεχόμενο της φόρμας λείπει, αυτή η αναφορά δε θα εκτυπωθεί."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__email_from
msgid "From"
msgstr "Από"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "Από Πληρωτέους λογαριασμούς"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "Από Εισπρακτέους λογαριασμούς"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Από αυτήν την αναφορά, μπορείτε να δείτε μια επισκόπηση του ποσού που "
"τιμολογήθηκε από τους  προμηθευτές σας. Το εργαλείο αναζήτησης μπορεί επίσης"
" να χρησιμοποιηθεί για να προσαρμόσετε τις εκθέσεις τιμολογίων σας και έτσι,"
" ταιριάζει την ανάλυση αυτή με τις ανάγκες σας."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Από αυτήν την αναφορά, μπορείτε να δείτε μια επισκόπηση του ποσού που "
"τιμολογήθηκε στους πελάτες σας. Το εργαλείο αναζήτησης μπορεί επίσης να "
"χρησιμοποιηθεί για να προσαρμόσετε τις εκθέσεις τιμολογίων σας και έτσι, "
"ταιριάζει την ανάλυση αυτή με τις ανάγκες σας."

#. module: account
#: code:addons/account/models/account_invoice.py:387
#, python-format
msgid "From: "
msgstr "Από:"

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__full_reconcile_id
msgid "Full Reconcile"
msgstr "Συμψηφισμός Πλήρης "

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:102
#, python-format
msgid "Future"
msgstr "Μελλοντικά"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Λογαριασμός Κερδών από Τιμή Συναλλάγματος"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Δημιουργία Εγγραφών"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "Γενικές Δηλώσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "Λάβετε προειδοποιήσεις κατά την τιμολόγηση συγκεκριμένων πελατών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"Εισάγετε τις τραπεζικές σας δηλώσεις αυτόματα κάθε 4 ώρες, ή μέσω ενός κλικ,"
" χρησιμοποιόντας υπηρεσίες Yodlee και Plaid. Μετά την εγκατάσταση, ορίστε "
"τις \"Τραπεζικές Τροφοδοσίες\" σε \"Τραπεζικός Συγχρονισμός\" στις ρυθμίσεις"
" τραπεζικού λογαριασμού. Μετά, κάντε κλικ στη \"Διαμόρφωση\" στον online "
"λογαριασμό για να εισάγετε τραπεζικά σας διαπιστευτήρια."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "Δίνει την ακολουθία αυτής της σειράς όταν εμφανίζεται το τιμολόγιο."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr ""
"Δίνει την ακολουθία της παραγγελίας κατά την εμφάνιση μιάς λίστας γραμμών "
"τραπεζικών συναλλαγών."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax__sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr ""
"Δίνει την σειρά ακολουθίας όταν εμφανίζεται μία λίστα από φόρους τιμολογίων."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""
"Δίνει τη σειρά ακολουθίας κατά την προβολή μιας λίστας με γραμμές όρων "
"πληρωμής."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:63
#, python-format
msgid "Go to bank statement(s)"
msgstr "Μεταβείτε στις τραπεζικές δηλώσεις"

#. module: account
#: code:addons/account/models/account_invoice.py:773
#: code:addons/account/models/company.py:544
#, python-format
msgid "Go to the configuration panel"
msgstr "Μετάβαση στον πίνακα ελέγχου"

#. module: account
#: code:addons/account/models/company.py:482
#, python-format
msgid "Go to the journal configuration"
msgstr ""

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "Καλός Οφειλέτης"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:48
#, python-format
msgid "Good Job!"
msgstr "Μπράβο!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__group_id
#: model:ir.model.fields,field_description:account.field_account_account_template__group_id
msgid "Group"
msgstr "Ομάδα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Ομαδοποίηση Γραμμών Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments__group_invoices
msgid "Group Invoices"
msgstr ""

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "Ομάδα Φόρων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group payments into a single batch to ease the reconciliation process"
msgstr ""

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Έχει Λογιστικές Εγγραφές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__has_invoices
msgid "Has Invoices"
msgstr "Έχει Τιμολόγια"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__has_outstanding
msgid "Has Outstanding"
msgstr "Έχει Εκκρεμείς"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Έχει Μη Συμψηφισμένες Εγγραφές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__qr_code_valid
msgid "Has all required arguments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__has_error
#: model:ir.model.fields,help:account.field_account_invoice_send__has_error
msgid "Has error"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment__hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments__hide_payment_method
msgid "Hide Payment Method"
msgstr "Απόκρυψη Τρόπου Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "Απόκρυψη Χρήσης Επιλογής Ταμειακής Βάσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_chatter
msgid "History"
msgstr "Ιστορικό"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "Πώς υπολογίζεται το συνολικό ποσό φόρου σε παραγγελίες και τιμολόγια"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__id
#: model:ir.model.fields,field_description:account.field_account_account__id
#: model:ir.model.fields,field_description:account.field_account_account_tag__id
#: model:ir.model.fields,field_description:account.field_account_account_template__id
#: model:ir.model.fields,field_description:account.field_account_account_type__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__id
#: model:ir.model.fields,field_description:account.field_account_chart_template__id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__id
#: model:ir.model.fields,field_description:account.field_account_common_report__id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_group__id
#: model:ir.model.fields,field_description:account.field_account_incoterms__id
#: model:ir.model.fields,field_description:account.field_account_invoice__id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__id
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__id
#: model:ir.model.fields,field_description:account.field_account_invoice_send__id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__id
#: model:ir.model.fields,field_description:account.field_account_journal__id
#: model:ir.model.fields,field_description:account.field_account_move__id
#: model:ir.model.fields,field_description:account.field_account_move_line__id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_payment__id
#: model:ir.model.fields,field_description:account.field_account_payment_method__id
#: model:ir.model.fields,field_description:account.field_account_payment_term__id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__id
#: model:ir.model.fields,field_description:account.field_account_print_journal__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__id
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:account.field_account_register_payments__id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__id
#: model:ir.model.fields,field_description:account.field_account_tax__id
#: model:ir.model.fields,field_description:account.field_account_tax_group__id
#: model:ir.model.fields,field_description:account.field_account_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_unreconcile__id
#: model:ir.model.fields,field_description:account.field_cash_box_in__id
#: model:ir.model.fields,field_description:account.field_cash_box_out__id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__id
#: model:ir.model.fields,field_description:account.field_validate_account_move__id
msgid "ID"
msgstr "Κωδικός"

#. module: account
#: code:addons/account/models/chart_template.py:418
#, python-format
msgid "INV"
msgstr "ΤΙΜ"

#. module: account
#: code:addons/account/models/account_bank_statement.py:325
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr ""
"Εάν το \"Ποσό Νομίσματος\" είναι καθορισμένο, τότε το \"Ποσό\" πρέπει να "
"καθοριστεί επίσης."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,help:account.field_account_invoice__message_unread
#: model:ir.model.fields,help:account.field_account_payment__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,help:account.field_account_invoice__message_needaction
#: model:ir.model.fields,help:account.field_account_payment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account.field_account_invoice__message_has_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr ""
"Εάν είναι τσεκαρισμένο το νέο λογιστικό σχέδιο δεν θα περιέχει αυτό εξ "
"ορισμού."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr ""
"Εάν είναι κενό, χρησιμοποιεί το ημερολόγιο της καταχώρησης ημερολογίου προς "
"αντιστροφή."

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments__group_invoices
msgid ""
"If enabled, groups invoices by commercial partner, invoice account,\n"
"                                                                    type and recipient bank account in the generated payments. If disabled,\n"
"                                                                    a distinct payment will be generated for each invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template__include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"Εάν οριστεί, οι φόροι υπολογίζονται μετά τον υπολογισμό αυτού βασισμένο στην"
" τιμή συμπεριλαμβανομένου φόρου."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally be booked is already closed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__analytic
#: model:ir.model.fields,help:account.field_account_tax_template__analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"Εάν οριστεί, το υπολογιζόμενο ποσό αυτόυ του φόρου θα ανατεθεί στον ίδιο "
"αναλυτικό λογαριασμό όπως τη γραμμή τιμολόγησης (εάν υπάρχει)"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term__active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"Έαν το ενεργό πεδίο οριστεί σε Ψευδές, θα σας επιτρέψει να αποκρύψετε τους "
"όρους πληρωμής χωρίς την αφαίρεσή του."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Αν αυτό το κουτί είναι επιλεγμένο το σύστημα θα προσπαθήσει να ομαδοποιήσει "
"τις λογιστικές γραμμές όταν τις δημιουργεί από τα τιμολόγια."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__auto_reverse
msgid ""
"If this checkbox is ticked, this entry will be automatically reversed at the"
" reversal date you defined."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"Εάν επιλέξετε αυτό το κουτάκι, θα είστε σε θέση να συλλέξετε πληρωμές "
"χρησιμοποιώντας διατακτικές Άμεσης Χρέωσης SEPA."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr ""
"Εάν επιλέξετε αυτό το κουτάκι, θα είστε σε θέση να καταχωρήσετε την πληρωμή "
"σας με τη χρήση SEPA."

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"If you have not installed a chart of account, please install one first.<br>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Εάν αποσυμφωνήσετε τις συναλλαγές, θα πρέπει επίσης να επαληθεύσετε όλες τις"
" ενέργειες που σχετίζονται με αυτές τις συναλλαγές επειδή δεν θα "
"απενεργοποιηθούν"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"Εάν χρησιμοποιείτε τρόπους πληρωμής, η ημερομηνία εξόφλησης θα υπολογιστεί "
"αυτόματα κατα την δημιουργία των λογιστικών εγγραφών. Εάν παραμείνουν κενά "
"αυτά τα πεδία σημαίνει ότι η πληρωμή γίνεται εκείνη την στιγμή. Ο όροι "
"πληρωμής μπορούν να υπολογίσουν πολλαπλές ημερομηνίες λήξης, για παράδειγμα "
"50% τώρα και 50% σε έναν μήνα."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"Εάν χρησιμοποιείτε όρους πληρωμής, η ημερομηνία εξόφλησης θα υπολογιστεί "
"αυτόματα κατά την δημιουργία των λογιστικών εγγραφών.  Οι όροι πληρωμής "
"μπορούν να υπολογίσουν πολλαπλές ημερομηνίες λήξης, για παράδειγμα 50% τώρα "
"και 50% σε έναν μήνα, αλλά αν θελήσετε να επιβάλετε μια ημερομηνία "
"εξόφλησης, βεβαιωθείτε ότι ο όρος πληρωμής δεν εχει καθοριστεί στο "
"τιμολόγιο. Εάν παραμείνουν κενά αυτά τα πεδία, σημαίνει άμεση πληρωμή."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"Εάν πωλείτε ψηφιακά προϊόντα σε πελάτες στην ΕΕ, πρέπει να χρεώνετε ΦΠΑ με "
"βάση τις τοποθεσίες των πελατών σας. Αυτός ο κανόνας ισχύει ανεξάρτητα από "
"το βρίσκεστε εσείς. Τα ψηφιακά προϊόντα ορίζονται στη νομοθεσία ως "
"ραδιοτηλεοπτικές εκπομπές, τηλεπικοινωνίες και υπηρεσίες που παρέχονται "
"ηλεκτρονικά αντί να αποστέλλονται. Οι κάρτες δώρων που αποστέλλονται "
"ηλεκτρονικά δεν περιλαμβάνονται στον ορισμό."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Εικόνα της παραλλαγής είδους (εάν είναι Ψευδές, τότε θα χρησιμοποιηθεί η "
"μεγάλου μεγέθους εικόνα του προτύπου είδους). Θα αλλάξει αυτόματα το μέγεθός"
" τους ως 1024x1024 εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Άμεση Πληρωμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
msgid "Import"
msgstr "Εισαγωγή"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "Εισαγωγή αρχείων .qif"

#. module: account
#: model:ir.actions.act_window,name:account.account_invoice_import_wizard_action
msgid "Import Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_import_wizard
msgid "Import Your Vendor Bills from Files."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "Εισαγωγή σε .csv φορμάτ"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "Εισαγωγή σε .ofx φορμάτ"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "Εισαγωγή σε CAMT.053 φορμάτ"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "Εισαγάγετε τα τραπεζικά σας statement/δηλώσεις, αυτόματα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "Εισαγάγετε τις τραπεζικές δηλώσεις σας με CAMT.053"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "Εισαγάγετε τις τραπεζικές δηλώσεις σας με CSV"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "Εισαγάγετε τις τραπεζικές δηλώσεις σας με OFX"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "Εισαγάγετε τις τραπεζικές δηλώσεις σας με QIF"

#. module: account
#: selection:account.invoice,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "In Payment"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:355
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"Για να διαγράψετε μια γραμμή τραπεζικής καταχώρισης, πρέπει πρώτα να την "
"ακυρώσετε για να διαγράψετε τα σχετικά αντικείμενα του ημερολογίου."

#. module: account
#: code:addons/account/models/account_bank_statement.py:201
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Για να διαγράψετε μια τραπεζική καταχώριση, πρέπει πρώτα να την ακυρώσετε "
"για να διαγράψετε τα σχετικά αντικείμενα του ημερολογίου."

#. module: account
#: code:addons/account/models/account_payment.py:89
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr ""
"Για να πληρώσετε πολλαπλά τιμολόγιο με τη μία, πρέπει αυτά να έχουν το ίδιο "
"νόμισμα."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Ανενεργή"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "Εισερχόμενο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__analytic
msgid "Include in Analytic Cost"
msgstr "Συμπερίληψη στο Αναλυτικό Κόστος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template__price_include
msgid "Included in Price"
msgstr "Περιέχεται στην Τιμή"

#. module: account
#: selection:account.account.type,internal_group:0
#: model:account.account.type,name:account.data_account_type_revenue
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Income"
msgstr "Έσοδα"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_income_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Λογαριασμός Εσόδων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_id
msgid "Income Account on Product Template"
msgstr "Λογαριασμός Εσόδων στο Πρότυπο Είδους"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:759
#, python-format
msgid "Incorrect Operation"
msgstr "Λανθασμένη Εργασία"

#. module: account
#: code:addons/account/wizard/setup_wizards.py:39
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__code
msgid "Incoterm Standard Code"
msgstr "Πρότυπος Κώδικας Διεθνών Οικονομικών Όρων"

#. module: account
#: model:ir.actions.act_window,name:account.action_incoterms_tree
#: model:ir.model,name:account.model_account_incoterms
#: model:ir.ui.menu,name:account.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_incoterms_tree
msgid "Incoterms"
msgstr "Συντομεύσεις"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-"
"the-art transportation practices."
msgstr ""
"Οι Διεθνείς Οικονομικοί Όροι είναι σειρές απο όρους  πωλήσεων. "
"Χρησιμοποιούνται για να διαχωρίσει τα κόστη συναλλαγών και τις αρμοδιότητες "
"μεταξύ αγοραστή και πωλητή και αντανακλά πρακτικές μεταφοράς σε τεχνολογία "
"αιχμής."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid ""
"Incoterms are used to divide transaction costs and responsibilities between "
"buyer and seller."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:25
#, python-format
msgid "Info"
msgstr "Πληροφορίες"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Πληροφορία"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__parent_id
msgid "Initial thread message."
msgstr "Αρχικό μήνυμα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "Εισαγωγή Λογαριασμού για Εκτίμηση Αποθέματος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "Εγκαταστήστε Περισσότερα Πακέτα"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "Λογαριασμός Διατραπεζικών Συναλλαγών"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_group
#: model:ir.model.fields,field_description:account.field_account_account_type__internal_group
msgid "Internal Group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__narration
msgid "Internal Note"
msgstr "Εσωτερική Σημείωση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__note
msgid "Internal Notes"
msgstr "Εσωτερικά Σημειώματα"

#. module: account
#: selection:account.payment,payment_type:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "Εσωτερική Μεταφορά"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_payments_transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfers"
msgstr "Εσωτερικές Μετακινήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_type
msgid "Internal Type"
msgstr "Εσωτερικός Τύπος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "Εσωτερικές σημειώσεις..."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__incoterm_id
#: model:ir.model.fields,help:account.field_res_company__incoterm_id
#: model:ir.model.fields,help:account.field_res_config_settings__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Οι Διεθνείς Εμπορικοί Όροι είναι μία σειρά προκαθορισμένων εμπορικών όρων "
"που χρησιμοποιούνται στις διεθνείς συναλλαγές."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr "Άκυρο \"Εύρος Τ.Κ.\", παρακαλούμε διαμορφώστε το σωστά."

#. module: account
#: code:addons/account/models/company.py:106
#, python-format
msgid "Invalid fiscal year last day"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1358
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line__invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice"
msgstr "Τιμολόγιο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__number
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Τιμολόγιο #"

#. module: account
#: code:addons/account/models/account_invoice.py:497
#, python-format
msgid "Invoice - %s"
msgstr "Τιμολόγιο - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "Δημιουργήθηκε το Τιμολόγιο"

#. module: account
#: code:addons/account/controllers/portal.py:38
#: model:ir.model.fields,field_description:account.field_account_invoice__date_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_report__date
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "Ημερ. Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__invoice_icon
msgid "Invoice Icon"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Invoice Layout"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "Γραμμή Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__invoice_line_ids
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "Γραμμές Τιμολογίου"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "Ο Αριθμός Τιμολογίου θα πρέπει να είναι μοναδικός ανά Εταιρία!"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "Αριθμός Τιμολογίου:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__invoice_id
msgid "Invoice Reference"
msgstr "Παραπομπή Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__state
msgid "Invoice Status"
msgstr "Κατάσταση τιμολογίου"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "Φόρος Τιμολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "Τιμολόγιο για το οποίο το τιμολόγιο αυτό είναι το πιστωτικό "

#. module: account
#: code:addons/account/models/account_invoice.py:884
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr ""
"Το τιμολόγιο πρέπει να ακυρωθεί ώστε να γίνει επαναφορά του σε προσχέδιο."

#. module: account
#: code:addons/account/models/account_invoice.py:908
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr ""
"Το τιμολόγιο πρέπει να είναι προσχέδιο ώστε να μπορέσει να επικυρωθεί."

#. module: account
#: code:addons/account/models/account_invoice.py:935
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr ""
"Το τιμολόγιο πρέπει να πληρωθεί ώστε να μπορέσει να οριστεί σε καταχώρηση "
"πληρωμής."

#. module: account
#: code:addons/account/models/account_invoice.py:922
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr ""
"Το τιμολόγιο πρέπει να επικυρωθεί ώστε να μπορέσει να οριστεί σε καταχώρηση "
"πληρωμής."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Εξοφλημένο τιμολόγιο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Invoice send & Print"
msgstr ""

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Επικυρωμένο Τιμολόγιο"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}${object.state == 'draft' "
"and '_draft' or ''}"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Τιμολογημένα"

#. module: account
#: code:addons/account/models/account_invoice_import_wizard.py:38
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users__invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model_terms:ir.ui.view,arch_db:account.view_invoice_pivot
#, python-format
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Ανάλυση Τιμολογίων "

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Στατιστικά Τιμολογίων"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:178
#, python-format
msgid "Invoices owed to you"
msgstr "Τα δικά σας τιμολόγια"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to Validate"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__reconciled_invoice_ids
msgid "Invoices whose journal items have been reconciled with this payment's."
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Τιμολόγια χωρίς Πληρωμές"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model_terms:ir.ui.view,arch_db:account.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Τιμολόγηση"

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Between"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_invoice__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_payment__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Greater Than"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Lower Than"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__printed
msgid "Is Printed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__is_second_tax_price_included
msgid "Is Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__is_tax_price_included
msgid "Is Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr ""
"Είναι μια γραμμή στρογγυλοποίησης στην περίπτωση στρογγυλοποίησης μετρητών."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__is_difference_zero
msgid "Is zero"
msgstr "Είναι μηδενικό"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company__income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "Λειτουργεί ως προεπιλεγμένος λογαριασμός για πίστωση ποσού"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company__expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "Λειτουργεί ως προεπιλεγμένος λογαριασμός για χρέωση ποσού"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__alias_name
msgid "It creates draft vendor bill by sending an email."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"Υποδεικνύει ότι το τιμολόγιο έχει εξοφληθεί και ότι η ημερολογιακή εγγραφή "
"του τιμολογίου έχει συμψηφισθεί με μία ή περισσότερες ημερολογιακές "
"καταχωρήσεις πληρωμής."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__sent
msgid "It indicates that the invoice has been sent."
msgstr "Δείχνει ότι το τιμολόγιο έχει αποσταλεί."

#. module: account
#: code:addons/account/models/account_move.py:963
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Είναι υποχρεωτικό να καθορίσετε έναν λογαριασμό και ένα ημερολόγιο για να "
"δημιουργήσετε μια διαγραφή."

#. module: account
#: code:addons/account/models/account_payment.py:585
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Είδη"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "Ιανουάριος"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:200
#: code:addons/account/static/src/xml/account_reconciliation.xml:285
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__journal_id
#: model:ir.model.fields,field_description:account.field_account_move__journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__linked_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__journal_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Ημερολόγιο"

#. module: account
#: code:addons/account/models/account_bank_statement.py:256
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Ημερολογιακές Καταχωρήσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__move_id
#: model:ir.model.fields,field_description:account.field_account_move_line__move_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "Εγγραφή Ημερολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,field_description:account.field_account_invoice__move_name
#: model:ir.model.fields,field_description:account.field_account_payment__move_name
msgid "Journal Entry Name"
msgstr "Όνομα Kαταχώρησης Hμερολογίου"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "Αριθμός Ημερολογιακής Καταχώρισης"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.model.fields,field_description:account.field_account_analytic_line__move_id
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Στοιχείο Ημερολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__label
#: model:ir.model.fields,field_description:account.field_account_register_payments__writeoff_label
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Ετικέτα Αντικειμένου Ημερολογίου"

#. module: account
#: code:addons/account/models/account_payment.py:529
#: code:addons/account/models/reconciliation_widget.py:196
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move__line_ids
#: model:ir.model.fields,field_description:account.field_res_partner__journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users__journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_pivot
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "Εγγραφές Ημερολογίων"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
msgid "Journal Items for Tax Audit"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:351
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Αντικείμενα Ημερολογίου προς Συμψηφισμό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__name
msgid "Journal Name"
msgstr "Όνομα Ημερολογίου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "Στοιχεία ημερολογίου όπου ο αριθμός αντιστοίχησης δεν έχει οριστεί"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_import_wizard__journal_id
msgid "Journal where to generate the bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_currency_id
msgid "Journal's Currency"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "Ημερολόγια"

#. module: account
#: model:ir.actions.report,name:account.action_report_journal
msgid "Journals Audit"
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "Ιούλιος"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "Ιούνιος"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Just done"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__reason
msgid "Justification"
msgstr "Αιτιολόγηση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard
msgid "Kanban Dashboard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "Κρατήστε κενό για κανένα έλεγχο"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date_invoice
msgid "Keep empty to use the current date"
msgstr "Αφήστε το άδειο για να χρησιμοποιήσετε την τρέχουσα ημερομηνία"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date
msgid "Keep empty to use the invoice date."
msgstr ""

#. module: account
#: selection:account.abstract.payment,payment_difference_handling:0
#: selection:account.payment,payment_difference_handling:0
#: selection:account.register.payments,payment_difference_handling:0
msgid "Keep open"
msgstr "Διατήρηση ανοιχτό"

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_income_id
#: model:ir.model.fields,help:account.field_product_template__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue_value
msgid "Kpi Account Total Revenue Value"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:547
#: code:addons/account/static/src/xml/account_reconciliation.xml:204
#: code:addons/account/static/src/xml/account_reconciliation.xml:286
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__name
#: model:ir.model.fields,field_description:account.field_account_move_line__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "Ετικέτα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label_param
msgid "Label Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__description
msgid "Label on Invoices"
msgstr "Ετικέτα στα Tιμολόγια"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_account____last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag____last_update
#: model:ir.model.fields,field_description:account.field_account_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_account_type____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding____last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line____last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report____last_update
#: model:ir.model.fields,field_description:account.field_account_common_report____last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_group____last_update
#: model:ir.model.fields,field_description:account.field_account_incoterms____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_send____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_move____last_update
#: model:ir.model.fields,field_description:account.field_account_move_line____last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line____last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template____last_update
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments____last_update
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config____last_update
#: model:ir.model.fields,field_description:account.field_account_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal____last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard____last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:125
#, python-format
msgid "Last Reconciliation:"
msgstr "Τελευταίος Συμψηφισμός:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_move__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments__write_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_date
#: model:ir.model.fields,field_description:account.field_account_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_account_type__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_date
#: model:ir.model.fields,field_description:account.field_account_common_report__write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_group__write_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_move__write_date
#: model:ir.model.fields,field_description:account.field_account_move_line__write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_payment__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__write_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_date
#: model:ir.model.fields,field_description:account.field_account_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Την τελευταία φορά πραγματοποιήθηκε η αντιστοίχιση τιμολογίων και πληρωμών "
"για αυτόν τον συνεργάτη. Ορίζεται είτε εάν δεν υπάρχει τουλάχιστον μια "
"ασυμψήφιστη χρέωση και μια ασυμψήφιστη πίστωση ή εάν κάνετε κλικ στο κουμπί "
"\"Τέλος\"."

#. module: account
#: model:ir.model.fields,help:account.field_account_account__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"Την τελευταία φορά πραγματοποιήθηκε η αντιστοίχιση τιμολογίων και πληρωμών "
"για αυτόν τον λογαριασμό. Ορίζεται είτε εάν δεν υπάρχει τουλάχιστον μια "
"ασυμψήφιστη χρέωση και μια ασυμψήφιστη πίστωση ή εάν κάνετε κλικ στο κουμπί "
"\"Τέλος\"."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Τελευταία Ημερομηνία Αντιστοίχισης Τιμολογίων & Πληρωμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__layout
msgid "Layout"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Νομικές Σημειώσεις..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Νομικές αναφορές που πρέπει να εκτυπωθούν στα τιμολόγια."

#. module: account
#: code:addons/account/models/account_invoice.py:219
#, python-format
msgid "Less Payment"
msgstr "Μικρότερη Πληρωμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "Αφήστε τους πελάτες σας να πληρώνουν τα τιμολόγιά τους ηλεκτρονικά"

#. module: account
#: selection:account.account.type,internal_group:0
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Liability"
msgstr "Παθητικό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__nbr
msgid "Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Line subtotals tax display"
msgstr ""

#. module: account
#: selection:account.setup.bank.manual.config,create_or_link_option:0
msgid "Link to an existing journal"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__move_id
msgid "Link to the automatically generated Journal Items."
msgstr "Σύνδεσμος για τις Εγγραφές Ημερολογίου που δημιουργήθηκαν αυτόματα."

#. module: account
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Ρευστότητα"

#. module: account
#: code:addons/account/models/chart_template.py:154
#, python-format
msgid "Liquidity Transfer"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Λιστα των φόρων που πρέπει να εγκατασταθούν από τον αυτόματο οδηγό"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:33
#, python-format
msgid "Load more"
msgstr "Φόρτωση περισσοτέρων"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:154
#, python-format
msgid "Load more... ("
msgstr "Φόρτωση περισσοτέρων ... ("

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_lock_date
msgid "Lock Date"
msgstr "Κλείδωμα Ημερομηνίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Κλείδωμα Ημερομηνίας για Μη-Συμβούλους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_log
msgid "Log an Internal Note"
msgstr "Καταγραφή ενός Εσωτερικού Σημειώματος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Looks great!"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:175
#, python-format
msgid "Loss"
msgstr "Ζημίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__loss_account_id
msgid "Loss Account"
msgstr "Λογαριασμός Ζημιών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Λογαριασμός Ζημιών από Τιμή Συναλλάγματος"

#. module: account
#: code:addons/account/models/chart_template.py:420
#, python-format
msgid "MISC"
msgstr "MISC"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Τύπος Δραστηριότητας email"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mailing_list_ids
msgid "Mailing List"
msgstr "Λίστα Αλληλογραφίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_invoice__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_payment__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Κύριο νόμισμα της εταιρίας."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Κύριο νόμισμα της εταιρείας σας"

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Διαχείριση"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__manual
msgid "Manual"
msgstr "Χειροκίνητα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "Χειροκίνητοι Φόροι Τιμολόγησης"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments__payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit, module account_batch_payment must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:19
#: code:addons/account/models/chart_template.py:981
#, python-format
msgid "Manually create a write-off on clicked button."
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "Μάρτιος"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Ανάλυση Περιθωρίων"

#. module: account
#: selection:account.abstract.payment,payment_difference_handling:0
#: selection:account.payment,payment_difference_handling:0
#: selection:account.register.payments,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "Σήμανση του τιμολογίου ως πλήρως εξοφλημένο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_id
msgid "Mass Mailing"
msgstr "Ομαδική Αλληλογραφία"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_campaign_id
msgid "Mass Mailing Campaign"
msgstr "Εκστρατεία Μαζικής Αλληλογραφίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Match Regex"
msgstr ""

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:21
#: code:addons/account/models/chart_template.py:983
#, python-format
msgid "Match existing invoices/bills."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_credit_ids
msgid "Matched Credit"
msgstr "Αντιστοιχισμένη Πίστωση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_debit_ids
msgid "Matched Debit"
msgstr "Αντιστοιχισμένη Χρέωση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__reconciled_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Αντιστοιχισμένα Στοιχεία Ημερολογίων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "Aντιστοίχιση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__full_reconcile_id
msgid "Matching Number"
msgstr "Αριθμός Aντιστοιχίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__max_date
msgid "Max Date of Matched Lines"
msgstr "Μέγιστη Ημερομηνία των Αντιπαραλαβομένων Γραμμών"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "Μάιος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__communication
#: model:ir.model.fields,field_description:account.field_account_payment__communication
#: model:ir.model.fields,field_description:account.field_account_register_payments__communication
msgid "Memo"
msgstr "Σχετικά "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
msgid "Memo will be computed from invoices"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "Υπόμνημα:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,field_description:account.field_account_invoice__message_has_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__record_name
msgid "Message Record Name"
msgstr "Περιγραφή εγγραφής Μηνύματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Μήνυμα για Τιμολόγιο"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Τύπος Μηνύματος: email για email μηνύματα, ειδοποίηση για μήνυμα συστήματος,"
" σχόλιο για άλλα μηνύματα όπως είναι οι απαντήσεις χρηστών"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_id
msgid "Message unique identifier"
msgstr "Μοναδικός ταυτοποιητής μηνύματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_id
msgid "Message-Id"
msgstr "Κωδικός Μηνύματος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "Διάφορα"

#. module: account
#: code:addons/account/models/chart_template.py:420
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "Ημερολόγιο Αναλυτικό"

#. module: account
#: sql_constraint:account.invoice.line:0
msgid "Missing required account on accountable invoice line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__moderator_id
msgid "Moderated By"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:175
#, python-format
msgid "Modify models"
msgstr "Τροποποίηση μοντέλων"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "Τροποποίηση ποσού φόρου"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr ""
"Τροποποίηση: δημιουργία πιστωτικού, συμψηφισμός και δημιουργία νέου "
"προσχεδίου "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "Παρακολουθήστε τα περιθώρια του προϊόντος σας από τιμολόγια"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "Μετακίνηση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_line_ids
msgid "Move Line"
msgstr "Μετακίνηση Γραμμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_count
msgid "Move Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_reconciled
msgid "Move Reconciled"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1134
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "Όνομα κίνησης (id): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__multi
#: model:ir.model.fields,field_description:account.field_account_payment__multi
#: model:ir.model.fields,field_description:account.field_account_register_payments__multi
msgid "Multi"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Πολύ-Νομισματικές Συναλλαγές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "Οι Δραστηριότητες μου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "Τα Τιμολόγιά μου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__name
#: model:ir.model.fields,field_description:account.field_account_account_tag__name
#: model:ir.model.fields,field_description:account.field_account_account_template__name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__name
#: model:ir.model.fields,field_description:account.field_account_chart_template__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account.field_account_group__name
#: model:ir.model.fields,field_description:account.field_account_incoterms__name
#: model:ir.model.fields,field_description:account.field_account_payment__name
#: model:ir.model.fields,field_description:account.field_account_payment_method__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "Περιγραφή"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__record_name
msgid "Name get of the related document."
msgstr "Όνομα που πήρε από το σχετικό έγγραφο"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "Όνομα:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__narration
msgid "Narration"
msgstr "Αφήγηση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""
"Περιηγηθείτε έυκολα σε αναφορές και δείτε τί υπάρχει πίσω από τους αριθμούς"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__needaction
#: model:ir.model.fields,help:account.field_account_invoice_send__needaction
msgid "Need Action"
msgstr "Απαιτεί Ενέργεια"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__need_moderation
msgid "Need moderation"
msgstr ""

#. module: account
#. openerp-web
#: selection:account.bank.statement,state:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#, python-format
msgid "New"
msgstr "Νέα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_name
msgid "New Journal Name"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Νέες Συναλλαγές"

#. module: account
#: code:addons/account/models/account_move.py:1111
#, python-format
msgid "New expected payment date: "
msgstr "Νέα αναμενόμενη ημερομηνία πληρωμής:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_date_deadline
#: model:ir.model.fields,field_description:account.field_account_payment__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_summary
#: model:ir.model.fields,field_description:account.field_account_payment__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_type_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_number_next
msgid "Next Number"
msgstr "Επόμενος Αριθμός"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__sequence_number_next_prefix
msgid "Next Number Prefix"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__blocked
msgid "No Follow-up"
msgstr "Να μην παρακολουθείται"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "Χωρίς μήνυμα"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:68
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:143
#, python-format
msgid "No Title"
msgstr "Χωρίς τίτλο"

#. module: account
#: code:addons/account/models/account_invoice.py:912
#, python-format
msgid ""
"No account was found to create the invoice, be sure you have installed a "
"chart of account."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__no_auto_thread
msgid "No threading for answers"
msgstr "Χωρίς νήμα για απαντήσεις"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__code_digits
msgid "No. of Digits to use for account code"
msgstr "Αριθμός Ψηφίων που θα χρησιμοποιηθούν στον κωδικό λογαριασμού"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Μη Κυκλοφορούντα Περιουσιακά Στοιχεία"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Μακροπρόθεσμες Υποχρεώσεις"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "Κανένα"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "Κανονικός Οφειλέτης"

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Not Contains"
msgstr ""

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Not done"
msgstr ""

#. module: account
#. openerp-web
#: selection:account.invoice.line,display_type:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:310
#: model:ir.model.fields,field_description:account.field_account_account_template__note
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
#, python-format
msgid "Note"
msgstr "Σημείωση"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly\n"
"                from the customer invoice."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_in_refund
msgid ""
"Note that the easiest way to create a vendor credit note it to do it "
"directly from the vendor bill."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__note
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Σημειώσεις"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:37
#, python-format
msgid "Nothing to do!"
msgstr "Καμία διαθέσιμη ενέργεια!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notification_ids
msgid "Notifications"
msgstr "Ειδοποιήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notify
msgid "Notify followers"
msgstr "Ειδοποίηση ακολούθων"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Ειδοποίηση για τους ακολούθους του εγγράφου (μόνο μαζικές αποστολές)"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "Νοέμβριος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__name
#: model:ir.model.fields,field_description:account.field_account_invoice__number
#: model:ir.model.fields,field_description:account.field_account_move__name
msgid "Number"
msgstr "Αριθμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "Αριθμός (Κίνηση)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__number
msgid "Number of Coins/Bills"
msgstr "Ποσότητα Νομισμάτων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__days
msgid "Number of Days"
msgstr "Αριθμός Ημερών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_payment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_unread_counter
#: model:ir.model.fields,help:account.field_account_payment__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "Εισαγωγή OFX"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "Οκτώβριος"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"Το Odoo σας επιτρέπει να συμψηφίσετε μια δήλωση άμεσα με\n"
"                τη σχετικό τιμολόγιο αγοράς ή πώλησης."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"Το Odoo σας επιτρέπει να συμψηφίσετε μια γραμμή δήλωσης άμεσα με\n"
"                τα σχετικά τιμολόγια αγοράς ή πώλησης."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_grouping_key
msgid "Old Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "On the"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"Μόλις τα πρόχειρα τιμολόγια επιβεβαιωθούν, δεν θα μπορείτε\n"
"                        να τα τροποποιήσετε. Τα τιμολόγια θα λάβουν μοναδικό\n"
"                        αριθμό και τα αντικείμενα ημερολογίου θα δημιουργηθούν\n"
"                        στον πίνακα λογαριασμών σας."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:599
#, python-format
msgid "Only a draft payment can be posted."
msgstr "Μόνο ένα προσχέδιο πληρωμής μπορεί να αναρτηθεί."

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Only administrators can load a charf of accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Open"
msgstr "Ανοιχτό"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:276
#, python-format
msgid "Open Balance"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:723
#: code:addons/account/static/src/xml/account_reconciliation.xml:137
#, python-format
msgid "Open balance"
msgstr "Άνοιγμα Ισοζυγίου"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_date
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_date
msgid "Opening Date"
msgstr "Ημερομηνία Έναρξης"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_journal_id
msgid "Opening Journal"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:365
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_move_posted
msgid "Opening Move Posted"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line__number
msgid "Opening Unit Numbers"
msgstr "Αριθμοί Αρχικών Μονάδων"

#. module: account
#: code:addons/account/models/account.py:169
#, python-format
msgid "Opening balance"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_credit
msgid "Opening credit"
msgstr "Πίστωση ανοίγματος"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_credit
msgid "Opening credit value for this account."
msgstr "Αξία πίστωσης ανοίγματος για αυτό το λογαριασμό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_debit
msgid "Opening debit"
msgstr "Χρέωση ανοίγματος"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_debit
msgid "Opening debit value for this account."
msgstr "Αξία χρέωσης ανοίγματος για αυτό το λογαριασμό"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:666
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and"
" make a regular revert of it in case you want to cancel it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr "Λειτουργίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__nocreate
msgid "Optional Create"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__tag_ids
#: model:ir.model.fields,help:account.field_account_account_template__tag_ids
#: model:ir.model.fields,help:account.field_account_tax__tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template__tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__option
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Επιλογές"

#. module: account
#: code:addons/account/models/account_invoice.py:711
#, python-format
msgid ""
"Or set an <a data-oe-id=%s data-oe-model=\"account.journal\" "
"href=#id=%s&model=account.journal>email alias</a> to allow draft vendor "
"bills to be created upon reception of an email."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:707
#, python-format
msgid ""
"Or share the email %s to your vendors: bills will be created automatically "
"upon mail reception."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:709
#, python-format
msgid ""
"Or share the emails %s to your vendors: bills will be created automatically "
"upon mail reception."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__payment_id
msgid "Originator Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_id
msgid "Originator tax"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Άλλα Εισοδήματα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Other Info"
msgstr "Λοιπές Πληροφορίες"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "Εξερχόμενη Κλήση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_server_id
msgid "Outgoing mail server"
msgstr "Διακομιστής εξερχόμενης αλληλογραφίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:133
#, python-format
msgid "Outstanding credits"
msgstr "Εκκρεμείς πιστώσεις"

#. module: account
#: code:addons/account/models/account_invoice.py:136
#, python-format
msgid "Outstanding debits"
msgstr "Εκκρεμείς χρεώσεις"

#. module: account
#: selection:account.invoice,activity_state:0
#: selection:account.payment,activity_state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Εκπρόθεσμο"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__overdue_msg
msgid "Overdue Payments Message"
msgstr "Μήνυμα Ληξιπρόθεσμων Οφειλών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_board_journal_1
msgid "Overview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Πακέτο"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Εξοφλημένη"

#. module: account
#: code:addons/account/models/account_payment.py:545
#, python-format
msgid "Paid Invoices"
msgstr "Εξοφλημένα Τιμολόγια"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Εξόφληση στις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__reconciled
msgid "Paid/Reconciled"
msgstr "Εξοφλημένα/Συμψηφισμένα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_id
msgid "Parent"
msgstr "Μητρικός"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__parent_id
msgid "Parent Chart Template"
msgstr "Πρότυπο Μητρικού Πίνακα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__parent_id
msgid "Parent Message"
msgstr "Γονικό μήνυμα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_path
msgid "Parent Path"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__parent_state
msgid "Parent State"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "Μερικός Συμψηφισμός"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:288
#: code:addons/account/static/src/xml/account_reconciliation.xml:305
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:account.field_account_move__partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__commercial_partner_id
msgid "Partner Company"
msgstr "Συνεργαζόμενη Εταιρία"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__country_id
msgid "Partner Company's Country"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contract_ids
#: model:ir.model.fields,field_description:account.field_res_users__contract_ids
msgid "Partner Contracts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner
msgid "Partner Is Set"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Partner Is Set & Matches"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_name
msgid "Partner Name"
msgstr "Όνομα Συνεργάτη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_type
#: model:ir.model.fields,field_description:account.field_account_payment__partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_type
msgid "Partner Type"
msgstr "Τύπος Συνεργάτη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__needaction_partner_ids
msgid "Partners with Need Action"
msgstr "Συνεργάτες με 'Απαιτεί Ενέργεια'"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:95
#, python-format
msgid "Past"
msgstr "Περασμένα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Payable"
msgstr "Πληρωτέα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_payable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Πληρωτέος Λογαριασμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Λογαριασμοί Πληρωτέοι"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit_limit
#: model:ir.model.fields,field_description:account.field_res_users__debit_limit
msgid "Payable Limit"
msgstr "Πληρωτέο Όριο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Payables"
msgstr "Πληρωτέα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__amount
#: model:ir.model.fields,field_description:account.field_account_payment__amount
#: model:ir.model.fields,field_description:account.field_account_register_payments__amount
msgid "Payment Amount"
msgstr "Ποσό Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_date
#: model:ir.model.fields,field_description:account.field_account_payment__payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_date
msgid "Payment Date"
msgstr "Ημερ. Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_difference
msgid "Payment Difference"
msgstr "Διαφορά Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_difference_handling
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference_handling
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_difference_handling
msgid "Payment Difference Handling"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__journal_id
msgid "Payment Journal"
msgstr "Ημερολόγιο Πληρωμών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "Αντιστοίχιση Συναλλαγής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Μέθοδος Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_method_id
msgid "Payment Method Type"
msgstr "Τύπος Μεθόδου Πληρωμής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment Method Types"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "Μέθοδος Πληρωμής:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
msgid "Payment Methods"
msgstr "Μέθοδοι Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_move_line_ids
msgid "Payment Move Lines"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "Payment Receipt:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__reference
msgid "Payment Ref."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_reference
msgid "Payment Reference"
msgstr "Αναφορά Πληρωμής"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__payment_id
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Όροι Πληρωμής"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Γραμμή Όρων Πληρωμής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method__payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_type
msgid "Payment Type"
msgstr "Τύπος Συναλλαγής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment term explanation for the customer..."
msgstr "Διευκρινήσεις όρων πληρωμής για τον πελάτη..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Πίστωση 15 Ημερών"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_2months
msgid "Payment terms: 2 Months"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "Πίστωση 30 Ημερών"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "30% Προκαταβολή στο τέλος του επόμενου μήνα"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_45days
msgid "Payment terms: 45 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "Τέλος του επόμενου μήνα"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Άμεση Πληρωμή"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__payment_id
msgid "Payment that created this entry"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:370
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Συναλλαγές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Αντιστοίχιση Συναλλαγών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__payments_widget
msgid "Payments Widget"
msgstr "Γραφικό Στοιχείο Πληρωμών"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "Τιμολόγιο σε Εκκρεμότητα"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "Ποσοστό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__matched_percentage
msgid "Percentage Matched"
msgstr "Ποσοστό Αντιστοιχισμένο"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "Ποσοστό της Τιμής"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr ""

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr ""

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:2018
#, python-format
msgid "Percentages on the Payment Terms lines must be between 0 and 100."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Περίοδος"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr ""

#. module: account
#: selection:account.invoice,activity_state:0
#: selection:account.payment,activity_state:0
msgid "Planned"
msgstr "Προγραμματισμένη"

#. module: account
#: code:addons/account/models/account_invoice.py:1187
#, python-format
msgid "Please add at least one invoice line."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr ""
"Παρακαλώ ελέγξτε ότι το πεδίο 'Ημερολόγιο' έχει οριστεί στην περίληψη "
"χρηματοοικονομικών συναλλαγών"

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1309
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1314
#: code:addons/account/models/account_move.py:297
#, python-format
msgid "Please define a sequence on the journal."
msgstr "Παρακαλώ ορίστε μια αλληλουχία στο ημερολόγιο."

#. module: account
#: code:addons/account/models/account_invoice.py:1185
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr ""
"Παρακαλώ ορίστε αλληλουχία στο ημερολόγιο που να σχετίζεται με το τιμολόγιο."

#. module: account
#: code:addons/account/models/company.py:357
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Please use the following communication for your payment :"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_url
msgid "Portal Access URL"
msgstr "Διεύθυνση URL πρόσβασης στην Πύλη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "Καταχωρηση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__post_at_bank_rec
msgid "Post At Bank Reconciliation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "Καταχώρηση Διαφοράς σε"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Καταχώριση Ημερολογιακών Εγγραφών"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Καταχωρημένη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Καταχωρημένες Ημερολογιακές Εγγραφές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Καταχωρημένα Ημερολογιακά Στοιχεία"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company__bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Πρόθεμα των τραπεζικών λογαριασμών"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__transfer_account_code_prefix
msgid "Prefix of the main transfer accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_code_prefix
msgid "Prefix of the transfer accounts"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
msgid "Prepayments"
msgstr "Προκαταβολές"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Προεπιλογή για τη δημιουργία καταχωρήσεων ημερολογίου κατά τη διάρκεια της "
"αντιστοίχισης τιμολογίων και πληρωμών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Preview"
msgstr "Προεπισκόπηση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Preview as a PDF"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Price"
msgstr "ΤΙΜΗ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_print
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_print
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Print"
msgstr "Εκτύπωση"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Εκτύπωση Αναφοράς με την στήλη νομίσματος εάν το νόμισμα διαφέρει από το "
"νόμισμα της εταιρίας."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_print
msgid "Print by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "Εκτυπώστε επιταγές για να πληρώσετε τους προμηθευτές σας"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "Είδος"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report__categ_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Κατηγορία Είδους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__product_image
msgid "Product Image"
msgstr "Εικόνα Είδους"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_qty
msgid "Product Quantity"
msgstr "Ποσότητα Είδους"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Πρότυπο Είδους "

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
#: model_terms:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "Είδη"

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid "Profit"
msgstr "Κέρδος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__profit_account_id
msgid "Profit Account"
msgstr "Λογαριασμός Κερδών"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:380
#, python-format
msgid ""
"Programmation Error: Can't call _get_invoice_matching_query() for different "
"rules than 'invoice_matching'"
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:505
#, python-format
msgid ""
"Programmation Error: Can't call _get_wo_suggestion_query() for different "
"rules than 'writeoff_suggestion'"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:282
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Ιδιότητες"

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Purchase"
msgstr "Αγορά"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Purchase Representative"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Purchase Tax"
msgstr "Φόρος Αγορών"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "Αγορές"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "Τοποθετήστε μέσα τα μετρητά"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "Python Code"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "Εισαγωγή QIF"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__quantity
#: model:ir.model.fields,field_description:account.field_account_move_line__quantity
msgid "Quantity"
msgstr "Ποσότητα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Quantity:"
msgstr "Ποσότητα:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__rating_value
msgid "Rating Value"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__description
#: model:ir.model.fields,field_description:account.field_cash_box_in__name
#: model:ir.model.fields,field_description:account.field_cash_box_out__name
msgid "Reason"
msgstr "Αιτία"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Αιτιολογία..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Receivable"
msgstr "Εισπρακτέα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_receivable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Εισπρακτέος Λογαριασμός"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Λογαριασμοί Εισπρακτέοι"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_id
msgid "Receivable/Payable Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Receivables"
msgstr "Εισπρακτέα"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
msgid "Receive Money"
msgstr "Είσπραξη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr "Τραπεζικός Λογαριασμός Παραλήπτη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Recipients"
msgstr "Αποδέκτες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__recompute_tax_line
msgid "Recompute Tax Line"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:79
#: code:addons/account/static/src/xml/account_reconciliation.xml:111
#: code:addons/account/static/src/xml/account_reconciliation.xml:112
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Συμψηφισμός"

#. module: account
#: model:ir.actions.client,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "Εγγραφές Συμψηφισμού"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "Reconcile Model Template"
msgstr ""

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line__reconciled
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Συμψηφισμένη"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoice_ids
msgid "Reconciled Invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "Συμψηφισμένες εγγραφές"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Συμφωνία"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.menu,name:account.action_account_reconcile_model_menu
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr ""

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "Συμφωνία σε περίληψη Τραπεζικού Λογαριασμού"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_vendor_bill_template
msgid "Record a new vendor bill"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "Καταγράψτε συναλλαγές σε ξένα νομίσματα"

#. module: account
#: code:addons/account/models/account.py:937
#, python-format
msgid "Recursion found for tax '%s'."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:287
#, python-format
msgid "Ref"
msgstr "Σχετικό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__ref
msgid "Ref."
msgstr "Παρ."

#. module: account
#: code:addons/account/controllers/portal.py:40
#: model:ir.model.fields,field_description:account.field_account_bank_statement__name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__ref
#: model:ir.model.fields,field_description:account.field_account_move__ref
#: model:ir.model.fields,field_description:account.field_account_move_line__ref
#: model:ir.model.fields,field_description:account.field_cash_box_in__ref
#, python-format
msgid "Reference"
msgstr "Σχετικό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__uom_name
msgid "Reference Unit of Measure"
msgstr "Σχετική Μονάδα Μέτρησης"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__origin
#: model:ir.model.fields,help:account.field_account_invoice_line__origin
msgid "Reference of the document that produced this invoice."
msgstr "Παραπομπή στο έγγραφο που δημιούργησε αυτό το τιμολόγιο."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__name
msgid "Reference/Description"
msgstr "Παραπομπή/Περιγραφή"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Refund"
msgstr "Επιστροφή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__refund_invoice_ids
msgid "Refund Invoices"
msgstr "Τιμολόγια Επιστροφών"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "Καταχώρηση Πληρωμής"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register Payments"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Register a bank statement"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid "Register a payment"
msgstr ""

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "Σύνηθες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__res_id
msgid "Related Document ID"
msgstr "Κωδικός σχετικού εγγράφου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__model
msgid "Related Document Model"
msgstr "Μοντέλο Σχετικού Εγγράφου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__rating_ids
msgid "Related ratings"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual
msgid "Remaining amount due."
msgstr "Οφειλόμενο Υπόλοιπο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_dest_id
msgid "Replacement Tax"
msgstr "Φόρος Αντικατάστασης"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Διεύθυνση απάντησης ηλεκτρονικού ταχυδρομείου. Ρύθμιση της reply_to "
"παρακάμπτει την αυτόματη δημιουργία νήματος."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__reply_to
msgid "Reply-To"
msgstr "Απάντηση στο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Επιλογές Αναφοράς"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Αναφορές"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__res_partner_bank_id
msgid "Res Partner Bank"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "Επαναφορά σε Προσχέδιο"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:292
#, python-format
msgid "Residual"
msgstr "Υπόλοιπο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual
msgid "Residual Amount"
msgstr "Υπόλοιπο Ποσού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Υπόλοιπο Ποσού σε Νόμισμα"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:32
#, python-format
msgid "Residual amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__user_id
msgid "Responsible"
msgstr "Υπεύθυνοι"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_user_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_category_ids
msgid "Restrict Partner Categories to"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_ids
msgid "Restrict Partners to"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_same_currency
msgid ""
"Restrict to propositions having the same currency as the statement line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue
msgid "Revenue"
msgstr "Έσοδα"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_line_id
msgid "Revenue/Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reverse_date
msgid "Reversal Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__date
msgid "Reversal date"
msgstr "Ημερ. Αντιλογισμού"

#. module: account
#: code:addons/account/models/account_move.py:390
#, python-format
msgid "Reversal of: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__auto_reverse
msgid "Reverse Automatically"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reverse_entry_id
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Αντιλογισμός"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "Κινήσεις Αντιλογισμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reversed entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Review"
msgstr "Επισκόπηση"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "Συνολική Στρογγυλοποίηση"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "Στρογγυλοποίηση ανά γραμμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__is_rounding_line
msgid "Rounding Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding_method
msgid "Rounding Method"
msgstr "Μέθοδος Στρογγυλοποίησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding
msgid "Rounding Precision"
msgstr "Ακρίβεια στρογγυλοποίησης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__strategy
msgid "Rounding Strategy"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:134
#, python-format
msgid "Run"
msgstr "Εκτέλεση"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA QR Code"
msgstr ""

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Sale"
msgstr "Πώληση"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "Πωλήσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Φόρος Πωλήσεων"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sale_tax
msgid "Sales tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Πωλητής"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_same_currency
msgid "Same Currency Matching"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:485
#, python-format
msgid "Sample invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:491
#, python-format
msgid "Sample invoice line name"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:498
#, python-format
msgid "Sample invoice line name 2"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample invoice sent!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sanitized_acc_number
msgid "Sanitized Account Number"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:218
#, python-format
msgid "Save and New"
msgstr "Αποθήκευση και Δημιουργία Νέου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as a new template"
msgstr "Αποθήκευση ως ένα νέο πρότυπο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as new template"
msgstr "Αποθήκευση ως νέο πρότυπο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""
"Αποθηκεύστε αυτήν την σελίδα και επιστρέψτε εδώ για να ρυθμίσετε αυτό το "
"χαρακτηριστικό."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Αναζήτηση Ημερολογίου Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Αναζήτηση Προτύπων Λογιστικής"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Αναζήτηση Δηλώσεων Τραπέζης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Αναζήτηση Κίνησης Τραπεζικού Λογαριασμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Αναζήτηση Πίνακα Προτύπων Λογαριασμών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Search Fiscal Positions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Αναζήτηση Τιμολογίου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Αναζήτηση εγγραφών Ημερολογίου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Αναζήτηση Κίνησης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Αναζήτηση Φορολογικών Προτύπων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Αναζήτηση Φόρων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_account_id
msgid "Second Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_type
msgid "Second Amount type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_account_id
msgid "Second Analytic Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_tag_ids
msgid "Second Analytic Tags"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_journal_id
msgid "Second Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_label
msgid "Second Journal Item Label"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_tax_id
msgid "Second Tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_amount_type
msgid "Second Tax Amount Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount
msgid "Second Write-off Amount"
msgstr ""

#. module: account
#: selection:account.invoice.line,display_type:0
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
msgid "Section"
msgstr "Τομέας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_token
msgid "Security Token"
msgstr "Διακριτικό Ασφαλείας"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:80
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Select an old vendor bill"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr ""
"Επιλέξτε εδώ το είδος αποτίμησης που σχετίζεται με αυτή τη γραμμή των όρων "
"πληρωμής."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__invoice_warn
#: model:ir.model.fields,help:account.field_res_users__invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Εάν επιλέξετε την \"Προειδοποίηση\" θα ειδοποιήσει το χρήστη με το μήνυμα.\n"
"Εάν επιλέξετε το \"Μήνυμα αποκλεισμού\" η διαδικασία θα διακοπεί και θα εμφανιστεί ένα μήνυμα. Γράψτε το μήνυμα στο επόμενο πεδίο."

#. module: account
#: model:ir.actions.act_window,name:account.invoice_send
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Send"
msgstr "Αποστολή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Send & Print"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_email
msgid "Send Email"
msgstr "Αποστολή Email"

#. module: account
#: code:addons/account/models/account_invoice.py:622
#, python-format
msgid "Send Invoice"
msgstr ""

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
msgid "Send Money"
msgstr "Πληρωμή"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sample_invoice
msgid "Send a sample invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send an invoice to test the customer portal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send sample"
msgstr ""

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice__sent
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Εστάλη"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "Σεπτέμβριος"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line__sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_journal__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__sequence
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sequence
#: model:ir.model.fields,field_description:account.field_account_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#, python-format
msgid "Set"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__active
#: model:ir.model.fields,help:account.field_account_tax_template__active
msgid "Set active to false to hide the tax without removing it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Set taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Ορίστε την τιμή σε Ψευδές εάν δεν θέλετε αυτό το πρότυπο να χρησιμοποιείται "
"ενεργά στον οδηγό που δημιουργεί Πίνακα Λογαριασμών από τα πρότυπα, αυτό "
"είναι χρήσιμο όταν θέλετε να δημιουργήσετε λογαριασμούς από αυτό το πρότυπο "
"μόνο κατά το φόρτωμα του θυγατρικού προτύπου."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "Ορισμός σε Πρόχειρη"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:85
#: code:addons/account/static/src/xml/account_reconciliation.xml:124
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.actions.act_window,name:account.action_open_settings
#: model:ir.ui.menu,name:account.menu_account_config
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "Ρυθμίσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Setup your bank account to sync bank feeds."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Setup your chart of accounts and record initial balances."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.dashboard_onboarding_company_step
msgid "Setup your company's data for reports headers."
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.model_account_invoice_action_share
msgid "Share"
msgstr "Κοινοποίηση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__code
msgid "Short Code"
msgstr "Σύντομος Κωδικός"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments__show_communication_field
msgid "Show Communication Field"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Εμφάνιση όλων των λογιστικών χαρακτηριστικών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__show_partner_bank_account
#: model:ir.model.fields,field_description:account.field_account_payment__show_partner_bank_account
#: model:ir.model.fields,field_description:account.field_account_register_payments__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Εμφάνιση ημερολογίου στο Ταμπλό"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr ""

#. module: account
#: model:res.groups,comment:account.group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes included (B2C)"
msgstr "Εμφάνιση των υποσύνολων γραμμής με τους φόρους (B2C)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_excluded
#: model:res.groups,comment:account.group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr "Εμφάνιση των υποσύνολων γραμμής χωρίς τους φόρους (B2B)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:113
#, python-format
msgid "Skip"
msgstr "Παράβλεψη"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Μικρού μεγέθους εικόνα για αυτή την επαφή. Αλλάζει αυτόματα ως εικόνα 64x64 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιείστε αυτό "
"το πεδίο οπουδήποτε απαιτείται μια μικρή εικόνα."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:780
#, python-format
msgid "Some fields are undefined"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__bank_bic
msgid "Sometimes called BIC or Swift."
msgstr "Μερικές φορές ονομάζονται BIC ή SWIFT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__origin
#: model:ir.model.fields,field_description:account.field_account_invoice_line__origin
msgid "Source Document"
msgstr "Έγγραφο Πηγή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__source_email
msgid "Source Email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__starred
msgid "Starred"
msgstr "Με αστέρι"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_from
#: model:ir.model.fields,field_description:account.field_account_common_report__date_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_from
msgid "Start Date"
msgstr "Ημερομηνία Έναρξης"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_start
msgid "Starting Balance"
msgstr "Υπόλοιπο Έναρξης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_start_id
msgid "Starting Cashbox"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_dashboard_onboarding_state
msgid "State of the account dashboard onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_invoice_onboarding_state
msgid "State of the account invoice onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_bank_data_state
msgid "State of the onboarding bank data step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_coa_state
msgid "State of the onboarding charts of account step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_fy_data_state
msgid "State of the onboarding fiscal year step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_invoice_layout_state
msgid "State of the onboarding invoice layout step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sale_tax_state
msgid "State of the onboarding sale tax step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sample_invoice_state
msgid "State of the onboarding sample invoice step"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__parent_state
msgid "State of the parent account.move"
msgstr "Κατάσταση τις γονικής κίνησης λογαριασμού"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Statement"
msgstr "Δήλωση"

#. module: account
#: code:addons/account/models/account_bank_statement.py:248
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Η δήλωση %s επιβεβαιώθηκε, δημιουργήθηκαν ημερολογιακά αντικείμενα."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Γραμμές Δήλωσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Δηλώσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "Νομοί/Πολιτείες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__states_count
msgid "States Count"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:41
#: model:ir.model.fields,field_description:account.field_account_bank_statement__state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__state
#: model:ir.model.fields,field_description:account.field_account_invoice__state
#: model:ir.model.fields,field_description:account.field_account_move__state
#: model:ir.model.fields,field_description:account.field_account_payment__state
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "Κατάσταση"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__activity_state
#: model:ir.model.fields,help:account.field_account_payment__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Step Completed!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subject
msgid "Subject"
msgstr "Θέμα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Subject..."
msgstr "Θέμα..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__subtotal
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Subtotal"
msgstr "Μερικό σύνολο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subtype_id
msgid "Subtype"
msgstr "Υποτύπος"

#. module: account
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/chart_template.py:982
#, python-format
msgid "Suggest a write-off."
msgstr ""

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:20
#, python-format
msgid "Suggest counterpart values."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "Πληρωμές Προμηθευτών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax__tag_ids
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "Ετικέτες"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "Ανάληψη των Μετρητών"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__target_move
#: model:ir.model.fields,field_description:account.field_account_common_report__target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal__target_move
msgid "Target Moves"
msgstr "Επιλεγμένες Κινήσεις"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__tax_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "Φόρος"

#. module: account
#: code:addons/account/models/chart_template.py:291
#: code:addons/account/models/chart_template.py:293
#, python-format
msgid "Tax %.2f%%"
msgstr "Φόρος %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__account_id
#: model:ir.model.fields,field_description:account.field_account_tax__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__account_id
msgid "Tax Account"
msgstr "Λογαριασμός Φόρων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__refund_account_id
msgid "Tax Account on Credit Notes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__refund_account_id
msgid "Tax Account on Refunds"
msgstr "Λογαριασμός Φόρου των Επιστροφών"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Φορολογικές Προσαρμογές"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Tax Adjustments Wizard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Ποσό Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_amount_type
msgid "Tax Amount Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Εφαρμογή Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Μέθοδος Στρογγυλοποίησης Υπολογισμού Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "Φόρος Ταμειακής Βάσης Εγγραφή του"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Ημερολόγιο Φόρου Ταμειακής Βάσης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Υπολογισμός Φόρου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Φορολογική Δήλωση"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__name
msgid "Tax Description"
msgstr "Περιγραφή Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_exigibility
msgid "Tax Due"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
msgid "Tax Excluded"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_group
#: model:ir.model.fields,field_description:account.field_account_tax__tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_group_id
msgid "Tax Group"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:212
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_tax_included
#, python-format
msgid "Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__tax_line_ids
msgid "Tax Lines"
msgstr "Γραμμές Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Χάρτης Φόρων"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Tax Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Tax Mapping of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__name
#: model:ir.model.fields,field_description:account.field_account_tax_template__name
msgid "Tax Name"
msgstr "Όνομα Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_account_id
msgid "Tax Received Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_template__type_tax_use
msgid "Tax Scope"
msgstr "Φορολογικό Πεδίο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_src_id
msgid "Tax Source"
msgstr "Πηγή Φόρου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Πρότυπο Φόρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__tax_template_ids
msgid "Tax Template List"
msgstr "Λίστα Προτύπων Φόρου"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Πρότυπα Φόρων"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_type_domain
msgid "Tax Type Domain"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_by_group
msgid "Tax amount by group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Μέθοδος στρογγυλοποίησης υπολογισμού φόρου"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_excluded
msgid "Tax display B2B"
msgstr "Εμφάνιση Φόρων B2B"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_included
msgid "Tax display B2C"
msgstr "Εμφάνιση Φόρων B2C"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_src_id
msgid "Tax on Product"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_dest_id
msgid "Tax to Apply"
msgstr ""

#. module: account
#: selection:res.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Excluded"
msgstr ""

#. module: account
#: selection:res.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Included"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line__invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "Φόροι"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Taxes Applied"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Χαρτογράφηση Φόρων"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Οι φόροι που χρησιμοποιούνται στη Αγορές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Οι φόροι που χρησιμοποιούνται στις Πωλήσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""
"Φόροι, δημοσιονομικές θέσεις, λογιστικό σχέδιο &amp; νομικές δηλώσεις για τη"
" χώρα σας"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__invoice_ids
msgid ""
"Technical field containing the invoices for which the payment has been generated.\n"
"                                                                                                                                                                       This does not especially correspond to the invoices reconciled with the payment,\n"
"                                                                                                                                                                       as it can have been generated first, and reconciled later"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,help:account.field_account_payment__move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__multi
#: model:ir.model.fields,help:account.field_account_payment__multi
#: model:ir.model.fields,help:account.field_account_register_payments__multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,help:account.field_account_payment__has_invoices
msgid "Technical field used for usability purposes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__matched_percentage
msgid "Technical field used in cash basis method"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_tax_amount_type
msgid ""
"Technical field used inside the view to make the force_second_tax_included "
"field invisible if the tax is a group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__is_second_tax_price_included
msgid ""
"Technical field used inside the view to make the force_second_tax_included "
"field readonly if the tax is already price included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__tax_amount_type
msgid ""
"Technical field used inside the view to make the force_tax_included field "
"invisible if the tax is a group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__is_tax_price_included
msgid ""
"Technical field used inside the view to make the force_tax_included field "
"readonly if the tax is already price included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__payment_method_code
#: model:ir.model.fields,help:account.field_account_payment__payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments__payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Τεχνικό πεδίο που χρησιμοποιείται για την προσαρμογή της διασύνδεσης στον "
"επιλεγμένο τύπο συναλλαγής."

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_type_domain
msgid "Technical field used to have a dynamic taxes domain on the form view."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment__hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__recompute_tax_line
msgid ""
"Technical field used to know if the tax_ids field has been modified in the "
"UI."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__show_partner_bank_account
#: model:ir.model.fields,help:account.field_account_payment__show_partner_bank_account
#: model:ir.model.fields,help:account.field_account_register_payments__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_number
msgid ""
"Technical field used to store the bank account number before its creation, "
"upon the line's processing"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_line_grouping_key
msgid ""
"Technical field used to store the old values of fields used to compute tax "
"lines (in account.move form view) between the moment the user changed it and"
" the moment the ORM reflects that change in its one2many"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__chart_template_id
msgid "Template"
msgstr "Πρότυπο"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Πρότυπο για Φορολογική Θέση"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Πρότυπα για Λογαριασμούς"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Πρότυπα για Φόρους"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Τύπος Όρου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Όροι"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "Όροι και προϋποθέσεις..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "That's on average"
msgstr "Κατά μέσο όρο"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_group
#: model:ir.model.fields,help:account.field_account_account_type__internal_group
msgid ""
"The 'Internal Group' is used to filter accounts based on the internal group "
"set on the account type."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_type
#: model:ir.model.fields,help:account.field_account_account_type__type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1051
#, python-format
msgid "The account %s (%s) is deprecated."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:536
#, python-format
msgid ""
"The account selected for payment does not belong to the same company as this"
" invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,help:account.field_res_partner_bank__journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:585
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,help:account.field_account_move_line__amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Το ποσό, εκφρασμένο σε ένα (προαιρετικό) διαφορετικό νόμισμα αν πρόκειται "
"για εγγραφή πολλαπλών νομισμάτων."

#. module: account
#: code:addons/account/models/account_move.py:687
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""
"Το ποσό εκφρασμένο στο δευτερεύον νόμισμα πρέπει να είναι θετικό όταν ο "
"λογαριασμός είναι χρεωστικός και αρνητικό όταν ο λογαριασμός είναι "
"πιστωτικός."

#. module: account
#: code:addons/account/models/account_bank_statement.py:319
#, python-format
msgid "The amount of a cash transaction cannot be 0."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:939
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or left empty."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:558
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"                                                                                                       This is useful if you install accounting after having used invoicing for some time and\n"
"                                                                                                       don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__statement_id
msgid "The bank statement used for bank reconciliation"
msgstr ""
"Η κατάσταση Κίνησης Τραπεζικού Λογαριασμού που χρησιμοποιείται στην συμφωνία"
" τραπεζών"

#. module: account
#: code:addons/account/models/account_invoice.py:1274
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__chart_template_id
msgid "The chart template for the company (if any)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr ""

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr ""

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr ""
"Ο κωδικός αυτού του λογαριασμού πρέπει να είναι μοναδικός για κάθε εταιρία !"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr ""
"Η οικονομική οντότητα που θα χρησιμοποιηθεί σε Ημερολογιακές Εγγραφές γι' "
"αυτό το τιμολόγιο"

#. module: account
#: code:addons/account/models/account_bank_statement.py:335
#, python-format
msgid ""
"The currency of the bank statement line must be different than the statement"
" currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:549
#, python-format
msgid ""
"The currency of the journal should be the same than the default credit "
"account."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:551
#, python-format
msgid ""
"The currency of the journal should be the same than the default debit "
"account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__currency_id
msgid "The currency used to enter statement"
msgstr "Νόμισμα Δήλωσης"

#. module: account
#: code:addons/account/models/account_invoice.py:2024
#, python-format
msgid "The day of the month used for this term must be stricly positive."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:193
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:42
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:906
#, python-format
msgid ""
"The field Vendor is required, please complete it to validate the Vendor "
"Bill."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,help:account.field_res_users__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:564
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__account_id
msgid "The income or expense account related to the selected product."
msgstr "Ο λογαριασμός εσόδων ή εξόδων που σχετίζεται με το επιλεγμένο είδος."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""
"Η εγγραφή στο ημερολόγιο περιέχει το αρχικό υπόλοιπο όλων των λογαριασμών "
"της εταιρείας αυτής."

#. module: account
#: code:addons/account/models/account_invoice.py:1935
#, python-format
msgid "The last line of a Payment Term should have the Balance type."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""
"Ο τύπος υπολογισμού της τελευταίας γραμμής θα πρέπει να είναι ισοσκελισμένη "
"για να διασφαλιστεί ότι θα διατεθεί ολόκληρο το ποσό."

#. module: account
#: code:addons/account/models/company.py:161
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__move_id
msgid "The move of this entry line."
msgstr "Η κίνηση της γραμμής εγγραφής"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__name
msgid "The name that will be used on account move lines"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:165
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:2026
#, python-format
msgid "The number of days used for a payment term cannot be negative."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,help:account.field_account_move_line__currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Το προαιρετικό νόμισμα αν πρόκειται για εγγραφή πολλαπλών νομισμάτων."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"Η προαιρετική ποσότητα εκφρασμένη από αυτή τη γραμμή: π.χ. πλήθος πωληθέντων"
" ειδών. Η ποσότητα δεν είναι ένα νομικό προαπαιτούμενο, αλλά είναι πολύ "
"χρήσιμη για κάποιες αναφορές."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__account_id
msgid "The partner account used for this invoice."
msgstr "Ο λογαριασμός συνεργάτη που χρησιμοποιήθηκε στο τιμολόγιο αυτό."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"Ο συνεργάτης έχει τουλάχιστον μία μη συμψηφισμένη χρέωση και πίστωση από την"
" τελευταία φορά που πραγματοποιήθηκαν η αντιστοίχιση τιμολογίων και "
"πληρωμών."

#. module: account
#: code:addons/account/models/account.py:657
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:118
#, python-format
msgid "The payment amount cannot be negative."
msgstr "Το ποσό πληρωμής δεν μπορεί να είναι αρνητικό."

#. module: account
#: code:addons/account/models/account_payment.py:602
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__reference
msgid ""
"The payment communication that will be automatically populated once the "
"invoice validation. You can also write a free communication."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"The payments which have not been matched with a bank statement will not be "
"shown in bank reconciliation data if they were made before this date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_category_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customer/vendor categories."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customers/vendors."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_nature
msgid ""
"The reconciliation model will only be applied to the selected transaction type:\n"
"        * Amount Received: Only applied when receiving an amount.\n"
"        * Amount Paid: Only applied when paying an amount.\n"
"        * Amount Paid/Received: Applied in both cases."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner
msgid ""
"The reconciliation model will only be applied when a customer/vendor is set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_amount
msgid ""
"The reconciliation model will only be applied when the amount being lower "
"than, greater than or between specified amount(s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_label
msgid ""
"The reconciliation model will only be applied when the label:\n"
"        * Contains: The proposition label must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_journal_ids
msgid ""
"The reconciliation model will only be available from the selected journals."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:672
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1806
#, python-format
msgid ""
"The selected unit of measure has to be in the same category as the product "
"unit of measure."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__sequence
#: model:ir.model.fields,help:account.field_account_tax_template__sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount_param
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount under this percentage."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:264
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:452
#, python-format
msgid ""
"There is no Transfer Account defined in the accounting settings. Please "
"define one to be able to confirm this transfer."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:181
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1559
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:48
#, python-format
msgid "There is nothing to reconcile."
msgstr "Δεν υπάρχει τίποτα για να συμφωνηθεί."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "Παρουσιάστηκε σφάλμα κατά την επεξεργασία αυτής της σελίδας."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Αυτοί οι τύποι καθορίζονται σύμφωνα με την χώρα σας. Αυτοί οι τύποι "
"περιέχουν περισσότερες πληροφορίες σχετικά με τον λογαριασμό και τους "
"τρόπους χρήσης του."

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:100
#, python-format
msgid "This Week"
msgstr "Της Εβδομάδας"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Αυτός ο λογαριασμός θα χρησιμοποιηθεί σαν λογαριασμός πληρωτέος για τον "
"παρών συνεργάτη αντί του προεπιλεγμένου."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Αυτός ο λογαριασμός θα χρησιμοποιηθεί για το συγκεκριμένο συνεργάτη ως "
"λογαριασμός εισπρακτέων αντί του προεπιλεγμένου."

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__module_account_batch_payment
msgid ""
"This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"Αυτή η επιλογή σας βοηθά να επιλέξετε εάν θέλετε να προτείνετε στον χρήστη "
"να κωδικοποιεί τα ποσοστά πωλήσεων και αγορών ή να επιλέγει από μια λίστα "
"φόρων. Αυτή η τελευταία επιλογή υποθέτει ότι το σύνολο του φόρου που "
"καθορίστηκε σε αυτό το πρότυπο είναι πλήρης"

#. module: account
#: code:addons/account/models/account_invoice.py:1482
#, python-format
msgid ""
"This customer invoice credit note has been created from: <a href=# data-oe-"
"model=account.invoice data-oe-id=%d>%s</a><br>Reason: %s"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Αυτό το πεδίο περιέχει τις πληροφορίες που σχετίζονται με την αρίθμηση των "
"ημερολογιακών καταχωρήσεων αυτού του ημερολογίου."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Αυτό το πεδίο χρησιμοποιείται για πληρωτέες και εισπρακτέες καταχωρήσεις. "
"Μπορείτε να προσθέσετε όριο ημερομηνίας για την πληρωμή αυτής της γραμμής."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"Αυτό το πεδίο χρησιμοποιείται για να καταγράψει την ονομασία του τρίτου "
"μέρους κατά την εισαγωγή των τραπεζικών στοιχείων σε ηλεκτρονική μορφή, όταν"
" ο συνεργάτης δεν έχει καταχωρηθεί στη βάση δεδομένων (ή δεν μπορεί να "
"εντοπιστεί)."

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "This is the accounting dashboard"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:627
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:636
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:650
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"Αυτό το προαιρετικό πεδίο σας επιτρέπει να συνδέσετε ένα πρότυπο λογαριασμού"
" με ένα συγκεκριμένο πρότυπο πίνακα το οποίο μπορεί να διαφέρει από αυτό στο"
" οποίο ανήκει ο γονικός λογαριασμός. Αυτό θα σας επιτρέψει να καθορίσετε "
"πρότυπα πίνακα τα οποία επεκτείνονται σε άλλον και να τα συμπληρώσετε με "
"λίγους νέους λογαριασμούς (Δεν χρειάζεται να καθορίσετε όλη την δομή που "
"πολλές φορές είναι κοινή και στους δύο)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:38
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Αυτή η σελίδα εμφανίζει όλες τις τραπεζικές συναλλαγές που θα συμψηφισθούν "
"και παρέχει ένα καθαρό περιβάλλον γι' αυτό."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"This parameter will be bypassed in case of a statement line communication "
"matching exactly existing entries"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:296
#, python-format
msgid "This payment is registered but not reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"Αυτό το τεχνικό πεδίο μπορεί να χρησιμοποιηθεί στη γραμμή χρόνος "
"δημιουργίας/εισαγωγής ώστε να αποφευχθεί η διαδικασία συμφωνίας αργότερα. Η "
"γραμμή θα δημιουργήσει απλά έναν ομόλογο γι' αυτόν τον λογαριασμό"

#. module: account
#: code:addons/account/models/account_invoice.py:1484
#, python-format
msgid ""
"This vendor bill credit note has been created from: <a href=# data-oe-"
"model=account.invoice data-oe-id=%d>%s</a><br>Reason: %s"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "Για Τιμολόγηση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "Προς Πληρωμή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:901
#, python-format
msgid "To reconcile the entries company should be the same for all entries."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:169
#, python-format
msgid "To speed up reconciliation, define"
msgstr ""

#. module: account
#: selection:account.invoice,activity_state:0
#: selection:account.payment,activity_state:0
msgid "Today"
msgstr "Σήμερα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report__amount_total
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "Σύνολο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Σύνολο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Total Base Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "Πιστωτικό Σύνολο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "Χρεωστικό Σύνολο"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users__total_invoiced
msgid "Total Invoiced"
msgstr "Σύνολο Τιμολογημένων"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit
#: model:ir.model.fields,field_description:account.field_res_users__debit
msgid "Total Payable"
msgstr "Υπόλοιπο ως Προμηθευτής"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__credit
#: model:ir.model.fields,field_description:account.field_res_users__credit
msgid "Total Receivable"
msgstr "Υπόλοιπο ως Πελάτης"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_residual
msgid "Total Residual"
msgstr "Συνολικό Υπόλοιπο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_price_total
msgid "Total Without Tax in Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "Συνολικό ποσό στο νόμισμα του τιμολογίου, αρνητικό για τα πιστωτικά."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__credit
#: model:ir.model.fields,help:account.field_res_users__credit
msgid "Total amount this customer owes you."
msgstr "Συνολικό οφειλόμενο ποσό πελάτη"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_total
msgid "Total amount with taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_subtotal
msgid "Total amount without taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__debit
#: model:ir.model.fields,help:account.field_res_users__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Το συνολικό ποσό που θα πρέπει να πληρώσετε σε αυτόν τον προμηθευτή."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total_company_signed
msgid "Total in Company Currency"
msgstr "Σύνολο σε Νόμισμα Εταιρίας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total_signed
msgid "Total in Invoice Currency"
msgstr "Σύνολο στο Νόμισμα του Τιμολογίου"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__total_entry_encoding
msgid "Total of transaction lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "Παρακολουθήστε το κόστος και τα έσοδα ανά έργο, τμήμα, κλπ."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Οι τιμές παρακολούθησης αποθηκεύονται σε ξεχωριστό μοντέλο. Αυτό το πεδίο "
"επιτρέπει την αναδόμηση της παρακολούθησης και τη δημιουργία στατιστικών "
"στοιχείων για το μοντέλο."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__tracking_value_ids
msgid "Tracking values"
msgstr "Τιμές παρακολούθησης"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:306
#, python-format
msgid "Transaction"
msgstr "Συναλλαγή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "Συναλλαγές"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__total_entry_encoding
msgid "Transactions Subtotal"
msgstr "Συναλλαγές Μερικού συνόλου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_journal_id
msgid "Transfer To"
msgstr "Μεταφορά Σε"

#. module: account
#: code:addons/account/models/account_payment.py:714
#, python-format
msgid "Transfer from %s"
msgstr "Μεταφορά από %s"

#. module: account
#: code:addons/account/models/account_payment.py:790
#, python-format
msgid "Transfer to %s"
msgstr "Μεταφορά σε %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Μετακινήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_template__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type__type
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,field_description:account.field_account_invoice__type
#: model:ir.model.fields,field_description:account.field_account_invoice_line__invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report__type
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_type
#: model:ir.model.fields,field_description:account.field_account_journal__type
#: model:ir.model.fields,field_description:account.field_account_move_line__user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__rule_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__rule_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_type
msgid "Type"
msgstr "Τύπος"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:405
#, python-format
msgid "Undefined Yet"
msgstr "Μη Καθορισμένη Ακόμη"

#. module: account
#: code:addons/account/models/company.py:386
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Αδιανέμητα Κέρδη και Ζημίες"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_unit
msgid "Unit Price"
msgstr "Τιμή Μονάδας"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Unit Price:"
msgstr "Τιμή Μονάδας:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_uom_id
msgid "Unit of Measure"
msgstr "Μονάδα Μέτρησης"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:220
#, python-format
msgid "Unknown Partner"
msgstr "Άγνωστος Συνεργάτης"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Unpaid Invoices"
msgstr "Μη Εξοφλημένα Τιμολόγια"

#. module: account
#: selection:account.move,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Μη Καταχωρημένη"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Μη Καταχωρημένες Ημερολογιακές Εγγραφές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Μη Καταχωρημένες Εγγραφές Ημερολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,field_description:account.field_account_invoice__message_unread
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Ακύρωση Συμψηφισμού"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "Εγγραφές Ακύρωσης Συμψηφισμού"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "Μη Συμψηφισμένες Συναλλαγές"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Μη Συμψηφισμένα"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "Μη Συμψηφισμένες Καταχωρήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_untaxed
msgid "Untaxed Amount"
msgstr "Αφορολόγητο Ποσό"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "Αφορολόγητο Ποσό στο Νόμισμα της Εταιρείας"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_total
msgid "Untaxed Total"
msgstr "Αφορολόγητο Σύνολο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/bills_tree_upload_views.xml:5
#, python-format
msgid "Upload"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
msgid "Upload Files"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_exigibility
msgid "Use Cash Basis"
msgstr "Χρήση Ταμειακής Βάσης"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__journal_id
msgid "Use Specific Journal"
msgstr "Χρήση Συγκ. Ημερολογίου"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__use_active_domain
msgid "Use active domain"
msgstr "Χρήση ενεργού τομέα"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr ""
"Χρησιμοποιήστε πίνακες απόσβεσης, αυτοματοποιήστε καταχωρίσεις απόσβεσης"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr ""
"Χρησιμοποιήστε τα επίπεδα παρακολούθησης και προγραμματίστε ενέργειες "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_products_in_bills
msgid "Use products in vendor bills"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_products_in_bills
msgid "Use products on vendor bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__template_id
msgid "Use template"
msgstr "Χρήση προτύπου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"Χρησιμοποιήστε αυτήν την επιλογή εάν θέλετε να ακυρώσετε ένα παραστατικό και να δημιουργήσετε\n"
"                                ένα νέο. Το πιστωτικό θα δημιουργηθεί, επικυρωθεί και συμψηφιστεί\n"
"                                με το τρέχον παραστατικό. Θα δημιουργηθεί ένα νέο προσχέδιο παραστατικού\n"
"                                ώστε να μπορείτε να το επεξεργαστείτε."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"Χρησιμοποιήστε αυτήν την επιλογή αν θέλετε να ακυρώσετε ένα παραστατικό που δεν θα έπρεπε\n"
"                                να εκδώσετε. Το πιστωτικό θα δημιουργηθεί, επικυρωθεί και συμψηφιστεί\n"
"                                με το παραστατικό. Δεν θα μπορείτε να τροποποιήσετε το πιστωτικό.  "

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type__include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence
msgid "Used to order Journals in the dashboard view"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,help:account.field_res_partner__currency_id
#: model:ir.model.fields,help:account.field_res_users__currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__vat_required
msgid "VAT required"
msgstr "Απαιτείται Φ.Π.Α."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:78
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Επικύρωση"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Επικύρωση Κίνησης Λογαριασμού"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__auto_reconcile
msgid ""
"Validate the statement line automatically (reconciliation based on your "
"rule)."
msgstr ""

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Επικυρωμένο"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value_amount
msgid "Value"
msgstr "Τιμή"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "Προμηθευτής"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:500
#: code:addons/account/models/account_invoice.py:1357
#: model:ir.model.fields,field_description:account.field_account_invoice__vendor_bill_id
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr ""
"Λογαριασμός χρέωσης Αγοραστή\n"
"Λογαριασμοί Χρέωσης Αγοραστών\n"
"Ροή Αγοραστή\n"
"Πληρωμή Αγοραστή\n"
"Ορος Πληρωμής Αγοραστή\n"
"\n"
"Πρότυπα κελιά Φόρου\n"
"Μέθοδος στρογγυλοποίησης υπολογισμού φόρου\n"
"Τα ονόματα φόρων πρέπει να είναι μοναδικά\n"
"Φόρος στο Προιόν\n"
"Φόρος να Επιβάλλεις  "

#. module: account
#: code:addons/account/models/account_invoice.py:501
#, python-format
msgid "Vendor Bill - %s"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:419
#: model:ir.actions.act_window,name:account.action_vendor_bill_template
#, python-format
msgid "Vendor Bills"
msgstr "Τιμολόγια Προμηθευτών"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:502
#: code:addons/account/models/account_payment.py:772
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Πιστωτικό Προμηθευτή"

#. module: account
#: code:addons/account/models/account_invoice.py:503
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "Πιστωτικό Προμηθευτή - %s"

#. module: account
#: code:addons/account/models/account_invoice.py:1359
#, python-format
msgid "Vendor Credit note"
msgstr "Πιστωτικό Προμηθευτή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__vendor_display_name
msgid "Vendor Display Name"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:774
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Vendor Payment"
msgstr "Πληρωμή Προμηθευτή"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Όροι Πληρωμής Προμηθευτή"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Vendor Payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "Αναφορά Προμηθευτή"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Φόροι Προμηθευτή"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Vendors"
msgstr "Προμηθευτές"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:82
#, python-format
msgid "View"
msgstr "Προβολή"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "Προσοχή"

#. module: account
#: code:addons/account/models/account_invoice.py:793
#, python-format
msgid "Warning for %s"
msgstr "Προειδοποίηση για %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "Προειδοποίηση στο Τιμολόγιο"

#. module: account
#: code:addons/account/models/account_invoice.py:1725
#: code:addons/account/models/account_invoice.py:1805
#, python-format
msgid "Warning!"
msgstr "Προειδοποίηση"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Warnings"
msgstr "Προειδοποιήσεις"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:541
#, python-format
msgid ""
"We cannot find a chart of accounts for this company, you should configure it. \n"
"Please go to Account Configuration and select or install a fiscal localization."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:480
#, python-format
msgid ""
"We cannot find any journal for this company. You should create one.\n"
"Please go to Configuration > Journals."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,help:account.field_account_invoice__website_message_ids
#: model:ir.model.fields,help:account.field_account_payment__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"When receiving an email with a bill, or uploading scanned bills, Odoo will "
"parse them (OCR) and auto-complete (AI) Draft Bills to validate."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__post_at_bank_rec
msgid ""
"Whether or not the payments made in this journal should be generated in "
"draft state, so that the related journal entries are only posted when "
"performing bank reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""
"Κατά πόσο το μήνυμα είναι ένα εσωτερικό σημείωμα (μόνο σε κατάσταση σχολίου)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__new_journal_name
msgid "Will be used to name the Journal related to this bank account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal__amount_currency
msgid "With Currency"
msgstr "Με Νόμισμα"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Με φόρο"

#. module: account
#: code:addons/account/models/account_move.py:971
#: code:addons/account/models/account_move.py:991
#, python-format
msgid "Write-Off"
msgstr "Διαγραφή"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount
msgid "Write-off Amount"
msgstr ""

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Λάθος πιστωτική ή χρεωστική αξία στην λογιστική καταχώριση !"

#. module: account
#: code:addons/account/models/account_move.py:899
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"Μπορείτε να επιλέξετε αυτό το πλαίσιο για να σημειώσετε αυτό το ημερολογιακό"
" αντικείμενο ως σε αμφισβήτηση με τον σχετικό συνεργάτη"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__partner_id
#: model:ir.model.fields,help:account.field_account_invoice_line__partner_id
msgid "You can find a contact by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1983
#, python-format
msgid ""
"You can not delete payment terms as other records still reference it. "
"However, you can archive it."
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:55
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1827
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:86
#, python-format
msgid "You can only register payments for open invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:361
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:363
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:283
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:317
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""
"Δεν μπορείτε να αλλάξετε την ιδιοκτήτρια εταιρία ενός λογαριασμού ο οποίος "
"ήδη περιέχει ημερολογιακά αντικείμενα."

#. module: account
#: code:addons/account/models/account_move.py:679
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' fields."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:187
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:583
#, python-format
msgid "You cannot delete a payment that is already posted."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:744
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:742
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""
"Δεν μπορείτε να διαγράψετε ένα τιμολόγιο που δεν είναι προσχέδιο ή "
"ακυρωμένο. Θα πρέπει να δημιουργήσετε ένα πιστωτικό σημείωμα αντ 'αυτού."

#. module: account
#: code:addons/account/models/res_config_settings.py:147
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1136
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1138
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"Δεν μπορείτε να εφαρμόσετε αυτή την τροποποίηση σε μία επιβεβαιωμένη καταχώριση. Μπορείτε να αλλάξετε απλά κάποια μη νομικά πεδία ή πρώτα να αποσυμψηφίστε.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:64
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:76
#, python-format
msgid ""
"You cannot have more than one account with \"Current Year Earnings\" as "
"type. (accounts: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:176
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:760
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:124
#, python-format
msgid "You cannot modify a journal entry linked to a posted payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:322
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"Δεν μπορείτε να τροποποιήσετε μια καταχωρημένη εγγραφή γι' αυτό το ημερολόγιο.\n"
"Πρώτα πρέπει να ορίσετε στο ημερολόγιο την δυνατότητα ακύρωσης εγγραφών."

#. module: account
#: code:addons/account/models/account_invoice.py:924
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:334
#, python-format
msgid ""
"You cannot perform this action on an account that contains journal items."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:651
#, python-format
msgid "You cannot remove the bank account from the journal once set."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:339
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:327
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:301
#, python-format
msgid ""
"You cannot switch an account to prevent the reconciliation if some partial "
"reconciliations are still pending."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1102
#, python-format
msgid "You cannot use a deprecated account."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1061
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Δεν μπορείτε να χρησιμοποιήσετε αυτόν τον γενικό λογαριασμό σε αυτό το "
"ημερολόγιο, ελέγξτε την καρτέλα 'Έλεγχος Εισαγωγής' στο σχετικό ημερολόγιο."

#. module: account
#: code:addons/account/models/account_invoice.py:76
#: code:addons/account/models/account_invoice.py:910
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "Έχετε"

#. module: account
#: code:addons/account/models/account_payment.py:622
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:50
#: code:addons/account/wizard/pos_box.py:69
#, python-format
msgid ""
"You have to define an 'Internal Transfer Account' in your cash register's "
"journal."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:147
#, python-format
msgid "You must first define an opening move."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1726
#, python-format
msgid "You must first select a partner."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:55
#, python-format
msgid "You reconciled"
msgstr "Συμψηφίσατε"

#. module: account
#: code:addons/account/models/account_move.py:1617
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1619
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Πρέπει να ρυθμίσετε το 'Λογαριασμό Κερδών από Διαφορές Συναλλάγματος' στις "
"ρυθμίσεις του λογαριασμού, ώστε να διαχειρίζεστε αυτοματοποιημένα την "
"καταχώριση των λογιστικών εγγραφών που σχετίζονται με διαφορές στις "
"συναλλαγματικές ισοτιμίες."

#. module: account
#: code:addons/account/models/account_move.py:1621
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Πρέπει να ρυθμίσετε το 'Λογαριασμό Απώλειες από Διαφορές Συναλλάγματος' στις"
" ρυθμίσεις του λογαριασμού, ώστε να διαχειρίζεστε αυτοματοποιημένα την "
"καταχώριση των λογιστικών εγγραφών που σχετίζονται με διαφορές στις "
"συναλλαγματικές ισοτιμίες."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
"Θα μπορείτε να επεξεργαστείτε και να επικυρώσετε αυτό\n"
"                                το πιστωτικό απευθείας ή να το κρατήσετε πρόχειρο,\n"
"                                αναμένοντας το έγγραφο να εκδοθεί από\n"
"                                τον προμηθευτή / πελάτη σας."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_from
msgid "Zip Range From"
msgstr "Διάστημα ΤΚ Από"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_to
msgid "Zip Range To"
msgstr "Διάστημα ΤΚ Έως"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "Κλείσιμο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "day(s) after the end of the invoice month"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "day(s) after the invoice date"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g ****************"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Bank of America"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Checking account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "π.χ. Χρεώσεις Τραπεζών"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"για αυτόν τον πελάτη. Μπορείτε να τις διαθέσετε για να επισημάνετε αυτό το "
"τιμολόγιο ως πληρωμένο"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:320
#, python-format
msgid "have been reconciled automatically."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:44
#, python-format
msgid "o_manual_statement"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "of the current month"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "of the following month"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "εκκρεμείς χρεώσεις"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "εκκρεμείς συναλλαγές"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:169
#, python-format
msgid "reconciliation models"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:154
#, python-format
msgid "remaining)"
msgstr "απομένουν)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "seconds per transaction."
msgstr "δευτερόλεπτα ανά συναλλαγή."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:321
#, python-format
msgid "statement lines"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "η μητρική εταιρία"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:55
#, python-format
msgid "transactions in"
msgstr "συναλλαγές σε"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr ""
