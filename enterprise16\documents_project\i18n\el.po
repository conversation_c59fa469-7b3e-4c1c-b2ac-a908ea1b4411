# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_project
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2019-01-16 08:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Συνημμένο"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Δημιουργία"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr ""

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__documents_project_settings
msgid "Documents Project Settings"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_tags
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_tags
msgid "Project Tags"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_folder
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__documents_project_settings
msgid "Project Workspace"
msgstr ""

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid ""
"Select the new workspace to be used for project's documents for the projects and tasks\n"
"                                of your company."
msgstr ""

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Select the tags to be applied to the project's new documents"
msgstr ""

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr "Εργασία"

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor Credit Note"
msgstr "Πιστωτικό Προμηθευτή"

#. module: documents_project
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_folder
msgid "project default workspace"
msgstr ""
