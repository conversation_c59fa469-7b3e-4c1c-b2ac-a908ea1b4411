# Translation of OpenERP Server.
# This file contains the translation of the following modules:
#	* hotel_housekeeping
#
msgid ""
msgstr ""
"Project-Id-Version: OpenERP Server 5.0.14\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2009-11-26 07:34+0000\n"
"PO-Revision-Date: 2010-11-23 01:01+0000\n"
"Last-Translator: <PERSON> (OpenERP) <Unknown>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Launchpad-Export-Date: 2011-01-18 05:26+0000\n"
"X-Generator: Launchpad (build 12177)\n"

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "From"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.module.module,description:hotel_housekeeping.module_meta_information
msgid ""
"\n"
"    Module for Hotel/Hotel Housekeeping. You can manage:\n"
"    * Housekeeping process\n"
"    * Housekeeping history room wise\n"
"\n"
"      Different reports are also provided, mainly for hotel statistics.\n"
"    "
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping_activities
msgid "Housekeeping Activities "
msgstr ""

#. module: hotel_housekeeping
#: constraint:ir.model:0
msgid ""
"The Object name must start with x_ and not contain any special character !"
msgstr ""

#. module: hotel_housekeeping
#: constraint:ir.ui.view:0
msgid "Invalid XML for View Architecture!"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.menu_open_hotel_housekeeping_form_tree_new
msgid "New HouseKeeping"
msgstr ""

#. module: hotel_housekeeping
#: view:h.activity:0
#: field:hotel.housekeeping.activities,activity_name:0
#: model:ir.actions.act_window,name:hotel_housekeeping.action_h_activity_form
#: model:ir.model,name:hotel_housekeeping.model_h_activity
msgid "Housekeeping Activity"
msgstr ""

#. module: hotel_housekeeping
#: model:product.category,name:hotel_housekeeping.hotel_housekeeping_activity_type_2_product_category
msgid "Bed Activity"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "Room Activities Report"
msgstr ""

#. module: hotel_housekeeping
#: model:product.category,name:hotel_housekeeping.hotel_housekeeping_activity_type_0_product_category
msgid "All Activities"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping.activity.type,activity_id:0
msgid "category"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,clean_type:0
msgid "Checkout"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,room_no:0
msgid "Room No"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
#: model:ir.actions.act_window,name:hotel_housekeeping.open_hotel_housekeeping_form_tree
#: model:ir.ui.menu,name:hotel_housekeeping.menu_open_hotel_housekeeping_form_tree
msgid "Housekeeping"
msgstr ""

#. module: hotel_housekeeping
#: model:product.category,name:hotel_housekeeping.hotel_housekeeping_activity_type_1_product_category
msgid "Room Activity"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
#: field:hotel.housekeeping,activity_lines:0
#: model:ir.ui.menu,name:hotel_housekeeping.menu_open_h_activity_form
msgid "Activities"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,quality:0
msgid "Good"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
#: field:hotel.housekeeping.activities,clean_end_time:0
msgid "Clean End Time"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
#: field:hotel.housekeeping.activities,clean_start_time:0
msgid "Clean Start Time"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping_activity_type
msgid "Activity Type"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping.activities,a_list:0
msgid "unknown"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,clean_type:0
msgid "Checkin"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
#: selection:hotel.housekeeping,state:0
msgid "Inspect"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.wizard_activity_menu
msgid "Activity Report"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "to"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,state:0
msgid "state"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.actions.act_window,name:hotel_housekeeping.open_hotel_housekeeping_form_tree_new
msgid "New Housekeeping"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.actions.wizard,name:hotel_housekeeping.wizard_hotel_housekeeping_activity_report
msgid "Activity Details"
msgstr ""

#. module: hotel_housekeeping
#: constraint:ir.actions.act_window:0
msgid "Invalid model name in the action definition."
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "Date"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.model,name:hotel_housekeeping.model_hotel_housekeeping
msgid "Reservation"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,clean_type:0
msgid "Clean Type"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.module.module,shortdesc:hotel_housekeeping.module_meta_information
msgid "Hotel Housekeeping"
msgstr ""

#. module: hotel_housekeeping
#: wizard_field:hotel.housekeeping.activity_list,init,date_end:0
msgid "End Date"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
msgid "Housekeeping12"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "Housekeeper Name"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,quality:0
msgid "Ok"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "Duration"
msgstr ""

#. module: hotel_housekeeping
#: field:h.activity,h_id:0
msgid "Product_id"
msgstr ""

#. module: hotel_housekeeping
#: wizard_view:hotel.housekeeping.activity_list,init:0
#: wizard_button:hotel.housekeeping.activity_list,init,print_activity_list:0
msgid "Activity List"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping.activities,housekeeper:0
msgid "Housekeeper"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,inspect_date_time:0
msgid "Inspect Date Time"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,current_date:0
msgid "Today's Date"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,clean_type:0
msgid "Daily"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
msgid "Activity Lines"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.ui.menu,name:hotel_housekeeping.menu_action_hotel_housekeeping_activity_type_view_form
msgid "Activity Types"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,state:0
#: field:hotel.housekeeping.activities,dirty:0
msgid "Dirty"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
#: selection:hotel.housekeeping,state:0
#: field:hotel.housekeeping.activities,clean:0
msgid "Clean"
msgstr ""

#. module: hotel_housekeeping
#: selection:hotel.housekeeping,state:0
msgid "Cancelled"
msgstr ""

#. module: hotel_housekeeping
#: wizard_field:hotel.housekeeping.activity_list,init,date_start:0
msgid "Start Date"
msgstr ""

#. module: hotel_housekeeping
#: rml:activity.detail:0
msgid "For Room :"
msgstr ""

#. module: hotel_housekeeping
#: wizard_field:hotel.housekeeping.activity_list,init,room_no:0
msgid "Room No."
msgstr ""

#. module: hotel_housekeeping
#: constraint:product.category:0
msgid "Error ! You can not create recursive categories."
msgstr ""

#. module: hotel_housekeeping
#: field:product.category,isactivitytype:0
msgid "Is Activity Type"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping.activity.type:0
#: model:ir.actions.act_window,name:hotel_housekeeping.action_hotel_housekeeping_activity_type_view_form
msgid "Housekeeping Activity Types"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
#: selection:hotel.housekeeping,state:0
msgid "Done"
msgstr ""

#. module: hotel_housekeeping
#: field:product.product,isact:0
msgid "Is Activity"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
#: wizard_button:hotel.housekeeping.activity_list,init,end:0
msgid "Cancel"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,inspector:0
msgid "Inspector"
msgstr ""

#. module: hotel_housekeeping
#: view:hotel.housekeeping:0
msgid "Set to Dirty"
msgstr ""

#. module: hotel_housekeeping
#: field:hotel.housekeeping,quality:0
msgid "Quality"
msgstr ""

#. module: hotel_housekeeping
#: model:ir.actions.report.xml,name:hotel_housekeeping.housekeeping_activity_detail
msgid "Activity Detail"
msgstr ""

#~ msgid "Hotel Housekeeping Management"
#~ msgstr "Hotel Housekeeping Management"
