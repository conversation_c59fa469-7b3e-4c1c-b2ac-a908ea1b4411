<?xml version="1.0"?>
<document filename="test.pdf">
  <template title="Details of Sales" author="OpenERP S.A. (<EMAIL>)" allowSplitting="20">
    <pageTemplate id="first">
      <frame id="first" x1="28.0" y1="57.0" width="511" height="728"/>
    </pageTemplate>
  </template>
  <stylesheet>
    <blockTableStyle id="Standard_Outline">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
    </blockTableStyle>
    <blockTableStyle id="Table1">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="0,0" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,0" stop="0,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="1,0" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,0" stop="1,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="2,0" stop="2,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="2,0" stop="2,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="2,-1" stop="2,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="3,0" stop="3,-1"/>
      <lineStyle kind="LINEAFTER" colorName="#e6e6e6" start="3,0" stop="3,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="3,0" stop="3,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="3,-1" stop="3,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="4,0" stop="4,-1"/>
      <lineStyle kind="LINEAFTER" colorName="#e6e6e6" start="4,0" stop="4,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="4,0" stop="4,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="4,-1" stop="4,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table2">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="0,0" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,0" stop="0,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="1,0" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,0" stop="1,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="2,0" stop="2,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="2,0" stop="2,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="2,-1" stop="2,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="3,0" stop="3,-1"/>
      <lineStyle kind="LINEAFTER" colorName="#e6e6e6" start="3,0" stop="3,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="3,0" stop="3,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="3,-1" stop="3,-1"/>
      <lineStyle kind="LINEBEFORE" colorName="#e6e6e6" start="4,0" stop="4,-1"/>
      <lineStyle kind="LINEAFTER" colorName="#e6e6e6" start="4,0" stop="4,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="4,0" stop="4,0"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="4,-1" stop="4,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table4">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="2,-1" stop="2,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="3,-1" stop="3,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="4,-1" stop="4,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="5,-1" stop="5,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="6,-1" stop="6,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table6">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="2,-1" stop="2,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="3,-1" stop="3,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="4,-1" stop="4,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="5,-1" stop="5,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="6,-1" stop="6,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table3">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="1,-1" stop="1,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table5">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table9">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#000000" start="1,-1" stop="1,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table11">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
    </blockTableStyle>
    <blockTableStyle id="Table8">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
    </blockTableStyle>
    <blockTableStyle id="Table7">
      <blockAlignment value="LEFT"/>
      <blockValign value="TOP"/>
      <lineStyle kind="LINEABOVE" colorName="#000000" start="0,0" stop="0,0"/>
      <lineStyle kind="LINEABOVE" colorName="#000000" start="1,0" stop="1,0"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,1" stop="0,1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,1" stop="1,1"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,2" stop="0,2"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,2" stop="1,2"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,3" stop="0,3"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,3" stop="1,3"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,4" stop="0,4"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,4" stop="1,4"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="0,5" stop="0,5"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="0,-1" stop="0,-1"/>
      <lineStyle kind="LINEABOVE" colorName="#e6e6e6" start="1,5" stop="1,5"/>
      <lineStyle kind="LINEBELOW" colorName="#e6e6e6" start="1,-1" stop="1,-1"/>
    </blockTableStyle>
    <initialize>
      <paraStyle name="all" alignment="justify"/>
    </initialize>
    <paraStyle name="Standard" fontName="Helvetica"/>
    <paraStyle name="Heading" fontName="Helvetica" fontSize="14.0" leading="17" spaceBefore="12.0" spaceAfter="6.0"/>
    <paraStyle name="Text body" fontName="Helvetica" spaceBefore="0.0" spaceAfter="6.0"/>
    <paraStyle name="List" fontName="Helvetica" spaceBefore="0.0" spaceAfter="6.0"/>
    <paraStyle name="Caption" fontName="Helvetica" fontSize="12.0" leading="15" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="Index" fontName="Helvetica"/>
    <paraStyle name="terp_header" fontName="Helvetica-Bold" fontSize="15.0" leading="19" alignment="LEFT" spaceBefore="12.0" spaceAfter="6.0"/>
    <paraStyle name="terp_default_8" fontName="Helvetica" fontSize="8.0" leading="10" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="Table Contents" fontName="Helvetica"/>
    <paraStyle name="Table Heading" fontName="Helvetica" alignment="CENTER"/>
    <paraStyle name="terp_default_Bold_8" fontName="Helvetica-Bold" fontSize="8.0" leading="10" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_Bold_9" fontName="Helvetica-Bold" fontSize="9.0" leading="11" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_9" fontName="Helvetica" fontSize="9.0" leading="11" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_tblheader_General" fontName="Helvetica-Bold" fontSize="8.0" leading="10" alignment="LEFT" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="terp_tblheader_General_Centre" fontName="Helvetica-Bold" fontSize="8.0" leading="10" alignment="CENTER" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="terp_default_Centre_8" fontName="Helvetica" fontSize="8.0" leading="10" alignment="CENTER" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_tblheader_Details" fontName="Helvetica-Bold" fontSize="9.0" leading="11" alignment="LEFT" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="Footer" fontName="Helvetica"/>
    <paraStyle name="Horizontal Line" fontName="Helvetica" fontSize="6.0" leading="8" spaceBefore="0.0" spaceAfter="14.0"/>
    <paraStyle name="Heading 9" fontName="Helvetica-Bold" fontSize="75%" leading="NaN" spaceBefore="12.0" spaceAfter="6.0"/>
    <paraStyle name="terp_tblheader_General_Right" fontName="Helvetica-Bold" fontSize="8.0" leading="10" alignment="RIGHT" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="terp_tblheader_Details_Centre" fontName="Helvetica-Bold" fontSize="9.0" leading="11" alignment="CENTER" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="terp_tblheader_Details_Right" fontName="Helvetica-Bold" fontSize="9.0" leading="11" alignment="RIGHT" spaceBefore="6.0" spaceAfter="6.0"/>
    <paraStyle name="terp_default_Right_8" fontName="Helvetica" fontSize="8.0" leading="10" alignment="RIGHT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_header_Right" fontName="Helvetica-Bold" fontSize="15.0" leading="19" alignment="LEFT" spaceBefore="12.0" spaceAfter="6.0"/>
    <paraStyle name="terp_header_Centre" fontName="Helvetica-Bold" fontSize="15.0" leading="19" alignment="CENTER" spaceBefore="12.0" spaceAfter="6.0"/>
    <paraStyle name="terp_default_address" fontName="Helvetica" fontSize="10.0" leading="13" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_Centre_9" fontName="Helvetica" fontSize="9.0" leading="11" alignment="CENTER" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_Right_9" fontName="Helvetica" fontSize="9.0" leading="11" alignment="RIGHT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_1" fontName="Helvetica" fontSize="2.0" leading="3" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_Right_9_Bold" fontName="Helvetica-Bold" fontSize="9.0" leading="11" alignment="RIGHT" spaceBefore="0.0" spaceAfter="0.0"/>
    <paraStyle name="terp_default_8_Italic" fontName="Helvetica-Oblique" fontSize="8.0" leading="10" alignment="LEFT" spaceBefore="0.0" spaceAfter="0.0"/>
    <images/>
  </stylesheet>
  <story>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <para style="terp_header_Centre">Details of Sales</para>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <blockTable colWidths="104.0,104.0,133.0,85.0,86.0" style="Table1">
      <tr>
        <td>
          <para style="terp_tblheader_General_Centre">Company</para>
        </td>
        <td>
          <para style="terp_tblheader_General_Centre">Users</para>
        </td>
        <td>
          <para style="terp_tblheader_General_Centre">Print Date</para>
        </td>
        <td>
          <para style="terp_tblheader_General_Centre">Start Period</para>
        </td>
        <td>
          <para style="terp_tblheader_General_Centre">End Period</para>
        </td>
      </tr>
    </blockTable>
    <blockTable colWidths="104.0,104.0,133.0,85.0,85.0" style="Table2">
      <tr>
        <td>
          <para style="terp_default_Centre_8">[[ company.name ]]</para>
        </td>
        <td>
          <para style="terp_default_Centre_8">[[ get_user_names(data['form']['user_ids']) or 'All' ]]</para>
        </td>
        <td>
          <para style="terp_default_Centre_8">[[ formatLang(time.strftime('%Y-%m-%d'),date=True) ]]</para>
        </td>
        <td>
          <para style="terp_default_Centre_8">[[ formatLang(data['form']['date_start'],date=True) ]] </para>
        </td>
        <td>
          <para style="terp_default_Centre_8">[[ formatLang(data['form']['date_end'],date=True) ]] </para>
        </td>
      </tr>
    </blockTable>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <blockTable colWidths="108.0,54.0,159.0,54.0,75.0,30.0,69.0" style="Table4">
      <tr>
        <td>
          <para style="terp_tblheader_Details">Date</para>
        </td>
        <td>
          <para style="terp_tblheader_Details">Order</para>
        </td>
        <td>
          <para style="terp_tblheader_Details">Product</para>
        </td>
        <td>
          <para style="terp_tblheader_Details_Right">Price </para>
        </td>
        <td>
          <para style="terp_tblheader_Details_Centre">Qty</para>
        </td>
        <td>
          <para style="terp_tblheader_Details_Right">Disc(%) </para>
        </td>
        <td>
          <para style="terp_tblheader_Details_Centre">Invoiced </para>
        </td>
      </tr>
    </blockTable>
    <section>
      <para style="terp_default_1">[[ repeatIn(pos_sales_details(data['form']), 'line_ids') ]]</para>
      <blockTable colWidths="108.0,54.0,159.0,54.0,75.0,30.0,69.0" style="Table6">
        <tr>
          <td>
            <para style="terp_default_9">[[ formatLang(line_ids['date_order'],date_time = True) ]]</para>
          </td>
          <td>
            <para style="terp_default_9">[[ line_ids['pos_name'] ]]</para>
          </td>
          <td>
            <para style="terp_default_9">[ [[ line_ids['code'] ]] ] [[ line_ids['name'] ]]</para>
          </td>
          <td>
            <para style="terp_default_Right_9">[[ formatLang(line_ids['price_unit'], dp='Sale Price', currency_obj = company.currency_id) ]]</para>
          </td>
          <td>
            <para style="terp_default_Centre_9">[[ formatLang(line_ids['qty']) ]] [[ line_ids['uom'] ]]</para>
          </td>
          <td>
            <para style="terp_default_Centre_9">[[ formatLang(line_ids['discount'], dp='Sale Price') ]]</para>
          </td>
          <td>
            <para style="terp_default_Centre_9">[[ getinvoice(line_ids['invoice_id']) or removeParentNode('font') ]]</para>
          </td>
        </tr>
      </blockTable>
    </section>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <blockTable colWidths="256.0,256.0" style="Table3">
      <tr>
        <td>
          <para style="terp_tblheader_Details">Taxes</para>
        </td>
        <td>
          <para style="terp_default_8">
            <font color="white"> </font>
          </para>
        </td>
      </tr>
    </blockTable>
    <section>
      <para style="terp_default_1">[[ repeatIn(gettaxamount(data['form']), 'p')]]</para>
      <blockTable colWidths="256.0,256.0" style="Table5">
        <tr>
          <td>
            <para style="terp_default_9">[[ p['name'] or removeParentNode('para') ]]</para>
          </td>
          <td>
            <para style="terp_default_Right_9_Bold">[[ formatLang(p['amount'], currency_obj = company.currency_id) or removeParentNode('tr') ]]</para>
          </td>
        </tr>
      </blockTable>
      <para style="terp_default_1">
        <font color="white"> </font>
      </para>
    </section>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <blockTable colWidths="257.0,255.0" style="Table9">
      <tr>
        <td>
          <para style="terp_tblheader_Details">Payment</para>
        </td>
        <td>
          <para style="terp_default_8">
            <font color="white"> </font>
          </para>
        </td>
      </tr>
    </blockTable>
    <section>
      <para style="terp_default_1">[[ repeatIn(getpayments(data['form']), 'p') ]]</para>
      <blockTable colWidths="255.0,255.0" style="Table11">
        <tr>
          <td>
            <para style="terp_default_9">[[ p['name'] or removeParentNode('para') ]]</para>
          </td>
          <td>
            <para style="terp_default_Right_9_Bold">[[ formatLang(p['sum'], dp='Account', currency_obj = company.currency_id) or removeParentNode('tr') ]]</para>
          </td>
        </tr>
      </blockTable>
      <para style="terp_default_1">
        <font color="white"> </font>
      </para>
    </section>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
    <blockTable colWidths="257.0,255.0" style="Table8">
      <tr>
        <td>
          <para style="terp_tblheader_Details">Summary</para>
        </td>
        <td>
          <para style="terp_default_9">
            <font color="white"> </font>
          </para>
        </td>
      </tr>
    </blockTable>
    <blockTable colWidths="257.0,254.0" style="Table7">
      <tr>
        <td>
          <para style="terp_default_Bold_9">Sales total(Revenue)</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(getsalestotal2(), dp='Sale Price', currency_obj = company.currency_id) ]]</para>
        </td>
      </tr>
      <tr>
        <td>
          <para style="terp_default_Bold_9">Qty of product</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(getqtytotal2()) ]]</para>
        </td>
      </tr>
      <tr>
        <td>
          <para style="terp_default_Bold_9">Total invoiced</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(getsuminvoice2(data['form']), dp='Sale Price', currency_obj = company.currency_id) ]]</para>
        </td>
      </tr>
      <tr>
        <td>
          <para style="terp_default_Bold_9">Total discount</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(getsumdisc(data['form']), dp='Sale Price', currency_obj =  company.currency_id) ]]</para>
      </td>
      </tr>
      <tr>
        <td>
          <para style="terp_default_Bold_9">Total paid</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(getpaidtotal2(), dp='Sale Price', currency_obj = company.currency_id) ]]</para>
        </td>
      </tr>
      <tr>
        <td>
          <para style="terp_default_Bold_9">Total of the day</para>
        </td>
        <td>
          <para style="terp_default_Right_9_Bold">[[ formatLang(gettotalofthaday(data['form']), dp='Sale Price', currency_obj = company.currency_id) ]]</para>
        </td>
      </tr>
    </blockTable>
    <para style="terp_default_8">
      <font color="white"> </font>
    </para>
  </story>
</document>
