# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_reports
# 
# Translators:
# <PERSON>, 2022
# Abe <PERSON>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:18+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_employee_exit
msgid "# Departure Employee"
msgstr "# <PERSON><PERSON><PERSON>"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_new_employee
msgid "# New Employees"
msgstr "# Karyawan Baru"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Company"
msgstr "Perusahaan"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_id
msgid "Contract"
msgstr "Kontrak"

#. module: hr_contract_reports
#: model:ir.model,name:hr_contract_reports.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Laporan Analisis Kontrak dan Karyawan"

#. module: hr_contract_reports
#: model:ir.ui.menu,name:hr_contract_reports.menu_report_contract_employee_all
msgid "Contracts"
msgstr "Kontrak"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date
msgid "Date"
msgstr "Tanggal"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_start
msgid "Date First Contract Started"
msgstr "Tanggal Kontrak Pertama Dimulai"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date_end_contract
msgid "Date Last Contract Ended"
msgstr "Tanggal Terakhir Kontrak Berakhir"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Department"
msgstr "Departemen"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__departure_reason_id
msgid "Departure Reason"
msgstr "Alasan Keluar"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__age_sum
msgid "Duration Contract"
msgstr "Durasi Kontrak"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employee"
msgstr "Karyawan"

#. module: hr_contract_reports
#: model:ir.actions.act_window,name:hr_contract_reports.contract_employee_report_action
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.hr_contract_employee_report_view_tree
msgid "Employees Analysis"
msgstr "Analisis Karyawan"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Last 365 Days"
msgstr "365 Hari Terakhir"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__start_date_months
msgid "Months of first date of this month since 01/01/1970"
msgstr "Bulan-bulan tanggal pertama bulan ini semenjak 01/01/1970"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__end_date_months
msgid "Months of last date of this month since 01/01/1970"
msgstr "Bulan-bulan tanggal terakhir bulan ini semenjak 01/01/1970"

#. module: hr_contract_reports
#: model_terms:ir.actions.act_window,help:hr_contract_reports.contract_employee_report_action
msgid "No data to display"
msgstr "Tidak data yang bisa ditampilkan"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__wage
msgid "Wage"
msgstr "Gaji Dasar"
