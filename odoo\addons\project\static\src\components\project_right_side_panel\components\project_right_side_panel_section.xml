<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="project.ProjectRightSidePanelSection" owl="1">
        <div class="o_rightpanel_section py-0" t-att-name="props.name" t-if="props.show">
            <div class="o_rightpanel_header d-flex align-items-center justify-content-between py-4" t-if="props.header">
                <div class="o_rightpanel_title d-flex flex-row-reverse align-items-center" t-if="props.slots.title">
                    <h3 class="m-0 lh-lg"><t t-slot="title"/></h3>
                </div>
                <t t-slot="header"/>
            </div>
            <div class="o_rightpanel_data fs-6" t-if="props.showData">
                <t t-slot="default"/>
            </div>
        </div>
    </t>

</templates>
