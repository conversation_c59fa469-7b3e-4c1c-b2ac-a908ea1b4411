<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <record id="group_mail_template_editor" model="res.groups">
        <field name="name">Mail Template Editor</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="base.group_system" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('mail.group_mail_template_editor'))]"/>
    </record>

    <!-- By default, allow all users to edit mail templates -->
    <record id="base.group_user" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('mail.group_mail_template_editor'))]"/>
    </record>
</data>
</odoo>
