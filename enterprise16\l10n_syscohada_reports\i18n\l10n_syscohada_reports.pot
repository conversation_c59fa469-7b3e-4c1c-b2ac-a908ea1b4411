# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_syscohada_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-07 13:42+0000\n"
"PO-Revision-Date: 2022-11-07 13:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_syscohada_reports
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_syscohada_bilan_actif_total
msgid "ACTIVE"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_bilan_actif_passif_total_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_bilan_actif_passif_total
msgid "ACTIVE - PASSIVE"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_3_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_3_syscohada_bilan_actif
msgid "AD | INTANGIBLE ASSETS"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_2_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_2_syscohada_bilan_actif
msgid "AE | Development and prospecting costs"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_3_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_3_syscohada_bilan_actif
msgid "AF | Patents, licenses, software, and similar rights"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_4_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_4_syscohada_bilan_actif
msgid "AG | Goodwill and Leasehold Rights"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_5_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_5_syscohada_bilan_actif
msgid "AH | Other intangible assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_4_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_4_syscohada_bilan_actif
msgid "AI | PROPERTY, PLANT AND EQUIPMENT"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_1_syscohada_bilan_actif
msgid "AJ | Land"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_2_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_2_syscohada_bilan_actif
msgid "AK | Buildings"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_3_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_3_syscohada_bilan_actif
msgid "AL | Fixtures and fittings"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_4_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_4_syscohada_bilan_actif
msgid "AM | Equipment, furniture and biological assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_5_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_5_syscohada_bilan_actif
msgid "AN | Transportation Equipment"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_6_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_6_syscohada_bilan_actif
msgid "AP | Advances and deposits paid on fixed assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_5_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_5_syscohada_bilan_actif
msgid "AQ | EQUITY SECURITIES"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_2_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_2_1_syscohada_bilan_actif
msgid "AR | Financial assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_2_6_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_2_6_syscohada_bilan_actif
msgid "AS | Other financial assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_6_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_6_syscohada_bilan_actif
msgid "AZ | TOTAL FIXED ASSETS (I)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_1_syscohada_bilan_actif
msgid "BA | CURRENT ASSETS O.A.S"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_2_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_2_syscohada_bilan_actif
msgid "BB | INVENTORY AND OUTSTANDING"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_3_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_3_syscohada_bilan_actif
msgid "BG | RECEIVABLES AND ASSIMILATED USES"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_4_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_4_syscohada_bilan_actif
msgid "BH | Suppliers advances paid"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_4_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_4_1_syscohada_bilan_actif
msgid "BI | Customers"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_4_2_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_4_2_syscohada_bilan_actif
msgid "BJ | Other receivables"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_2_02_4_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_2_02_4_syscohada_bilan_actif
msgid "BK | TOTAL CURRENT ASSETS"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_5_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_5_1_syscohada_bilan_actif
msgid "BQ | Investment Securities"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_5_2_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_5_2_syscohada_bilan_actif
msgid "BR | Values to be cashed"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_5_3_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_5_3_syscohada_bilan_actif
msgid "BS | Banks, postal cheques, cash registers and similar"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_2_02_5_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_2_02_5_syscohada_bilan_actif
msgid "BT | TOTAL CASH AND ASSETS (II)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_4_02_1_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_4_02_1_syscohada_bilan_actif
msgid "BU | Translation adjustment assets (V)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_11_syscohada_bilan_actif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_11_syscohada_bilan_actif
msgid "BZ | LARGE TOTAL"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.column,name:l10n_syscohada_reports.account_financial_report_l10n_pcg_cdr_column
#: model:account.report.column,name:l10n_syscohada_reports.account_financial_report_syscohada_bilan_column
msgid "Balance"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report,name:l10n_syscohada_reports.account_financial_report_syscohada_bilan
msgid "Balance Sheet (SYSCOHADA)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_1_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_1_syscohada_bilan_passif
msgid "CA | Capital"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_4_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_4_syscohada_bilan_passif
msgid "CB | Contributors uncalled capital"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_2_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_2_syscohada_bilan_passif
msgid "CD | Share Capital Premiums"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_0_3_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_0_3_syscohada_bilan_passif
msgid "CE | Revaluation differences"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_1_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_1_syscohada_bilan_passif
msgid "CF | Unavailable Reserves"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_2_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_2_syscohada_bilan_passif
msgid "CG | Free reserves"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_5_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_5_syscohada_bilan_passif
msgid "CH | Retained earnings"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_6_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_6_syscohada_bilan_passif
msgid "CJ | Net income for the year"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_7_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_7_syscohada_bilan_passif
msgid "CL | Investment Grants"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_1_8_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_1_8_syscohada_bilan_passif
msgid "CM | Regulated Provisions"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_3_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_3_syscohada_bilan_passif
msgid "CP | TOTAL EQUITY AND SIMILAR RESOURCES (I)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_2_1_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_2_1_syscohada_bilan_passif
msgid "DA | Borrowings and other financial liabilities"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_2_2_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_2_2_syscohada_bilan_passif
msgid "DB | Capital lease liabilities"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_2_3__syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_2_3__syscohada_bilan_passif
msgid "DC | Financial provision for risks and expenses"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_5_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_5_syscohada_bilan_passif
msgid "DD | TOTAL FINANCIAL LIABILITIES AND SIMILAR RESOURCES (II)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_02_6_syscohada_bilan_passif_balance
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_2_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_02_6_syscohada_bilan_passif
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_2_syscohada_bilan_passif
msgid "DF | TOTAL STABLE RESOURCES (III)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_1_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_1_syscohada_bilan_passif
msgid "DH | Circulating liabilities OOA"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_3_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_3_syscohada_bilan_passif
msgid "DJ | Operating Suppliers"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_3c_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_3c_syscohada_bilan_passif
msgid "DK | Tax and social security liabilities"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_4_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_4_syscohada_bilan_passif
msgid "DM | Other debts"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_5_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_5_syscohada_bilan_passif
msgid "DN | Provisions for short-term risks"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_6_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_6_syscohada_bilan_passif
msgid "DP | TOTAL CURRENT LIABILITIES"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_7_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_7_syscohada_bilan_passif
msgid "DQ | Banks, discount and treasury credits"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_8_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_8_syscohada_bilan_passif
msgid "DR | Banks, financial institutions and treasury credits"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_9_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_9_syscohada_bilan_passif
msgid "DT | TOTAL CASH LIABILITIES"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_10_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_10_syscohada_bilan_passif
msgid "DV | Currency translation adjustment - passive"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_03_3_11_syscohada_bilan_passif_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_03_3_11_syscohada_bilan_passif
msgid "DZ | GENERAL TOTAL"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_tot
msgid "INCOME AND EXPENSES"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_syscohada_bilan_passif_total
msgid "PASSIVE"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report,name:l10n_syscohada_reports.account_financial_report_l10n_pcg_cdr
msgid "Profit and Loss (SYSCOHADA)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_acht_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_acht
msgid "RA | Purchases of goods"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_stck_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_stck
msgid "RB | Change in inventory of goods"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc8_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc8
msgid "RC | Purchases of raw materials and related supplies"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc9_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc9
msgid "RD | Change in inventory of raw materials and related supplies"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc10_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc10
msgid "RE | Other purchases"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc11_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc11
msgid "RF | Change in inventory of other supplies"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc12_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc12
msgid "RG | Transport"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc13_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc13
msgid "RH | External Services"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc14_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc14
msgid "RI | Taxes and Fees"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc15_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc15
msgid "RJ | Other expenses"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc17_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc17
msgid "RK | Personnel expenses"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_dap_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_dap
msgid "RL | Depreciation, amortization, provisions and impairment"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_ff_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_ff
msgid "RM | Financial expenses and similar charges"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_dpdf_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_dpdf
msgid "RN | Allocation to provisions and financial depreciation"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc23_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc23
msgid "RO | Book value of fixed asset disposals"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_rp_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_rp
msgid "RP | Other expenses O.A.S"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_prct_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_prct
msgid "RQ | Employee Share Ownership"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_ir_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_ir
msgid "RS | Income taxes"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_vent_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_vent
msgid "TA | Sales of goods"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_fbr_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_fbr
msgid "TB | Sales of manufactured products"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_tc_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_tc
msgid "TC | Work, services sold"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_pa_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_pa
msgid "TD | Accessory Products"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_te_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_te
msgid "TE | Stored production (or destocking)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_tf_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_tf
msgid "TF | Capitalized production"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_tg_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_tg
msgid "TG | Operating Grants"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_th_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc_th
msgid "TH | Other products"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc5_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc5
msgid "TI | Operating expense transfers"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_rpam_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_rpam
msgid "TJ | Reversal of depreciation, provisions and impairment"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_rvfn_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_rvfn
msgid "TK | Financial and Similar Income"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_rpad_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_rpad
msgid "TL | Reversals of provisions and financial depreciation"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_trfcgf_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_trfcgf
msgid "TM | Financial expense transfers"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc21_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc21
msgid "TN | Proceeds from disposal of fixed assets"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc22_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc22
msgid "TO | Other O.A.S products"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_marg_comr_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_marg_comr
msgid "XA | TRADE MARGIN (Sum of TA to RB)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_exp_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_exp
msgid "XB | SALES REVENUES (TA + TB + TC + TD)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_fin_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_fin
msgid "XC | ADDED VALUE (XB+RA+RB) + (Sum of TE to RJ)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc18_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc18
msgid "XD | GROSS OPERATING SURPLUS (XC+RK)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc19_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc19
msgid "XE | OPERATING INCOME (XD+TJ+RL)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc20_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_syscohada_cdrc20
msgid "XF | FINANCIAL RESULT (Sum of TK to RN)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_exc_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr_char_exc
msgid "XG | INCOME FROM ORDINARY ACTIVITIES (XE+XF)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr24_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr24
msgid "XH | INCOME OUTSIDE OF ORDINARY ACTIVITIES (Sum of TN to RP)"
msgstr ""

#. module: l10n_syscohada_reports
#: model:account.report.expression,report_line_name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr25_balance
#: model:account.report.line,name:l10n_syscohada_reports.account_financial_report_line_01_syscohada_cdr25
msgid "XI | NET INCOME (XG+XH+RQ+RS)"
msgstr ""