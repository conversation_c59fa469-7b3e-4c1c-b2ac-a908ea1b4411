<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MailTemplate" owl="1">
        <t t-if="mailTemplateView">
            <div class="o_MailTemplate d-flex align-items-baseline flex-wrap" t-attf-class="{{ className }}" t-ref="root">
                <div class="text-truncate">
                    <i class="fa fa-envelope-o" title="Mail" role="img"/>
                    <span class="o_MailTemplate_name ms-2" t-esc="mailTemplateView.mailTemplate.name"/>
                    <span>:</span>
                </div>
                <div class="d-flex align-items-baseline">
                    <button
                        class="o_MailTemplate_button o_MailTemplate_preview btn btn-link py-0"
                        t-att-data-mail-template-id="mailTemplateView.mailTemplate.id"
                        t-on-click="mailTemplateView.onClickPreview"
                    >
                        Preview
                    </button>
                    <em class="o_MailTemplate_text text-500">or</em>
                    <button
                        class="o_MailTemplate_button o_MailTemplate_send btn btn-link py-0"
                        t-att-data-mail-template-id="mailTemplateView.mailTemplate.id"
                        t-on-click="mailTemplateView.onClickSend"
                    >
                        Send Now
                    </button>
                </div>
            </div>
        </t>
    </t>

</templates>
