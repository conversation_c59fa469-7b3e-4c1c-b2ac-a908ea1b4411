# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_extract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:29+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "%s's Application"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Buy credits"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Refresh"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Resend For Digitization"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"All fields will be automated by Artificial Intelligence, it might take 5 "
"seconds."
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "Pojavila se je napaka"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_applicant
msgid "Applicant"
msgstr "Kandidat"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_ir_attachment
msgid "Attachment"
msgstr "Priponka"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "CV is being Digitized"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_company
msgid "Companies"
msgstr "Podjetja"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__done
msgid "Completed flow"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_error_message
msgid "Error message"
msgstr "Sporočilo o napaki"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_can_show_resend_button
msgid "Extract Can Show Resend Button"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_can_show_send_button
msgid "Extract Can Show Send Button"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_state
msgid "Extract State"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Generated Applicant"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Generated Applicants"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__is_first_stage
msgid "Is First Stage"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__no_extract_requested
msgid "No extract requested"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_parse_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_ocr_parse
msgid "Recruitment OCR: Parse CV"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_update_ocr_status
msgid "Recruitment OCR: Update All Status"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_validate_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_ocr_validate
msgid "Recruitment OCR: Validate CV"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_config_settings__recruitment_extract_show_ocr_option_selection
msgid "Recruitment processing option"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_remote_id
msgid "Request ID to IAP-OCR"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_actions_server_digitize_cv
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "Send For Digitization"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_company__recruitment_extract_show_ocr_option_selection
msgid "Send mode on applicant attachments"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__state_processed
msgid "State Processed"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_status_code
msgid "Status code"
msgstr "Kodo statusa"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 10 seconds."
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "The document could not be found"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__to_validate
msgid "To validate"
msgstr "Za potrditev"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Unsupported image format"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__extract_not_ready
msgid "Waiting extraction, but not ready"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_upload
msgid "Waiting upload"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "Čakanje na potrditev"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "You cannot send a CV for an applicant who's not in first stage!"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "You don't have enough credit to extract data from your Resume."
msgstr ""
