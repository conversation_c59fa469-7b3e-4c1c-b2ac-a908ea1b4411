<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_appraisal_skill_view_form" model="ir.ui.view">
        <field name="name">hr.appraisal.skill.form</field>
        <field name="model">hr.appraisal</field>
        <field name="inherit_id" ref="hr_appraisal.view_hr_appraisal_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='appraisal']" position="after">
                <page string="Skills">
                    <div class="o_hr_skills_group o_group_skills col-lg-12 ps-0 pe-0 position-relative w-100">
                        <span class="o_appraisal_overlay" attrs="{'invisible': [('state', '!=', 'new')]}">
                            Skills tab will be active once the appraisal is confirmed.
                        </span>
                        <field mode="tree" nolabel="1" name="skill_ids"
                                widget="appraisal_skills_one2many"
                                context="{'appraisal_state': state}"
                                attrs="{'readonly': &quot;['|', ('state','!=','pending'), '&amp;', ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}"
                                class="o_appraisal_skill">
                            <tree editable="bottom">
                                <field name="skill_type_id" invisible="1"/>
                                <field name="skill_id" width="200px"
                                        options="{'no_create_edit': True}"
                                        context="{'from_skill_dropdown': True}"/>
                                <field name="skill_level_id"
                                        domain="[('skill_type_id', '=', skill_type_id)]"
                                        width="100px"
                                        options="{'no_create': True}"
                                        context="{'from_skill_level_dropdown': True}"/>
                                <field name="level_progress" widget="progressbar" width="200px"/>
                                <field name="justification" width="500px"/>
                            </tree>
                        </field>
                    </div>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
