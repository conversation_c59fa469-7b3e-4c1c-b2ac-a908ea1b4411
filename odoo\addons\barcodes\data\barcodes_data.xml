<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    	<record id="default_barcode_nomenclature" model="barcode.nomenclature">
            <field name="name">Default Nomenclature</field>
        </record>

        <record id="barcode_rule_product" model="barcode.rule">
            <field name="name">Product Barcodes</field>
            <field name="barcode_nomenclature_id" ref="default_barcode_nomenclature"/>
            <field name="sequence">90</field>
            <field name="type">product</field>
            <field name="encoding">any</field>
            <field name="pattern">.*</field>
        </record>
</odoo>
