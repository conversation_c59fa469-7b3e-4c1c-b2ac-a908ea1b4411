<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <record id="l10n_pe_edi_certificate_form" model="ir.ui.view">
            <field name="name">l10n_pe_edi.certificate.form</field>
            <field name="model">l10n_pe_edi.certificate</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="content"/>
                            <field name="password" password="True"/>
                            <label for="date_start" string="Validity"/>
                            <div>
                                <field name="date_start"/> -
                                <field name="date_end"/>
                            </div>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="l10n_pe_edi_certificate_tree" model="ir.ui.view">
            <field name="name">l10n_pe_edi.certificate.tree</field>
            <field name="model">l10n_pe_edi.certificate</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="l10n_pe_edi_certificate_action" model="ir.actions.act_window">
            <field name="name">Certificates for EDI invoices on Peru</field>
            <field name="res_model">l10n_pe_edi.certificate</field>
            <field name="view_mode">tree,form</field>
            <field name="context" eval="{'search_default_my_certificates': 1}"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">Create the first certificate</p>
            </field>
        </record>

    </data>
</odoo>
