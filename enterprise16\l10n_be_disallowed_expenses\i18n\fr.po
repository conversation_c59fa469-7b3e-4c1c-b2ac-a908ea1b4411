# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_disallowed_expenses
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-10 09:42+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1212
msgid "Abnormal or voluntary benefits"
msgstr "Avantages anormaux ou bénévoles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1215
msgid "Benefits of meal vouchers, sports/culture vouchers or eco-vouchers"
msgstr "Avantages de titres-repas, chèques sport/culture ou éco-chèques"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1222
msgid "Bonuses, subsidies in capital and in regional interest"
msgstr "Primes, subsides en capital et en intérêt régionaux"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1205
msgid "Car expenses and capital losses on non-deductible motor vehicles"
msgstr ""
"Frais de voiture et moins-values sur véhicules automobiles non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1206
msgid "Car expenses up to a portion of the benefit in kind"
msgstr ""
"Frais de voiture à concurrence d'une quotité de l'avantage de toute nature"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1220
msgid "Compensation for missing coupon"
msgstr "Indemnités pour coupon manquant"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1229
msgid "Correction based on the minimum taxable income from diamond trading "
msgstr ""
"Correction en fonction du montant minimum du revenu net imposable issu du "
"commerce de diamants "

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1233
msgid "Employee participation and beneficiary bonuses"
msgstr "Participation des travailleurs et primes bénéficiaires"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1210
msgid "Exaggerated interests"
msgstr "Intérêts exagérés"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1235
msgid ""
"Expenses of the mobility allowance up to a proportion of the benefit kind"
msgstr ""
"Frais de l’allocation de mobilité à concurrence d’une quotité de l’avantage "
"de toute nature"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1232
msgid "Expenses of works approved tax shelter"
msgstr "Frais d'œuvres agréées tax shelter"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1203
msgid "Fines, penalties and confiscations of any kind"
msgstr "Amendes, pénalités et confiscations de toute nature"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1207
msgid "Hospitality expenses and non-deductible business gifts"
msgstr "Frais de réception et de cadeaux d'affaires non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1211
msgid "Interest related to a portion of certain borrowings"
msgstr "Intérêts relatifs à une partie de certains emprunts"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1216
msgid "Liberalities"
msgstr "Libéralités"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1234
msgid "Non-deductible mobility allowances"
msgstr "Allocations de mobilité non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1223
msgid "Non-deductible payments to certain States"
msgstr "Paiements non déductibles vers certains États"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1204
msgid "Non-deductible pensions, capital, employer contributions and premiums"
msgstr "Pensions, capitaux, cotisations et primes patronales non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1208
msgid "Non-deductible restaurant expenses"
msgstr "Frais de restaurant non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1201
msgid "Non-deductible taxes"
msgstr "Impôts non déductibles"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1209
msgid "Non-specific professional clothing expenses"
msgstr "Frais de vêtements professionnels non spécifiques"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1239
msgid "Other disallowed expenses"
msgstr "Autres dépenses non admises"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1226
msgid ""
"Positive corrections in application under Diamond Scheme. Positive "
"difference between the gross profit determined on a flat-rate basis and the "
"gross profit determined on an accounting basis"
msgstr ""
"Corrections positives en application du Régime Diamant. Différence positive "
"entre le bénéfice brut déterminé forfaitairement et le bénéfice brut "
"déterminé comptablement"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1228
msgid ""
"Positive difference between the reference income for a corporate executive "
"and the income of the highest corporate executive"
msgstr ""
"Différence positive entre la rémunération de référence pour un dirigeant "
"d’entreprise et la rémunération de dirigeant d’entreprise la plus élevée"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1217
msgid "Reductions in value and losses on shares or parts"
msgstr "Réductions de valeur et moins-values sur actions ou parts"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1202
msgid "Regional taxes, duties and fees"
msgstr "Impôts, taxes et rétributions régionaux"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1231
msgid ""
"Resumption of deduction for innovation income following non-reinvestment in "
"qualifying expenses"
msgstr ""
"Reprise de déduction pour revenus d’innovation suite au non remploi en "
"dépenses qualifiantes"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1218
msgid "Resumptions of previous exemptions"
msgstr "Reprises d'exonérations antérieures"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1230
msgid ""
"Reversal of deduction for innovation income in the event of staggering of "
"historical costs"
msgstr ""
"Reprise de déduction pour revenus d’innovation en cas d’étalement des frais "
"historiques"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1214
msgid "Social benefits"
msgstr "Avantages sociaux"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1225
msgid "Unjustified expenses"
msgstr "Dépenses non justifiées"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1227
msgid "Write-down on inventory and non-deductible costs"
msgstr "Réduction de valeur sur stock et frais non déductibles"
