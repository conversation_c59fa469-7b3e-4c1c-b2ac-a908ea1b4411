# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_batch
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# Anna, 2023
# JanaAvalah, 2023
# <PERSON><PERSON><PERSON> a<PERSON>, 2023
# <PERSON>eli Õigus <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>s, 2024
# Egon <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid ""
"<span class=\"o_form_label fw-bold\" attrs=\"{'invisible':[('auto_batch', "
"'=', False)]}\">Group by</span>"
msgstr ""
"<span class=\"o_form_label fw-bold\" attrs=\"{'invisible':[('auto_batch', "
"'=', False)]}\">grupeeri</span>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>FROM</strong>"
msgstr "<strong>ALATES</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Partii/Seerianumber</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr "<strong>Pakend</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Toote ribakood</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Vastutav:</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>TO</strong>"
msgstr "<strong>KUHU</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>→</strong>"
msgstr "<strong>→</strong>"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of lines if the transfer is added to it.\n"
"Leave this value as '0' if no line limit."
msgstr ""
"Koondsaatelehtedele ei lisata automaatselt ridu juurde, kui on ületatud eelnevalt määratud ridade arv.\n"
"Kui ridadele piirangut ei soovi, jätke selle väärtuseks \"0\"."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of transfers.\n"
"Leave this value as '0' if no transfer limit."
msgstr ""
"A transfer will not be automatically added to batches that will exceed this number of transfers.\n"
"Leave this value as '0' if no transfer limit."

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandi tüüp"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_state
msgid "Activity State"
msgstr "Tegevuse staatus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevustüübi ikoon"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Add Operations"
msgstr "Lisa tegevused"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to"
msgstr "Lisa siirded"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to"
msgstr "Lisa"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_move_line.py:0
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree_detailed_wave
#, python-format
msgid "Add to Wave"
msgstr "Lisa laine siirdele"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
msgid "Add to batch"
msgstr "Lisa koondsaatelehele"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_add_to_wave_action_stock_picking
msgid "Add to wave"
msgstr "Lisa laine siirdele"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Allocation"
msgstr "Taotlus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__allowed_picking_ids
msgid "Allowed Picking"
msgstr "Lubatud siire"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#, python-format
msgid "Assigned to %s Responsible"
msgstr "Määratud %s vastutajale"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_auto_confirm
msgid "Auto-confirm"
msgstr "Automaatne kinnitus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__auto_batch
msgid "Automatic Batches"
msgstr "Automaatne komplekteerimine"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
msgid "Automatically group batches by contacts."
msgstr "Komplekteerige automaatselt kontakti järgi."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Automatically group batches by destination country."
msgstr "Komplekteerige automaatselt asukoha maa järgi."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Automatically group batches by their destination location."
msgstr "Komplekteerige automaatselt sihtasukoha järgi."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Automatically group batches by their source location."
msgstr "Komplekteerige automaatselt algallika järgi."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__auto_batch
msgid ""
"Automatically put pickings into batches as they are confirmed when possible."
msgstr ""
"Kui võimalik, jaotage siirded kinnitamisel automaatselt koondsaatelehele."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr "Triipkood"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_form_inherit
msgid "Batch"
msgstr "Komplekt"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_move_line_view_search_inherit_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_internal_search_inherit
#, python-format
msgid "Batch Transfer"
msgstr "Koondsaateleht"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Transfer Lines"
msgstr "Koondsaatelehe read"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Batch Transfers"
msgstr "Koondsaateleht"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Transfers not finished"
msgstr "Koondsaateleht pole lõpetatud"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this transfer"
msgstr "Selle siirdega seotud koondsaateleht"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Batches"
msgstr "Koondsaatelehed"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "Tühista"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__cancel
msgid "Cancelled"
msgstr "Tühistatud"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Cannot create wave transfers"
msgstr "Ei saa luua lainetena siirdeid"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Check Availability"
msgstr "Kontrolli saadavust"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "Vali sildi kujundus"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Type of Labels To Print"
msgstr "Vali prinditava sildi tüüp"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Clear quantities"
msgstr "Tühjenda kogus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__company_id
msgid "Company"
msgstr "Ettevõte"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Confirm"
msgstr "Kinnitage"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Contact"
msgstr "Kontakt"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_batch
msgid "Count Picking Batch"
msgstr "Count Picking Batch"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_wave
msgid "Count Picking Wave"
msgstr "Count Picking Wave"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Create a new batch transfer"
msgstr "Loo uus koondsaateleht"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid "Create a new wave transfer"
msgstr "Loo uus lainetena siire"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr "Loodud"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Destination Country"
msgstr "Sihtriik"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Destination Location"
msgstr "Sihtkoht"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Detailed Operations"
msgstr "Detailsed tegevused"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr "Kuva nimi"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__done
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Done"
msgstr "Tehtud"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__draft
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Draft"
msgstr "Planeerimata"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Future Activities"
msgstr "Tulevased tegevused"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "Rühmitamine"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr "ID"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon"
msgstr "Ikoon"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Kui kontrollitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningate sõnumitel saatmiserror."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#, python-format
msgid ""
"If the Automatic Batches feature is enabled, at least one 'Group by' option "
"must be selected."
msgstr ""
"Kui automaatne koondsaatelehtede loomine on valitud, peab vähemalt üks "
"grupeerimise viis valitud."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "Töös"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__in_progress
msgid "In progress"
msgstr "Töös"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud (millal)"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud (kelle poolt)"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Late Activities"
msgstr "Hilinenud tegevused"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__line_ids
msgid "Line"
msgstr "Rida"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of transfers associated to this batch"
msgstr "Selle komplektiga seotud siirded"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid "Maximum lines per batch"
msgstr "Koondsaatelehe maksimaalne ridade arv"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid "Maximum transfers per batch"
msgstr "Koondsaatelehe maksimaalne siirete arv"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise veateade"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "Sõnumid"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__mode
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__mode
msgid "Mode"
msgstr "Mudel"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree
msgid "Move Lines"
msgstr "Siirde kirjed"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "My Transfers"
msgstr "Minu siirded"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse kuupäev"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of errors"
msgstr "Vigade arv"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Tegevust nõudvate sõnumite arv"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_id
msgid "Operation Type"
msgstr "Operatsiooni tüüp"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Operations"
msgstr "Tegevused"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__picking_ids
msgid "Picking"
msgstr "Laoleht"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Laolehe tüüp"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Please add 'Done' quantities to the batch picking to create a new pack."
msgstr "Uue paki loomiseks lisage \"Tehtud\" kogused koondsiirdele."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "Prindi"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print Labels"
msgstr "Prindi sildid"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "Toode"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Toote liikumised"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Put in Pack"
msgstr "Paki"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "Kogus"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "Vastutaja"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise viga"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr "Planeeritud kuupäev"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__scheduled_date
msgid ""
"Scheduled date for the transfers to be processed.\n"
"              - If manually set then scheduled date for all transfers in batch will automatically update to this date.\n"
"              - If not manually changed and transfers are added/removed/updated then this will be their earliest scheduled date\n"
"                but this scheduled date will not be set for all transfers in batch."
msgstr ""
"Planeeritud kuupäev siirete protsessimiseks.\n"
"              - Kui on käsitsi määratud, siis selle komplekti kõikide siirete planeeritud kuupäev uueneb automaatselt sellele kuupäevale.\n"
"              - Kui seda ei muudeta käsitsi ja siirdeid lisatakse/eemaldatakse/värskendatakse, on see nende varaseim kavandatud kuupäev\n"
"                kuid seda planeeritud kuupäeva ei määrata kõigile siiretele komplektis."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Transfer"
msgstr "Otsi koondsaatelehte"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Set quantities"
msgstr "Määra kogused"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid "Show Allocation Button"
msgstr "Näita toodete jaotamise nuppu"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid "Show Check Availability"
msgstr "Näita Kontrolli saadavust"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_clear_qty_button
msgid "Show Clear Qty Button"
msgstr "Näita Kustuta kogused nuppu"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_lots_text
msgid "Show Lots Text"
msgstr "Näita partii kirja"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_set_qty_button
msgid "Show Set Qty Button"
msgstr "Näita Määra kogused nuppu"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_validate
msgid "Show Validate Button"
msgstr "Näita Kinnitamise nuppu"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Näita kõiki andmeid, mille järgmise tegevuse kuupäev on ennem tänast "
"kuupäeva"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Source Location"
msgstr "Lähtekoht"

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr "Etapp muudetud"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "Staatus"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "Olek"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Transfer"
msgstr "Lao koondsaateleht"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move
msgid "Stock Move"
msgstr "Laoliikumine"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Lao pakendi sihtkoht"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_line_ids
msgid "Stock move lines"
msgstr "Laoliikumiste read"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_ids
msgid "Stock moves"
msgstr "Laoliikumised"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr "Kokkuvõte:"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"The following transfers cannot be added to batch transfer %s. Please check their states and operation types, if they aren't immediate transfers.\n"
"\n"
"Incompatibilities: %s"
msgstr ""
"Järgnevaid siirdeid ei saa lisada koondsiirdesse %s. Palun kontrollige nende staatuseid ja operatsiooni tüüpe, ega need ei ole kohesed siirded või mõne teise komplekti siirded.\n"
"\n"
"Sobimatud: %s"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"Koondiirde eesmärgiks on koondada operatsioonid, mis\n"
"            (peaks) olema tehtud koos tõstmaks nende efektiivsust. \n"
"            Samuti võib olla kasulik määrata töösid (üks inimene= üks kogumik) või\n"
"aidata toimingute ajastamist (ülesanded teha valmis kella 1ks)"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid ""
"The goal of the wave transfer is to group operations from different transfer\n"
"                    together in order to increase their efficiency.\n"
"                    It may also be useful to assign jobs (one person = one batch) or\n"
"                    help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"The goal of the wave transfer is to group operations from different transfer\n"
"                    together in order to increase their efficiency.\n"
"                    It may also be useful to assign jobs (one person = one batch) or\n"
"                    help the timing management of operations (tasks to be done at 1pm)."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected operations should belong to a unique company."
msgstr "Valitud toimingud peaksid kuuluma unikaalsele ettevõttele."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
#, python-format
msgid "The selected pickings should belong to an unique company."
msgstr "Valitud siirded peaksid kuuluma unikaalsele ettevõttele"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to a unique company."
msgstr "Valitud siirded peaksid kuuluma unikaalsele ettevõttele."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to the same operation type"
msgstr "Valitud siirded peaksid kuuluma samasse operatsioonitüüpi."

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__is_wave
msgid "This batch is a wave"
msgstr "See koond on lainena siire"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "To Do"
msgstr "Tegemiseks"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Today Activities"
msgstr "Tänased tegevused"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer"
msgstr "Siirded"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Transferred by"
msgstr "Siirdatud"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Transfers"
msgstr "Siirded"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "Operatsiooni tüüp"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Validate"
msgstr "Kinnita"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_warehouse
msgid "Warehouse"
msgstr "Ladu"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__wave_id
msgid "Wave Transfer"
msgstr "Lainetena siirde"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_add_to_wave
msgid "Wave Transfer Lines"
msgstr "Lainetena siirde read"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_picking_tree_wave
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_wave_menu
msgid "Wave Transfers"
msgstr "Lainetena siirded"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Waves"
msgstr "Lainetena siirded"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
msgid "When checked, create the batch in draft status"
msgstr "Kui see on märgitud, looge koondsaateleht \"Mustand\" staatuses"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You cannot delete Done batch transfers."
msgstr "Te ei saa \"Tehtud\" statuses koondsaatelehti kustutada."

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You have to set some pickings to batch."
msgstr "Te peate sisestama grupeerimiseks mõningad siirded"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__new
msgid "a new batch transfer"
msgstr "loo uus koondsaateleht"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__new
msgid "a new wave transfer"
msgstr "a new wave transfer"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__existing
msgid "an existing batch transfer"
msgstr "lisa olemasolevale koondsaatelehele"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__existing
msgid "an existing wave transfer"
msgstr "an existing wave transfer"
