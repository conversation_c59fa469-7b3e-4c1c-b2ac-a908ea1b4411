# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _, tools
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import pytz

_logger = logging.getLogger(__name__)

class BiotimeReconciliationReport(models.Model):
    _name = 'biotime.reconciliation.report'
    _description = 'Biotime Reconciliation Report'
    _auto = False
    _order = 'date desc, employee_id'
    
    employee_id = fields.Many2one('hr.employee', string="Employee", readonly=True)
    date = fields.Date(string="Date", readonly=True)
    
    biotime_check_in = fields.Datetime(string="Biotime Check In", readonly=True)
    biotime_check_out = fields.Datetime(string="Biotime Check Out", readonly=True)
    
    odoo_check_in = fields.Datetime(string="Odoo Check In", readonly=True)
    odoo_check_out = fields.Datetime(string="Odoo Check Out", readonly=True)
    
    biotime_punch_in_id = fields.Many2one('biotime.punch', string="Biotime Punch In", readonly=True)
    biotime_punch_out_id = fields.Many2one('biotime.punch', string="Biotime Punch Out", readonly=True)
    
    attendance_id = fields.Many2one('hr.attendance', string="Attendance Record", readonly=True)
    
    status = fields.Selection([
        ('matched', 'Matched'),
        ('biotime_only', 'In Biotime Only'),
        ('odoo_only', 'In Odoo Only'),
        ('time_mismatch', 'Time Mismatch'),
        ('missing_checkout', 'Missing Check Out'),
        ('missing_checkin', 'Missing Check In')
    ], string="Status", readonly=True)
    
    discrepancy_minutes = fields.Float(string="Discrepancy (Minutes)", readonly=True, 
        help="Difference in minutes between Biotime and Odoo records")
    
    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
        CREATE OR REPLACE VIEW %s AS (
            WITH biotime_records AS (
                -- Get Biotime check-in records
                SELECT 
                    bp_in.employee_id,
                    bp_in.punch_datetime::date AS date,
                    bp_in.punch_datetime AS check_in,
                    bp_out.punch_datetime AS check_out,
                    bp_in.id AS punch_in_id,
                    bp_out.id AS punch_out_id,
                    bp_in.attendance_id
                FROM biotime_punch bp_in
                LEFT JOIN biotime_punch bp_out ON 
                    bp_in.employee_id = bp_out.employee_id AND
                    bp_in.punch_datetime::date = bp_out.punch_datetime::date AND
                    bp_in.punch_state = '0' AND bp_out.punch_state = '1' AND
                    bp_out.punch_datetime > bp_in.punch_datetime
                WHERE bp_in.punch_state = '0'
                GROUP BY 
                    bp_in.employee_id, 
                    bp_in.punch_datetime::date, 
                    bp_in.punch_datetime,
                    bp_out.punch_datetime,
                    bp_in.id,
                    bp_out.id,
                    bp_in.attendance_id
            ),
            
            odoo_records AS (
                -- Get Odoo attendance records
                SELECT 
                    a.employee_id,
                    a.check_in::date AS date,
                    a.check_in,
                    a.check_out,
                    a.id AS attendance_id
                FROM hr_attendance a
            ),
            
            combined AS (
                -- Combine Biotime and Odoo records based on employee and date
                SELECT
                    COALESCE(b.employee_id, o.employee_id) AS employee_id,
                    COALESCE(b.date, o.date) AS date,
                    b.check_in AS biotime_check_in,
                    b.check_out AS biotime_check_out,
                    o.check_in AS odoo_check_in,
                    o.check_out AS odoo_check_out,
                    b.punch_in_id AS biotime_punch_in_id,
                    b.punch_out_id AS biotime_punch_out_id,
                    COALESCE(b.attendance_id, o.attendance_id) AS attendance_id,
                    -- Determine match status
                    CASE
                        WHEN b.employee_id IS NULL THEN 'odoo_only'
                        WHEN o.employee_id IS NULL THEN 'biotime_only'
                        WHEN o.check_out IS NULL AND b.check_out IS NOT NULL THEN 'missing_checkout'
                        WHEN b.check_out IS NULL AND o.check_out IS NOT NULL THEN 'missing_checkout'
                        WHEN ABS(EXTRACT(EPOCH FROM (b.check_in - o.check_in))/60) > 5 OR 
                             (b.check_out IS NOT NULL AND o.check_out IS NOT NULL AND 
                              ABS(EXTRACT(EPOCH FROM (b.check_out - o.check_out))/60) > 5) 
                            THEN 'time_mismatch'
                        ELSE 'matched'
                    END AS status,
                    -- Calculate time discrepancy in minutes
                    CASE
                        WHEN b.check_in IS NOT NULL AND o.check_in IS NOT NULL 
                            THEN ABS(EXTRACT(EPOCH FROM (b.check_in - o.check_in))/60)
                        ELSE 0
                    END AS check_in_diff,
                    CASE
                        WHEN b.check_out IS NOT NULL AND o.check_out IS NOT NULL 
                            THEN ABS(EXTRACT(EPOCH FROM (b.check_out - o.check_out))/60)
                        ELSE 0
                    END AS check_out_diff
                FROM biotime_records b
                FULL OUTER JOIN odoo_records o ON 
                    b.employee_id = o.employee_id AND 
                    b.date = o.date
            )
            
            SELECT
                row_number() OVER () as id,
                employee_id,
                date,
                biotime_check_in,
                biotime_check_out,
                odoo_check_in,
                odoo_check_out,
                biotime_punch_in_id,
                biotime_punch_out_id,
                attendance_id,
                status,
                (check_in_diff + check_out_diff) AS discrepancy_minutes
            FROM combined
            WHERE date >= current_date - interval '30 days'
        )
        """ % self._table)
    
    def action_fix_record(self):
        """Action to fix discrepancies in the selected record"""
        self.ensure_one()
        
        if self.status == 'biotime_only':
            # Create missing attendance in Odoo
            if self.biotime_check_in and self.employee_id:
                vals = {
                    'employee_id': self.employee_id.id,
                    'check_in': self.biotime_check_in,
                }
                if self.biotime_check_out:
                    vals['check_out'] = self.biotime_check_out
                
                attendance = self.env['hr.attendance'].create(vals)
                
                # Link the attendance to the biotime punches
                if self.biotime_punch_in_id:
                    self.biotime_punch_in_id.write({
                        'attendance_id': attendance.id,
                        'state': 'processed'
                    })
                if self.biotime_punch_out_id:
                    self.biotime_punch_out_id.write({
                        'attendance_id': attendance.id,
                        'state': 'processed'
                    })
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'reload',
                }
                
        elif self.status == 'missing_checkout' and self.attendance_id and self.biotime_check_out:
            # Add missing check-out to attendance
            self.attendance_id.write({
                'check_out': self.biotime_check_out
            })
            
            # Link the punch to the attendance
            if self.biotime_punch_out_id:
                self.biotime_punch_out_id.write({
                    'attendance_id': self.attendance_id.id,
                    'state': 'processed'
                })
                
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
            
        elif self.status == 'time_mismatch' and self.attendance_id:
            # Update attendance times to match Biotime
            update_vals = {}
            if self.biotime_check_in and abs((self.biotime_check_in - self.odoo_check_in).total_seconds() / 60) > 5:
                update_vals['check_in'] = self.biotime_check_in
                
            if self.biotime_check_out and self.odoo_check_out and \
               abs((self.biotime_check_out - self.odoo_check_out).total_seconds() / 60) > 5:
                update_vals['check_out'] = self.biotime_check_out
                
            if update_vals:
                self.attendance_id.write(update_vals)
                
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
            
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Information'),
                'message': _('No action available for this record type.'),
                'sticky': False,
                'type': 'warning',
            }
        }
    
    def action_refresh_report(self):
        """Refresh the reconciliation report data"""
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
        
    def action_export_discrepancies(self):
        """Export discrepancies to Excel"""
        return {
            'type': 'ir.actions.act_url',
            'url': '/web/export/xls?model=biotime.reconciliation.report&ids=%s' % ','.join(map(str, self.ids)),
            'target': 'self',
        } 