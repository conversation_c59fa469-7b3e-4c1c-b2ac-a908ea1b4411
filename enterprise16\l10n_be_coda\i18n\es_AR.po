# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_be_coda
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-28 12:14+0000\n"
"PO-Revision-Date: 2015-09-26 22:25+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Argentina) (http://www.transifex.com/odoo/odoo-9/language/es_AR/)\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:135
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr ""

#. module: l10n_be_coda
#: model:ir.actions.act_window,name:l10n_be_coda.action_account_bank_statement_line_coda
msgid "Bank Statement Lines"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Bank Transaction"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_form
msgid "CODA Statement Line"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:216
#, python-format
msgid "Communication"
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:206
#, python-format
msgid "Counter Party Account"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Credit"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Credit Transactions."
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Debit"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Debit Transactions."
msgstr ""

#. module: l10n_be_coda
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:39
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:54
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:59
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:61
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:112
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:118
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:135
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:151
#: code:addons/l10n_be_coda/wizard/account_bank_statement_import_coda.py:155
#, python-format
msgid "Error"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Extended Filters..."
msgstr "Filtros Extendidos..."

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Group By"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_form
msgid "Notes"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Partner"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Search Bank Transactions"
msgstr ""

#. module: l10n_be_coda
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_bank_statement_line_coda_filter
msgid "Statement"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_coda.view_account_bank_statement_line_coda_tree
msgid "Statement lines"
msgstr "Líneas de extracto"

#~ msgid "<i class=\"fa fa-download mr4\"/>Import Sample Template"
#~ msgstr ""

#~ msgid "Accounting Date"
#~ msgstr ""

#~ msgid "Action Needed"
#~ msgstr ""

#~ msgid "All Lines Reconciled"
#~ msgstr ""

#~ msgid "Attachment Count"
#~ msgstr ""

#~ msgid "Balance as calculated based on Opening Balance and transaction lines"
#~ msgstr ""

#~ msgid "Bank Statement File"
#~ msgstr ""

#~ msgid "Belgium Coded Statement of Account (.CODA)"
#~ msgstr ""

#~ msgid "CODA Notes"
#~ msgstr ""

#, python-format
#~ msgid "CODA V%s statements are not supported, please contact your bank"
#~ msgstr ""

#, python-format
#~ msgid ""
#~ "CODA parsing error on information data record 3.2, seq nr %s! Please report "
#~ "this issue via your Odoo support channel."
#~ msgstr ""

#, python-format
#~ msgid ""
#~ "CODA parsing error on information data record 3.3, seq nr %s! Please report "
#~ "this issue via your Odoo support channel."
#~ msgstr ""

#, python-format
#~ msgid ""
#~ "CODA parsing error on movement data record 2.2, seq nr %s! Please report "
#~ "this issue via your Odoo support channel."
#~ msgstr ""

#, python-format
#~ msgid ""
#~ "CODA parsing error on movement data record 2.3, seq nr %s! Please report "
#~ "this issue via your Odoo support channel."
#~ msgstr ""

#~ msgid "Check if difference is zero."
#~ msgstr ""

#~ msgid "Closed On"
#~ msgstr ""

#~ msgid "Company"
#~ msgstr ""

#~ msgid "Company related to this journal"
#~ msgstr ""

#~ msgid "Computed Balance"
#~ msgstr ""

#, python-format
#~ msgid "Counter Party"
#~ msgstr ""

#, python-format
#~ msgid "Counter Party Address"
#~ msgstr ""

#~ msgid "Created by"
#~ msgstr ""

#~ msgid "Created on"
#~ msgstr ""

#~ msgid "Currency"
#~ msgstr ""

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Difference"
#~ msgstr ""

#~ msgid ""
#~ "Difference between the computed ending balance and the specified ending "
#~ "balance."
#~ msgstr ""

#~ msgid "Display Name"
#~ msgstr ""

#~ msgid "Ending Balance"
#~ msgstr ""

#~ msgid "Ending Cashbox"
#~ msgstr ""

#~ msgid "Entry lines"
#~ msgstr ""

#~ msgid "External Reference"
#~ msgstr ""

#~ msgid "Filename"
#~ msgstr ""

#~ msgid "Followers"
#~ msgstr ""

#~ msgid "Followers (Channels)"
#~ msgstr ""

#~ msgid "Followers (Partners)"
#~ msgstr ""

#, python-format
#~ msgid "Foreign bank accounts with BBAN structure are not supported "
#~ msgstr ""

#, python-format
#~ msgid "Foreign bank accounts with IBAN structure are not supported "
#~ msgstr ""

#~ msgid ""
#~ "Get you bank statements in electronic format from your bank and select them "
#~ "here."
#~ msgstr ""

#~ msgid "ID"
#~ msgstr ""

#~ msgid "If checked new messages require your attention."
#~ msgstr ""

#~ msgid "If checked, new messages require your attention."
#~ msgstr ""

#~ msgid "If checked, some messages have a delivery error."
#~ msgstr ""

#~ msgid ""
#~ "If set, the accounting entries created during the bank statement reconciliation process will be created at this date.\n"
#~ "This is useful if the accounting period in which the entries should normally be booked is already closed."
#~ msgstr ""

#~ msgid "Is Follower"
#~ msgstr ""

#~ msgid "Is zero"
#~ msgstr ""

#~ msgid "Journal"
#~ msgstr ""

#~ msgid "Last Modified on"
#~ msgstr ""

#~ msgid "Last Updated by"
#~ msgstr ""

#~ msgid "Last Updated on"
#~ msgstr ""

#~ msgid "Main Attachment"
#~ msgstr ""

#~ msgid "Message Delivery error"
#~ msgstr ""

#~ msgid "Messages"
#~ msgstr ""

#~ msgid "Move Line Count"
#~ msgstr ""

#~ msgid "New"
#~ msgstr ""

#~ msgid "Number of Actions"
#~ msgstr ""

#~ msgid "Number of error"
#~ msgstr ""

#~ msgid "Number of messages which requires an action"
#~ msgstr ""

#~ msgid "Number of messages with delivery error"
#~ msgstr ""

#~ msgid "Number of unread messages"
#~ msgstr ""

#~ msgid "Reference"
#~ msgstr ""

#~ msgid "Responsible"
#~ msgstr ""

#~ msgid "Starting Balance"
#~ msgstr ""

#~ msgid "Starting Cashbox"
#~ msgstr ""

#~ msgid "Status"
#~ msgstr ""

#~ msgid "Technical field used for usability purposes"
#~ msgstr ""

#~ msgid "Total of transaction lines."
#~ msgstr ""

#~ msgid "Transactions Subtotal"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Unread Messages"
#~ msgstr ""

#~ msgid "Unread Messages Counter"
#~ msgstr ""

#, python-format
#~ msgid "Unsupported bank account structure "
#~ msgstr ""

#~ msgid ""
#~ "Used to hold the reference of the external mean that created this statement "
#~ "(name of imported file, reference of online synchronization...)"
#~ msgstr ""

#~ msgid "Validated"
#~ msgstr ""

#~ msgid "Website Messages"
#~ msgstr ""

#~ msgid "Website communication history"
#~ msgstr ""
