<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_reconcile_model_line_form" model="ir.ui.view">
            <field name="name">account.reconcile.model.line.form</field>
            <field name="model">account.reconcile.model.line</field>
            <field name="arch" type="xml">
                <form>
                    <field name="model_id" invisible="1"/>
                    <field name="allow_payment_tolerance" invisible="1"/>
                    <field name="payment_tolerance_param" invisible="1"/>
                    <field name="rule_type" invisible="1"/>
                    <group>
                        <group>
                            <field name="account_id" options="{'no_create': True}" domain="[('company_id', '=', company_id)]"
                                   attrs="{'required': ['|', ('rule_type', '!=', 'invoice_matching'), '&amp;', '&amp;', ('rule_type', '=', 'invoice_matching'), ('allow_payment_tolerance', '=', True), ('payment_tolerance_param', '!=', 0.0)]}"/>
                            <field name="amount_type"/>
                            <field name="tax_ids"
                                   domain="[('company_id', '=', company_id)]"
                                   options="{'no_create': True}"
                                   context="{'append_type_to_tax_name': True}"
                                   widget="many2many_tags"/>
                            <field name="show_force_tax_included" invisible="1"/>
                            <field name="force_tax_included"
                                   attrs="{'invisible': [('show_force_tax_included', '=', False)]}" force_save="1"/>
                            <field name="analytic_distribution" widget="analytic_distribution"
                                   groups="analytic.group_analytic_accounting"
                                   options="{'account_field': 'account_id', 'business_domain': 'general'}"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="label"/>
                            <label for="amount_string"/>
                            <div>
                                <field name="amount_string" class="oe_inline"/>
                                <span class="o_form_label oe_inline" attrs="{'invisible':[('amount_type','!=','percentage')]}">%</span>
                            </div>
                        </group>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_account_reconcile_model_tree" model="ir.ui.view">
            <field name="name">account.reconcile.model.tree</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <tree string="Bank Reconciliation Move Presets">
                    <field name="sequence" widget="handle" />
                    <field name="name"/>
                    <field name="rule_type"/>
                    <field name="auto_reconcile"/>
                    <field name="match_journal_ids" optional="hidden" widget="many2many_tags"/>
                </tree>
            </field>
        </record>

        <record id="view_account_reconcile_model_form" model="ir.ui.view">
            <field name="name">account.reconcile.model.form</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <form string="Operation Templates">
                    <field name="active" invisible="1"/>
                    <field name="payment_tolerance_param" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button type="object" name="action_reconcile_stat"
                              class="oe_stat_button" icon="fa-book">
                                <field name="number_entries" string="Journal Entries" widget="statinfo"/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <label for="name" placeholder="Model Name"/>
                            <h1><field name="name" placeholder="e.g. Bank Fees"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="rule_type" widget="radio"/>
                            </group>
                            <group>
                                <field name="auto_reconcile" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                <field name="to_check" attrs="{'invisible': [('rule_type', '!=', 'writeoff_button')]}"/>
                                <field name="past_months_limit" attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}"/>
                                <field name="matching_order" attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}"/>
                            </group>
                        </group>
                        <notebook>
                            <page id="conditions_tab" string="Bank Transactions Conditions">
                                <group id="conditions_tab_group">
                                    <group id="left_column" class="col-6">
                                        <label for="match_journal_ids"/>
                                        <div>
                                            <field name="match_journal_ids"
                                                   widget="many2many_tags"
                                                   nolabel="1"
                                                   options="{'no_create': True}"/>
                                        </div>
                                        <field name="match_nature" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <label for="match_amount" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <div class="d-flex gap-2" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}">
                                            <field name="match_amount"/>
                                            <field name="match_amount_min"
                                                   attrs="{'invisible': [('match_amount', 'in', (False, 'lower'))], 'required': [('match_amount', '!=', False)]}"/>
                                            <span class="o_form_label"
                                                  attrs="{'invisible': [('match_amount', '!=', 'between')]}">and</span>
                                            <field name="match_amount_max"
                                                   attrs="{'invisible': [('match_amount', 'in', (False, 'greater'))], 'required': [('match_amount', '=', 'between')]}"/>
                                        </div>
                                        <label for="allow_payment_tolerance"
                                               attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}"/>
                                        <div class="d-flex gap-2" attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}">
                                            <field name="allow_payment_tolerance"/>
                                            <span attrs="{'invisible': [('allow_payment_tolerance', '=', False)]}" class="d-flex gap-2 w-100">
                                                <field name="payment_tolerance_param"/>
                                                <field name="payment_tolerance_type"/>
                                            </span>
                                        </div>
                                        <field name="match_same_currency" attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}"/>
                                    </group>
                                    <group id="right column" class="col-6">
                                        <span attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}"
                                              class="o_form_label o_td_label">Match Invoice/bill with</span>
                                        <div class="d-flex gap-3" attrs="{'invisible': [('rule_type', '!=', 'invoice_matching')]}">
                                            <label for="match_text_location_label" string="Label"/>
                                            <field name="match_text_location_label"/>
                                            <label for="match_text_location_note" string="Note"/>
                                            <field name="match_text_location_note"/>
                                            <label for="match_text_location_reference" string="Reference"/>
                                            <field name="match_text_location_reference"/>
                                        </div>
                                        <label for="match_label" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <div class="d-flex gap-3" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}">
                                            <field name="match_label"/>
                                            <field name="match_label_param"
                                                   attrs="{'invisible': [('match_label', '=', False)], 'required': [('match_label', '!=', False)]}"/>
                                        </div>
                                        <label for="match_note" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <div class="d-flex gap-3" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}">
                                            <field name="match_note"/>
                                            <field name="match_note_param"
                                                   attrs="{'invisible': [('match_note', '=', False)], 'required': [('match_note', '!=', False)]}"/>
                                        </div>
                                        <label for="match_transaction_type" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <div class="d-flex gap-3" attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}">
                                            <field name="match_transaction_type"/>
                                            <field name="match_transaction_type_param"
                                                   attrs="{'invisible': [('match_transaction_type', '=', False)], 'required': [('match_transaction_type', '!=', False)]}"/>
                                        </div>
                                        <field name="match_partner"
                                               attrs="{'invisible': [('rule_type', '=', 'writeoff_button')]}"/>
                                        <label for="match_partner_ids" class="ml16"
                                               attrs="{'invisible': ['|', ('match_partner', '=', False), ('rule_type', '=', 'writeoff_button')]}"/>
                                        <field name="match_partner_ids" nolabel="1"
                                               widget="many2many_tags"
                                               options="{'no_quick_create': True}"
                                               attrs="{'invisible': ['|', ('match_partner', '=', False), ('rule_type', '=', 'writeoff_button')]}"/>
                                        <label for="match_partner_category_ids" class="ml16"
                                               attrs="{'invisible': ['|', ('match_partner', '=', False), ('rule_type', '=', 'writeoff_button')]}"/>
                                        <field name="match_partner_category_ids" nolabel="1"
                                               widget="many2many_tags"
                                               attrs="{'invisible': ['|', ('match_partner', '=', False), ('rule_type', '=', 'writeoff_button')]}"/>
                                    </group>
                                </group>
                                <group string="Counterpart Entries" colespan="4"
                                       class="oe_inline"
                                       attrs="{'invisible': [('rule_type', '=', 'invoice_matching'), '|', ('allow_payment_tolerance', '=', False), '&amp;', ('allow_payment_tolerance', '=', True), ('payment_tolerance_param', '=', 0.0)]}">
                                    <group>
                                        <field name="show_decimal_separator" invisible="1"/>
                                        <field name="decimal_separator"
                                               attrs="{'invisible': [('show_decimal_separator', '=', False)]}"
                                               groups="base.group_no_one"/>
                                    </group>
                                    <field name="line_ids"
                                           default="{'default_model_id': self, 'default_company_id': self.company_id}"
                                           nolabel="1">
                                        <tree editable="bottom">
                                            <field name="show_force_tax_included" invisible="1"/>
                                            <field name="company_id" invisible="1"/>

                                            <field name="sequence"
                                                   widget="handle"/>
                                            <field name="account_id"/>
                                            <field name="amount_type"/>
                                            <field name="journal_id"
                                                   attrs="{'column_invisible': [('parent.rule_type', '!=', 'writeoff_button')]}"
                                                   optional="hide"/>
                                            <field name="amount_string"/>
                                            <field name="tax_ids"
                                                   widget="many2many_tags"
                                                   optional="hide"/>
                                            <field name="analytic_distribution" widget="analytic_distribution"
                                                   groups="analytic.group_analytic_accounting"
                                                   options="{'account_field': 'account_id', 'business_domain': 'general'}"/>
                                            <field name="force_tax_included"
                                                   widget="boolean_toggle"
                                                   options="{'autosave': False}"
                                                   attrs="{'invisible': [('show_force_tax_included', '=', False)]}"
                                                   optional="hide"/>
                                            <field name="label"/>
                                        </tree>
                                    </field>
                                </group>
                            </page>
                            <page id="partner_tab"
                                  string="Partner Mapping"
                                  attrs="{'invisible': [('rule_type', 'not in', ('invoice_matching', 'writeoff_suggestion'))]}">
                                <field name="partner_mapping_line_ids"
                                       nolabel="1">
                                    <tree editable="bottom">
                                        <field name="payment_ref_regex"
                                               attrs="{'required': [('narration_regex', '=', False)]}"/>
                                        <field name="narration_regex"
                                               attrs="{'required': [('payment_ref_regex', '=', False)]}"/>
                                        <field name="partner_id"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>

                    </sheet>

                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_account_reconcile_model_search" model="ir.ui.view">
            <field name="name">account.reconcile.model.search</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <search string="Bank Reconciliation Move preset">
                    <field name="name"/>
                    <filter string="Matching rules" name="matching_rules" domain="[('rule_type', '=', 'invoice_matching')]"/>
                    <filter string="Counterpart rules" name="counterpart_rules" domain="[('rule_type', '=', 'writeoff_suggestion')]"/>
                    <filter string="Counterpart buttons" name="counterpart_buttons" domain="[('rule_type', '=', 'writeoff_button')]"/>
                    <separator />
                    <filter string="Auto validate" name="auto_validate" domain="[('auto_reconcile', '=', True)]"/>
                    <separator />
                    <filter string="With Partner matching" name="with_partner_matching" domain="[('match_partner', '=', True)]"/>
                    <filter string="With tax" name="withtax" domain="[('line_ids.tax_ids', '!=', False)]"/>
                    <separator/>
                    <filter name="inactive" string="Archived" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Type" name="group_by_type" context="{'group_by': 'rule_type'}"/>
                        <filter string="Journals Availability" name="group_by_journal" context="{'group_by': 'match_journal_ids'}"/>
                        <filter string="Auto-validate" name="group_by_auto_validate" context="{'group_by': 'auto_reconcile'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_account_reconcile_model" model="ir.actions.act_window">
            <field name="name">Reconciliation Models</field>
            <field name="res_model">account.reconcile.model</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_reconcile_model_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new reconciliation model
              </p><p>
                Those can be used to quickly create a journal items when reconciling
                a bank statement or an account.
              </p>
            </field>
        </record>

    </data>
</odoo>
