<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="report_statement">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.internal_layout">
                        <div class="page">
                            <div class="border border-top-0 border-end-0 border-start-0 border-info">
                                <div class="row">
                                    <div class="col-12">
                                        <h4 class="text-end w-100">
                                            <strong>
                                                <span t-if="o.journal_id.type == 'bank'">Bank Statement</span>
                                                <span t-else="">Cash Statement</span>
                                            </strong>
                                        </h4>
                                    </div>
                                </div>
                            </div>

                            <div class="pt-2 pb-2">
                                <div class="row">
                                    <div class="col-12">
                                        <h5>
                                            <strong>
                                                <span t-field="o.journal_id"/>
                                                <t t-if="o.journal_id.bank_account_id"> -
                                                    <span t-field="o.journal_id.bank_account_id"/>
                                                </t>
                                                <t t-if="o.journal_id.code"> -                                                
                                                    <span t-field="o.journal_id.code"/>
                                                </t>
                                            </strong>
                                        </h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <h5>
                                            <strong>
                                                <t t-if="o.name">
                                                    <span t-field="o.name"/> 
                                                - </t>
                                                <span t-field="o.date"/>
                                            </strong>
                                        </h5>
                                    </div>
                                </div>
                            </div>

                            <table class="table table-lg m-0 table-borderless border border-end-0 border-start-0 border-info">
                                <tbody>
                                    <tr>
                                        <td class="pt-2 pb-2 ps-0 pe-0">
                                            <table class="table table-borderless m-0">
                                                <tr>
                                                    <td class="p-0 w-25">
                                                        <strong>Starting Balance</strong>
                                                    </td>
                                                    <td class="p-0">
                                                        <strong>
                                                            <span t-esc="o.line_ids and o.line_ids.sorted(lambda line: line.date)[0].date" t-options='{"widget": "date"}'/>
                                                        </strong>
                                                    </td>
                                                    <td class="text-end p-0">
                                                        <strong>
                                                            <span t-field="o.balance_start"/>
                                                        </strong>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0 w-25">
                                                        <strong>Ending Balance</strong>
                                                    </td>
                                                    <td class="p-0">
                                                        <strong>
                                                            <span t-esc="o.line_ids and o.line_ids.sorted(lambda line: line.date)[-1].date" t-options='{"widget": "date"}'/>
                                                        </strong>
                                                    </td>
                                                    <td class="text-end p-0">
                                                        <strong>
                                                            <span t-field="o.balance_end_real"/>
                                                        </strong>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <table class="table table-borderless m-0">
                                <tbody>
                                    <tr t-foreach="o.line_ids" t-as="line" class="pb-2">
                                        <td class="ps-0 pe-0">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td class="p-0 w-25">
                                                        <span class="d-block fw-bold" t-field="line.date"/>
                                                    </td>
                                                    <td class="p-0">
                                                        <span class="d-block fw-bold" t-if="line.partner_id" t-field="line.partner_id"/>
                                                        <span class="d-block" t-if="line.partner_bank_id" t-field="line.partner_bank_id"/>
                                                        <span class="d-block" t-if="line.payment_ref" t-field="line.payment_ref"/>
                                                        <span class="d-block" t-if=" not is_html_empty(line.narration)" t-field="line.narration"/>
                                                    </td>
                                                    <td class="text-end p-0">
                                                        <span class="d-block fw-bold" t-field="line.amount"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>