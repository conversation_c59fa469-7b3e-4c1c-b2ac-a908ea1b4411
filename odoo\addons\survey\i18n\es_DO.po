# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * survey
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-05-19 06:01+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: survey
#: model:mail.template,body_html:survey.email_template_survey
msgid ""
"\n"
"                \n"
"<p>Hello,</p>\n"
"<p>We are conducting a survey, and your response would be appreciated.</p>\n"
"<p><a style=\"margin-left: 85px; padding:5px 10px; border-radius: 3px; "
"background-color:#a24689; text-align:center; text-decoration:none; color:"
"#F7FBFD;\" href=\"__URL__\">Please, click here to start survey</a></p>\n"
"<p>Thanks for your participation!</p>\n"
"                \n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "#Questions"
msgstr "#Preguntas"

#. module: survey
#: code:addons/survey/survey.py:231
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<b>Question </b>"
msgstr "<b>Pregunta </b>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\">123..</i>"
msgstr "<i class=\"fa fa-2x\">123..</i>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Graph"
msgstr ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Gráfica"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Graph"
msgstr "<i class=\"fa fa-bar-chart-o\"/> Gráfico"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Pie Chart"
msgstr "<i class=\"fa fa-bar-chart-o\"/> Gráfico de Torta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/> respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    All Data"
msgstr ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Todos los Datos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Data"
msgstr ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Datos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> Datos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Most Common"
msgstr ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Más Común"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> Borrar Todos los Filtros"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  Filtros"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"label label-default only_left_radius filter-all\">All surveys</"
"span><span class=\"label label-primary only_right_radius filter-finished"
"\">Finished surveys</span>"
msgstr ""
"<span class=\"label label-default only_left_radius filter-all\">Todas</"
"span><span class=\"label label-primary only_right_radius filter-finished"
"\">Encuestas terminadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Average </span>"
msgstr "<span class=\"label label-default only_left_radius\">Promedio </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Maximum </span>"
msgstr "<span class=\"label label-default only_left_radius\">Máximo </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Minimum </span>"
msgstr "<span class=\"label label-default only_left_radius\">Mínimo </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Sum </span>"
msgstr "<span class=\"label label-default only_left_radius\">Suma </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"label label-primary only_left_radius filter-all\">All surveys</"
"span><span class=\"label label-default only_right_radius filter-finished"
"\">Finished surveys</span>"
msgstr ""
"<span class=\"label label-primary only_left_radius filter-all\">Todas</"
"span><span class=\"label label-default only_right_radius filter-finished"
"\">Encuestas terminadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.no_result
msgid ""
"<span>\n"
"                            <i style=\"font-size:1.8em\" class=\"fa fa-users "
"pull-right\"/>\n"
"                        </span>\n"
"                        Sorry, No one answered this survey yet"
msgstr ""
"<span>\n"
"                            <i style=\"font-size:1.8em\" class=\"fa fa-users "
"pull-right\"/>\n"
"                        </span>\n"
"                        Nadie ha respondido esta encuesta todavía"

#. module: survey
#: constraint:survey.label:0
msgid "A label must be attached to one and only one question"
msgstr "Una etiqueta debe estar adjuntada a una y sólo una pregunta"

#. module: survey
#: sql_constraint:survey.question:0
msgid "A length must be positive!"
msgstr "Una duración debe ser positiva"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_description
msgid "A long description of the purpose of the survey"
msgstr "Una descripción larga del propósito de la encuesta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_label_quizz_mark
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Una puntuación positiva indica una elección correcta; una puntuación "
"negativa o nula indica una respuesta incorrecta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "A problem has occured"
msgstr "Ha ocurrido un problema"

#. module: survey
#: model:survey.label,value:survey.frow_2_2_4
msgid "A process is defined for all enterprise flows"
msgstr "Un proceso es definido para todos los flujos empresariales"

#. module: survey
#: constraint:survey.user_input_line:0
msgid "A question cannot be unanswered and skipped"
msgstr "Una de las preguntas no puede ser no contestada y saltada"

#. module: survey
#: sql_constraint:survey.user_input:0
msgid "A token must be unique!"
msgstr "¡El token debe ser único!"

#. module: survey
#: model:survey.page,title:survey.feedback_1
msgid "About your Odoo usage"
msgstr "Sobre el uso de su Odoo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_active
#, fuzzy
msgid "Active"
msgstr "Dominio activo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"Add list of email of recipients (will not converted in partner), separated "
"by commas, semicolons or newline..."
msgstr ""
"Añadir lista de los correos de los destinatarios (no se convertirán en "
"empresas), separador por comas, puntos y comas o nuevas líneas..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Add list of existing contacts..."
msgstr "Añadir lista de contactos existentes..."

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_3
#: model:survey.label,value:survey.fcol_2_2_3
#: model:survey.label,value:survey.fcol_2_5_3
#: model:survey.label,value:survey.fcol_2_7_3
msgid "Agree"
msgstr "De acuerdo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "Permitir Comentarios"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_page_description
msgid "An introductory text to your page"
msgstr "Un texto introductorio de su página"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Analyze Answers"
msgstr "Analizar Respuestas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Answer Choices"
msgstr "Opciones de Respuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_answer_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input_type
msgid "Answer Type"
msgstr "Tipo Respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Answered"
msgstr "Contestadas"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.model.fields,field_description:survey.field_survey_question_user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input_user_input_line_ids
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Respuestas"

#. module: survey
#: model:survey.question,question:survey.feedback_1_1
msgid "Are you using Odoo on a daily basis?"
msgstr "¿Está usando Odoo diariamente?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_attachment_ids
msgid "Attachments"
msgstr "Adjuntos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
msgid "Back to Survey"
msgstr "Volver a Encuestas"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_4
msgid "CRM"
msgstr "CRM"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Cancel"
msgstr "Cancelar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Opciones"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.simple_choice
msgid "Choose..."
msgstr "Seleccione..."

#. module: survey
#: code:addons/survey/survey.py:125
#: code:addons/survey/wizard/survey_email_compose_message.py:34
#, python-format
msgid "Click here to start survey"
msgstr "Clic para comenzar la encuesta"

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:92
#, python-format
msgid "Click here to take survey"
msgstr "Clic para diligenciar la encuesta"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Click to add a survey."
msgstr "Clic para añadir una encuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Close"
msgstr "Cerrar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_closed
#: model:survey.stage,name:survey.stage_closed
msgid "Closed"
msgstr "Cerrado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_color
msgid "Color Index"
msgstr "Índice de colores"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_comments
msgid "Comment"
msgstr "Comentario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "El Campo Comentario es una Opción de Respuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comments_message
msgid "Comment Message"
msgstr "Mensaje del Comentario"

#. module: survey
#: model:survey.page,title:survey.feedback_3
msgid "Community and contributors"
msgstr "Comunidad y colaboradores"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.user_input,state:0
msgid "Completed"
msgstr "Completada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Compose Email"
msgstr "Componer correo electrónico"

#. module: survey
#: model:ir.ui.menu,name:survey.menu_surveys_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_2
msgid "Configuration wizard exists for each important setting"
msgstr "Asistente de configuración para cada configuración importante"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Restricciones"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Copy and paste the HTML code below to add this web link to any webpage."
msgstr ""
"Copie y pegue el código HTML a continuación para añadir este enlace web a "
"cualquier página."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Copy, paste and share the web link below to your audience."
msgstr "Copiar, pegar y compartir el enlace a continuación a su audiencia."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_date_create
msgid "Create Date"
msgstr "Fecha de creación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_page_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_create_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_create_date
#: model:ir.model.fields,field_description:survey.field_survey_page_create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_create_date
#: model:ir.model.fields,field_description:survey.field_survey_stage_create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey_create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_create_date
msgid "Created on"
msgstr "Creado en"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_date_create
msgid "Creation Date"
msgstr "Fecha creación"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Date"
msgstr "Fecha"

#. module: survey
#: selection:survey.question,type:0
msgid "Date and Time"
msgstr "Fecha y Hora"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_date
msgid "Date answer"
msgstr "Fecha de respuesta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input_deadline
msgid "Date by which the person can open the survey and submit answers"
msgstr ""
"Fecha a la que el usuario puede abrir la encuesta y enviar las respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message_date_deadline
msgid ""
"Deadline to which the invitation to respond for this survey is valid. If the "
"field is empty, the invitation is still valid."
msgstr ""
"Fecha límite para la que la invitación para responder esta encuesta es "
"válida. Si el campo está vacío, la invitación será válida siempre."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_date_deadline
msgid "Deadline to which the invitation to respond is valid"
msgstr "Fecha límite para la que la invitación a responder es válida"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_description
#: model:ir.model.fields,field_description:survey.field_survey_question_description
#: model:ir.model.fields,field_description:survey.field_survey_survey_description
msgid "Description"
msgstr "Descripción"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_1
msgid "Descriptions and help tooltips are clear enough"
msgstr "Descripciones y ayuda contextual son lo suficientemente claros"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Design"
msgstr "Diseño"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Design Survey"
msgstr "Diseñar Encuesta"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"Design easily your survey, send invitations to answer by email and analyse "
"answers."
msgstr ""
"Diseñe fácilmente su encuesta, envíe invitaciones a responderla vía correo y "
"analice las respuestas."

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_2
#: model:survey.label,value:survey.fcol_2_2_2
#: model:survey.label,value:survey.fcol_2_5_2
#: model:survey.label,value:survey.fcol_2_7_2
msgid "Disagree"
msgstr "No estoy de acuerdo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_display_name
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_display_name
#: model:ir.model.fields,field_description:survey.field_survey_page_display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_display_name
#: model:ir.model.fields,field_description:survey.field_survey_stage_display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey_display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_display_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display mode"
msgstr "Modo de visualización"

#. module: survey
#: model:survey.question,question:survey.feedback_3_3
msgid "Do you have a proposition to attract new contributors?"
msgstr "¿Tiene alguna propuesta para atraer nuevos contribuidores?"

#. module: survey
#: model:survey.question,question:survey.feedback_3_2
msgid "Do you have a proposition to help people to contribute?"
msgstr "¿Tiene una propuesta para ayudar a la gente a contribuir?"

#. module: survey
#: model:survey.question,question:survey.feedback_2_3
msgid "Do you have suggestions on how to improve the process view ?"
msgstr "¿Tiene sugerencias en cómo mejor la vista de procesos?"

#. module: survey
#: model:survey.stage,name:survey.stage_draft
msgid "Draft"
msgstr "Borrador"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_email
msgid "E-mail"
msgstr "Correo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Edit Pages and Questions"
msgstr "Editar Páginas y Preguntas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "Editar Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Email"
msgstr "Email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_email_template_id
msgid "Email Template"
msgstr "Plantilla de Correo"

#. module: survey
#: model:ir.model,name:survey.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr "Asistente de redacción de correo para la Encuesta"

#. module: survey
#: model:survey.page,title:survey.feedback_2
msgid "Ergonomy and ease of use"
msgstr "Ergonomía y facilidad de uso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_constr_error_msg
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_error_msg
msgid "Error message"
msgstr "Mensaje de error"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_partner_ids
msgid "Existing contacts"
msgstr "Contactos existentes"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_3
msgid "Extra modules proposed are relevant"
msgstr "Los módulos extra propuestos son relevantes"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_3
msgid "Financial Management"
msgstr "Gestión Financiera"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_fold
msgid "Folded in kanban view"
msgstr "Plegado en la vista kanban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Format"
msgstr "Formato"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Free Text"
msgstr "Texto Libre"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_free_text
msgid "Free Text answer"
msgstr "Respuesta de Texto Libre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Group By"
msgstr "Agrupar por"

#. module: survey
#: model:survey.question,question:survey.feedback_3_1
msgid "How do you contribute or plan to contribute to Odoo?"
msgstr "¿Cómo contribuye o planea contribuir a Odoo?"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_6
#, fuzzy
msgid "Human Resources"
msgstr "Recursos Humanos"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_3
msgid "I develop new features"
msgstr "Desarrollo nuevos características"

#. module: survey
#: model:survey.label,value:survey.choice_4_1_4
msgid "I do not publish my developments"
msgstr "No publico mis desarrollos"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_4
msgid "I help to translate"
msgstr "Ayudo a traducir"

#. module: survey
#: model:survey.label,value:survey.choice_4_1_3
msgid "I host them on my own website"
msgstr "Lo alojo en mi propio sitio web"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_1
msgid "I participate to discussion and forums"
msgstr "Participo de charlas y foros"

#. module: survey
#: model:survey.label,value:survey.choice_4_1_1
msgid "I use Github, like all official Odoo projects"
msgstr "Yo uso Github, al igual que todos los proyectos oficiales Odoo"

#. module: survey
#: model:survey.label,value:survey.choice_4_1_2
msgid "I use another repository system (SourceForge...)"
msgstr "Uso otro sistema de repositorios  (SourceForge...)"

#. module: survey
#: model:survey.label,value:survey.frow_2_1_3
msgid "I use the contextual help in Odoo"
msgstr "Utilizo la ayuda contextual en Odoo"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_5
msgid "I write documentations"
msgstr "Escribo documentación"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_2
msgid "I'd like to contribute but I don't know how?"
msgstr "Me gustaría contribuir pero... ¿cómo hacerlo?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_id
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_id
#: model:ir.model.fields,field_description:survey.field_survey_page_id
#: model:ir.model.fields,field_description:survey.field_survey_question_id
#: model:ir.model.fields,field_description:survey.field_survey_stage_id
#: model:ir.model.fields,field_description:survey.field_survey_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_token
msgid "Identification token"
msgstr "Token de identificación"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Si está marcado, los usuarios pueden volver a las páginas anteriores."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_stage_closed
msgid "If closed, people won't be able to answer to surveys in this column."
msgstr ""
"Si está cerrada, la gente no podrá responder encuestas en esta columna."

#. module: survey
#: code:addons/survey/survey.py:624
#: model:survey.question,comments_message:survey.feedback_1_1
#: model:survey.question,comments_message:survey.feedback_1_2
#: model:survey.question,comments_message:survey.feedback_2_1
#: model:survey.question,comments_message:survey.feedback_2_2
#: model:survey.question,comments_message:survey.feedback_2_3
#: model:survey.question,comments_message:survey.feedback_2_4
#: model:survey.question,comments_message:survey.feedback_2_5
#: model:survey.question,comments_message:survey.feedback_2_6
#: model:survey.question,comments_message:survey.feedback_2_7
#: model:survey.question,comments_message:survey.feedback_3_1
#: model:survey.question,comments_message:survey.feedback_3_2
#: model:survey.question,comments_message:survey.feedback_3_3
#: model:survey.question,comments_message:survey.feedback_4_1
#, fuzzy, python-format
msgid "If other, please specify:"
msgstr "Si otros, precisar:"

#. module: survey
#: model_terms:survey.page,description:survey.feedback_4
msgid "If you do not contribute or develop in Odoo, skip this page."
msgstr "Si no contribuyes o desarrolla en Odoo, omita esta página."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "If you wish, you can"
msgstr "Si quiere, puede"

#. module: survey
#: model:survey.stage,name:survey.stage_in_progress
msgid "In progress"
msgstr "En proceso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_email
msgid "Input must be an email"
msgstr "La entrada debe ser un correo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Invitations sent"
msgstr "Invitaciones enviadas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_designed
msgid "Is designed?"
msgstr "Está diseñado?"

#. module: survey
#: model:survey.label,value:survey.choice_2_4_2
msgid "It can be improved"
msgstr "Puede ser mejorado"

#. module: survey
#: model:survey.label,value:survey.frow_2_1_2
msgid "It helps in the beginning"
msgstr "Ello ayuda en el comienzo"

#. module: survey
#: model:survey.label,value:survey.frow_2_1_5
msgid "It is clear"
msgstr "Está claro"

#. module: survey
#: model:survey.label,value:survey.frow_2_1_4
msgid "It is complete"
msgstr "￼Está completo"

#. module: survey
#: model:survey.label,value:survey.frow_2_1_1
msgid "It is up-to-date"
msgstr "Está al día"

#. module: survey
#: model:survey.label,value:survey.frow_2_2_5
msgid "It's easy to find the process you need"
msgstr "Es fácil encontrar el proceso que necesita"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_sequence
msgid "Label Sequence order"
msgstr "Secuencia de orden de la Etiqueta"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_label_form
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Labels"
msgstr "Etiquetas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label___last_update
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message___last_update
#: model:ir.model.fields,field_description:survey.field_survey_page___last_update
#: model:ir.model.fields,field_description:survey.field_survey_question___last_update
#: model:ir.model.fields,field_description:survey.field_survey_stage___last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey___last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input___last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_page_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_write_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_write_date
#: model:ir.model.fields,field_description:survey.field_survey_page_write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_write_date
#: model:ir.model.fields,field_description:survey.field_survey_stage_write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey_write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_last_displayed_page_id
msgid "Last displayed page"
msgstr "Última página mostrada"

#. module: survey
#: selection:survey.user_input,type:0
msgid "Link"
msgstr "Enlace"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_multi_email
msgid "List of emails"
msgstr "Lista de correos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_auth_required
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "Login required"
msgstr "Sesión requerida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Respuesta Obligatoria"

#. module: survey
#: selection:survey.user_input,type:0
msgid "Manually"
msgstr "Manualmente"

#. module: survey
#: selection:survey.question,type:0
msgid "Matrix"
msgstr "Matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_matrix_subtype
msgid "Matrix Type"
msgstr "Tipo de Matriz"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Matrix:"
msgstr "Matriz:"

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max date cannot be smaller than min date!"
msgstr "¡La fecha máxima no puede ser menor que la mínima!"

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max length cannot be smaller than min length!"
msgstr "¡La longitud máxima no puede ser menor que la mínima!"

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max value cannot be smaller than min value!"
msgstr "¡El valor máximo no puede ser menor que el mínimo!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_max_date
msgid "Maximum Date"
msgstr "Fecha Máxima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_length_max
msgid "Maximum Text Length"
msgstr "Longitud Máxima del Texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_max_float_value
msgid "Maximum value"
msgstr "Valor máximo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_min_date
msgid "Minimum Date"
msgstr "Fecha Mínima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_length_min
msgid "Minimum Text Length"
msgstr "Longitud Mínima de Texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_min_float_value
msgid "Minimum value"
msgstr "Valor mínimo"

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple Lines Text Box"
msgstr "Caja de Texto Multilínea"

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: multiple answers allowed"
msgstr "Elección múltiple, con múltiples respuestas"

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: only one answer"
msgstr "Elección múltiple, sólo una respuesta"

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "Multiple choices per row"
msgstr "Elecciones múltiples por fila"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_name
msgid "Name"
msgstr "Nombre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "New"
msgstr "Nuevo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Next page"
msgstr "Página siguiente"

#. module: survey
#: model:survey.label,value:survey.choice_1_1_4
msgid "No, I just tested it"
msgstr "No, sólo lo he probado"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_selected_survey_user_input
msgid "Nobody has replied to your survey yet."
msgstr "Nadie ha contestado la encuesta todavía."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet."
msgstr "Nadie ha contestado sus encuestas todavía."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "Not open"
msgstr "No abierto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "Not ready"
msgstr "No está listo"

#. module: survey
#: selection:survey.user_input,state:0
msgid "Not started yet"
msgstr "Sin comenzar aún"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Number"
msgstr "Número"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "Número de columnas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_comp_survey
msgid "Number of completed surveys"
msgstr "Número de encuestas terminadas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_sent_survey
msgid "Number of sent surveys"
msgstr "Número de encuestas enviadas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_start_survey
msgid "Number of started surveys"
msgstr "Número de encuestas empezadas"

#. module: survey
#: selection:survey.question,type:0
msgid "Numerical Value"
msgstr "Valor Numérico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_number
msgid "Numerical answer"
msgstr "Respuesta numérica"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "Occurence"
msgstr "Ocurrencia"

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "One choice per row"
msgstr "Una elección por fila"

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:78
#, python-format
msgid "One email at least is incorrect: %s"
msgstr "Al menos un correo electrónico es incorrecto: %s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "Opciones"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_page_id
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Page"
msgstr "Página"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_title
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
msgid "Page Title"
msgstr "Título de la Página"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_sequence
msgid "Page number"
msgstr "Número de página"

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_pages
#: model:ir.actions.act_window,name:survey.action_survey_page_form
#: model:ir.model.fields,field_description:survey.field_survey_survey_page_ids
#: model:ir.ui.menu,name:survey.menu_survey_page_form1
msgid "Pages"
msgstr "Páginas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.user_input,state:0
msgid "Partially completed"
msgstr "Parcialmente completado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Partner"
msgstr "Empresa"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_partner_survey_mail
#: model:ir.actions.act_window,name:survey.action_partner_survey_mail_crm
msgid "Partner Survey Mailing"
msgstr "Envío de Encuesta a los Contactos"

#. module: survey
#: model:survey.stage,name:survey.stage_permanent
msgid "Permanent"
msgstr "Permanente"

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:199
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Introduzca al menos un destinatario válido"

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:95
#, python-format
msgid "Please select a survey"
msgstr "Seleccione una encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Previous page"
msgstr "Pagina anterior"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Print Survey"
msgstr "Imprimir Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Print These Answers"
msgstr "Imprimir Estas Respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_print_url
msgid "Print link"
msgstr "Imprimir enlace"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_5
msgid "Project Management"
msgstr "Gestión de proyectos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public_url_html
msgid "Public HTML web link"
msgstr "Enlace web HTML público"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_public_url
msgid "Public link"
msgstr "Enlace público"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_public_url_html
msgid "Public link (html version)"
msgstr "Enlace público (versión HTML)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_print_url
msgid "Public link to the empty survey"
msgstr "Enlace público a la encuesta vacía"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_result_url
msgid "Public link to the survey results"
msgstr "Enlace público a los resultados de la encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public_url
msgid "Public url"
msgstr "URL pública"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_2
msgid "Purchases Management"
msgstr "Gestión de Compras"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_question_id
#: model:ir.model.fields,field_description:survey.field_survey_label_question_id_2
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Question"
msgstr "Pregunta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Name"
msgstr "Nombre de la Pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question name"
msgstr "Nombre de la pregunta"

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_page_question
#: model:ir.actions.act_window,name:survey.act_survey_question
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_page_question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
msgid "Questions"
msgstr "Preguntas"

#. module: survey
#: model:survey.page,title:survey.feedback_4
msgid "Questions for developers"
msgstr "Preguntas para desarrolladores"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_quizz_mode
msgid "Quiz mode"
msgstr "Modo cuestionario"

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Radio Buttons"
msgstr "Botones Radio"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_result_url
msgid "Results link"
msgstr "Enlace de resultados"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_suggested_row
msgid "Row answer"
msgstr "Fila de la respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Fila1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Fila2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Fila3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Filas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_labels_ids_2
msgid "Rows of the Matrix"
msgstr "Filas de la Matriz"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_4
msgid "Running the configuration wizards is a good way to spare time"
msgstr ""
"Ejecutar los asistentes de configuración es una buena forma de ahorrar tiempo"

#. module: survey
#: model:survey.label,value:survey.choice_1_2_1
msgid "Sales Management"
msgstr "Gestión de ventas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as a new template"
msgstr "Guardar como una nueva plantilla"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as new template"
msgstr "Guardar como nueva plantilla"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_quizz_score
msgid "Score for the quiz"
msgstr "Marcador para el concurso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_quizz_mark
msgid "Score for this choice"
msgstr "Puntuación para esta opción"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_quizz_mark
msgid "Score given for this choice"
msgstr "Puntuación dada para esta opción"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
msgid "Search Label"
msgstr "Buscar etiqueta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
msgid "Search Page"
msgstr "Buscar página"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Buscar pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Search Survey"
msgstr "Buscar Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "Search User input lines"
msgstr "Buscar líneas de entrada del usuario"

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Selection Box"
msgstr "Caja de Selección"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Send"
msgstr "Enviar"

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Send by email the public web link to your audience."
msgstr "Enviar por correo electrónico el enlace web público a su audiencia."

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid ""
"Send private invitation to your audience (only one response per recipient "
"and per invitation)."
msgstr ""
"Enviar una invitación privada a su audiencia (sólo una respuesta por "
"destinatario y por invitación)."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Sent Invitation Again"
msgstr "Enviar Invitación Otra Vez"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_sequence
#: model:ir.model.fields,field_description:survey.field_survey_stage_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: survey
#: sql_constraint:survey.stage:0
msgid "Sequence number MUST be a natural"
msgstr "El número de secuencia DEBE ser un número natural"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share &amp;amp; Invite"
msgstr "Compartir &amp;amp; Invitar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Share and invite by email"
msgstr "Compartir e invitar por correo electrónico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public
msgid "Share options"
msgstr "Opciones de compartición"

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Share the public web link to your audience."
msgstr "Compartir en enlace web público a su audiencia."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comments_allowed
msgid "Show Comments Field"
msgstr "Mostrar Campo de Comentarios"

#. module: survey
#: selection:survey.question,type:0
msgid "Single Line Text Box"
msgstr "Caja de Texto de una Línea"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_skipped
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Skipped"
msgstr "Omitida"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid ""
"Something went wrong while contacting survey server. <strong class=\"text-"
"danger\">Your answers have probably not been recorded.</strong> Try "
"refreshing."
msgstr ""
"No se ha podido contactar con el servidor de la encuesta. <strong class="
"\"text-danger\">Sus respuestas probablemente no han sido registradas.</"
"strong> Intente refrescando."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Sorry, No one answered this question."
msgstr "Lo sentimos, nadie ha respondido esta pregunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_stage_id
#: model_terms:ir.ui.view,arch_db:survey.survey_stage_form
msgid "Stage"
msgstr "Fase"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "Start Survey"
msgstr "Comenzar Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Started"
msgstr "Comenzada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_state
msgid "Status"
msgstr "Estado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Subject..."
msgstr "Asunto..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Submit survey"
msgstr "Enviar Encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_suggested
msgid "Suggested answer"
msgstr "Respuesta sugerida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_value
msgid "Suggested value"
msgstr "Valor sugerido"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Suggestion"
msgstr "Sugerencia"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_page_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Survey"
msgstr "Planificación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_tree
msgid "Survey Answer Line"
msgstr "Líne de Respuesta Encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_label
#: model_terms:ir.ui.view,arch_db:survey.survey_label_tree
msgid "Survey Label"
msgstr "Etiqueta de la Encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "Survey Page"
msgstr "Página de la Encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Pregunta de la Encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_stage
msgid "Survey Stage"
msgstr "Etapa de la Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Title"
msgstr "Título de la Encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Entrada de usuario de la encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Línea de Entrada de Usuario de la Encuesta"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input_line
msgid "Survey User Input lines"
msgstr "Líneas de Entrada de Usuario de la Encuesta"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_selected_survey_user_input
msgid "Survey User input"
msgstr "Entrada de Usuario de la Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_tree
msgid "Survey User inputs"
msgstr "Entradas de Usuario de la Encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_page_id
msgid "Survey page"
msgstr "Página de la encuesta"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Encuestas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Test"
msgstr "Prueba"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test Survey"
msgstr "Probar Encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_test_entry
msgid "Test entry"
msgstr "Probar entrada"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Text"
msgstr "Texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_text
msgid "Text answer"
msgstr "Respuesta de texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_thank_you_message
msgid "Thank you message"
msgstr "Mensaje de agradecimiento"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Thank you!"
msgstr "Gracias!"

#. module: survey
#: model:survey.label,value:survey.frow_2_5_3
msgid "The 'Usability/Extended View' group helps in daily work"
msgstr "El grupo 'Usabilidad/Vista Extendida' le ayuda en su trabajo diario"

#. module: survey
#: model:survey.label,value:survey.frow_2_5_4
msgid "The 'Usability/Extended View' group hides only optional fields"
msgstr "El grupo 'Usabilidad/Vista Extendida' oculta sólo campos opcionales."

#. module: survey
#: constraint:survey.user_input_line:0
msgid "The answer must be in the right type"
msgstr "La respuesta debe estar en el tipo adecuado"

#. module: survey
#: code:addons/survey/survey.py:622
#: model:survey.question,validation_error_msg:survey.feedback_1_1
#: model:survey.question,validation_error_msg:survey.feedback_1_2
#: model:survey.question,validation_error_msg:survey.feedback_2_1
#: model:survey.question,validation_error_msg:survey.feedback_2_2
#: model:survey.question,validation_error_msg:survey.feedback_2_3
#: model:survey.question,validation_error_msg:survey.feedback_2_4
#: model:survey.question,validation_error_msg:survey.feedback_2_5
#: model:survey.question,validation_error_msg:survey.feedback_2_6
#: model:survey.question,validation_error_msg:survey.feedback_2_7
#: model:survey.question,validation_error_msg:survey.feedback_3_1
#: model:survey.question,validation_error_msg:survey.feedback_3_2
#: model:survey.question,validation_error_msg:survey.feedback_3_3
#: model:survey.question,validation_error_msg:survey.feedback_4_1
#, python-format
msgid "The answer you entered has an invalid format."
msgstr "La respuesta introducida tiene un formato no válido."

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:174
#, python-format
msgid ""
"The content of the text don't contain '__URL__'.                     __URL__ "
"is automaticaly converted into the special url of the survey."
msgstr ""
"El contenido del texto no contiene '__URL__'. __URL__ se convierte "
"automáticamente en la URL de la encuesta."

#. module: survey
#: model:survey.label,value:survey.choice_2_4_1
msgid "The current menu structure is good"
msgstr "La estructura actual del menú es buena"

#. module: survey
#: sql_constraint:survey.user_input:0
msgid "The deadline cannot be in the past"
msgstr "El plazo mínimo no puede establecerse en el pasado"

#. module: survey
#: model:survey.label,value:survey.frow_2_5_5
msgid "The groups set on menu items are relevant"
msgstr "Los grupos fijados en las opciones son relevantes"

#. module: survey
#: model:survey.label,value:survey.choice_2_6_3
msgid "The number of groups is good"
msgstr "El número de grupos es bueno"

#. module: survey
#: model:survey.label,value:survey.frow_2_5_1
msgid "The security rules defined on groups are useful"
msgstr "Las reglas de seguridad definidas en los grupos son útiles"

#. module: survey
#: model:survey.label,value:survey.choice_2_6_2
msgid "There are too few groups defined, security isn't accurate enough"
msgstr ""
"Hay muy pocos grupos definidos, la seguridad no es lo suficientemente precisa"

#. module: survey
#: model:survey.label,value:survey.choice_2_6_1
msgid "There are too many groups defined, security is too complex to set"
msgstr "Hay muchos grupos definidos, la seguridad es muy compleja de controlar"

#. module: survey
#: model:survey.label,value:survey.choice_2_4_3
msgid "There are too much menus, it's complex to understand"
msgstr "Hay muchos menús, es complejo de entender."

#. module: survey
#: model_terms:survey.page,description:survey.feedback_2
msgid ""
"These questions relate to the ergonomy and ease of use of Odoo. Try to "
"remind your firsts days on Odoo and\n"
"what have been your difficulties."
msgstr ""
"Estas preguntas se refieren a la ergonomía y facilidad de uso de Odoo. Trate "
"de recordar a sus primeros días en Odoo y \n"
"cuáles han sido sus dificultades."

#. module: survey
#: model:survey.label,value:survey.frow_2_2_2
msgid "They are clean and correct"
msgstr "Son claros y correctos"

#. module: survey
#: model:survey.label,value:survey.frow_2_2_3
msgid "They are useful on a daily usage"
msgstr "Son útiles en el uso diario"

#. module: survey
#: model:survey.label,value:survey.frow_2_2_1
msgid "They help new users to understand Odoo"
msgstr "Ayudan a los nuevos usuarios a comprender Odoo"

#. module: survey
#: code:addons/survey/survey.py:671 code:addons/survey/tests/test_survey.py:94
#, python-format
msgid "This answer must be an email address"
msgstr "Esta respuesta debe ser una dirección de correo electrónica"

#. module: survey
#: code:addons/survey/survey.py:713 code:addons/survey/tests/test_survey.py:115
#, python-format
msgid "This is not a date/time"
msgstr "Esto no es una fecha/hora"

#. module: survey
#: code:addons/survey/survey.py:690 code:addons/survey/tests/test_survey.py:105
#, python-format
msgid "This is not a number"
msgstr "Esto no es un número"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message_multi_email
msgid ""
"This list of emails of recipients will not converted in contacts. Emails "
"separated by commas, semicolons or newline."
msgstr ""
"Esta lista de correos electrónicos destinatarios no se convertirá en "
"contactos. Las direcciones deben separarse por comas, puntos y comas o "
"nuevas líneas."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_thank_you_message
msgid "This message will be displayed when survey is completed"
msgstr "Este mensaje será mostrado cuando se complete la encuesta"

#. module: survey
#: code:addons/survey/survey.py:621
#: model:survey.question,constr_error_msg:survey.feedback_1_1
#: model:survey.question,constr_error_msg:survey.feedback_1_2
#: model:survey.question,constr_error_msg:survey.feedback_2_1
#: model:survey.question,constr_error_msg:survey.feedback_2_2
#: model:survey.question,constr_error_msg:survey.feedback_2_3
#: model:survey.question,constr_error_msg:survey.feedback_2_4
#: model:survey.question,constr_error_msg:survey.feedback_2_5
#: model:survey.question,constr_error_msg:survey.feedback_2_6
#: model:survey.question,constr_error_msg:survey.feedback_2_7
#: model:survey.question,constr_error_msg:survey.feedback_3_1
#: model:survey.question,constr_error_msg:survey.feedback_3_2
#: model:survey.question,constr_error_msg:survey.feedback_3_3
#: model:survey.question,constr_error_msg:survey.feedback_4_1
#, python-format
msgid "This question requires an answer."
msgstr "Esta pregunta requiere una respuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "This survey has no pages by now!"
msgstr "Esta encuesta no tiene páginas por el momento!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "This survey is not open. Thank you for your interest!"
msgstr "Esta encuesta no está abierta. ¡Gracias por su interés!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Esta encuesta está abierta sólo para gente registrada. Por favor"

#. module: survey
#: model:survey.survey,description:survey.feedback_form
msgid "This survey should take less than five minutes."
msgstr "Esta encuesta debería tomar menos de cinco minutos."

#. module: survey
#: model:survey.label,value:survey.frow_2_5_2
msgid ""
"Those security rules are standard and can be used out-of-the-box in most "
"cases"
msgstr ""
"Las reglas de seguridad son estándar y puede ser usadas tal cual en la "
"mayoría de casos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_title
msgid "Title"
msgstr "Título"

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_4
#: model:survey.label,value:survey.fcol_2_2_4
#: model:survey.label,value:survey.fcol_2_5_4
#: model:survey.label,value:survey.fcol_2_7_4
msgid "Totally agree"
msgstr "Totalmente de acuerdo"

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_1
#: model:survey.label,value:survey.fcol_2_2_1
#: model:survey.label,value:survey.fcol_2_5_1
#: model:survey.label,value:survey.fcol_2_7_1
msgid "Totally disagree"
msgstr "Totalmente en desacuerdo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tipo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_type
msgid "Type of Question"
msgstr "Tipo de Pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Type of answers"
msgstr "Tipo de respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_labels_ids
msgid "Types of answers"
msgstr "Tipos de respuestas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Use template"
msgstr "Usar plantilla"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_description
msgid ""
"Use this field to add             additional explanations about your question"
msgstr "Use este campo para añadir explicaciones adicionales sobre su pregunta"

#. module: survey
#: model:survey.survey,title:survey.feedback_form
msgid "User Feedback Form"
msgstr "Formulario de Comentarios del Usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "User Input"
msgstr "Entrada del Usuario"

#. module: survey
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "User Input Lines"
msgstr "Líneas de Entrada del Usuario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
#: model_terms:ir.ui.view,arch_db:survey.result_number
#: model_terms:ir.ui.view,arch_db:survey.result_text
msgid "User Responses"
msgstr "Respuestas del Usuario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "User can come back in the previous page"
msgstr "El usuario puede regresar a la página anterior"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_form
msgid "User input line details"
msgstr "Detalles de la línea de entrada del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_user_input_ids
msgid "User responses"
msgstr "Respuestas del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_users_can_go_back
msgid "Users can go back"
msgstr "Los usuarios pueden volver atrás"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_auth_required
msgid ""
"Users with a public link will be requested to login before taking part to "
"the survey"
msgstr ""
"Los usuarios que accedan con un enlace público tendrán que iniciar sesión "
"antes de hacer parte de la encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_required
msgid "Validate entry"
msgstr "Validar entrada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "View Results"
msgstr "Ver Resultados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "View results"
msgstr "Ver resultados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Vote"
msgstr "Votar"

#. module: survey
#: model:survey.question,question:survey.feedback_2_7
msgid "What do you think about configuration wizards?"
msgstr "Qué piensa acerca de los asistentes de configuración?"

#. module: survey
#: model:survey.question,question:survey.feedback_2_1
msgid "What do you think about the documentation available on doc.odoo.com?"
msgstr "¿Qué piensa de la documentación disponible en doc.odoo.com?"

#. module: survey
#: model:survey.question,question:survey.feedback_2_5
msgid "What do you think about the groups of users?"
msgstr "Qué piensa de los grupos de usuarios?"

#. module: survey
#: model:survey.question,question:survey.feedback_2_2
msgid ""
"What do you think about the process views of Odoo, available in the web "
"client ?"
msgstr ""
"Qué piensa acerca de las vistas de proceso de Odoo, disponibles en el "
"cliente web?"

#. module: survey
#: model:survey.question,question:survey.feedback_2_4
#: model:survey.question,question:survey.feedback_2_6
msgid "What do you think about the structure of the menus?"
msgstr "Qué piensa de la estructura de los menús?"

#. module: survey
#: model:survey.question,question:survey.feedback_4_1
msgid "Where do you develop your new features?"
msgstr "¿Dónde desarrolla nuevas características?"

#. module: survey
#: model:survey.question,question:survey.feedback_1_2
msgid "Which modules are you using/testing?"
msgstr "¿Qué módulos está usando/probando?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.datetime
msgid "YYYY-MM-DD hh:mm:ss"
msgstr "YYYY-MM-DD hh:mm:ss"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                            <i class=\"fa fa-calendar fa-2x"
"\"/>"
msgstr ""
"YYYY-MM-DD hh:mm:ss\n"
"<i class=\"fa fa-calendar fa-2x\"/>"

#. module: survey
#: model:survey.label,value:survey.choice_1_1_1
msgid "Yes, I use a version < 7.0"
msgstr "Sí, uso una versión < 7.0"

#. module: survey
#: model:survey.label,value:survey.choice_1_1_2
msgid "Yes, I use the 7.0 version, installed locally"
msgstr "Sí, uso la versión 7.0, instalado localmente"

#. module: survey
#: model:survey.label,value:survey.choice_1_1_3
msgid "Yes, I use the online version of Odoo"
msgstr "Sí, uso la versión en línea de Odoo"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"Usted puede crear encuestas para diferentes propósitos: opinión del cliente, "
"retroalimentación de los servicios, entrevistas de trabajo, evaluaciones "
"periódicas de los empleados, campañas de marketing, etc."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"You can share your survey web public link and/or send private invitations to "
"your audience. People can answer once per invitation, and whenever they want "
"with the public web link (in this case, the \"Public in website\" setting "
"must be enabled)."
msgstr ""
"Puede compartir en enlace público web de la encuesta y/o enviar invitaciones "
"privadas a su audiencia. La gente puede contestar una vez por invitación, y "
"siempre que quieran con el enlace público (en este caso, la opción "
"\"Publicar en el sitio web\" debe esta activa)."

#. module: survey
#: code:addons/survey/survey.py:424
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "No puede enviar invitaciones para una encuesta que no tiene preguntas."

#. module: survey
#: code:addons/survey/survey.py:429
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "No puede enviar invitaciones para una encuesta cerrada."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "You scored"
msgstr "Ha obtenido"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "res"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "log in"
msgstr "iniciar sesión"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "of"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "points."
msgstr "puntos."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "review your answers"
msgstr "revisar sus respuestas"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/tour_test_survey.js:10
#, python-format
msgid "try to create and fill survey"
msgstr "trata de crear y diligenciar una encuesta."

#~ msgid ""
#~ "\n"
#~ "                \n"
#~ "                <div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, "
#~ "Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); \">\n"
#~ "                    <p>Hello,</p>\n"
#~ "                    <p>We are conducting a survey, and your response "
#~ "would be appreciated.</p>\n"
#~ "                    <p><a href=\"__URL__\">Please, click here to start "
#~ "survey</a></p>\n"
#~ "                    <p>Thanks for your participation!</p>\n"
#~ "                </div>\n"
#~ "                \n"
#~ "            "
#~ msgstr ""
#~ "\n"
#~ "                \n"
#~ "                <div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, "
#~ "Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); \">\n"
#~ "                    <p>Hola,</p>\n"
#~ "                    <p>Estamos realizando una encuesta, y su respuesta "
#~ "sería muy apreciada.</p>\n"
#~ "                    <p><a href=\"__URL__\">Por favor, pulse aquí para "
#~ "iniciar la encuesta</a></p>\n"
#~ "                    <p>¡Gracias por su participación!</p>\n"
#~ "                </div>\n"
#~ "                \n"
#~ "            "

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid ""
#~ "Answers do not go in the original document discussion thread. This has an "
#~ "impact on the generated message-id."
#~ msgstr ""
#~ "Las respuestas no van en el hilo de discusión del documento original. "
#~ "Esto tiene un impacto en el id de mensaje generado."

#~ msgid "Author"
#~ msgstr "Autor"

#~ msgid ""
#~ "Author of the message. If not set, email_from may hold an email address "
#~ "that did not match any partner."
#~ msgstr ""
#~ "Autor del Mensaje. Si no se especifica, email_from puede guardar un "
#~ "correo que no coincidió con ningún contacto."

#~ msgid "Author's avatar"
#~ msgstr "Avatar del autor"

#~ msgid "Automatically sanitized HTML contents"
#~ msgstr "Contenidos HTML automáticamente saneados"

#~ msgid "Channels"
#~ msgstr "Canales"

#~ msgid "Child Messages"
#~ msgstr "Mensajes Hijos"

#~ msgid "Composition mode"
#~ msgstr "Modo de composición"

#~ msgid "Contents"
#~ msgstr "Contenidos"

#~ msgid "Current user has a starred notification linked to this message"
#~ msgstr ""
#~ "El usuario actual tiene una notificación destacada enlazada a este mensaje"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid ""
#~ "Email address of the sender. This field is set when no matching partner "
#~ "is found and replaces the author_id field in the chatter."
#~ msgstr ""
#~ "Dirección de correo electrónico del remitente. Este campo se establece "
#~ "cuando ningún contacto coincidente se encuentra y reemplaza el campo "
#~ "Identificación del autor en la charla."

#~ msgid "Favorited By"
#~ msgstr "Favorito Por"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Contactos)"

#~ msgid "From"
#~ msgstr "Desde"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, los nuevos mensajes requerirán su atención."

#~ msgid "Initial thread message."
#~ msgstr "Mensaje inicial del hilo."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Log an Internal Note"
#~ msgstr "Registrar una Nota Interna"

#~ msgid "Message Record Name"
#~ msgstr "Nombre de Registro del Mensaje"

#~ msgid ""
#~ "Message type: email for email message, notification for system message, "
#~ "comment for other messages such as user replies"
#~ msgstr ""
#~ "Tipo de mensaje: email para mensajes de correo, notification para "
#~ "mensajes de sistema, comment para otros tipos de mensaje como respuestas "
#~ "de usuarios."

#~ msgid "Message unique identifier"
#~ msgstr "Identificador único del mensaje"

#~ msgid "Message-Id"
#~ msgstr "Id-Mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Name get of the related document."
#~ msgstr "Nombre obtenido del documento relacionado."

#~ msgid "Need Action"
#~ msgstr "Necesita Acción"

#~ msgid "No threading for answers"
#~ msgstr "Sin hilo para las respuestas"

#~ msgid "Notify followers"
#~ msgstr "Notificar a los seguidores"

#~ msgid "Notify followers of the document (mass post only)"
#~ msgstr "Notificar a los seguidores del documento (sólo envíos masivos)"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Open Survey Menu"
#~ msgstr "Abrir el Menú de Encuestas"

#~ msgid "Outgoing mail server"
#~ msgstr "Servidor de correo saliente"

#~ msgid "Parent Message"
#~ msgstr "Mensaje Padre"

#~ msgid "Related Document ID"
#~ msgstr "ID Doc Relacionado"

#~ msgid "Related Document Model"
#~ msgstr "Modelo Doc Relacionado"

#~ msgid ""
#~ "Reply email address. Setting the reply_to bypasses the automatic thread "
#~ "creation."
#~ msgstr ""
#~ "Dirección de respuesta de los correos. Estableciendo este campo, se "
#~ "saltará la creación automática de hilos."

#~ msgid "Reply-To"
#~ msgstr "Responder-A"

#~ msgid ""
#~ "Small-sized image of this contact. It is automatically resized as a "
#~ "64x64px image, with aspect ratio preserved. Use this field anywhere a "
#~ "small image is required."
#~ msgstr ""
#~ "Imagen de tamaño pequeño de este contacto. Se redimensiona "
#~ "automáticamente a 64x64 px, con el ratio de aspecto preservado. Use este "
#~ "campo donde se requiera una imagen pequeña."

#~ msgid "Starred"
#~ msgstr "Destacados"

#~ msgid "Subject"
#~ msgstr "Asunto"

#~ msgid "Subtype"
#~ msgstr "Subtipo"

#~ msgid "System notification"
#~ msgstr "Notificación del sistema"

#~ msgid ""
#~ "Tracked values are stored in a separate model. This field allow to "
#~ "reconstructthe tracking and to generate statistics on the model."
#~ msgstr ""
#~ "Los campos rastreados son almacenados en un modelo separado. Este campo "
#~ "permite reconstruir el rastreo y generar estadísticas del modelo."

#~ msgid "Tracking values"
#~ msgstr "Rastreando campos"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Use active domain"
#~ msgstr "Usar dominio activo"

#~ msgid "Whether the message is an internal note (comment mode only)"
#~ msgstr "Si el mensaje es una nota interna (modo de sólo comentarios)"

#~ msgid "on"
#~ msgstr "en"
