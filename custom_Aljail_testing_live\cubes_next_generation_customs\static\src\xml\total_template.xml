<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-inherit="account.TaxTotalsField" t-inherit-mode="extension" owl="1">
        <xpath expr="//tbody/tr" position="before">
            <tr>
                <td class="o_td_label">
                    <label class="o_form_label o_tax_total_label">Original Amount</label>
                </td>
                <td>
                    <span
                                name="original_total"
                                t-att-class="Object.keys(totals.groups_by_subtotal).length > 0 ? 'oe_subtotal_footer_separator' : ''"
                                t-out="totals.formatted_original_amount_total"
                                style="white-space: nowrap; font-weight: bold; font-size: 1.3em;"
                        />
                </td>
            </tr>
            <tr>
                <td class="o_td_label">
                    <label class="o_form_label o_tax_total_label">Discount</label>
                </td>
                <td>
                    <span
                                name="total_discount"
                                t-att-class="Object.keys(totals.groups_by_subtotal).length > 0 ? 'oe_subtotal_footer_separator' : ''"
                                t-out="totals.formatted_discount_amount_total"
                                style="white-space: nowrap; font-weight: bold; font-size: 1.3em;"
                        />
                </td>
            </tr>
        </xpath>
    </div>

</templates>
