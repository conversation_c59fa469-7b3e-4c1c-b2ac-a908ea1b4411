from odoo import models, fields, api, exceptions, _
from datetime import datetime
from odoo.exceptions import ValidationError


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    '''override create method'''

    @api.model_create_multi
    def create(self, vals_list):
        '''Code before create
           Can use the `vals` dictionary'''
        new_record = super(AccountPayment, self).create(vals_list)
        for rec in new_record:
            if not rec.partner_id:
                raise ValidationError(_("Please insert Customer"))
            if rec.amount == 0:
                raise ValidationError(_("Please Check Amount"))

        return new_record
