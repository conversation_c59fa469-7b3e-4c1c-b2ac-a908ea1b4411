<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

       <record id="res_parking_payment_view" model="ir.ui.view">
           <field name="name">Reservation Parking Form</field>
           <field name="model">reservation.parking.payment</field>
           <field name="arch" type="xml">
               <form string="_form">
                   <header>
                       <field name="state" widget="statusbar"/>
                       <button type="object" name="action_draft" string="Draft" class="oe_highlight" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                       <button type="object" name="action_posted" string="Post" class="oe_highlight" attrs="{'invisible': [('state', '=', 'posted')]}"/>
                       <button type="object" name="action_cancel" string="Cancel" class="oe_highlight" attrs="{'invisible': [('state', '=', 'cancel')]}"/>
                   </header>
                   <sheet>
                       <div class="oe_title">
                           <h1>
                               <field name="name" readonly="1"/>
                           </h1>
                       </div>
                       <group>
                           <group>
                               <field name="payment_type"/>
                               <field name="partner_id"/>
                               <field name="amount"/>
                               <field name="guarantee_amount"/>
                           </group>
                           <group>
                               <field name="journal_id"/>
                               <field name="guarantee_journal_id"/>
                               <field name="reservation_id" readonly="1"/>
                           </group>
                       </group>
                   </sheet>
               </form>
           </field>
       </record>

        <record id="parking_payment_tree_view" model="ir.ui.view">
            <field name="name">Parking Payment Tree</field>
            <field name="model">reservation.parking.payment</field>
            <field name="arch" type="xml">
                <tree string="_tree">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="payment_type"/>
                    <field name="amount"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="parking_payment_action" model="ir.actions.act_window">
            <field name="name">Payments</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">reservation.parking.payment</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="parking_payment_menu" name="Reservation Payments" parent="hotel_management.main_menu_hotel_reservation_tree_all" action="parking_payment_action"/>

    </data>
</odoo>