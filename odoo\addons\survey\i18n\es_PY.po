# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * survey
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-19 08:26+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Paraguay) (http://www.transifex.com/odoo/odoo-9/"
"language/es_PY/)\n"
"Language: es_PY\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: survey
#: model:mail.template,body_html:survey.email_template_survey
msgid ""
"\n"
"                \n"
"<p>Hello,</p>\n"
"<p>We are conducting a survey, and your response would be appreciated.</p>\n"
"<p><a style=\"margin-left: 85px; padding:5px 10px; border-radius: 3px; "
"background-color:#a24689; text-align:center; text-decoration:none; color:"
"#F7FBFD;\" href=\"__URL__\">Please, click here to start survey</a></p>\n"
"<p>Thanks for your participation!</p>\n"
"                \n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "#Questions"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:231
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "&amp;times;"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<b>Question </b>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\">123..</i>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Pie Chart"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    All Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Most Common"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"label label-default only_left_radius filter-all\">All surveys</"
"span><span class=\"label label-primary only_right_radius filter-finished"
"\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Average </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Maximum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Minimum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"label label-default only_left_radius\">Sum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"label label-primary only_left_radius filter-all\">All surveys</"
"span><span class=\"label label-default only_right_radius filter-finished"
"\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.no_result
msgid ""
"<span>\n"
"                            <i style=\"font-size:1.8em\" class=\"fa fa-users "
"pull-right\"/>\n"
"                        </span>\n"
"                        Sorry, No one answered this survey yet"
msgstr ""

#. module: survey
#: constraint:survey.label:0
msgid "A label must be attached to one and only one question"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "A length must be positive!"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_description
msgid "A long description of the purpose of the survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_label_quizz_mark
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "A problem has occured"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_4
msgid "A process is defined for all enterprise flows"
msgstr ""

#. module: survey
#: constraint:survey.user_input_line:0
msgid "A question cannot be unanswered and skipped"
msgstr ""

#. module: survey
#: sql_constraint:survey.user_input:0
msgid "A token must be unique!"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_1
msgid "About your Odoo usage"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_active
msgid "Active"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"Add list of email of recipients (will not converted in partner), separated "
"by commas, semicolons or newline..."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Add list of existing contacts..."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_3
#: model:survey.label,value:survey.fcol_2_2_3
#: model:survey.label,value:survey.fcol_2_5_3
#: model:survey.label,value:survey.fcol_2_7_3
msgid "Agree"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_page_description
msgid "An introductory text to your page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Analyze Answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Answer Choices"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_answer_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input_type
msgid "Answer Type"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Answered"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.model.fields,field_description:survey.field_survey_question_user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input_user_input_line_ids
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Respuestas"

#. module: survey
#: model:survey.question,question:survey.feedback_1_1
msgid "Are you using Odoo on a daily basis?"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_attachment_ids
msgid "Attachments"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
msgid "Back to Survey"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_4
msgid "CRM"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Cancel"
msgstr "Cancelar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.simple_choice
msgid "Choose..."
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:125
#: code:addons/survey/wizard/survey_email_compose_message.py:34
#, python-format
msgid "Click here to start survey"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:92
#, python-format
msgid "Click here to take survey"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Click to add a survey."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Close"
msgstr "Cerrar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_closed
#: model:survey.stage,name:survey.stage_closed
msgid "Closed"
msgstr "Cierre"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_color
msgid "Color Index"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_comments
msgid "Comment"
msgstr "Comentario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comments_message
msgid "Comment Message"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_3
msgid "Community and contributors"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.user_input,state:0
msgid "Completed"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Compose Email"
msgstr ""

#. module: survey
#: model:ir.ui.menu,name:survey.menu_surveys_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_2
msgid "Configuration wizard exists for each important setting"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Copy and paste the HTML code below to add this web link to any webpage."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Copy, paste and share the web link below to your audience."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_date_create
msgid "Create Date"
msgstr "Fecha de creación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_page_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_create_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_create_date
#: model:ir.model.fields,field_description:survey.field_survey_page_create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_create_date
#: model:ir.model.fields,field_description:survey.field_survey_stage_create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey_create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_create_date
msgid "Created on"
msgstr "Creado en"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_date_create
msgid "Creation Date"
msgstr "Fecha creación"

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Date"
msgstr "Fecha"

#. module: survey
#: selection:survey.question,type:0
msgid "Date and Time"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_date
msgid "Date answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input_deadline
msgid "Date by which the person can open the survey and submit answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message_date_deadline
msgid ""
"Deadline to which the invitation to respond for this survey is valid. If the "
"field is empty, the invitation is still valid."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_date_deadline
msgid "Deadline to which the invitation to respond is valid"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_description
#: model:ir.model.fields,field_description:survey.field_survey_question_description
#: model:ir.model.fields,field_description:survey.field_survey_survey_description
msgid "Description"
msgstr "Descripción"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_1
msgid "Descriptions and help tooltips are clear enough"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Design"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Design Survey"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"Design easily your survey, send invitations to answer by email and analyse "
"answers."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_2
#: model:survey.label,value:survey.fcol_2_2_2
#: model:survey.label,value:survey.fcol_2_5_2
#: model:survey.label,value:survey.fcol_2_7_2
msgid "Disagree"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_display_name
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_display_name
#: model:ir.model.fields,field_description:survey.field_survey_page_display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_display_name
#: model:ir.model.fields,field_description:survey.field_survey_stage_display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey_display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_display_name
msgid "Display Name"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_display_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display mode"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_3_3
msgid "Do you have a proposition to attract new contributors?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_3_2
msgid "Do you have a proposition to help people to contribute?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_3
msgid "Do you have suggestions on how to improve the process view ?"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_draft
msgid "Draft"
msgstr "Borrador"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_email
msgid "E-mail"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Edit Pages and Questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Email"
msgstr "Correo electrónico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_email_template_id
msgid "Email Template"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_2
msgid "Ergonomy and ease of use"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_constr_error_msg
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_error_msg
msgid "Error message"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_partner_ids
msgid "Existing contacts"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_7_3
msgid "Extra modules proposed are relevant"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_3
msgid "Financial Management"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_fold
msgid "Folded in kanban view"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Format"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Free Text"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_free_text
msgid "Free Text answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Group By"
msgstr "Agrupado por"

#. module: survey
#: model:survey.question,question:survey.feedback_3_1
msgid "How do you contribute or plan to contribute to Odoo?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_6
msgid "Human Resources"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_3
msgid "I develop new features"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_4
msgid "I do not publish my developments"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_4
msgid "I help to translate"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_3
msgid "I host them on my own website"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_1
msgid "I participate to discussion and forums"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_1
msgid "I use Github, like all official Odoo projects"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_2
msgid "I use another repository system (SourceForge...)"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_3
msgid "I use the contextual help in Odoo"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_5
msgid "I write documentations"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_2
msgid "I'd like to contribute but I don't know how?"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_id
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_id
#: model:ir.model.fields,field_description:survey.field_survey_page_id
#: model:ir.model.fields,field_description:survey.field_survey_question_id
#: model:ir.model.fields,field_description:survey.field_survey_stage_id
#: model:ir.model.fields,field_description:survey.field_survey_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_token
msgid "Identification token"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_stage_closed
msgid "If closed, people won't be able to answer to surveys in this column."
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:624
#: model:survey.question,comments_message:survey.feedback_1_1
#: model:survey.question,comments_message:survey.feedback_1_2
#: model:survey.question,comments_message:survey.feedback_2_1
#: model:survey.question,comments_message:survey.feedback_2_2
#: model:survey.question,comments_message:survey.feedback_2_3
#: model:survey.question,comments_message:survey.feedback_2_4
#: model:survey.question,comments_message:survey.feedback_2_5
#: model:survey.question,comments_message:survey.feedback_2_6
#: model:survey.question,comments_message:survey.feedback_2_7
#: model:survey.question,comments_message:survey.feedback_3_1
#: model:survey.question,comments_message:survey.feedback_3_2
#: model:survey.question,comments_message:survey.feedback_3_3
#: model:survey.question,comments_message:survey.feedback_4_1
#, python-format
msgid "If other, please specify:"
msgstr ""

#. module: survey
#: model_terms:survey.page,description:survey.feedback_4
msgid "If you do not contribute or develop in Odoo, skip this page."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "If you wish, you can"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_in_progress
msgid "In progress"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_email
msgid "Input must be an email"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Invitations sent"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_designed
msgid "Is designed?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_2
msgid "It can be improved"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_2
msgid "It helps in the beginning"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_5
msgid "It is clear"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_4
msgid "It is complete"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_1
msgid "It is up-to-date"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_5
msgid "It's easy to find the process you need"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_sequence
msgid "Label Sequence order"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_label_form
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Labels"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label___last_update
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message___last_update
#: model:ir.model.fields,field_description:survey.field_survey_page___last_update
#: model:ir.model.fields,field_description:survey.field_survey_question___last_update
#: model:ir.model.fields,field_description:survey.field_survey_stage___last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey___last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input___last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line___last_update
msgid "Last Modified on"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_page_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_write_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_write_date
#: model:ir.model.fields,field_description:survey.field_survey_page_write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_write_date
#: model:ir.model.fields,field_description:survey.field_survey_stage_write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey_write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_last_displayed_page_id
msgid "Last displayed page"
msgstr ""

#. module: survey
#: selection:survey.user_input,type:0
msgid "Link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_multi_email
msgid "List of emails"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_auth_required
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "Login required"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr ""

#. module: survey
#: selection:survey.user_input,type:0
msgid "Manually"
msgstr "Manualmente"

#. module: survey
#: selection:survey.question,type:0
msgid "Matrix"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_matrix_subtype
msgid "Matrix Type"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Matrix:"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max date cannot be smaller than min date!"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max length cannot be smaller than min length!"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max value cannot be smaller than min value!"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_max_date
msgid "Maximum Date"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_length_max
msgid "Maximum Text Length"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_max_float_value
msgid "Maximum value"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_min_date
msgid "Minimum Date"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_length_min
msgid "Minimum Text Length"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_min_float_value
msgid "Minimum value"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple Lines Text Box"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: multiple answers allowed"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: only one answer"
msgstr ""

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "Multiple choices per row"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage_name
msgid "Name"
msgstr "Nombre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "New"
msgstr "Nuevo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Next page"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_4
msgid "No, I just tested it"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_selected_survey_user_input
msgid "Nobody has replied to your survey yet."
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "Not open"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "Not ready"
msgstr ""

#. module: survey
#: selection:survey.user_input,state:0
msgid "Not started yet"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Number"
msgstr "Número"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_comp_survey
msgid "Number of completed surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_sent_survey
msgid "Number of sent surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_tot_start_survey
msgid "Number of started surveys"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Numerical Value"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_number
msgid "Numerical answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "Occurence"
msgstr ""

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "One choice per row"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:78
#, python-format
msgid "One email at least is incorrect: %s"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_page_id
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Page"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_title
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
msgid "Page Title"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page_sequence
msgid "Page number"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_pages
#: model:ir.actions.act_window,name:survey.action_survey_page_form
#: model:ir.model.fields,field_description:survey.field_survey_survey_page_ids
#: model:ir.ui.menu,name:survey.menu_survey_page_form1
msgid "Pages"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.user_input,state:0
msgid "Partially completed"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Partner"
msgstr "Empresa"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_partner_survey_mail
#: model:ir.actions.act_window,name:survey.action_partner_survey_mail_crm
msgid "Partner Survey Mailing"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_permanent
msgid "Permanent"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:199
#, python-format
msgid "Please enter at least one valid recipient."
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:95
#, python-format
msgid "Please select a survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Previous page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Print Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Print These Answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_print_url
msgid "Print link"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_5
msgid "Project Management"
msgstr "Gestión de proyectos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public_url_html
msgid "Public HTML web link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_public_url
msgid "Public link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_public_url_html
msgid "Public link (html version)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_print_url
msgid "Public link to the empty survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_result_url
msgid "Public link to the survey results"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public_url
msgid "Public url"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_2
msgid "Purchases Management"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_question_id
#: model:ir.model.fields,field_description:survey.field_survey_label_question_id_2
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Question"
msgstr "Pregunta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Name"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question name"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_page_question
#: model:ir.actions.act_window,name:survey.act_survey_question
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_page_question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
msgid "Questions"
msgstr "Preguntas"

#. module: survey
#: model:survey.page,title:survey.feedback_4
msgid "Questions for developers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_quizz_mode
msgid "Quiz mode"
msgstr ""

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Radio Buttons"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_result_url
msgid "Results link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_suggested_row
msgid "Row answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_labels_ids_2
msgid "Rows of the Matrix"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_7_4
msgid "Running the configuration wizards is a good way to spare time"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_1
msgid "Sales Management"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as a new template"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as new template"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_quizz_score
msgid "Score for the quiz"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_quizz_mark
msgid "Score for this choice"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_quizz_mark
msgid "Score given for this choice"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
msgid "Search Label"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
msgid "Search Page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Search Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "Search User input lines"
msgstr ""

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Selection Box"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Send"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Send by email the public web link to your audience."
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid ""
"Send private invitation to your audience (only one response per recipient "
"and per invitation)."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Sent Invitation Again"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_sequence
#: model:ir.model.fields,field_description:survey.field_survey_stage_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: survey
#: sql_constraint:survey.stage:0
msgid "Sequence number MUST be a natural"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share &amp;amp; Invite"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Share and invite by email"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_public
msgid "Share options"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Share the public web link to your audience."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_comments_allowed
msgid "Show Comments Field"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Single Line Text Box"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_skipped
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Skipped"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid ""
"Something went wrong while contacting survey server. <strong class=\"text-"
"danger\">Your answers have probably not been recorded.</strong> Try "
"refreshing."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Sorry, No one answered this question."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_stage_id
#: model_terms:ir.ui.view,arch_db:survey.survey_stage_form
msgid "Stage"
msgstr "Etapa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "Start Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Started"
msgstr "Iniciado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_state
msgid "Status"
msgstr "Estado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Subject..."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Submit survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_suggested
msgid "Suggested answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label_value
msgid "Suggested value"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Suggestion"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_page_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_tree
msgid "Survey Answer Line"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_label
#: model_terms:ir.ui.view,arch_db:survey.survey_label_tree
msgid "Survey Label"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "Survey Page"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_stage
msgid "Survey Stage"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Title"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input_line
msgid "Survey User Input lines"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_selected_survey_user_input
msgid "Survey User input"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_tree
msgid "Survey User inputs"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_page_id
msgid "Survey page"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Test"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test Survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_test_entry
msgid "Test entry"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Text"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_value_text
msgid "Text answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_thank_you_message
msgid "Thank you message"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Thank you!"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_3
msgid "The 'Usability/Extended View' group helps in daily work"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_4
msgid "The 'Usability/Extended View' group hides only optional fields"
msgstr ""

#. module: survey
#: constraint:survey.user_input_line:0
msgid "The answer must be in the right type"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:622
#: model:survey.question,validation_error_msg:survey.feedback_1_1
#: model:survey.question,validation_error_msg:survey.feedback_1_2
#: model:survey.question,validation_error_msg:survey.feedback_2_1
#: model:survey.question,validation_error_msg:survey.feedback_2_2
#: model:survey.question,validation_error_msg:survey.feedback_2_3
#: model:survey.question,validation_error_msg:survey.feedback_2_4
#: model:survey.question,validation_error_msg:survey.feedback_2_5
#: model:survey.question,validation_error_msg:survey.feedback_2_6
#: model:survey.question,validation_error_msg:survey.feedback_2_7
#: model:survey.question,validation_error_msg:survey.feedback_3_1
#: model:survey.question,validation_error_msg:survey.feedback_3_2
#: model:survey.question,validation_error_msg:survey.feedback_3_3
#: model:survey.question,validation_error_msg:survey.feedback_4_1
#, python-format
msgid "The answer you entered has an invalid format."
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:174
#, python-format
msgid ""
"The content of the text don't contain '__URL__'.                     __URL__ "
"is automaticaly converted into the special url of the survey."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_1
msgid "The current menu structure is good"
msgstr ""

#. module: survey
#: sql_constraint:survey.user_input:0
msgid "The deadline cannot be in the past"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_5
msgid "The groups set on menu items are relevant"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_3
msgid "The number of groups is good"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_1
msgid "The security rules defined on groups are useful"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_2
msgid "There are too few groups defined, security isn't accurate enough"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_1
msgid "There are too many groups defined, security is too complex to set"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_3
msgid "There are too much menus, it's complex to understand"
msgstr ""

#. module: survey
#: model_terms:survey.page,description:survey.feedback_2
msgid ""
"These questions relate to the ergonomy and ease of use of Odoo. Try to "
"remind your firsts days on Odoo and\n"
"what have been your difficulties."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_2
msgid "They are clean and correct"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_3
msgid "They are useful on a daily usage"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_1
msgid "They help new users to understand Odoo"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:671 code:addons/survey/tests/test_survey.py:94
#, python-format
msgid "This answer must be an email address"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:713 code:addons/survey/tests/test_survey.py:115
#, python-format
msgid "This is not a date/time"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:690 code:addons/survey/tests/test_survey.py:105
#, python-format
msgid "This is not a number"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message_multi_email
msgid ""
"This list of emails of recipients will not converted in contacts. Emails "
"separated by commas, semicolons or newline."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_thank_you_message
msgid "This message will be displayed when survey is completed"
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:621
#: model:survey.question,constr_error_msg:survey.feedback_1_1
#: model:survey.question,constr_error_msg:survey.feedback_1_2
#: model:survey.question,constr_error_msg:survey.feedback_2_1
#: model:survey.question,constr_error_msg:survey.feedback_2_2
#: model:survey.question,constr_error_msg:survey.feedback_2_3
#: model:survey.question,constr_error_msg:survey.feedback_2_4
#: model:survey.question,constr_error_msg:survey.feedback_2_5
#: model:survey.question,constr_error_msg:survey.feedback_2_6
#: model:survey.question,constr_error_msg:survey.feedback_2_7
#: model:survey.question,constr_error_msg:survey.feedback_3_1
#: model:survey.question,constr_error_msg:survey.feedback_3_2
#: model:survey.question,constr_error_msg:survey.feedback_3_3
#: model:survey.question,constr_error_msg:survey.feedback_4_1
#, python-format
msgid "This question requires an answer."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "This survey has no pages by now!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "This survey is not open. Thank you for your interest!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "This survey is open only to registered people. Please"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.feedback_form
msgid "This survey should take less than five minutes."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_2
msgid ""
"Those security rules are standard and can be used out-of-the-box in most "
"cases"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_title
msgid "Title"
msgstr "Título"

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_4
#: model:survey.label,value:survey.fcol_2_2_4
#: model:survey.label,value:survey.fcol_2_5_4
#: model:survey.label,value:survey.fcol_2_7_4
msgid "Totally agree"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_1
#: model:survey.label,value:survey.fcol_2_2_1
#: model:survey.label,value:survey.fcol_2_5_1
#: model:survey.label,value:survey.fcol_2_7_1
msgid "Totally disagree"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tipo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_type
msgid "Type of Question"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Type of answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_labels_ids
msgid "Types of answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Use template"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_description
msgid ""
"Use this field to add             additional explanations about your question"
msgstr ""

#. module: survey
#: model:survey.survey,title:survey.feedback_form
msgid "User Feedback Form"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line_user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "User Input"
msgstr ""

#. module: survey
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "User Input Lines"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
#: model_terms:ir.ui.view,arch_db:survey.result_number
#: model_terms:ir.ui.view,arch_db:survey.result_text
msgid "User Responses"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "User can come back in the previous page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_form
msgid "User input line details"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_user_input_ids
msgid "User responses"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey_users_can_go_back
msgid "Users can go back"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey_auth_required
msgid ""
"Users with a public link will be requested to login before taking part to "
"the survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_validation_required
msgid "Validate entry"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "View Results"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "View results"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Vote"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_7
msgid "What do you think about configuration wizards?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_1
msgid "What do you think about the documentation available on doc.odoo.com?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_5
msgid "What do you think about the groups of users?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_2
msgid ""
"What do you think about the process views of Odoo, available in the web "
"client ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_4
#: model:survey.question,question:survey.feedback_2_6
msgid "What do you think about the structure of the menus?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_4_1
msgid "Where do you develop your new features?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_1_2
msgid "Which modules are you using/testing?"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.datetime
msgid "YYYY-MM-DD hh:mm:ss"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                            <i class=\"fa fa-calendar fa-2x"
"\"/>"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_1
msgid "Yes, I use a version < 7.0"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_2
msgid "Yes, I use the 7.0 version, installed locally"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_3
msgid "Yes, I use the online version of Odoo"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"You can share your survey web public link and/or send private invitations to "
"your audience. People can answer once per invitation, and whenever they want "
"with the public web link (in this case, the \"Public in website\" setting "
"must be enabled)."
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:424
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""

#. module: survey
#: code:addons/survey/survey.py:429
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "You scored"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "log in"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "of"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "points."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "review your answers"
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/tour_test_survey.js:10
#, python-format
msgid "try to create and fill survey"
msgstr ""

#~ msgid "Contents"
#~ msgstr "Contenidos"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "From"
#~ msgstr "Desde"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si marcado la nueva mensaje requiere atencion"

#~ msgid "Last Message Date"
#~ msgstr "Fecha de la ultima mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes y historial de comunicación"

#~ msgid "Reply-To"
#~ msgstr "Responder a"

#~ msgid "Subject"
#~ msgstr "Asunto"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"
