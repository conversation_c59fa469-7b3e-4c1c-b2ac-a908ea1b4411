# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid " : Check Number Sequence"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;</span>"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr ""
"ปรับระยะขอบของเช็คที่สร้างขึ้นเพื่อให้พอดีกับการตั้งค่าเครื่องพิมพ์ของคุณ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "จํานวนเงินเป็นตัวอักษร"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "ยกเลิก"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "ตรวจสอบเค้าโครง"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "ตรวจสอบระยะขอบด้านซ้าย"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "ตรวจสอบหมายเลข"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "ตรวจสอบการพิมพ์"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "ตรวจสอบระยะขอบด้านขวา"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "ตรวจสอบลำดับ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "ตรวจสอบระยะขอบด้านบน"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Check numbers can only consist of digits"
msgstr "หมายเลขเช็คต้องประกอบด้วยตัวเลขเท่านั้น"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "เลือกตัวเลือกนี้ หากเช็คที่พิมพ์ไว้ล่วงหน้าของคุณไม่มีหมายเลขกำกับ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "ตรวจสอบเพื่อสั่งพิมพ์"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "ตรวจสอบ"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "ตรวจสอบลำดับเลข"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
#, python-format
msgid "Checks to Print"
msgstr "ตรวจสอบเพื่อสั่งพิมพ์"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "ตรวจสอบเพื่อสั่งพิมพ์"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "หลายบริษัท"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "ไปยังหน้าการตั้งค่า"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ไอดี"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"หากต้องการพิมพ์เช็คหลายรายการในคราวเดียว "
"เช็คเหล่านั้นจะต้องอยู่ในสมุดรายวันของธนาคารเดียวกัน"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "การกำหนดหมายเลขด้วยตนเอง"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"จ่ายด้วยตนเอง: ชำระเงินหรือรับเงินด้วยวิธีการอื่นที่นอกเหนือจาก Odoo\n"
"ผู้รับการชำระเงิน: ผู้รับการชำระเงินแต่ละรายมีวิธีการชำระเงินของตนเอง ร้องขอการทำธุรกรรมบน/ไปยังบัตรด้วยโทเคนการชำระเงินที่บันทึกโดยพาร์ทเนอร์เมื่อซื้อหรือสมัครสมาชิกออนไลน์\n"
"เช็ค: ชำระค่าใช้จ่ายด้วยเช็คและพิมพ์จาก Odoo\n"
"การฝากแบบกลุ่ม: รวบรวมเช็คของลูกค้าหลายรายการพร้อมกันเพื่อสร้างและส่งการฝากแบบกลุ่มไปยังธนาคารของคุณ โมดูล account_batch_payment เป็นสิ่งจำเป็น\n"
"การโอนเครดิต SEPA: ชำระเงินในโซน SEPA โดยส่งไฟล์การโอนเครดิต SEPA ไปยังธนาคารของคุณ โมดูล account_sepa จำเป็น\n"
"การหักบัญชีอัตโนมัติ SEPA: รับเงินในโซน SEPA ด้วยอาณัติที่พาร์ทเนอร์ของคุณจะมอบให้กับคุณ โมดูล account_sepa จำเป็น\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "Stub ตรวจสอบหลายหน้า"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "หมายเลขเช็คถัดไป"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
#, python-format
msgid "Next Check Number should only contains numbers."
msgstr "หมายเลขเช็คถัดไปควรมีเฉพาะตัวเลขเท่านั้น"

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "ไม่มี"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_users__property_payment_method_id
msgid "Payment Method"
msgstr "วิธีการชำระเงิน"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "วิธีการชำระเงิน"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "การชำระเงิน"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"การชำระเงินที่จะพิมพ์เป็นเช็คจะต้องเลือก \"เช็ค\" "
"เป็นวิธีการชำระเงินและยังไม่ได้รับการกระทบยอด"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "โปรดป้อนหมายเลขเช็คที่พิมพ์ไว้ล่วงหน้าฉบับแรกที่คุณกำลังจะพิมพ์"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_bank_statement_line__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_move__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__preferred_payment_method_id
msgid "Preferred Payment Method"
msgstr "วิธีการชำระเงินที่ต้องการ"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,help:account_check_printing.field_res_users__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""
"ใช้วิธีการชำระเงินที่ต้องการเมื่อชำระเงินให้กับผู้ขายรายนี้ "
"กรองใบเรียกเก็บเงินของผู้จำหน่ายโดยวิธีการชำระเงินที่ต้องการเพื่อลงทะเบียนการชำระเงินเป็นกลุ่ม"
" กรณีการใช้งาน: สร้างไฟล์ธนาคารสำหรับการโอนเงินแบบกลุ่มและดำเนินการตรวจสอบ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "พิมพ์"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "พิมพ์เช็ค"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "พิมพ์เช็ค"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "พิมพ์ป้ายวันที่"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "พิมพ์เช็คที่กำหนดหมายเลขล่วงหน้า"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_register
msgid "Register Payment"
msgstr "ลงทะเบียนการชำระเงิน"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "ขอบด้านขวา"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"เลือกรูปแบบที่สอดคล้องกับกระดาษเช็คที่คุณจะพิมพ์เช็ค\n"
"หากต้องการปิดใช้งานฟีเจอร์การพิมพ์ ให้เลือก 'ไม่มี'"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "หมายเลขลำดับของเช็คที่พิมพ์ครั้งถัดไป"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "แสดงหมายเลขเช็ค"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr ""
"มีข้อผิดพลาดเกิดขึ้นกับเค้าโครงของเช็ค "
"โปรดเลือกเค้าโครงอื่นในการตั้งค่าการออกใบแจ้งหนี้/การบัญชีแล้วลองอีกครั้ง"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"มีการใช้หมายเลขต่อไปนี้แล้ว:\n"
"%s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"หมายเลขเช็คล่าสุดคือ %s เพื่อหลีกเลี่ยงไม่ให้ธนาคารปฏิเสธเช็ค "
"คุณสามารถใช้ตัวเลขที่มากกว่าได้เท่านั้น"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"สมุดรายวันที่เลือกได้รับการกำหนดค่าให้พิมพ์หมายเลขเช็ค "
"หากกระดาษเช็คที่พิมพ์ไว้ล่วงหน้าของคุณมีตัวเลขอยู่แล้ว "
"หรือหากหมายเลขปัจจุบันไม่ถูกต้อง "
"คุณสามารถเปลี่ยนได้ในหน้าการกำหนดค่าสมุดรายวัน"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""
"ตัวเลือกนี้ช่วยให้คุณสามารถพิมพ์รายละเอียดเช็ค (ต้นขั้ว) บนหลายหน้าได้ "
"หากไม่พอดีกับหน้าเดียว"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"ตัวเลือกนี้ช่วยให้คุณสามารถพิมพ์ป้ายกำกับวันที่บนเช็คตาม CPA\n"
"ปิดการใช้งานนี้หากเช็คที่พิมพ์ไว้ล่วงหน้าของคุณมีป้ายกำกับวันที่อยู่แล้ว"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "ซึ่งจะช่วยให้สามารถประหยัดการชำระเงินตามจำนวนเช็คที่เกี่ยวข้อง"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "ยกเลิกการทำเครื่องหมายว่าส่งแล้ว"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "ยกเลิกเช็ค"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
"คุณต้องเลือกเค้าโครงเช็ค สำหรับการดำเนินการนี้ "
"ให้ไปที่การตั้งค่าการออกใบแจ้งหนี้/การบัญชี ค้นหา 'เค้าโครงเช็ค' "
"และตั้งค่าหนึ่งรายการ"
