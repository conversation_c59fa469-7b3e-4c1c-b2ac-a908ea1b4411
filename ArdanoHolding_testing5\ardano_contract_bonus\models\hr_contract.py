from odoo import api, models, fields
from odoo.exceptions import ValidationError


class HrContract(models.Model):
    _inherit = "hr.contract"

    location_bonus = fields.Boolean(string='Location Bonus', default=False, copy=False)
    location_bonus_percentage = fields.Integer(string='Location Percentage(%)', copy=False)
    location_bonus_amount = fields.Integer(string='Location Amount', compute='_calc_location_bonus_amount')
    job_bonus = fields.Boolean(string='Job Bonus', default=False, copy=False)
    job_bonus_percentage = fields.Integer(string='Job Percentage(%)', copy=False)
    job_bonus_amount = fields.Integer(string='Job Amount', compute='_calc_job_bonus_amount')
    general_manager_bonus = fields.Boolean(string='General Manager Bonus', default=False, copy=False)
    general_manager_bonus_percentage = fields.Integer(string='General Manager Percentage(%)', copy=False)
    general_manager_bonus_amount = fields.Integer(string='General Manager Amount', compute='_calc_general_manager_bonus_amount')

    @api.depends('location_bonus', 'location_bonus_percentage', 'wage')
    def _calc_location_bonus_amount(self):
        for rec in self:
            rec.location_bonus_amount = int(rec.wage * rec.location_bonus_percentage / 100) if rec.location_bonus else 0

    @api.depends('job_bonus', 'job_bonus_percentage', 'wage')
    def _calc_job_bonus_amount(self):
        for rec in self:
            rec.job_bonus_amount = int(rec.wage * rec.job_bonus_percentage / 100) if rec.job_bonus else 0

    @api.constrains('general_manager_bonus_percentage')
    def _check_general_manager_bonus_percentage(self):
        for rec in self:
            if rec.general_manager_bonus_percentage > 25:
                raise ValidationError("General manager bonus percentage not exceed 25%")

    @api.depends('general_manager_bonus', 'general_manager_bonus_percentage', 'wage')
    def _calc_general_manager_bonus_amount(self):
        for rec in self:
            rec.general_manager_bonus_amount = int(
                rec.wage * rec.general_manager_bonus_percentage / 100) if rec.general_manager_bonus else 0
