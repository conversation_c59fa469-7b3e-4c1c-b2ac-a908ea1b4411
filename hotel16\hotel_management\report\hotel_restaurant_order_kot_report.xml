<odoo>
	<data>
		<template id = "hotel_restaurant_order_kot_report111">
		 <t t-call="web.html_container">
			<t t-call="web.external_layout">
			<t t-foreach="docs" t-as="o">
				<div class="page" style="padding-top:1px !important;">
					<div class="row">
							<br/>
							<br/>
							<div class="col-xs-12 text-center">
								<strong style ="font-family :Times New Roman ;line-height: 200%; font-size: 30px;">Kitchen Order Ticket</strong>
							</div>
						</div>
					<table width="100%">
						<tr height="30" width = "100%" style ="font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
							<td width ="50%" style="border: 1px solid black; background-color: #dab082;">
								<strong>Date</strong>
							</td>
							<td width ="50%" style="border: 1px solid black; background-color: #dab082;">
								<strong>Served By</strong>
							</td>
						</tr>
						
						<tr height="30" width = "100%" style ="font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
        					<td width ="505"  style="border: 1px solid black;">
					          <span t-field="o.o_date"/>
					        </td>
					        <td width ="50%"  style="border: 1px solid black;">
					          <p t-field="o.waiter_name1"/>
					        </td>
					     </tr>	
					</table>
					      <br/>
					      
					      <table  width="100%">
					      
					      <tr height="30" width ="100%" style =" font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
							<td width ="50%"  style="border: 1px solid black; background-color: #dab082;">
								<strong>Order Number</strong>
							</td>
							<td width ="50%" style="border: 1px solid black; background-color: #dab082;">
								<strong>Room Number</strong>
							</td>
						</tr>
						<tr width ="100%" height="30" style ="t font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
        					<td width ="50%"  style="border: 1px solid black;">
					          	<span t-field="o.order_no"/>
					        </td>
					        <td width ="50%"  style="border: 1px solid black;">
					          	<span t-field="o.room_no.name"/>
					        </td>
						</tr>	
						</table>
						<table  width="100%">
					    <tr height="30" style =" font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
							<td  style="border: 1px solid black; background-color: #dab082;">
								<strong>Table Number</strong>
							</td>
						</tr>
						
        				<tr t-foreach="o.table_no" t-as="l">
        				<td style="border: 1px solid black;">
					          <span t-field="l.name" />
					          </td>
					    </tr>    
					
					
					  <tr height="30" style ="; font-family: HelveticaNeue-CondensedBold; text-color: #19140e;">
							<td style="border: 1px solid black; background-color: #dab082;">
								<strong>Food Item List</strong>
						   </td>
					` </tr>
					</table>
					<table width="100%">
				      <tr height="30" width ="100%" style =" font-family: HelveticaNeue-CondensedBold; text-color: #19140e;"> 
				        <td width ="50%" style="border: 1px solid black; background-color: #dab082;">
								<strong>Food Item</strong>
							
				        </td>
				        <td width ="50%" style="border: 1px solid black; background-color: #dab082;">
								<strong>Quantity</strong>
						
				        </td>
				      </tr>
					<tr height="30" width ="100%" t-foreach="o.order_list" t-as="l">
								<td width ="50%" style="border: 1px solid black;">
									<span t-esc="l.product_id.name" />
								</td>
								<td width ="50%" style="border: 1px solid black;">
									<span t-field="l.item_qty" />
								</td>
					</tr>									
					</table>
				</div>
			</t>
		</t>
		 </t>
		</template>
	</data>
</odoo>