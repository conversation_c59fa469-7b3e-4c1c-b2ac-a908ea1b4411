<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_bulk_price_update_wizard_simple_form" model="ir.ui.view">
        <field name="name">bulk.price.update.wizard.simple.form</field>
        <field name="model">bulk.price.update.wizard</field>
        <field name="arch" type="xml">
            <form string="Bulk Price Update">
                <sheet>
                    <!-- Setup fields -->
                    <group string="Price Adjustment Settings">
                        <group>
                            <field name="adjustment_type"/>
                            <field name="adjustment_value"/>
                            <field name="operation_type"/>
                        </group>
                        <group>
                            <field name="category_ids" widget="many2many_tags"/>
                            <field name="tag_ids" widget="many2many_tags"/>
                        </group>
                        <group string="Rounding Options">
                            <field name="rounding_method"/>
                            <field name="decimal_places"/>
                        </group>
                    </group>
                    
                    <!-- Preview section - only visible when show_preview is True -->
                    <field name="show_preview" invisible="1"/>
                    <notebook attrs="{'invisible': [('show_preview', '=', False)]}">
                        <page string="Preview">
                            <field name="preview_line_ids" nolabel="1" widget="one2many">
                                <tree string="Price Preview" editable="false" create="false" delete="false">
                                    <field name="product_id" string="Product" width="200px"/>
                                    <field name="old_price" string="Current Price" width="100px"/>
                                    <field name="new_price" string="New Price" width="100px"/>
                                    <field name="price_difference" string="Difference" width="100px"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name="action_preview" string="Preview" type="object" 
                            class="btn-primary"/>
                    <button name="action_apply" string="Apply Changes" type="object" 
                            class="btn-primary" attrs="{'invisible': [('show_preview', '=', False)]}"
                            confirm="Are you sure you want to update these prices?"/>
                    <button special="cancel" string="Cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_bulk_price_update_wizard" model="ir.actions.act_window">
        <field name="name">Bulk Price Update</field>
        <field name="res_model">bulk.price.update.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
