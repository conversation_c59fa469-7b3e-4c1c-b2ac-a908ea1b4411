<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="reservation_payment_report_format" model="report.paperformat">
            <field name="name">Sale Quotation Custom format</field>
            <field name="default" eval="True"/>
            <field name="format">custom</field>
            <field name="page_height">350</field>
            <field name="page_width">250</field>
            <field name="orientation">Landscape</field>
            <field name="margin_top">35</field>
            <field name="margin_bottom">10</field>
            <field name="margin_left">5</field>
            <field name="margin_right">5</field>
            <field name="header_spacing">30</field>
            <field name="dpi">80</field>
        </record>

        <record id="reservation_payment_report_id" model="ir.actions.report">
            <field name="name">Reservation Payment Report</field>
            <field name="model">reservation.payment.report.wizard</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">banquet_managment.reservation_report_temp_id</field>
            <field name="report_file">banquet_managment.reservation_report_temp_id</field>
            <field name="paperformat_id" ref="banquet_managment.reservation_payment_report_format"/>
        </record>

    </data>
</odoo>