<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_financial_report_l10n_pt_ddr" model="account.report">
        <field name="name">Demonstração de Resultados</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="country_id" ref="base.pt"/>
        <field name="filter_journals" eval="True"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_l10n_pt_ddr_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos" model="account.report.line">
                <field name="name">RENDIMENTOS E GASTOS</field>
                <field name="code">RENDIMENTOS_E_GASTOS</field>
                <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_pt_ddr_reg_vendas_e_servicos_prestados" model="account.report.line">
                        <field name="name">Vendas e serviços prestados</field>
                        <field name="code">VENDAS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_vendas_e_servicos_prestados_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-72 - 71</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_subsidios_a_exploracao" model="account.report.line">
                        <field name="name">Subsídios à exploração</field>
                        <field name="code">EXPLORACAO</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_subsidios_a_exploracao_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-75</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_ganhos_perdas_imputados_de_subsidiarias_associadas_e_empreend_conjuntos" model="account.report.line">
                        <field name="name">Ganhos/perdas imputados de subsidiárias, associadas e empreend. conjuntos</field>
                        <field name="code">SUBSIDIARIAS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_ganhos_perdas_imputados_de_subsidiarias_associadas_e_empreend_conjuntos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SUBSIDIARIAS.c785_balance - SUBSIDIARIAS.C685_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_ganhos_perdas_imputados_de_subsidiarias_associadas_e_empreend_conjuntos_c785_balance" model="account.report.expression">
                                <field name="label">c785_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-785</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_ganhos_perdas_imputados_de_subsidiarias_associadas_e_empreend_conjuntos_c685_balance" model="account.report.expression">
                                <field name="label">C685_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-685</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_variacao_nos_inventarios_da_producao" model="account.report.line">
                        <field name="name">Variação nos inventários da produção</field>
                        <field name="code">VARIACAO</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_variacao_nos_inventarios_da_producao_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-73</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_trabalhos_para_a_propria_entidade" model="account.report.line">
                        <field name="name">Trabalhos para a própria entidade</field>
                        <field name="code">TRABALHOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_trabalhos_para_a_propria_entidade_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-74</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_custo_das_mercadorias_vendidas_e_das_materias_consumidas" model="account.report.line">
                        <field name="name">Custo das mercadorias vendidas e das matérias consumidas</field>
                        <field name="code">CUSTO_MERCADORIAS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_custo_das_mercadorias_vendidas_e_das_materias_consumidas_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">61</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_fornecimentos_e_servicos_externos" model="account.report.line">
                        <field name="name">Fornecimentos e serviços externos</field>
                        <field name="code">FORNECIMENTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_fornecimentos_e_servicos_externos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">62</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_gastos_com_o_pessoal" model="account.report.line">
                        <field name="name">Gastos com o pessoal</field>
                        <field name="code">GASTOS_PESSOAL</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_gastos_com_o_pessoal_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">63</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_ajustamentos_de_inventarios_perdas_reversoes" model="account.report.line">
                        <field name="name">Ajustamentos de Inventários (perdas/reversões)</field>
                        <field name="code">AJUSTAMENTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_ajustamentos_de_inventarios_perdas_reversoes_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-AJUSTAMENTOS.c652_balance + AJUSTAMENTOS.c7622_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_ajustamentos_de_inventarios_perdas_reversoes_c652_balance" model="account.report.expression">
                                <field name="label">c652_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-652</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_ajustamentos_de_inventarios_perdas_reversoes_c7622_balance" model="account.report.expression">
                                <field name="label">c7622_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7622</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_dividas_a_receber_perdas_reversoes" model="account.report.line">
                        <field name="name">Imparidade de dívidas a receber (perdas/reversões)</field>
                        <field name="code">IMPARIDADE</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_dividas_a_receber_perdas_reversoes_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-IMPARIDADE.c651_balance + IMPARIDADE.c7621_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_dividas_a_receber_perdas_reversoes_c651_balance" model="account.report.expression">
                                <field name="label">c651_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-651</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_dividas_a_receber_perdas_reversoes_c7621_balance" model="account.report.expression">
                                <field name="label">c7621_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7621</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_provisoes_aumento_reducoes" model="account.report.line">
                        <field name="name">Provisões (aumento/reduções)</field>
                        <field name="code">PROVISOES</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_provisoes_aumento_reducoes_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-PROVISOES.c67_balance + PROVISOES.c763_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_provisoes_aumento_reducoes_c67_balance" model="account.report.expression">
                                <field name="label">c67_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-67</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_provisoes_aumento_reducoes_c763_balance" model="account.report.expression">
                                <field name="label">c763_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-763</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes" model="account.report.line">
                        <field name="name">Imparidade de activos não depreciáveis/amortizáveis (perdas/reversões)</field>
                        <field name="code">IMPARIDADE_NAO_DEPRECIAVEIS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-IMPARIDADE_NAO_DEPRECIAVEIS.c653_balance - IMPARIDADE_NAO_DEPRECIAVEIS.c657_balance - IMPARIDADE_NAO_DEPRECIAVEIS.c658_balance + IMPARIDADE_NAO_DEPRECIAVEIS.sum_c7623_c7627_c7628_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes_c653_balance" model="account.report.expression">
                                <field name="label">c653_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-653</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes_c657_balance" model="account.report.expression">
                                <field name="label">c657_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-657</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes_c658_balance" model="account.report.expression">
                                <field name="label">c658_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-658</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_nao_depreciaveis_amortizaveis_perdas_reversoes_sum_c7623_c7627_c7628_balance" model="account.report.expression">
                                <field name="label">sum_c7623_c7627_c7628_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7628 - C7623 - C7627</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_aumentos_reducoes_de_justo_valor" model="account.report.line">
                        <field name="name">Aumentos/reduções de justo valor</field>
                        <field name="code">AUMENTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_aumentos_reducoes_de_justo_valor_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">AUMENTOS.c77_balance - AUMENTOS.c66_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_aumentos_reducoes_de_justo_valor_c77_balance" model="account.report.expression">
                                <field name="label">c77_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-77</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_aumentos_reducoes_de_justo_valor_c66_balance" model="account.report.expression">
                                <field name="label">c66_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-66</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_outros_rendimentos_e_ganhos" model="account.report.line">
                        <field name="name">Outros rendimentos e ganhos</field>
                        <field name="code">OUTROS_RENDIMENTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_rendimentos_e_ganhos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">OUTROS_RENDIMENTOS.c78_e785_balance + OUTROS_RENDIMENTOS.sum_c7918_c7928_c7988_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_rendimentos_e_ganhos_c78_e785_balance" model="account.report.expression">
                                <field name="label">c78_e785_balance</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('account_id.code', '=like', '78%'),('account_id.code', 'not like', '785')]"/>
                                <field name="date_scope">normal</field>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_rendimentos_e_ganhos_sum_c7918_c7928_c7988_balance" model="account.report.expression">
                                <field name="label">sum_c7918_c7928_c7988_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7918 - 7928 - 7988</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas" model="account.report.line">
                        <field name="name">Outros gastos e perdas</field>
                        <field name="code">OUTROS_GASTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">OUTROS_GASTOS.c68_e685_balance - OUTROS_GASTOS.c6918_balance - OUTROS_GASTOS.c6928_balance - OUTROS_GASTOS.c6988_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas_c68_e685_balance" model="account.report.expression">
                                <field name="label">c68_e685_balance</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('account_id.code', '=like', '68%'),('account_id.code', 'not like', '685')]"/>
                                <field name="date_scope">normal</field>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas_c6918_balance" model="account.report.expression">
                                <field name="label">c6918_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6918</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas_c6928_balance" model="account.report.expression">
                                <field name="label">c6928_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6928</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_outros_gastos_e_perdas_c6988_balance" model="account.report.expression">
                                <field name="label">c6988_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6988</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_resultado_antes_de_depreciacoes_gastos_de_financiamento_e_impostos" model="account.report.line">
                        <field name="name">Resultado antes de depreciações, gastos de financiamento e impostos</field>
                        <field name="code">REG_RESULTADO_ANTES_DE_DEPRECIACOES_GASTOS_DE_FINANCIAMENTO_E_IMPOSTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_resultado_antes_de_depreciacoes_gastos_de_financiamento_e_impostos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">VENDAS.balance + EXPLORACAO.balance + SUBSIDIARIAS.balance + VARIACAO.balance + TRABALHOS.balance - CUSTO_MERCADORIAS.balance - FORNECIMENTOS.balance - GASTOS_PESSOAL.balance - AJUSTAMENTOS.balance + IMPARIDADE.balance - PROVISOES.balance - IMPARIDADE_NAO_DEPRECIAVEIS.balance + AUMENTOS.balance + OUTROS_RENDIMENTOS.balance - OUTROS_GASTOS.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_gastos_reversoes_de_depreciacao_e_de_amortizacao" model="account.report.line">
                        <field name="name">Gastos/reversões de depreciação e de amortização</field>
                        <field name="code">GASTOS_DEPRECIACAO</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_gastos_reversoes_de_depreciacao_e_de_amortizacao_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-GASTOS_DEPRECIACAO.c64_balance + GASTOS_DEPRECIACAO.c761_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_gastos_reversoes_de_depreciacao_e_de_amortizacao_c64_balance" model="account.report.expression">
                                <field name="label">c64_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-64</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_gastos_reversoes_de_depreciacao_e_de_amortizacao_c761_balance" model="account.report.expression">
                                <field name="label">c761_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-761</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes" model="account.report.line">
                        <field name="name">Imparidade de activos depreciáveis/amortizáveis (perdas/reversões)</field>
                        <field name="code">IMPARIDADE_DEPRECIAVEIS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-IMPARIDADE_DEPRECIAVEIS.c654_balance - IMPARIDADE_DEPRECIAVEIS.c655_balance - IMPARIDADE_DEPRECIAVEIS.c656_balance + IMPARIDADE_DEPRECIAVEIS.sum_c7624_c7825_c7826_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes_c654_balance" model="account.report.expression">
                                <field name="label">c654_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-654</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes_c655_balance" model="account.report.expression">
                                <field name="label">c655_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-655</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes_c656_balance" model="account.report.expression">
                                <field name="label">c656_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-656</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_imparidade_de_activos_depreciaveis_amortizaveis_perdas_reversoes_sum_c7624_c7825_c7826_balance" model="account.report.expression">
                                <field name="label">sum_c7624_c7825_c7826_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7826 - 7624 - 7825</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_resultado_operacional_antes_de_financiamento_e_impostos" model="account.report.line">
                        <field name="name">Resultado operacional (antes de financiamento e Impostos)</field>
                        <field name="code">REG_RESULTADO_OPERACIONAL_ANTES_DE_FINANCIAMENTO_E_IMPOSTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_resultado_operacional_antes_de_financiamento_e_impostos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">REG_RESULTADO_ANTES_DE_DEPRECIACOES_GASTOS_DE_FINANCIAMENTO_E_IMPOSTOS.balance - GASTOS_DEPRECIACAO.balance - IMPARIDADE_DEPRECIAVEIS.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_rendimentos_similares_obtidos" model="account.report.line">
                        <field name="name">Juros e rendimentos similares obtidos</field>
                        <field name="code">JUROS_E_RENDIMENTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_rendimentos_similares_obtidos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-7921 - 7981 - 7911</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_gastos_similares_suportados" model="account.report.line">
                        <field name="name">Juros e gastos similares suportados</field>
                        <field name="code">JUROS_E_GASTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_gastos_similares_suportados_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-JUROS_E_GASTOS.c6911_balance - JUROS_E_GASTOS.c6921_balance - JUROS_E_GASTOS.c6981_balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_gastos_similares_suportados_c6911_balance" model="account.report.expression">
                                <field name="label">c6911_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6911</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_gastos_similares_suportados_c6921_balance" model="account.report.expression">
                                <field name="label">c6921_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6921</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_juros_e_gastos_similares_suportados_c6981_balance" model="account.report.expression">
                                <field name="label">c6981_balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-6981</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_resultado_antes_de_impostos" model="account.report.line">
                        <field name="name">Resultado antes de Impostos</field>
                        <field name="code">REG_RESULTADO_ANTES_DE_IMPOSTOS</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_resultado_antes_de_impostos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">REG_RESULTADO_OPERACIONAL_ANTES_DE_FINANCIAMENTO_E_IMPOSTOS.balance + JUROS_E_RENDIMENTOS.balance - JUROS_E_GASTOS.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_imposto_sobre_o_rendimento_do_periodos" model="account.report.line">
                        <field name="name">Imposto sobre o rendimento do período</field>
                        <field name="code">IMPOSTO_RENDIMENTO_PERIODO</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_imposto_sobre_o_rendimento_do_periodos_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-812</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_pt_ddr_reg_resultado_liquido_do_periodo" model="account.report.line">
                        <field name="name">Resultado líquido do período</field>
                        <field name="code">REG_RESULTADO_LIQUIDO_DO_PERIODO</field>
                        <field name="report_id" ref="account_financial_report_l10n_pt_ddr"/>
                        <field name="parent_id" ref="account_financial_report_l10n_pt_ddr_rendimentos_e_gastos"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_pt_ddr_reg_resultado_liquido_do_periodo_balance_account_codes" model="account.report.expression">
                                <field name="label">balance_account_codes</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">818</field>
                                <field name="date_scope">normal</field>
                            </record>
                            <record id="account_financial_report_l10n_pt_ddr_reg_resultado_liquido_do_periodo_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">REG_RESULTADO_LIQUIDO_DO_PERIODO.balance_account_codes + REG_RESULTADO_ANTES_DE_IMPOSTOS.balance - IMPOSTO_RENDIMENTO_PERIODO.balance</field>
                                <field name="date_scope">normal</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
