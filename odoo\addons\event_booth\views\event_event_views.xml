<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="event_event_view_form" model="ir.ui.view">
        <field name="name">event.event.view.form.inherit.event.booth</field>
        <field name="model">event.event</field>
        <field name="inherit_id" ref="event.view_event_form"/>
        <field name="priority" eval="4"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button class="oe_stat_button" name="%(event_booth_action_from_event)d"
                        type="action" icon="fa-university">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <span attrs="{'invisible': [('event_booth_count', '=', 0)]}">
                                <field name="event_booth_count_available"/> /
                            </span>
                            <field name="event_booth_count"/>
                        </span>
                        <span class="o_stat_text">Booths</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

</data></odoo>
