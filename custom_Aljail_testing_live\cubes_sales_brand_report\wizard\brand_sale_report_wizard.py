# -*- coding: utf-8 -*-
from odoo import models, fields, api

from dateutil import rrule
from datetime import date
from datetime import datetime


class sales_brand_report_wizard(models.TransientModel):
    _name = 'sales.brand.report.wizard'

    from_date = fields.Date('From Date')
    to_date = fields.Date('To Date')
    brand_id = fields.Many2one('product.tag', string="Brand")
    categ_id = fields.Many2one('product.category', string="Category")
    branch_id = fields.Many2one('sale.order.branch', string="Branch")

    def print_pdf(self):
        dates = list(rrule.rrule(rrule.MONTHLY, dtstart=self.from_date, until=self.to_date))
        if self.to_date.day < self.from_date.day:
            dates.append(datetime.combine(self.to_date, datetime.min.time()))
        dates[-1] = dates[-1].replace(day=self.to_date.day)
        domain = []
        if self.brand_id:
            domain.append(('brand_id', '=', self.brand_id.id))

        if self.categ_id:
            domain.append(('categ_id', '=', self.categ_id.id))
        products = self.env['product.product'].sudo().with_context({'months_array': dates,
                                                                    'branch_id':self.branch_id.id
                                                                    }).search(domain)
        dates = [x.strftime("%b %Y") for x in dates]
        data = self.read()
        datas = {
            'form': self.id,
            'dates': dates,
            'from_date': self.from_date,
            'to_date': self.to_date,
            'products': products.read()
        }
        return self.env.ref('cubes_sales_brand_report.print_sales_brand_report').report_action(self, data=datas)

    def print_excel(self):
        datas = {
            'form': self.id,
        }
        return self.env.ref('cubes_sales_brand_report.sales_brand_report_xlsx').report_action(self, data=datas)
