<template>
    <div t-name="product_matrix.matrix">
        <table class="o_matrix_input_table o_product_variant_matrix table table-sm table-striped table-bordered cursor-default">
            <thead>
                <tr>
                    <t t-foreach="header" t-as="column_header">
                        <th t-attf-class="o_matrix_title_header {{column_header_first?'text-start':'text-center'}}">
                            <span t-esc="column_header.name"/>
                            <t t-call="product_matrix.extra_price">
                                <t t-set="cell" t-value="column_header"/>
                            </t>
                        </th>
                    </t>
                </tr>
            </thead>
            <tbody>
                <tr t-foreach="rows" t-as="row">
                    <t t-foreach="row" t-as="cell">
                        <th t-if="cell.name" class="text-start">
                            <strong t-esc="cell.name"/>
                            <t t-call="product_matrix.extra_price"/>
                        </th>
                        <td t-else="">
                            <div t-if="cell.is_possible_combination" class="input-group">
                                <input type="number"
                                  class="o_matrix_input"
                                  t-att-ptav_ids="cell.ptav_ids"
                                  t-att-value="cell.qty"/>
                            </div>
                            <span t-else="" class="o_matrix_cell o_matrix_text_muted o_matrix_nocontent_container"> Not available </span>
                        </td>
                    </t>
                </tr>
            </tbody>
        </table>
    </div>
    <span t-name="product_matrix.extra_price" t-if="cell.price" class="badge rounded-pill text-bg-secondary">
            <!--
                price_extra is displayed as catalog price instead of
                price after pricelist because it is impossible to
                compute. Indeed, the pricelist rule might depend on the
                selected variant, so the price_extra will be different
                depending on the selected combination. The price of an
                attribute is therefore variable and it's not very
                accurate to display it.
                -->
            <span class="variant_price_extra" style="white-space: nowrap;">
                <t t-out="format(cell)"/>
            </span>
        </span>
</template>
