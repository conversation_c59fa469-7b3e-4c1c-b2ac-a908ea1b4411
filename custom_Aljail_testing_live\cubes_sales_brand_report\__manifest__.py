# -*- coding: utf-8 -*-
{
    'name': 'C<PERSON>s | Sales Branch Report PDF',
    'version': '********',
    'sequence': 1,
    'category': 'Generic Modules/Warehouse',
    'summary': 'Cubes | Sales Branch Report PDF',
    'description': "Cubes | Sales Branch Report PDF.",
    'author': '<PERSON>',
    'website': 'https://github.com/mohamedsherif91',
    "images": ['images/main_screenshot.png'],
    'depends': ['base', 'sale','stock','report_xlsx','sale_order_by_group_16'],
    'data': [
        'security/ir.model.access.csv',
        'wizard/brand_sale_report_wizard_view.xml',
        'report/brand_sale_report.xml',
        'views/brand_sale_report_view.xml',
        'views/stock_quant.xml',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'currency': 'EUR',
}