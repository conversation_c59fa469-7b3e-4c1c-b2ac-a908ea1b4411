# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (備份)"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "%s Payment Reminder - %s"
msgstr "%s 繳款提醒 － %s"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Follow-up ' + object.display_name"
msgstr "'跟進 ' + object.display_name"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__type
#: model:ir.model.fields,help:account_followup.field_res_users__type
msgid ""
"- Contact: Use this to organize the contact details of employees of a given company (e.g. CEO, CFO, ...).\n"
"- Invoice Address : Preferred address for all invoices. Selected by default when you invoice an order that belongs to this company.\n"
"- Delivery Address : Preferred address for all deliveries. Selected by default when you deliver an order that belongs to this company.\n"
"- Private: Private addresses are only visible by authorized users and contain sensitive data (employee home addresses, ...).\n"
"- Other: Other address for the company (e.g. subsidiary, ...)"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "15 Days"
msgstr "15 天"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "30 Days"
msgstr "30天"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "40 Days"
msgstr "40 天"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "50 Days"
msgstr "50 天"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "60 Days"
msgstr "60 天"

#. module: account_followup
#: model:mail.template,body_html:account_followup.email_template_followup_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        Exception made if there was a mistake of ours, it seems that an amount of <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"></t>\n"
"                        stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br>\n"
"                        Would your payment have been carried out after this mail was sent, please ignore this message.\n"
"                        Do not hesitate to contact our accounting department.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_4
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        Despite several reminders, your account is still not settled.\n"
"                        Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"                        I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"                        In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <br>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\"><t t-out=\"object.name or ''\"></t> （<t t-out=\"object.commercial_partner_id.name or ''\"></t>）你好！</t>\n"
"                        <t t-else=\"\"><t t-out=\"object.name or ''\"></t> 你好！</t>\n"
"                        <br>\n"
"                        雖然我們已幾次提醒和催繳，但你的帳戶仍有欠款未繳清。\n"
"                        除非欠款能在未來 8 天內全數繳清，否則，我們會展開法律行動追討欠債，不會另行通知。\n"
"                        我相信並希望，此事並不需要動用到法律行動這一步。下文已詳列到期付款的賬項詳情。\n"
"                        若就欠款事宜有疑問，請即聯絡 Odoo 會計部門。\n"
"                        <br>\n"
"                        順祝  安康\n"
"                        <br>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_2
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"></t> (<t t-out=\"object.commercial_partner_id.name or ''\"></t>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"></t>,</t>\n"
"                        <br>\n"
"                        We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"                        <br>\n"
"                        It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services). Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br>\n"
"                        If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"                        <br>\n"
"                        Details of due payments is printed below.\n"
"                        <br>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\"><t t-out=\"object.name or ''\"></t> （<t t-out=\"object.commercial_partner_id.name or ''\"></t>）你好！</t>\n"
"                        <t t-else=\"\"><t t-out=\"object.name or ''\"></t> 你好！</t>\n"
"                        <br>\n"
"                        很遺憾，雖然我們已發送催繳提醒，但你的帳戶仍有欠款未繳清，而且逾時不短。\n"
"                        <br>\n"
"                        請即繳付有關款項，此乃當前要務。若閣下持續欠款，我們需考慮停用你的帳戶，屆時我們將無法繼續向你公司提供產品 / 服務。敬請採取一切適當措施，確保能在未來 8 天內成功完成付款。\n"
"                        <br>\n"
"                        若出現我們未知的問題，妨礙你繳付發票款項，請盡快聯絡 Odoo 會計部門，好讓我們快速處理該問題。\n"
"                        <br>\n"
"                        已到期付款的賬項詳情如下。\n"
"                        <br>\n"
"                        順祝  安康\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"></t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br>\n"
"                            --\n"
"                            <br>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"></t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> 合作夥伴："

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\">到期</span>"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr "跟進操作名稱必須獨一無二。該名稱已設置給另一操作。"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "賬目總表範本"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__account_manager
msgid "Account Manager"
msgstr "科目管理"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "賬戶報表跟進；執行跟進"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "動作"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Activity"
msgstr "活動"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Activity Notes"
msgstr "活動備註"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_type_id
msgid "Activity Type"
msgstr "活動類型"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "加入備註"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Add contacts to notify..."
msgstr "加入要通知的聯絡人⋯"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__type
#: model:ir.model.fields,field_description:account_followup.field_res_users__type
msgid "Address Type"
msgstr "地址類型"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Assigned to me"
msgstr "編配給我"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__join_invoices
msgid "Attach Invoices"
msgstr "附加發票"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__attachment_ids
msgid "Attachment"
msgstr "附件"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__automatic
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Automatic"
msgstr "自動的"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body_html
msgid "Body Html"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__can_edit_body
msgid "Can Edit Body"
msgstr "可以編輯內文"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Cancel"
msgstr "取消"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "交流"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "公司"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Content Template"
msgstr "內容範本"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body
msgid "Contents"
msgstr "內容"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Country"
msgstr "國家"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_uid
msgid "Created by"
msgstr "創立者"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_date
msgid "Created on"
msgstr "建立於"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Customer"
msgstr "客戶"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "客戶編號："

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "客戶對帳單"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
#, python-format
msgid "Date"
msgstr "日期"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr "應收專案的動作的日期。通常在通過客戶對帳單發送提醒的時候會被自動設定"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "日期:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up lines must be different per company"
msgstr "每個公司的跟進資料行天數必須不同"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Dear %s,\n"
"\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"\n"
msgstr ""
"%s 你好！\n"
"\n"
"\n"
"若非我們出錯，記錄顯示下述金額仍未付清。敬請閣下立即採取適當行動，確保能在未來 8 天內完成繳款。\n"
"\n"
"若在收到本郵件前，閣下已完成繳付上述款項，請不用理會本郵件。如有疑問，請聯絡 Odoo 會計部門。謝謝！\n"
"\n"
"順祝  安康\n"
"\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Dear client, we kindly remind you that you still have unpaid invoices. "
"Please check them and take appropriate action. %s"
msgstr "親愛的客戶，謹提醒你尚有未清繳的發票。請翻查資料及採取適當措施。 %s"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "預設追蹤等級和相關功能"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Description"
msgstr "說明"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__activity_default_responsible_type
msgid ""
"Determine who will be assigned to the activity:\n"
"- Follow-up Responsible (default)\n"
"- Salesperson: Sales Person defined on the invoice\n"
"- Account Manager: Sales Person defined on the customer"
msgstr ""
"確定誰將被分配到該活動：\n"
"- 跟進負責人（預設）\n"
"- 銷售人員：發票上定義的銷售人員\n"
"- 客戶經理：根據客戶定義的銷售人員"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "到期時間"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "截止天數"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email
msgid "Email"
msgstr "電郵"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_add_signature
msgid "Email Add Signature"
msgstr "電子郵件及簽署"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Recipients"
msgstr "電郵收件人"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Subject"
msgstr "電郵主題"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Exclude from Follow-ups"
msgstr "不納入後續行動"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_recipient_ids
msgid "Extra Recipients"
msgstr "額外收件人"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "第一次提醒"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Follow up"
msgstr "跟進"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up %s - %s"
msgstr "跟進 %s － %s"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__type__followup
msgid "Follow-up Address"
msgstr "跟進地址"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "催款準則"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_line_id
msgid "Follow-up Level"
msgstr "催款等級"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "催款等級"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "催款報表"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "催款報表"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "查看樹形催款報表"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__followup
msgid "Follow-up Responsible"
msgstr "催款責任人"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "跟進狀態"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "後續追蹤步驟"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter generated"
msgstr "跟進郵件已生成"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "催款報表"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"            possible to use print and e-mail templates to send specific messages to\n"
"            the customer."
msgstr ""
"對於每個步驟，指定要採取的操作和延遲天數。\n"
"            可使用列印和電郵範本向\n"
"            客戶發送特定消息。"

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_4
msgid "Fourth reminder followup"
msgstr "第四次提醒跟進"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Group By"
msgstr "分組方式"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "需要動作"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "需要介入"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Invoice No."
msgstr "發票號碼"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_date
msgid "Invoice/Bill Date"
msgstr "應收憑單/帳單 日期"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Invoices"
msgstr "應收憑單"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__is_mail_template_editor
msgid "Is Editor"
msgstr "是編輯器"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "日記帳項目"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Kind reminder!"
msgstr "溫馨提醒！"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__lang
msgid "Language"
msgstr "語言"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__last_followup_date
msgid "Latest Follow-up"
msgstr "最近的催款"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Letter/Email Content"
msgstr "信件 / 電郵內容"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__mail_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__template_id
msgid "Mail Template"
msgstr "信件範本"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__manual
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Manual"
msgstr "手動"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr "下一行動日期"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "下次提醒日期設為 %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_next_action_date
msgid "Next reminder"
msgstr "下次提醒"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "無需動作"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "沒有要發送的催款!"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_note
msgid "Note"
msgstr "備註"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Notification"
msgstr "通知"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_manual_reminder__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"發送電子郵件時要選擇的可選翻譯語言（ISO 代碼）。如果未設置，將使用英文版本。這通常應該是提供適當語言的佔位符表達式，例如{{ "
"object.partner_id.lang }}。"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__followup_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the activities. If empty, we will find someone responsible."
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "選項"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_origin
#, python-format
msgid "Origin"
msgstr "原始"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "逾期發票"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr " 逾期未支付%s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:mail.template,name:account_followup.email_template_followup_1
#, python-format
msgid "Payment Reminder"
msgstr "繳款提醒"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__print
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Print"
msgstr "列印"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "列印催款信件"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process Automatic Follow-ups"
msgstr "處理自動跟進"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Reconcile"
msgstr "調節"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "編號"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Remind"
msgstr "提醒"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_reminder_type
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_reminder_type
msgid "Reminders"
msgstr "提醒"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__render_model
msgid "Rendering Model"
msgstr "渲染模型"

#. module: account_followup
#: model:ir.model,name:account_followup.model_ir_actions_report
msgid "Report Action"
msgstr "報告動作"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Residual Amount"
msgstr "殘值金額"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_default_responsible_type
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_responsible_id
msgid "Responsible"
msgstr "負責人"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "SMS"
msgstr "電話短訊（SMS）"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__salesperson
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Salesperson"
msgstr "銷售員"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_activity
msgid "Schedule Activity"
msgstr "安排活動"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "搜索催款"

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_2
msgid "Second reminder followup"
msgstr "第二次提醒跟進"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send"
msgstr "發送"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send & Print"
msgstr "發送與列印"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send Email"
msgstr "發送電子信件"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send SMS Message"
msgstr "發送短訊"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.manual_reminder_action
msgid "Send and Print"
msgstr "傳送及列印"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms
msgid "Sms"
msgstr "電話短訊（SMS）"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_body
msgid "Sms Body"
msgstr "短訊內文"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Sms Content"
msgstr "短訊內容"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_template_id
msgid "Sms Template"
msgstr "簡訊範本"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__subject
msgid "Subject"
msgstr "主題"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_summary
msgid "Summary"
msgstr "摘要"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__followup_next_action_date
msgid "The date before which no follow-up action should be taken."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr "建立應收憑單的文件."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Can be negative if you want to send the reminder before the "
"invoice due date."
msgstr "發票到期後發送提醒前等待的天數。 如果想在發票到期前發送提醒，可設為負數。"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "總計"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "Total Amount"
msgstr "金額總計"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "訂單合計"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "逾期 總計"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoice_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoice_ids
msgid "Unpaid Invoice"
msgstr "未付發票"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices_count
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices_count
msgid "Unpaid Invoices Count"
msgstr "未付發票數目"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "未核銷的Aml"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "帶有逾期發票的"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_form_view
msgid "With overdue invoices"
msgstr "帶有逾期發票"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "手動傳送客戶提醒精靈"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Write your message here..."
msgstr "寫下訊息⋯"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send an Email, but no follow-up contact has any email "
"address set for customer '%s'"
msgstr "你正嘗試發送電郵，但沒有後續聯絡人有為客戶「%s」設置任何電郵地址."

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send an SMS, but no follow-up contact has any mobile/phone"
" number set for customer '%s'"
msgstr "你正嘗試發送電話短訊，但沒有後續聯絡人有為客戶「%s」設置任何電話號碼."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days after due date"
msgstr "天(到期日後)"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "例：第一次提醒電郵"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "繳款提醒"

#. module: account_followup
#: model:mail.template,subject:account_followup.demo_followup_email_template_2
#: model:mail.template,subject:account_followup.demo_followup_email_template_4
#: model:mail.template,subject:account_followup.email_template_followup_1
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Payment Reminder - {{ object.commercial_company_name }}"
msgstr ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} 繳款提醒 － {{ object.commercial_company_name }}"
