# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>EN <<EMAIL>>, 2023
# <PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__signatures_count
msgid "# Signatures"
msgstr "# 签字"

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-att-href=\"ctx.get('salary_package_url')\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"   <h2>恭喜你！你可以通过点击下面的链接来配置你的工资套餐。</h2>\n"
"    你可以通过点击下面的链接来配置你的工资套餐。\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-att-href=\"ctx.get('salary_package_url')\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">配置你的软件包</a>\n"
"    </div>\n"
"</div>\n"
"        "

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr "<i class=\"fa fa-check-circle-o mr8\"/>恭喜"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/月</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/年</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Salary Package Configurator</span>"
msgstr "<span class=\"o_form_label\">工资包配置</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text ml4\">Previous Contract</span>"
msgstr "<span class=\"o_stat_text ml4\">以前的合同</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr "<span class=\"o_stat_text\">合同</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Reviews</span>"
msgstr "<span class=\"o_stat_text\">回顾</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid ""
"<span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('contract_id', '!=', False)]}\">An offer template need to be selected to have an offer link.</span>\n"
"                        <span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('display_warning_message', '=', False)]}\">An applicant name needs to be set to have an offer link.</span>"
msgstr ""
"<span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('contract_id', '!=', False)]}\">需要选择录用模板，才能有录用链接。</span>\n"
"                        <span class=\"text-muted fst-italic\" attrs=\"{'invisible': [('display_warning_message', '=', False)]}\">需要设置申请人姓名，以获得录用链接。</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<span class=\"text-muted mr4 ml4\">|</span>"
msgstr "<span class=\"text-muted mr4 ml4\">|</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "<span> /year</span>"
msgstr "<span>/年</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ 月</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>days / year</span>"
msgstr "<span>天数/年</span>"

#. module: hr_contract_salary
#: model:ir.model.constraint,message:hr_contract_salary.constraint_hr_contract_salary_advantage_required_fold_res_field_id
msgid "A folded field is required"
msgstr "需要有一个折叠的字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token_end_date
msgid "Access Token Validity Date"
msgstr "访问令牌的有效日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__active
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__active
msgid "Active"
msgstr "启用"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation
msgid "Activity Creation"
msgstr "活动创建"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation_type
msgid "Activity Creation Type"
msgstr "活动创建类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_type_id
msgid "Activity Type"
msgstr "活动类型"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Add a line"
msgstr "添加明细行"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Add a section"
msgstr "添加节"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_advantage.py:0
#, python-format
msgid "Advanges that are not linked to a field should always be displayed."
msgstr "与某一字段未链接的补贴应始终被显示"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__advantage_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__advantage_ids
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "Advantage"
msgstr "补贴"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__res_field_id
msgid "Advantage Field"
msgstr "补贴字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__advantage_type_id
msgid "Advantage Type"
msgstr "补贴类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__uom
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__uom
msgid "Advantage Unit of Measure"
msgstr "补贴 计量单位"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_advantage_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_advantage
msgid "Advantages"
msgstr "补贴"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__always
msgid "Always Selected"
msgstr "始终选择"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_employee_report__final_yearly_costs
msgid "Annual Employee Budget"
msgstr "年度员工预算"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Annual Employer Cost"
msgstr "员工年度成本"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__applicant_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__applicant_id
msgid "Applicant"
msgstr "申请人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__applies_on
msgid "Applies On"
msgstr "适用于"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Apply Now"
msgstr "现在申请"

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_clean_redundant_salary_data_ir_actions_server
#: model:ir.cron,cron_name:hr_contract_salary.ir_cron_clean_redundant_salary_data
msgid "Archive/Delete redundant generated salary data"
msgstr "存档/删除 多余工资数据"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Archived"
msgstr "已存档"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__activity_responsible_id
msgid "Assigned to"
msgstr "分派给"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_bachelor
msgid "Bachelor"
msgstr "本科"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__bank_account
msgid "Bank Account"
msgstr "银行账户"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_acc_number
msgid "Bank account"
msgstr "银行账户"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_birthdate
msgid "Birthdate"
msgstr "生日"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__category_id
msgid "Category"
msgstr "类别"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_certificate
msgid "Certificate"
msgstr "证书"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "Changes summary since previous contract :"
msgstr "自上一份合同以来的变更摘要："

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__checkbox
msgid "Checkbox"
msgstr "复选框"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__child_ids
msgid "Child"
msgstr "子级"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation_type
msgid ""
"Choose whether to create a next activity each time that the advantage is "
"taken by the employee or on modification only."
msgstr "选择是否在员工每次利用补贴时创建下一个活动，或仅在修改时创建。"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_city
msgid "City"
msgstr "城市"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "代号"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__color
msgid "Color"
msgstr "颜色"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__company_id
msgid "Company"
msgstr "公司"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage_type
msgid "Contract Advantage Type"
msgstr "合同补贴类型"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage_value
msgid "Contract Advantage Value"
msgstr "合同补贴价值"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Contract Information:"
msgstr "合同信息"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_start_date
msgid "Contract Start Date"
msgstr "合同开始日期"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job__default_contract_id
msgid "Contract Template"
msgstr "合同模板"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
msgid "Contract Templates"
msgstr "合同模板"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Contract Type"
msgstr "合同类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid "Contract Update Document Template"
msgstr "合同更新文档模板"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__contract
msgid "Contract Value"
msgstr "合同价值"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr ""
"合同和员工分析报告\n"
" "

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__res_field_id
msgid "Contract field linked to this advantage"
msgstr "与此补贴相关的合同字段"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__cost_res_field_id
msgid ""
"Contract field linked to this advantage cost. If not set, the advantage "
"won't be taken into account when computing the employee budget."
msgstr "与该补贴成本相关的合同字段。如果不设置，在计算雇员预算时，该补贴将不被考虑在内。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__fold_res_field_id
msgid "Contract field used to fold this advantage."
msgstr "合同字段用于折叠这一补贴。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__manual_res_field_id
msgid "Contract field used to manually encode an advantage value."
msgstr "用于手动编码补贴值的合同字段。"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_history
msgid "Contract history"
msgstr "合同历史"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation__countersigned
msgid "Contract is countersigned"
msgstr "合同已加签"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contracts Reviews"
msgstr "合同评审"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__cost_res_field_id
msgid "Cost Field"
msgstr "成本字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__cost_field
msgid "Cost Field Name"
msgstr "成本字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__country
msgid "Countries"
msgstr "国家"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__country_id
msgid "Country"
msgstr "国家"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Country of Birth"
msgstr "国籍"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__hash_token
msgid "Created From Token"
msgstr "从令牌创建"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_uid
msgid "Created by"
msgstr "创建人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_date
msgid "Created on"
msgstr "创建时间"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__currency_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__currency
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__currency
msgid "Currency"
msgstr "币种"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr "定制您的工资"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__date
msgid "Date"
msgstr "日期"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__days
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__days
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_advantages
#, python-format
msgid "Days"
msgstr "天"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__access_token_validity
msgid "Default Access Token Validity Duration"
msgstr "默认访问令牌的有效期限"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job__default_contract_id
msgid ""
"Default contract used to generate an offer. If empty, advantages will be "
"taken from current contract of the employee/nothing for an applicant."
msgstr "默认合约，用于生成聘用通知。如果为空，将从员工当前的合约中获取福利，申请人则没有任何福利。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr "向申请人发出聘用通知时使用的默认合同。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr "默认文档，即申请人因为接受合同报价而应当签署的文档。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr "默认文档，即员工为更新他的合同应当签署的文档。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__department_id
msgid "Department"
msgstr "部门"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__description
msgid "Description"
msgstr "说明"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#, python-format
msgid "Details"
msgstr "细节"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Discard"
msgstr "丢弃"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_type
msgid "Display Type"
msgstr "显示类型"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__display_warning_message
msgid "Display Warning Message"
msgstr "显示警告消息"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_doctor
msgid "Doctor"
msgstr "博士"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__document
msgid "Document"
msgstr "单据"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "没有要签署的文档"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"要签署的文件。只有有1个或2个不同负责人的文件可以选择。\n"
"        只有一个负责人的文件必须由雇员签字，而有两个不同负责人的文件必须由雇员和负责人签字。\n"
"        "

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__dropdown
msgid "Dropdown"
msgstr "下拉"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__dropdown-group
msgid "Dropdown Group"
msgstr "下拉组"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_email
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__email
msgid "Email"
msgstr "Email"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__email_to
msgid "Email To"
msgstr "EMail发送给"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__sign_copy_partner_id
msgid "Email address to which to transfer the signature."
msgstr "转移签字的电子邮件地址。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__employee
msgid "Employee"
msgstr "员工"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_contract_id
msgid "Employee Contract"
msgstr "员工合同"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Employee Name"
msgstr "员工姓名"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_image_1920
msgid "Employee Photo"
msgstr "员工照片"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation__running
msgid "Employee signs his contract"
msgstr "雇员签署合同"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer
msgid "Employee: Contract And Salary Package"
msgstr "员工：合同以及工资包"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"Equals to the sum of the following values:\n"
"\n"
"%s"
msgstr ""
"等于以下数值之和: \n"
"\n"
"%s"

#. module: hr_contract_salary
#: model:hr.contract.salary.advantage,name:hr_contract_salary.advantage_extra_time_off
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__holidays
msgid "Extra Time Off"
msgstr "额外的休息时间"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_female
msgid "Female"
msgstr "女性"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__field
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__field
msgid "Field Name"
msgstr "字段名称"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_field
msgid "Field of Study"
msgstr "研究领域"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__fixed_value
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__fixed
msgid "Fixed Value"
msgstr "固定值"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_field
msgid "Fold Field Name"
msgstr "折叠字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_label
msgid "Fold Label"
msgstr "折叠标签"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__fold_res_field_id
msgid "Fold Res Field"
msgstr "折叠资源字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__folded
msgid "Folded"
msgstr "收起"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_gender
msgid "Gender"
msgstr "性别"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Generate"
msgstr "生成"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Generate Offer"
msgstr "生成录取书"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_generate_simulation_link
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Generate Simulation Link"
msgstr "生成模拟链接"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.generate_offer_link_action
#: model:ir.actions.act_window,name:hr_contract_salary.generate_simulation_link_action
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Generate a Simulation Link"
msgstr "生成一个模拟链接"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__color__green
msgid "Green"
msgstr "绿色"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_search
msgid "Group By"
msgstr "分组"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
#, python-format
msgid ""
"HR Responsible %s should be a User of Sign and have a valid email address "
"when New Contract Document Template is specified"
msgstr "当指定新的合同文件模板时，HR负责人%s应该是签名用户，并有一个有效的电子邮件地址"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__helper
msgid "Helper"
msgstr "助手"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Hide <i class=\"fa fa-chevron-up\"/>"
msgstr "隐藏 <i class=\"fa fa-chevron-up\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide Children"
msgstr "隐藏子级"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__hide_description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__hide_description
msgid "Hide Description"
msgstr "隐藏的描述"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide children personal info when checked."
msgstr "检查时隐藏儿童的个人信息。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__hide_description
msgid "Hide the description if the advantage is not taken."
msgstr "如果不利用补贴，则隐藏描述。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__icon
msgid "Icon"
msgstr "图标"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920
msgid "Image"
msgstr "图像"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid "Impacts Monthly Total"
msgstr "每月影响总数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__impacts_net_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid "Impacts Net Salary"
msgstr "影响净薪"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__info_type_id
msgid "Info Type"
msgstr "信息类型"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "Information"
msgstr "信息"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__integer
msgid "Integer"
msgstr "整数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__is_required
msgid "Is Required"
msgstr "必填项"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__is_origin_contract_template
msgid "Is origin contract a contract template ?"
msgstr "原合同是一个合同模板吗？"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model,name:hr_contract_salary.model_hr_job
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_job_id
#, python-format
msgid "Job Position"
msgstr "工作岗位"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__job_title
#, python-format
msgid "Job Title"
msgstr "工作头衔"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__lang
msgid "Languages"
msgstr "语言"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume____last_update
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Let's create one"
msgstr "让我们创建一个"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__display_type__line
msgid "Line"
msgstr "行"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__left
msgid "Main Panel"
msgstr "主面板"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_male
msgid "Male"
msgstr "男性"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__manual_field
msgid "Manual Field Name"
msgstr "手动字段名称"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__manual
msgid "Manual Input"
msgstr "手工输入"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__manual_res_field_id
msgid "Manual Res Field"
msgstr "手动资源字段"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_master
msgid "Master"
msgstr "主版本"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_personal_info.py:0
#, python-format
msgid ""
"Mismatch between res_field_id %(field)s and model %(model)s for info "
"%(personal_info)s"
msgstr "信息%(personal_info)s的res_field_id %(field)s和模型%(model)s不匹配。"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_type__periodicity__monthly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__monthly
msgid "Monthly"
msgstr "每月"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Cost"
msgstr "每月费用"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Monthly Cost (Real)"
msgstr "每月费用(实际)"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Monthly Gross Salary"
msgstr "月工资总额"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__monthly_total
msgid "Monthly Total"
msgstr "每月总数"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "月工资"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__name
msgid "Name"
msgstr "名称"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Name of the field related to this personal info."
msgstr "与此个人信息相关的字段名称。"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "National Identification Number"
msgstr "公民身份证号码"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_id
msgid "Nationality"
msgstr "国籍"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr "净计算"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid "New Contract Document Template"
msgstr "新增的合同文档模板"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "No"
msgstr "否"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr "在工作岗位中没有定义人力资源主管。请与管理员联系。"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "No Template found"
msgstr "未找到模板"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
#, python-format
msgid "No private address defined on the employee!"
msgstr "该员工没有私人住址信息！"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"No signature template defined on the contract. Please contact the HR "
"responsible."
msgstr "该合同没有签名模板。请与人力资源主管联系。"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Not a valid e-mail address"
msgstr "无效的电子邮件地址"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Not a valid input in integer field"
msgstr "在整数字段不是一个有效的输入"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__holidays
msgid "Number of days of paid leaves the employee gets per year."
msgstr "雇员每年获得的带薪休假天数。"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_id
msgid "Offer Template"
msgstr "Offer模板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__url
msgid "Offer link"
msgstr "Offer链接"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/wizard/generate_simulation_link.py:0
#, python-format
msgid "Offer link can not be send. The applicant needs to have a name."
msgstr "无法发送要约链接。需要有申请人姓名。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Oops"
msgstr "哎呀"

#. module: hr_contract_salary
#: model:sign.template,redirect_url_text:hr_contract_salary.sign_template_cdi_developer
msgid "Open Link"
msgstr "打开链接"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "Origin Contract"
msgstr "原始合同"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Original Link"
msgstr "原链接"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_other
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_other
msgid "Other"
msgstr "其他"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__parent_id
msgid "Parent"
msgstr "上级"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__uom__percent
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__percent
msgid "Percent"
msgstr "百分比"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__periodicity
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__periodicity
msgid "Periodicity"
msgstr "周期"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_contact
msgid "Person to call"
msgstr "联络人"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Personal Documents"
msgstr "个人文件"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_personal_info_action
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__personal_info_id
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_personal_info
msgid "Personal Info"
msgstr "个人信息"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Personal Information"
msgstr "个人信息"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_phone
msgid "Phone Number"
msgstr "电话号码"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_phone
msgid "Phone number"
msgstr "电话号码"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_place_of_birth
msgid "Place of Birth"
msgstr "出生地"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__placeholder
msgid "Placeholder"
msgstr "占位符"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__position
msgid "Position"
msgstr "位置"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Previous Contract"
msgstr "前合同"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__address
msgid "Private Home Address"
msgstr "私人住宅地址"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr "草拟合同"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_reviews_count
msgid "Proposed Contracts Count"
msgstr "草拟的合同数"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__radio
msgid "Radio"
msgstr "单选"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__radio
msgid "Radio Buttons"
msgstr "单选按钮"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
#, python-format
msgid "Recompute"
msgstr "重新计算"

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer_applicant
msgid "Recruitment: Your Salary Package"
msgstr "招聘：工资包"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__color__red
msgid "Red"
msgstr "红色"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Related Field"
msgstr "关联字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__requested_documents_field_ids
msgid "Requested Documents"
msgstr "要求的文档"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__requested_documents
msgid "Requested Documents Fields"
msgstr "要求的文档字段"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_model
msgid "Res Model"
msgstr "Res 模型"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_resume_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_resume
msgid "Resume"
msgstr "重新开始"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Review Contract &amp; Sign"
msgstr "审查合同和签署"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_payroll_structure_type__salary_advantage_ids
msgid "Salary Advantage"
msgstr "工资补贴"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_advantage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_tree
msgid "Salary Package Advantage"
msgstr "工资包补贴"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_menu
msgid "Salary Package Configurator"
msgstr "工资包配置"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_tree
msgid "Salary Package Personal Info"
msgstr "工资包 个人信息"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_type
msgid "Salary Package Personal Info Type"
msgstr "工资包 个人信息类型"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_value
msgid "Salary Package Personal Info Value"
msgstr "工资包 个人信息价值"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr "工资包"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume_category
msgid "Salary Package Resume Category"
msgstr "工资包 简历类别"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Salary Package Summary"
msgstr "工资包摘要"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__structure_type_id
msgid "Salary Structure Type"
msgstr "工资结构类型"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_school
msgid "School"
msgstr "毕业院校"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_value__display_type__section
msgid "Section"
msgstr "节"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token
msgid "Security Token"
msgstr "安全令牌"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__dropdown
msgid "Selection"
msgstr "选中内容"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__dropdown_selection
msgid "Selection Nature"
msgstr "选择性质"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_copy_partner_id
msgid "Send a copy to"
msgstr "抄送"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer_applicant
msgid "Sent automatically when you generate an offer for an application"
msgstr "当您为一个申请生成要约时将自动发送"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer
msgid ""
"Sent manually when you generate a simulation link on the employee contract"
msgstr "在员工合同上生成模拟链接时手动发送"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__sequence
msgid "Sequence"
msgstr "单号规则"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Show <i class=\"fa fa-chevron-down\"/>"
msgstr "显示<i class=\"fa fa-chevron-down\"/>"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__right
msgid "Side Panel"
msgstr "侧边面板"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_frenquency
msgid "Sign Frenquency"
msgstr "签署频率"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "在合同文件上签字"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "签名请求 - %s"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__slider
msgid "Slider"
msgstr "滑块"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__slider_max
msgid "Slider Max"
msgstr "滑动最大值  Slider Max"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__slider_min
msgid "Slider Min"
msgstr "滑动最小值  Slider Min"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
#, python-format
msgid "Some required fields are not filled"
msgstr "一些必要的字段没有填写"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__specific
msgid "Specific Values"
msgstr "具体值"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_state_id
msgid "State"
msgstr "省/州"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__state
msgid "States"
msgstr "状态"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street
msgid "Street"
msgstr "街道"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street2
msgid "Street 2"
msgstr "街道2"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Structure Type"
msgstr "结构类型"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__sum
msgid "Sum of Advantages Values"
msgstr "补贴值之和"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__sign_template_id
msgid "Template to Sign"
msgstr "签署模板"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Templates"
msgstr "模板"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__display_type__text
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__text
msgid "Text"
msgstr "文本"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_creation
msgid ""
"The benefit is created when the employee signs his contract at the end of "
"the salary configurator or when the HR manager countersigns the contract."
msgstr "当员工在工资配置器的末尾签署合同时，或者当人力资源经理加签合同时，该福利就产生了。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr "本合同是复制的合同。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid ""
"The employee does not have a valid work email set. The Simulation Link won't"
" be able to be completed."
msgstr "员工未设置有效的工作电子邮件。模拟链接无法完成。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"The employee is not linked to an existing user, please contact the "
"administrator.."
msgstr "该员工没有与现有的用户联系，请联系管理员。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_advantage.py:0
#, python-format
msgid ""
"The minimum value for the slider should be inferior to the maximum value."
msgstr "滑块的最小值应低于最大值。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__signatures_count
msgid "The number of signatures on the pdf contract with the most signatures."
msgstr "PDF合同中最大签字数目。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_advantage__activity_type_id
msgid ""
"The type of activity that will be created automatically on the contract if "
"this advantage is chosen by the employee."
msgstr "如果员工选择这一补贴，将在合同上自动创建的活动类型。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_advantages
msgid "There is no available option to customize your salary"
msgstr "没有可用的选项来定制你的工资"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"They will review your contract.<br/> Feel free to contact them if you have "
"any questions."
msgstr "他们会审查您的合同。<br/> 如有任何疑问，请随时联系。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "This contract has been updated, please request an updated link.."
msgstr "此合同已被更新，请要求提供更新的链接。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid ""
"This link is invalid. Please contact the HR Responsible to get a new one..."
msgstr "这个链接是无效的。请联系人力资源部门负责人，以获得一个新的链接..."

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Total real monthly cost of the employee for the employer."
msgstr "雇主的员工每月实际费用总额。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Total real yearly cost of the employee for the employer."
msgstr "员工对雇主的实际年度总成本。"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
msgid "Total yearly cost of the employee for the employer."
msgstr "雇主每年为员工支付的总费用。"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr "新申请人工资包请求的有效期限"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_advantage_value__value
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__value
msgid "Value"
msgstr "价值"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__value_type
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Value Type"
msgstr "值类型"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Wage"
msgstr "工资"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on Payroll"
msgstr "工资册上的工资"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on contract signature"
msgstr "签字合同时的工资"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
msgid "Wage update with holidays retenues"
msgstr "工资更新与假期收入"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_with_holidays
msgid "Wage with Holidays"
msgstr "带假期的工资"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation_type__onchange
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__sign_frenquency__always
msgid "When the advantage is modified"
msgstr "当补贴被修改时"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__activity_creation_type__always
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage__sign_frenquency__onchange
msgid "When the advantage is set"
msgstr "当补贴被设定为"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_advantage_type__periodicity__yearly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__yearly
msgid "Yearly"
msgstr "每年"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__final_yearly_costs
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Yearly Cost"
msgstr "年度成本"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Yearly Cost (Real)"
msgstr "电镀成本（实际）。"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#, python-format
msgid "Yes"
msgstr "是"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Your Personal Information"
msgstr "您的个人信息"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr "您的合同已发送至："

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_zip
msgid "Zip Code"
msgstr "邮政编码"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "close"
msgstr "关闭"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_contract_employee_id
msgid "contract employee"
msgstr "合同员工"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "days"
msgstr "天数"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "e.g. Birthdate"
msgstr "例如：出生日期"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_advantage_view_form
msgid "e.g. Meal Vouchers"
msgstr "例如餐券"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer_applicant
msgid "{{ object.company_id.name }} : Job Offer - {{ object.name }}"
msgstr "{{ object.company_id.name }} : 录取通知 - {{ object.name }}"
