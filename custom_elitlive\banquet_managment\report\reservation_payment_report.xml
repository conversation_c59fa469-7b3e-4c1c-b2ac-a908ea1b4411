<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="reservation_report_temp_id">
            <t t-call="web.html_container">
                <t t-call="web.internal_layout">
                    <!-- <t t-call="report.external_layout"> -->
                    <div class="page">

                        <table class="table table-sm table-bordered">
                            <thead>
                                <th>No#</th>
                                <th>Reservation No</th>
                                <th>Checkin Date</th>
                                <th>Real Checkout Date</th>
                                <th>Customer</th>
                                <th>Reservation Status</th>
                                <th>Check-In Status</th>
                                <th>Paid Status</th>
                                <th>Paid Reservation</th>
                                <th>Paid Guarantee</th>
                                <th>Note</th>
                            </thead>
                            <t t-if="record">
                            <t t-set="ser" t-value="0"/>
                            <t t-foreach="record" t-as="rec">
                                <t t-set="ser" t-value="ser + 1"/>
                                <tr>
                                    <td>
                                        <t t-esc="ser"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_id.name"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_id.reservation_line[0].checkin.replace(hour=rec.reservation_id.reservation_line[0].checkin.hour -7 , minute=0, second=0, microsecond=0)"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_id.reservation_line[0].real_checkout_time.replace(hour=rec.reservation_id.reservation_line[0].real_checkout_time.hour -7 , minute=0, second=0, microsecond=0)"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.partner_id.name"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_id.state.capitalize()"/>
                                    </td>
                                    <td>
                                        <t t-if="rec.reservation_id.state != 'done'">
                                            Not Check-in
                                        </t>
                                        <t t-else="">
                                            Check-in
                                        </t>
                                    </td>
                                    <td>
                                        <t t-esc="rec.state.capitalize()"/>
                                    </td>
                                    <td class="text-end">
                                        <t t-esc="rec.reservation_id.total_cost1"/>
                                    </td>
                                    <td class="text-end">
                                        <t t-esc="rec.guarantee_amount"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_id.note"/>
                                    </td>
                                </tr>
                            </t>
                            <tr>
                                <td class="fw-bold" colspan="8">Total</td>
                                <td class="fw-bold text-end" ><t t-esc="sum([rec.reservation_id.total_cost1 for rec in record])"/></td>
                                <td class="fw-bold text-end"><t t-esc="sum([rec.guarantee_amount for rec in record])"/></td>
                                <td></td>
                            </tr>
                            </t>
                            <t t-else="">
                                <t t-set="ser" t-value="0"/>
                            <t t-foreach="reservation" t-as="rec">
                                <t t-set="ser" t-value="ser + 1"/>
                                <tr>
                                    <td>
                                        <t t-esc="ser"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.name"/>
                                    </td>


                                    <td>
                                        <t t-esc="rec.reservation_line[0].checkin.replace(hour=rec.reservation_line[0].checkin.hour -7 , minute=0, second=0, microsecond=0)"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.reservation_line[0].real_checkout_time.replace(hour=rec.reservation_line[0].real_checkout_time.hour -7 , minute=0, second=0, microsecond=0)"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.partner_id.name"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.state.capitalize()"/>
                                    </td>
                                    <td>
                                        <t t-if="rec.state != 'done'">
                                            Not Check-in
                                        </t>
                                        <t t-else="">
                                            Check-in
                                        </t>
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td class="text-end">
                                        <t t-esc="rec.total_cost1"/>
                                    </td>
                                    <td class="text-end">
                                        <t t-esc="rec.guarrante_amount"/>
                                    </td>
                                    <td>
                                        <t t-esc="rec.note"/>
                                    </td>
                                </tr>
                            </t>
                            <tr>
                                <td class="fw-bold" colspan="8">Total</td>
                                <td class="fw-bold text-end" ><t t-esc="sum([rec.total_cost1 for rec in reservation])"/></td>
                                <td class="fw-bold text-end"><t t-esc="sum([rec.guarrante_amount for rec in reservation])"/></td>
                                <td></td>
                            </tr>
                            </t>
                        </table>

                    </div>
                </t>
            </t>
        </template>


    </data>
</odoo>