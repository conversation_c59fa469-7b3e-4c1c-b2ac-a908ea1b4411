# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_no_saft
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-21 15:57+0000\n"
"PO-Revision-Date: 2024-06-21 15:57+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_no_saft
#: model_terms:ir.ui.view,arch_db:l10n_no_saft.saft_template_inherit_l10n_no_saft
msgid "100"
msgstr ""

#. module: l10n_no_saft
#: model_terms:ir.ui.view,arch_db:l10n_no_saft.saft_template_inherit_l10n_no_saft
msgid "GL"
msgstr ""

#. module: l10n_no_saft
#: model:ir.model,name:l10n_no_saft.model_account_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: l10n_no_saft
#: code:addons/l10n_no_saft/models/account_general_ledger.py:0
#, python-format
msgid "Go to Apps"
msgstr ""

#. module: l10n_no_saft
#: model_terms:ir.ui.view,arch_db:l10n_no_saft.l10n_no_tax_information
#: model_terms:ir.ui.view,arch_db:l10n_no_saft.saft_template_inherit_l10n_no_saft
msgid "MVA"
msgstr ""

#. module: l10n_no_saft
#: model_terms:ir.ui.view,arch_db:l10n_no_saft.saft_template_inherit_l10n_no_saft
msgid "Merverdiavgift"
msgstr ""

#. module: l10n_no_saft
#: code:addons/l10n_no_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please change your tax names to include their Norwegian standard tax code, delimited by spaces.\n"
"For example: 'Utgående mva lav sats 12%' => '33 Utgående mva lav sats 12%'"
msgstr ""

#. module: l10n_no_saft
#: code:addons/l10n_no_saft/models/account_general_ledger.py:0
#, python-format
msgid "SAF-T"
msgstr ""

#. module: l10n_no_saft
#: code:addons/l10n_no_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"The version for the SAF-T file has been updated. Please upgrade its module "
"(l10n_no_saft) for the export to work properly."
msgstr ""

#. module: l10n_no_saft
#: code:addons/l10n_no_saft/models/account_general_ledger.py:0
#, python-format
msgid "XML"
msgstr ""
