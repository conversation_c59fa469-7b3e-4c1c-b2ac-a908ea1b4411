# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_reports_sbr_icp
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-15 15:40+0000\n"
"PO-Revision-Date: 2024-04-15 15:40+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid ""
"A new module (l10n_nl_reports_sbr_status_info) needs to be installed for the "
"service to work correctly."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_type
msgid ""
"BPL: if the taxpayer files a turnover tax return as an individual "
"entrepreneur.INT: if the turnover tax return is made by an intermediary."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__can_report_be_sent
msgid "Can Report Be Sent"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__password
msgid "Certificate or private key password"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__is_test
msgid ""
"Check this if you want the system to use the pre-production environment with"
" test certificates."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Close"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Closing Entry"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_initials
msgid "Contact Initials"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_surname
msgid "Contact Last Name"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_prefix
msgid "Contact Name Infix"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_phone
msgid "Contact Phone"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__contact_type
msgid "Contact Type"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Create Closing Entry"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Create EC Sales (ICP) XBRL for SBR"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Download"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_l10n_nl_ec_sales_report_handler
msgid "Dutch EC Sales Report Custom Handler for SBR"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid "EC Sales (ICP) SBR"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Go to Apps"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "ICP report sent"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__is_test
msgid "Is Test"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model,name:l10n_nl_reports_sbr_icp.model_l10n_nl_reports_sbr_icp_icp_wizard
msgid "L10n NL Intra-Communautaire Prestaties for SBR Wizard"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_res_company__l10n_nl_reports_sbr_icp_last_sent_date_to
msgid "Last Date Sent (ICP)"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "New SBR File"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"No Closing Entry was found for the selected period. Please create one and "
"post it before sending your report."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__date_to
msgid "Period Ending Date"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__date_from
msgid "Period Starting Date"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.l10n_nl_reports_sbr_icp_icp_wizard_form
msgid "Send"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid "Sending your report"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_res_company__l10n_nl_reports_sbr_icp_last_sent_date_to
msgid ""
"Stores the date of the end of the last period submitted to the Digipoort "
"Services for ICP"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__tax_consultant_number
msgid "Tax Consultant Number"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The Closing Entry for the selected period is still in draft. Please post it "
"before sending your report."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The ICP report from %s to %s was sent to Digipoort.<br/>We will post its "
"processing status in this chatter once received.<br/>Discussion id: %s"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"The Tax Services returned the error hereunder. Please upgrade your module "
"and try again before submitting a ticket."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__password
msgid "The password is not needed for just printing the XBRL file."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model:ir.model.fields,help:l10n_nl_reports_sbr_icp.field_l10n_nl_reports_sbr_icp_icp_wizard__tax_consultant_number
msgid ""
"The tax consultant number of the office aware of the content of this report."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#: code:addons/l10n_nl_reports_sbr_icp/models/account_sales_report.py:0
#, python-format
msgid "XBRL"
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#. odoo-python
#: code:addons/l10n_nl_reports_sbr_icp/wizard/l10n_nl_sbr_icp_wizard.py:0
#, python-format
msgid ""
"Your ICP report is being sent to Digipoort. Check its status in the closing "
"entry's chatter."
msgstr ""

#. module: l10n_nl_reports_sbr_icp
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr_icp.icp_report_sbr
msgid "iso4217:EUR"
msgstr ""
