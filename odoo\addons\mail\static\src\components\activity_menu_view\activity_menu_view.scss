.o_ActivityMenuView_activityGroups {
    flex: 0 1 auto;
    max-height: 400px;
    min-height: 50px;
    overflow-y: auto;

    @include media-breakpoint-down(md) {
        max-height: none;
        padding-bottom: 52px; // leave space for tabs
    }
}

.o_ActivityMenuView_activityGroup {
    display: flex;
    background-color: transparent;
    color: $o-main-text-color;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    &:hover {
        background-color: map-get($theme-colors, 'light');
        .o_ActivityMenuView_activityGroupName {
            color: $headings-color;
        }
    }
    &:not(:last-child) {
        border-bottom: 1px solid map-get($grays, '400');
    }
    @include media-breakpoint-down(md) {
        padding: $o-mail-chatter-mobile-gap;
    }

    @include media-breakpoint-up(md) {
        min-height: 50px;
        padding: 5px;
    }
}

.o_ActivityMenuView_activityGroupActionButtons {
    display: flex;
    flex: 1 1 auto;
    flex-flow: row-reverse wrap;
}

.o_ActivityMenuView_activityGroupActionButton {
    padding-top: 0px;
    padding-bottom: 0px;
    padding-right: 0px;
}

.o_ActivityMenuView_activityGroupFilterButton {
    padding: 2px;
}

.o_ActivityMenuView_activityGroupIconContainer {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
    position: relative;
    width: 40px;
    height: 40px;
    object-fit: cover;
    > img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        border-radius: 2px;
    }

    @include media-breakpoint-up(md) {
        .fa-circle-o {
            display: none;
        }
    }
}

.o_ActivityMenuView_activityGroupInfo {
    flex: 1 1 100%;
    overflow: hidden;

    @include media-breakpoint-down(md) {
        margin-left: $o-mail-chatter-mobile-gap;
    }

    @include media-breakpoint-up(md) {
        margin-left: 10px;
    }
}

.o_ActivityMenuView_activityGroupName {
    flex: 0 1 auto;
    @include o-text-overflow;

    @include media-breakpoint-down(md) {
        font-size: 1.1em;
    }
}

.o_ActivityMenuView_activityGroupNoCount {
    cursor: initial;
    align-items: center;
    opacity: 0.5;
    padding: 3px;
    min-height: inherit;
}

.o_ActivityMenuView_activityGroupTitle {
    align-items: center;
    display: flex;
}

.o_ActivityMenuView_dropdownMenu {
    direction: ltr;
    width: 350px;
    padding: 0;

    @include media-breakpoint-down(md) {
        position: fixed;
        top: $o-mail-chat-window-header-height-mobile;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        margin: 0;
        max-height: none;
    }
}

.o_ActivityMenuView_noActivity {
    cursor: initial;
    align-items: center;
    color: grey;
    opacity: 0.5;
    padding: 3px;
    min-height: inherit;
}
