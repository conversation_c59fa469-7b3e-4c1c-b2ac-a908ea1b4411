# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project_forecast
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# alenafairy, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 13:29+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: alenafairy, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_project_forecast
#: model:ir.model,name:sale_project_forecast.model_planning_slot
msgid "Planning Shift"
msgstr "Планирование смены"

#. module: sale_project_forecast
#: model:ir.model.fields,field_description:sale_project_forecast.field_planning_slot__sale_line_id
msgid "Sales Order Item"
msgstr "Элемент заказа на продажу"

#. module: sale_project_forecast
#: model:ir.model,name:sale_project_forecast.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_project_forecast
#: model:ir.model.fields,help:sale_project_forecast.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""
"Позиция в заказе на продажу, для которого будет выполнена эта смена. При "
"автоматическом планировании заказов на продажу учитываются оставшиеся часы "
"элемента заказа на продажу, а также роль, определенная в услуге."
