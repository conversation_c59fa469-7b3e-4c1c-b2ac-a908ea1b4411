<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_financial_report_rs_BS" model="account.report">
        <field name="name">Balance Sheet</field>
        <field name="root_report_id" ref="account_reports.balance_sheet"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_date_range" eval="False"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.rs"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_rs_BS_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
                <field name="sequence" eval="1"/>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_report_rs_BS_assets" model="account.report.line">
                <field name="name">Assets (A + B + C + D)</field>
                <field name="code">RS_0071</field>
                <field name="hierarchy_level" eval="0"/>
                <field name="sequence" eval="1"/>
                <field name="expression_ids">
                    <record id="account_financial_report_rs_BS_assets_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">RS_0001.balance + RS_0002.balance + RS_0042.balance + RS_0043.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_rs_BS_assets_A" model="account.report.line">
                        <field name="name">A. Subscribed capital unpaid</field>
                        <field name="code">RS_0001</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="2"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_assets_A_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">00</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_assets_B" model="account.report.line">
                        <field name="name">B. Permanent assets (I + II + III + IV + V)</field>
                        <field name="code">RS_0002</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="3"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_assets_B_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">RS_0003.balance + RS_0010.balance + RS_0019.balance + RS_0024.balance + RS_0034.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_rs_BS_assets_B_I" model="account.report.line">
                                <field name="name">I. Intangible assets</field>
                                <field name="code">RS_0003</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="4"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_I_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0004.balance + RS_0005.balance + RS_0006.balance + RS_0007.balance + RS_0008.balance + RS_0009.balance + RS_Assets_B_I_7.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_I_1" model="account.report.line">
                                        <field name="name">1. Investment in development</field>
                                        <field name="code">RS_0004</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="5"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">010</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_2" model="account.report.line">
                                        <field name="name">2. Concessions, patents, licenses, trademarks, service marks, software and similar rights</field>
                                        <field name="code">RS_0005</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="6"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">011 + 012</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_3" model="account.report.line">
                                        <field name="name">3. Goodwill</field>
                                        <field name="code">RS_0006</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="7"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">013</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_4" model="account.report.line">
                                        <field name="name">4. Other intangible assets</field>
                                        <field name="code">RS_0007</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="8"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">014</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_5" model="account.report.line">
                                        <field name="name">5. In-process intangible assets</field>
                                        <field name="code">RS_0008</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="9"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">015</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_6" model="account.report.line">
                                        <field name="name">6. Advances for intangible assets</field>
                                        <field name="code">RS_0009</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="10"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">016</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_I_7" model="account.report.line">
                                        <field name="name">7. Provisions for acquisition of intangible assets</field>
                                        <field name="code">RS_Assets_B_I_7</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="11"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_I_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">019</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_B_II" model="account.report.line">
                                <field name="name">II. Immovables, plants and equipment</field>
                                <field name="code">RS_0010</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="12"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_II_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0011.balance + RS_0012.balance + RS_0013.balance + RS_0014.balance + RS_0015.balance + RS_0016.balance + RS_0017.balance + RS_0018.balance + RS_Assets_B_II_9.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_II_1" model="account.report.line">
                                        <field name="name">1. Land</field>
                                        <field name="code">RS_0011</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="13"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">021 + 020</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_2" model="account.report.line">
                                        <field name="name">2. Buildings</field>
                                        <field name="code">RS_0012</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="14"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">022</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_3" model="account.report.line">
                                        <field name="name">3. Plant and equipment</field>
                                        <field name="code">RS_0013</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="15"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">023</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_4" model="account.report.line">
                                        <field name="name">4. Investment immovables</field>
                                        <field name="code">RS_0014</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="16"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">024</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_5" model="account.report.line">
                                        <field name="name">5. Other immovables, plant and equipment</field>
                                        <field name="code">RS_0015</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="17"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">025</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_6" model="account.report.line">
                                        <field name="name">6. Immovables, plant and equipment under construction</field>
                                        <field name="code">RS_0016</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="18"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">026</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_7" model="account.report.line">
                                        <field name="name">7. Investments in third-party immovables, plant and equipment</field>
                                        <field name="code">RS_0017</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="19"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">027</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_8" model="account.report.line">
                                        <field name="name">8. Advances for immovables, plant and equipment</field>
                                        <field name="code">RS_0018</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="20"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">028</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_II_9" model="account.report.line">
                                        <field name="name">9. Provisions for property, plant and equipment</field>
                                        <field name="code">RS_Assets_B_II_9</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="21"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_II_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">029</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_B_III" model="account.report.line">
                                <field name="name">III. Biological resources</field>
                                <field name="code">RS_0019</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="22"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_III_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0020.balance + RS_0021.balance + RS_0022.balance + RS_0023.balance + RS_Assets_B_III_5.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_III_1" model="account.report.line">
                                        <field name="name">1. Forest and plantations</field>
                                        <field name="code">RS_0020</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="23"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_III_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">030 + 031</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_III_2" model="account.report.line">
                                        <field name="name">2. Livestock</field>
                                        <field name="code">RS_0021</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="24"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_III_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">032</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_III_3" model="account.report.line">
                                        <field name="name">3. Biological resources in preparation</field>
                                        <field name="code">RS_0022</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="25"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_III_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">037</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_III_4" model="account.report.line">
                                        <field name="name">4. Advances for biological resources</field>
                                        <field name="code">RS_0023</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="26"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_III_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">038</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_III_5" model="account.report.line">
                                        <field name="name">5. Provisions of natural assets</field>
                                        <field name="code">RS_Assets_B_III_5</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="27"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_III_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">039</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_B_IV" model="account.report.line">
                                <field name="name">IV. Long-term financial investments</field>
                                <field name="code">RS_0024</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="28"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_IV_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0025.balance + RS_0026.balance + RS_0027.balance + RS_0028.balance + RS_0029.balance + RS_0030.balance + RS_0031.balance + RS_0032.balance + RS_0033.balance + RS_Assets_B_IV_10.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_IV_1" model="account.report.line">
                                        <field name="name">1. Participation in equity of subsidiaries</field>
                                        <field name="code">RS_0025</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="29"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">040</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_2" model="account.report.line">
                                        <field name="name">2. Participation in equity of associates and joint ventures</field>
                                        <field name="code">RS_0026</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="30"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">041</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_3" model="account.report.line">
                                        <field name="name">3. Participation in equity in other legal entities and other securities for sale</field>
                                        <field name="code">RS_0027</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="31"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">042</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_4" model="account.report.line">
                                        <field name="name">4. Long-term investments in parent companies and subsidiaries</field>
                                        <field name="code">RS_0028</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="32"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">0440 + 0430</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_5" model="account.report.line">
                                        <field name="name">5. Long-term investments in other associated legal entities</field>
                                        <field name="code">RS_0029</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="33"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">0431 + 0441</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_6" model="account.report.line">
                                        <field name="name">6. Long-term investments - domestic</field>
                                        <field name="code">RS_0030</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="34"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">0450</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_7" model="account.report.line">
                                        <field name="name">7. Long-term investments - foreign</field>
                                        <field name="code">RS_0031</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="35"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">0451</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_8" model="account.report.line">
                                        <field name="name">8. Securities held to maturity</field>
                                        <field name="code">RS_0032</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="36"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">046</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_9" model="account.report.line">
                                        <field name="name">9. Other long-term financial investments</field>
                                        <field name="code">RS_0033</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="37"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">048</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_IV_10" model="account.report.line">
                                        <field name="name">10. Provisions for long-term financial investments</field>
                                        <field name="code">RS_Assets_B_IV_10</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="38"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_IV_10_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">049</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_B_V" model="account.report.line">
                                <field name="name">V. Long-term financial receivables</field>
                                <field name="code">RS_0034</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="39"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_V_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0035.balance + RS_0036.balance + RS_0037.balance + RS_0038.balance + RS_0039.balance + RS_0040.balance + RS_0041.balance + RS_Assets_B_V_8.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_B_V_1" model="account.report.line">
                                        <field name="name">1. Receivables from parent company and subsidiaries</field>
                                        <field name="code">RS_0035</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="40"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">050</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_2" model="account.report.line">
                                        <field name="name">2. Receivables from other associated companies</field>
                                        <field name="code">RS_0036</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="41"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">051</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_3" model="account.report.line">
                                        <field name="name">3. Receivables from credit sales</field>
                                        <field name="code">RS_0037</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="42"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">052</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_4" model="account.report.line">
                                        <field name="name">4. Receivables from sales made under financial leasing contracts</field>
                                        <field name="code">RS_0038</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="43"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">053</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_5" model="account.report.line">
                                        <field name="name">5. Receivables on sureties</field>
                                        <field name="code">RS_0039</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="44"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">054</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_6" model="account.report.line">
                                        <field name="name">6. Contested and doubtful receivables</field>
                                        <field name="code">RS_0040</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="45"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">055</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_7" model="account.report.line">
                                        <field name="name">7. Other long-term receivables</field>
                                        <field name="code">RS_0041</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="46"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">056</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_B_V_8" model="account.report.line">
                                        <field name="name">8. Provisions for long-term financial investments</field>
                                        <field name="code">RS_Assets_B_V_8</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="47"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_B_V_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">059</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_assets_C" model="account.report.line">
                        <field name="name">C. Deferred tax assets</field>
                        <field name="code">RS_0042</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="48"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_assets_C_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">288</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_assets_D" model="account.report.line">
                        <field name="name">D. Current assets (I + II + III + IV + V + VI + VII + VIII + IX)</field>
                        <field name="code">RS_0043</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="49"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_assets_D_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">RS_0044.balance + RS_0051.balance + RS_0059.balance + RS_0060.balance + RS_0061.balance + RS_0062.balance + RS_0068.balance + RS_0069.balance + RS_0070.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_rs_BS_assets_D_I" model="account.report.line">
                                <field name="name">I. Inventories</field>
                                <field name="code">RS_0044</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="50"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_I_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0045.balance + RS_0046.balance + RS_0047.balance + RS_0048.balance + RS_0049.balance + RS_0050.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_I_1" model="account.report.line">
                                        <field name="name">1. Materials, spare parts, tools and small inventory</field>
                                        <field name="code">RS_0045</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="51"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">10</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_I_2" model="account.report.line">
                                        <field name="name">2. Work and services in progress</field>
                                        <field name="code">RS_0046</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="52"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">11</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_I_3" model="account.report.line">
                                        <field name="name">3. Finished products</field>
                                        <field name="code">RS_0047</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="53"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">12</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_I_4" model="account.report.line">
                                        <field name="name">4. Goods</field>
                                        <field name="code">RS_0048</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="54"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">13</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_I_5" model="account.report.line">
                                        <field name="name">5. Permanent assets held for sale</field>
                                        <field name="code">RS_0049</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="55"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">14</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_I_6" model="account.report.line">
                                        <field name="name">6. Advances paid for inventories and services</field>
                                        <field name="code">RS_0050</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="56"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_I_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">15</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_II" model="account.report.line">
                                <field name="name">II. Receivables from sales</field>
                                <field name="code">RS_0051</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="57"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_II_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0052.balance + RS_0053.balance + RS_0054.balance + RS_0055.balance + RS_0056.balance + RS_0057.balance + RS_0058.balance + RS_D_II_8.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_II_1" model="account.report.line">
                                        <field name="name">1. Domestic trade receivables - parent companies and subsidiaries</field>
                                        <field name="code">RS_0052</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="58"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">200</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_2" model="account.report.line">
                                        <field name="name">2. Foreign trade receivables - parent companies and subsidiaries</field>
                                        <field name="code">RS_0053</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="59"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">201</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_3" model="account.report.line">
                                        <field name="name">3. Domestic trade receivables - other associated companies</field>
                                        <field name="code">RS_0054</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="60"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">202</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_4" model="account.report.line">
                                        <field name="name">4. Foreign trade receivables - other associated companies</field>
                                        <field name="code">RS_0055</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="61"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">203</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_5" model="account.report.line">
                                        <field name="name">5. Trade receivables - domestic</field>
                                        <field name="code">RS_0056</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="62"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">204</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_6" model="account.report.line">
                                        <field name="name">6. Trade receivables - foreign</field>
                                        <field name="code">RS_0057</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="63"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">205</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_7" model="account.report.line">
                                        <field name="name">7. Other receivables from sales</field>
                                        <field name="code">RS_0058</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="64"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">206</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_II_8" model="account.report.line">
                                        <field name="name">8. Provisions for trade receivables</field>
                                        <field name="code">RS_D_II_8</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="65"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_II_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">209</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_III" model="account.report.line">
                                <field name="name">III. Receivables from specific business operations</field>
                                <field name="code">RS_0059</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="66"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_III_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">21</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_IV" model="account.report.line">
                                <field name="name">IV. Other receivables</field>
                                <field name="code">RS_0060</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="67"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_IV_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">22</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_V" model="account.report.line">
                                <field name="name">V. Financial assets at fair value through profit and loss account</field>
                                <field name="code">RS_0061</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="68"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_V_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">236</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_VI" model="account.report.line">
                                <field name="name">VI. Short-term financial investments</field>
                                <field name="code">RS_0062</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="69"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_VI_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0063.balance + RS_0064.balance + RS_0065.balance + RS_0066.balance + RS_0067.balance + RS_D_VI_6.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_VI_1" model="account.report.line">
                                        <field name="name">1. Short-term loans and investments in parent companies and subsidiaries</field>
                                        <field name="code">RS_0063</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="70"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">230</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_VI_2" model="account.report.line">
                                        <field name="name">2. Short-term loans and investments in other associated companies</field>
                                        <field name="code">RS_0064</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="71"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">231</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_VI_3" model="account.report.line">
                                        <field name="name">3. Short-term loans - domestic</field>
                                        <field name="code">RS_0065</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="72"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">232</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_VI_4" model="account.report.line">
                                        <field name="name">4. Short-term loans - foreign</field>
                                        <field name="code">RS_0066</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="73"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">233</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_VI_5" model="account.report.line">
                                        <field name="name">5. Other short-term financial investments</field>
                                        <field name="code">RS_0067</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="74"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">235 + 238 + 234</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_assets_D_VI_6" model="account.report.line">
                                        <field name="name">6. Provisions for short–term investments</field>
                                        <field name="code">RS_D_VI_6</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="75"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_assets_D_VI_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">239</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_VII" model="account.report.line">
                                <field name="name">VII. Cash and cash equivalents</field>
                                <field name="code">RS_0068</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="76"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_VII_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">25 + 24</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_VIII" model="account.report.line">
                                <field name="name">VIII. Value added tax</field>
                                <field name="code">RS_0069</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="77"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_VIII_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">27</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_assets_D_IX" model="account.report.line">
                                <field name="name">IX. Accrued expenses</field>
                                <field name="code">RS_0070</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="78"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_assets_D_IX_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">28\(288)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_rs_BS_equity_liabilities" model="account.report.line">
                <field name="name">Equity and liabilities (A + B + C + D - E)</field>
                <field name="code">RS_0464</field>
                <field name="hierarchy_level" eval="0"/>
                <field name="sequence" eval="79"/>
                <field name="expression_ids">
                    <record id="account_financial_report_rs_BS_equity_liabilities_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">RS_0424.balance + RS_0442.balance + RS_0441.balance + RS_0401.balance - RS_0463.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_rs_BS_equity_liabilities_A" model="account.report.line">
                        <field name="name">A. Equity (I + II - III + IV + V + VI - VII + VIII + IX - X)</field>
                        <field name="code">RS_0401</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="80"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">(RS_0402.balance + RS_0411.balance + RS_0413.balance + RS_0414.balance + RS_0415.balance + RS_0417.balance + RS_0420.balance) - (RS_0412.balance + RS_0416.balance + RS_0421.balance)</field>
                                <field name="subformula">if_above(RSD(0))</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I" model="account.report.line">
                                <field name="name">I. Capital</field>
                                <field name="code">RS_0402</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="81"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0403.balance + RS_0404.balance + RS_0405.balance + RS_0406.balance + RS_0407.balance + RS_0408.balance + RS_0409.balance + RS_0410.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_1" model="account.report.line">
                                        <field name="name">1. Share capital</field>
                                        <field name="code">RS_0403</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="82"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-300</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_2" model="account.report.line">
                                        <field name="name">2. Stakes in limited liability companies</field>
                                        <field name="code">RS_0404</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="83"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-301</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_3" model="account.report.line">
                                        <field name="name">3. Participating interests</field>
                                        <field name="code">RS_0405</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="84"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-302</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_4" model="account.report.line">
                                        <field name="name">4. State owned capital</field>
                                        <field name="code">RS_0406</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="85"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-303</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_5" model="account.report.line">
                                        <field name="name">5. Socially owned capital</field>
                                        <field name="code">RS_0407</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="86"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-304</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_6" model="account.report.line">
                                        <field name="name">6. Stakes in cooperatives</field>
                                        <field name="code">RS_0408</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="87"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-305</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_7" model="account.report.line">
                                        <field name="name">7. Share premium</field>
                                        <field name="code">RS_0409</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="88"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-306</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_I_8" model="account.report.line">
                                        <field name="name">8. Other capital</field>
                                        <field name="code">RS_0410</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="89"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_I_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-309</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_II" model="account.report.line">
                                <field name="name">II. Subscribed capital unpaid</field>
                                <field name="code">RS_0411</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="90"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_II_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-31</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_III" model="account.report.line">
                                <field name="name">III. Treasury shares</field>
                                <field name="code">RS_0412</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="91"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_III_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">237 + 047</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_IV" model="account.report.line">
                                <field name="name">IV. Reserves</field>
                                <field name="code">RS_0413</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="92"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_IV_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-32</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_V" model="account.report.line">
                                <field name="name">V. Revaluation reserves from intangible assets, immovables, plants and equipment</field>
                                <field name="code">RS_0414</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="93"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_V_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-330</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VI" model="account.report.line">
                                <field name="name">VI. Unrealized profits from securities and other elements of other comprehensive income (credit balance accounts of group 33 except 330)</field>
                                <field name="code">RS_0415</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="94"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_VI_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">domain</field>
                                        <field name="formula">[('credit', '!=', 0.0),('account_id.code', '=like', '33%'), '!', ('account_id.code', '=like', '330%')]</field>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VII" model="account.report.line">
                                <field name="name">VII. Unrealized losses from securities and other elements of other comprehensive income (debit balance accounts of group 33 except 330)</field>
                                <field name="code">RS_0416</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="95"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_VII_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">domain</field>
                                        <field name="formula">[('debit', '!=', 0.0),('account_id.code', '=like', '33%'), '!', ('account_id.code', '=like', '330%')]</field>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII" model="account.report.line">
                                <field name="name">VIII. Retained earnings</field>
                                <field name="code">RS_0417</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="96"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0418.balance + RS_0419.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_1" model="account.report.line">
                                        <field name="name">1. Retained earnings from previous years</field>
                                        <field name="code">RS_0418</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="97"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_1_cross_report" model="account.report.expression">
                                                <field name="label">cross_report</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">RS_1064.balance</field>
                                                <field name="subformula">cross_report</field>
                                                <field name="date_scope">to_beginning_of_fiscalyear</field>
                                            </record>
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_1_accounts" model="account.report.expression">
                                                <field name="label">accounts</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-340 - 341 - 350 - 351</field>
                                            </record>
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_1_balance_signed" model="account.report.expression">
                                                <field name="label">balance_signed</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">RS_0418.cross_report + RS_0418.accounts</field>
                                            </record>
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">RS_0418.balance_signed</field>
                                                <field name="subformula">if_above(RSD(0))</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_2" model="account.report.line">
                                        <field name="name">2. Retained earnings for the current year</field>
                                        <field name="code">RS_0419</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="98"/>
                                        <field name="action_id" ref="action_account_report_rs_PnL"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_2_cross_report" model="account.report.expression">
                                                <field name="label">balance_signed</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">RS_1064.balance</field>
                                                <field name="subformula">cross_report</field>
                                                <field name="date_scope">from_fiscalyear</field>
                                            </record>
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_VIII_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">RS_0419.balance_signed</field>
                                                <field name="subformula">if_above(RSD(0))</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_IX" model="account.report.line">
                                <field name="name">IX. Participation without control rights</field>
                                <field name="code">RS_0420</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="99"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_IX_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_A_X" model="account.report.line">
                                <field name="name">X. Loss</field>
                                <field name="code">RS_0421</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="100"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_X_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0422.balance + RS_0423.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_X_1" model="account.report.line">
                                        <field name="name">1. Loss from previous years</field>
                                        <field name="code">RS_0422</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="101"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_X_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">-RS_0418.balance_signed</field>
                                                <field name="subformula">if_above(RSD(0))</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_A_X_2" model="account.report.line">
                                        <field name="name">2. Loss for the current year</field>
                                        <field name="code">RS_0423</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="102"/>
                                        <field name="action_id" ref="action_account_report_rs_PnL"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_A_X_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">-RS_0419.balance_signed</field>
                                                <field name="subformula">if_above(RSD(0))</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_equity_liabilities_B" model="account.report.line">
                        <field name="name">B. Long-term provisions and liabilities (I + II)</field>
                        <field name="code">RS_0424</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="103"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_B_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">RS_0425.balance + RS_0432.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I" model="account.report.line">
                                <field name="name">I. Long-term provisions</field>
                                <field name="code">RS_0425</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="104"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0426.balance + RS_0427.balance + RS_0428.balance + RS_0429.balance + RS_0430.balance + RS_0431.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_1" model="account.report.line">
                                        <field name="name">1. Provisions for costs incurred during the warranty period</field>
                                        <field name="code">RS_0426</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="105"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-400</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_2" model="account.report.line">
                                        <field name="name">2. Provisions for the recovery of natural resources</field>
                                        <field name="code">RS_0427</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="106"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-401</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_3" model="account.report.line">
                                        <field name="name">3. Provisions for restructuring costs</field>
                                        <field name="code">RS_0428</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="107"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-403</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_4" model="account.report.line">
                                        <field name="name">4. Provisions for compensations and other employment benefits</field>
                                        <field name="code">RS_0429</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="108"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-404</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_5" model="account.report.line">
                                        <field name="name">5. Provisions for litigation expenses</field>
                                        <field name="code">RS_0430</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="109"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-405</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_I_6" model="account.report.line">
                                        <field name="name">6. Other long-term provisions</field>
                                        <field name="code">RS_0431</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="110"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_I_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-402 - 409</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II" model="account.report.line">
                                <field name="name">II. Long-term liabilities</field>
                                <field name="code">RS_0432</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="111"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0433.balance + RS_0434.balance + RS_0435.balance + RS_0436.balance + RS_0437.balance + RS_0438.balance + RS_0439.balance + RS_0440.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_1" model="account.report.line">
                                        <field name="name">1. Debts convertible into equity</field>
                                        <field name="code">RS_0433</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="112"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-410</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_2" model="account.report.line">
                                        <field name="name">2. Liabilities to parent companies and subsidiaries</field>
                                        <field name="code">RS_0434</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="113"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-411</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_3" model="account.report.line">
                                        <field name="name">3. Liabilities to other associated companies</field>
                                        <field name="code">RS_0435</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="114"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-412</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_4" model="account.report.line">
                                        <field name="name">4. Liabilities for long-term securities</field>
                                        <field name="code">RS_0436</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="115"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-413</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_5" model="account.report.line">
                                        <field name="name">5. Long-term loans - domestic</field>
                                        <field name="code">RS_0437</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="116"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-414</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_6" model="account.report.line">
                                        <field name="name">6. Long-term loans - foreign</field>
                                        <field name="code">RS_0438</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="117"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-415</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_7" model="account.report.line">
                                        <field name="name">7. Financial leasing liabilities</field>
                                        <field name="code">RS_0439</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="118"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-416</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_B_II_8" model="account.report.line">
                                        <field name="name">8. Other long-term liabilities</field>
                                        <field name="code">RS_0440</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="119"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_B_II_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-419</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_equity_liabilities_C" model="account.report.line">
                        <field name="name">C. Deferred tax liabilities</field>
                        <field name="code">RS_0441</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="120"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_C_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-498</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_equity_liabilities_D" model="account.report.line">
                        <field name="name">D. Short-term liabilities (I + II + III + IV + V + VI + VII)</field>
                        <field name="code">RS_0442</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="121"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">RS_0443.balance + RS_0450.balance + RS_0451.balance + RS_0459.balance + RS_0460.balance + RS_0461.balance + RS_0462.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I" model="account.report.line">
                                <field name="name">I. Short-term financial liabilities</field>
                                <field name="code">RS_0443</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="122"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0444.balance + RS_0445.balance + RS_0446.balance + RS_0447.balance + RS_0448.balance + RS_0449.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_1" model="account.report.line">
                                        <field name="name">1. Short-term loans from parent company and subsidiaries</field>
                                        <field name="code">RS_0444</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="123"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-420</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_2" model="account.report.line">
                                        <field name="name">2. Short-term loans from other associated companies</field>
                                        <field name="code">RS_0445</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="124"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-421</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_3" model="account.report.line">
                                        <field name="name">3. Short-term loans - domestic</field>
                                        <field name="code">RS_0446</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="125"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-422</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_4" model="account.report.line">
                                        <field name="name">4. Short-term loans - foreign</field>
                                        <field name="code">RS_0447</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="126"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-423</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_5" model="account.report.line">
                                        <field name="name">5. Liabilities for permanent assets and assets of discontinued operations held for sale</field>
                                        <field name="code">RS_0448</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="127"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-427</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_I_6" model="account.report.line">
                                        <field name="name">6. Other short-term financial liabilities</field>
                                        <field name="code">RS_0449</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="128"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_I_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-425 - 429 - 426 - 424</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_II" model="account.report.line">
                                <field name="name">II. Prepayments, deposits and guarantees</field>
                                <field name="code">RS_0450</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="129"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_II_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-430</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III" model="account.report.line">
                                <field name="name">III. Operating liabilities</field>
                                <field name="code">RS_0451</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="130"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">RS_0452.balance + RS_0453.balance + RS_0454.balance + RS_0455.balance + RS_0456.balance + RS_0457.balance + RS_0458.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_1" model="account.report.line">
                                        <field name="name">1. Trade payables - domestic parent company and subsidiaries</field>
                                        <field name="code">RS_0452</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="131"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-431</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_2" model="account.report.line">
                                        <field name="name">2. Trade payables - foreign parent company and subsidiaries</field>
                                        <field name="code">RS_0453</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="132"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-432</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_3" model="account.report.line">
                                        <field name="name">3. Trade payables - other domestic associated companies</field>
                                        <field name="code">RS_0454</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="133"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-433</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_4" model="account.report.line">
                                        <field name="name">4. Trade payables - other foreign associated companies</field>
                                        <field name="code">RS_0455</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="134"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-434</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_5" model="account.report.line">
                                        <field name="name">5. Trade payables - domestic</field>
                                        <field name="code">RS_0456</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="135"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-435</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_6" model="account.report.line">
                                        <field name="name">6. Trade payables - foreign</field>
                                        <field name="code">RS_0457</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="136"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-436</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_III_7" model="account.report.line">
                                        <field name="name">7. Other operating liabilities</field>
                                        <field name="code">RS_0458</field>
                                        <field name="hierarchy_level" eval="3"/>
                                        <field name="sequence" eval="137"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_rs_BS_equity_liabilities_D_III_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-439</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_IV" model="account.report.line">
                                <field name="name">IV. Other short-term liabilities</field>
                                <field name="code">RS_0459</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="138"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_IV_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-44 - 45 - 46</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_V" model="account.report.line">
                                <field name="name">V. Liabilities for value added tax</field>
                                <field name="code">RS_0460</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="139"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_V_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-47</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_VI" model="account.report.line">
                                <field name="name">VI. Liabilities for other taxes, contributions and other duties</field>
                                <field name="code">RS_0461</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="140"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_VI_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-48</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_rs_BS_equity_liabilities_D_VII" model="account.report.line">
                                <field name="name">VII. Deferred expenses</field>
                                <field name="code">RS_0462</field>
                                <field name="hierarchy_level" eval="2"/>
                                <field name="sequence" eval="141"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_rs_BS_equity_liabilities_D_VII_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-49\(498)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_equity_liabilities_E" model="account.report.line">
                        <field name="name">E. Loss above equity (A.III + A.VII + A.X - A.IX - A.VIII - A.VI - A.V - A.IV - A.II - A.I)</field>
                        <field name="code">RS_0463</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="142"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_equity_liabilities_E_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">(RS_0412.balance + RS_0416.balance + RS_0421.balance) - (RS_0402.balance + RS_0411.balance + RS_0413.balance + RS_0414.balance + RS_0415.balance + RS_0417.balance + RS_0420.balance)</field>
                                <field name="subformula">if_above(RSD(0))</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_rs_BS_off_balance" model="account.report.line">
                <field name="name">Off-balance items</field>
                <field name="hierarchy_level" eval="0"/>
                <field name="sequence" eval="143"/>
                <field name="children_ids">
                    <record id="account_financial_report_rs_BS_off_balance_A" model="account.report.line">
                        <field name="name">A. Off-balance sheet assets</field>
                        <field name="code">RS_0072</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="144"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_off_balance_A_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">88</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_rs_BS_off_balance_B" model="account.report.line">
                        <field name="name">B. Off-balance sheet liabilities</field>
                        <field name="code">RS_0465</field>
                        <field name="hierarchy_level" eval="1"/>
                        <field name="sequence" eval="145"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_rs_BS_off_balance_B_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-89</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
