<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="ir_rule_sms_template_stock_manager" model="ir.rule">
        <field name="name">SMS Template: stock manager CUD on stock picking templates</field>
        <field name="model_id" ref="sms.model_sms_template"/>
        <field name="groups" eval="[(4, ref('stock.group_stock_manager'))]"/>
        <field name="domain_force">[('model_id.model', '=', 'stock.picking')]</field>
        <field name="perm_read" eval="False"/>
    </record>

</odoo>
