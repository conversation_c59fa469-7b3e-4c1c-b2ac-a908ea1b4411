<?xml version="1.0"?>
<odoo>
    <record model="ir.ui.view" id="view_hr_referral_alert_form">
        <field name="name">hr.referral.alert.form</field>
        <field name="model">hr.referral.alert</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="%(hr_referral_alert_mail_wizard_action)d" type="action"
                        string="Send Mail"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group>
                        <group name="duration_details">
                            <field name="date_from"/>
                            <field name="active" invisible="1"/>
                            <field name="date_to"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group name="alert_type">
                            <field name="onclick" widget="radio"/>
                            <field name="url" attrs="{'invisible': [('onclick', '!=', 'url')], 'required': [('onclick', '=', 'url')]}" placeholder="https://www.google.com"/>
                        </group>
                    </group>
                    <group col="1">
                        <group name="name_details">
                            <field name="name"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_hr_referral_alert_tree">
        <field name="name">hr.referral.alert.tree</field>
        <field name="model">hr.referral.alert</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_from"/>
                <field name="date_to"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_referral_alert_view_search">
        <field name="name">hr.referral.alert.view.search</field>
        <field name="model">hr.referral.alert</field>
        <field name="arch" type="xml">
            <search string="HR Referral Alert Search">
                <field name="name"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_referral_alert_configuration">
        <field name="name">Alerts</field>
        <field name="res_model">hr.referral.alert</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="hr_referral_alert_view_search"/>
        <field name="groups_id" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create new alerts
            </p>
        </field>
    </record>

    <menuitem parent="menu_hr_referral_configuration" id="menu_hr_referral_alert_configuration" action="action_hr_referral_alert_configuration" sequence="4" groups="hr_recruitment.group_hr_recruitment_manager"/>


</odoo>
