# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "By using our Social YouTube Application, you implicitly agree to the:"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr ""

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Clear"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Confirmation"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Do you also want to remove the video from your YouTube account?"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Draft Video"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube____last_update
msgid "Last Modified on"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Likes"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "No"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
#, python-format
msgid "Please select a single YouTube account at a time."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Processing..."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Reason:"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoke Account"
msgstr ""

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Select"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr ""

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "There is no channel linked with this YouTube account."
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid "Unknown"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Upload Video"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Upload failed. Please try again."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading %s"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading... %s%%"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "Video"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Video Upload"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "Video not found. It could have been removed from Youtube."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Yes, delete it"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "You have to upload a video when posting on YouTube."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
msgid "YouTube"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "YouTube Comments"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_ratio
msgid "YouTube Likes Ratio"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placehdoler"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid authorization code."
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "Your video is missing a correct title or description."
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr ""

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr ""
