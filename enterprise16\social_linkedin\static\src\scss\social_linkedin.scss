.o_social_linkedin_preview {
    .o_social_preview_icon_wrapper {
        background-color: #0274B3;
        .fa {
            transform: translate(0.3em, 0.2em);
        }
    }

    .o_social_preview_author {
        line-height: 1.2;
        b {
            color: #0274B3;
        }
        small{
            line-height: 1;
        }
    }
    .o_social_preview_description {
        font-size: 0.75em;
    }
}

.o_social_comments_modal_linkedin {
    a:not(.btn-outline-secondary):not(.o_social_comment_published_date):not(.o_social_post_published_date):not(.dropdown-item) {
        color: #0274B3;

        &:hover, &:focus {
            color: #0274B3;
            text-decoration: underline;
        }
    }
}
