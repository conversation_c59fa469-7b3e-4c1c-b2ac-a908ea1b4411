# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"Externe link\""
" title=\"Externe link\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Alle"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Alle landen"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Soort lidmaatschap"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "Sluiten"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Vind een zakenpartner"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "Free Members"
msgstr "Gratis leden"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Location"
msgstr "Locatie"

#. module: website_membership
#: code:addons/website_membership/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_membership.index
#, python-format
msgid "Members"
msgstr "Leden"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Members Page"
msgstr "Ledenpagina"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Lidmaatschapregel"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "Geen resultaten gevonden."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Onze ledenlijst"

#. module: website_membership
#: model:ir.model,name:website_membership.model_website
msgid "Website"
msgstr "Website"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "World Map"
msgstr "Wereldkaart"
