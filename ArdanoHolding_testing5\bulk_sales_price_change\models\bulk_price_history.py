from odoo import models, fields, api

class BulkPriceHistory(models.Model):
    _name = 'bulk.price.history'
    _description = 'Bulk Price Update History'
    _order = 'date desc'

    name = fields.Char(string='Reference', required=True, copy=False, readonly=True, default='New')
    date = fields.Datetime(string='Update Date', default=fields.Datetime.now, required=True)
    user_id = fields.Many2one('res.users', string='Updated By', default=lambda self: self.env.user, required=True)
    adjustment_type = fields.Selection([
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount')
    ], string='Adjustment Type', required=True)
    adjustment_value = fields.Float(string='Adjustment Value', required=True)
    operation_type = fields.Selection([
        ('increase', 'Increase'),
        ('decrease', 'Decrease')
    ], string='Operation Type', required=True)
    category_ids = fields.Many2many('product.category', string='Product Categories')
    tag_ids = fields.Many2many('product.tag', string='Product Tags')
    line_ids = fields.One2many('bulk.price.history.line', 'history_id', string='Price Update Lines')
    total_products = fields.Integer(string='Total Products Updated', compute='_compute_totals', store=True)
    
    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('bulk.price.history') or 'New'
        return super().create(vals_list)

    @api.depends('line_ids')
    def _compute_totals(self):
        for record in self:
            record.total_products = len(record.line_ids)


class BulkPriceHistoryLine(models.Model):
    _name = 'bulk.price.history.line'
    _description = 'Bulk Price Update History Line'

    history_id = fields.Many2one('bulk.price.history', string='Price Update History', required=True, ondelete='cascade')
    product_id = fields.Many2one('product.product', string='Product', required=True)
    old_price = fields.Float(string='Old Price', digits='Product Price', required=True)
    new_price = fields.Float(string='New Price', digits='Product Price', required=True)
    price_difference = fields.Float(string='Price Difference', compute='_compute_price_difference', store=True)

    @api.depends('old_price', 'new_price')
    def _compute_price_difference(self):
        for record in self:
            record.price_difference = record.new_price - record.old_price 