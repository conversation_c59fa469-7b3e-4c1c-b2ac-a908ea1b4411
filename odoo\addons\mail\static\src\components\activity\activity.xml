<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.Activity" owl="1">
        <t t-if="activityView">
            <div class="o_Activity d-flex py-2 px-3" t-attf-class="{{ className }}" t-on-click="activityView.onClickActivity" t-ref="root">
                <div class="o_Activity_sidebar me-3">
                    <div class="o_Activity_user position-relative h-100 w-100">
                        <t t-if="activityView.activity.assignee">
                            <img class="o_Activity_userAvatar rounded-circle h-100 w-100 o_object_fit_cover" t-attf-src="/web/image/res.users/{{ activityView.activity.assignee.id }}/avatar_128" t-att-alt="activityView.activity.assignee.nameOrDisplayName"/>
                        </t>
                        <div class="o_Activity_iconContainer d-flex align-items-center justify-content-center rounded-circle w-50 h-50"
                            t-att-class="{
                                'text-bg-success': activityView.activity.state === 'planned',
                                'text-bg-warning': activityView.activity.state === 'today',
                                'text-bg-danger': activityView.activity.state === 'overdue',
                            }"
                        >
                            <i class="o_Activity_icon fa small" t-attf-class="{{ activityView.activity.icon }}"/>
                        </div>
                    </div>
                </div>
                <div class="o_Activity_core">
                    <div class="o_Activity_info d-flex align-items-baseline">
                        <div class="o_Activity_dueDateText me-2"
                            t-att-class="{
                                'text-danger': activityView.activity.state === 'overdue',
                                'text-success': activityView.activity.state === 'planned',
                                'text-warning': activityView.activity.state === 'today',
                            }"
                        >
                            <b t-esc="activityView.delayLabel"/>
                        </div>
                        <t t-if="activityView.activity.summary">
                            <b class="o_Activity_summary text-900 me-2">
                                <t t-esc="activityView.summary"/>
                            </b>
                        </t>
                        <t t-elif="activityView.activity.type">
                            <b class="o_Activity_summary o_Activity_type text-900 me-2">
                                <t t-esc="activityView.activity.type.displayName"/>
                            </b>
                        </t>
                        <t t-if="activityView.activity.assignee">
                            <div class="o_Activity_userName">
                                <t t-esc="activityView.assignedUserText"/>
                            </div>
                        </t>
                        <a
                            href="#"
                            class="o_Activity_detailsButton btn py-0"
                            t-att-class="activityView.areDetailsVisible ? 'text-primary' : 'btn-link btn-primary'"
                            t-att-aria-expanded="activityView.areDetailsVisible ? 'true' : 'false'"
                            t-on-click="activityView.onClickDetailsButton"
                            role="button"
                        >
                            <i class="fa fa-info-circle" role="img" title="Info" aria-label="Info"/>
                        </a>
                    </div>

                    <t t-if="activityView.areDetailsVisible">
                        <div class="o_Activity_details">
                            <div class="d-md-table table table-sm mt-2 mb-3">
                                <div t-if="activityView.activity.type" class="d-md-table-row mb-3">
                                    <div class="d-md-table-cell fw-bold text-md-end m-0 py-md-1 px-md-4">Activity type</div>
                                    <div class="o_Activity_type d-md-table-cell py-md-1 pe-4">
                                        <t t-esc="activityView.activity.type.displayName"/>
                                    </div>
                                </div>
                                <div t-if="activityView.activity.creator" class="d-md-table-row mb-3">
                                    <div class="d-md-table-cell fw-bold text-md-end m-0 py-md-1 px-md-4">Created</div>
                                    <div class="o_Activity_detailsCreation d-md-table-cell py-md-1 pe-4">
                                        <t t-esc="activityView.formattedCreateDatetime"/>, <br t-if="messaging.device.isSmall"/>by
                                        <img class="o_Activity_detailsUserAvatar o_Activity_detailsCreatorAvatar ms-1 me-1 rounded-circle align-text-bottom p-0" t-attf-src="/web/image/res.users/{{ activityView.activity.creator.id }}/avatar_128" t-att-title="activityView.activity.creator.nameOrDisplayName" t-att-alt="activityView.activity.creator.nameOrDisplayName"/>
                                        <b class="o_Activity_detailsCreator">
                                            <t t-esc="activityView.activity.creator.nameOrDisplayName"/>
                                        </b>
                                    </div>
                                </div>
                                <div t-if="activityView.activity.assignee" class="d-md-table-row mb-3">
                                    <div class="d-md-table-cell fw-bold text-md-end m-0 py-md-1 px-md-4">Assigned to</div>
                                    <div class="o_Activity_detailsAssignation d-md-table-cell py-md-1 pe-4">
                                        <img class="o_Activity_detailsUserAvatar o_Activity_detailsAssignationUserAvatar me-1 rounded-circle align-text-bottom p-0" t-attf-src="/web/image/res.users/{{ activityView.activity.assignee.id }}/avatar_128" t-att-title="activityView.activity.assignee.nameOrDisplayName" t-att-alt="activityView.activity.assignee.nameOrDisplayName"/>
                                        <b t-esc="activityView.activity.assignee.nameOrDisplayName"/>
                                    </div>
                                </div>
                                <div class="d-md-table-row">
                                    <div class="d-md-table-cell fw-bold text-md-end m-0 py-md-1 px-md-4">Due on</div>
                                    <div class="o_Activity_detailsDueDate d-md-table-cell py-md-1 pe-4">
                                        <span class="o_Activity_deadlineDateText"
                                            t-att-class="{
                                                'text-danger': activityView.activity.state === 'overdue',
                                                'text-success': activityView.activity.state === 'planned',
                                                'text-warning': activityView.activity.state === 'today',
                                            }"
                                        >
                                            <t t-esc="activityView.formattedDeadlineDate"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>

                    <t t-if="activityView.activity.note">
                        <div class="o_Activity_note">
                            <t t-out="activityView.activity.noteAsMarkup"/>
                        </div>
                    </t>

                    <t t-if="activityView.mailTemplateViews.length > 0">
                        <div class="o_Activity_mailTemplates">
                            <t t-foreach="activityView.mailTemplateViews" t-as="mailTemplateView" t-key="mailTemplateView.localId">
                                <MailTemplate
                                    className="'o_Activity_mailTemplate'"
                                    record="mailTemplateView"
                                />
                            </t>
                        </div>
                    </t>

                    <t t-if="activityView.activity.canWrite">
                        <div name="tools" class="o_Activity_tools d-flex">
                            <button class="o_Activity_toolButton o_Activity_markDoneButton btn btn-link btn-primary pt-0 ps-0" t-att-title="activityView.markDoneText" t-ref="markDoneButton" t-on-click="activityView.onClickMarkDoneButton">
                                <i class="fa fa-check"/> Mark Done
                            </button>
                            <t t-if="activityView.fileUploader">
                                <button class="o_Activity_toolButton o_Activity_uploadButton btn btn-link btn-primary pt-0 ps-0" t-on-click="activityView.onClickUploadDocument">
                                    <i class="fa fa-upload"/> Upload Document
                                </button>
                            </t>
                            <button class="o_Activity_toolButton o_Activity_editButton btn btn-link btn-primary pt-0" t-on-click="activityView.onClickEdit">
                                <i class="fa fa-pencil"/> Edit
                            </button>
                            <button class="o_Activity_toolButton o_Activity_cancelButton btn btn-link btn-primary pt-0" t-on-click="activityView.onClickCancel" >
                                <i class="fa fa-times"/> Cancel
                            </button>
                        </div>
                    </t>
                </div>
            </div>
        </t>
    </t>

</templates>
