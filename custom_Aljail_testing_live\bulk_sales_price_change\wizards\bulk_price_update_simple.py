from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BulkPriceUpdate(models.TransientModel):
    _name = 'bulk.price.update.wizard'
    _description = 'Bulk Price Update Wizard'

    adjustment_type = fields.Selection([
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount')
    ], string='Adjustment Type', required=True, default='percentage')
    
    adjustment_value = fields.Float(
        string='Adjustment Value', 
        required=True,
        help="For percentage: enter value between 0-100. For fixed amount: enter the amount to adjust."
    )
    
    operation_type = fields.Selection([
        ('increase', 'Increase'),
        ('decrease', 'Decrease')
    ], string='Operation Type', required=True, default='increase')
    
    rounding_method = fields.Selection([
        ('no_round', 'No Rounding'),
        ('round_up', 'Round Up'),
        ('round_down', 'Round Down'),
        ('round_half_up', 'Round to Nearest (0.5 → Up)'),
    ], string='Rounding Method', required=True, default='round_half_up')
    
    decimal_places = fields.Integer(
        string='Decimal Places',
        required=True,
        default=2,
        help="Number of decimal places to round to"
    )
    
    category_ids = fields.Many2many(
        'product.category',
        string='Product Categories',
        help="Select categories to filter products. Leave empty to apply to all categories."
    )
    
    tag_ids = fields.Many2many(
        'product.tag',
        string='Product Tags',
        help="Select tags to filter products. Leave empty to apply to all tagged products."
    )
    
    # Preview fields - always visible, populated when preview is clicked
    preview_line_ids = fields.One2many(
        'bulk.price.update.preview.line',
        'wizard_id',
        string='Preview Lines',
        readonly=True
    )

    show_preview = fields.Boolean(string='Show Preview', default=False)

    def _round_price(self, price):
        """Round price based on selected rounding method and decimal places."""
        if self.rounding_method == 'no_round':
            return price
        
        precision = self.decimal_places
        if self.rounding_method == 'round_up':
            return float(f"{price:.{precision}f}")
        elif self.rounding_method == 'round_down':
            # Round down by truncating
            factor = 10 ** precision
            return float(int(price * factor) / factor)
        else:  # round_half_up
            return round(price, precision)

    def _calculate_new_price(self, old_price):
        if self.adjustment_type == 'percentage':
            adjustment = old_price * (self.adjustment_value / 100)
        else:  # fixed
            adjustment = self.adjustment_value

        if self.operation_type == 'increase':
            new_price = old_price + adjustment
        else:  # decrease
            new_price = old_price - adjustment

        # First ensure price is not negative
        new_price = max(0, new_price)
        
        # Then apply rounding
        return self._round_price(new_price)

    def action_preview(self):
        # Validate required fields for preview
        if self.adjustment_value <= 0:
            raise UserError(_("Adjustment value must be greater than 0."))
        if self.adjustment_type == 'percentage' and self.adjustment_value > 100:
            raise UserError(_("Percentage adjustment cannot exceed 100%."))

        # Generate preview and show it
        self._generate_preview()

        # Use write to ensure the changes are persisted
        self.write({'show_preview': True})

        # Return a simple reload action
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def _generate_preview(self):
        # Clear existing preview lines using write command
        self.write({'preview_line_ids': [(5, 0, 0)]})

        products = self._get_products()
        preview_lines = []

        for product in products:
            old_price = product.lst_price
            new_price = self._calculate_new_price(old_price)

            if new_price < 0:
                continue

            preview_lines.append((0, 0, {
                'product_id': product.id,
                'old_price': old_price,
                'new_price': new_price,
            }))

        # Write the preview lines
        if preview_lines:
            self.write({'preview_line_ids': preview_lines})

    def _get_products(self):
        domain = [('sale_ok', '=', True)]

        if self.category_ids:
            domain.append(('categ_id', 'in', self.category_ids.ids))
        if self.tag_ids:
            domain.append(('tag_ids', 'in', self.tag_ids.ids))

        products = self.env['product.product'].search(domain, limit=50)  # Limit for testing
        return products

    def action_apply(self):
        # Validate required fields for apply
        if self.adjustment_value <= 0:
            raise UserError(_("Adjustment value must be greater than 0."))
        if self.adjustment_type == 'percentage' and self.adjustment_value > 100:
            raise UserError(_("Percentage adjustment cannot exceed 100%."))
            
        if not self.preview_line_ids:
            raise UserError(_("No products found matching the selected criteria. Please click Preview first."))

        history_vals = {
            'adjustment_type': self.adjustment_type,
            'adjustment_value': self.adjustment_value,
            'operation_type': self.operation_type,
            'category_ids': [(6, 0, self.category_ids.ids)],
            'tag_ids': [(6, 0, self.tag_ids.ids)],
            'line_ids': [],
        }

        for line in self.preview_line_ids:
            product = line.product_id
            product.lst_price = line.new_price
            history_vals['line_ids'].append((0, 0, {
                'product_id': product.id,
                'old_price': line.old_price,
                'new_price': line.new_price,
            }))

        self.env['bulk.price.history'].create(history_vals)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Prices have been updated successfully!'),
                'type': 'success',
                'sticky': False,
            }
        }


class BulkPriceUpdatePreviewLine(models.TransientModel):
    _name = 'bulk.price.update.preview.line'
    _description = 'Bulk Price Update Preview Line'

    wizard_id = fields.Many2one('bulk.price.update.wizard', string='Wizard')
    product_id = fields.Many2one('product.product', string='Product', required=True)
    old_price = fields.Float(string='Current Price', digits='Product Price')
    new_price = fields.Float(string='New Price', digits='Product Price')
    price_difference = fields.Float(string='Difference', compute='_compute_price_difference')

    @api.depends('old_price', 'new_price')
    def _compute_price_difference(self):
        for record in self:
            record.price_difference = record.new_price - record.old_price
