<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_bulk_price_history_tree" model="ir.ui.view">
        <field name="name">bulk.price.history.tree</field>
        <field name="model">bulk.price.history</field>
        <field name="arch" type="xml">
            <tree string="Price Update History">
                <field name="name"/>
                <field name="date"/>
                <field name="user_id"/>
                <field name="adjustment_type"/>
                <field name="adjustment_value"/>
                <field name="operation_type"/>
                <field name="total_products"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_bulk_price_history_form" model="ir.ui.view">
        <field name="name">bulk.price.history.form</field>
        <field name="model">bulk.price.history</field>
        <field name="arch" type="xml">
            <form string="Price Update History">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="user_id"/>
                            <field name="total_products"/>
                        </group>
                        <group>
                            <field name="adjustment_type"/>
                            <field name="adjustment_value"/>
                            <field name="operation_type"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Products">
                            <field name="line_ids" readonly="1">
                                <tree>
                                    <field name="product_id"/>
                                    <field name="old_price"/>
                                    <field name="new_price"/>
                                    <field name="price_difference"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Filters Applied">
                            <group>
                                <field name="category_ids" widget="many2many_tags"/>
                                <field name="tag_ids" widget="many2many_tags"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_bulk_price_history_search" model="ir.ui.view">
        <field name="name">bulk.price.history.search</field>
        <field name="model">bulk.price.history</field>
        <field name="arch" type="xml">
            <search string="Search Price Update History">
                <field name="name"/>
                <field name="user_id"/>
                <field name="category_ids"/>
                <field name="tag_ids"/>
                <filter string="My Updates" name="my_updates" domain="[('user_id', '=', uid)]"/>
                <filter string="Today" name="today" domain="[('date', '>=', context_today().strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="group_by_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Adjustment Type" name="group_by_type" context="{'group_by': 'adjustment_type'}"/>
                    <filter string="Operation" name="group_by_operation" context="{'group_by': 'operation_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_bulk_price_history" model="ir.actions.act_window">
        <field name="name">Price Update History</field>
        <field name="res_model">bulk.price.history</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_updates': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No price updates yet!
            </p>
            <p>
                Use the bulk price update wizard to update product prices.
            </p>
        </field>
    </record>
</odoo>
