<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.social.facebook</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="social.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('o_social_developer_settings')]" position="inside">
                <div class="col-md-6 o_setting_box">
                    <div class="o_setting_left_pane">
                        <field name="facebook_use_own_account"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="facebook_use_own_account" class="oe_inline o_form_label" string="Facebook Developer Account"/>
                        <div class="content-group" attrs="{'invisible': [('facebook_use_own_account', '=', False)]}">
                            <div class="mt16">
                                <label for="facebook_app_id" string="App ID" class="col-3 col-lg-3 o_form_label"/>
                                <field name="facebook_app_id" class="oe_inline"/>
                            </div>
                            <div class="mt16">
                                <label for="facebook_client_secret" string="App Secret" class="col-3 col-lg-3 o_form_label"/>
                                <field name="facebook_client_secret" password="True" class="oe_inline"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
