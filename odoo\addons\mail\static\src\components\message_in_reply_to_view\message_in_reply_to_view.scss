.o_MessageInReplyToView_corner {
    &.o-isLeftAlign {
        left: $o-mail-message-sidebar-width / 2;
        border-radius: $o-mail-rounded-rectangle-border-radius-sm 0 0 0;
    }
    
    &.o-isRightAlign {
        right: $o-mail-message-sidebar-width / 2;
        border-radius: 0 $o-mail-rounded-rectangle-border-radius-sm 0 0;
    }
}

.o_MessageInReplyToView_avatar {
    width: $o-mail-thread-avatar-size / 2;
    height: $o-mail-thread-avatar-size / 2;
}

.o_MessageInReplyToView_wrapInner {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.o_MessageInReplyToView_body {
    // Make the body single line when possible
    p, div {
        display: inline;
        margin: 0;
    }

    br {
        display: none;
    }
}
