<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_schedule_change_allocation" model="ir.cron">
            <field name="name">Belgian Payroll: Update time off allocations on schedule change</field>
            <field name="model_id" ref="l10n_be_hr_payroll.model_l10n_be_schedule_change_allocation"/>
            <field name="state">code</field>
            <field name="code">model._cron_update_allocation_from_new_schedule()</field>
            <field name="active" eval="True"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="nextcall" eval="(DateTime.now() + relativedelta(days=1, hour=0, minute=0))"/>
        </record>
    </data>
</odoo>
