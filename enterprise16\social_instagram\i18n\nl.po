# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "34 SECONDEN GELEDEN"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Commentaren\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<span>Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required by Instagram.</span><br/>\n"
"                <span>We don't automatically resize your image to avoid undesired result.</span><br/>\n"
"                <span>More information on:</span>"
msgstr ""
"<span>Je afbeelding moet binnen de 4:5 en de 1,91:1 beeldverhouding zijn, zoals vereist door Instagram.</span><br/>\n"
"                <span>We passen het formaat van je afbeelding niet automatisch aan om ongewenste resultaten te voorkomen.</span><br/>\n"
"                <span>Meer informatie over:</span>"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "Toegangstoken"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "An image is required when posting on Instagram."
msgstr "Een afbeelding is vereist bij het posten op Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "App ID"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "App geheim"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Afbeelding auteur"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr ""
"Vink dit aan als je je persoonlijke Instagram-ontwikkelaarsaccount wilt "
"gebruiken in plaats van de verstrekte."

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Could not find any account to add."
msgstr "Kan geen account vinden om toe te voegen."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "Instagram-voorbeeld weergeven"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"Facebook-account-ID geleverd door de Facebook-API, dit mag nooit handmatig worden ingesteld.\n"
"        Het Instagram account (\"Professional\") is altijd gekoppeld aan een Facebook-account."

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
msgid "Instagram"
msgstr "Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "Instagram-toegangstoken"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"Instagram Access Token geleverd door de Facebook API, dit mag nooit handmatig worden ingesteld.\n"
"        Het wordt gebruikt om verzoeken te verifiëren bij het posten naar of lezen van informatie van dit account."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "Instagram account-ID"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"Instagram account-ID geleverd door de Facebook-API, dit mag nooit handmatig "
"worden ingesteld."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "Instagram-app-ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "Geheim Instagram-app"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
#, python-format
msgid "Instagram Comments"
msgstr "Instagram-opmerkingen"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "Instagram-ontwikkelaarsaccount"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "Instagram Facebook-account-ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "Instagram Facebook Auteur ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_id
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_id
msgid "Instagram Image"
msgstr "Instagram-afbeelding"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "Instagram vind-ik-leuks"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "Instagram bericht-ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "URL van Instagram-bericht"

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "Instagram-berichten"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "Instagram-voorbeeld"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Instagram did not provide a valid access token."
msgstr "Instagram heeft geen geldige toegangstoken verstrekt."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Vind-ik-leuks"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "Mediatype"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "No Instagram accounts linked with your Facebook page"
msgstr "Geen Instagram accounts gekoppeld aan je Facebook-pagina"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Only .jpg/.jpeg images can be posted on Instagram."
msgstr "Alleen .jpg/.jpeg-afbeeldingen kunnen op Instagram worden gepost."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Only the first attached image will be posted on Instagram."
msgstr "Alleen de eerste bijgevoegde afbeelding wordt op Instagram gepost."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
#, python-format
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr ""

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "Afbeelding posten"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "Social account"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "Social media live post"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "Social media"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "Social media post"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "Social media post sjabloon"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "Social media stream"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "Social media post"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr ""
"De Facebook-ID van de auteur van deze Instagram-post, gebruikt om de "
"profielfoto op te halen."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "Er is een authenticatieprobleem opgetreden tijdens je verzoek."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "Niet geautoriseerd. Neem contact op met je applicatiebeheerder."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "Gebruik je eigen Instagram account"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr ""
"Wordt gebruikt om toegang tot Instagram toe te staan om de postafbeelding op"
" te halen"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Wordt gebruikt om vergelijkingen te maken wanneer we sommige functies moeten"
" beperken tot een specifiek medium ('facebook', 'twitter', ...)."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Je hebt geen actief abonnement. Je kunt er hier één kopen: %s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
#, python-format
msgid "Your image appears to be corrupted, please try loading it again."
msgstr ""
"Je afbeelding lijkt beschadigd te zijn, probeer deze opnieuw te laden."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
#, python-format
msgid ""
"Your image has to be within the 4:5 and the 1.91:1 aspect ratio as required "
"by Instagram."
msgstr ""
"Je afbeelding moet binnen de 4:5 en de 1.91:1 beeldverhouding zijn, zoals "
"vereist door Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "https://www.facebook.com/help/instagram/1631821640426723"
msgstr "https://www.facebook.com/help/instagram/1631821640426723"
