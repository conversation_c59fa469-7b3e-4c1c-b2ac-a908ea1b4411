<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Job Position Database Groups -->
        <record id="group_job_position_user" model="res.groups">
            <field name="name">Job Position Database: User</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Can view job positions and JVA forms</field>
        </record>

        <record id="group_job_position_manager" model="res.groups">
            <field name="name">Job Position Database: Manager</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="implied_ids" eval="[(4, ref('group_job_position_user')), (4, ref('hr.group_hr_user'))]"/>
            <field name="comment">Can create and manage job positions and JVA forms</field>
        </record>

        <record id="group_jva_approver" model="res.groups">
            <field name="name">JVA Approver</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="implied_ids" eval="[(4, ref('group_job_position_manager'))]"/>
            <field name="comment">Can approve or reject JVA forms</field>
        </record>

        <!-- Record Rules for JVA Forms -->
        <record id="jva_form_rule_own_department" model="ir.rule">
            <field name="name">JVA Forms: Own Department</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_job_position_user'))]"/>
        </record>

        <record id="jva_form_rule_manager" model="ir.rule">
            <field name="name">JVA Forms: Manager Access</field>
            <field name="model_id" ref="model_hr_jva_form"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_job_position_manager'))]"/>
        </record>

        <!-- Record Rules for Job Positions -->
        <record id="job_position_rule_department" model="ir.rule">
            <field name="name">Job Positions: Department Access</field>
            <field name="model_id" ref="hr.model_hr_job"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_job_position_user'))]"/>
        </record>

        <record id="job_position_rule_manager" model="ir.rule">
            <field name="name">Job Positions: Manager Access</field>
            <field name="model_id" ref="hr.model_hr_job"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_job_position_manager'))]"/>
        </record>

    </data>
</odoo>
