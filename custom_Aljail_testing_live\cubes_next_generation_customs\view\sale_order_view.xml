<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_order_form_next_gen_x2" model="ir.ui.view">
        <field name="name">sale.order.form.x1</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']//tree//field[@name='margin']" position="attributes">
                <attribute name="groups">cubes_next_generation_customs.sale_margin_group</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='margin_percent']" position="attributes">
                <attribute name="groups">cubes_next_generation_customs.sale_margin_group</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='purchase_price']" position="attributes">
                <attribute name="groups">cubes_next_generation_customs.sale_margin_group</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_order_form_next_gen_x1" model="ir.ui.view">
        <field name="name">sale.order.form.x1</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_term_id']" position="after">
                <field name="global_discount" groups="cubes_next_generation_customs.group_detailed_discount_sales"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="after">
                <field name="price_unit_after_discount"
                       groups="cubes_next_generation_customs.group_detailed_discount_sales" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='discount']" position="after">
                <field name="original_total_amount" groups="cubes_next_generation_customs.group_detailed_discount_sales"
                       optional="hide"/>
                <field name="is_warn" invisible="1"/>
                <field name="qty_available" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='product_template_id']" position="after">
                <field name="barcode"/>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="partner_balance" readonly="1" force_save="1"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/tree/field[@name='product_template_id']" position="attributes">
                <attribute name="options">{'no_create_edit' : True,'no_create' : True}</attribute>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_margin.sale_margin_sale_order">
        <field name="name">sale.order.margin.view.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='tax_totals']" position="after">
                <label for="margin" groups="cubes_next_generation_customs.sale_margin_group"/>
                <div class="text-nowrap" groups="cubes_next_generation_customs.sale_margin_group">
                    <field name="margin" class="oe_inline"/>
                    <field name="amount_untaxed" invisible="1"/>
                    <span class="oe_inline" attrs="{'invisible': [('amount_untaxed', '=', 0)]}">
                        (<field name="margin_percent" nolabel="1" class="oe_inline" widget="percentage"/>)
                    </span>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
