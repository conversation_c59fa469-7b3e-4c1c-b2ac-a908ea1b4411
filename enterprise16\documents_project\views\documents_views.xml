<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="folder_view_form_inherit" model="ir.ui.view">
        <field name="name">folder.view.form.inherit</field>
        <field name="model">documents.folder</field>
        <field name="inherit_id" ref="documents.folder_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <field name="parent_folder_id" position="attributes">
                <attribute name="context">{'project_documents_template_folder': context.get('project_documents_template_folder')}</attribute>
                <attribute name="options">{'no_create': True, 'no_open': True}</attribute>
            </field>
        </field>
    </record>

    <record id="tag_view_form_inherit" model="ir.ui.view">
        <field name="name">tag.view.form.inherit</field>
        <field name="model">documents.tag</field>
        <field name="inherit_id" ref="documents.tag_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <field name="facet_id" position="attributes">
                <attribute name="context">{'form_view_ref': 'documents_project.facet_view_form_with_folder_inherit', 'documents_project_folder': context.get('documents_project_folder')}</attribute>
            </field>
        </field>
    </record>

    <record id="facet_view_form_with_folder_inherit" model="ir.ui.view">
        <field name="name">facet.view.form.with.folder.inherit</field>
        <field name="model">documents.facet</field>
        <field name="inherit_id" ref="documents.facet_view_form_with_folder"/>
        <field name="mode">primary</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <field name="folder_id" position="attributes">
                <attribute name="readonly">1</attribute>
            </field>
        </field>
    </record>
</odoo>
