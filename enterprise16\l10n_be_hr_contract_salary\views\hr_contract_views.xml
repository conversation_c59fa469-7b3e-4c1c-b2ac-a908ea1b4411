<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_view_form" model="ir.ui.view">
        <field name="name">hr.contract.form</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract_salary.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='%(hr_contract_salary.generate_simulation_link_action)d']" position="attributes">
                <attribute name="attrs">{'invisible': ['|', ('employee_id', '=', False), '&amp;', ('country_code', '=', 'BE'), '&amp;', ('time_credit', '=', True), ('work_time_rate', '=', 0)]}</attribute>
            </xpath>
            <field name="image_1920" position="after">
                <field name="id_card"/>
                <field name="driving_license" attrs="{'invisible': [('transport_mode_car', '=', False)]}"/>
                <field name="mobile_invoice" attrs="{'invisible': [('mobile', '=', 0)]}"/>
                <field name="sim_card" attrs="{'invisible': [('mobile', '=', 0)]}"/>
                <field name="internet_invoice" attrs="{'invisible': [('internet', '=', 0)]}"/>
            </field>
            <div name="div_wage_with_holidays" position="attributes">
                <attribute name="attrs">{'invisible': [('holidays', '=', 0.0), ('l10n_be_group_insurance_rate', '=', 0.0)]}</attribute>
            </div>
            <label name="label_wage_with_holidays" position="attributes">
                <attribute name="attrs">{'invisible': [('holidays', '=', 0.0), ('l10n_be_group_insurance_rate', '=', 0.0)]}</attribute>
            </label>
        </field>
    </record>
</odoo>
