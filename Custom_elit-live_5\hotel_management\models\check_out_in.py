from odoo import api, fields, models

class CheckOutIn(models.Model):
    _name = 'check.out.in'
    _rec_name = 'checkout_time'

    checkout_time = fields.Float(
        string="Check Out Time"
    )
    checkin_time = fields.Float(
        string="Check In Time"
    )
    real_checkout_time = fields.Float(
        string="Real Check Out Time"
    )
    shop_id = fields.Many2one(
         comodel_name='sale.shop',
         string='Hotel'
    )
    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Journal'
    )
    guarantee_journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Guarantee Journal',
        required=True
    )


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    guarantee_amount = fields.Float(
        string='Guarantee Amount'
    )
