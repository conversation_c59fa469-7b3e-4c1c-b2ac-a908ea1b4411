# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# Mostafa Bar<PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_kwh
msgid "1 000 kWh"
msgstr "1 000 kWh"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_m3
msgid "1 000 m3"
msgstr "1 000 m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_p/st
msgid "1 000 p/st"
msgstr "1 000 p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__100_p/st
msgid "100 p/st"
msgstr "100 p/st"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> گزینه‌ها:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> طرف حساب:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Account move line"
msgstr "سطر سند حسابداری"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Active"
msgstr "فعال"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "All"
msgstr "همه"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Arrival"
msgstr "ورودی ها"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "کشور مقصد"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "دسته‌بندی طبق کشور"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "طبق نوع "

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Check the expired"
msgstr "بررسی موارد منقضی شده"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Check the premature"
msgstr "بررسی مواردی که هنوز موعد آنها نرسیده"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "کالا"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_commodity_code
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_code_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Commodity Code"
msgstr "کد کالا"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_code_id
msgid "Commodity code"
msgstr "کد کالا"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "شرکت"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "کشور شرکت"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_country_code
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "کشور"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "کشور مبدأ"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_invoice_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_invoice_transaction_code_id
msgid "Default invoice transaction code"
msgstr "کد تراکنش فاکتور پیشفرض"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_refund_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_refund_transaction_code_id
msgid "Default refund transaction code"
msgstr "کد پیشفرض تراکنش بازپرداخت"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default transport mode"
msgstr "روش پیشفرض انتقال"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Dispatch"
msgstr "ارسال"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr "روش گسترده"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Include VAT"
msgstr ""

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_incoterm_code
msgid "Incoterm Code"
msgstr "کد ارتباط داخلی"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "درون ایالتی"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "کد درون کشوری"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat Country"
msgstr "درون کشوری"

#. module: account_intrastat
#: model:account.report,name:account_intrastat.intrastat_report
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
msgid "Intrastat Report"
msgstr "گزارش درون کشوری"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report_handler
msgid "Intrastat Report Custom Handler"
msgstr "کنترل‌کننده‌ی اختصاصی گزارش درون کشوری"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr "روش انتقال درون کشوری"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr "کد درون کشوری"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr "کشور intrastat، مقصد فروش و ارسال خریدها"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr "شماره‌ی intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr "منطقه‌ی intrastat"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Intrastat transaction code"
msgstr "کد تراکنش intrastat"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid commodity intrastat code products."
msgstr "محصولات دارای کد intrastat نامعتبر"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid transaction intrastat code entries."
msgstr "اسناد دارای کد intrastat تراکنش نامعتبر"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "داده روزنامه"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "آیتم روزنامه"

#. module: account_intrastat
#: model:res.country,name:account_intrastat.xi
msgid "Northern Ireland"
msgstr "ایرلند شمالی"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Only with VAT numbers"
msgstr "فقط با کدهای مالیات بر ارزش افزوده"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
msgid "Origin"
msgstr "مبدا"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_product_origin_country
msgid "Origin Country"
msgstr ""

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_partner_vat
msgid "Partner VAT"
msgstr "کد مالیات بر ارزش افزوده‌ی همکار"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Product"
msgstr "محصول"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr "کشور محصول"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product Variant"
msgstr "گونه محصول"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "منطقه"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_region_code
msgid "Region Code"
msgstr "کد منطقه"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have expired intrastat"
msgstr "کد intrastat برخی سطرها منقضی شده است"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have premature intrastat"
msgstr "تاریخ برخی سطرها هنوز نرسیده است"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some lines have undefined"
msgstr "برخی سطرها نامشخص هستند"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "Some products have undefined"
msgstr "برخی محصولات نامشخص هستند"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard"
msgstr "استاندارد"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Supplementary Unit"
msgstr "واحد تکمیلی"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_supplementary_units
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "Supplementary Units"
msgstr "واحدهای تکمیلی"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "Supplementary Units per Product"
msgstr "واحدهای تکمیلی هر محصول"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_system
msgid "System"
msgstr "سیستم"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "این کشور از گزارشات مالیاتی مربوط به این شرکت استفاده کند"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
#: model:ir.model.fields,help:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "The number of supplementary units per product quantity."
msgstr "تعداد واحدهای تکمیلی به ازای مقدار هر محصول"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Total"
msgstr "جمع کل:"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "تراکنش"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transaction_code
msgid "Transaction Code"
msgstr "کد تراکنش"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "حمل و نقل"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transport_code
msgid "Transport Code"
msgstr "کد انتقال"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr "شناسه‌ی سه‌گانه کد/نوع/کشور باید منحصر به فرد باشد."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Undefined supplementary unit products."
msgstr "محصولات نامشخص واحد تکمیلی"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Undefined weight products."
msgstr "محصولات دارای وزن نامشخص "

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_value
msgid "Value"
msgstr "مقدار"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "View Journal Entry"
msgstr "نمایش ورودی روزنامه"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_weight
msgid "Weight"
msgstr "وزن"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "With VAT numbers"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__c/k
msgid "c/k"
msgstr "c/k"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ce/el
msgid "ce/el"
msgstr "ce/el"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "commodity codes"
msgstr "کدهای کالا"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ct/l
msgid "ct/l"
msgstr "ct/l"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__gi_f_/_s
msgid "gi F / S"
msgstr "gi F / S"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_90_%_sdt
msgid "kg 90 % sdt"
msgstr "kg 90 % sdt"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_h2o2
msgid "kg H2O2"
msgstr "kg H2O2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_k2o
msgid "kg K2O"
msgstr "kg K2O"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_koh
msgid "kg KOH"
msgstr "kg KOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_n
msgid "kg N"
msgstr "kg N"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_naoh
msgid "kg NaOH"
msgstr "kg NaOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_p2o5
msgid "kg P2O5"
msgstr "kg P2O5"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_u
msgid "kg U"
msgstr "kg U"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_met_am_
msgid "kg met.am."
msgstr "kg met.am."

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg/net_eda
msgid "kg/net eda"
msgstr "kg/net eda"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__l_alc__100_%
msgid "l alc. 100 %"
msgstr "l alc. 100 %"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m2
msgid "m2"
msgstr "m2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m3
msgid "m3"
msgstr "m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__p/st
msgid "p/st"
msgstr "p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__pa
msgid "pa"
msgstr "pa"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "product"
msgstr "محصول"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "product's commodity codes"
msgstr "کدهای محصولات"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "supplementary units"
msgstr "واحدهای تکمیلی"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__t__co2
msgid "t. CO2"
msgstr "t. CO2"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "transaction codes"
msgstr "کدهای تراکنش"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "weights"
msgstr "وزن‌ها"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_intrastat_main_template
msgid "when they are required."
msgstr "هنگامی که مورد نیاز هستند."
