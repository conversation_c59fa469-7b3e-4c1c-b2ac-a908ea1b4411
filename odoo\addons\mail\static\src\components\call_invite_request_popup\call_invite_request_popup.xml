<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.CallInviteRequestPopup" owl="1">
        <t t-if="callInviteRequestPopup">
            <div class="o_CallInviteRequestPopup d-flex flex-column m-2 p-5 border border-dark rounded-1 bg-900" t-attf-class="{{ className }}" t-ref="root">
                <t t-if="callInviteRequestPopup.thread.rtcInvitingSession">
                    <div class="o_CallInviteRequestPopup_partnerInfo d-flex flex-column justify-content-around align-items-center text-nowrap">
                        <img class="o_CallInviteRequestPopup_partnerInfoImage mb-2 rounded-circle cursor-pointer"
                            t-att-src="callInviteRequestPopup.thread.rtcInvitingSession.channelMember.avatarUrl"
                            t-on-click="callInviteRequestPopup.onClickAvatar"
                            alt="Avatar"/>
                        <span class="o_CallInviteRequestPopup_partnerInfoName w-100 fw-bolder text-truncate text-center overflow-hidden" t-esc="callInviteRequestPopup.thread.rtcInvitingSession.channelMember.persona.name"/>
                        <span class="o_CallInviteRequestPopup_partnerInfoText fst-italic opacity-75">Incoming Call...</span>
                    </div>
                </t>
                <div class="o_CallInviteRequestPopup_buttonList d-flex justify-content-around align-items-center w-100 mt-4">
                    <button class="o_CallInviteRequestPopup_buttonListButton o_CallInviteRequestPopup_buttonListRefuse p-2 rounded-circle border-0 bg-danger"
                        aria-label="Refuse"
                        title="Refuse"
                        t-on-click="callInviteRequestPopup.onClickRefuse">
                        <i class="o_CallInviteRequestPopup_buttonListButtonIcon fa fa-lg fa-times m-3"/>
                    </button>
                    <button class="o_CallInviteRequestPopup_buttonListButton o_CallInviteRequestPopup_buttonListAccept p-2 rounded-circle border-0 bg-success"
                        aria-label="Accept"
                        title="Accept"
                        t-on-click="callInviteRequestPopup.onClickAccept">
                        <i class="o_CallInviteRequestPopup_buttonListButtonIcon fa fa-lg fa-phone m-3"/>
                    </button>
                </div>
            </div>
        </t>
    </t>

</templates>
