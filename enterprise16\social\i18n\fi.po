# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <mi<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ka<PERSON><PERSON>l<PERSON>@emsystems.fi>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>k<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Tiliä"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "%s (max %s chars)"
msgstr "%s (max %s merkkiä)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ", you must first link a social media."
msgstr ", sinun on ensin linkitettävä sosiaalinen media."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Tapahtui virhe, kun yritit yhdistää tilisi</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"Katso julkaisu\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<small class=\"pe-1\">Clicks:</small>"
msgstr "<small class=\"pe-1\">Klikkaa:</small>"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Onneksi olkoon! Tule takaisin muutaman minuutin kuluttua "
"tarkistamaan tilastosi.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Aktiivinen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Toimenpiteet"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add"
msgstr "Lisää"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Lisää julkaisu"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add a Stream"
msgstr "Lisää suoratoisto"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Lisää suoratoisto"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add an image"
msgstr "Lisää kuva"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "All Companies"
msgstr "Kaikki yritykset"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Kaikki asiaan liittyvät sosiaalisen median viestit"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Sallitut tilit"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Ovatko tilit saatavilla?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Liitä kuvia"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
#, python-format
msgid "Audience"
msgstr "Yleisö"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Yleisön trendi"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Kirjoittajan linkki"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Tekijän nimi"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid ""
"Author link to the external social.media (ex: link to the Twitter Account)."
msgstr ""
"Kirjoittajan linkki ulkoiseen sosiaaliseen mediaan (esim. linkki Twitter-"
"tiliin)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr "Ennen lähettämistä linkit muunnetaan jäljitettäviksi."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Suoratoiston mukaan"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF-tunniste"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Kalenteripäivä"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Kampanja"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Kampanjat"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Kampanjoita käytetään markkinointitoimien keskittämiseen ja niiden tulosten "
"seurantaan."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts ?"
msgstr "Voiko tilejä yhdistää ?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Valitse ensin, minkä <b>tilin</b> haluat yhdistää."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Click to refresh."
msgstr "Klikkaa päivittääksesi."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Klikkausta"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Comment Image"
msgstr "Kommentoi kuvaa"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Yritys"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Asetukset"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Connecting Problem"
msgstr "Liittämisongelma"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' Twitter account"
msgstr ""
"Sisältää tämän tilin luoneen henkilön sosiaalisen median kahvan. Esim: "
"'@odoo.official' Odoo Twitter-tilille"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Jälkikäsitellyn sosiaalisen viestin sisältö (linkit lyhennetään, UTM:t, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr ""
"Valvoo, käsitelläänkö sosiaalisen median virtoja tässä sosiaalisessa "
"mediassa."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Valvoo, voimmeko yhdistää tilejä vai emme."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Luo kampanja"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Luo viesti"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
#, python-format
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Luo muita tilejä %(media_names)s tälle yritykselle tai pyydä "
"%(company_names)s jakamaan tilinsä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Luotu"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Määrittää, onko tällä tilillä yleisö-, sitoutumis- ja tarinatilastoja.\n"
"        Tilit, joilla on tilastot, näkyvät kojelaudalla."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Määrittää, onko tällä tilillä tilastoja vai ei."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Delete Comment"
msgstr "Poista kommentti"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Demo-tila"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Kuvaus"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Kehittäjätilit"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "Do you really want to delete %s"
msgstr "Haluatko todella poistaa %s"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Luonnos"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Pituusrajoitusten vuoksi seuraavia viestejä ei voida lähettää:\n"
" %s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Edit Comment"
msgstr "Muokkaa kommenttia"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Ota Demo-tila käyttöön"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module.<br/>\n"
"                                        This must never be used on a production database!"
msgstr ""
"Ota tämä vaihtoehto käyttöön ja lataa demotiedot sosiaalisen moduulin testaamiseksi.<br/>\n"
"                                        Tätä ei saa koskaan käyttää tuotantotietokannassa!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Engagement"
msgstr "Palkitseminen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Sitoutumisen trendi"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr "Aina kun tili luodaan, luodaan myös utm.medium, joka liitetään tiliin"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Epäonnistui"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Epäonnistumisen syy"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Syöte"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Feed Posts"
msgstr "Syötteen julkaisut"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Syötteen julkaisujen määrä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Muotoiltu julkaisupäivä"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr "Sosiaalisen tilin yleinen yleisö (sivutykkäykset, seuraajat, ...)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "Go back to Odoo"
msgstr "Palaa takaisin Odoohon"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Mene osoitteeseen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Nimimerkki / Lyhyt nimi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Happy with the result? Let's post it!"
msgstr "Oletko tyytyväinen tulokseen? Julkaistaan se!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "On tilitilastot"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "Sisältää trendejä?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoni joka kertoo poikkeustoiminnosta."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Kuva"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "Kuvan URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Image Url"
msgstr "Kuvan URL"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "Kuvien URL-osoitteet"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Kuvat, jotka on jaettu tämän julkaisun yhteydessä."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Insights"
msgstr "Näkemykset"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Näyttää siltä, että sosiaalisen median linkin kanssa on ongelma, klikkaa "
"tästä linkittääksesi tilin uudelleen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "It will appear in the Feed once it has posts to display."
msgstr "Näkyy syötteessä, kun viestejä on näytettäväksi."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account____last_update
#: model:ir.model.fields,field_description:social.field_social_live_post____last_update
#: model:ir.model.fields,field_description:social.field_social_media____last_update
#: model:ir.model.fields,field_description:social.field_social_post____last_update
#: model:ir.model.fields,field_description:social.field_social_post_template____last_update
#: model:ir.model.fields,field_description:social.field_social_stream____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_post_image____last_update
#: model:ir.model.fields,field_description:social.field_social_stream_type____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's <b>connect</b> to Facebook, LinkedIn or Twitter."
msgstr "<b>Otetaan yhteys</b> Facebookiin, LinkedIniin tai Twitteriin."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Luodaan oma <b>sosiaalisen median</b> kojelauta."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's start posting."
msgstr "Aloitetaan julkaiseminen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Like"
msgstr "Tykkää"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Likes"
msgstr "Tykkäykset"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Linkitä kuvaus"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Link Image"
msgstr "Linkitä kuva"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "Linkitä kuvan URL-osoite"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Linkin otsikko"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "Linkin URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Link a new account"
msgstr "Yhdistä uusi tili"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Linkki tilille"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Linkitä tili"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Linkitä tili yritykseen rajoittaaksesi sen käyttöä tai pidä se tyhjänä, "
"jotta kaikki yritykset voivat käyttää sitä."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Linkitä jatkuvasti päivittyvä julkaisu kohdemediassa."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Linkitä sosiaaliset tilit"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Linkki ulkoisen sosiaalisen tilin tilastoihin"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Linkki ulkoiseen sosiaaliseen mediaan on rikki"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Luettelo tileistä, jotka voidaan valita tähän virkaan."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Live Posts by Social Media"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Load more comments..."
msgstr "Lataa lisää kommentteja..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_main_attachment_id
#: model:ir.model.fields,field_description:social.field_social_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Main actions"
msgstr "Päätoiminnot"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Suurin julkaisun pituus"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Median tyyppi"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Viesti"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__message_length
#: model:ir.model.fields,field_description:social.field_social_post_template__message_length
msgid "Message Length"
msgstr "Viestin pituus"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Message posted"
msgstr "Viesti on julkaistu"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Message posted partially. These are the ones that couldn't be posted: <br>%s"
msgstr "Viesti julkaistu osittain. Seuraavia ei voitu julkaista: <br>%s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"Useimmat social.live.posts-postaukset siirtyvät suoraan tilasta Valmis tilaan Lähetetty/Palautettu, koska ne ovat tulosta yhdestä kutsusta kolmannen osapuolen API:lle.\n"
"        Posting-tila on käytettävissä myös niille, jotka lähetetään eräajona (kuten push-ilmoitukset)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Omat julkaisut"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Omat suoratoistot"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Nimi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New Post"
msgstr "Uusi kirjoitus"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New content available"
msgstr "Uutta sisältöä saatavilla"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Next"
msgstr "Seuraava"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Ei sosiaalista tiliä vielä!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Ei vielä sosiaalisia suoratoistoja!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Suoratoistoa ei ole vielä lisätty!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "No comments yet."
msgstr "Ei vielä kommentteja."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "No social accounts configured, please contact your administrator."
msgstr "Sosiaalisia tilejä ei ole määritetty, ota yhteyttä ylläpitäjään."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Number of Followers of your channel"
msgstr "Kanavasi seuraajien määrä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Klikkausten lukumäärä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Vuorovaikutuksen määrä (tykkäykset, jakamiset, kommentit jne.) sosiaalisten "
"julkaisujen kanssa"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "Yleisön sitoutuminen julkaisuun (Tykkäykset, kommentit...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Yleisön sitoutuminen julkaisuihisi (tykkäykset, kommentit, ...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Re-tweets, ...)."
msgstr ""
"Julkaisuistasi luotujen tarinoiden määrä (jakamiset, uudelleentwiittaukset, "
"...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Kuinka monta kertaa ihmiset ovat ottaneet yhteyttä julkaisuihisi "
"(tykkäykset, kommentit, jakamiset...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Retweets...)"
msgstr ""
"Kuinka monta kertaa kanavasi kanssa tekemisissä olleet ihmiset ovat luoneet "
"tarinoita ystäviensä tai seuraajiensa syötteeseen (jakoja, "
"uudelleentwiittauksia...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Or add"
msgstr "Tai lisää"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Yleisömäärän kasvun/laskun prosenttiosuus määritellyn ajanjakson aikana."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Sitoumuksen lisääntymisen/vähenemisen prosenttiosuus määritellyn ajanjakson "
"aikana."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Tarinoiden kasvun/laskun prosenttiosuus määritellyn ajanjakson aikana."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Määritä vähintään yksi tili, jolle haluat lähettää viestin (viestin ID(s) "
"%s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Post"
msgstr "Kirjaa"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Post Image"
msgstr "Artikkelin kuva"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Post Images"
msgstr "Atikkelien kuvat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Artikkelin linkki"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Viestin lähettäminen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Julkaise nyt"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Artikkelin linkki ulkoiseen sosiaaliseen mediaan (esim. linkki varsinaiseen "
"Facebook-postaukseen)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Julkaise kohteessa"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Kirjattu"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Julkaisu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Julkaisuja"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Viestit tilin mukaan"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Posts By Accounts"
msgstr "Viestit tilien mukaan"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Paina Enter julkaistaaksesi viestin. Paina Shift+Enter lisätäksesi "
"rivinvaihdon."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Esikatsele viestiäsi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Previous"
msgstr "Edellinen"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "Julkaise postauksesi heti tai ajoita se myöhempään ajankohtaan."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Julkaisija"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Julkaisupäivä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Julkaisupäivä"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Valmis siirrettäväksi"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Aiheeseen liittyvä sosiaalinen media"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, Twitter, ...)."
msgstr "Aiheeseen liittyvä sosiaalinen media (Facebook, Twitter, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Aiheeseen liittyvä sosiaalinen tili"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Reply"
msgstr "Vastaa"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Yritä uudelleen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Aikatauluta"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Aikatauluta myöhemmäksi"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Ajoitettu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Suunniteltu päivämäärä"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Selected accounts (%s) do not match the selected company (%s)"
msgstr "Valitut tilit (%s) eivät vastaa valittua yritystä (%s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Lähetä nyt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Jakso, jota käytetään suoratoistojen järjestämiseen (lähinnä 'Feed' kanban-"
"näkymässä)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Aseta enimmäismerkkimäärä, jonka voi lähettää viestissä. 0 tarkoittaa, että "
"rajoitusta ei ole."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Asetukset"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Sosiaalisen median tili"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Sosiaalisen median tilit"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Sosiaalisen median live-julkaisu"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Sosiaalisen median päällikkö"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
msgid "Social Marketing"
msgstr "Some-markkinointi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Social Media"
msgstr "Sosiaalinen media"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Sosiaalisen median viestit"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Sosiaalisen median julkaisu"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Sosiaalisen median julkaisun malli"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Sosiaalisen viestin mallit"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Sosiaalisen median julkaisut"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Sosiaalinen media"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Sosiaalisen median suoratoiston julkaisu"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Sosiaalisen median julkaisun kuvaliite"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Sosiaalisen median suoratoistot"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Sosiaalisen median käyttäjä"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
#: model:ir.cron,cron_name:social.ir_cron_post_scheduled
msgid "Social: Publish Scheduled Posts"
msgstr "Sosiaalinen media: Julkaise ajastettuja julkaisuja"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
#, python-format
msgid "Something went wrong while posting the comment."
msgstr "Jokin meni pieleen kommenttia lähetettäessä."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
#, python-format
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Ikävä kyllä et voilinkittää tätä tiliä uudelleen. Ota yhteyttä ylläpitäjään."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Lähde"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Erityinen tekninen kenttä, joka sisältää diktin, joka sisältää live-virkojen"
" nimet mediatunnusten mukaan (käytetään kanban-näkymässä)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Tilastotlinkki"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Tila"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
#, python-format
msgid "Stories"
msgstr "Tarinat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Tarinoiden trendit"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "Stream Added (%s)"
msgstr "Suoratoisto lisätty (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Suoratoiston julkaisu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Suoratoiston julkaisun kuvat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "Suoratoiston julkaisun kuvat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Suoratoiston tyypit"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Suoratoiston tyypin nimi (tekninen)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Suoratoistot käytössä"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr ""
"Alijulkaisut, jotka julkaistaan kullakin valitulla sosiaalisella tilillä."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Synchronize"
msgstr "Synkronoi"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "Viesti-kenttä vaaditaan viestin ID:lle %s"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Tilit, joilla tämä viesti julkaistaan."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"Kolmannen osapuolen tietoihin perustuva viestin kirjoittajan nimi (esim. "
"\"John Doe\")."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Julkaisu katsotaan \"Lähetetyksi\", kun kaikki sen alijulkaisut (yksi per "
"sosiaalinen tili) ovat joko \"Epäonnistunut\" tai \"Lähetetty\""

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "Julkaisupäivä perustuu kolmansien osapuolten tietoihin."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"Syy siihen, miksi julkaisua ei onnistuttu julkaisemaan sosiaalisessa "
"mediassa (esim. yhteysvirhe, päällekkäinen postaus, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "Aiheeseen liittyvä sosiaalisen median kuva"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Valittuihin sosiaalisiin tileihin liittyvät sosiaaliset mediat."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Alaviesteissä on julkaisuvirheitä"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Tätä tunnusta voidaan käyttää sen tarkistamiseen, että sosiaalipalvelujen "
"tarjoajalta saapuvaa pyyntöä ei ole väärennetty."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Otsikko"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "To add a stream"
msgstr "Suoratoiston  lisääminen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Tyyppi"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella"

#. module: social
#: model:ir.model,name:social.model_utm_campaign
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
msgid "UTM Campaign"
msgstr "UTM-kampanja"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM-media"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "UTM-lähde"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Unknown error"
msgstr "Tuntematon virhe"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "Uploaded file does not seem to be a valid image."
msgstr "Ladattu tiedosto ei näytä olevan kelvollinen kuva."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app.\n"
"                            Those credentials are provided in the developer section of your professional social media account."
msgstr ""
"Käytä omia kehittäjätilejäsi sosiaalisessa sovelluksessamme.\n"
"                            Nämä tunnukset annetaan ammatillisen sosiaalisen median tilisi kehittäjäosiossa."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Käytetään vertailujen tekemiseen, kun joitakin ominaisuuksia on rajoitettava"
" tiettyyn mediaan ('facebook', 'twitter', ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "View"
msgstr "Näytä"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Milloin"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Kun maailmanlaajuinen viesti julkaistiin. Varsinaiset alapostien "
"julkaisupäivät voivat vaihdella mediasta riippuen."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr "Liitä kuvia viesteihisi (jos sosiaalinen media tukee sitä)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a comment..."
msgstr "Kirjoita kommentti..."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Write a message to get a preview of your post."
msgstr "Kirjoita viesti saadaksesi esikatselun viestistäsi."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a reply..."
msgstr "Kirjoita vastaus…"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Kirjoita houkutteleva julkaisu, lisää kuvia ja ajoita se julkaistavaksi "
"myöhemmin useilla alustoilla kerralla."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "You can only move posts that are scheduled."
msgstr "Voit siirtää vain ajastettuja julkaisuja."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Et voi poistaa näitä UTM-medioita, koska ne on linkitetty seuraaviin sosiaalisiin tileihin sosiaalisessa mediassa:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Et voi poistaa näitä UTM-lähteitä, koska ne on linkitetty sosiaalisiin viesteihin Social-osiossa:\n"
"%(utm_sources)s"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Omat viestisi"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "a Stream from an existing account"
msgstr "stream olemassa olevalta tililtä"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "ennen lähettämistä."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "comment/reply"
msgstr "kommentti/vastaus"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "kojelauta"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "for"
msgstr "tekijänä"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "replies..."
msgstr "vastaukset..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"pitää silmällä omia julkaisujasi ja seurata kaikkea sosiaalista toimintaa."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "yhdistääksesi tilisi ja aloittaaksesi lähettämisen."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "aloittaa lähettämisen Odoosta."
