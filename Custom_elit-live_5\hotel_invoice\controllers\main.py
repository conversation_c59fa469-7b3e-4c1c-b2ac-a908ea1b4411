# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime, timedelta
import calendar


class MonthlyReservationExcel(http.Controller):

    @http.route('/hotel_invoice/monthly_reservation_excel', type='http', auth='user')
    def monthly_reservation_excel(self, month, year, **kwargs):
        """Generate Excel file for monthly reservation schedule."""
        # Convert parameters to integers
        month = int(month)
        year = int(year)
        
        # Create a model instance to use its methods
        report_model = request.env['monthly.reservation.report']
        
        # Get month name and days in month
        month_name = report_model.get_month_name(month)
        actual_days_in_month = calendar.monthrange(year, month)[1]
        
        # Date range for the report
        start_date = datetime(year, month, 1)
        end_date = datetime(year, month, actual_days_in_month)
        
        # Format dates for domain filter
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Map of room.product_id to room.id for efficient lookup
        room_product_map = {}
        
        # Get all rooms
        rooms = request.env['hotel.room'].search([])
        
        # Create mapping from product_id to room
        for room in rooms:
            if room.product_id:
                room_product_map[room.product_id.id] = room.id
        
        # Find all reservation lines that have dates overlapping with our month
        resv_line_model = request.env['hotel.reservation.line']
        resv_line_domain = [
            ('checkout', '>=', start_date_str),
            ('checkin', '<=', end_date_str)
        ]
        reservation_lines = resv_line_model.search(resv_line_domain)
        
        # Group reservation lines by room
        room_reservation_map = {}
        for line in reservation_lines:
            # Get the product_id from the room_number field
            if not line.room_number or not line.room_number.id:
                continue
            
            product_id = line.room_number.id
            
            # Check if this product maps to a room
            if product_id not in room_product_map:
                continue
            
            room_id = room_product_map[product_id]
            
            # Add this reservation line to the room's reservations
            if room_id not in room_reservation_map:
                room_reservation_map[room_id] = []
            
            reservation = line.line_id  # Get the parent reservation
            if reservation:
                room_reservation_map[room_id].append({
                    'reservation': reservation,
                    'line': line
                })
        
        # Prepare the output stream for the Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Monthly Reservation Schedule')
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True, 
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#f8f9fa',
            'border': 1
        })
        
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'left'
        })
        
        date_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'right'
        })
        
        room_format = workbook.add_format({
            'bold': True, 
            'align': 'left',
            'bg_color': '#f8f9fa',
            'border': 1
        })
        
        # Status color formats with text alignment
        draft_format = workbook.add_format({
            'bg_color': '#FFE4B5', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        confirmed_format = workbook.add_format({
            'bg_color': '#98FB98', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        checkin_format = workbook.add_format({
            'bg_color': '#87CEEB', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        empty_format = workbook.add_format({'bg_color': '#FFFFFF', 'border': 1})
        extra_day_format = workbook.add_format({'bg_color': '#F5F5F5', 'border': 1})
        
        # Set column widths
        worksheet.set_column(0, 0, 20)  # Room name column
        worksheet.set_column(1, 31, 5)  # Day columns
        
        # Write title
        worksheet.merge_range(0, 0, 0, 1, 'Monthly Reservation Schedule', title_format)
        worksheet.write(0, 2, f"{month_name} {year}", date_format)
        
        # Write legend
        worksheet.write(1, 0, 'Legend:', workbook.add_format({'bold': True}))
        worksheet.write(1, 1, 'Draft', draft_format)
        worksheet.write(1, 2, 'Confirmed', confirmed_format)
        worksheet.write(1, 3, 'Checked In', checkin_format)
        
        # Write headers
        row = 3
        worksheet.write(row, 0, 'Room', header_format)
        for day in range(1, 32):
            worksheet.write(row, day, day, header_format)
        
        # Process each room
        row = 4
        for room in rooms:
            worksheet.write(row, 0, room.name, room_format)
            
            # Prepare days data for this room
            days_data = []
            
            # Initialize all days as unoccupied
            for day in range(1, 32):  # Always create 31 days
                days_data.append({
                    'occupied': 0,
                    'guest': '',
                    'color': '#FFFFFF' if day <= actual_days_in_month else '#F5F5F5',  # Light gray for invalid days
                    'reservation_id': False,
                    'state': False,
                    'guest_name': '',
                    'seq': 0  # Sequence to track consecutive days of the same reservation
                })
            
            # Get reservations for this room
            room_reservations = room_reservation_map.get(room.id, [])
            
            # Process each reservation for this room
            for res_data in room_reservations:
                reservation = res_data['reservation']
                line = res_data['line']
                
                # Only process reservations in draft, confirm, or done (checked-in) states
                if reservation.state not in ['draft', 'confirm', 'done']:
                    continue
                
                # Get guest name
                guest_name = reservation.partner_id.name if hasattr(reservation, 'partner_id') and reservation.partner_id else 'Guest'
                
                # Get reservation state
                state = reservation.state
                
                try:
                    # Get check-in/check-out dates from the reservation line
                    checkin_date = datetime.strptime(line.checkin, '%Y-%m-%d') if isinstance(line.checkin, str) else line.checkin
                    checkout_date = datetime.strptime(line.checkout, '%Y-%m-%d') if isinstance(line.checkout, str) else line.checkout
                    
                    # Adjust dates to be within the month
                    if checkin_date < start_date:
                        checkin_date = start_date
                    if checkout_date > end_date:
                        checkout_date = end_date
                    
                    # Mark days as occupied
                    current_date = checkin_date
                    seq = 1  # Sequence counter for consecutive days
                    
                    # Calculate the span of days for this reservation to set sequence numbers
                    while current_date < checkout_date:  # Not including checkout day
                        if current_date.month == month and current_date.year == year:
                            day_index = current_date.day - 1  # 0-based index
                            
                            # Only set the day if it's not already occupied or if it has the same reservation_id
                            if days_data[day_index]['occupied'] == 0 or days_data[day_index]['reservation_id'] == reservation.id:
                                reservation_info = f"{guest_name}\n"
                                reservation_info += f"Check-in: {line.checkin}\nCheck-out: {line.checkout}"
                                
                                days_data[day_index] = {
                                    'occupied': 1,
                                    'guest': reservation_info,
                                    'guest_name': guest_name,
                                    'state': state,
                                    'color': '#FFE4B5' if state == 'draft' else '#98FB98' if state == 'confirm' else '#87CEEB',
                                    'reservation_id': reservation.id,
                                    'seq': seq
                                }
                                seq += 1
                        
                        # Move to next day
                        current_date = current_date + timedelta(days=1)
                except Exception as e:
                    # If there's an error with the dates, skip this reservation
                    continue
            
            # Process the days for merging cells
            day = 1
            while day <= 31:
                if day > actual_days_in_month:
                    # Extra days beyond the month
                    worksheet.write(row, day, '', extra_day_format)
                    day += 1
                    continue
                
                day_data = days_data[day-1]
                
                if day_data['occupied'] == 1:
                    # Get current reservation details
                    reservation_id = day_data['reservation_id']
                    state = day_data['state']
                    guest_name = day_data['guest_name']
                    cell_note = day_data['guest']
                    
                    # Determine how many consecutive days have this reservation
                    merge_length = 1
                    for next_day in range(day+1, 32):
                        if next_day > actual_days_in_month:
                            break
                        
                        next_day_data = days_data[next_day-1]
                        if (next_day_data['occupied'] == 1 and 
                            next_day_data['reservation_id'] == reservation_id and 
                            next_day_data['seq'] == day_data['seq'] + merge_length):
                            merge_length += 1
                        else:
                            break
                    
                    # Select appropriate format based on state
                    if state == 'draft':
                        cell_format = draft_format
                    elif state == 'confirm':
                        cell_format = confirmed_format
                    elif state == 'done':
                        cell_format = checkin_format
                    else:
                        cell_format = empty_format
                    
                    # If more than one day, merge cells
                    if merge_length > 1:
                        worksheet.merge_range(row, day, row, day + merge_length - 1, guest_name, cell_format)
                        # Add comment to only the first cell of the merged range
                        worksheet.write_comment(row, day, cell_note, {'width': 200, 'height': 80})
                        day += merge_length
                    else:
                        # Single day reservation
                        worksheet.write(row, day, guest_name, cell_format)
                        worksheet.write_comment(row, day, cell_note, {'width': 200, 'height': 80})
                        day += 1
                else:
                    # Empty cell
                    worksheet.write(row, day, '', empty_format)
                    day += 1
            
            row += 1
        
        # Close the workbook
        workbook.close()
        
        # Prepare the HTTP response with the Excel file
        output.seek(0)
        filename = f"Monthly_Reservation_Schedule_{month_name}_{year}.xlsx"
        
        return request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        ) 