<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <template id="survey_page_statistics" name="Survey: result statistics page">
        <t t-call="survey.layout">
            <t t-call="survey.survey_button_form_view" />
            <t t-set="page_record_limit" t-value="10"/><!-- Change this record_limit to change number of record  per page-->
            <div class="container o_survey_result">
                <t t-call="survey.survey_page_statistics_header" />
                <t t-call="survey.survey_page_statistics_inner" />
            </div>
        </t>
    </template>

    <template id="survey_page_statistics_header" name="Survey: result statistics header">
        <div class="o_survey_statistics_header pt-5 mt32">
            <h1>
                <span t-field="survey.title"/>
                <span style="font-size:1.5em;"
                    t-attf-class="fa fa-bar-chart-o #{'fa-bar-chart-o' if survey.scoring_type == 'no_scoring' else 'fa-trophy' if survey.certification else 'fa-question-circle-o'} float-end " role="img" aria-label="Chart" title="Chart"/>
            </h1>
            <div t-field="survey.description" class="oe_no_empty"/>
            <h2 t-if="not question_and_page_data">
                Sorry, no one answered this survey yet.
            </h2>
        </div>
    </template>

    <template id="survey_page_statistics_inner" name="Survey: result statistics content">
        <div t-if="survey.session_show_leaderboard" class="o_survey_session_leaderboard mb-5 mt-1">
            <h2 class="mt16 text-uppercase text-muted">Leaderboard</h2>
            <t t-call="survey.user_input_session_leaderboard"/>
        </div>
        <div t-if="question_and_page_data" class="o_survey_results_topbar rounded d-print-none mb-5">
            <t t-call="survey.survey_results_filters"/>
        </div>
        <div t-if="survey.scoring_type in ['scoring_with_answers', 'scoring_without_answers']">
            <h2 class="mt16 text-uppercase text-muted">Results Overview</h2>
            <div>Success rate: <span class="fw-bold"><t t-call="survey.survey_remove_unnecessary_decimals">
                <t t-set="value_to_format" t-value="survey_data['global_success_rate']"/></t>%</span></div>
            <div class="survey_graph" data-graph-type="pie" t-att-data-graph-data="survey_data['global_success_graph']">
                <!-- canvas element for drawing pie chart -->
                <canvas/>
            </div>
            <hr/>
        </div>

        <div t-foreach="question_and_page_data" t-as='question_data'>
            <t t-set="question" t-value="question_data['question']"/>
            <t t-if="question_data['is_page']">
                <h2 class="mt16 text-uppercase text-muted" t-field="question.title"/>
                <div t-field="question.description" class="oe_no_empty" />
                <hr class="mt-2 mb-2"/>
            </t>
            <div t-else="" class="ms-4 mt-5">
                <t t-call="survey.survey_page_statistics_question" />
            </div>
        </div>
    </template>

    <template id="survey_page_statistics_question" name="Question: result statistics">
        <t t-set="comment_lines" t-value="question_data['comment_line_ids']"/>
        <t t-set="graph_data" t-value="question_data['graph_data']"/>
        <t t-set="table_data" t-value="question_data['table_data']"/>

        <div class="o_survey_results_question pb-5 border-bottom">
        <div class="row mb-3">
            <div class="col-sm-6">
                <h5 t-field="question.title" class="me-3"/>
            </div>
            <!-- Question info -->
            <div class="col-sm-6 text-sm-end">
                <span class="badge text-bg-info ms-0 ms-sm-1 me-1 me-sm-0" t-field='question.question_type'/>
                <t t-if="question.question_type == 'matrix'">
                    <span class="badge text-bg-info ms-0 ms-sm-1 me-1 me-sm-0" t-field='question.matrix_subtype'/>
                </t>
                <!-- Scoring info -->
                <t t-if="question.question_type in ['numerical_box', 'date', 'datetime']">
                    <span class="badge text-bg-success ms-0 ms-sm-1 me-1 me-sm-0"><span t-esc="question_data['right_inputs_count']" class="me-1"/>Correct</span>
                    <t t-if="question.question_type in ['simple_choice', 'multiple_choice']">
                        <span class="badge text-bg-warning ms-0 ms-sm-1 me-1 me-sm-0" t-if="question.question_type == 'multiple_choice'">
                            <span t-esc="question_data['partial_inputs_count']" class="me-1"/>Partial
                        </span>
                    </t>
                </t>
                <!-- Inputs info -->
                <span class="badge text-bg-info ms-0 ms-sm-1 me-1 me-sm-0">
                    <span t-esc="len(question_data['answer_input_done_ids'])" class="me-1"/>Responded
                </span>
                <span class="badge text-bg-info ms-0 ms-sm-1 me-1 me-sm-0">
                    <span t-esc="len(question_data['answer_input_skipped_ids'])" class="me-1"/>Skipped
                </span>
            </div>
        </div>

        <!-- Question Description -->
        <div class="ms-3 text-muted" t-field="question.description"/>

        <!-- Answers -->
        <t t-set="question_answered" t-value="question_data['answer_input_done_ids']"/>
        <div t-if="not question_answered" class="alert alert-secondary m-3" role="alert">
            No one answered this question.
        </div>

        <t t-if="question_answered and question.question_type in ['text_box', 'char_box']"
           t-call="survey.question_result_text"/>
        <t t-elif="question_answered and question.question_type in ['numerical_box', 'date', 'datetime']"
           t-call="survey.question_result_number_or_date_or_datetime"/>
        <t t-elif="question.question_type in ['simple_choice', 'multiple_choice']"
           t-call="survey.question_result_choice"/>
        <t t-elif="question.question_type in ['matrix']"
           t-call="survey.question_result_matrix"/>
        </div>
    </template>

    <template id="survey_results_filters" name="Survey: Filter results">
        <t t-set="search_passed_or_failed" t-value="search_failed or search_passed"/>
        <div class="card-header">
            <span class="fa fa-filter me-1"/>Filters
            <span t-if="search_filters or search_finished or search_passed_or_failed"
                  class="o_survey_results_topbar_clear_filters text-primary ms-4">
                <i class="fa fa-trash me-1"/>Remove all filters
            </span>
        </div>
        <div class="question_and_page_data o_survey_result p-0">
            <nav class="navbar navbar-light rounded">
                <div t-if="question_and_page_data" class="justify-content-between w-100">
                    <ul class="nav o_survey_results_topbar_dropdown_filters">
                        <t t-set="dropdown_item_classes" t-translation="off">dropdown-item d-flex align-items-center justify-content-between</t>
                        <li class="nav-item dropdown me-2 my-1">
                            <a href="#" role="button" data-bs-toggle="dropdown"
                               t-attf-class="btn btn-outline-primary dropdown-toggle #{'active' if search_finished else ''}">
                                <span t-if="search_finished">Completed surveys</span>
                                <span t-else="">All surveys</span>
                            </a>
                            <div class="dropdown-menu">
                                <a t-attf-class="#{dropdown_item_classes} filter-finished-or-not #{'active' if not search_finished else ''}">
                                    <span>All surveys</span>
                                    <span t-if="not search_finished" t-esc="survey_data['count_all']" class="badge rounded-pill text-bg-primary ms-3"/>
                                </a>
                                <a t-attf-class="#{dropdown_item_classes} filter-finished #{'active' if search_finished else ''}">
                                    <span>Completed surveys</span>
                                    <span t-if="not search_finished" t-esc="survey_data['count_finished']" class="badge rounded-pill text-bg-primary ms-3"/>
                                </a>
                            </div>
                        </li>
                        <li t-if="survey.scoring_type != 'no_scoring'" class="nav-item dropdown me-2 my-1">
                            <a href="#" role="button" data-bs-toggle="dropdown"
                               t-attf-class="btn btn-outline-primary dropdown-toggle #{'active' if search_passed_or_failed else ''}">
                                <span t-if="search_failed">Failed only</span>
                                <span t-elif="search_passed">Passed only</span>
                                <span t-else="">Passed and Failed</span>
                            </a>
                            <div class="dropdown-menu">
                                <a t-attf-class="#{dropdown_item_classes} filter-passed-and-failed #{'active' if not search_passed_or_failed else ''}">
                                    <span>Passed and Failed</span>
                                    <span t-if="not search_passed_or_failed" t-esc="survey_data['count_all']" class="badge rounded-pill text-bg-primary ms-3"/>
                                </a>
                                <a t-attf-class="#{dropdown_item_classes} filter-passed #{'active' if search_passed else ''}">
                                    <span>Passed only</span>
                                    <span t-if="not search_passed_or_failed" t-esc="survey_data['count_passed']" class="badge rounded-pill text-bg-primary ms-3"/>
                                </a>
                                <a t-attf-class="#{dropdown_item_classes} filter-failed #{'active' if search_failed else ''}">
                                    <span>Failed only</span>
                                    <span t-if="not search_passed_or_failed" t-esc="survey_data['count_failed']" class="badge rounded-pill text-bg-primary ms-3"/>
                                </a>
                            </div>
                        </li>
                    </ul>
                    <ul class="nav o_survey_results_topbar_answer_filters mt-2">
                        <t t-if="search_filters">
                            <li t-foreach="search_filters" t-as="filter_data" class="nav-item me-2 my-1">
                                <span t-attf-class="btn btn-light filter-remove-answer cursor-default">
                                    <span t-esc="filter_data['question']"/> | <span t-esc="filter_data['answers']"></span>
                                    <i class="fa fa-times filter-remove-answer text-primary"
                                       t-att-data-row-id="filter_data['row_id']" t-att-data-answer-id="filter_data['answer_id']"></i>
                                </span>
                            </li>
                        </t>
                        <t t-else="">
                            <p class="my-auto text-muted fst-italic">
                                <span>Click on the filter icon (<span class="fa fa-filter text-primary"/>)
                                    next to an answer to filter surveys on similar answers only.</span>
                            </p>
                        </t>
                    </ul>
                </div>
            </nav>
        </div>
    </template>

    <template id="question_result_text" name="Question: text result (text_box, char_box)">
        <table class="table table-hover table-sm" t-att-id="'survey_table_question_%d' % question.id">
            <thead>
                <tr>
                    <th>#</th>
                    <th>User Responses</th>
                </tr>
            </thead>
            <tbody>
                <t t-foreach="table_data" t-as="input_line">
                    <tr t-if="not input_line.skipped" t-att-class="'d-none' if input_line_index >= page_record_limit else ''">
                        <td>
                            <t t-if="no_print_url"><t t-esc="input_line_index + 1"></t></t>
                            <t t-else="">
                                <a t-att-href="input_line.user_input_id.get_print_url()">
                                    <t t-esc="input_line_index + 1"></t>
                                </a>
                            </t>
                        </td>
                        <td>
                            <t t-if="question.question_type == 'text_box'">
                                <span t-if="no_print_url" t-field="input_line.value_text_box" />
                                <a t-else="" t-att-href="input_line.user_input_id.get_print_url()" t-field="input_line.value_text_box"></a>
                            </t>
                            <t t-if="question.question_type == 'char_box'">
                                <span t-if="no_print_url" t-field="input_line.value_char_box" />
                                <a t-else="" t-att-href="input_line.user_input_id.get_print_url()" t-field="input_line.value_char_box"></a>
                            </t>
                        </td>
                    </tr>
                </t>
            </tbody>
        </table>
        <t t-call="survey.question_table_pagination"/>
    </template>

    <template id="question_result_number_or_date_or_datetime" name="Question: number or date (and time) result (numerical_box or date or datetime)">
        <t t-if="question.question_type == 'numerical_box'">
            <span class="float-end mt8">
                <div class="btn-group o_survey_results_question_pill" role="group" aria-label="Maximum">
                    <span class="badge text-bg-secondary only_left_radius px-2 pt-1">Maximum </span>
                    <span class="badge text-bg-success only_right_radius px-2 pt-1" t-esc="question_data['numerical_max']"></span>
                </div>
                <div class="btn-group o_survey_results_question_pill" role="group" aria-label="Minimum">
                    <span class="badge text-bg-secondary only_left_radius px-2 pt-1">Minimum </span>
                    <span class="badge text-bg-danger only_right_radius px-2 pt-1" t-esc="question_data['numerical_min']"></span>
                </div>
                <div class="btn-group o_survey_results_question_pill" role="group" aria-label="Average">
                    <span class="badge text-bg-secondary only_left_radius px-2 pt-1">Average </span>
                    <span class="badge text-bg-warning only_right_radius px-2 pt-1" t-esc="question_data['numerical_average']"></span>
                </div>
            </span>
        </t>

        <ul class="nav nav-tabs d-print-none" role="tablist">
            <li class="nav-item">
                <a t-att-href="'#survey_stats_question_%d' % question.id"
                    t-att-aria-controls="'survey_stats_question_%d' % question.id"
                    class="nav-link active" data-bs-toggle="tab" role="tab">
                    <i class="fa fa-list-ol me-1"/>Most Common
                </a>
            </li>
            <li class="nav-item">
                <a t-att-href="'#survey_data_question_%d' % question.id"
                    t-att-aria-controls="'survey_data_question_%d' % question.id"
                    class="nav-link" data-bs-toggle="tab" role="tab">
                    <i class="fa fa-list-alt me-1"/>All Data
                </a>
            </li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active with-3d-shadow with-transitions" t-att-id="'survey_stats_question_%d' % question.id">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>User Responses</th>
                            <th>Occurrence</th>
                            <th t-if="question.is_scored_question">Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="question_data['common_lines']" t-as="common_line">
                            <td>
                                <t t-if="question.question_type == 'numerical_box'">
                                    <t t-set="right_answer" t-value="common_line[0] == question.answer_numerical_box"/>
                                    <span t-call="survey.survey_remove_unnecessary_decimals"><t t-set="value_to_format" t-value="common_line[0]"/></span>
                                    <i t-if="right_answer" class="fa fa-check"/>
                                </t>
                                <t t-elif="question.question_type == 'date'">
                                    <t t-set="right_answer" t-value="common_line[0] == question.answer_date"/>
                                    <span t-esc="common_line[0]" t-options='{"widget": "date"}'/>
                                    <i t-if="right_answer" class="fa fa-check"/>
                                </t>
                                <t t-elif="question.question_type == 'datetime'">
                                    <t t-set="right_answer" t-value="common_line[0] == question.answer_datetime"/>
                                    <span t-esc="common_line[0]" t-options='{"widget": "datetime"}'/>
                                    <i t-if="right_answer" class="fa fa-check"/>
                                </t>
                            </td>
                            <td><span t-esc="common_line[1]"></span></td>
                            <td t-if="question.is_scored_question">
                                <span t-if="right_answer" t-call="survey.survey_remove_unnecessary_decimals">
                                    <t t-set="value_to_format" t-value="question.answer_score"/>
                                </span>
                                <span t-else="">0</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div role="tabpanel" class="tab-pane" t-att-id="'survey_data_question_%d' % question.id">
                <table class="table table-hover table-sm" t-att-id="'survey_table_question_%d' % question.id">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>User Responses</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="table_data" t-as="input_line">
                            <tr t-if="not input_line.skipped" t-att-class="'d-none' if input_line_index >= page_record_limit else ''">
                                <td>
                                    <t t-if="no_print_url"><t t-esc="input_line_index + 1"></t></t>
                                    <t t-else="">
                                        <a t-att-href="input_line.user_input_id.get_print_url()">
                                            <t t-esc="input_line_index + 1"></t>
                                        </a>
                                    </t>
                                </td>
                                <td>
                                    <a t-if="question.question_type == 'numerical_box'" t-att-href="input_line.user_input_id.get_print_url()" t-field="input_line.value_numerical_box"/>
                                    <a t-if="question.question_type == 'date'" t-att-href="input_line.user_input_id.get_print_url()" t-field="input_line.value_date"/>
                                    <t t-if="question.question_type == 'datetime'">
                                        <span t-if="no_print_url" t-field="input_line.value_datetime"/>
                                        <a t-else="" t-att-href="input_line.user_input_id.get_print_url()" t-field="input_line.value_datetime"></a>
                                    </t>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
               <t t-call="survey.question_table_pagination"/>
            </div>
        </div>
    </template>

    <template id="question_result_choice" name="Question: choice result (simple_choice, multiple_choice)">
        <ul class="nav nav-tabs d-print-none" role="tablist">
            <li t-if="question_answered" class="nav-item">
                <a t-att-href="'#survey_graph_question_%d' % question.id"
                    t-att-aria-controls="'survey_graph_question_%d' % question.id"
                    class="nav-link active" data-bs-toggle="tab" role="tab">
                    <i class="fa fa-bar-chart-o"></i>
                    <span t-if="question.question_type == 'simple_choice'">Pie Graph</span>
                    <span t-else="">Graph</span>
                </a>
            </li>
            <li class="nav-item">
                <a t-att-href="'#survey_data_question_%d' % question.id"
                   t-att-aria-controls="'survey_data_question_%d' % question.id"
                   t-attf-class="nav-link #{'active' if not question_answered else ''}"
                   data-bs-toggle="tab" role="tab">
                    <i class="fa fa-list-alt me-1"/>Data
                </a>
            </li>
        </ul>
        <div class="tab-content">
            <div t-if="question_answered" role="tabpanel" class="tab-pane active survey_graph"
                t-att-id="'survey_graph_question_%d' % question.id"
                t-att-data-question-id="question.id"
                t-att-data-graph-type="'pie' if question.question_type == 'simple_choice' else 'bar'"
                t-att-data-graph-data="graph_data"
                t-att-data-right-answers="list(question_data['right_answers'].mapped('value'))">
                <!-- canvas element for drawing bar chart -->
                <canvas/>
            </div>
            <div role="tabpanel" t-att-id="'survey_data_question_%d' % question.id"
                 t-attf-class="tab-pane #{'active' if not question_answered else ''}">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Answer</th>
                            <th>User Choice</th>
                            <th t-if="question.is_scored_question">Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="table_data" t-as="choice_data">
                            <td>
                                <p>
                                    <span t-if="question.question_type == 'numerical_box'" t-call="survey.survey_remove_unnecessary_decimals">
                                        <t t-set="value_to_format" t-value="choice_data['value']"/>
                                    </span>
                                    <span t-else="" t-esc="choice_data['value']"/>
                                    <i t-if="question.is_scored_question and choice_data['suggested_answer'].is_correct" class="fa fa-check"/>
                                </p>
                            </td>
                            <td class="o_survey_answer">
                                <span t-esc="round(choice_data['count'] * 100.0/ (len(question_data['answer_line_done_ids']) or 1), 2)"></span> %
                                <span t-out="choice_data['count_text']" class="badge text-bg-primary"/>
                                <i t-if="choice_data['suggested_answer'].id and choice_data['count']"
                                    class="fa fa-filter text-primary filter-add-answer"
                                    t-att-data-question-id="question.id"
                                    t-att-data-answer-id="choice_data['suggested_answer'].id"
                                    role="img" aria-label="Filter surveys" title="Only show survey results having selected this answer"/>
                            </td>
                            <td t-if="question.is_scored_question" t-call="survey.survey_remove_unnecessary_decimals">
                                <t t-set="value_to_format" t-value="choice_data['suggested_answer'].answer_score"/>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div t-if="comment_lines" t-call="survey.question_result_comments" />
    </template>

    <template id="question_result_matrix" name="Question: matrix result (matrix)">
        <t t-set="graph_data" t-value="question_data['graph_data']"/>
        <t t-set="table_data" t-value="question_data['table_data']"/>
        <ul class="nav nav-tabs d-print-none" role="tablist">
            <li t-if="question_answered" class="nav-item active" >
                <a t-att-href="'#survey_graph_question_%d' % question.id"
                    t-att-aria-controls="'survey_graph_question_%d' % question.id"
                    class="nav-link active" data-bs-toggle="tab" role="tab">
                    <i class="fa fa-bar-chart me-1"/>Graph
                </a>
            </li>
            <li class="nav-item">
                <a t-att-href="'#survey_data_question_%d' % question.id"
                    t-att-aria-controls="'survey_data_question_%d' % question.id"
                    t-attf-class="nav-link #{'active' if not question_answered else ''}" data-bs-toggle="tab" role="tab">
                    <i class="fa fa-list-alt me-1"/>Data
                </a>
            </li>
        </ul>
        <div class="tab-content">
            <div t-if="question_answered" role="tabpanel" class="tab-pane active with-3d-shadow with-transitions survey_graph"
                t-att-id="'survey_graph_question_%d' % question.id"
                t-att-data-question-id= "question.id"
                data-graph-type= "multi_bar"
                t-att-data-graph-data="graph_data">
                <!-- canvas element for drawing Multibar chart -->
                <canvas/>
            </div>
            <div role="tabpanel" t-attf-class="tab-pane #{'active' if not question_answered else ''}" t-att-id="'survey_data_question_%d' % question.id">
                <table class="table table-hover table-sm text-end">
                    <thead t-if="table_data">
                        <tr>
                            <th></th>
                            <th class="text-end" t-foreach="table_data[0]['columns']" t-as="column_data">
                                <span t-esc="column_data['suggested_answer'].value"></span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="table_data" t-as="choice_data">
                            <td>
                                <span t-esc="choice_data['row'].value"></span>
                            </td>
                            <td class="o_survey_answer" t-foreach="choice_data['columns']" t-as="column_data">
                                <span t-esc="round(column_data['count'] * 100.0/ (len(question_data['answer_input_done_ids']) or 1), 2)"></span> %
                                <span class="badge text-bg-primary" t-esc="column_data['count']"></span>
                                <i t-if="column_data['count']" class="fa fa-filter text-primary filter-add-answer"
                                   t-att-data-question-id="question.id"
                                   t-att-data-row-id="choice_data['row'].id"
                                   t-att-data-answer-id="column_data['suggested_answer'].id" role="img" aria-label="Filter surveys"
                                   title="Only show survey results having selected this answer"></i>
                                <i t-else="" class="o_survey_answer_matrix_whitespace"/>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        <div t-if="comment_lines" t-call="survey.question_result_comments" />
        </div>
    </template>

    <template id="question_result_comments" name="Question: comments">
        <table class="table table-hover table-sm" t-att-id="'survey_table_question_%d' % question.id">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Comment</th>
                </tr>
            </thead>
            <tbody>
                <tr t-foreach="comment_lines" t-as="input_line">
                    <td>
                        <t t-if="no_print_url"><t t-esc="input_line_index + 1"></t></t>
                        <t t-else="">
                            <a t-att-href="input_line.user_input_id.get_print_url()">
                                <t t-esc="input_line_index + 1"></t>
                            </a>
                        </t>
                    </td>
                    <td>
                        <span t-field="input_line.value_char_box"></span><br/>
                    </td>
                </tr>
            </tbody>
        </table>
    </template>

    <template id="question_table_pagination" name="Survey: statistics table pagination">
        <ul t-att-id="'pagination_%d' % question.id" class="pagination" t-att-data-question_id="question.id" t-att-data-record_limit="page_record_limit">
            <t t-if="len(table_data) > page_record_limit">
                <t t-set="total" t-value="ceil(len(table_data) / page_record_limit) + 1"/>
                <li t-foreach="range(1, total)" t-as="num"
                    t-att-class="'page-item o_survey_js_results_pagination %s' % ('active' if num == 1 else '')">
                    <a href="#" class="page-link" t-esc="num"></a>
                </li>
            </t>
        </ul>
    </template>

    <template id="survey_remove_unnecessary_decimals"  name="Survey: remove .0">
        <t t-esc="value_to_format if value_to_format % 1 else int(value_to_format)"/>
    </template>

</data>
</odoo>
