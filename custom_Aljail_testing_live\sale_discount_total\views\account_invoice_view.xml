<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="discount_account_invoice_view_form" model="ir.ui.view">
            <field name="name">discount.account.invoice</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='discount']" position="attributes">
                    <attribute name="digits">[16, 2]</attribute>
                </xpath>
                <xpath expr="//field[@name='tax_totals']" position="after">
                    <field name="amount_discount"/>
                </xpath>
                <xpath expr="//field[@name='narration']" position="before">
                    <div>
                        <label for="discount_type" string="Discount Type :"/>
                        <field name="discount_type" class="oe_inline"/>
                    </div>
                    <div>
                        <label for="discount_rate" string="Discount Rate :"/>
                        <field name="discount_rate" class="oe_inline"/>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
