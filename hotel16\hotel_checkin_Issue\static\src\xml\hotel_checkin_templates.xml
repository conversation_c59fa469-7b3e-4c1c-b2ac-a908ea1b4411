<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Header Template -->
    <t t-name="HotelCheckinHeader">
        <div class="o_hotel_checkin_header">
            <h2><i class="fa fa-umbrella-beach mr-2"/>
                <t t-esc="resortName"/>
            </h2>
            <div class="o_hotel_checkin_search">
                <input type="text" placeholder="Search villas..." />
                <i class="fa fa-search"/>
            </div>
        </div>
    </t>

    <!-- Villa Card Template - This will be used in the kanban view -->
    <t t-name="HotelCheckinVillaCard">
        <div t-attf-class="villa_header #{record.state.raw_value === 'done' ? 'villa_occupied' : 'villa_available'}">
            <span>
                <t t-esc="record.room_id.value"/>
            </span>
            <span t-attf-class="status_indicator #{record.state.raw_value === 'done' ? 'status_occupied' : 'status_available'}"/>
        </div>
        <div class="villa_content">
            <t t-if="record.state.raw_value === 'done'">
                <!-- Occupied Villa -->
                <div class="guest_name">
                    <t t-esc="record.partner_id.value"/>
                </div>
                <div class="detail_row">
                    <i class="fa fa-calendar-alt"/>
                    <span><t t-esc="record.checkin_date.value"/> - <t t-esc="record.checkout_date.value"/></span>
                </div>
                <div class="detail_row">
                    <i class="fa fa-user"/>
                    <span>Res #: <t t-esc="record.reservation_no.value"/></span>
                </div>
                <div t-if="record.checkout_date.raw_value === moment().format('YYYY-MM-DD')" class="detail_row text-danger">
                    <i class="fa fa-exclamation-triangle"/>
                    <span>Late check-out</span>
                </div>
            </t>
            <t t-else="">
                <!-- Available Villa -->
                <div class="price_tag">
                    <i class="fa fa-tag mr-1"/>
                    <t t-esc="record.price.value"/> / night
                </div>
                <div class="detail_row">
                    <i class="fa fa-bed"/>
                    <span><t t-esc="record.room_type_id.value"/></span>
                </div>
                <div t-if="record.is_premium" class="premium_tag">
                    <i class="fa fa-star mr-1"/>Premium Suite
                </div>
            </t>
        </div>
    </t>
</templates> 