<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_pl_small_pl" model="account.report">
        <field name="name">Rachunek zysków i strat - Mały</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="country_id" ref="base.pl"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="filter_multi_company">selector</field>
        <field name="default_opening_date_filter">this_year</field>
        <field name="column_ids">
            <record id="l10n_pl_small_pl_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_pl_small_pl_a" model="account.report.line">
                <field name="name">A. Przychody netto ze sprzedaży i zrównane z nimi</field>
                <field name="code">pl_small_pl_a</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_a_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_a_1.balance - pl_small_pl_a_2.balance - pl_small_pl_a_3.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_pl_a_1" model="account.report.line">
                        <field name="name">I. Przychody netto ze sprzedaży</field>
                        <field name="code">pl_small_pl_a_1</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-70.0 - 73.0 - 74.0</field>
                    </record>
                    <record id="l10n_pl_small_pl_a_2" model="account.report.line">
                        <field name="name">II. Zmiana stanu produktów</field>
                        <field name="code">pl_small_pl_a_2</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">49</field>
                    </record>
                    <record id="l10n_pl_small_pl_a_3" model="account.report.line">
                        <field name="name">III. Koszt wytworzenia produktów na własne potrzeby jednostki</field>
                        <field name="code">pl_small_pl_a_3</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">79 + 70.1</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_b" model="account.report.line">
                <field name="name">B. Koszty działalności operacyjnej</field>
                <field name="code">pl_small_pl_b</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_b_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_b_1.balance + pl_small_pl_b_2.balance + pl_small_pl_b_3.balance + pl_small_pl_b_4.balance + pl_small_pl_b_5.balance + pl_small_pl_b_6.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_pl_b_1" model="account.report.line">
                        <field name="name">I. Amortyzacja</field>
                        <field name="code">pl_small_pl_b_1</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">42</field>
                    </record>
                    <record id="l10n_pl_small_pl_b_2" model="account.report.line">
                        <field name="name">II. Zużycie materiałów i energii</field>
                        <field name="code">pl_small_pl_b_2</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.01</field>
                    </record>
                    <record id="l10n_pl_small_pl_b_3" model="account.report.line">
                        <field name="name">III. Usługi obce</field>
                        <field name="code">pl_small_pl_b_3</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.02</field>
                    </record>
                    <record id="l10n_pl_small_pl_b_4" model="account.report.line">
                        <field name="name">IV. Wynagrodzenia</field>
                        <field name="code">pl_small_pl_b_4</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.04</field>
                    </record>
                    <record id="l10n_pl_small_pl_b_5" model="account.report.line">
                        <field name="name">V. Ubezpieczenia społeczne i inne świadczenia</field>
                        <field name="code">pl_small_pl_b_5</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">40.05 + 41</field>
                    </record>
                    <record id="l10n_pl_small_pl_b_6" model="account.report.line">
                        <field name="name">VI. Pozostałe koszty</field>
                        <field name="code">pl_small_pl_b_6</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">43 + 5 + 73.1 + 74.1 + 40\(40.01,40.02,40.04,40.05)</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_c" model="account.report.line">
                <field name="name">C. Zysk (strata) ze sprzedaży (A–B)</field>
                <field name="code">pl_small_pl_c</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_c_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_a.balance - pl_small_pl_b.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_d" model="account.report.line">
                <field name="name">D. Pozostałe przychody operacyjne</field>
                <field name="code">pl_small_pl_d</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">-76.0 - 77.0 - 999997</field>
            </record>
            <record id="l10n_pl_small_pl_e" model="account.report.line">
                <field name="name">E. Pozostałe koszty operacyjne</field>
                <field name="code">pl_small_pl_e</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">76.1 + 77.1 + 999998</field>
            </record>
            <record id="l10n_pl_small_pl_f" model="account.report.line">
                <field name="name">F. Przychody finansowe</field>
                <field name="code">pl_small_pl_f</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_f_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_f_1.balance + pl_small_pl_f_2.balance + pl_small_pl_f_3.balance + pl_small_pl_f_4.balance + pl_small_pl_f_5.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_pl_f_1" model="account.report.line">
                        <field name="name">I. Dywidendy i udziały w zyskach od jednostek, w których jednostka posiada zaangażowanie w kapitale</field>
                        <field name="code">pl_small_pl_f_1</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-75.02</field>
                    </record>
                    <record id="l10n_pl_small_pl_f_2" model="account.report.line">
                        <field name="name">II. Odsetki</field>
                        <field name="code">pl_small_pl_f_2</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-75.03</field>
                    </record>
                    <record id="l10n_pl_small_pl_f_3" model="account.report.line">
                        <field name="name">III. Zysk z tytułu rozchodu aktywów finansowych</field>
                        <field name="code">pl_small_pl_f_3</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-75.01 - 75.04</field>
                    </record>
                    <record id="l10n_pl_small_pl_f_4" model="account.report.line">
                        <field name="name">IV. Aktualizacja wartości aktywów finansowych</field>
                        <field name="code">pl_small_pl_f_4</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-75.05</field>
                    </record>
                    <record id="l10n_pl_small_pl_f_5" model="account.report.line">
                        <field name="name">V. Inne</field>
                        <field name="code">pl_small_pl_f_5</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">-75.0\(75.01,75.02,75.03,75.04,75.05)</field>
                        <field name="hide_if_zero" eval="True"/>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_g" model="account.report.line">
                <field name="name">G. Koszty finansowe</field>
                <field name="code">pl_small_pl_g</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_g_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_g_1.balance + pl_small_pl_g_2.balance + pl_small_pl_g_3.balance + pl_small_pl_g_4.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="l10n_pl_small_pl_g_1" model="account.report.line">
                        <field name="name">I. Odsetki</field>
                        <field name="code">pl_small_pl_g_1</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">75.13</field>
                    </record>
                    <record id="l10n_pl_small_pl_g_2" model="account.report.line">
                        <field name="name">II. Strata z tytułu rozchodu aktywów finansowych</field>
                        <field name="code">pl_small_pl_g_2</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">75.11</field>
                    </record>
                    <record id="l10n_pl_small_pl_g_3" model="account.report.line">
                        <field name="name">III. Aktualizacja wartości aktywów finansowych</field>
                        <field name="code">pl_small_pl_g_3</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">75.12</field>
                    </record>
                    <record id="l10n_pl_small_pl_g_4" model="account.report.line">
                        <field name="name">IV. Inne</field>
                        <field name="code">pl_small_pl_g_4</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="account_codes_formula">75.1\(75.11,75.12,75.13)</field>
                        <field name="hide_if_zero" eval="True"/>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_h" model="account.report.line">
                <field name="name">H. Zysk (strata) brutto (C+D–E+F–G)</field>
                <field name="code">pl_small_pl_h</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_h_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_c.balance + pl_small_pl_d.balance - pl_small_pl_e.balance + pl_small_pl_f.balance - pl_small_pl_g.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
            <record id="l10n_pl_small_pl_i" model="account.report.line">
                <field name="name">I. Podatek dochodowy</field>
                <field name="code">pl_small_pl_i</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="account_codes_formula">87</field>
            </record>
            <record id="l10n_pl_small_pl_j" model="account.report.line">
                <field name="name">J. Zysk (strata) netto (H–I)</field>
                <field name="code">pl_small_pl_j</field>
                <field name="expression_ids">
                    <record id="l10n_pl_small_pl_j_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">pl_small_pl_h.balance - pl_small_pl_i.balance</field>
                        <field name="date_scope">normal</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
