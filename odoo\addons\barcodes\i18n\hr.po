# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# <PERSON><PERSON><PERSON> <i<PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <vo<PERSON><PERSON>.<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*' ?"
msgstr "'*' nije ispravan regex barkod uzorak. Jeste li mislili '.*'?"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr ""

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "Dodavanje nove barkod nomenklature"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "Alias"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "Uvijek"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "Interni identifikator barkod nomenklature"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "Bilokoji"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr ""

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "Barkod nomenklatura"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "Barkod nomenklature"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "Barkod uzorak"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "Barkod pravilo"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "Barkod čitač"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Barcode: "
msgstr "Barkod: "

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "EAN-13 u UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Kodna stranica:"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmjeravanje"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "ID"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature____last_update
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "Nikad"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "Nomenklatrua"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the \n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, \n"
"                                the barcode field on the associated records <i>must</i> show these digits as \n"
"                                zeroes."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "Naziv pravila"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "Pravila"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "Tablice"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "Uzorak barkoda za validaciju"

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"The barcode pattern %(pattern)s does not lead to a valid regular expression."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "Popis pravila barkodova"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Ovo pravilo će se primjeniti jedino ako je barcod u specifičnom formatu"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "Vrsta"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "UPC-A u EAN-13"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "UPC/EAN Konverzija"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "Jedinica proizvoda"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Unknown barcode command"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""
"Koristi se za redoslijed pravila tako da pravilo s manjom sekvencom primjeni"
" prije"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Vrijednost posljednjeg skeniranog barkoda."
