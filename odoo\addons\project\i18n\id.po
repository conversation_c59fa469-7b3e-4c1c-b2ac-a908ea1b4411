# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON>, 2022
# arfa si<PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Tidak ada milestone yang tersedia. Ayo buat milestone!\n"
"                </p><p>\n"
"                    Lacak proses poin yang harus diraih untuk mencapai kesuksesan.\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_calendar/project_calendar_controller.js:0
#, python-format
msgid " - Tasks by Deadline"
msgstr "- Tasks berdasarkan Deadline"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "# Kolaborator"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# Rating"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "# Kegiatans"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
#, python-format
msgid "# of Tasks"
msgstr "# Kegiatans"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "#{record.commercial_partner_id.value}"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "#{record.partner_id.value}"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Burndown Chart"
msgstr "Grafik Burndown %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Milestones"
msgstr "Milestone %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Rating"
msgstr "Rating %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Tasks Analysis"
msgstr "Analisis Task %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "%(name)s's Updates"
msgstr "Update %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (salinan)"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ %(child_count)s tasks)"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ 1 task)"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(jatuh tempo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(update project terakhir),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- diraih pada"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
".\n"
"      Attach all documents or links to the task directly, to have all research information centralized."
msgstr ""
".\n"
"      Lampirkan langsung semua dokumen atau link ke task, agar semua informasi penelitian terpusat."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__10
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__10
msgid "10"
msgstr "10"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__11
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__11
msgid "11"
msgstr "11"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__12
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__12
msgid "12"
msgstr "12"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__13
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__13
msgid "13"
msgstr "13"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__14
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__14
msgid "14"
msgstr "14"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__15
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__15
msgid "15"
msgstr "15"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__16
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__16
msgid "16"
msgstr "16"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__17
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__17
msgid "17"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__18
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__18
msgid "18"
msgstr "18"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__19
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__19
msgid "19"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__20
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__20
msgid "20"
msgstr "20"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__21
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__21
msgid "21"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__22
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__22
msgid "22"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__23
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__23
msgid "23"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__24
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__24
msgid "24"
msgstr "24"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__25
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__25
msgid "25"
msgstr "25"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__26
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__26
msgid "26"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__27
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__27
msgid "27"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__28
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__28
msgid "28"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__29
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__29
msgid "29"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__30
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__30
msgid "30"
msgstr "30"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__31
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__31
msgid "31"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr ""
"<b>Drag &amp; drop</b> ke kartu untuk merubah task Anda dari tahap ke tahap."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this task won't be notified \n"
"    of the note you are logging unless you specifically tag them)</i>. Use @ <b>mentions</b> to ping a colleague \n"
"    or # <b>mentions</b> to reach an entire team."
msgstr ""
"<b>Catatan log</b> untuk komunikasi internal <i>(mereka yang mengikuti task ini tidak akan dinotifikasi\n"
"    mengenai catatan yang Anda log kecuali Anda secara spesifik tag mereka)</i>. Gunakan @ <b>mention</b> untuk ping rekan\n"
"    atau # <b>mention</b> untuk mencapai seluruh tim."

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br><br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the task \"<strong t-out=\"object.name or ''\">Planning and budget</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In progress</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">Weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Halo <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Halo,<br><br>\n"
"            </t>\n"
"            Mohon ambil sedikit waktu untuk menilai layanan kami terkait task \"<strong t-out=\"object.name or ''\">Planning dan budget</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                yang ditugaskan ke <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Beritahu kami pendapat Anda mengenai layanan kami</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(klik salah satu emoji ini)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            Kami menghargai feedback Anda. Feedback Anda membantu kami terus berkembang.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Survei pelanggan ini dikirim karena tiket Anda telah dipindahkan ke tahap <b t-out=\"object.stage_id.name or ''\">Sedang berlangsung</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Survei pelanggan ini dikirim <b t-out=\"object.project_id.rating_status_period or ''\">Setiap Minggu</b> selama tugas masih berada dalam tahap <b t-out=\"object.stage_id.name or ''\">Sedang berlangsung</b>.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"    Yth <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Dengan bahagia kami kabarkan bahwa Anda telah menyelesaikan project \"<strong t-out=\"object.name or ''\">Renovasi</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">Anda menerima email ini karena project Anda telah dimasukkan ke tahap <b t-out=\"object.stage_id.name or ''\">Selesai</b></span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Thank you for your enquiry.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Yth <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Terima kasih untuk pertanyaan Anda.<br>\n"
"    Bila Anda memiliki pertanyaan tambahan apapun, mohon beritahu kami.\n"
"    <br><br>\n"
"    Terima kasih,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_avg', '&lt;', 3.66)]}\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_avg', '&lt;', 2.33), ('rating_avg', '&gt;=', 3.66)]}\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_avg', '&gt;=', 2.33)]}\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_avg', '<', 3.66)]}\" title=\"Puas\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_avg', '&lt;', 2.33), ('rating_avg', '&gt;=', 3.66)]}\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_avg', '>=', 2.33)]}\" title=\"Tidak Puas\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> Privat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Pelanggan dinonaktifkan pada "
"project\"/><b> Rating Pelanggan</b> dinonaktifkan untuk project berikut : "
"<br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "<p><em>Number of tasks: %(tasks_count)s</em></p>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">Tahap:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">of</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"Tanggal\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Alias Domain\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Mitra\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Kolaborator\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Blocking</span>"
msgstr "<span class=\"o_stat_text\">Blocking</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">Rating Terakhir</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text\">Sub-tasks</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">Task</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">in Recurrence</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Laporan</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Lihat</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">Lampiran</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_planned_hours_template
msgid "<strong>Allocated Hours:</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assignees</strong>"
msgstr "<strong>Petugas</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Customer</strong>"
msgstr "<strong>Pelanggan</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Deadline:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Description</strong>"
msgstr "<strong>Deskripsi</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>Pesan dan riwayat komunikasi</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>Milestone:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Project:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>Milestones</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Kamus Python yang akan dievaluasi untuk memberikan nilai baku ketika membuat"
" catatan baru untuk alias ini."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"Kolaborator tidak dapat dipilih lebih dari satu kali di akses sharing "
"project. Mohon hapus duplikat dan coba lagi."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "A new task will be created on the following dates:"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"Tahap pribadi tidak dapat di-link ke project karena hanya tampak ke user "
"yang terkait."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "Tag dengan nama yang sama sudah tersedia."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "Task hanya dapat memiliki satu tahap pribadi per user."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Menerima Email Dari"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_instruction_message
msgid "Access Instruction Message"
msgstr "Pesan Instruksi Akses"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "Mode Akses"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Peringatan akses"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "Aktif"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Aktivitas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Add Milestone"
msgstr "Tambahkan Milestone"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "Tambahkan keterangan..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "Tambahkan catatan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"Tambahkan kolom-kolom untuk mengatur tugas-tugas Anda menjadi <b>tahap-"
"tahap</b> <i>contohnya Baru - Sedang Berlangsung - Selesai</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "Tambahkan kontak untuk membagi project..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Tambahkan konten ekstra untuk ditampilkan di email"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "Tambahkan task Anda setelah siap."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Administrator"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "Agile Scrum"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Kontak Keamanan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Nama Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr "Domain alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_value
msgid "Alias email"
msgstr "Alias email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Model Alias"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Semua"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "Semua user internal"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__all
msgid "All tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__planned_hours
msgid "Allocated Hours"
msgstr "Jam yang Dialokasikan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_rating
msgid "Allow Customer Ratings"
msgstr "Izinkan Rating Pelanggan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allow_subtasks
msgid "Allow Sub-tasks"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "Akun Analitik"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
#: model:ir.model.fields,help:project.field_project_task__project_analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""
"Akun analitik di mana project, tugas dan timesheet-nya terhubung. \n"
"Lacak biaya dan pendapatan dari project Anda dengan menyetel akun analitik ini pada dokumen Anda yang terkait (contohnya sales order, faktur, purchase order, tagihan vendor, pengeluaran dsb.).\n"
"Akun analitik ini dapat dirubah pada setiap tugas secara individu bila diperlukan.\n"
"Akun analitik diperlukan agar bisa menggunakan timesheet."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task and its timesheets are linked.\n"
"Track the costs and revenues of your task by setting its analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"By default, the analytic account of the project is set. However, it can be changed on each task individually if necessary."
msgstr ""
"Akun analitik di mana tugas dan timesheet-nya terhubung. Lacak biaya Lacak "
"biaya dan pendapatan dari tugas Anda dengan menyetel akun analitik-nya pada "
"dokumen Anda yang terkait contohnya sales order, faktur, purchase order, "
"tagihan vendor, pengeluaran dsb.). Secara default, akun analitik project ini"
" sudah disetel. Namun, akun tersebut dapat dirubah untuk setiap tugas secara"
" individu bila diperlukan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Analitik"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"Analisa kecepatan tim Anda dalam menyelesaikan task project dan periksa "
"apakah semuanya berjalan sesuai rencana."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "Analisa progress project Anda dan performa karyawan Anda."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__ancestor_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__ancestor_id
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Ancestor Task"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__april
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__april
msgid "April"
msgstr "April"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Arsip"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "Arsip Tahap"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "Diarsipkan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Archived tasks cannot be recurring. Please unarchive the task first."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "Apakah Anda yakin ingin melanjutkan?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete those stages ?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "Tanda Panah"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "Ikon Tanda Panah"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "Perakitan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Assign a responsible to your task"
msgstr "Tetapkan penanggung jawab untuk task Anda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Assign each new project to this plan"
msgstr "Tetapkan project baru ke rencana ini"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "Ditugaskan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Assigned On: %s"
msgstr "Ditetapkan Pada: %s"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "Kegiatan yang ditetapkan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Ditugaskan untuk"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Assignees"
msgstr "Petugas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Menetapkan tanggal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Tanggal Tugas"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "Beresiko"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage, employees can block tasks or mark them as ready for the next step.\n"
"                                    You can customize here the labels for each state."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "Lampiran yang tidak datang dari pesan."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__august
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__august
msgid "August"
msgstr "Agustus"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Penulis"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "Buat otomatis task untuk kegiatan reguler"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic Kanban Status"
msgstr "Status Kanban Otomatis"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating"
msgstr "Rata-Rata Rating"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Rating Rata-Rata (%)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "Rating Rata-Rata: Tidak Puas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "Rating Rata-Rata: Okay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "Rating Rata-Rata: Puas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "Backlog"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Saldo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Billed"
msgstr "Billing"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
msgid "Block"
msgstr "Blokir"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_6
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_6
#: model:project.task.type,legend_blocked:project.project_stage_0
#: model:project.task.type,legend_blocked:project.project_stage_2
#: model:project.task.type,legend_blocked:project.project_stage_3
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Blocked"
msgstr "Diblokir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "Diblokir Oleh"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Blocking"
msgstr "Blokir"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "Brainstor"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "Bug"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "Grafik Burndown"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "Buzz atau set seperti yang dilakukan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__can_be_marked_as_done
msgid "Can Be Marked As Done"
msgstr "Dapat Ditandai Sebagai Selesai"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Canceled"
msgstr "Dibatalkan"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Cannot aggregate field %r."
msgstr "Tidak dapat menjumlah keseluruhan baris %r."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__partner_is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "Periksa bila kontak merupakan perusahaan, bila tidak maka orang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__child_text
msgid "Child Text"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the name of a customer,\n"
"     of a product, of a team, of a construction site, etc.</i>"
msgstr ""
"Pilih <b>nama</b> untuk project Anda. <i>Dapat merupakan apapun yang Anda inginkan: nama pelanggan,\n"
"     produk, tim, lokasi konstruksi, dsb.</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr "Pilih nama <b>task</b> <i>(contoh Website Design, Beli Barang...)</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr "Kota"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "Review Klien"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Closed"
msgstr "Ditutup"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Last 30 Days"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Last 7 Days"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Closed Tasks"
msgstr "Task Tertutup"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closing Stage"
msgstr "Tahap penutupan"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid ""
"Collaborate efficiently with key stakeholders by sharing with them the "
"Kanban view of your tasks. Collaborators will be able to edit parts of tasks"
" and send messages."
msgstr ""
"Kolaborasi secara efisien dengan stakeholder penting dengan membagi ke "
"mereka tampilan Kanban task Anda. Kolaborator akan dapat mengedit bagian-"
"bagian task dan mengirimkan pesan."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "Kolaborator"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
#, python-format
msgid "Collaborators"
msgstr "Kolaborator-kolaborator"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Kolaborator dalam project yang di-share"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"Kumpulkan feedback dari pelanggan Anda dengan mengirimkan mereka permintaan rating saat task mencapai tahapan tertentu. Untuk melakukan ini, definisikan templat rating email ke tahapan yang sesuai.\n"
"Rating saat merubah tahap: email akan secara otomatis dikirim saat task mencapai tahap di mana templat rating email ditetapkan.\n"
"Rating berkala: email akan secara otomatis dikirim pada selang reguler selama task masih dalam tahap di mana templat rating email ditetapkan."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "Warna"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__commercial_partner_id
#: model:ir.model.fields,field_description:project.field_project_task__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entitas Komersial"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt. Organize priorities amongst orders using the"
msgstr ""
"Berkomunikasi dengan pelanggan pada tugas menggunakan gateway email. Lampirkan desain logo pada tugas, supaya informasi mengalir dari\n"
"      desainer ke pekerja yang akan mencetak t-shirt. Atur prioritas di antara pesanan menggunakan"

#. module: project
#: model:ir.model,name:project.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Konfigurasi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "Konfigurasi Tahap"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "Konfirmasi"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_task_type_delete.py:0
#, python-format
msgid "Confirmation"
msgstr "Konfirmasi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Congratulations, you are now a master of project management."
msgstr "Selamat, Anda sekarang adalah master manajemen project."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "Konsultasi"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Kontak"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_stop_recurrence_confirmation_dialog/project_stop_recurrence_confirmation_dialog.xml:0
#, python-format
msgid "Continue Recurrence"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Copywriting"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Costs"
msgstr "Biaya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Cover Image"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr ""
"Buat <b>kegiatan</b> untuk memberikan diri Anda to-do atau menjadwalkan "
"meeting."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Tanggal Dibuat"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "Buat Proyek"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "Buat tahap baru di pipeline task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "Buat project"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"Buat project untuk mengatur tugas-tugas Anda dan definisikan workflow "
"berbeda untuk setiap project."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""
"Buat project untuk mengatur tugas-tugas Anda. Definisikan workflow berbeda "
"untuk setiap project."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "Buat task dengan mengirimkan email ke"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "Buat task dengan mengirim email ke alamat email project Anda."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Dibuat Pada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr "Tanggal Pembuatan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "Project task saat ini"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current stage of the task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Tahap saat ini untuk task ini"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"Saat ini tersedia utnuk semua orang yang melihat dokumen ini, klik untuk "
"membatasi hanya ke karyawan internal."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"Saat ini terbatas hanya untuk karyawan internal, klik di sini untuk "
"membuatnya tersedia untuk semua orang yang melihat dokumen ini."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Pesan Kustom yang Dikembalikan"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Customer"
msgstr "Pelanggan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr "Email Pelanggan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "Feedback Pelanggan"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "Customer Portal URL"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "Rating Pelanggan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "Status Rating Pelanggan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly. Your managers decide which feedback is accepted"
msgstr ""
"Pelanggan mengajukan feedback melalui email; Odoo membuat task seacara otomatis, dan Anda dapat\n"
"      berkomunikasi langsung pada task. Manajer Anda menentukan feedback mana yang diterima"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customers will be added to the followers of their project and tasks."
msgstr "Pelanggan akan ditambahkan ke pengikut project dan task mereka."

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Customize how tasks are named according to the project and create tailor "
"made status messages for each step of the workflow. It helps to document "
"your workflow: what should be done at which step."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Harian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
#, python-format
msgid "Date"
msgstr "Tanggal"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "Date and Stage"
msgstr "Tanggal dan Tahap"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__date
msgid "Date of the Month"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__date
msgid "Date of the Year"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the stage of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage to another."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"Tanggal pada mana project ini berakhir. Jangka waktu yang didefinisikan pada"
" project diperhitungkan saat melihat rencananya."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"Tanggal pada mana task ini terakhir ditetapkan (atau tidak ditetapkan). "
"Berdasarkan ini, Anda akan mendapatkan statistik mengenai waktu yang "
"biasanya digunakan untuk menetapkan task. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_weekday
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_weekday
msgid "Day Of The Week"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__day
msgid "Day of the Month"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__day
msgid "Day of the Year"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Hari"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "Hari ke Deadline"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Deadline"
msgstr "Batas waktu"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Deadline: %s"
msgstr "Deadline: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Kepada"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__december
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__december
msgid "December"
msgstr "Desember"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_company__analytic_plan_id
#: model:ir.model.fields,field_description:project.field_res_config_settings__analytic_plan_id
msgid "Default Plan"
msgstr "Default Plan"

#. module: project
#: model:ir.model.fields,help:project.field_res_company__analytic_plan_id
#: model:ir.model.fields,help:project.field_res_config_settings__analytic_plan_id
msgid "Default Plan for a new analytic account for projects"
msgstr "Rencana Default untuk akun analitik baru untuk project"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Nilai Baku"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Menentukan langkah-langkah yang akan digunakan dalam proyek dari penciptaan tugas, hingga penutupan tugas atau masalah.\n"
"                Anda akan menggunakan tahap ini untuk melacak kemajuan dalam menyelesaikan sebuah tugas atau masalah."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr ""
"Definisikan langkah-langkah pergerakan project Anda dari pembuatan sampai "
"selesai."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr ""
"Definisikan langkah-langkah pergerakan task Anda dari pembuatan sampai "
"selesai."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete"
msgstr "Hapus"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "Hapus Meilstone"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
#, python-format
msgid "Delete Stage"
msgstr "Hapus Tahap"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"Kirimkan layanan Anda secara otomatis saat milestone diraih dengan "
"menghubungkannya ke item sales order."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "Sudah Terkirim"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "Task yang Tergantung"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Deskripsi"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr ""
"Keterangan utnuk menyediakan lebih banyak informasi dan konteks mengenai "
"project ini"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "Desain"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "Tentukan urutan dalam melakukan task"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "Pengembangan"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Singkat"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "Digital Marketing"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "Nonaktifkan Peringatan Rating"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "Buang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "Display Access Mode"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_parent_task_button
msgid "Display Parent Task Button"
msgstr "Tampilkan Tombol Task Induk"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_project_id
msgid "Display Project"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__ko
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Dissatisfied"
msgstr "Tidak puas"

#. module: project
#. odoo-python
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Tidak punya akses, lewati data ini untuk email singkat pengguna"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "Selesai"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "Draft"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit"
msgstr "Edit"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_renderer.js:0
#, python-format
msgid "Edit Personal Stage"
msgstr "Edit Tahap Pribadi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Edit recurring task"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "Edit"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_email
#: model:ir.model.fields,field_description:project.field_project_project__partner_email
#: model:ir.model.fields,field_description:project.field_project_task__partner_email
msgid "Email"
msgstr "Email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email From"
msgstr "Email Dari"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "Template Email"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""
"Alamat email yang dalam CC email masuk dari task ini dan yang saat ini tidak"
" di-link ke pelanggan."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "Email dikirim k"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Employees Only"
msgstr "Hanya Karyawan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "Tanggal Berakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Tanggal Akhir"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "Error! Anda tidak dapat membuat hirarki task yang rekursif."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr ""
"Siapapun dapat mengajukan ide, dan Editor akan menandai yang terbaik sebagai"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Expected"
msgstr "Diharapkan margin * 100 / diharapkan dijual"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "Eksperimen"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Tanggal Kedaluwarsa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "Penyaring Tambahan"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "Eksternal"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Info Tambahan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "Favorit"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__february
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__february
msgid "February"
msgstr "Februari"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid ""
"Field %s is not a stored field, only stored fields (regular or many2many) "
"are valid for the 'groupby' parameter"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "Dokumen Final"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__first
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__first
msgid "First"
msgstr "Pertama"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "Dilipat di Kanban"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__is_closed
#: model:ir.model.fields,help:project.field_report_project_task_user__is_closed
msgid "Folded in Kanban stages are closing stages."
msgstr "Dilipat di tahap Kanban berarti tahap penutup."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"Ikuti proyek ini untuk secara otomatis melacak aktivitas terkait dengan "
"kegiatan dan masalah dari proyek ini."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr "Diikuti"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Followed Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "Update yang Diikuti"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Mitra)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "Selamanya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__fri
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__fri
msgid "Fri"
msgstr "Jum"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__fri
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__fri
msgid "Friday"
msgstr "Jumat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "GTD"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"Dapatkan gambaran status project Anda dan bagikan perkembangan dengan "
"stakeholder penting."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr "Dapatkan feedback pelanggan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Grant portal users access to your project or tasks by adding them as "
"followers."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr "Label Kanban Hijau"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr "Label Kanban Abu-Abu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the"
msgstr ""
"Tangani pengumpulan ide Anda dalam Task di Project baru Anda dan diskusikan "
"mereka di chatter task. Gunakan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "Handoff"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "Muka bahagia"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__has_late_and_unreached_milestone
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__has_late_and_unreached_milestone
msgid "Has Late And Unreached Milestone"
msgstr "Memiliki Milestone yang Terlambat dan Belum Tercapai"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "Tinggi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "High Priority"
msgstr "Prioritas Tinggi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "Riwayat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Jam"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "Bagaimana kemajuan project ini?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID dari catatan induk alias (contoh: proyek yang menahan pembuatan alias "
"tugas)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Gagasan"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"Bila diaktifkan, tahap ini akan ditampilkan terlipat di tampilan Kanban "
"project Anda. Project yang dalam tahap terlipat dianggap sebagai tertutup."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your tasks. Tasks in a folded stage are considered as closed (not applicable"
" to personal stages)."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"Bila ditetapkan, permintaan rating akan secara otomatis dikirim melalui email ke pelanggan saat task mencapai task ini.\n"
"Jika tidak, akan dikirim dalam selang reguler selama task berada di tahap ini, tergantung pada konfigurasi project Anda. \n"
"Untuk menggunakan fitur ini pastikan bawha opsi 'Rating Pelanggan' diaktifkan pada project Anda."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""
"Bila diaktifkan, email akan secara otomatis dikirim ke pelanggan saat "
"project mencapai tahap ini."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""
"Bila diaktifkan, email akan dikirim secara otomatis ke pelanggan saat task "
"mencapai tahap ini."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jika di tetapkan/atur, konten ini akan secara otomatis dikirimkan untuk "
"pengguna tanpa otorisasi daripada dari pesan bawaan/default."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"Jika bidang aktif diatur ke False, itu akan memungkinkan Anda untuk "
"menyembunyikan proyek tanpa menghapus itu."

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_0
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_2
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_3
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_4
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_5
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_6
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_0
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_1
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_2
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_3
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_4
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_5
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_6
#: model:project.task.type,legend_normal:project.project_stage_0
#: model:project.task.type,legend_normal:project.project_stage_1
#: model:project.task.type,legend_normal:project.project_stage_2
#: model:project.task.type,legend_normal:project.project_stage_3
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "Dalam Proses"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "Dalam perkembangan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "Kotak Pesan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Initially Planned Hours"
msgstr "Awalnya direncanakan jam"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "Internal"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal Note"
msgstr "Catatan Internal"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Internal email yang terkait dengan proyek ini. Email masuk secara otomatis "
"disinkronisasi dengan tugas (atau opsional masalah jika pelacak isu modul "
"diinstal)."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "Catatan internal hanya ditampilkan ke user internal."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid aggregation function %r."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid field %r on model %r"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Invalid field specification %r."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr "Operator tidak valid: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "Value tidak valid: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "Undang Orang"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Invoiced"
msgstr "Telah Difakturkan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_analytic_account_id_changed
msgid "Is Analytic Account Manually Changed"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_blocked
msgid "Is Blocked"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "Apakah Deadline Terlewat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "Apakah Deadline Di Masa Depan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_milestone_exceeded
msgid "Is Milestone Exceeded"
msgstr "Apakah Milestone Terlewat"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_reached
msgid "Is Milestone Reached"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_private
msgid "Is Private"
msgstr "Apakah Privat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_is_company
msgid "Is a Company"
msgstr "Perusahaan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Versi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid "It seems that some tasks are part of a recurrence."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid ""
"It seems that some tasks are part of a recurrence. At least one of them must"
" be kept as a model to create the next occurences."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid "It seems that this task is part of a recurrence."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/services/project_task_recurrence.js:0
#, python-format
msgid ""
"It seems that this task is recurrent. Would you like to stop its recurrence?"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__january
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__january
msgid "January"
msgstr "Januari"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__july
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__july
msgid "July"
msgstr "Juli"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__june
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__june
msgid "June"
msgstr "Juni"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Kanban diblokir penjelasan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanban berkelanjutan penjelasan"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Kanban State"
msgstr "Status Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr "Label Status Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban berlaku penjelasan"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Tetap lacak perkembangan task Anda dari pembuatan sampai selesai.<br>\n"
"                    Berkolaborasi secara efisien dengan berbicara secara real-time atau melalui email."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Tetap lacak perkembangan task Anda dari pembuatan sampai selesai.<br>\n"
"                Berkolaborasi secara efisien dengan berbicara secara real-time atau melalui email."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "Kpi Project Task Opened Value"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__last
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__last
msgid "Last"
msgstr "Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator____last_update
#: model:ir.model.fields,field_description:project.field_project_milestone____last_update
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_project_stage____last_update
#: model:ir.model.fields,field_description:project.field_project_share_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_recurrence____last_update
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_update____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Bulan Lalu"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "Update Tahap Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "Terakhir diperbarui"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "Warna Update Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "Status Updater Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "Update Terakhir Pada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Milestones"
msgstr "Milestone Terlambat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Tasks"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "Nanti"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Leave a comment"
msgstr "Tinggalkan komentar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "Ayo buat <b>project</b> pertama Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>stage</b>."
msgstr "Mari membuat <b>tahap</b> pertama Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "Ayo buat <b>task</b> pertama Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your second <b>stage</b>."
msgstr "Mari membuat <b>tahap</b> kedua Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"Ayo kembali ke <b>tampilan kanban</b> untuk mendapatkan gambaran umum task "
"Anda yang berikutnya."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "Ayo mulai mengerjakan task Anda."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "Mari menunggu pelanggan Anda datang sendiri."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Link"
msgstr "Tautan..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "Live"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "Desain Logo"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Rendah"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Low Priority"
msgstr "Prioritas Rendah"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_update__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "Lampiran Utama"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"Kelola siklus hidup project Anda menggunakan tampilan kanban. Tambahkan project yang baru saja diperoleh,\n"
"      tugaskan mereka dan gunakanassign them and use the"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "Produksi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__march
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__march
msgid "March"
msgstr "Maret"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Margin"
msgstr "Margin"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "Sourcing Material"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__may
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__may
msgid "May"
msgstr "Mei"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""
"Ukur kepuasan pelanggan Anda dengan mengirimkan permintaan rating saat task "
"Anda mencapai tahap tertentu."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "Anggota"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "Pesan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Milestone"
msgstr "Milestone"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "Jumlah Milestone"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count_reached
msgid "Milestone Count Reached"
msgstr "Jumlah Milestone Tercapai"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_deadline
msgid "Milestone Deadline"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_project_task__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Milestones"
msgstr "Milestone"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "Mixing"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__mon
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__mon
msgid "Mon"
msgstr "Sen"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__mon
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__mon
msgid "Monday"
msgstr "Senin"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Bulan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Favorite Projects"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Favorit Saya"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Private Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Projects"
msgstr "Proyek"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr " Kegiatanku"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "Update Saya"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "Nama"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "Nama Di-crop"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__name
msgid ""
"Name of your project. It can be anything you want e.g. the name of a "
"customer or a service."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""
"Nama yang digunakan untuk merujuk ke task dari project Anda contoh task, "
"tiket, sprint, dsb..."

#. module: project
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "Butuh bantuan fungsional atau teknis"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "Muka netral"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#, python-format
msgid "New"
msgstr "Baru"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "Fitur Baru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#, python-format
msgid "New Milestone"
msgstr "Milestone Baru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "Order Baru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "Project Baru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "Permintaan Baru"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Terbaru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Next"
msgstr "Berikutnya"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr "Aktivitas Berikutnya"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Next Activity"
msgstr "Aktivitas Berikutnya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__next_recurrence_date
msgid "Next Recurrence Date"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_message
msgid "Next Recurrencies"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "Tidak ada Pelanggan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "Tidak ada Milestone"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "No Rating"
msgstr "Tidak Ada Rating"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__none
msgid "No Rating yet"
msgstr "Belum ada Rating"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr "Tidak Ada Judul"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "Tidak ada tipe kegiatan yang ditemukan. Ayo buat baru!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid "No collaborators found"
msgstr "Tidak ada kolaborator yang ditemukan"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "Belum ada rating pelanggan"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "Belum ada data!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "Tidak ada project yang ditemukan. Ayo buat baru!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "Tidak ada tahap yang ditemukan. Ayo buat baru!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "Tidak ada tah yang ditemukan. Ayo buat baru!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "Tidak ada task yang ditemukan. Ayo buat baru!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "Tidak ada update yang ditemukan. Ayo buat baru!"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Tidak Ada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Not Blocked"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Not Blocking"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Not Implemented."
msgstr "Tidak Diimplementasi."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Catatan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__november
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__november
msgid "November"
msgstr "November"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__after
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__after
msgid "Number of Repetitions"
msgstr "Jumlah Pengulangan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "Jumlah Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__recurrence_left
msgid "Number of Tasks Left to Create"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "Jumlah dokumen yang dilampirkan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan tindakan"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__october
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__october
msgid "October"
msgstr "Oktober"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "Keluar dari Jalur"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "Desain Kantor"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__ok
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Okay"
msgstr "Okay"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "Sprint Lama yang Selesai"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "Tertahan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "Sesuai Jalur"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "Sekali Sebulan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "Oops! Terjadi kesalahan. Coba muat ulang halaman dan log in."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Open"
msgstr "Terbuka"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Open Tasks"
msgstr "Buka Task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Open tasks"
msgstr "Buka task"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Operation should be = or != (not %s)"
msgstr "Operasi harusnya = or != (bukan %s)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID Opsional dari file (catatan) di mana semua pesan masuk akan dilampirkan, "
"bahkan jika mereka tidak menjawabnya. Jika diatur, hal ini akan "
"menonaktifkan pembuatan catatan baru sepenuhnya."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Atur tugas-tugas Anda dengan mengirimkan mereka di seluruh pipeline.<br>\n"
"                    Kolaborasi efisien dengan mengobrol secara real-time atau melalui email."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "Lainnya"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Output name %r is used twice."
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Kegiatan-kegiatan overpassed"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr "Pemilik"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "Ide Halaman"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Model Induk"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Catatan Induk ID File"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr "Task Induk"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Model induk dengan alias. Model yang memiliki referensi alias ini tidak "
"selalu berarti model yang diberikan oleh alias_model_id (contoh: proyek "
"(parent_model) dan tugas (model))"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"Orang yang dapat melihat project ini dan tugasnya.\n"
"\n"
"- User internal yang diundang: saat mengikuti project, user internal mendapatkan akses ke semua task. Jika tidak, mereka hanya mendapatkan akses ke tugas-tugas yang mereka ikuti.\n"
" User dengan hak akses project > administrator masih dapat mengakses project ini dan tugasnya, bahkan bila mereka tidak termasuk sebagai follower.\n"
"\n"
"- Semua user internal: semua user internal dapat mengakses project dan semua tugasnya.\n"
"\n"
"- User portal yang diundang dan semua user internal: semua user internal dapat mengakses project dan semua tugasnya.\n"
"Saat mengikuti project, user portal akan dapat mengakses semua tugasnya. Jika tidak, mereka hanya mendapatkan akses ke tugas-tugas yang mereka ikuti.\n"
"\n"
"Saat project dibagikan dalam format read-only, user portal akan dialihkan ke portal mereka. Mereka dapat melihat tugasnya, tapi tidak dapat mengedit.\n"
"Saat project dibagikan dalam format bisa diedit, user portal akan dialihkan ke tampilan daftar dan kanban dari tugas mereka. Mereka dapat memodifikasi field-field terpilih tertentu pada tugas mereka.\n"
"\n"
"Dalam kasus apapun juga, user internal tanpa hak akses project masih dapat mengakses tugas, asalkan mereka diberikan URL yang sesuai (dan mereka merupakan bagian dari follower bila project bersifat privat."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Persentase rating bahagia"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodic rating"
msgstr "Rating berkala"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Personal Stage"
msgstr "Tahap Pribadi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "Status Tahap Pribadi"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "Status Task Pribadi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
msgid "Personal User Stage"
msgstr "Tahap User Pribadi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_phone
#: model:ir.model.fields,field_description:project.field_project_task__partner_phone
msgid "Phone"
msgstr "Telepon"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr ""
"Rencanakan alokasi sumber daya di seluruh project dan perkirakan deadline "
"dengan lebih akurat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Planned Date"
msgstr "Tanggal Yang Direncanakan"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Planning"
msgstr "Perencanaan"

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Silakan Hapus tugas-tugas yang ada dalam proyek yang terkait dengan akun "
"yang ingin Anda hapus."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "Produksi Podcast dan Video"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Kebijakan untuk mengirimkan pesan di dokumen menggunakan mailgateway.\n"
"- semua orang: setiap orang dapat mengirim\n"
"- rekanan: hanya rekanan yang diijinkan\n"
"- pengikut: hanya pengikut dokumen terkait\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "Portal Access URL"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "Nama User Portal"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "User portal akan dihapus dari pengikut project dan tasknya."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Previous"
msgstr "Sebelumnya"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Prioritize Tasks by using the"
msgstr "Prioritaskan Task dengan menggunakan"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#, python-format
msgid "Priority"
msgstr "Prioritas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "Prioritas: {{'Important' if task.priority == '1' else 'Normal'}}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr "Peringatan Visibilitas Privasi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_private_task_many2one_field/project_private_task_many2one_field.xml:0
#: code:addons/project/static/src/components/project_private_task_many2one_field/project_private_task_many2one_field.xml:0
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Private"
msgstr "Pribadi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Profitability"
msgstr "Profitabilitas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Kemajuan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "Persentase Progress"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
#, python-format
msgid "Project"
msgstr "Proyek"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_analytic_account_id
msgid "Project Analytic Account"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "Kolaborator Project"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_color
msgid "Project Color"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "Jumlah Proyek"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Manajer Proyek"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "Milestone Project"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Nama Proyek"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "Status Rating Projec"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "Project Dishare"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "Sharing Project"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "Sharing Project: Task"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "Tahap Project"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "Tahap Project Diganti"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "Wizard Penghapusan Tahap Project"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "Tahap Project"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "Tag Project"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Kegiatan-kegiatan proyek"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "Update Project"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "Update Project"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "Visibilitas Project"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "Keterangan project..."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "Status project - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Kegiatan-kegiatan proyek"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_recurring_tasks_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_recurring_tasks
msgid "Project: Create Recurring Tasks"
msgstr ""

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "Project: Project Selesai"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "Project: Pengakuan Permintaan"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr "Project: Kirim rating"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "Project: Permintaan Rating Task"

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_tags__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#, python-format
msgid "Projects"
msgstr "Proyek"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr ""
"Project memiliki task pada topik yang sama, dan masing-masing memiliki "
"dashboardnya sendiri."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"Project di mana tahap ini tersedia. Bila Anda mengikuti workflow yang mirip "
"di beberapa project, Anda dapat membagikan tahap ini di antara mereka dan "
"menkonsolidasi informasi dengan cara ini."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
msgid "Properties"
msgstr "Properti"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "Terpublikasi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.js:0
#, python-format
msgid "Published on %s"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "Publikasi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Triwulanan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Rating"
msgstr "Rating"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "Rating (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "Menilai Rata-Rata Teks"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "Templat Rating Email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "Frekuensi Rating"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Nilai Feedback Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Nilai Gambar Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_text
msgid "Rating Last Text"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Nilai Value Terakhir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "Deadline Permintaan Rating"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Nilai Kepuasan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "Nilai Teks"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#, python-format
msgid "Rating Value (/5)"
msgstr "Nilai Rating (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Jumlah nilai"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr "Rating saat mengganti tahap"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "Mencapai"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "Reached Date"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "Readonly"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
#: model:project.task.type,legend_done:project.project_personal_stage_admin_0
#: model:project.task.type,legend_done:project.project_personal_stage_admin_1
#: model:project.task.type,legend_done:project.project_personal_stage_admin_2
#: model:project.task.type,legend_done:project.project_personal_stage_admin_3
#: model:project.task.type,legend_done:project.project_personal_stage_admin_4
#: model:project.task.type,legend_done:project.project_personal_stage_admin_5
#: model:project.task.type,legend_done:project.project_personal_stage_admin_6
#: model:project.task.type,legend_done:project.project_personal_stage_demo_0
#: model:project.task.type,legend_done:project.project_personal_stage_demo_1
#: model:project.task.type,legend_done:project.project_personal_stage_demo_2
#: model:project.task.type,legend_done:project.project_personal_stage_demo_3
#: model:project.task.type,legend_done:project.project_personal_stage_demo_4
#: model:project.task.type,legend_done:project.project_personal_stage_demo_5
#: model:project.task.type,legend_done:project.project_personal_stage_demo_6
#: model:project.task.type,legend_done:project.project_stage_0
#: model:project.task.type,legend_done:project.project_stage_2
#, python-format
msgid "Ready"
msgstr "Siap"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for Next Stage"
msgstr "Siap untuk Tahap Berikutnya"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "Siap untuk membuka kembali"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "Penerimaan {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Penerima"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Rekam ID Thread"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "Pencatatan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "Pengulangan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_update
msgid "Recurrence Update"
msgstr "Update Pengulangan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurrent"
msgstr "Berulang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_project_task__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
msgid "Recurring Tasks"
msgstr "Task Berulang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr "Label Kanban Merah"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "Ditolak"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Dokumen Terkait"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "ID Dokumen Terkait"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Model Dokumen Terkait"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "Renovasi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_day
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_day
msgid "Repeat Day"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Ulangi Setiap"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_month
msgid "Repeat Month"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Repeat On"
msgstr "Ulang Pada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_month
msgid "Repeat On Month"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_year
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_year
msgid "Repeat On Year"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_day
msgid "Repeat Show Day"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_dow
msgid "Repeat Show Dow"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_month
msgid "Repeat Show Month"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_week
msgid "Repeat Show Week"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "Unit Berulang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_week
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_week
msgid "Repeat Week"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_number
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_number
msgid "Repetitions"
msgstr "Pengulangan-Pengulangan"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Laporan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "Penelitian"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "Penelitian & Pengembangan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "Project Penelitian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "Penelitian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "Alokasi Sumber Daya"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Revenues"
msgstr "Pendapatan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "Muka sedih"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sat
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sat
msgid "Sat"
msgstr "Sab"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Satisfaction"
msgstr "Kepuasan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__rating_last_text__top
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Satisfied"
msgstr "Puas"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sat
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sat
msgid "Saturday"
msgstr "Sabtu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Schedule your activity once it is ready."
msgstr "Jadwalkan kegiatan Anda setelah siap."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "Script"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "Cari <span class=\"nolabel\"> (di Konten)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Cari proyek"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "Cari Update"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Cari di Semua"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "Cari pada Petugas"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "Cari di Pesan"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Milestone"
msgstr "Cari di Milestone"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "Cari di Prioritas"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Cari di Project"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "Cari di Referensi"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "Cari di Tahap"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "Cari di Status"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__second
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__second
msgid "Second"
msgstr "Kedua"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Token Keamanan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#, python-format
msgid "Send"
msgstr "Kirim"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "Kirim Email"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__september
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__september
msgid "September"
msgstr "September"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Foto Cover"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
#, python-format
msgid "Set Status"
msgstr "Tetapkan Status"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "Tetapkan Templat Rating Email pada Tahap"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""
"Tetapkan tahap project ini untuk menginformasi pelanggan saat project "
"mencapai tahap tersebut"

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""
"Tetapkan templat ini pada tahap project untuk memiinta feedback project dari"
" pelanggan Anda. Aktifkan fitur \"rating pelanggan\" pada projec"

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"Tetapkan templat pada tahap project untuk mengotomatiskan email saat task "
"sampai di tahap"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "Pengaturan"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "Bagikan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "Share Editable"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "Share Projec"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Readonly"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "Should repeat at least once"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "Tunjukkan Project pada Dashboard"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "Sejak"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "Software Development"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "Maaf. Anda tidak dapat menetapkan task sebagai induk task-nya."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "Spesifikasi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "Sprint Backlog"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "Sprint Selesai"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "Sprint sedang Berlangsung"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr "Tahapan"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Tahapan Diubah"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "Tahap Deskripsi dan Tooltips"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "Pemilik Tahap"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Tahapan diubah"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "Tahap Aktif"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Tahap Untuk Dihapu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Stalling for 30 Days+"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "Tanggal Mulai"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "Status"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Status Update - "
msgstr "Status Update - "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_stop_recurrence_confirmation_dialog/project_stop_recurrence_confirmation_dialog.xml:0
#, python-format
msgid "Stop Recurrence"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "Jumlah Sub-tas"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_project__allow_subtasks
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "Sub-task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Sub-tasks Planned Hours"
msgstr "Jam Sub-task yang Direncanakan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "Diberikan Pada"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"Jumlah jam yang dialokasikan untuk semua sub-task (dan sub-task mereka) yang"
" di-link ke task ini. Biasanya lebih sedikit dari atau sama dengan jam yang "
"dialokasikan untuk task ini."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "Ringkasan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sun
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sun
msgid "Sun"
msgstr "Min"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sun
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sun
msgid "Sunday"
msgstr "Minggu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "Pencetakan T-shirt"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
msgid "Tags"
msgstr "Label"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Task"
msgstr "Kegiatan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "Kegiatan kegiatan"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "Kegiatan yang diblokir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr "Jumlah Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count_with_subtasks
msgid "Task Count With Subtasks"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "Task Dibuat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "Ketergantungan Task"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_dependency_change
#: model:mail.message.subtype,name:project.mt_task_dependency_change
msgid "Task Dependency Changes"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Log Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "Properti Task"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "Rating Task"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "Tugas siap"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Pengulangan Task"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Tahap kegiatan"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Tahap kegiatan berubah"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "Tahap Task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "Tugas judul"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Tugas judul..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "Kegiatan yang diblokir"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task description..."
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_task_progress
msgid "Task in Progress"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr ""
"Tugas sedang dalam progres. Klik untuk blok atau tetapkan sebagai selesai."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"Tugas diblok. Klik untuk menghilangkan blokir atau tetapkan sebagai selesai."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "Tugas siap untuk tahap berikutnya"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "Task:"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_milestone__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_project_tags__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
#, python-format
msgid "Tasks"
msgstr "Kegiatan"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.report_project_task_user_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Kegiatan analisis"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Tasks Due Today"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "Manajemen Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Tahap kegiatan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
#, python-format
msgid "Tasks in Recurrence"
msgstr "Task pada Pengulangan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "Tes"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "The Burndown Chart must be grouped by"
msgstr "Grafik Burndown harus dikelompokkan berdasarkan"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "Tahap pribadi user saat ini."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "Tahap task pribadi user saat ini."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid ""
"The end date should be after the day of the month or the last day of the "
"month"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The end date should be in the future"
msgstr "Tanggal akhir harus di masa depan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "Milestone berikut telah ditambahkan:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "Milestone berikut telah ditambahkan:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The interval should be greater than 0"
msgstr "Interval harus lebih dari 0"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Model (Jenis Dokumen Odoo) di mana alias ini dikaitkan. Email masuk yang "
"tidak menjawab catatan yang sudah ada akan menyebabkan pembuatan rekor baru "
"model ini (misalnya tugas proyek)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Nama email alias, misalnya 'pekerjaan' jika Anda ingin menangkap email untuk"
" <<EMAIL>>"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Pemilik catatan yang dibuat setelah menerima email pada alias ini. Jika "
"kolom ini belum ditetapkan, sistem akan berusaha untuk menemukan pemilik "
"yang tepat berdasarkan pengirim (dari) alamat, atau akan menggunakan akun "
"Administrator jika tidak ada pengguna sistem yang ditemukan untuk alamat "
"tersebut."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"Project tidak dapat dibagi dengan penerima karena privasi project terlalu "
"terbatas. Tetapkan privasi menjadi 'Terlihat oleh pelanggan berikut' agar "
"dapat diakses oleh penerima."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "Tanggal mulai project harus sebelum tanggal akhir."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "The search does not support the %s operator or %s value."
msgstr "Pencarian tidak mendukung operator %s atau value %s."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"Task tidak dapat dibagi dengan penerima karena privasi task terlalu "
"terbatas. Tetapkan privasi menjadi 'Terlihat oleh pelanggan berikut' agar "
"dapat diakses oleh penerima."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "The view must be grouped by date and by stage_id"
msgstr "Tampilan harus dikelompokkan berdasarkan tanggal dan stage_id"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "Tidak ada komentar saat ini."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "There are no more occurrences."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "Tidak ada project."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "Saat ini tidak ada rating untuk project ini "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "Tidak ada task."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "There is nothing to report."
msgstr "Tidak ada yang perlu dilaporkan."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr "Orang-orang ini akan menerima email."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__third
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__third
msgid "Third"
msgstr "Ketiga"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "Bulan Ini"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "Pekan Ini"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__subsequent
msgid "This and following tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr "Langkah ini sudah selesai. Klik untuk blok atau mulai progres."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__this
msgid "This task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"Ini akan mengarsip tahap dan semua task yang mereka miliki dari project "
"berikut:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Mereka mewakili kategori-kategori berbeda untuk kegiatan yang harus Anda "
"lakukan (contohnya \"Telepon\" atau \"Kirim email\")."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__thu
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__thu
msgid "Thu"
msgstr "Kam"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__thu
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__thu
msgid "Thursday"
msgstr "Kamis"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "Manajemen Waktu"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "Tip: Buat task dari email masuk"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Customize tasks and stages according to the project"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#, python-format
msgid "Title"
msgstr "Judul"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Bill"
msgstr "Untuk Ditagih"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Todo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Invoice"
msgstr "Siap Difakturkan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "Untuk Dicetak"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"Untuk menyelesaikan tugas, gunakan kegiatan dan status pada task.<br>\n"
"                Chat secara real-time atau melalui email untuk berkolaborasi dengan efisien."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "Hari Ini"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "Lacak kepuasan pelanggan terhadap tugas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr ""
"Lacak poin perkembangan utama yang harus diraih untuk mencapai kesuksesan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"Lacak poin perkembangan utama yang harus diraih untuk mencapai kesuksesan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your projects. Any project, its tasks and "
"timesheets are linked to an analytic account and any analytic account "
"belongs to a plan."
msgstr ""
"Lacak profitabilitas project Anda. Project, task dan timesheet apapun akan "
"di-linhk ke akun analitik dan akun analitik apapun yang dimiliki rencana."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "Lacak kemajuan project Anda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "Lacak waktu yang digunakan pada project dan task"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""
"Tag transparan tidak terlihat pada tampilan kanban project dan task Anda."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__tue
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__tue
msgid "Tue"
msgstr "Sel"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__tue
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__tue
msgid "Tuesday"
msgstr "Selasa"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "Dua Kali Sebulan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Two tasks cannot depend on each other."
msgstr "Dua task tidak dapat bergantung satu sama lain"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_list/project_task_list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Batalkan Pengarsipan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unarchive Tasks"
msgstr "Batalkan Arsip Task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Unassign Me"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "Tak diserah-tugaskan"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "Akun Analitik Tidak Diketahui"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "Unknown field %r in 'groupby'"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
msgid "Until"
msgstr "Hingga"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "Perbaharui"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "Update Dibuat"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "Kegunaan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use"
msgstr "Gunakan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_enabled
msgid "Use Email Alias"
msgstr "Gunakan Alias Email"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "Gunakan Milestone"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Gunakan Rating pada Project"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "Gunakan Task Berulang"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "Gunakan Tahap pada Projec"

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "Gunakan Ketergantungan Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Menggunakan tugas sebagai"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "Gunakan Ini Untuk Project Saya"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "Gunakan tags untuk mengkategorikan taks Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use the"
msgstr "Gunakan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the main changes about this task."
msgstr ""
"Gunakan chatter untuk <b>mengirim email</b> dan berkomunikasi secara efisien dengan pelanggan Anda.\n"
"    Tambahkan orang baru ke daftar pengikut agar mereka menyadari perubahan utama mengenai task ini."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "Pengguna"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "Value should be True or False (not %s)"
msgstr "Value harus True atau False (bukan %s)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "Tampilan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "View Task"
msgstr "Lihat Task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
msgid "View Tasks"
msgstr "Lihat Task"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Visibilitas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Visible"
msgstr "Terlihat"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"Ingin cara lebih baik untuk <b>mengelola project Anda</b>? <i>Semuanya "
"dimulai di sini.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Pesan situs"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "Desain Ulang Website"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi situs"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__wed
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__wed
msgid "Wed"
msgstr "Rab"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__wed
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__wed
msgid "Wednesday"
msgstr "Rabu"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Mingguan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Minggu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "Hari Kerja untuk Ditugaskan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "Hari Kerja untuk Ditutup"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "Jam Kerja untuk Ditugaskan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "Jam Kerja untuk Ditutup"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "Waktu Kerja"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "Waktu Kerja untuk Ditugaskan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "Waktu Kerja untuk Ditutup"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr ""
"Apakah Anda kiga ingin membatalkan arsip semua task yang didalam tahap-tahap"
" ini?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "Tulisan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Tahunan"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Tahun"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Anda juga dapat menambahkan keterangan untuk membantu rekan kerja Anda "
"memahami arti dan tujuan panggung."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__tag_ids
msgid ""
"You can only see tags that are already present in your project. If you try "
"creating a tag that is already existing in other projects, it won't generate"
" any duplicates."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You can only set a personal stage on a private task."
msgstr "Anda hanya dapat menetapkan tahap pribadi pada task privat."

#. module: project
#. odoo-python
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"You cannot change the company of an analytic account if it is related to a "
"project."
msgstr ""
"Anda tidak dapat merubah perusahaan dari akun analitik bila terkait ke "
"project."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete recurring tasks. Please disable the recurrence first."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"Anda tidak dapat menghapus tahap yang memiliki task. Anda harus mengarsip "
"mereka atau menghapus semua task mereka terlebih dahulu."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"Anda tidak dapat menghapus stage yang memiliki task. Anda harus terlebih "
"dahulu semua task mereka."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "Anda tidak dapat membaca field %s di task."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "Anda tidak dapat menulis pada field %s di task."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Anda telah ditugaskan ke %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Anda telah ditugaskan ke"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "Anda tidak dapat menulis akses field %s."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""
"Anda memiliki perubahan yang tidak disimpan - jangan khawatir! Odoo akan "
"secara otomatis menyimpannya selagi Anda bernavigasi.<br/> Anda dapat "
"membuang perubahan ini dari sini atau secara manual simpan task "
"Anda.<br/>Ayo simpan secara manual."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "You must be"
msgstr "Anda haru"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You should at least have one personal stage. Create a new stage to which the"
" tasks can be transferred after this one is deleted."
msgstr ""
"Andah harus memiliki setidaknya satu tahap pribadi. Buat tahap baru tempat "
"task dapat ditransfer setelah yang ini dihapus."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "You should select a least one day"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "and"
msgstr "dan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"dan feedback mana yang\n"
"      dipindahkan ke kolom \"Ditolak\"."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignee"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "ditugasi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "avatar"
msgstr "avatar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "bullets to indicate the status of a task."
msgstr "lingkaran untuk mengindikasikan status task."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"button to inform your colleagues that a task is ready for the next stage."
msgstr ""
"tombol untuk menginformasikan rekan Anda task siap untuk tahap berikutnya."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "comments"
msgstr "komentar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "contoh Tinjauan bulanan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr "contoh Office Party"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "contoh Peluncuran Produk"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "contoh Kirim Undangan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "contoh office-party"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "contoh: Peluncuran Produk"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon to organize your daily activities."
msgstr "ikon untuk mengatur kegiatan sehari-hari Anda."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon."
msgstr "ikon."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "logged in"
msgstr "logged in"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "project."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "siap untuk ditandai sebagai tercapai"

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr "task"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "deadline untuk meilstone berikut telah diupdate:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "deadline untuk milestone berikut telah diupdate:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"untuk mendefinisikan apakah project\n"
"      siap untuk langkah berikutnya."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to indicate a problem or a need for discussion on a task."
msgstr ""
"untuk mengindikasikan masalah atau kebutuhan untuk didiskusikan pada task."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "to post a comment."
msgstr "untuk posting komentar."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to signalize what is the current status of your Idea."
msgstr "untuk memberikan signal apa status saat ini untuk Ide Anda."

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "akan membuat task di "

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "{{ object.project_id.company_id.name }}: Satisfaction Survey"
msgstr "{{ object.project_id.company_id.name }}: Survei Kepuasan"
