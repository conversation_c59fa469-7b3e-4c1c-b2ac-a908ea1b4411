# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_3way_match
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 10:05+0000\n"
"PO-Revision-Date: 2018-09-18 10:05+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice__release_to_pay_manual
msgid ""
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities."
msgstr ""
"  * Ναι: πρέπει να πληρώσετε το λογαριασμό, έχετε λάβει τα προϊόντα\n"
"  * Όχι, δεν πρέπει να πληρώσετε το λογαριασμό, δεν έχετε λάβει τα προϊόντα\n"
"  * Εξαίρεση, υπάρχει διαφορά μεταξύ των ληφθέντων και των τιμολογημένων ποσοτήτων"

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills in Exception"
msgstr "Λογαριασμοί σε Εξαίρεση"

#. module: account_3way_match
#: model_terms:ir.ui.view,arch_db:account_3way_match.account_invoice_filter_inherit_account_3way_match
msgid "Bills to Pay"
msgstr "Λογαριασμοί για Πληρωμή"

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "Exception"
msgstr "Εξαίρεση"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice__force_release_to_pay
msgid "Force status"
msgstr "Κατάσταση Εξαναγκασμού"

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice__force_release_to_pay
msgid ""
"Indicates whether the 'Can be paid' status is defined automatically or "
"manually."
msgstr ""
"Υποδεικνύει εάν η κατάσταση \"Μπορεί να πληρωθεί\" ορίζεται αυτόματα ή "
"χειροκίνητα."

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_invoice
msgid "Invoice"
msgstr "Τιμολόγιο"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_invoice_line
msgid "Invoice Line"
msgstr "Γραμμή Τιμολογίου"

#. module: account_3way_match
#: model:ir.model,name:account_3way_match.model_account_journal
msgid "Journal"
msgstr "Ημερολόγιο"

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "No"
msgstr "Όχι"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice_line__can_be_paid
msgid "Release to Pay"
msgstr "Άδεια για Πληρωμή"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice__release_to_pay
msgid "Should be paid"
msgstr "Πρέπει να πληρωθεί"

#. module: account_3way_match
#: model:ir.model.fields,field_description:account_3way_match.field_account_invoice__release_to_pay_manual
msgid "Should be paid Manual"
msgstr ""

#. module: account_3way_match
#: model:ir.model.fields,help:account_3way_match.field_account_invoice__release_to_pay
msgid ""
"This field can take the following values :\n"
"  * Yes: you should pay the bill, you have received the products\n"
"  * No, you should not pay the bill, you have not received the products\n"
"  * Exception, there is a difference between received and billed quantities\n"
"This status is defined automatically, but you can force it by ticking the 'Force Status' checkbox."
msgstr ""
"Αυτό το πεδίο μπορεί να λάβει τις ακόλουθες αξίες :\n"
"  * Ναι: πρέπει να πληρώσετε το λογαριασμό, έχετε λάβει τα προϊόντα\n"
"  * Όχι, δεν πρέπει να πληρώσετε το λογαριασμό, δεν έχετε λάβει τα προϊόντα\n"
"  * Εξαίρεση, υπάρχει διαφορά μεταξύ των ληφθέντων και των τιμολογημένων ποσοτήτων\n"
"Αυτή η κατάσταση ορίζεται αυτόματα, αλλά μπορείτε να την αλλάξετε επιλέγοντας το κουτάκι \"Εξαναγκασμός Κατάστασης\"."

#. module: account_3way_match
#: selection:account.invoice,release_to_pay:0
#: selection:account.invoice,release_to_pay_manual:0
#: selection:account.invoice.line,can_be_paid:0
msgid "Yes"
msgstr "Ναι"
