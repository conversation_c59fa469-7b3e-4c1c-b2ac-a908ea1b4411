<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_invoice_form" model="ir.ui.view">
        <field name="model">account.move</field>
        <field name="name">account.move.edi.form</field>
        <field name="inherit_id" ref="account.view_move_form" />
        <field name="arch" type="xml">
            <xpath expr="//page[@id='other_tab']" position="inside">
                <group id="l10n_pe_group">
                    <group
                        string="Peru"
                        name="l10n_pe_group"
                        attrs="{'invisible': ['|', '|', '|', ('country_code', '!=', 'PE'), ('move_type', '!=', ('in_invoice')), ('l10n_latam_document_type_id', '=', False), ('l10n_latam_document_type_id_code', 'in', ['91', '97', '98'])]}"
                    >
                        <field name="l10n_latam_document_type_id_code" invisible="1"/>
                        <field name="l10n_pe_detraction_number" />
                        <field name="l10n_pe_detraction_date" />
                    </group>
                    <group
                        string="Peru"
                        name="l10n_pe_group_foreign"
                        attrs="{'invisible': ['|', '|', '|', ('country_code', '!=', 'PE'), ('move_type', '!=', ('in_invoice')), ('l10n_latam_document_type_id', '=', False), ('l10n_latam_document_type_id_code', 'not in', ['91', '97', '98'])]}"
                    >
                        <field name="l10n_pe_dua_invoice_id" options="{'no_create': True}" />
                        <field name="l10n_pe_service_modality" />
                        <field name="l10n_pe_usage_type_id" options="{'no_create': True}" />
                    </group>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
