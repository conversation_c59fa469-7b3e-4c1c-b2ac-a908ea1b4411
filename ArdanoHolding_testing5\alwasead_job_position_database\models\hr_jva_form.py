# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class HrJvaForm(models.Model):
    _name = 'hr.jva.form'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Job Vacancy Announcement Form'
    _order = 'create_date desc'

    name = fields.Char(
        string='JVA Reference',
        required=True,
        default=lambda self: self.env['ir.sequence'].next_by_code('hr.jva.form') or _('New'),
        readonly=True
    )

    # Auto-populated from Job Description Form
    job_id = fields.Many2one(
        'hr.job', 
        string='Job Position',
        required=True,
        readonly=True,
        states={'draft': [('readonly', False)]}
    )
    job_title = fields.Char(
        string='Job Title',
        related='job_id.name',
        readonly=True
    )
    division_id = fields.Many2one(
        'hr.division',
        string='Division',
        related='job_id.division_id',
        readonly=True
    )
    department_id = fields.Many2one(
        'hr.department',
        string='Department',
        related='job_id.department_id',
        readonly=True
    )
    unit_id = fields.Many2one(
        'hr.unit',
        string='Unit/Team',
        related='job_id.unit_id',
        readonly=True
    )
    job_category_id = fields.Many2one(
        'hr.job.category',
        string='Job Category',
        related='job_id.job_category_id',
        readonly=True
    )
    minimum_requirements = fields.Text(
        string='Minimum Requirements',
        related='job_id.education_background',
        readonly=True
    )

    # Department Head Input Fields
    requested_positions = fields.Integer(
        string='Number of Positions Requested',
        required=True,
        readonly=True,
        states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]}
    )
    suggested_positions = fields.Integer(
        string='Suggested Positions',
        compute='_compute_suggested_positions',
        help="Auto-calculated: Required - Current employees"
    )
    additional_qualifications = fields.Text(
        string='Additional Desired Qualifications',
        readonly=True,
        states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]}
    )
    language_requirements = fields.Text(
        string='Required Language Knowledge & Level',
        readonly=True,
        states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]}
    )
    experience_level = fields.Selection([
        ('trainee', 'Trainee'),
        ('intern', 'Intern'),
        ('junior', 'Junior'),
        ('lead', 'Lead'),
        ('senior', 'Senior'),
        ('associate_consultant', 'Associate Consultant'),
        ('lead_consultant', 'Lead Consultant')
    ], string='Required Experience Level',
       readonly=True,
       states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]})
    
    justification = fields.Text(
        string='Justification/Comments',
        readonly=True,
        states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]}
    )
    priority = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent')
    ], string='Priority Level', 
       default='medium',
       readonly=True,
       states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]})
    
    expected_start_date = fields.Date(
        string='Expected Start Date',
        readonly=True,
        states={'draft': [('readonly', False)], 'rejected': [('readonly', False)]}
    )

    # Workflow Fields
    state = fields.Selection([
        ('draft', 'Draft'),
        ('approval_pending', 'Approval Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    # Tracking Fields
    requested_by = fields.Many2one(
        'hr.employee',
        string='Requested By',
        default=lambda self: self.env.user.employee_id,
        readonly=True
    )
    request_date = fields.Datetime(
        string='Request Date',
        default=fields.Datetime.now,
        readonly=True
    )
    submitted_date = fields.Datetime(string='Submitted Date', readonly=True)
    reviewed_by = fields.Many2one(
        'hr.employee',
        string='Reviewed By (General Manager)',
        readonly=True
    )
    review_date = fields.Datetime(string='Review Date', readonly=True)
    approved_by = fields.Many2one('hr.employee', string='Approved By', readonly=True)
    approval_date = fields.Datetime(string='Approval Date', readonly=True)
    rejected_by = fields.Many2one('hr.employee', string='Rejected By', readonly=True)
    rejection_date = fields.Datetime(string='Rejection Date', readonly=True)
    rejection_reason = fields.Text(string='Rejection Reason', readonly=True)

    # Computed Fields
    can_request_approval = fields.Boolean(
        string='Can Request Approval',
        compute='_compute_can_request_approval'
    )
    is_editable = fields.Boolean(
        string='Is Editable',
        compute='_compute_is_editable'
    )

    @api.depends('job_id', 'job_id.required_positions', 'job_id.current_employees')
    def _compute_suggested_positions(self):
        for record in self:
            if record.job_id:
                record.suggested_positions = max(0, record.job_id.required_positions - record.job_id.current_employees)
            else:
                record.suggested_positions = 0

    @api.depends('state', 'requested_positions', 'job_id')
    def _compute_can_request_approval(self):
        for record in self:
            record.can_request_approval = (
                record.state == 'draft' and
                record.requested_positions > 0 and
                record.job_id
            )

    @api.depends('state')
    def _compute_is_editable(self):
        for record in self:
            record.is_editable = record.state in ('draft', 'rejected')

    def action_request_approval(self):
        """Submit JVA for General Manager approval"""
        if not self.can_request_approval:
            raise UserError(_('JVA Form is not ready for approval. Please complete all required fields.'))

        self.write({
            'state': 'approval_pending',
            'submitted_date': fields.Datetime.now()
        })

        # Send notification to General Manager
        self._send_approval_notification()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('JVA Submitted'),
                'message': _('JVA Form has been submitted to General Manager for approval.'),
                'type': 'success'
            }
        }

    def action_approve(self):
        """General Manager approves the JVA"""
        if self.state != 'approval_pending':
            raise UserError(_('Only pending JVA forms can be approved.'))

        self.write({
            'state': 'approved',
            'approved_by': self.env.user.employee_id.id,
            'approval_date': fields.Datetime.now()
        })

        # Notify Recruiter and Department Head
        self._send_approval_result_notification(approved=True)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('JVA Approved'),
                'message': _('JVA Form has been approved. Recruitment can now proceed.'),
                'type': 'success'
            }
        }

    def action_reject(self):
        """General Manager rejects the JVA"""
        if self.state != 'approval_pending':
            raise UserError(_('Only pending JVA forms can be rejected.'))

        # Open wizard for rejection reason
        return {
            'type': 'ir.actions.act_window',
            'name': _('Reject JVA'),
            'res_model': 'hr.jva.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_jva_id': self.id}
        }

    def action_reset_to_draft(self):
        """Reset rejected JVA to draft for revision"""
        if self.state != 'rejected':
            raise UserError(_('Only rejected JVA forms can be reset to draft.'))

        self.write({
            'state': 'draft',
            'rejection_reason': False,
            'rejected_by': False,
            'rejection_date': False
        })

    def action_cancel(self):
        """Cancel JVA form"""
        if self.state in ('approved', 'cancelled'):
            raise UserError(_('Cannot cancel approved or already cancelled JVA forms.'))
        
        self.write({'state': 'cancelled'})

    def _send_approval_notification(self):
        """Send email notification to General Manager"""
        template = self.env.ref('alwasead_job_position_database.email_template_jva_approval_request', False)
        if template:
            template.send_mail(self.id, force_send=True)

    def _send_approval_result_notification(self, approved=True):
        """Send notification after approval/rejection"""
        if approved:
            template = self.env.ref('alwasead_job_position_database.email_template_jva_approved', False)
        else:
            template = self.env.ref('alwasead_job_position_database.email_template_jva_rejected', False)
        
        if template:
            template.send_mail(self.id, force_send=True)

    @api.constrains('requested_positions')
    def _check_requested_positions(self):
        for record in self:
            if record.requested_positions < 1:
                raise ValidationError(_('Number of requested positions must be at least 1.'))

    @api.onchange('job_id')
    def _onchange_job_id(self):
        if self.job_id:
            self.requested_positions = self.suggested_positions
