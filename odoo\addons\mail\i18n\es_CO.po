# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-11-30 13:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Colombia) (https://www.transifex.com/odoo/teams/41243/es_CO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail
#: model:mail.template,body_html:mail.mail_template_data_notification_email_default
msgid ""
"\n"
"% set company = ctx.get('company', user.company_id)\n"
"<div>\n"
"% if ctx.get('has_button_access'):\n"
"<div itemscope itemtype=\"http://schema.org/EmailMessage\">\n"
"    <div itemprop=\"potentialAction\" itemscope itemtype=\"http://schema.org/ViewAction\">\n"
"        <link itemprop=\"target\" href=\"${ctx['button_access']['url']}\"/>\n"
"        <link itemprop=\"url\" href=\"${ctx['button_access']['url']}\"/>\n"
"        <meta itemprop=\"name\" content=\"View ${ctx['model_name']}\"/>\n"
"    </div>\n"
"</div>\n"
"% endif\n"
"% if not ctx['is_discussion'] or not len(ctx['actions']) == 0 or ctx.get('has_button_access'):\n"
"<div summary=\"o_mail_notification\" style=\"padding: 0px; width:600px;\">\n"
"<table cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 600px; margin-top: 5px;\">\n"
"<tbody><tr>\n"
"    <td valign=\"center\">\n"
"        % if ctx.get('has_button_access'):\n"
"        <a href=\"${ctx['button_access']['url']}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">${ctx['button_access']['title']}</a>\n"
"        % endif\n"
"\n"
"        % if ctx.get('has_button_follow'):\n"
"        % if ctx.get('has_button_access'):\n"
"           |\n"
"        % endif\n"
"        <a href=\"${ctx['button_follow']['url']}\" style=\"color: #875A7B; text-decoration: none !important;\">${ctx['button_follow']['title']}</a>\n"
"        % elif ctx.get('has_button_unfollow'):\n"
"        % if ctx.get('has_button_access'):\n"
"           |\n"
"        % endif\n"
"        <a href=\"${ctx['button_unfollow']['url']}\" style=\"color: #875A7B; text-decoration: none !important;\">${ctx['button_unfollow']['title']}</a>\n"
"        % endif\n"
"\n"
"        % if ctx.get('actions'):\n"
"        % for action in ctx['actions']:\n"
"        |\n"
"        <a href=\"${action['url']}\" style=\"color: #875A7B; text-decoration:none !important;\">${action['title']}</a>\n"
"        % endfor\n"
"        % endif\n"
"    </td>\n"
"    <td valign=\"center\" align=\"right\">\n"
"        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; max-width: 80px; max-height: 40px;\" alt=\"${company.name}\">\n"
"    </td>\n"
"</tr>\n"
"<tr>\n"
"    <td colspan=\"2\" style=\"text-align:center;\">\n"
"        <hr width=\"100%\"\n"
"            style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:10px 0;\">\n"
"        % if ctx.get('subtype') and ctx.get('subtype').internal:\n"
"        <p style=\"background-color: #f2dede; padding: 5px; margin-bottom: 16px;\">\n"
"            <strong>Internal communication</strong>: Replying will post an internal note. Followers won't receive any email notification.\n"
"        </p>\n"
"        % endif\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"% endif\n"
"<div>\n"
"    ${object.body | safe}\n"
"</div>\n"
"% if ctx.get('tracking'):\n"
"    <ul>\n"
"    % for tracking in ctx['tracking']\n"
"        <li>${tracking[0]} : ${tracking[1]} -&gt; ${tracking[2]}</li>\n"
"    % endfor\n"
"    </ul>\n"
"% endif\n"
"\n"
"% if ctx.get('signature'):\n"
"    ${ctx['signature'] | safe}\n"
"% endif\n"
"<br/>\n"
"<p style=\"color: #555555;\">\n"
"    Sent by\n"
"    % if ctx.get('website_url'):\n"
"    <a href=\"${ctx['website_url']}\" style=\"text-decoration:none; color: #875A7B;\">\n"
"    % endif\n"
"    ${ctx.get('company_name')}\n"
"    % if ctx.get('website_url'):\n"
"    </a>\n"
"    % endif\n"
"    using\n"
"    <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"text-decoration:none; color: #875A7B;\">Odoo</a>.\n"
"</p>\n"
"</div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:674
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_days
msgid "# Days"
msgstr ""

#. module: mail
#: model:mail.template,subject:mail.mail_template_data_notification_email_default
msgid ""
"${object.subject or (object.record_name and 'Re: %s' % object.record_name) "
"or (object.parent_id and object.parent_id.subject and 'Re: %s' % "
"object.parent_id.subject) or (object.parent_id and "
"object.parent_id.record_name and 'Re: %s' % object.parent_id.record_name)}"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:63
#, python-format
msgid "%d Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:32
#, python-format
msgid "%d days overdue"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:248
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:236
#, python-format
msgid "%s created"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:77
#, python-format
msgid "%s has joined the %s network."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:211
#, python-format
msgid "&nbsp;("
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:179
#, python-format
msgid "(from"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode_shortcode_type
msgid ""
"* Smiley are only used for HTML code to display an image * Text (default "
"value) is used to substitute text with another text"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:49
#, python-format
msgid ", due on"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:331
#, python-format
msgid "-------- Show older messages --------"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:78
#, python-format
msgid "0 Future"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:74
#, python-format
msgid "0 Late"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:76
#, python-format
msgid "0 Today"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:678
#, python-format
msgid ""
"<br><br>\n"
"            You can mention someone by typing <b>@username</b>, this will grab its attention.<br>\n"
"            You can mention a channel by typing <b>#channel</b>.<br>\n"
"            You can execute a command by typing <b>/command</b>.<br>\n"
"            You can insert canned responses in your message by typing <b>:shortcut</b>.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:584
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:508
#: code:addons/mail/models/mail_channel.py:561
#, python-format
msgid ""
"<div class=\"o_mail_notification\">joined <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:182
#, python-format
msgid ""
"<div class=\"o_mail_notification\">left <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/invite.py:23
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow %s document: %s.</p></div>"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/invite.py:26
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow a new document.</p></div>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:28
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:15
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:11
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong> Feedback</strong>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel_alias_defaults
#: model:ir.model.fields,help:mail.field_mail_test_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_needaction
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_needaction
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_needaction
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_needaction
#: model:ir.model.fields,field_description:mail.field_account_payment_message_needaction
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_needaction
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_needaction
#: model:ir.model.fields,field_description:mail.field_blog_post_message_needaction
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_needaction
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_needaction
#: model:ir.model.fields,field_description:mail.field_crm_team_message_needaction
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_needaction
#: model:ir.model.fields,field_description:mail.field_event_event_message_needaction
#: model:ir.model.fields,field_description:mail.field_event_registration_message_needaction
#: model:ir.model.fields,field_description:mail.field_event_track_message_needaction
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_needaction
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_needaction
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_needaction
#: model:ir.model.fields,field_description:mail.field_forum_post_message_needaction
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_needaction
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_needaction
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_department_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_needaction
#: model:ir.model.fields,field_description:mail.field_hr_job_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_test_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_needaction
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_needaction
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_needaction
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_needaction
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_needaction
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_needaction
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_needaction
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_needaction
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_needaction
#: model:ir.model.fields,field_description:mail.field_note_note_message_needaction
#: model:ir.model.fields,field_description:mail.field_product_product_message_needaction
#: model:ir.model.fields,field_description:mail.field_product_template_message_needaction
#: model:ir.model.fields,field_description:mail.field_project_project_message_needaction
#: model:ir.model.fields,field_description:mail.field_project_task_message_needaction
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_needaction
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner_message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users_message_needaction
#: model:ir.model.fields,field_description:mail.field_sale_order_message_needaction
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_needaction
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_needaction
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_needaction
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_needaction
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_needaction
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_needaction
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_needaction
msgid "Action Needed"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_default
msgid "Activated by default when subscribing."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_active_domain
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_active_domain
msgid "Active domain"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:87
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_ids
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_ids
#: model:ir.model.fields,field_description:mail.field_event_track_activity_ids
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_ids
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_ids
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_ids
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_ids
#: model:ir.model.fields,field_description:mail.field_note_note_activity_ids
#: model:ir.model.fields,field_description:mail.field_product_product_activity_ids
#: model:ir.model.fields,field_description:mail.field_product_template_activity_ids
#: model:ir.model.fields,field_description:mail.field_project_task_activity_ids
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users_activity_ids
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_ids
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model:mail.message.subtype,name:mail.mt_activities
#, python-format
msgid "Activities"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:60
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_mail_activity_activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Activity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:37
#, python-format
msgid "Activity type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:87
#, python-format
msgid "Add"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:64
#: model:ir.model.fields,field_description:mail.field_ir_act_server_channel_ids
#, python-format
msgid "Add Channels"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:61
#: model:ir.model.fields,field_description:mail.field_ir_act_server_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_actions.py:36
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_user_signature
#: model:ir.model.fields,field_description:mail.field_mail_template_user_signature
msgid "Add Signature"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:41
#: code:addons/mail/static/src/xml/client_action.xml:186
#, python-format
msgid "Add a channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:60
#, python-format
msgid "Add a private channel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add channels to notify..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_partner_ids
msgid "Additional Contacts"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_id
#: model:ir.model.fields,field_description:mail.field_res_users_alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_contact
#: model:ir.model.fields,field_description:mail.field_res_users_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings_alias_domain
msgid "Alias Domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_name
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_name
msgid "Alias Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_domain
msgid "Alias domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:17
#: code:addons/mail/static/src/xml/systray.xml:28
#, python-format
msgid "All"
msgstr ""

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "Always"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:198
#, python-format
msgid "Anonymous"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_mail_no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_message_no_auto_thread
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template_model_id
msgid "Applies to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:333
#, python-format
msgid "Apply"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:45
#: model:ir.model.fields,field_description:mail.field_mail_activity_user_id
#, python-format
msgid "Assigned to"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_attachment_ids
#: model:ir.model.fields,help:mail.field_mail_mail_attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message_attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Employees"
msgstr ""

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Partners"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_author_id
#: model:ir.model.fields,field_description:mail.field_mail_message_author_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Author Signature (mass mail only)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_author_id
#: model:ir.model.fields,help:mail.field_mail_mail_author_id
#: model:ir.model.fields,help:mail.field_mail_message_author_id
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_mail_author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message_author_avatar
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_author_avatar
msgid "Author's avatar"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_group_public_id
msgid "Authorized Group"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_mail_auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template_auto_delete
msgid "Auto Delete"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_group_ids
msgid "Auto Subscription"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Available for mass mailing"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:89
#, python-format
msgid "Be careful with channels following internal notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_body_html
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Body"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner_message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users_message_bounce
msgid "Bounce"
msgstr ""

#. module: mail
#: selection:mail.notification,email_status:0
msgid "Bounced"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:341
#: code:addons/mail/static/src/xml/activity.xml:65
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr ""

#. module: mail
#: selection:mail.mail,state:0
msgid "Cancelled"
msgstr "Cancelado(a)"

#. module: mail
#: selection:mail.shortcode,shortcode_type:0
msgid "Canned Response"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_email_cc
msgid "Carbon copy message recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_email_cc
#: model:ir.model.fields,help:mail.field_mail_template_email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type_category
msgid "Categories may trigger specific behavior like opening calendar view"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_category
msgid "Category"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_email_cc
#: model:ir.model.fields,field_description:mail.field_mail_mail_email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_email_cc
msgid "Cc"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Change"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_field
msgid "Changed Field"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_channel_id
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: selection:mail.channel,channel_type:0
msgid "Channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_channel_message_ids
msgid "Channel Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_channel_type
msgid "Channel Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:36
#: code:addons/mail/static/src/xml/client_action.xml:172
#: code:addons/mail/static/src/xml/systray.xml:19
#: code:addons/mail/static/src/xml/systray.xml:36
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users_channel_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:168
#: code:addons/mail/static/src/xml/systray.xml:18
#: code:addons/mail/static/src/xml/systray.xml:32
#: model:ir.actions.client,name:mail.mail_channel_action_client_chat
#, python-format
msgid "Chat"
msgstr ""

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Chat Discussion"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_child_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_child_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_child_ids
msgid "Child Messages"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose an example"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:342
#, python-format
msgid "Click here to add new %(document)s or send an email to: %(email_link)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:358
#, python-format
msgid "Click here to add new %s"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Click to define a new chat shortcode."
msgstr ""

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Closed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_partner_to
#: model:ir.model.fields,help:mail.field_mail_template_partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_email_to
#: model:ir.model.fields,help:mail.field_mail_template_email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
#: selection:survey.mail.compose.message,message_type:0
msgid "Comment"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_composition_mode
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_composition_mode
msgid "Composition mode"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:109
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:647
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_body
#: model:ir.model.fields,field_description:mail.field_mail_mail_body
#: model:ir.model.fields,field_description:mail.field_mail_message_body
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_body
msgid "Contents"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_fold_state
msgid "Conversation Fold State"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_is_minimized
msgid "Conversation is minimized"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:6
#, python-format
msgid "Conversations"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner_message_bounce
#: model:ir.model.fields,help:mail.field_res_users_message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:307
#, python-format
msgid "Create %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_create_date
msgid "Create Date"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_test_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:41
#: model:ir.model.fields,field_description:mail.field_email_template_preview_create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail_create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_create_date
#: model:ir.model.fields,field_description:mail.field_mail_test_create_date
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_create_date
#, python-format
msgid "Created on"
msgstr "Creado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Month"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_starred
#: model:ir.model.fields,help:mail.field_mail_mail_starred
#: model:ir.model.fields,help:mail.field_mail_message_starred
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_starred
msgid "Current user has a starred notification linked to this message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_date
#: model:ir.model.fields,field_description:mail.field_mail_mail_date
#: model:ir.model.fields,field_description:mail.field_mail_message_date
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_date
msgid "Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_account_analytic_account_message_last_post
#: model:ir.model.fields,help:mail.field_account_asset_asset_message_last_post
#: model:ir.model.fields,help:mail.field_account_bank_statement_message_last_post
#: model:ir.model.fields,help:mail.field_account_invoice_message_last_post
#: model:ir.model.fields,help:mail.field_account_payment_message_last_post
#: model:ir.model.fields,help:mail.field_account_voucher_message_last_post
#: model:ir.model.fields,help:mail.field_blog_blog_message_last_post
#: model:ir.model.fields,help:mail.field_blog_post_message_last_post
#: model:ir.model.fields,help:mail.field_calendar_event_message_last_post
#: model:ir.model.fields,help:mail.field_crm_lead_message_last_post
#: model:ir.model.fields,help:mail.field_crm_team_message_last_post
#: model:ir.model.fields,help:mail.field_crossovered_budget_message_last_post
#: model:ir.model.fields,help:mail.field_event_event_message_last_post
#: model:ir.model.fields,help:mail.field_event_registration_message_last_post
#: model:ir.model.fields,help:mail.field_event_track_message_last_post
#: model:ir.model.fields,help:mail.field_fleet_vehicle_log_contract_message_last_post
#: model:ir.model.fields,help:mail.field_fleet_vehicle_message_last_post
#: model:ir.model.fields,help:mail.field_forum_forum_message_last_post
#: model:ir.model.fields,help:mail.field_forum_post_message_last_post
#: model:ir.model.fields,help:mail.field_forum_tag_message_last_post
#: model:ir.model.fields,help:mail.field_gamification_badge_message_last_post
#: model:ir.model.fields,help:mail.field_gamification_challenge_message_last_post
#: model:ir.model.fields,help:mail.field_hr_applicant_message_last_post
#: model:ir.model.fields,help:mail.field_hr_contract_message_last_post
#: model:ir.model.fields,help:mail.field_hr_department_message_last_post
#: model:ir.model.fields,help:mail.field_hr_employee_message_last_post
#: model:ir.model.fields,help:mail.field_hr_expense_message_last_post
#: model:ir.model.fields,help:mail.field_hr_expense_sheet_message_last_post
#: model:ir.model.fields,help:mail.field_hr_holidays_message_last_post
#: model:ir.model.fields,help:mail.field_hr_job_message_last_post
#: model:ir.model.fields,help:mail.field_mail_channel_message_last_post
#: model:ir.model.fields,help:mail.field_mail_mass_mailing_contact_message_last_post
#: model:ir.model.fields,help:mail.field_mail_test_message_last_post
#: model:ir.model.fields,help:mail.field_mail_test_simple_message_last_post
#: model:ir.model.fields,help:mail.field_mail_thread_message_last_post
#: model:ir.model.fields,help:mail.field_maintenance_equipment_category_message_last_post
#: model:ir.model.fields,help:mail.field_maintenance_equipment_message_last_post
#: model:ir.model.fields,help:mail.field_maintenance_request_message_last_post
#: model:ir.model.fields,help:mail.field_mrp_bom_message_last_post
#: model:ir.model.fields,help:mail.field_mrp_production_message_last_post
#: model:ir.model.fields,help:mail.field_mrp_repair_message_last_post
#: model:ir.model.fields,help:mail.field_mrp_unbuild_message_last_post
#: model:ir.model.fields,help:mail.field_mrp_workorder_message_last_post
#: model:ir.model.fields,help:mail.field_note_note_message_last_post
#: model:ir.model.fields,help:mail.field_product_product_message_last_post
#: model:ir.model.fields,help:mail.field_product_template_message_last_post
#: model:ir.model.fields,help:mail.field_project_project_message_last_post
#: model:ir.model.fields,help:mail.field_project_task_message_last_post
#: model:ir.model.fields,help:mail.field_purchase_order_message_last_post
#: model:ir.model.fields,help:mail.field_purchase_requisition_message_last_post
#: model:ir.model.fields,help:mail.field_res_partner_message_last_post
#: model:ir.model.fields,help:mail.field_res_users_message_last_post
#: model:ir.model.fields,help:mail.field_sale_order_message_last_post
#: model:ir.model.fields,help:mail.field_slide_channel_message_last_post
#: model:ir.model.fields,help:mail.field_slide_slide_message_last_post
#: model:ir.model.fields,help:mail.field_stock_landed_cost_message_last_post
#: model:ir.model.fields,help:mail.field_stock_picking_batch_message_last_post
#: model:ir.model.fields,help:mail.field_stock_picking_message_last_post
#: model:ir.model.fields,help:mail.field_stock_production_lot_message_last_post
#: model:ir.model.fields,help:mail.field_survey_survey_message_last_post
msgid "Date of the last message posted on the record."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_default
msgid "Default"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_null_value
#: model:ir.model.fields,field_description:mail.field_mail_template_null_value
msgid "Default Value"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_defaults
msgid "Default Values"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_use_default_to
#: model:ir.model.fields,field_description:mail.field_mail_template_use_default_to
msgid "Default recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_use_default_to
#: model:ir.model.fields,help:mail.field_mail_template_use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_auto_delete
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_auto_delete
msgid "Delete Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_auto_delete_message
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_auto_delete_message
msgid "Delete Message Copy"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_auto_delete
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:320
#, python-format
msgid "Delete this attachment"
msgstr ""

#. module: mail
#: selection:mail.mail,state:0
msgid "Delivery Failed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_description
#: model:ir.model.fields,field_description:mail.field_mail_test_description
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Descripción"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:46
#, python-format
msgid "Direct Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:81
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Discard"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_menu_root_chat
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion channel"
msgstr ""

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers_display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail_display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_display_name
#: model:ir.model.fields,field_description:mail.field_mail_test_display_name
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread_display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_display_name
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_res_name
msgid "Display name of the related document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_auto_delete_message
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_res_name
msgid "Document Name"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:79
#, python-format
msgid "Done"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:77
#, python-format
msgid "Done & Schedule Next"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:296
#, python-format
msgid "Download this PDF"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:272
#: code:addons/mail/static/src/xml/thread.xml:285
#: code:addons/mail/static/src/xml/thread.xml:308
#, python-format
msgid "Download this attachment"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_date_deadline
msgid "Due Date"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:38
#, python-format
msgid "Due in %d days"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:62
#, python-format
msgid "Edit"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:330
#, python-format
msgid "Edit Subscription of "
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:49
#, python-format
msgid "Edit subscription"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_partner_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
#: selection:survey.mail.compose.message,message_type:0
msgid "Email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_email_from
msgid "Email From"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Email Preview"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_email_status
msgid "Email Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_act_server_template_id
msgid "Email Template"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_email_template_preview
msgid "Email Template Preview"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_template
msgid "Email Templates"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_alias_id
msgid ""
"Email address internally associated with this user. Incoming emails will "
"appear in the user's notifications."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_email_from
#: model:ir.model.fields,help:mail.field_mail_mail_email_from
#: model:ir.model.fields,help:mail.field_mail_message_email_from
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to redirect replies..."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr ""

#. module: mail
#: code:addons/mail/models/update.py:97
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_mail.py:302
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a channel cannot follow twice the same object."
msgstr ""

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a partner cannot follow twice the same object."
msgstr ""

#. module: mail
#: sql_constraint:mail.followers:0
msgid ""
"Error: A follower must be either a partner or a channel (but not both)."
msgstr ""

#. module: mail
#: selection:mail.alias,alias_contact:0 selection:mail.channel,public:0
msgid "Everyone"
msgstr ""

#. module: mail
#: selection:mail.notification,email_status:0
msgid "Exception"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings_fail_counter
msgid "Fail Mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:374
#, python-format
msgid "Failed to render template %r using values %r"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_starred_partner_ids
msgid "Favorited By"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:209
#: model:ir.model.fields,field_description:mail.field_mail_activity_feedback
#, python-format
msgid "Feedback"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template_model_object_field
msgid "Field"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:24
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_field_desc
msgid "Field Description"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_field_type
msgid "Field Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_copyvalue
#: model:ir.model.fields,help:mail.field_mail_template_copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Folded"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:696
#: code:addons/mail/static/src/xml/followers.xml:20
#, python-format
msgid "Follow"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_account_payment_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_blog_post_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_crm_team_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_event_event_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_event_registration_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_event_track_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_forum_post_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_department_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_hr_job_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_note_note_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_product_product_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_product_template_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_project_project_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_project_task_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_sale_order_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_follower_ids
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_account_payment_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_blog_post_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_crm_team_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_event_event_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_event_registration_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_event_track_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_forum_post_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_department_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_hr_job_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_note_note_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_product_product_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_product_template_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_project_project_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_project_task_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_sale_order_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_channel_ids
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_account_payment_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_blog_post_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_crm_team_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_event_event_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_event_registration_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_event_track_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_forum_post_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_department_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_hr_job_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_note_note_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_product_product_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_product_template_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_project_project_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_project_task_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_sale_order_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:15
#, python-format
msgid "Followers of"
msgstr ""

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Followers only"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:24
#, python-format
msgid "Following"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_email_from
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail_email_from
#: model:ir.model.fields,field_description:mail.field_mail_message_email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_email_from
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_email_from
msgid "From"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:77
#, python-format
msgid "Future"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Gateway"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:54
#, python-format
msgid "Go to the configuration panel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr ""

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle by Emails"
msgstr ""

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle in Odoo"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_hidden
msgid "Hidden"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_hidden
msgid "Hide the subtype in the follower options"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_id
#: model:ir.model.fields,field_description:mail.field_mail_followers_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_id
#: model:ir.model.fields,field_description:mail.field_mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_id
#: model:ir.model.fields,field_description:mail.field_mail_template_id
#: model:ir.model.fields,field_description:mail.field_mail_test_id
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_id
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract_id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel_alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_test_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_icon
msgid "Icon"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers_res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite_res_id
msgid "Id of the followed resource"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:119
#: code:addons/mail/static/src/xml/client_action.xml:203
#, python-format
msgid "Idle"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_account_analytic_account_message_unread
#: model:ir.model.fields,help:mail.field_account_asset_asset_message_unread
#: model:ir.model.fields,help:mail.field_account_bank_statement_message_unread
#: model:ir.model.fields,help:mail.field_account_invoice_message_unread
#: model:ir.model.fields,help:mail.field_account_payment_message_unread
#: model:ir.model.fields,help:mail.field_account_voucher_message_unread
#: model:ir.model.fields,help:mail.field_blog_blog_message_unread
#: model:ir.model.fields,help:mail.field_blog_post_message_unread
#: model:ir.model.fields,help:mail.field_calendar_event_message_unread
#: model:ir.model.fields,help:mail.field_crm_lead_message_unread
#: model:ir.model.fields,help:mail.field_crm_team_message_unread
#: model:ir.model.fields,help:mail.field_crossovered_budget_message_unread
#: model:ir.model.fields,help:mail.field_event_event_message_unread
#: model:ir.model.fields,help:mail.field_event_registration_message_unread
#: model:ir.model.fields,help:mail.field_event_track_message_unread
#: model:ir.model.fields,help:mail.field_fleet_vehicle_log_contract_message_unread
#: model:ir.model.fields,help:mail.field_fleet_vehicle_message_unread
#: model:ir.model.fields,help:mail.field_forum_forum_message_unread
#: model:ir.model.fields,help:mail.field_forum_post_message_unread
#: model:ir.model.fields,help:mail.field_forum_tag_message_unread
#: model:ir.model.fields,help:mail.field_gamification_badge_message_unread
#: model:ir.model.fields,help:mail.field_gamification_challenge_message_unread
#: model:ir.model.fields,help:mail.field_hr_applicant_message_unread
#: model:ir.model.fields,help:mail.field_hr_contract_message_unread
#: model:ir.model.fields,help:mail.field_hr_department_message_unread
#: model:ir.model.fields,help:mail.field_hr_employee_message_unread
#: model:ir.model.fields,help:mail.field_hr_expense_message_unread
#: model:ir.model.fields,help:mail.field_hr_expense_sheet_message_unread
#: model:ir.model.fields,help:mail.field_hr_holidays_message_unread
#: model:ir.model.fields,help:mail.field_hr_job_message_unread
#: model:ir.model.fields,help:mail.field_mail_channel_message_unread
#: model:ir.model.fields,help:mail.field_mail_mass_mailing_contact_message_unread
#: model:ir.model.fields,help:mail.field_mail_test_message_unread
#: model:ir.model.fields,help:mail.field_mail_test_simple_message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_message_unread
#: model:ir.model.fields,help:mail.field_maintenance_equipment_category_message_unread
#: model:ir.model.fields,help:mail.field_maintenance_equipment_message_unread
#: model:ir.model.fields,help:mail.field_maintenance_request_message_unread
#: model:ir.model.fields,help:mail.field_mrp_bom_message_unread
#: model:ir.model.fields,help:mail.field_mrp_production_message_unread
#: model:ir.model.fields,help:mail.field_mrp_repair_message_unread
#: model:ir.model.fields,help:mail.field_mrp_unbuild_message_unread
#: model:ir.model.fields,help:mail.field_mrp_workorder_message_unread
#: model:ir.model.fields,help:mail.field_note_note_message_unread
#: model:ir.model.fields,help:mail.field_product_product_message_unread
#: model:ir.model.fields,help:mail.field_product_template_message_unread
#: model:ir.model.fields,help:mail.field_project_project_message_unread
#: model:ir.model.fields,help:mail.field_project_task_message_unread
#: model:ir.model.fields,help:mail.field_purchase_order_message_unread
#: model:ir.model.fields,help:mail.field_purchase_requisition_message_unread
#: model:ir.model.fields,help:mail.field_res_partner_message_unread
#: model:ir.model.fields,help:mail.field_res_users_message_unread
#: model:ir.model.fields,help:mail.field_sale_order_message_unread
#: model:ir.model.fields,help:mail.field_slide_channel_message_unread
#: model:ir.model.fields,help:mail.field_slide_slide_message_unread
#: model:ir.model.fields,help:mail.field_stock_landed_cost_message_unread
#: model:ir.model.fields,help:mail.field_stock_picking_batch_message_unread
#: model:ir.model.fields,help:mail.field_stock_picking_message_unread
#: model:ir.model.fields,help:mail.field_stock_production_lot_message_unread
#: model:ir.model.fields,help:mail.field_survey_survey_message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_account_analytic_account_message_needaction
#: model:ir.model.fields,help:mail.field_account_asset_asset_message_needaction
#: model:ir.model.fields,help:mail.field_account_bank_statement_message_needaction
#: model:ir.model.fields,help:mail.field_account_invoice_message_needaction
#: model:ir.model.fields,help:mail.field_account_payment_message_needaction
#: model:ir.model.fields,help:mail.field_account_voucher_message_needaction
#: model:ir.model.fields,help:mail.field_blog_blog_message_needaction
#: model:ir.model.fields,help:mail.field_blog_post_message_needaction
#: model:ir.model.fields,help:mail.field_calendar_event_message_needaction
#: model:ir.model.fields,help:mail.field_crm_lead_message_needaction
#: model:ir.model.fields,help:mail.field_crm_team_message_needaction
#: model:ir.model.fields,help:mail.field_crossovered_budget_message_needaction
#: model:ir.model.fields,help:mail.field_event_event_message_needaction
#: model:ir.model.fields,help:mail.field_event_registration_message_needaction
#: model:ir.model.fields,help:mail.field_event_track_message_needaction
#: model:ir.model.fields,help:mail.field_fleet_vehicle_log_contract_message_needaction
#: model:ir.model.fields,help:mail.field_fleet_vehicle_message_needaction
#: model:ir.model.fields,help:mail.field_forum_forum_message_needaction
#: model:ir.model.fields,help:mail.field_forum_post_message_needaction
#: model:ir.model.fields,help:mail.field_forum_tag_message_needaction
#: model:ir.model.fields,help:mail.field_gamification_badge_message_needaction
#: model:ir.model.fields,help:mail.field_gamification_challenge_message_needaction
#: model:ir.model.fields,help:mail.field_hr_applicant_message_needaction
#: model:ir.model.fields,help:mail.field_hr_contract_message_needaction
#: model:ir.model.fields,help:mail.field_hr_department_message_needaction
#: model:ir.model.fields,help:mail.field_hr_employee_message_needaction
#: model:ir.model.fields,help:mail.field_hr_expense_message_needaction
#: model:ir.model.fields,help:mail.field_hr_expense_sheet_message_needaction
#: model:ir.model.fields,help:mail.field_hr_holidays_message_needaction
#: model:ir.model.fields,help:mail.field_hr_job_message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel_message_needaction
#: model:ir.model.fields,help:mail.field_mail_mass_mailing_contact_message_needaction
#: model:ir.model.fields,help:mail.field_mail_test_message_needaction
#: model:ir.model.fields,help:mail.field_mail_test_simple_message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_message_needaction
#: model:ir.model.fields,help:mail.field_maintenance_equipment_category_message_needaction
#: model:ir.model.fields,help:mail.field_maintenance_equipment_message_needaction
#: model:ir.model.fields,help:mail.field_maintenance_request_message_needaction
#: model:ir.model.fields,help:mail.field_mrp_bom_message_needaction
#: model:ir.model.fields,help:mail.field_mrp_production_message_needaction
#: model:ir.model.fields,help:mail.field_mrp_repair_message_needaction
#: model:ir.model.fields,help:mail.field_mrp_unbuild_message_needaction
#: model:ir.model.fields,help:mail.field_mrp_workorder_message_needaction
#: model:ir.model.fields,help:mail.field_note_note_message_needaction
#: model:ir.model.fields,help:mail.field_product_product_message_needaction
#: model:ir.model.fields,help:mail.field_product_template_message_needaction
#: model:ir.model.fields,help:mail.field_project_project_message_needaction
#: model:ir.model.fields,help:mail.field_project_task_message_needaction
#: model:ir.model.fields,help:mail.field_purchase_order_message_needaction
#: model:ir.model.fields,help:mail.field_purchase_requisition_message_needaction
#: model:ir.model.fields,help:mail.field_res_partner_message_needaction
#: model:ir.model.fields,help:mail.field_res_users_message_needaction
#: model:ir.model.fields,help:mail.field_sale_order_message_needaction
#: model:ir.model.fields,help:mail.field_slide_channel_message_needaction
#: model:ir.model.fields,help:mail.field_slide_slide_message_needaction
#: model:ir.model.fields,help:mail.field_stock_landed_cost_message_needaction
#: model:ir.model.fields,help:mail.field_stock_picking_batch_message_needaction
#: model:ir.model.fields,help:mail.field_stock_picking_message_needaction
#: model:ir.model.fields,help:mail.field_stock_production_lot_message_needaction
#: model:ir.model.fields,help:mail.field_survey_survey_message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite_send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_user_signature
#: model:ir.model.fields,help:mail.field_mail_template_user_signature
msgid ""
"If checked, the user's signature will be appended to the text version of the"
" message"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner_opt_out
#: model:ir.model.fields,help:mail.field_res_users_opt_out
msgid ""
"If opt-out is checked, this contact has refused to receive emails for mass "
"mailing and marketing campaign. Filter 'Available for Mass Mailing' allows "
"users to filter the partners when performing mass mailing."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_scheduled_date
#: model:ir.model.fields,help:mail.field_mail_template_scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Jinja2 placeholders may be used."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings_alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:141
#, python-format
msgid "Inactive Alias"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:655
#: code:addons/mail/static/src/xml/client_action.xml:22
#: code:addons/mail/static/src/xml/client_action.xml:154
#: code:addons/mail/static/src/xml/client_action.xml:164
#, python-format
msgid "Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_parent_id
#: model:ir.model.fields,help:mail.field_mail_mail_parent_id
#: model:ir.model.fields,help:mail.field_mail_message_parent_id
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_parent_id
msgid "Initial thread message."
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_internal
msgid "Internal Only"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:99
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1792
#, python-format
msgid ""
"Invalid record set: should be called as model (without records) or on "
"single-record recordset"
msgstr ""

#. module: mail
#: code:addons/mail/controllers/main.py:41
#, python-format
msgid "Invalid token in route %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:579
#, python-format
msgid "Invitation"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/invite.py:53
#, python-format
msgid "Invitation to follow %s: %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:44
#: code:addons/mail/static/src/xml/client_action.xml:133
#, python-format
msgid "Invite"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:170
#, python-format
msgid "Invite Follower"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:31
#: code:addons/mail/static/src/xml/client_action.xml:133
#, python-format
msgid "Invite people"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:867
#, python-format
msgid "Invite people to #%s"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr ""

#. module: mail
#: selection:mail.channel,public:0
msgid "Invited people only"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_is_follower
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_is_follower
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_is_follower
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_is_follower
#: model:ir.model.fields,field_description:mail.field_account_payment_message_is_follower
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_is_follower
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_is_follower
#: model:ir.model.fields,field_description:mail.field_blog_post_message_is_follower
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_is_follower
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_is_follower
#: model:ir.model.fields,field_description:mail.field_crm_team_message_is_follower
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_is_follower
#: model:ir.model.fields,field_description:mail.field_event_event_message_is_follower
#: model:ir.model.fields,field_description:mail.field_event_registration_message_is_follower
#: model:ir.model.fields,field_description:mail.field_event_track_message_is_follower
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_is_follower
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_is_follower
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_is_follower
#: model:ir.model.fields,field_description:mail.field_forum_post_message_is_follower
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_is_follower
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_is_follower
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_department_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_is_follower
#: model:ir.model.fields,field_description:mail.field_hr_job_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_test_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_is_follower
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_is_follower
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_is_follower
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_is_follower
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_is_follower
#: model:ir.model.fields,field_description:mail.field_note_note_message_is_follower
#: model:ir.model.fields,field_description:mail.field_product_product_message_is_follower
#: model:ir.model.fields,field_description:mail.field_product_template_message_is_follower
#: model:ir.model.fields,field_description:mail.field_project_project_message_is_follower
#: model:ir.model.fields,field_description:mail.field_project_task_message_is_follower
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_is_follower
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner_message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users_message_is_follower
#: model:ir.model.fields,field_description:mail.field_sale_order_message_is_follower
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_is_follower
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_is_follower
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_is_follower
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_is_follower
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_is_follower
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_is_follower
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_is_follower
msgid "Is Follower"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_notification
msgid "Is Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_is_read
msgid "Is Read"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_is_subscribed
msgid "Is Subscribed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_is_member
msgid "Is a member"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_is_pinned
msgid "Is pinned on the interface"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_lang
#: model:ir.model.fields,field_description:mail.field_mail_template_lang
msgid "Language"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_last_post
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_last_post
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_last_post
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_last_post
#: model:ir.model.fields,field_description:mail.field_account_payment_message_last_post
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_last_post
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_last_post
#: model:ir.model.fields,field_description:mail.field_blog_post_message_last_post
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_last_post
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_last_post
#: model:ir.model.fields,field_description:mail.field_crm_team_message_last_post
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_last_post
#: model:ir.model.fields,field_description:mail.field_event_event_message_last_post
#: model:ir.model.fields,field_description:mail.field_event_registration_message_last_post
#: model:ir.model.fields,field_description:mail.field_event_track_message_last_post
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_last_post
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_last_post
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_last_post
#: model:ir.model.fields,field_description:mail.field_forum_post_message_last_post
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_last_post
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_last_post
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_department_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_last_post
#: model:ir.model.fields,field_description:mail.field_hr_job_message_last_post
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_last_post
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_last_post
#: model:ir.model.fields,field_description:mail.field_mail_test_message_last_post
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_last_post
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_last_post
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_last_post
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_last_post
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_last_post
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_last_post
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_last_post
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_last_post
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_last_post
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_last_post
#: model:ir.model.fields,field_description:mail.field_note_note_message_last_post
#: model:ir.model.fields,field_description:mail.field_product_product_message_last_post
#: model:ir.model.fields,field_description:mail.field_product_template_message_last_post
#: model:ir.model.fields,field_description:mail.field_project_project_message_last_post
#: model:ir.model.fields,field_description:mail.field_project_task_message_last_post
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_last_post
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_last_post
#: model:ir.model.fields,field_description:mail.field_res_partner_message_last_post
#: model:ir.model.fields,field_description:mail.field_res_users_message_last_post
#: model:ir.model.fields,field_description:mail.field_sale_order_message_last_post
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_last_post
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_last_post
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_last_post
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_last_post
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_last_post
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_last_post
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_last_post
msgid "Last Message Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview___last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity___last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin___last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type___last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias___last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin___last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel___last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner___last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message___last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers___last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail___last_update
#: model:ir.model.fields,field_description:mail.field_mail_message___last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel___last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype___last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode___last_update
#: model:ir.model.fields,field_description:mail.field_mail_template___last_update
#: model:ir.model.fields,field_description:mail.field_mail_test___last_update
#: model:ir.model.fields,field_description:mail.field_mail_test_simple___last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread___last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value___last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite___last_update
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_seen_message_id
msgid "Last Seen"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_test_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail_write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_write_date
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_write_date
#: model:ir.model.fields,field_description:mail.field_mail_test_write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:73
#, python-format
msgid "Late"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Leave"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:687
#: code:addons/mail/static/src/xml/client_action.xml:112
#, python-format
msgid "Leave this channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite_channel_ids
msgid ""
"List of channels that will be added as listeners of the current document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite_partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:698
#, python-format
msgid "List users in the current channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers_channel_id
msgid "Listener"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_channel_partner_ids
msgid "Listeners"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr ""

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Livechat Conversation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chatter_composer.js:31
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:56
#, python-format
msgid "Log a note. Followers will not be notified."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log a note..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_is_log
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_is_log
msgid "Log an Internal Note"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:56
#, python-format
msgid "Log note"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:59
#, python-format
msgid "Log or schedule an activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message_mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_mail_activity_type_id
msgid "Mail Activity Type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_mail.py:369
#, python-format
msgid "Mail Delivery Failed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:832
#, python-format
msgid "Mailbox unavailable - %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "Mailing Opt-Out"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:59
#, python-format
msgid "Mark Done"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:134
#, python-format
msgid "Mark all as read"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:134
#, python-format
msgid "Mark all read"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:206
#, python-format
msgid "Mark as Read"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:153
#: code:addons/mail/static/src/xml/thread.xml:200
#, python-format
msgid "Mark as Todo"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:49
#, python-format
msgid "Mark as done"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_image_medium
msgid "Medium-sized photo"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_image_medium
msgid ""
"Medium-sized photo of the group. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""

#. module: mail
#: selection:mail.activity.type,category:0
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail_mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_mail_message_id
msgid "Message ID"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail_record_name
#: model:ir.model.fields,field_description:mail.field_mail_message_record_name
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_record_name
msgid "Message Record Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_name
msgid "Message Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_email_to
msgid "Message recipients (emails)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_references
msgid "Message references, such as identifiers of previous messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:124
#, python-format
msgid "Message sent in \""
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers_subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_message_type
#: model:ir.model.fields,help:mail.field_mail_mail_message_type
#: model:ir.model.fields,help:mail.field_mail_message_message_type
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_message_id
#: model:ir.model.fields,help:mail.field_mail_mail_message_id
#: model:ir.model.fields,help:mail.field_mail_message_message_id
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_message_id
msgid "Message unique identifier"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_message_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_message_id
msgid "Message-Id"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_ids
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_ids
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_ids
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_ids
#: model:ir.model.fields,field_description:mail.field_account_payment_message_ids
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_ids
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_ids
#: model:ir.model.fields,field_description:mail.field_blog_post_message_ids
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_ids
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_ids
#: model:ir.model.fields,field_description:mail.field_crm_team_message_ids
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_ids
#: model:ir.model.fields,field_description:mail.field_event_event_message_ids
#: model:ir.model.fields,field_description:mail.field_event_registration_message_ids
#: model:ir.model.fields,field_description:mail.field_event_track_message_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_ids
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_ids
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_ids
#: model:ir.model.fields,field_description:mail.field_forum_post_message_ids
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_ids
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_ids
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_department_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_ids
#: model:ir.model.fields,field_description:mail.field_hr_job_message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_ids
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_message_ids
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_ids
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_ids
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_ids
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_ids
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_ids
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_ids
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_ids
#: model:ir.model.fields,field_description:mail.field_note_note_message_ids
#: model:ir.model.fields,field_description:mail.field_product_product_message_ids
#: model:ir.model.fields,field_description:mail.field_product_template_message_ids
#: model:ir.model.fields,field_description:mail.field_project_project_message_ids
#: model:ir.model.fields,field_description:mail.field_project_task_message_ids
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_ids
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users_message_ids
#: model:ir.model.fields,field_description:mail.field_sale_order_message_ids
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_ids
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_ids
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_ids
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_ids
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_ids
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:20
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_res_model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite_res_model
msgid "Model of the followed resource"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstallation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Month"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "My Activities"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_name
#: model:ir.model.fields,field_description:mail.field_mail_template_name
#: model:ir.model.fields,field_description:mail.field_mail_test_name
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_name
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_record_name
#: model:ir.model.fields,help:mail.field_mail_mail_record_name
#: model:ir.model.fields,help:mail.field_mail_message_record_name
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_record_name
msgid "Name get of the related document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_report_name
#: model:ir.model.fields,help:mail.field_mail_template_report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_mail_needaction
#: model:ir.model.fields,field_description:mail.field_mail_message_needaction
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_needaction
#: model:ir.model.fields,help:mail.field_mail_compose_message_needaction
#: model:ir.model.fields,help:mail.field_mail_mail_needaction
#: model:ir.model.fields,help:mail.field_mail_message_needaction
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_res_partner_id
msgid "Needaction Recipient"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:138
#, python-format
msgid "New Channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:137
#, python-format
msgid "New Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_char
msgid "New Value Char"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_datetime
msgid "New Value Datetime"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_float
msgid "New Value Float"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_integer
msgid "New Value Integer"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_monetary
msgid "New Value Monetary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_new_value_text
msgid "New Value Text"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:55
#: code:addons/mail/static/src/js/window_manager.js:139
#: code:addons/mail/static/src/xml/systray.xml:12
#: code:addons/mail/static/src/xml/systray.xml:21
#, python-format
msgid "New message"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:233
#, python-format
msgid "New messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:110
#, python-format
msgid "New messages appear here."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:97
#, python-format
msgid "New people"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_event_track_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_note_note_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_product_product_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_product_template_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_project_task_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_summary
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_summary
#: model:ir.model.fields,field_description:mail.field_event_track_activity_summary
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_summary
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_summary
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_summary
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_summary
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_summary
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_summary
#: model:ir.model.fields,field_description:mail.field_note_note_activity_summary
#: model:ir.model.fields,field_description:mail.field_product_product_activity_summary
#: model:ir.model.fields,field_description:mail.field_product_template_activity_summary
#: model:ir.model.fields,field_description:mail.field_project_task_activity_summary
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users_activity_summary
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_summary
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_summary
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_type_id
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_type_id
#: model:ir.model.fields,field_description:mail.field_event_track_activity_type_id
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_type_id
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_type_id
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_type_id
#: model:ir.model.fields,field_description:mail.field_note_note_activity_type_id
#: model:ir.model.fields,field_description:mail.field_product_product_activity_type_id
#: model:ir.model.fields,field_description:mail.field_product_template_activity_type_id
#: model:ir.model.fields,field_description:mail.field_project_task_activity_type_id
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users_activity_type_id
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_type_id
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_type_id
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_has_recommended_activities
msgid "Next activities available"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:58
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:22
#, python-format
msgid "No activities planned."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:47
#, python-format
msgid "No discussion yet..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:185
#, python-format
msgid "No follower"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:16
#, python-format
msgid "No matches found"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:17
#, python-format
msgid "No message matches your search. Try to change your search filters."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:113
#, python-format
msgid "No starred message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_mail_no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_message_no_auto_thread
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_no_auto_thread
msgid "No threading for answers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:157
#, python-format
msgid "Note by"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_notification_type
msgid "Notification Management"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_notification_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_notification_ids
msgid "Notifications"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_notify
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_notify
msgid "Notify followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_notify
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_notify
msgid "Notify followers of the document (mass post only)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_account_payment_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_blog_post_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_crm_team_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_event_event_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_event_registration_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_event_track_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_forum_post_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_department_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_hr_job_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_test_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_note_note_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_product_product_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_product_template_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_project_project_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_project_task_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_sale_order_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type_days
msgid ""
"Number of days before executing the action. It allows to plan the action "
"deadline."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_account_analytic_account_message_needaction_counter
#: model:ir.model.fields,help:mail.field_account_asset_asset_message_needaction_counter
#: model:ir.model.fields,help:mail.field_account_bank_statement_message_needaction_counter
#: model:ir.model.fields,help:mail.field_account_invoice_message_needaction_counter
#: model:ir.model.fields,help:mail.field_account_payment_message_needaction_counter
#: model:ir.model.fields,help:mail.field_account_voucher_message_needaction_counter
#: model:ir.model.fields,help:mail.field_blog_blog_message_needaction_counter
#: model:ir.model.fields,help:mail.field_blog_post_message_needaction_counter
#: model:ir.model.fields,help:mail.field_calendar_event_message_needaction_counter
#: model:ir.model.fields,help:mail.field_crm_lead_message_needaction_counter
#: model:ir.model.fields,help:mail.field_crm_team_message_needaction_counter
#: model:ir.model.fields,help:mail.field_crossovered_budget_message_needaction_counter
#: model:ir.model.fields,help:mail.field_event_event_message_needaction_counter
#: model:ir.model.fields,help:mail.field_event_registration_message_needaction_counter
#: model:ir.model.fields,help:mail.field_event_track_message_needaction_counter
#: model:ir.model.fields,help:mail.field_fleet_vehicle_log_contract_message_needaction_counter
#: model:ir.model.fields,help:mail.field_fleet_vehicle_message_needaction_counter
#: model:ir.model.fields,help:mail.field_forum_forum_message_needaction_counter
#: model:ir.model.fields,help:mail.field_forum_post_message_needaction_counter
#: model:ir.model.fields,help:mail.field_forum_tag_message_needaction_counter
#: model:ir.model.fields,help:mail.field_gamification_badge_message_needaction_counter
#: model:ir.model.fields,help:mail.field_gamification_challenge_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_applicant_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_contract_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_department_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_employee_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_expense_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_expense_sheet_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_holidays_message_needaction_counter
#: model:ir.model.fields,help:mail.field_hr_job_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_mass_mailing_contact_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_test_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_test_simple_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_message_needaction_counter
#: model:ir.model.fields,help:mail.field_maintenance_equipment_category_message_needaction_counter
#: model:ir.model.fields,help:mail.field_maintenance_equipment_message_needaction_counter
#: model:ir.model.fields,help:mail.field_maintenance_request_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mrp_bom_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mrp_production_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mrp_repair_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mrp_unbuild_message_needaction_counter
#: model:ir.model.fields,help:mail.field_mrp_workorder_message_needaction_counter
#: model:ir.model.fields,help:mail.field_note_note_message_needaction_counter
#: model:ir.model.fields,help:mail.field_product_product_message_needaction_counter
#: model:ir.model.fields,help:mail.field_product_template_message_needaction_counter
#: model:ir.model.fields,help:mail.field_project_project_message_needaction_counter
#: model:ir.model.fields,help:mail.field_project_task_message_needaction_counter
#: model:ir.model.fields,help:mail.field_purchase_order_message_needaction_counter
#: model:ir.model.fields,help:mail.field_purchase_requisition_message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner_message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users_message_needaction_counter
#: model:ir.model.fields,help:mail.field_sale_order_message_needaction_counter
#: model:ir.model.fields,help:mail.field_slide_channel_message_needaction_counter
#: model:ir.model.fields,help:mail.field_slide_slide_message_needaction_counter
#: model:ir.model.fields,help:mail.field_stock_landed_cost_message_needaction_counter
#: model:ir.model.fields,help:mail.field_stock_picking_batch_message_needaction_counter
#: model:ir.model.fields,help:mail.field_stock_picking_message_needaction_counter
#: model:ir.model.fields,help:mail.field_stock_production_lot_message_needaction_counter
#: model:ir.model.fields,help:mail.field_survey_survey_message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_account_analytic_account_message_unread_counter
#: model:ir.model.fields,help:mail.field_account_asset_asset_message_unread_counter
#: model:ir.model.fields,help:mail.field_account_bank_statement_message_unread_counter
#: model:ir.model.fields,help:mail.field_account_invoice_message_unread_counter
#: model:ir.model.fields,help:mail.field_account_payment_message_unread_counter
#: model:ir.model.fields,help:mail.field_account_voucher_message_unread_counter
#: model:ir.model.fields,help:mail.field_blog_blog_message_unread_counter
#: model:ir.model.fields,help:mail.field_blog_post_message_unread_counter
#: model:ir.model.fields,help:mail.field_calendar_event_message_unread_counter
#: model:ir.model.fields,help:mail.field_crm_lead_message_unread_counter
#: model:ir.model.fields,help:mail.field_crm_team_message_unread_counter
#: model:ir.model.fields,help:mail.field_crossovered_budget_message_unread_counter
#: model:ir.model.fields,help:mail.field_event_event_message_unread_counter
#: model:ir.model.fields,help:mail.field_event_registration_message_unread_counter
#: model:ir.model.fields,help:mail.field_event_track_message_unread_counter
#: model:ir.model.fields,help:mail.field_fleet_vehicle_log_contract_message_unread_counter
#: model:ir.model.fields,help:mail.field_fleet_vehicle_message_unread_counter
#: model:ir.model.fields,help:mail.field_forum_forum_message_unread_counter
#: model:ir.model.fields,help:mail.field_forum_post_message_unread_counter
#: model:ir.model.fields,help:mail.field_forum_tag_message_unread_counter
#: model:ir.model.fields,help:mail.field_gamification_badge_message_unread_counter
#: model:ir.model.fields,help:mail.field_gamification_challenge_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_applicant_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_contract_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_department_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_employee_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_expense_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_expense_sheet_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_holidays_message_unread_counter
#: model:ir.model.fields,help:mail.field_hr_job_message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel_message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_mass_mailing_contact_message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_test_message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_test_simple_message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_message_unread_counter
#: model:ir.model.fields,help:mail.field_maintenance_equipment_category_message_unread_counter
#: model:ir.model.fields,help:mail.field_maintenance_equipment_message_unread_counter
#: model:ir.model.fields,help:mail.field_maintenance_request_message_unread_counter
#: model:ir.model.fields,help:mail.field_mrp_bom_message_unread_counter
#: model:ir.model.fields,help:mail.field_mrp_production_message_unread_counter
#: model:ir.model.fields,help:mail.field_mrp_repair_message_unread_counter
#: model:ir.model.fields,help:mail.field_mrp_unbuild_message_unread_counter
#: model:ir.model.fields,help:mail.field_mrp_workorder_message_unread_counter
#: model:ir.model.fields,help:mail.field_note_note_message_unread_counter
#: model:ir.model.fields,help:mail.field_product_product_message_unread_counter
#: model:ir.model.fields,help:mail.field_product_template_message_unread_counter
#: model:ir.model.fields,help:mail.field_project_project_message_unread_counter
#: model:ir.model.fields,help:mail.field_project_task_message_unread_counter
#: model:ir.model.fields,help:mail.field_purchase_order_message_unread_counter
#: model:ir.model.fields,help:mail.field_purchase_requisition_message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner_message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users_message_unread_counter
#: model:ir.model.fields,help:mail.field_sale_order_message_unread_counter
#: model:ir.model.fields,help:mail.field_slide_channel_message_unread_counter
#: model:ir.model.fields,help:mail.field_slide_slide_message_unread_counter
#: model:ir.model.fields,help:mail.field_stock_landed_cost_message_unread_counter
#: model:ir.model.fields,help:mail.field_stock_picking_batch_message_unread_counter
#: model:ir.model.fields,help:mail.field_stock_picking_message_unread_counter
#: model:ir.model.fields,help:mail.field_stock_production_lot_message_unread_counter
#: model:ir.model.fields,help:mail.field_survey_survey_message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Odoo"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:895
#, python-format
msgid ""
"Odoo has now the permission to send you native notifications on this device."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:11
#, python-format
msgid "Odoo needs your permission to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:892
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:120
#, python-format
msgid "Offline"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_char
msgid "Old Value Char"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_datetime
msgid "Old Value DateTime"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_float
msgid "Old Value Float"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_integer
msgid "Old Value Integer"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_monetary
msgid "Old Value Monetary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value_old_value_text
msgid "Old Value Text"
msgstr ""

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "On Change"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:24
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:187
#, python-format
msgid "One follower"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:118
#: code:addons/mail/static/src/xml/client_action.xml:202
#, python-format
msgid "Online"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:22
#, python-format
msgid "Only custom models can be modified."
msgstr ""

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Open"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:139
#, python-format
msgid "Open channel settings"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:187
#, python-format
msgid "Open chat"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/extended_chat_window.xml:15
#, python-format
msgid "Open in Discuss"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner_opt_out
#: model:ir.model.fields,field_description:mail.field_res_users_opt_out
msgid "Opt-Out"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel_alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_test_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_mail_server_id
#: model:ir.model.fields,help:mail.field_mail_template_mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_report_template
#: model:ir.model.fields,field_description:mail.field_mail_template_report_template
msgid "Optional report to print and attach"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_lang
#: model:ir.model.fields,help:mail.field_mail_template_lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. "
"${object.partner_id.lang}."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_null_value
#: model:ir.model.fields,help:mail.field_mail_template_null_value
msgid "Optional value to use if the target field is empty"
msgstr ""

#. module: mail
#: selection:mail.activity.type,category:0
msgid "Other"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search selection:mail.mail,state:0
msgid "Outgoing"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_template_mail_server_id
msgid "Outgoing Mail Server"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message_mail_server_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_mail_server_id
msgid "Outgoing mail server"
msgstr ""

#. module: mail
#: selection:account.invoice,activity_state:0
#: selection:crm.lead,activity_state:0 selection:event.track,activity_state:0
#: selection:hr.applicant,activity_state:0 selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:maintenance.equipment,activity_state:0
#: selection:maintenance.request,activity_state:0
#: selection:mrp.production,activity_state:0
#: selection:mrp.repair,activity_state:0
#: selection:mrp.unbuild,activity_state:0 selection:note.note,activity_state:0
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
#: selection:project.task,activity_state:0
#: selection:purchase.order,activity_state:0
#: selection:res.partner,activity_state:0
#: selection:sale.order,activity_state:0
#: selection:stock.picking,activity_state:0
#: selection:survey.survey,activity_state:0
msgid "Overdue"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_user_id
msgid "Owner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_parent_id
msgid "Parent"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message_parent_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_parent_id
msgid "Parent Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel_alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_test_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Partner Mass Mailing"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_partner.py:32
#, python-format
msgid "Partner Profile"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Partners that did not ask not to be included in mass mailing campaigns"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_needaction_partner_ids
msgid "Partners with Need Action"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail_auto_delete
#: model:ir.model.fields,help:mail.field_mail_template_auto_delete
msgid "Permanently delete this email after sending it, to save space"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:891
#, python-format
msgid "Permission denied"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:894
#, python-format
msgid "Permission granted"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_image
msgid "Photo"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template_copyvalue
msgid "Placeholder Expression"
msgstr ""

#. module: mail
#: selection:account.invoice,activity_state:0
#: selection:crm.lead,activity_state:0 selection:event.track,activity_state:0
#: selection:hr.applicant,activity_state:0 selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:maintenance.equipment,activity_state:0
#: selection:maintenance.request,activity_state:0
#: selection:mrp.production,activity_state:0
#: selection:mrp.repair,activity_state:0
#: selection:mrp.unbuild,activity_state:0 selection:note.note,activity_state:0
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
#: selection:project.task,activity_state:0
#: selection:purchase.order,activity_state:0
#: selection:res.partner,activity_state:0
#: selection:sale.order,activity_state:0
#: selection:stock.picking,activity_state:0
#: selection:survey.survey,activity_state:0
msgid "Planned"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:6
#, python-format
msgid "Planned activities"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:282
#, python-format
msgid "Play this video"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chatter_composer.js:158
#, python-format
msgid "Please complete customer's informations"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/many2many_tags_email.js:84
#, python-format
msgid "Please complete customer's informations and email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composer.js:651
#, python-format
msgid "Please, wait while the file is uploading."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Emails: notifications are sent to your email\n"
"- Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_contact
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel_alias_contact
#: model:ir.model.fields,help:mail.field_mail_test_alias_contact
#: model:ir.model.fields,help:mail.field_res_users_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Powered by"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_previous_type_ids
msgid "Preceding Activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preferred reply address"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_reply_to
#: model:ir.model.fields,help:mail.field_mail_template_reply_to
msgid "Preferred response address (placeholders may be used here)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Preview of"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_previous_activity_type_id
msgid "Previous Activity Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:56
#: code:addons/mail/static/src/xml/client_action.xml:176
#, python-format
msgid "Private Channels"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:848
#, python-format
msgid "Public Channels"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:179
#, python-format
msgid "Re:"
msgstr ""

#. module: mail
#: selection:mail.notification,email_status:0
msgid "Ready to Send"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search selection:mail.mail,state:0
msgid "Received"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner_partner_id
msgid "Recipient"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_next_type_ids
msgid "Recommended Next Activities"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_test_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_references
msgid "References"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers_res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail_res_id
#: model:ir.model.fields,field_description:mail.field_mail_message_res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_res_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_res_id
msgid "Related Document ID"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_model
#: model:ir.model.fields,field_description:mail.field_mail_mail_model
#: model:ir.model.fields,field_description:mail.field_mail_message_model
#: model:ir.model.fields,field_description:mail.field_mail_template_model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_res_model
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_model
msgid "Related Document Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers_res_model
msgid "Related Document Model Name"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers_partner_id
msgid "Related Partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_relation_field
msgid "Relation field"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:51
#, python-format
msgid "Remove this follower"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:203
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_reply_to
#: model:ir.model.fields,help:mail.field_mail_mail_reply_to
#: model:ir.model.fields,help:mail.field_mail_message_reply_to
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_reply_to
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_reply_to
#: model:ir.model.fields,field_description:mail.field_mail_mail_reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message_reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_reply_to
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_reply_to
msgid "Reply-To"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_report_name
#: model:ir.model.fields,field_description:mail.field_mail_template_report_name
msgid "Report Filename"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_user_id
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_user_id
#: model:ir.model.fields,field_description:mail.field_event_track_activity_user_id
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_user_id
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_user_id
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_user_id
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_user_id
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_user_id
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_user_id
#: model:ir.model.fields,field_description:mail.field_note_note_activity_user_id
#: model:ir.model.fields,field_description:mail.field_product_product_activity_user_id
#: model:ir.model.fields,field_description:mail.field_product_template_activity_user_id
#: model:ir.model.fields,field_description:mail.field_project_task_activity_user_id
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users_activity_user_id
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_user_id
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_user_id
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_user_id
msgid "Responsible"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_body_html
msgid "Rich-text Contents"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail_body_html
msgid "Rich-text/HTML message"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_res_id
msgid "Sample Document"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_window.js:40
#, python-format
msgid "Say something"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:60
#, python-format
msgid "Schedule activity"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:56
#, python-format
msgid "Schedule an activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template_model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""

#. module: mail
#: selection:mail.channel,public:0
msgid "Selected group of users"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composer.js:374
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite_send_mail
msgid "Send Email"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:264
#, python-format
msgid "Send Mail (%s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:53
#, python-format
msgid "Send a message"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:53
#, python-format
msgid "Send message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_email_send
msgid "Send messages by email"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_email_from
#: model:ir.model.fields,help:mail.field_mail_template_email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:83
#, python-format
msgid "Sending Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:108
#, python-format
msgid "Sends messages by email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search selection:mail.mail,state:0
#: selection:mail.notification,email_status:0
msgid "Sent"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_res_partner_needaction_rel_is_email
msgid "Sent by Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:139
#, python-format
msgid "Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_shortcode_type
msgid "Shortcode Type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_source
msgid "Shortcut"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:667
#, python-format
msgid "Show an helper message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_ref_ir_act_window
#: model:ir.model.fields,field_description:mail.field_mail_template_ref_ir_act_window
msgid "Sidebar action"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_ref_ir_act_window
#: model:ir.model.fields,help:mail.field_mail_template_ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_author_avatar
#: model:ir.model.fields,help:mail.field_mail_mail_author_avatar
#: model:ir.model.fields,help:mail.field_mail_message_author_avatar
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_image_small
msgid "Small-sized photo"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_image_small
msgid ""
"Small-sized photo of the group. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: mail
#: selection:mail.shortcode,shortcode_type:0
msgid "Smiley"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type_res_model_id
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:661
#: code:addons/mail/static/src/xml/client_action.xml:28
#: code:addons/mail/static/src/xml/client_action.xml:156
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_starred
#: model:ir.model.fields,field_description:mail.field_mail_mail_starred
#: model:ir.model.fields,field_description:mail.field_mail_message_starred
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_starred
#, python-format
msgid "Starred"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_invoice_activity_state
#: model:ir.model.fields,field_description:mail.field_crm_lead_activity_state
#: model:ir.model.fields,field_description:mail.field_event_track_activity_state
#: model:ir.model.fields,field_description:mail.field_hr_applicant_activity_state
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin_activity_state
#: model:ir.model.fields,field_description:mail.field_mail_activity_state
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_activity_state
#: model:ir.model.fields,field_description:mail.field_maintenance_request_activity_state
#: model:ir.model.fields,field_description:mail.field_mrp_production_activity_state
#: model:ir.model.fields,field_description:mail.field_mrp_repair_activity_state
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_activity_state
#: model:ir.model.fields,field_description:mail.field_note_note_activity_state
#: model:ir.model.fields,field_description:mail.field_product_product_activity_state
#: model:ir.model.fields,field_description:mail.field_product_template_activity_state
#: model:ir.model.fields,field_description:mail.field_project_task_activity_state
#: model:ir.model.fields,field_description:mail.field_purchase_order_activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner_activity_state
#: model:ir.model.fields,field_description:mail.field_res_users_activity_state
#: model:ir.model.fields,field_description:mail.field_sale_order_activity_state
#: model:ir.model.fields,field_description:mail.field_stock_picking_activity_state
#: model:ir.model.fields,field_description:mail.field_survey_survey_activity_state
msgid "State"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_state
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,help:mail.field_account_invoice_activity_state
#: model:ir.model.fields,help:mail.field_crm_lead_activity_state
#: model:ir.model.fields,help:mail.field_event_track_activity_state
#: model:ir.model.fields,help:mail.field_hr_applicant_activity_state
#: model:ir.model.fields,help:mail.field_mail_activity_mixin_activity_state
#: model:ir.model.fields,help:mail.field_maintenance_equipment_activity_state
#: model:ir.model.fields,help:mail.field_maintenance_request_activity_state
#: model:ir.model.fields,help:mail.field_mrp_production_activity_state
#: model:ir.model.fields,help:mail.field_mrp_repair_activity_state
#: model:ir.model.fields,help:mail.field_mrp_unbuild_activity_state
#: model:ir.model.fields,help:mail.field_note_note_activity_state
#: model:ir.model.fields,help:mail.field_product_product_activity_state
#: model:ir.model.fields,help:mail.field_product_template_activity_state
#: model:ir.model.fields,help:mail.field_project_task_activity_state
#: model:ir.model.fields,help:mail.field_purchase_order_activity_state
#: model:ir.model.fields,help:mail.field_res_partner_activity_state
#: model:ir.model.fields,help:mail.field_res_users_activity_state
#: model:ir.model.fields,help:mail.field_sale_order_activity_state
#: model:ir.model.fields,help:mail.field_stock_picking_activity_state
#: model:ir.model.fields,help:mail.field_survey_survey_activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template_sub_model_object_field
msgid "Sub-field"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template_sub_object
msgid "Sub-model"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:8
#: model:ir.model.fields,field_description:mail.field_email_template_preview_subject
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_subject
#: model:ir.model.fields,field_description:mail.field_mail_mail_subject
#: model:ir.model.fields,field_description:mail.field_mail_message_subject
#: model:ir.model.fields,field_description:mail.field_mail_template_subject
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_subject
#, python-format
msgid "Subject"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_subject
#: model:ir.model.fields,help:mail.field_mail_template_subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:216
#, python-format
msgid "Subject:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_substitution
msgid "Substitution"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers_subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype_id_1986
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_type_summary
msgid "Summary"
msgstr ""

#. module: mail
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
#: selection:survey.mail.compose.message,message_type:0
msgid "System notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_has_recommended_activities
msgid "Technical field for UX purpose"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.wizard_email_template_preview
msgid "Template Preview"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_test
msgid "Test Mail Model"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_test_simple
msgid "Test Simple Chatter Record"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode_substitution
msgid "The escaped html code replacing the shortcut"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel_alias_model_id
#: model:ir.model.fields,help:mail.field_mail_test_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_name
#: model:ir.model.fields,help:mail.field_mail_channel_alias_name
#: model:ir.model.fields,help:mail.field_mail_test_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_alias_user_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel_alias_user_id
#: model:ir.model.fields,help:mail.field_mail_test_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_activity.py:165
#: code:addons/mail/models/mail_message.py:579
#: code:addons/mail/models/mail_message.py:674
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode_source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode_unicode_source
msgid "The source is replaced by this unicode character in the Chat Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_model_id
#: model:ir.model.fields,help:mail.field_mail_template_model_id
msgid "The type of document this template can be used with"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_image
msgid ""
"This field holds the image used as photo for the group, limited to "
"1024x1024px."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail_email_to
msgid "To"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_email_to
msgid "To (Emails)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview_partner_to
#: model:ir.model.fields,field_description:mail.field_mail_mail_recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_partner_to
msgid "To (Partners)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:14
#: code:addons/mail/static/src/xml/extended_chat_window.xml:7
#, python-format
msgid "To:"
msgstr ""

#. module: mail
#. openerp-web
#: selection:account.invoice,activity_state:0
#: code:addons/mail/static/src/js/activity.js:26
#: code:addons/mail/static/src/js/thread.js:240
#: code:addons/mail/static/src/xml/systray.xml:75
#: selection:crm.lead,activity_state:0 selection:event.track,activity_state:0
#: selection:hr.applicant,activity_state:0 selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:maintenance.equipment,activity_state:0
#: selection:maintenance.request,activity_state:0
#: selection:mrp.production,activity_state:0
#: selection:mrp.repair,activity_state:0
#: selection:mrp.unbuild,activity_state:0 selection:note.note,activity_state:0
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
#: selection:project.task,activity_state:0
#: selection:purchase.order,activity_state:0
#: selection:res.partner,activity_state:0
#: selection:sale.order,activity_state:0
#: selection:stock.picking,activity_state:0
#: selection:survey.survey,activity_state:0
#, python-format
msgid "Today"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "Todo"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:36
#, python-format
msgid "Tomorrow"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_mail_tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message_tracking_value_ids
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields_track_visibility
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Tracking"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail_tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message_tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_tracking_value_ids
msgid "Tracking values"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_message_type
#: model:ir.model.fields,field_description:mail.field_mail_mail_message_type
#: model:ir.model.fields,field_description:mail.field_mail_message_message_type
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_message_type
msgid "Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_uuid
msgid "UUID"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_mail.py:235
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:31
#, python-format
msgid "Unable to send email, please configure the sender's email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:700
#: code:addons/mail/static/src/xml/followers.xml:22
#, python-format
msgid "Unfollow"
msgstr ""

#. module: mail
#: sql_constraint:mail.alias:0
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode_unicode_source
msgid "Unicode Character"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_unread
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_unread
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_unread
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_unread
#: model:ir.model.fields,field_description:mail.field_account_payment_message_unread
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_unread
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_unread
#: model:ir.model.fields,field_description:mail.field_blog_post_message_unread
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_unread
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_unread
#: model:ir.model.fields,field_description:mail.field_crm_team_message_unread
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_unread
#: model:ir.model.fields,field_description:mail.field_event_event_message_unread
#: model:ir.model.fields,field_description:mail.field_event_registration_message_unread
#: model:ir.model.fields,field_description:mail.field_event_track_message_unread
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_unread
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_unread
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_unread
#: model:ir.model.fields,field_description:mail.field_forum_post_message_unread
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_unread
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_unread
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_department_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_unread
#: model:ir.model.fields,field_description:mail.field_hr_job_message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_unread
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_unread
#: model:ir.model.fields,field_description:mail.field_mail_test_message_unread
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_unread
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_unread
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_unread
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_unread
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_unread
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_unread
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_unread
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_unread
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_unread
#: model:ir.model.fields,field_description:mail.field_note_note_message_unread
#: model:ir.model.fields,field_description:mail.field_product_product_message_unread
#: model:ir.model.fields,field_description:mail.field_product_template_message_unread
#: model:ir.model.fields,field_description:mail.field_project_project_message_unread
#: model:ir.model.fields,field_description:mail.field_project_task_message_unread
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_unread
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner_message_unread
#: model:ir.model.fields,field_description:mail.field_res_users_message_unread
#: model:ir.model.fields,field_description:mail.field_sale_order_message_unread
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_unread
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_unread
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_unread
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_unread
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_unread
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_unread
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_unread
msgid "Unread Messages"
msgstr "Mensajes sin Leer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_account_analytic_account_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_account_asset_asset_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_account_bank_statement_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_account_invoice_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_account_payment_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_account_voucher_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_blog_blog_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_blog_post_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_calendar_event_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_crm_lead_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_crm_team_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_crossovered_budget_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_event_event_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_event_registration_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_event_track_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_log_contract_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_fleet_vehicle_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_forum_forum_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_forum_post_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_forum_tag_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_gamification_badge_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_gamification_challenge_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_applicant_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_contract_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_department_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_employee_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_expense_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_expense_sheet_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_holidays_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_hr_job_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_mass_mailing_contact_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_test_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_test_simple_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_category_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_equipment_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_maintenance_request_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mrp_bom_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mrp_production_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mrp_repair_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mrp_unbuild_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mrp_workorder_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_note_note_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_product_product_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_product_template_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_project_project_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_project_task_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_purchase_order_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_purchase_requisition_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_sale_order_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_slide_channel_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_slide_slide_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_stock_landed_cost_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_stock_picking_batch_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_stock_picking_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_stock_production_lot_message_unread_counter
#: model:ir.model.fields,field_description:mail.field_survey_survey_message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:135
#, python-format
msgid "Unstar all"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:135
#, python-format
msgid "Unstar all messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:136
#, python-format
msgid "Unsubscribe"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:136
#, python-format
msgid "Unsubscribe from channel"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:510
#, python-format
msgid "Unsupported report type %s found."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:322
#, python-format
msgid "Uploading"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composer.js:651
#, python-format
msgid "Uploading error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_use_active_domain
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_use_active_domain
msgid "Use active domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message_template_id
#: model:ir.model.fields,field_description:mail.field_survey_mail_compose_message_template_id
msgid "Use template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use your own email servers"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype_sequence
msgid "Used to order subtypes."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:51
#: code:addons/mail/static/src/xml/extended_chat_window.xml:8
#, python-format
msgid "User name"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:711
#, python-format
msgid "Users in this channel: %s %s and you."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:670
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:668
#, python-format
msgid "View %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:240
#, python-format
msgid ""
"Warning! \n"
" If you remove a follower, he won't be notified of any email or discussion on this document.\n"
" Do you really want to remove this follower ?"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template_sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_sub_object
#: model:ir.model.fields,help:mail.field_mail_template_sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields_track_visibility
msgid ""
"When set, every modification to this field will be tracked in the chatter."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message_is_log
#: model:ir.model.fields,help:mail.field_survey_mail_compose_message_is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:75
#, python-format
msgid "Write Feedback"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:12
#, python-format
msgid "Write something..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:30
#: code:addons/mail/static/src/js/thread.js:242
#, python-format
msgid "Yesterday"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/client_action.js:96
#, python-format
msgid "You added <b>%s</b> to the conversation."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:708
#, python-format
msgid "You are alone in this channel."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:677
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:672
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:114
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this channel."
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:53
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:144
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:349
#, python-format
msgid ""
"You could also add a new %(document)s by sending an email to: "
"%(email_link)s."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:579
#, python-format
msgid "You have been invited to: "
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview_attachment_ids
#: model:ir.model.fields,help:mail.field_mail_template_attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:473
#, python-format
msgid "You unpinned your conversation with <b>%s</b>."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chat_manager.js:471
#, python-format
msgid "You unsubscribed from <b>%s</b>."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:217
#, python-format
msgid "You:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:83
#, python-format
msgid "Your message has not been sent."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_actions.py:30
#, python-format
msgid "Your template should define email_from"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:966
#, python-format
msgid "alias %s: %s"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "created"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:315
#, python-format
msgid "document"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid ""
"done\n"
"        by"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "e.g. Discuss proposal"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/client_action.xml:11
#, python-format
msgid "enable desktop notifications"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:189
#, python-format
msgid "followers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:30
#, python-format
msgid "for"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "from:"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:282
#, python-format
msgid "incorrectly configured alias"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:278
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "ir.actions.server"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_autovacuum
msgid "ir.autovacuum"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "mail.alias.mixin"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:943
#, python-format
msgid "model %s does not accept document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:938
#, python-format
msgid "model %s does not accept document update"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:931
#, python-format
msgid ""
"model %s does not accept document update, fall back on document creation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "modified"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "mycompany.odoo.com"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread.js:21
#, python-format
msgid "now"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:177
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:913
#, python-format
msgid ""
"posting a message without model should be with a null res_id (private "
"message), received %s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:917
#, python-format
msgid ""
"posting a message without model should be with a parent_id (private message)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "publisher_warranty.contract"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread.js:17
#, python-format
msgid "read less"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread.js:16
#, python-format
msgid "read more"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "record:"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:935
#, python-format
msgid "reply to missing document (%s,%s)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:928
#, python-format
msgid "reply to missing document (%s,%s), fall back on new document creation"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:913
#, python-format
msgid "resetting thread_id"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:254
#, python-format
msgid "restricted to channel members"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:287
#, python-format
msgid "restricted to followers"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:291
#, python-format
msgid "restricted to known authors"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:917
#: code:addons/mail/models/mail_thread.py:935
#: code:addons/mail/models/mail_thread.py:938
#: code:addons/mail/models/mail_thread.py:943
#: code:addons/mail/models/mail_thread.py:966
#, python-format
msgid "skipping"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:20
#, python-format
msgid "this document"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:966
#, python-format
msgid "unknown error"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:906
#, python-format
msgid "unknown target model %s"
msgstr ""
