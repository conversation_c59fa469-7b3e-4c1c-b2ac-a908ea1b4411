from odoo import models, fields, api, exceptions
from datetime import datetime


class Product(models.Model):
    _inherit = 'product.template'

    '''override create method'''

    @api.model
    def create(self, vals):
        '''Code before create
           Can use the `vals` dictionary'''
        new_record = super(Product, self).create(vals)
        # if not new_record.default_code:
        #     if new_record.categ_id.short_code:
        #         new_record.default_code = new_record.categ_id.short_code or ' ' + '/' + self.env[
        #             "ir.sequence"].next_by_code("product.ref") or "New"
        #     else:
        #         new_record.default_code = self.env["ir.sequence"].next_by_code("product.ref") or "New"

        return new_record


class ProductCategory(models.Model):
    _inherit = 'product.category'

    short_code = fields.Char()


class ProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model
    def create(self, vals):
        '''Code before create
           Can use the `vals` dictionary'''
        new_record = super(ProductProduct, self).create(vals)
        if not new_record.default_code:
            if new_record.categ_id.short_code:
                new_record.default_code = new_record.categ_id.short_code or ' ' + '/' + self.env[
                    "ir.sequence"].next_by_code("product.ref") or "New"
            else:
                new_record.default_code = self.env["ir.sequence"].next_by_code("product.ref") or "New"

        return new_record
