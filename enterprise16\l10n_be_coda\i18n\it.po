# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_coda
#
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-28 08:56+0000\n"
"PO-Revision-Date: 2022-12-28 08:56+0000\n"
"Last-Translator: \n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "1-st (recurrent)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "ATM/POS debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Access right to database"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Agio on supplier's bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Amount as totalised by the customer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Avgas"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bancontact/Mister Cash"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Estratto conto"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bank confirmation to revisor or accountant"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bill claimed back"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Bills - calculation of interest"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on information data record 3.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.2, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"CODA parsing error on movement data record 2.3, seq nr %s! Please report "
"this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cancellation or correction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Capital and/or interest term investment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cards"
msgstr "Schede"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash deposit at an ATM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal by your branch or agents"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cash withdrawal from an ATM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charge for safe custody"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Charging fees for transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheque-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing"
msgstr "Chiusura"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Closing (periodical settlements for interest, costs,...)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Codes proper to each bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective payments of wages"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Collective transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial bills"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Commercial paper claimed back"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication"
msgstr "Comunicazione"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communication: "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Communicaton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Compensation for missing coupon"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Correction for prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs"
msgstr "Costi"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for holding a documentary cash credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for opening a bank guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs for the safe custody of correspondence"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs related to commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to electronic output"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to incoming foreign and non-SEPA transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to outgoing foreign transfers and non-SEPA transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to payment of foreign cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Costs relating to the payment of a foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter Party Address"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Counter transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Country code of the principal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit"
msgstr "Credito"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after Proton payments"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after a payment at a terminal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Credit-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Creditor’s identification code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Currency"
msgstr "Valuta"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Damage relating to bills and cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Department store cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail"
msgstr "Dettaglio"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Amount as totalised by the customer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Detail of Simple amount with detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Difference in payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct Debit scheme"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Direct debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount abroad"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Discount foreign supplier's bills"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary credit charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary export credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Documentary import credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Domestic or local SEPA credit transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Download of prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Drawing up a certificate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Equivalent in EUR"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Error"
msgstr "Errore"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Extension of maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fees and commissions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (credit)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralisation (debit)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Financial centralization"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"First credit of cheques, vouchers, luncheon vouchers, postal orders, credit "
"under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – capital and interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Fixed advance – interest only"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Foreign commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward purchase of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Forward sale of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the currency of the account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Gross amount in the original currency"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Handling costs instalment credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Idem without guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Income from payments by GSM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Individual transfer order initiated by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Information charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Instant SEPA credit transfer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Insurance costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interest term investment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Interim interest on subscription"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "International credit transfers - non-SEPA credit transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Issues"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_journal
msgid "Journal"
msgstr "Registro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "LPG"
msgstr "GPL"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading GSM cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading Proton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Loading a GSM card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Long-term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Maestro"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Management/custody"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Mandate reference"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Masked PAN or card number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Method of calculation (VAT, withholding tax on income, commission, etc.)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Miscellaneous fees and commissions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Name: {name}, Town: {city}"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Night safe"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "No date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Non-presented circular cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Number of the credit card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Original amount of the transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other"
msgstr "Altro"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Other credit applications"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "PAN or card number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit - individual transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS credit – Globalisation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "POS others"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Paid or reason for refused payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Partial payment subscription"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Participation in and management of interest refund system"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Pay-packet charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payable coupons/repayable securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment"
msgstr "Pagamento"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by GSM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card outside the Eurozone"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by means of a payment card within the Eurozone"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment by your branch/agents"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment documents abroad"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in advance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment in your favour"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment night safe"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of a foreign cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Payment of coupons from a deposit or settlement of coupons delivered over "
"the counter - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of wages, etc."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment of your cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Payment with tank card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Postage"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Printing of forms"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Private"
msgstr "Privato"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Provisionally unpaid"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of Smartcard"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of an international bank cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of fiscal stamps"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of foreign bank notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of gold/pieces"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of petrol coupons"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Purchase of traveller’s cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Rate"
msgstr "Valore"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reason"
msgstr "Motivo"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Registering compensation for savings accounts"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Regularisation costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement"
msgstr "Rimborso"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of cheque-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reimbursement of costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheque by your branch - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of cheques, vouchers, etc. credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of commercial paper for discount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of documents abroad - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign bill credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of foreign cheque credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of guaranteed foreign supplier's bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill with guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Remittance of supplier's bill without guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of direct debit box"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Renting of safes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid ""
"Repayable securities from a deposit or delivered at the counter - credit "
"under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Research costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Retrocession of issue commission"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Return of an irregular bill of exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Reversal of voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA B2B"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA Direct Debit"
msgstr "Addebito Diretto SEPA"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "SEPA core"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of foreign bank notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of gold/pieces under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Sale of traveller’s cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Second credit of unpaid cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Securities"
msgstr "Titoli"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Separately charged costs and provisions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement Date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement credit cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of bank acceptances"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of discount bank acceptance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of fixed advance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of instalment credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Settlement of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Share option plan – exercising an option"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Short-term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount with detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Simple amount without detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Special charge for safe custody"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split Transactions"
msgstr "Separare operazioni"

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split collective payments for CODA files"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot purchase of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Spot sale of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Standing order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Structured format communication"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subscription to securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Subsidy"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Surety fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "TINA"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tender"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Tenders"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term Investments"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Terminal cash deposit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Trade information"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer"
msgstr "Trasferisci"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer from your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer in your favour – initiated by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Transfer to your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Travel insurance premium"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type Direct Debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of R transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Type of structured communication not supported: "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Undefined transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable reimbursement"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unexecutable transfer order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unloading Proton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid debt"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid foreign cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid postal order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unpaid voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Unsupported bank account structure "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Upload of prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Value (date) correction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Various transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Warrant fallen due"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Writ service fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Wrong CODA code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your certified cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your issue circular cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your purchase bank cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment hire-purchase and similar claims"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment instalment credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repayment mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "Your repurchase of issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount (equivalent in foreign currency)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "amount on which %% is calculated"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "basic amount of the calculation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cancellation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "card scheme"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "company number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conformity code or blank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "conventional maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "cumulative on network"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "currency"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date"
msgstr "data"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of issue of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "date of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor disagrees"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "debtor’s account problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "deposit number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "diesel"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "distribution sector"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "domestic fuel oil"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "end date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in EUR"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "equivalent in the currency of the account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "europremium"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "exchange rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "extension zone of account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "fuel"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "guarantee number (no. allocated by the bank)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "hour of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "identification of terminal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "interest rates, calculation basis"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "invoice number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "issuing institution"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "last (recurrent)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "lubricants"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "maturity date of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "message (structured of free)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum"
msgstr "minimo"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum not applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "minimum rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "new balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "nominal interest rate or rate of charge"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "number of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "old balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "one-off"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "original amount (given by the customer)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "other types"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "paid"
msgstr "pagato"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "payment day"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "percentage"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period from {} to {}"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "period number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "petrol"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium 99+"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium plus 98 oct"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "premium with lead substitute"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "product code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reason not specified"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "recurrent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reference of transaction on credit account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "refund"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "regular unleaded"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reject"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "return"
msgstr "invio"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "reversal of purchases"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "sequence number of validation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "starting date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "technical problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "teledata"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "term in days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "terminal number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "transaction type"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "undefined"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unit price"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unset"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "unspecified"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "validation date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "volume"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
#, python-format
msgid "withdrawal"
msgstr ""
