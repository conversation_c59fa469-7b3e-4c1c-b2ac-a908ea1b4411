<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_auto_post_draft_entry" model="ir.cron">
        <field name="name">Account: Post draft entries with auto_post enabled and accounting date up to today</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="nextcall" eval="(DateTime.now().replace(hour=2, minute=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="doall" eval="False"/>
        <field name="model_id" ref="model_account_move"/>
        <field name="code">model._autopost_draft_entries()</field>
        <field name="state">code</field>
    </record>
</odoo>
