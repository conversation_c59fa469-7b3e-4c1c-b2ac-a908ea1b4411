# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_disallowed_expenses
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-10 09:42+0000\n"
"PO-Revision-Date: 2023-01-10 09:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1212
msgid "Abnormal or voluntary benefits"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1215
msgid "Benefits of meal vouchers, sports/culture vouchers or eco-vouchers"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1222
msgid "Bonuses, subsidies in capital and in regional interest"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1205
msgid "Car expenses and capital losses on non-deductible motor vehicles"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1206
msgid "Car expenses up to a portion of the benefit in kind"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1220
msgid "Compensation for missing coupon"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1229
msgid "Correction based on the minimum taxable income from diamond trading "
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1233
msgid "Employee participation and beneficiary bonuses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1210
msgid "Exaggerated interests"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1235
msgid ""
"Expenses of the mobility allowance up to a proportion of the benefit kind"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1232
msgid "Expenses of works approved tax shelter"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1203
msgid "Fines, penalties and confiscations of any kind"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1207
msgid "Hospitality expenses and non-deductible business gifts"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1211
msgid "Interest related to a portion of certain borrowings"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1216
msgid "Liberalities"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1234
msgid "Non-deductible mobility allowances"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1223
msgid "Non-deductible payments to certain States"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1204
msgid "Non-deductible pensions, capital, employer contributions and premiums"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1208
msgid "Non-deductible restaurant expenses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1201
msgid "Non-deductible taxes"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1209
msgid "Non-specific professional clothing expenses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1239
msgid "Other disallowed expenses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1226
msgid ""
"Positive corrections in application under Diamond Scheme. Positive "
"difference between the gross profit determined on a flat-rate basis and the "
"gross profit determined on an accounting basis"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1228
msgid ""
"Positive difference between the reference income for a corporate executive "
"and the income of the highest corporate executive"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1217
msgid "Reductions in value and losses on shares or parts"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1202
msgid "Regional taxes, duties and fees"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1231
msgid ""
"Resumption of deduction for innovation income following non-reinvestment in "
"qualifying expenses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1218
msgid "Resumptions of previous exemptions"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1230
msgid ""
"Reversal of deduction for innovation income in the event of staggering of "
"historical costs"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1214
msgid "Social benefits"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1225
msgid "Unjustified expenses"
msgstr ""

#. module: l10n_be_disallowed_expenses
#: model:account.disallowed.expenses.category,name:l10n_be_disallowed_expenses.demo_disallowed_expenses_category_1227
msgid "Write-down on inventory and non-deductible costs"
msgstr ""
