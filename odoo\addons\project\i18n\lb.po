# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON> ALT <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 13:48+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON> ALT <<EMAIL>>, 2019\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "# Days to Deadline"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "# Working Days to Assign"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "# Working Days to Close"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr ""

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "${object.project_id.company_id.name}: Satisfaction Survey"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "% On"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Assign the task</b> to someone. <i>You can create and invite a new user "
"on the fly.</i>"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Click the save button</b> to apply your changes to the task."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Star tasks</b> to mark team priorities."
msgstr ""

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    % set access_token = object.rating_get_access_token()\n"
"    % set partner = object.rating_get_partner_id()\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            Hello ${partner.name},<br/>\n"
"            Please take a moment to rate our services related to the task \"<strong>${object.name}</strong>\"\n"
"            % if object.rating_get_rated_partner_id().name:\n"
"                assigned to <strong>${object.rating_get_rated_partner_id().name}</strong>.<br/>\n"
"            % else:\n"
"                .<br/>\n"
"            % endif\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/10\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_10.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/5\">\n"
"                                    <img alt=\"Not satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Not satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/1\">\n"
"                                    <img alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Highly Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            % if object.project_id.rating_status == 'stage':\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b>${object.stage_id.name}</b></span>\n"
"            % endif\n"
"            % if object.project_id.rating_status == 'periodic':\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b>${object.project_id.rating_status_period}</b> as long as the task is in the <b>${object.stage_id.name}</b> stage.</span>\n"
"            % endif\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear ${object.partner_id.name or 'customer'},<br/>\n"
"    Thank you for your enquiry.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <br/>\n"
"</div>\n"
"        "
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_index
msgid "<i class=\"fa fa-arrow-circle-right \"/> See the feedbacks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_index
msgid "<i class=\"fa fa-calendar\"/> End date :"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i class=\"fa fa-comments\" role=\"img\" aria-label=\"Unread Messages\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_index
msgid "<i class=\"fa fa-envelope\"/> Email :"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-smile-o\"/> No rating yet"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Documents are attached to the tasks of your project.</p><p>\n"
"                        Send messages or log internal notes with attachments to link\n"
"                        documents to your project.\n"
"                    </p>"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                    Create a new project</p>"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<p>Once your task is ready, you can save it.</p>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
msgid "<small class=\"text-muted\">Project - </small>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-right\">Status:</small>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
msgid "<span class=\"fa fa-tasks\" role=\"img\" aria-label=\"Tasks\" title=\"Tasks\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"o_label\">Profitability</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Description</strong>"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "<strong style=\"font-size: 16px;\">Try the mail gateway</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assigned to</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Date:</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_popover
msgid "<strong>Rated by: </strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Reported by</strong>"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
msgid "Action Needed"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_task__active
msgid "Active"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
msgid "Activities"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
msgid "Activity State"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to configure <b>stages for your tasks</b>.<br/><i>e.g. New - In "
"Progress - Done</i>"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr ""

#. module: project
#: model:project.task.type,name:project.project_stage_data_2
msgid "Advanced"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr ""

#. module: project
#: model:ir.ui.menu,name:project.menu_project_management
msgid "All Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All employees"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Analysis"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Assignation Date"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_id
msgid "Assigned To"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_id
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Assigned to"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage employees can block or make task/issue ready for next stage.\n"
"                                You can define here labels that will be displayed for the state instead\n"
"                                of the default labels."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachment that don't come from message."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Attachments"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic kanban status"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * A good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * A medium or a bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_progressbar
msgid "Average"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_progressbar
msgid "Bad"
msgstr ""

#. module: project
#: model:project.task.type,name:project.project_stage_data_1
msgid "Basic"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task,legend_blocked:project.project_task_data_0
#: model:project.task,legend_blocked:project.project_task_data_1
#: model:project.task,legend_blocked:project.project_task_data_11
#: model:project.task,legend_blocked:project.project_task_data_12
#: model:project.task,legend_blocked:project.project_task_data_13
#: model:project.task,legend_blocked:project.project_task_data_14
#: model:project.task,legend_blocked:project.project_task_data_2
#: model:project.task,legend_blocked:project.project_task_data_4
#: model:project.task,legend_blocked:project.project_task_data_5
#: model:project.task,legend_blocked:project.project_task_data_6
#: model:project.task,legend_blocked:project.project_task_data_7
#: model:project.task,legend_blocked:project.project_task_data_9
#: model:project.task.type,legend_blocked:project.project_stage_data_0
#: model:project.task.type,legend_blocked:project.project_stage_data_1
#: model:project.task.type,legend_blocked:project.project_stage_data_2
#, python-format
msgid "Blocked"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr ""

#. module: project
#: model:ir.filters,name:project.filter_task_report_responsible
msgid "By Responsible"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>project name</b>. (e.g. Website Launch, Product Development, "
"Office Party, etc.)"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Choose a <b>task name</b>. (e.g. Website Design, Purchase Goods etc.)"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Choose a Project Email"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
msgid "Click <i>Create</i> to start a new task."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Click on the card to write more information about it and collaborate with "
"your coworkers."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Click on this button to modify the task."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo "
"designs to the task, so that information flow from designers to the workers "
"who print the t-shirt. Organize priorities amongst orders %s using the icon."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Company"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Computed using sum of hours planned of all subtasks created from main task. "
"Usually these hours are less or equal to the Planned Hours (of main task)."
msgstr ""

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Konfiguratioun"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Contact"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid "Create a new project"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Create a new stage in the task pipeline"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Create a new tag"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid "Create a new task"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
msgid "Created by"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
msgid "Created on"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr ""

#. module: project
#: model:ir.filters,name:project.filter_task_report_cumulative_flow
msgid "Cumulative Flow"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "Current stage of the task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Customer"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.ui.menu,name:project.rating_rating_menu_project
#: model_terms:ir.ui.view,arch_db:project.rating_project_rating_page
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Customer Ratings"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid ""
"Customer ratings on tasks. If you have no rating, change your project "
"Settings to activate it."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer(s) Ratings"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and "
"you can communicate on the task directly. Your managers decide which "
"feedback is accepted %s and which feedback is moved to the \"Refused\" "
"column."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
msgid "Deadline"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
msgid ""
"Defines the visibility of the tasks of the project:\n"
"- Invited employees: employees may only see the followed project and tasks.\n"
"- All employees: employees may see all project and tasks.\n"
"- Portal users and all employees: employees may see everything.   Portal users may see project and tasks followed by.\n"
"   them or by someone of their company."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Delete"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Discard"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr ""

#. module: project
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Documents"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Edit"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit Task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Edit project's stages and set an email template on the stages on which you "
"want to activate the rating."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Email Alias"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
msgid "Email cc"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Emails"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of task(s)."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "Error! project start-date must be lower than project end-date."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Everyone can propose ideas, and the Editor marks the best ones as %s. Attach"
" all documents or links to the task directly, to have all information about "
"a research centralized."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Exception Activities"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Fill your Inbox easily with the email gateway. Periodically review your "
"Inbox and schedule tasks by moving them to others columns. Every day, you "
"review the \"This Week\" column to move important tasks \"Today\". Every "
"Monday, you review the \"This Month\" column."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Followed Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
msgid "Followers"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_channel_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Forecasts"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr ""

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Getting Things Done (GTD)"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__sequence
msgid "Gives the sequence order when displaying a list of Projects."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__sequence
msgid "Gives the sequence order when displaying a list of tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
msgid "Green"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
msgid "Grey"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the %s and %s to signalize what is the "
"current status of your Idea"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_progressbar
msgid "Happy"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__2
msgid "High"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"How to get customer feedback?\n"
"- Rating when changing stage: an email will be sent when a task is pulled in another stage.\n"
"- Periodical Rating: email will be sent periodically.\n"
"\n"
"Don't forget to set up the mail templates on the stages for which you want to get the customer's feedbacks."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "I take it"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_unread
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set an email will be sent to the customer when the task or issue reaches "
"this step."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set and if the project's rating configuration is 'Rating when changing "
"stage', then an email will be sent to the customer when the task reaches "
"this step."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
msgid "Important"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.task,legend_normal:project.project_task_data_0
#: model:project.task,legend_normal:project.project_task_data_1
#: model:project.task,legend_normal:project.project_task_data_11
#: model:project.task,legend_normal:project.project_task_data_12
#: model:project.task,legend_normal:project.project_task_data_13
#: model:project.task,legend_normal:project.project_task_data_14
#: model:project.task,legend_normal:project.project_task_data_2
#: model:project.task,legend_normal:project.project_task_data_4
#: model:project.task,legend_normal:project.project_task_data_5
#: model:project.task,legend_normal:project.project_task_data_6
#: model:project.task,legend_normal:project.project_task_data_7
#: model:project.task,legend_normal:project.project_task_data_9
#: model:project.task.type,legend_normal:project.project_stage_data_0
#: model:project.task.type,legend_normal:project.project_stage_data_1
#: model:project.task.type,legend_normal:project.project_stage_data_2
#, python-format
msgid "In Progress"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Inbox"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited employees"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__planned_hours
msgid ""
"It is the time planned to achieve the task. If this document has sub-tasks, "
"it means the time needed to achieve this tasks and its childs."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "Kanban State"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid "Label used for the tasks of the project."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_project_rating_page
msgid "Last 3 months"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 30 Days"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_project_rating_page
msgid "Last 30 days"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_project_rating_page
msgid "Last 7 days"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Higly Dissatisfied"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Not Satisfied"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Satisfied"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first project."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first task."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Lets the company customize which Pad installation should be used to link to "
"new pads (for example: http://etherpad.com/)."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Log time on tasks"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Long Term"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly "
"acquired project, assign them and use the %s and %s to define if the project"
" is ready for the next step."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
msgid "Messages"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#, python-format
msgid "Name"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks :"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model:project.task.type,name:project.project_stage_data_0
#, python-format
msgid "New"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"New tasks can be generated from incoming emails. You just need to set email "
"aliases on your projects.<br>"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_index
msgid "No project rating published for now."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__no
msgid "No rating"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "Normal"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_open
msgid "Number of Working Days to Open the task"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_close
msgid "Number of Working Days to close the task"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__message_unread_counter
#: model:ir.model.fields,help:project.field_project_task__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid ""
"Odoo's project management allows you to manage the pipeline of your tasks efficiently.<br>\n"
"                    You can track progress, discuss on tasks, attach documents, etc."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
msgid "Open Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid "Or"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Organize your activities (plan tasks, track issues, invoice timesheets) for "
"internal, personal or customer projects."
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_blocked
#: model:ir.model.fields,help:project.field_project_task_type__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_done
#: model:ir.model.fields,help:project.field_project_task_type__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_normal
#: model:ir.model.fields,help:project.field_project_task_type__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"Percentage of happy ratings over the past 30 days. Get rating details from "
"the More menu."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodical Rating"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_popover
msgid "Placeholder"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Planned Hours"
msgstr ""

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Portal users and all employees"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to signalize to "
"your colleagues that a task is ready for the next stage.%s Use the %s to "
"signalize a problem or a need for discussion on a task."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
msgid "Priority"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Project"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Project Name"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_index
msgid "Project Satisfaction"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
msgid "Project Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__subtask_project_id
#: model:ir.model.fields,help:project.field_project_task__subtask_project_id
msgid ""
"Project in which sub-tasks of the current project will be created. It can be"
" the current project itself."
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr ""

#. module: project
#: model:ir.actions.server,name:project.ir_actions_server_project_sample
msgid "Project: Activate Sample Project"
msgstr ""

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
#: model:ir.cron,name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.rating_index
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Projects"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_popover
msgid "Rated partner"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Rated tasks"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Rating Average"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__portal_show_rating
msgid "Rating visible publicly"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:mail.message.subtype,description:project.mt_task_rating
msgid "Ratings"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Ratings of %s"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.task,legend_done:project.project_task_data_0
#: model:project.task,legend_done:project.project_task_data_1
#: model:project.task,legend_done:project.project_task_data_11
#: model:project.task,legend_done:project.project_task_data_12
#: model:project.task,legend_done:project.project_task_data_13
#: model:project.task,legend_done:project.project_task_data_14
#: model:project.task,legend_done:project.project_task_data_2
#: model:project.task,legend_done:project.project_task_data_4
#: model:project.task,legend_done:project.project_task_data_5
#: model:project.task,legend_done:project.project_task_data_6
#: model:project.task,legend_done:project.project_task_data_7
#: model:project.task,legend_done:project.project_task_data_9
#: model:project.task.type,legend_done:project.project_stage_data_0
#: model:project.task.type,legend_done:project.project_stage_data_1
#: model:project.task.type,legend_done:project.project_stage_data_2
#, python-format
msgid "Ready for Next Stage"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for next stage"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of ${object.name}"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
msgid "Red"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "Ref"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr ""

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Schedule your teams across projects and estimate deadlines more accurately."
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Customer"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Set Email Aliases"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set Email Template to Stages"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Settings"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on dashboard"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__name
msgid "Stage Name"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Stages"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Starred"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
msgid "Start Date"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__subtask_project_id
#: model:ir.model.fields,field_description:project.field_project_task__subtask_project_id
msgid "Sub-task Project"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task count"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Subtasks"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__name
msgid "Tag Name"
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
msgid "Tags"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Task"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Task %s cannot have several subtask levels."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr ""

#. module: project
#: model:ir.filters,name:project.filter_task_report_task_pipe
msgid "Task Pipe"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Tasks"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Testing"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There is no rating for this object at the moment"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"This report allows you to analyse the performance of your projects and "
"users. You can analyse the quantities of tasks, the hours spent compared to "
"the planned hours, the average number of days to open or close a task, etc."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "This will create a new project and redirect us to its stages."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Time Scheduling"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__resource_calendar_id
msgid "Timetable working hours to adjust the gantt diagram report"
msgstr ""

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#, python-format
msgid "Title"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                    Chat in real time or by email to collaborate efficiently."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Today"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
msgid "Top 5 partner ratings of last 15 days."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_unread
#: model:ir.model.fields,field_description:project.field_project_task__message_unread
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Use collaborative rich text pads on tasks"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Use the breadcrumbs to <b>go back to your tasks pipeline</b>."
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_user
msgid "User"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_email
msgid "User Email"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Waiting for the next stage: use %s and %s bullets."
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__is_favorite
msgid "Whether this project should be displayed on your dashboard."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
msgid "Working days to assign"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
msgid "Working days to close"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
msgid "Working hours to assign"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
msgid "Working hours to close"
msgstr ""

#. module: project
#: model:ir.filters,name:project.filter_task_report_workload
msgid "Workload"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot create recursive tasks."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete a project containing tasks. You can either archive it or "
"first delete all of its tasks."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Your task must be in the same company as its project."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid "activate a sample project"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
msgid "average"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
msgid "bad"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "for project:"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_project_rating_partner_stat
msgid "happy"
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid "to play with."
msgstr ""
