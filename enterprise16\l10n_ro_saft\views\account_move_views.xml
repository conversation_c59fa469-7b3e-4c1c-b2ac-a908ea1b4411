<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_move_form_l10n_ro" model="ir.ui.view">
        <field name="name">account.move.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <group name="accounting_info_group" position="inside">
                <field name="l10n_ro_is_self_invoice" string="Is self invoice (RO)?" attrs="{'invisible': ['|', ('move_type', 'not in', ['in_invoice', 'in_refund']), ('country_code', '!=', 'RO')]}"/>
            </group>
        </field>
    </record>
</odoo>
