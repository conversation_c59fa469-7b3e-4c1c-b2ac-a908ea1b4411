<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Job Category Form View -->
        <record id="view_hr_job_category_form" model="ir.ui.view">
            <field name="name">hr.job.category.form</field>
            <field name="model">hr.job.category</field>
            <field name="arch" type="xml">
                <form string="Job Category">
                    <sheet>
                        <group>
                            <field name="name" required="1"/>
                            <field name="code" required="1"/>
                            <field name="sequence"/>
                            <field name="minimum_education_level" required="1"/>
                            <field name="minimum_experience_years"/>
                            <field name="required_field_experience"/>
                            <field name="active"/>
                        </group>

                        <group string="Education Requirements">
                            <field name="education_requirements" required="1"/>
                        </group>

                        <group string="Description">
                            <field name="description"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>



        <!-- Job Categories Action -->
        <record id="action_hr_job_category" model="ir.actions.act_window">
            <field name="name">Job Categories</field>
            <field name="res_model">hr.job.category</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_hr_job_category_form"/>
            <field name="target">current</field>
        </record>

    </data>
</odoo>
