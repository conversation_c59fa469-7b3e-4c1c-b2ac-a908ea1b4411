<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="ir_cron_scheduler_appraisal" model="ir.cron">
            <field name="name">Appraisal: Run employee appraisal</field>
            <field name="model_id" ref="hr_appraisal.model_res_company"/>
            <field name="state">code</field>
            <field name="code">model._run_employee_appraisal_plans()</field>
            <field eval="True" name="active" />
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>
        <record forcecreate="True" id="appraisal_create_in_advance_days" model="ir.config_parameter">
            <field name="key">hr_appraisal.appraisal_create_in_advance_days</field>
            <field name="value">8</field>
        </record>
    </data>
</odoo>
