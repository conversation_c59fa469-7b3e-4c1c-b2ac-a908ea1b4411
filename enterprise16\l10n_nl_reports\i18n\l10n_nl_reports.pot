# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-09 09:29+0000\n"
"PO-Revision-Date: 2022-11-09 09:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#, python-format
msgid "- Country"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#, python-format
msgid "- VAT number"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_assets
msgid "ACTIVA"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_depr_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_depr
msgid "Afschrijving"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_idep_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_idep
msgid "Afschrijving immateriële vaste activa"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_mdep_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_mdep
msgid "Afschrijving materiële vaste activa"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_premium
msgid "Agio"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_gcost_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_gcost
msgid "Algemene beheerkosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_gen_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_gen
msgid "Algemene kosten"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.model,name:l10n_nl_reports.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.column,name:l10n_nl_reports.account_financial_report_bs_column
#: model:account.report.column,name:l10n_nl_reports.account_financial_report_pnl_column
msgid "Balance"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report,name:l10n_nl_reports.account_financial_report_bs
msgid "Balans"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_taxes_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_taxes
msgid "Belastingen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_profit_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_profit
msgid "Bruto-omzetresultaat"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:94
#, python-format
msgid "Continue and skip country fields"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_cred_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_cred
msgid "Crediteuren"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_distr_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_distr
msgid "Distributiekosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_eq
msgid "EIGEN VERMOGEN"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_fin
msgid "Financiële Vaste Active"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_fres_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_fres
msgid "Financiële resultaat"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_err_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_err
msgid "Foutenrekening"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.model,name:l10n_nl_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_cap
msgid "Gestort en Opgevraagd Kapitaal"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_revaluation_res
msgid "Herwaarderingsreserve"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_curr_year_earnings_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_curr_year_earnings
msgid "Huidig Jaar Winst"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_hous_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_hous
msgid "Huisvestingskosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_immat
msgid "Immateriële Vaste Active"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_off_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_off
msgid "Kantoorkosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_sh_debt_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_sh_debt
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_short_debt
msgid "Kortlopende Schulden"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_pcost_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_pcost
msgid "Kosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_cogs_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_cogs
msgid "Kostprijs van de omzet"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.model.fields,field_description:l10n_nl_reports.field_res_users__l10n_nl_report_xaf_userid
msgid "L10N Nl Report Xaf Userid"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_long_debt_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_long_debt
msgid "Langlopende Schulden"
msgstr ""

#. module: l10n_nl_reports
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports.xaf_audit_file
msgid "Last write"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_cash
msgid "Liquide Middelen"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.actions.server,name:l10n_nl_reports.ir_cron_load_xsd_file_ir_actions_server
#: model:ir.cron,cron_name:l10n_nl_reports.ir_cron_load_xsd_file
#: model:ir.cron,name:l10n_nl_reports.ir_cron_load_xsd_file
msgid "Load XSD File (Dutch reports)"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_sala_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_sala
msgid "Lonen en salarissen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_mat
msgid "Materiële Vaste Active"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.actions.client,name:l10n_nl_reports.action_account_financial_report_nl_pnl
msgid "NL Profit And Loss"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_net_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_net
msgid "Netto-omzet"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_ebitda_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_ebitda
msgid "Netto-omzetresultaat"
msgstr ""

#. module: l10n_nl_reports
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports.xaf_audit_file
msgid "Odoo"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_undist_profit_balance
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_undist_profit_balance_account_codes
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_undist_profit_balance_aggregate
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_undist_profit
msgid "Onverdeelde Winst"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_otcb_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_otcb
msgid "Overige Lasten En Opbrengsten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_other_res
msgid "Overige Reserves"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_oth_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_oth
msgid "Overige bedrijfsopbrengsten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_opc_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_opc
msgid "Overige personeelskosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_leq
msgid "PASSIVA"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_pens_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_pens
msgid "Pensioenlasten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_2_1_1_1_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_2_1_1_1
msgid "Rente baten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_intl_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_intl
msgid "Rente en overige financiële lasten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_resnb_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_resnb
msgid "Resultaat Na Belastingen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_resvb_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_resvb
msgid "Resultaat Voor Belastingen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_reso_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_reso
msgid "Resultaat overige activa"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_l
msgid "SCHULDEN"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_soc_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_soc
msgid "Sociale lasten"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#, python-format
msgid "Some fields must be specified on the company:"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:87
#, python-format
msgid ""
"Some partners are located in countries forbidden in dutch audit reports.\n"
"Those countries are:\n\n"
"%s\n"
"If you continue, please note that the fields <country> and <taxRegistrationCountry> "
"will be skipped in the report for those partners.\n\n"
"Otherwise, please change the address of the partners located in those countries.\n"
msgstr ""

#. module: l10n_nl_reports
#: model:ir.model,name:l10n_nl_reports.model_res_users
msgid "User"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#, python-format
msgid "There is no data to export."
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_fixed
msgid "VASTE ACTIVA"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_current
msgid "VLOTTENDE ACTIVA"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_sale_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_sale
msgid "Verkoopkosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_pnl_trans_balance
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_pnl_trans
msgid "Vervoerskosten"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_prev_years_earnings_balance
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_prev_years_earnings_balance_aggregate
#: model:account.report.expression,report_line_name:l10n_nl_reports.account_financial_report_bs_prev_years_earnings_balance_domain
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_prev_years_earnings
msgid "Voorgaande Jaren Winst"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_stock
msgid "Voorraden"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_provisions
msgid "Voorzieningen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_claims
msgid "Vorderingen"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report.line,name:l10n_nl_reports.account_financial_report_bs_legal_res
msgid "Wettelijke en Statutaire Reserves"
msgstr ""

#. module: l10n_nl_reports
#: model:account.report,name:l10n_nl_reports.account_financial_report_pnl
msgid "Winst-en-verliesrekening"
msgstr ""

#. module: l10n_nl_reports
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#: code:addons/l10n_nl_reports/models/account_general_ledger.py:0
#, python-format
msgid "XAF"
msgstr ""
