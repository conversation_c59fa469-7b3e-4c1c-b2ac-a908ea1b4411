<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_company_be" model="res.company">
        <field name="name">My Belgian Company</field>
        <field name="country_id" ref="base.be"/>
        <field name="currency_id" ref="base.EUR"/>
        <field name="l10n_be_company_number">0477472701</field>
        <field name="l10n_be_revenue_code">1293</field>
        <field name="street">Rue du Paradis</field>
        <field name="zip">6870</field>
        <field name="city">Eghezee</field>
        <field name="vat">BE0897223670</field>
        <field name="phone">*********</field>
        <field name="sdworx_code">1111111</field>
        <field name="resource_calendar_id" ref="resource.resource_calendar_std_38h"/>
    </record>
    <record id="base.user_admin" model="res.users">
        <field name="company_ids" eval="[(4, ref('l10n_be_hr_payroll.res_company_be'))]"/>
    </record>
    <record id="base.user_demo" model="res.users">
        <field name="company_ids" eval="[(4, ref('l10n_be_hr_payroll.res_company_be'))]"/>
    </record>

    <record id="hr_holidays_allocation_" model="hr.leave.allocation">
        <field name="name">Paid Time Off allocation</field>
        <field name="holiday_status_id" ref="hr_holidays.holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="holiday_type">company</field>
        <field name="mode_company_id" ref="l10n_be_hr_payroll.res_company_be"/>
        <field name="date_from" eval="time.strftime('%Y-01-01')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="holiday_type_european" model="hr.leave.type">
        <field name="company_id" ref="l10n_be_hr_payroll.res_company_be"/>
    </record>

    <!-- Public Holiday -->
    <record id="resource_public_time_off_be" model="resource.calendar.leaves">
        <field name="name">Celebration Day</field>
        <field name="company_id" ref="l10n_be_hr_payroll.res_company_be"/>
        <field name="date_from" eval="(datetime.today() + relativedelta(days=+14)).strftime('%Y-%m-%d 07:00:00')"></field>
        <field name="date_to" eval="(datetime.today() + relativedelta(days=+14)).strftime('%Y-%m-%d 15:36:00')"></field>
        <field name="work_entry_type_id" ref="hr_work_entry_contract.work_entry_type_legal_leave"/>
    </record>

    <!-- Stress day -->
    <record id="hr_leave_stress_day_be" model="hr.leave.stress.day">
        <field name="name">Annual Event</field>
        <field name="company_id" ref="l10n_be_hr_payroll.res_company_be"/>
        <field name="start_date" eval="(datetime.today() + relativedelta(days=+21)).strftime('%Y-%m-%d 07:00:00')"></field>
        <field name="end_date" eval="(datetime.today() + relativedelta(days=+21)).strftime('%Y-%m-%d 16:00:00')"></field>
        <field name="color">9</field>
    </record>

    <data noupdate="1">
        <function model="res.company"
            name="_create_dashboard_notes"
            eval="[ref('l10n_be_hr_payroll.res_company_be')]"/>
    </data>
</odoo>
