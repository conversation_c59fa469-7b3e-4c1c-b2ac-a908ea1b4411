.ibox-tools {
	cursor: pointer !important;
	float: right !important;
	position: relative !important;
	height: 20px !important;
	width: 15px !important;
	z-index: 1000 !important;
	border: 1px solid #AEAEAE !important;
	border-radius: 16px !important;
	background: #605F61 !important;
	font-size: 16px !important;
	font-weight: bold !important;
	display: inline-block !important;
	line-height: 0px !important;
	padding: 0px 0px !important;
	margin-top: -13px !important;
	margin-right: -2px !important;
	text-align: center !important;
}
/*.fc-event-container {*/
/*	width: 2% !important;*/
/*}*/
.fc-event span.fc-time {
	display: none;
}
.fc-state-active {
	border-color: #7C7BAD;
}

.fc-header-toolbar {
	margin: 10px;
}

button:focus {
	outline: 0;
}



.prev {
	background: #7C7BAD;
	color: white;
	border-color: #7C7BAD;
	outline: none;
	border: none;
}

.next {
	background: #7C7BAD;
	color: white;
	border-color: #7C7BAD;
	outline: none;
	border: none;
}

.title-display {
	font-size: 17px;
	font-weight: 800;
}

/*#shops{*/
/*width:80px*/
/*}*/


.done {
    background-color: #FF0000;
    color: #FF0000;
}

.room_available {
    width: 100%;
    height: 100%;
    background-color: #32CD32;
    color: #32CD32;
    top: 0;
    left: 0;
    }


.draft {
    color: #FFDA2F;
    background-color: #FFDA2F;
}

.room_booked_and_dirty {
    width: 100%;
    height: 100%;
    background-color: #61380B;
    color: #61380B;
    top: 0;
    left: 0;
    }


.room_dirty {
    width: 100%;
    height: 100%;
    background-color: black;
    color: black;
    top: 0;
    left: 0;
}


.confirm {
    background-color: #A6A6A6;
    color: #A6A6A6;
}

.popper {
	width: 130px;
	padding: 5px;
	text-align: center;
}

.popover-header {
	font-size: 15px;
	text-align: center;
}

.popover-body {
	font-size: 13px;
}

.fc-right {
	position: sticky;
	margin-right: -518px;
	left: 46em;
}

.fc-scroller {
	background-color: white;
}

th.fc-today div {
	background-color: #0077be !important;
	color: white;
}

td.fc-today {
	background-color: white !important;
    border: 1px solid #0077be !important;
}

.fc-prev-button, .fc-next-button {
	font-size: x-large !important;
    /*color: grey !important;*/
	top:-1px;
}

.fc-expander {
	padding: 3px 0 12px 18em;
    position: absolute;
	left: -3px;
}

.fc-cell-content:has(.fc-expander) {
	font-weight: 900;
}

/*---------------------------------------------------*/

.background {
    height: 100%;
    background: ##DBE1E6;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    70% {
        opacity: 1;
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.background {
    animation: bounceIn 0.5s ease;
}




/*
.o_action_manager center h1 {
    margin-top: 32px !important;
    font-family: fangsong;
    font-size: 36px;
    letter-spacing: 1vh;
}*/

.hotel_dash_logo{
    height: 85px;
    margin-top: 17px;
}

.card-category{
	font-size: 20px;
/*    font-family: fantasy;*/
/*    font-weight: 600;*/
}
.fa-sign-out{
	font-size: 20px;
}
.col_count{
	margin: auto;
}
.col_count .card-title{
    font-size: 40px;
/*    font-family: emoji;*/
}


/*-------------------------------*/



.parent {
  align-items: center;
  display: flex;
  justify-content: center;
border-radius: 10px;
-moz-border-radius: 10px;
-webkit-border-radius: 10px;
}

.child {
  display: flex;
  align-items: center;
}

.flex_box_icon {
	background-color: rgba(255, 255, 255, 0.6);
	width: 40px;
	height: 40px;
	border-radius: 50%;
}
.flex_box_icon i {
	margin-left: 38%;
	margin-top: 35%;
}

.booking_col_h2{
	text-align: center;
}

.hover-effect {
    color: white;
    background: linear-gradient(135deg, #B881D9 0%, #8E4BB5 50%, #6B2391 100%);
}

.hover-effect:hover {
color: black;
background: linear-gradient(135deg, #FCC2D7 0%, #FF94BB 50%, #FF6198 100%);
}

.hover-effect-blue {
    color: white;
    background: #6A5ACD;
}

.hover-effect-brown {
    color: white;
    background: #2CBAA0;
}

.hover-effect-orange {
    color: white;
    background: #F87B1C;
}

.hover-effect-green {
    color: white;
    background: #009462;
}

.hover-effect-grey {
    color: white;
    background: grey;
}

.hover-effect-purple {
    color: white;
    background: purple;
}

.hotel {
    color: white;
    background: #b4a1ff;
}

.parent {
background: #FFFFFF;
}

.parent:hover {
background: #b4a1ff;
}

.card-text:last-child{
margin-bottom: 7px;
}

.hover-color {
background: #b4a1ff;
}

.hover-pop {
    transition: transform 0.3s ease;
}

.hover-pop:hover {
    transform: scale(1.1);
}

.card-3d-effect {
    perspective: 1000px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.3s ease;
}

/* CSS for freezing the calendar header */
.fc-day-header, .fc-head {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}

.fc-scroller {
    height: auto !important;
    overflow-y: auto !important;
}

.fc-head {
    position: sticky;
    top: 0;
    z-index: 20;
}

.fc-body > tr > .fc-widget-content {
    overflow: visible !important;
}

.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 1;
}

.fc-row .fc-highlight-skeleton {
    z-index: 2;
}

/* Fix for resource headers */
.fc-timeline .fc-divider {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}

.fc-time-area .fc-rows .fc-widget-header {
    position: sticky;
    left: 0;
    z-index: 9;
    background-color: white;
}

/* Ensure scrollable content */
#booking_calendar .fc-view {
    overflow: visible;
}

/* Make the content scrollable while keeping header fixed */
.fc-timeline-body {
    overflow-y: auto;
}

.fc-timeline-event-container {
    position: relative;
}

