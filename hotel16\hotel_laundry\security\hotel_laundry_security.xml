<?xml version="1.0" encoding="utf-8"?>
<openerp>
<data noupdate="1">
	<record id="group_laundry_manager" model="res.groups">
        <field name="name">Hotel Management/ Laundry Manager</field>
    </record>
    <record id="group_laundry_user" model="res.groups">
        <field name="name">Hotel Management / Laundry User</field>
    </record>
	
	 <!-- Multi - Company Rules -->
	
	<record model="ir.rule" id="laundry_management_comp_rule">
        <field name="name">Laundry Mang multi-company</field>
        <field name="model_id" ref="model_laundry_management"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>

    <!--<record model="ir.rule" id="checkout_configuration_comp_rule">
        <field name="name">Checkout Configuration multi-company</field>
        <field name="model_id" ref="model_checkout_configuration"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="hotel_reservation_line_comp_rule">
        <field name="name">Sale Shop multi-company</field>
        <field name="model_id" ref="model_hotel_reservation"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>-->
	
</data>
</openerp>
