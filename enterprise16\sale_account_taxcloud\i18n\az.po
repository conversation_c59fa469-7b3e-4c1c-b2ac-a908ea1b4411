# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_taxcloud
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2022\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Go to Settings."
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr ""

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "Satış Sifarişi"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "Satış Sifarişi Sətri"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Tax"
msgstr "Vergi"

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Update taxes"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Use TaxCloud API"
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
