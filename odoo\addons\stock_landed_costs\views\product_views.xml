<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_product_landed_cost_form" model="ir.ui.view">
            <field name="name">product.template.landed.cost.form</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="account.product_template_form_view"/>
            <field name="arch" type="xml">
                <group name="bill" position="inside">
                    <field name="landed_cost_ok" attrs="{'invisible':[('detailed_type', '!=', 'service')]}"/>
                    <field name="split_method_landed_cost" attrs="{'invisible': ['|', ('landed_cost_ok', '=', False), ('type', '!=', 'service')]}"/>
                </group>
            </field>
        </record>
    </data>
</odoo>
