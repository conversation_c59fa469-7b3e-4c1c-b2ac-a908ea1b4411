# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_multiple_barcodes
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-16 14:30+0000\n"
"PO-Revision-Date: 2022-08-16 14:30+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_product__barcode_ids
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_template__barcode_ids
msgid "Additional Barcodes"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__name
msgid "Barcode"
msgstr ""

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Cancel"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__create_uid
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__create_uid
msgid "Created by"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__create_date
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__create_date
msgid "Created on"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__display_name
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__display_name
msgid "Display Name"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__id
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__id
msgid "ID"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard____last_update
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi____last_update
msgid "Last Modified on"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__write_uid
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__write_uid
msgid "Last Updated by"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__write_date
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__write_date
msgid "Last Updated on"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__name
msgid "New Barcode"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.constraint,message:product_multiple_barcodes.constraint_product_product_barcode_uniq
msgid "No error"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_product
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_product_barcode_multi__product_id
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_barcode_multi_view_search
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_variant_barcode_multi_view_search
msgid "Product"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_barcode_multi
msgid "Product Barcode Multi"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_product_template
msgid "Product Template"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model.fields,field_description:product_multiple_barcodes.field_multiply_barcode_wizard__remember_previous_barcode
msgid "Remember previous barcode in \"Additional Barcodes\" field"
msgstr ""

#. module: product_multiple_barcodes
#: code:addons/product_multiple_barcodes/models/product_product.py:0
#, python-format
msgid ""
"The following barcode(s): {0} was found in other active products.\n"
"Note that product barcodes should not repeat themselves both in \"Barcode\" field and \"Additional Barcodes\" field."
msgstr ""

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_product_view_form_multiply_barcode
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.product_template_view_form_multiply_barcode
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Update Barcode"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.actions.act_window,name:product_multiple_barcodes.action_multiply_barcode_wizard
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "Update Product Barcode"
msgstr ""

#. module: product_multiple_barcodes
#: model:ir.model,name:product_multiple_barcodes.model_multiply_barcode_wizard
msgid "Update Product Multiply Barcode Wizard"
msgstr ""

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid ""
"Use this popup in case you would like to update barcode of current product. It also allows you to record automatically previous barcode into \"Additional Barcodes\" field,\n"
"                        so it will still be searchable and also in this case you will be 100% sure that this barcode will not be used by other products also"
msgstr ""

#. module: product_multiple_barcodes
#: model_terms:ir.ui.view,arch_db:product_multiple_barcodes.view_multiply_barcode_wizard_form
msgid "or"
msgstr ""
