.point-of-sale #rightheader .header-button-switch{
    float:right;
    height:32px;
    padding-left:10px;
    padding-right:10px;
    border-right:  1px solid #3a3a3a;
    border-left:  1px solid #3a3a3a;
    color:#DDD;
    line-height:32px;
    
    cursor: pointer;

    -webkit-transition-property: background;
    -webkit-transition-duration: 0.2s;
    -webkit-transition-timing-function: ease-out;
}

.onoffswitch {
    position: relative; width: 103px;
    -webkit-user-select:none; -moz-user-select:none; -ms-user-select: none;
}
.onoffswitch-checkbox {
    display: none;
}
.onoffswitch-label {
    display: block; overflow: hidden; cursor: pointer;
    border: 2px solid #666666; border-radius: 30px;
}
.onoffswitch-inner {
    width: 200%; margin-left: -100%;
    -moz-transition: margin 0.3s ease-in 0s; -webkit-transition: margin 0.3s ease-in 0s;
    -o-transition: margin 0.3s ease-in 0s; transition: margin 0.3s ease-in 0s;
}
.onoffswitch-inner:before, .onoffswitch-inner:after {
    float: left; width: 50%; height: 30px; padding: 0; line-height: 30px;
    font-size: 16px; color: white; font-family: Trebuchet, Arial, sans-serif; font-weight: bold;
    -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box;
    border-radius: 30px;
    box-shadow: 0px 15px 0px rgba(0,0,0,0.08) inset;
}
.onoffswitch-inner:before {
    content: "Room";
    padding-left: 11px;
    background-color: #ABACD1; color: #FFFFFF;
    border-radius: 30px 0 0 30px;
}
.onoffswitch-inner:after {
    content: "Table";
    padding-right: 11px;
    background-color: #FFFFFF; color: #666666;
    text-align: right;
    border-radius: 0 30px 30px 0;
}
.onoffswitch-switch {
    width: 30px; margin: 0px;
    background: #FFFFFF;
    border: 2px solid #666666; border-radius: 30px;
    position: absolute; top: 0; bottom: 0; right: 69px;
    -moz-transition: all 0.3s ease-in 0s; -webkit-transition: all 0.3s ease-in 0s;
    -o-transition: all 0.3s ease-in 0s; transition: all 0.3s ease-in 0s; 
    background-image: -moz-linear-gradient(center top, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 80%); 
    background-image: -webkit-linear-gradient(center top, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 80%); 
    background-image: -o-linear-gradient(center top, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 80%); 
    background-image: linear-gradient(center top, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 80%);
    box-shadow: 0 1px 1px white inset;
}
.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
    margin-left: 0;
}
.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
    right: 0px; 
}