###########################################
from odoo import api, fields, models, tools,_
from odoo.exceptions import UserError


class HotelReservationLine(models.Model):
    _inherit = 'hotel.reservation.line'

    real_checkout_time = fields.Datetime(string="Real Checkout Time")

class hotel_reservation(models.Model):
    _inherit = "hotel.reservation"

    gust_no = fields.Integer(string="Guests", compute='_compute_gust_no', store=True)

    @api.depends('adults', 'childs')
    def _compute_gust_no(self):
        for rec in self:
            rec.gust_no = rec.adults + rec.childs




class ResCompany(models.Model):
    _inherit = 'res.company'

    footer_img = fields.Binary(string='Footer')
    header_img = fields.Binary(string='Header1')