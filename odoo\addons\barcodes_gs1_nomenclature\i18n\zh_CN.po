# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes_gs1_nomenclature
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>EN <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:50+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__alpha
msgid "Alpha-Numeric Name"
msgstr "阿尔法-数字名称"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid ""
"Alternative regex delimiter for the FNC1. The separator must not match the "
"begin/end of any related rules pattern."
msgstr "FNC1的替代性正则表达式分隔符。分隔符不能与任何相关规则模式的开始/结束匹配。"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__associated_uom_id
msgid "Associated Uom"
msgstr "相关联的Uom"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_nomenclature
msgid "Barcode Nomenclature"
msgstr "条码命名规则"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_rule
msgid "Barcode Rule"
msgstr "条码规则"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__use_date
msgid "Best before Date"
msgstr "在此日期前最佳"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__date
msgid "Date"
msgstr "日期"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid "Decimal"
msgstr "小数"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location_dest
msgid "Destination location"
msgstr "目的位置"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid "Encoding"
msgstr "编码"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__expiration_date
msgid "Expiration Date"
msgstr "过期日期"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid "FNC1 Separator"
msgstr "FNC1分离器"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid "GS1 Content Type"
msgstr "GS1内容类型"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__encoding__gs1-128
msgid "GS1-128"
msgstr "GS1-128"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid ""
"If True, use the last digit of AI to determine where the first decimal is"
msgstr "如果为真，使用AI的最后一位数字来确定第一个小数的位置"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: can't be formated as date"
msgstr "无效的条形码：不能作为日期的形式。"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: the check digit is incorrect"
msgstr "无效的条形码：检查数字不正确"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid "Is GS1 Nomenclature"
msgstr "GS1命名法是什么？"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location
msgid "Location"
msgstr "位置"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "批次"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__measure
msgid "Measure"
msgstr "测量"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__identifier
msgid "Numeric Identifier"
msgstr "数值标识符"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__pack_date
msgid "Pack Date"
msgstr "包装日期"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package
msgid "Package"
msgstr "包裹"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package_type
msgid "Package Type"
msgstr "包裹类型"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__quantity
msgid "Quantity"
msgstr "数量"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid "The FNC1 Separator Alternative is not a valid Regex: "
msgstr "FNC1分离器备选方案不是一个有效的Regex。"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid ""
"The GS1 content type defines what kind of data the rule will process the "
"barcode as:        * Date: the barcode will be converted into a Odoo "
"datetime;        * Measure: the barcode's value is related to a specific "
"UoM;        * Numeric Identifier: fixed length barcode following a specific "
"encoding;        * Alpha-Numeric Name: variable length barcode."
msgstr ""
"GS1内容类型定义了规则将把条形码处理成什么样的数据。        * 日期：条形码将被转换为Odoo日期时间； * "
"测量：条形码的值与特定的UoM有关； * 数字标识符：遵循特定编码的固定长度的条形码； * 阿尔法-数字名称：可变长度的条形码。"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid "The rule pattern \"%s\" is not a valid Regex: "
msgstr "规则模式\"%s\"不是一个有效的Regex。"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid ""
"The rule pattern \"%s\" is not valid, it needs two groups:\n"
"\t- A first one for the Application Identifier (usually 2 to 4 digits);\n"
"\t- A second one to catch the value."
msgstr ""
"规则模式\"%s\"是无效的，它需要两组。\n"
"\t- 第一组是应用标识符（通常是2到4位）。\n"
"\t- 第二组用来捕捉数值。"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid ""
"There is something wrong with the barcode rule \"%s\" pattern.\n"
"If this rule uses decimal, check it can't get sometime else than a digit as last char for the Application Identifier.\n"
"Check also the possible matched values can only be digits, otherwise the value can't be casted as a measure."
msgstr ""
"条码规则\"%s\"模式有问题。\n"
"如果这个规则使用十进制，请检查它不能得到除数字以外的任何其他数字作为应用标识符的最后一个字符。\n"
"还要检查可能的匹配值是否只能是数字，否则该值就不能被铸成一个度量。"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid ""
"This Nomenclature use the GS1 specification, only GS1-128 encoding rules is "
"accepted is this kind of nomenclature."
msgstr "这种命名法使用GS1规范，只有GS1-128编码规则被接受为这种命名法。"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be parsed by any barcode rules."
msgstr "这个条形码不能被任何条形码规则所解析。"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be partially or fully parsed."
msgstr "这个条形码不能被部分或完全解析。"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr "这条规则只应用于特殊编码的条形编码"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__type
msgid "Type"
msgstr "类型"
