<odoo>
    <data>


        <record id="product_template_view_form" model="ir.ui.view">
            <field name="name">product_template_view_form</field>
            <field name="model">product.template</field>
             <field name="priority">100</field>
            <field name="arch" type="xml">
                <form string="Product">
                <sheet name="product_form">
                    <field name="active" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="id" invisible="True"/>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_title">
                        <label for="name" string="Product Name"/>
                        <h1>
                            <div class="d-flex">
                                <field name="priority" widget="priority" class="me-3"/>
                                <field class="text-break" name="name" placeholder="e.g. <PERSON><PERSON>"/>
                            </div>
                        </h1>
                    </div>
                    <notebook>
                        <page string="General Information" name="general_information">
                            <group>
                                <group name="group_standard_price">
                                    <label for="list_price"/>
                                    <div name="pricing">
                                      <field name="list_price" class="oe_inline" widget='monetary'
                                        options="{'currency_field': 'currency_id', 'field_digits': True}"/>
                                    </div>
                                </group>
                                <group>
                                    <field name="default_code"/>
                                </group>
                            </group>
                            <group string="Internal Notes">
                                <field colspan="2" name="description" nolabel="1" placeholder="This note is only for internal purposes."/>
                            </group>
                        </page>
                      </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
            </field>
        </record>


        <record id="product_product_view_form" model="ir.ui.view">
            <field name="name">product_product_view_form</field>
            <field name="model">product.product</field>
             <field name="priority">100</field>
            <field name="arch" type="xml">
                <form string="Product">
                <sheet name="product_form">
                    <field name="type" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="active" invisible="1"/>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="id" invisible="True"/>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_title">
                        <label for="name" string="Product Name"/>
                        <h1>
                            <div class="d-flex">
                                <field name="priority" widget="priority" class="me-3"/>
                                <field class="text-break" name="name" placeholder="e.g. Cheese Burger"/>
                            </div>
                        </h1>
                    </div>
                    <notebook>
                        <page string="General Information" name="general_information">
                            <group>
                                <group name="group_standard_price">
                                    <label for="list_price"/>
                                    <div name="pricing">
                                      <field name="list_price" class="oe_inline" widget='monetary'
                                        options="{'currency_field': 'currency_id', 'field_digits': True}"/>
                                    </div>
                                </group>
                                <group>
                                    <field name="default_code"/>
                                </group>
                            </group>
                            <group string="Internal Notes">
                                <field colspan="2" name="description" nolabel="1" placeholder="This note is only for internal purposes."/>
                            </group>
                        </page>
                      </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
            </field>
        </record>


    </data>
</odoo>