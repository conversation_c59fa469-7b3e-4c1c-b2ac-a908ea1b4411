# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_crm
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', False)]}\">Leads</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', True)]}\">Opportunities</span>"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span>at</span>"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span>posted on</span>"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__conversion_source
msgid "Conversion Source"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert"
msgstr "Converter"

#. module: social_crm
#: model:ir.actions.act_window,name:social_crm.social_post_to_lead_action
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert Post to Lead"
msgstr ""

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post_to_lead
msgid "Convert Social Post to Lead"
msgstr ""

#. module: social_crm
#. odoo-javascript
#: code:addons/social_crm/static/src/xml/social_crm_button.xml:0
#: code:addons/social_crm/static/src/xml/social_crm_button.xml:0
#, python-format
msgid "Create Lead"
msgstr ""

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__create
msgid "Create a new customer"
msgstr "Criar um novo cliente"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_date
msgid "Created on"
msgstr "Criado em"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__partner_id
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Customer"
msgstr "Cliente"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__display_name
msgid "Display Name"
msgstr "Nome"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__nothing
msgid "Do not link to a customer"
msgstr "Não relacionar com um cliente"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__comment
msgid "From a comment"
msgstr ""

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__stream_post
msgid "From a stream post"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__id
msgid "ID"
msgstr "ID"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__leads_opportunities_count
msgid "Leads / Opportunities count"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Leads:"
msgstr ""

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__exist
msgid "Link to an existing customer"
msgstr "Associar a um cliente existente"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Opportunities:"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__author_name
msgid "Post Author Name"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_content
msgid "Post Content"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_datetime
msgid "Post Datetime"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_link
msgid "Post Link"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__action
msgid "Related Customer"
msgstr "Cliente relacionado"

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
#, python-format
msgid "Request from %s"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_account_id
msgid "Social Account"
msgstr ""

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post
msgid "Social Post"
msgstr "Publicação Social"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_stream_post_id
msgid "Social Stream Post"
msgstr ""

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
#, python-format
msgid "The lead has been created successfully."
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__use_leads
msgid "Use Leads"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_campaign_id
msgid "Utm Campaign"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_medium_id
msgid "Utm Medium"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_source_id
msgid "Utm Source"
msgstr ""
