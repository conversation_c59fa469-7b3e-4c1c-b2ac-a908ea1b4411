# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-27 09:19+0000\n"
"PO-Revision-Date: 2019-08-26 09:38+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Accept"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__category
msgid "Action to Perform"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_mail_activity_type
msgid "Activity Type"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Add to Call Queue"
msgstr ""

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "Add voip queue support to a model"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_always_transfer
msgid "Always Redirect to Handset"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "CONTACTS"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid "Call activity type is not of category \"phonecall\""
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/phone_field.js:0
#, python-format
msgid "Calling %s"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model_terms:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
#, python-format
msgid "Cancel"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Cancel failed: %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Connecting..."
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__create_date
msgid "Created on"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Customer unavailable"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__display_name
msgid "Display Name"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_external_phone
msgid "Handset Extension"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__id
msgid "ID"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__incoming
msgid "Incoming"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin____last_update
msgid "Last Modified on"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Log"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "NEXT ACTIVITIES"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__outgoing
msgid "Outgoing"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
msgid "Phone"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Phone call cannot be created. Is it any phone number linked to record %s?"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please Allow the use of the microphone"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Please check if a phone number is given for the current phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "RECENT"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Reject"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_ignore_incoming
msgid "Reject All Incoming Calls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Reject failed: %s"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_login
msgid "SIP Login / Browser's Extension"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_password
msgid "SIP Password"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from CRM app"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search Phone Calls..."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/phone_field.js:0
#, python-format
msgid "Start Calling"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP adress of your PBX Server"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The call was rejected as access rights to the microphone were not given"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The connection cannot be made.</br> Please check your configuration.</br> "
"(Reason received: %s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The customer is temporary unavailable. Please try later."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The server configuration could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The websocket uri could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"There was an error with your registration: Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__transfer_choice
msgid "Transfer Choice"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phonecall_type
msgid "Type"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/phone_call_details.js:0
#, python-format
msgid "Unknown"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "Users"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "VOIP"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
msgid "VOIP Configuration"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_configurator
msgid "VOIP Configurator"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_transfer_wizard
msgid "VOIP Transfer Wizard of Phonecalls"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr ""

#. module: voip
#: code:addons/voip/wizard/voip_phonecall_transfer_wizard.py:0
#, python-format
msgid "Warning"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr ""

#. module: voip
#: code:addons/voip/wizard/voip_phonecall_transfer_wizard.py:0
#, python-format
msgid ""
"Wrong configuration for the call. There is no external phone number "
"configured"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
msgid "transfer Option"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_transfer_wizard__transfer_number
msgid "transfer To"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.wizard_transfer_call_form_view
msgid "transfer the Call"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_transfer_wizard__transfer_choice__extern
msgid "transfer to another External Phone"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_transfer_wizard__transfer_choice__physical
msgid "transfer to your external phone"
msgstr ""
