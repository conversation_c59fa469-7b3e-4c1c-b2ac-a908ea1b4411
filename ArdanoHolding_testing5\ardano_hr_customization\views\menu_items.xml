<odoo>
    <data>
        <record id="action_job_category_list" model="ir.actions.act_window">
            <field name="name">Job Categories</field>
            <field name="res_model">job.category</field>
            <field name="view_mode">tree</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">Add job categories for employee classification.</p>
            </field>
        </record>

        <record id="action_contract_classification_list" model="ir.actions.act_window">
            <field name="name">Contracts Classifications</field>
            <field name="res_model">contract.classification</field>
            <field name="view_mode">tree</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">Add job Contract Classifications for contracts.</p>
            </field>
        </record>

        <menuitem id="menu_contract_classification" name="Contract Classification"
                  parent="hr.menu_human_resources_configuration"
                  action="action_contract_classification_list"
                  sequence="10"/>

        <menuitem id="menu_job_category" name="Job Categories"
                  parent="hr.menu_human_resources_configuration"
                  action="action_job_category_list"
                  sequence="10"/>
    </data>
</odoo>