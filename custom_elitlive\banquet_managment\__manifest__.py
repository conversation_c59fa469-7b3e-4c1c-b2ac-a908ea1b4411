{
    "name" : "Banquet Management ",
    "version" : "********.9",
    "author" : "Pragmatic",
    "category" : "Generic Modules/Banquet Management",

    "description": """
    Module for Banquet/Resort/Property management. You can manage:
    * Configure Property
    * Banquet Configuration
    * Check In, Check out
    * Manage Folio
    * Payment

    Different reports are also provided, mainly for Banquet statistics.
    """,
    "depends": ["base", 'hotel', 'crm', 'hotel_management'],
    "init_xml": [],
    "demo_xml": [
    ],
    "data": [
        'security/banquet_security.xml',
        'security/ir.model.access.csv',
        'data/parking_payment_sequence.xml',
        'wizard/banquet_deposite_amt_view.xml',
        'wizard/reservation_payment_report_view.xml',
        "views/banquet_managment_view.xml",
        'views/banquet_sequence_view.xml',
        'views/reservation_parking_payment.xml',
        'report/actions.xml',
        'report/reservation_payment_report.xml'

    ],
    "active": False,
    "installable": True,
    'license': 'OPL-1',
}
