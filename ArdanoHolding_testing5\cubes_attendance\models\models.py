from odoo import models, fields, api
from datetime import datetime

class YourModel(models.Model):
    _inherit = 'hr.attendance'
    _description = 'Your Model'


    @api.depends('check_in', 'check_out')
    def _compute_work_hours(self):
        for record in self:
            if record.check_in and record.check_out:
                # Calculate the difference between check_in and check_out
                delta = record.check_out - record.check_in
                # Convert the difference to hours (float)
                record.worked_hours = delta.total_seconds() / 3600
            else:
                record.worked_hours = 0.0


    def update_check_in_out(self):
        for line in self.search([]):
            line._compute_work_hours()


    @api.model
    def create(self, vals_list):
        res = super().create(vals_list)
        res._compute_work_hours()
        return res