<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main menu for Hotel Check-in as a standalone module -->
    <menuitem id="hotel_checkin_main_menu" name="Hotel Check-in"
              web_icon="hotel_checkin,static/description/icon.png" sequence="50"
              groups="hotel_checkin.group_hotel_checkin_user,base.group_system"/>
    
    <!-- Confirmed Reservations List View -->
    <record model="ir.ui.view" id="view_hotel_confirmed_reservation_tree">
        <field name="name">hotel.confirmed.reservation.tree</field>
        <field name="model">hotel.reservation</field>
        <field name="arch" type="xml">
            <tree string="Confirmed Reservations" default_order="id desc" create="false" delete="false">
                <field name="reservation_no"/>
                <field name="partner_id"/>
                <field name="reservation_line" invisible="1"/>
                <button name="action_checkin" string="Check-in" type="object" class="btn-primary"/>
            </tree>
        </field>
    </record>
    
    <!-- Confirmed Reservations Action -->
    <record model="ir.actions.act_window" id="action_hotel_confirmed_reservation">
        <field name="name">Confirmed Reservations</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hotel.reservation</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state','=','confirm')]</field>
        <field name="view_id" ref="view_hotel_confirmed_reservation_tree"/>
    </record>
    
    <!-- Confirmed Reservations Menu -->
    <menuitem name="Confirmed Reservations" id="menu_hotel_confirmed_reservation"
              action="action_hotel_confirmed_reservation" parent="hotel_checkin_main_menu"
              sequence="1" groups="hotel_checkin.group_hotel_checkin_user,base.group_system"/>
    
    <!-- Check-in Wizard Form View -->
    <record model="ir.ui.view" id="view_hotel_checkin_wizard_form">
        <field name="name">hotel.checkin.wizard.form</field>
        <field name="model">hotel.checkin.wizard</field>
        <field name="arch" type="xml">
            <form string="Guest Check-in">
                <sheet>
                    <group>
                        <field name="reservation_id" options="{'no_create': True, 'no_open': True}"/>
                        <field name="guest_name"/>
                        <field name="reservation_no"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_checkin" string="Check-in" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    
    <!-- Check-in Wizard Action -->
    <record model="ir.actions.act_window" id="action_hotel_checkin_wizard">
        <field name="name">Guest Check-in</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hotel.checkin.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_hotel_checkin_wizard_form"/>
    </record>
    
    <!-- Check-in Wizard Menu -->
    <menuitem name="Guest Check-in" id="menu_hotel_checkin_wizard"
              action="action_hotel_checkin_wizard" parent="hotel_checkin_main_menu"
              sequence="2" groups="hotel_checkin.group_hotel_checkin_user,base.group_system"/>

    <!-- Service Wizard Form View -->
    <record id="view_hotel_service_wizard_form" model="ir.ui.view">
        <field name="name">hotel.service.wizard.form</field>
        <field name="model">hotel.service.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Service Line">
                <sheet>
                    <group>
                        <group>
                            <field name="reservation_id" options="{'no_create': True}" readonly="1"/>
                            <field name="folio_id" options="{'no_create': True}" readonly="1"/>
                            <field name="product_id"/>
                            <field name="product_uom_qty"/>
                            <field name="product_uom"/>
                        </group>
                        <group>
                            <field name="name"/>
                            <field name="price_unit"/>
                            <field name="discount"/>
                            <field name="tax_id" widget="many2many_tags"/>
                        </group>
                    </group>
                </sheet>
                <footer class="d-flex justify-content-between">
                    <button string="Add Service" name="action_add_service" type="object" class="btn btn-primary fw-bold px-4 py-2" icon="fa-plus-circle"/>
                    <button string="Cancel" class="btn btn-secondary fw-bold px-4" special="cancel" icon="fa-times"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Add Service Action -->
    <record id="action_add_service_wizard" model="ir.actions.act_window">
        <field name="name">Add Service</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hotel.service.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_hotel_service_wizard_form"/>
    </record>
    
    <!-- Add Service Menu -->
    <menuitem name="Add Service" id="menu_hotel_add_service"
              action="action_add_service_wizard" parent="hotel_checkin_main_menu"
              sequence="3" groups="hotel_checkin.group_hotel_checkin_user,base.group_system"/>

    <!-- Add Hotel Check-in User checkbox to user form -->
    <record id="view_users_form_hotel_checkin" model="ir.ui.view">
        <field name="name">res.users.form.hotel.checkin</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook//page[@name='access_rights']//group" position="inside">
                <group string="Hotel Check-in" name="hotel_checkin">
                    <field name="is_hotel_checkin_user"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- New Modern UI Kanban View for Hotel Check-in -->
    <record id="view_hotel_reservation_kanban" model="ir.ui.view">
        <field name="name">hotel.reservation.kanban</field>
        <field name="model">hotel.reservation</field>
        <field name="arch" type="xml">
            <kanban class="o_hotel_checkin_kanban" create="false" delete="false" edit="false">
                <field name="name"/>
                <field name="reservation_no"/>
                <field name="partner_id"/>
                <field name="state"/>
                <field name="checkin_date_display"/>
                <field name="checkout_date_display"/>
                <field name="stay_days"/>
                <field name="is_late_checkout"/>
                <field name="room_count"/>
                <field name="room_names"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click position-relative #{record.state.raw_value === 'done' ? (record.is_late_checkout.raw_value ? 'bg-danger-subtle' : 'bg-success-subtle') : 'bg-white'}">
                            <!-- Status indicator -->
                            <div t-attf-class="position-absolute top-0 end-0 m-2 badge #{record.state.raw_value === 'done' ? (record.is_late_checkout.raw_value ? 'bg-danger' : 'bg-success') : 'bg-warning'}">
                                <t t-if="record.state.raw_value === 'done'">
                                    <t t-if="record.is_late_checkout.raw_value">Late Checkout</t>
                                    <t t-else="">Checked In</t>
                                </t>
                                <t t-else="">Ready for Check-in</t>
                            </div>
                            <div class="oe_kanban_details p-3">
                                <!-- Header -->
                                <div class="o_kanban_record_top mb-2">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title h5 mb-0">
                                            <field name="partner_id"/>
                                        </strong>
                                        <div class="text-muted">
                                            Reservation #<field name="reservation_no"/>
                                        </div>
                                    </div>
                                </div>
                                <!-- Body -->
                                <div class="o_kanban_record_body">
                                    <!-- Dates -->
                                    <div class="reservation_details mb-3">
                                        <div class="detail_row mb-2">
                                            <i class="fa fa-calendar-check text-success me-2"/>
                                            <span>Check In: <field name="checkin_date_display"/></span>
                                        </div>
                                        <div class="detail_row mb-2">
                                            <i class="fa fa-calendar-times text-danger me-2"/>
                                            <span>Check Out: <field name="checkout_date_display"/></span>
                                        </div>
                                        <div class="detail_row mb-2">
                                            <i class="fa fa-clock text-primary me-2"/>
                                            <span>Days: <field name="stay_days"/></span>
                                        </div>
                                    </div>
                                    <!-- Rooms -->
                                    <div class="rooms_list">
                                        <div class="fw-bold mb-2">
                                            <i class="fa fa-bed me-2"/>Rooms: <t t-esc="record.room_count.value || 0"/>
                                        </div>
                                        <div class="ms-3">
                                            <div class="room_item mb-1 d-flex align-items-center">
                                                <i class="fa fa-key text-muted me-2"/>
                                                <span><field name="room_names"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Footer -->
                                <div class="o_kanban_record_bottom mt-3">
                                    <t t-if="record.state.raw_value === 'confirm'">
                                        <button name="action_checkin" type="object" class="btn btn-primary w-100 fw-bold shadow-sm">
                                            <i class="fa fa-check-circle me-2"/>Check-in
                                        </button>
                                    </t>
                                    <t t-if="record.state.raw_value === 'done'">
                                        <div class="d-flex gap-2">
                                            <button name="action_checkout" type="object" class="btn btn-success flex-grow-1 fw-bold shadow-sm">
                                                <i class="fa fa-sign-out-alt me-2"/>Check-out
                                            </button>
                                            <button name="action_add_service" type="object" class="btn btn-info fw-bold shadow-sm">
                                                <i class="fa fa-plus-circle me-2"/>Add Service
                                            </button>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Modern UI Dashboard Action -->
    <record id="action_hotel_checkin_dashboard" model="ir.actions.act_window">
        <field name="name">Beach Vista Resort</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hotel.reservation</field>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="view_hotel_reservation_kanban"/>
        <field name="domain">['|', ('state', '=', 'confirm'), ('state', '=', 'done')]</field>
        <field name="context">{'search_default_state': 'confirm'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No active reservations found
            </p>
            <p>
                Waiting for new reservations to check in or check out.
            </p>
        </field>
    </record>

    <!-- Search View for Reservations -->
    <record id="view_hotel_reservation_search" model="ir.ui.view">
        <field name="name">hotel.reservation.search</field>
        <field name="model">hotel.reservation</field>
        <field name="arch" type="xml">
            <search>
                <field name="reservation_no"/>
                <field name="partner_id"/>
                <filter string="Ready for Check-in" name="state_confirm" domain="[('state', '=', 'confirm')]"/>
                <filter string="Currently Checked In" name="state_done" domain="[('state', '=', 'done')]"/>
                <filter string="Late Checkout" name="late_checkout" domain="[('state', '=', 'done'), ('is_late_checkout', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Guest" name="partner_id" context="{'group_by': 'partner_id'}"/>
                    <filter string="Status" name="state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Modern UI Dashboard Menu -->
    <menuitem name="Resort Dashboard" id="menu_hotel_checkin_dashboard"
              action="action_hotel_checkin_dashboard" parent="hotel_checkin_main_menu"
              sequence="0" groups="hotel_checkin.group_hotel_checkin_user,base.group_system"/>
</odoo> 