# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * helpdesk_timesheet
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Argentina) (https://www.transifex.com/odoo/teams/41243/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project_ticket_count
msgid "# Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_is_task_active
msgid "Active"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:73
#, python-format
msgid ""
"All timesheet hours will be assigned to the selected task on save. Discard "
"to avoid the change."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:19
#: code:addons/helpdesk_timesheet/models/helpdesk.py:34
#, python-format
msgid "Closed"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Customer's task is closed."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description"
msgstr "Descripción"

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description of the ticket..."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line_helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:18
#: code:addons/helpdesk_timesheet/models/helpdesk.py:33
#, python-format
msgid "In Progress"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_is_closed
msgid "Is Closed"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_task_type_is_closed
msgid "Is a close stage"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team_project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_project_id
msgid "Project"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
msgid "Project Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_task_id
msgid "Task"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_task_type
msgid "Task Stage"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_is_closed
#: model:ir.model.fields,help:helpdesk_timesheet.field_project_task_type_is_closed
msgid "Tasks in this stage are considered as closed."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_task_id
msgid "The task must have the same customer as this ticket."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_use_helpdesk_timesheet
msgid "This required to have project module installed."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
msgid "Ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project_ticket_ids
#: model:ir.ui.view,arch_db:helpdesk_timesheet.project_project_view_form_inherit_helpdesk_timesheet
msgid "Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:75
#, python-format
msgid ""
"Timesheet hours will not be assigned to a customer task. Set a task to "
"charge a customer."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_timesheet_ids
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Total hours"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "View task."
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:78
#, python-format
msgid "Warning"
msgstr ""
