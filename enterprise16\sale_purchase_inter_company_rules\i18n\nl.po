# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_inter_company_rules
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-07 10:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_generated
msgid "Auto Generated Purchase Order"
msgstr "Automatisch gegenereerd inkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_generated
msgid "Auto Generated Sales Order"
msgstr "Automatisch gegenereerd inkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__auto_validation
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__auto_validation
msgid "Automatic Validation"
msgstr "Automatische validatie"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr ""
"Automatisch gegenereerd op basis van %(origin)s van bedrijf %(company)s."

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""
"Configureer een correct magazijn voor het bedrijf (%s) vanuit het menu: "
"Instellingen/Gebruikers/Bedrijven"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__warehouse_id
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""
"Standaardwaarde om in te stellen op inkooporders (verkooporders) die worden "
"aangemaakt op basis van de verkooporders (inkooporders) gemaakt naar dit "
"bedrijf"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                using "
"warehouse %(warehouse)s when a company confirms a %(event_type)s for "
"%(company)s."
msgstr ""
"Genereer een %(validation)s %(generated_object)s voor het magazijn "
"%(warehouse)s wanneer een bedrijf een %(event_type)s voor %(company)s "
"bevestigt."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                when a company"
" confirms a %(event_type)s for %(company)s."
msgstr ""
"Genereer een %(validation)s %(generated_object)s wanneer een bedrijf een "
"%(event_type)s bevestigt voor %(company)s."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"Inter company user of company %(name)s doesn't have enough access rights"
msgstr ""
"Intercompany-gebruiker van bedrijf %(name)s heeft niet voldoende "
"toegangsrechten"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""
"Inter-bedrijf gebruiker of bedrijf %s heeft niet voldoende toegangsrechten"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid "Provide at least one user for inter company relation for %(name)s"
msgstr ""
"Geef ten minste één gebruiker op voor een intercompany-relatie voor: "
"%(name)s"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Provide one user for intercompany relation for %(name)s "
msgstr "Voorzie een gebruiker voor een inter-bedrijf relatie voor %(name)s"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Inkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Rule"
msgstr "Regel"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""
"Selecteer de soort om op te zetten voor inter-bedrijf regels in het "
"geselecteerde bedrijf."

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_purchase_order_id
msgid "Source Purchase Order"
msgstr "Bron inkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_sale_order_id
msgid "Source Sales Order"
msgstr "Bron verkooporder"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__purchase
msgid "Synchronize Purchase Order"
msgstr "Synchroniseer inkooporders"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale
msgid "Synchronize Sales Order"
msgstr "Synchroniseer verkooporders"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale_purchase
msgid "Synchronize Sales and Purchase Order"
msgstr "Synchroniseer verkoop en inkooporder"

#. module: sale_purchase_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr "Gebruik magazijn"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__warehouse_id
msgid "Warehouse"
msgstr "Magazijn"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "Magazijn voor inkooporders"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different than purchase price list currency.\n"
"The currency of the SO is obtained from the pricelist of the company partner.\n"
"\n"
"(SO currency: %(so_currency)s, Pricelist: %(pricelist)s, Partner: %(partner)s (ID: %(id)s))"
msgstr ""
"Je kunt de verkooporder niet maken vanuit een inkooporder omdat de valuta van de verkoopprijslijst anders is dan de valuta van de inkoopprijslijst.\n"
"De valuta van de verkooporder wordt verkregen uit de prijslijst van de relatie gekoppeld aan het bedrijf.\n"
"\n"
"(Verkoopordervaluta: %(so_currency)s, Prijslijst: %(pricelist)s, Relatie: %(relatie)s (ID: %(id)s))"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "draft"
msgstr "concept"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase order"
msgstr "inkooporder"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase/sales order"
msgstr "inkoop/verkooporder"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales order"
msgstr "verkooporder"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales/purchase order"
msgstr "verkoop/inkooporder"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "validated"
msgstr "bevestigd"
