# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth_sale
# 
# Translators:
# <PERSON><PERSON><PERSON> <piotr.w.c<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Martin <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2022
# <PERSON><PERSON><PERSON> <tadeusz.karp<PERSON><EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <tadeus<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:52+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_booth_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Produkt rejestrowany to produkt, którego stanem magazynowym zarządzasz. Wymaga zainstalowania aplikacji Magazynowanie.\n"
"Produkt pomocniczy to produkt, którego stan magazynowy nie jest zarządzany.\n"
"Usługa jest dostarczanym przez Ciebie niefizycznym produktem."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_ids
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__event_booth_id
msgid "Booth"
msgstr "Stoisko"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_id
msgid "Booth Category"
msgstr "Kategoria stoiska"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr ""
"Kategoria stoiska, dla której stoiska są nadal dostępne. Używane we frontend"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_count
msgid "Booth Count"
msgstr "Liczba stoisk"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_category_view_form
msgid "Booth Details"
msgstr "Szczegóły stoiska"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_tree
msgid "Booth Registration"
msgstr "Rejestracja stoiska"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_ids
#: model_terms:ir.ui.view,arch_db:event_booth_sale.sale_order_view_form
msgid "Booths"
msgstr "Stoiska"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_category_id
msgid "Booths Category"
msgstr "Kategoria stoisk"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_ids
msgid "Confirmed Booths"
msgstr "Potwierdzone stoiska"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_registration_ids
msgid "Confirmed Registration"
msgstr "Potwierdzona rejestracja"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Contact"
msgstr "Kontakt"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_email
msgid "Contact Email"
msgstr "E-mail kontaktowy"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_mobile
msgid "Contact Mobile"
msgstr "Kontakt z telefonem komórkowym"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_name
msgid "Contact Name"
msgstr "Nazwa kontaktu"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_phone
msgid "Contact Phone"
msgstr "Telefon kontaktowy"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__currency_id
msgid "Currency"
msgstr "Waluta"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__partner_id
msgid "Customer"
msgstr "Klient"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Details"
msgstr "Szczegóły"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__display_name
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_id
msgid "Event"
msgstr "Wydarzenie"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth
#: model:ir.model.fields.selection,name:event_booth_sale.selection__product_template__detailed_type__event_booth
msgid "Event Booth"
msgstr "Stoisko wydarzenia"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_category
msgid "Event Booth Category"
msgstr "Kategoria stoiska wydarzenia"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "Dostępna kategoria stoisk eventowych"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_configurator
msgid "Event Booth Configurator"
msgstr "Konfigurator stoisk na wydarzenia"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_registration
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__event_booth_registration_ids
msgid "Event Booth Registration"
msgstr "Rejestracja stoiska na imprezę"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_type_booth
msgid "Event Booth Template"
msgstr "Szablon stoiska eventowego"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_id
msgid "Final Sale Order Line"
msgstr "Linia ostatecznego zamówienia sprzedaży"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__id
msgid "ID"
msgstr "ID"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__image_1920
msgid "Image"
msgstr "Obraz"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__is_event_booth
msgid "Is Event Booth"
msgstr "Czy stoisko wydarzenia"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__is_paid
msgid "Is Paid"
msgstr "Jest płatny"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_account_move
msgid "Journal Entry"
msgstr "Zapis dziennika"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator____last_update
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Ok"
msgstr "Ok"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_id
msgid "Order Reference"
msgstr "Numer"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Paid"
msgstr "Zapłacona"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Pending Booths"
msgstr "Oczekujące stoiska"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__price
msgid "Price"
msgstr "Cena"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce
msgid "Price Reduce"
msgstr "zredukowano cenę"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Price Reduce Tax inc"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_template
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_template_option__product_id
msgid "Product"
msgstr "Produkt"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_booth_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Typ produktu"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_product
msgid "Product Variant"
msgstr "Wariant produktu"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Pozycja szablonu oferty"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Opcja szablonu oferty"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Registrations"
msgstr "Rejestracje"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid "Registrations from the same Order Line must belong to a single event."
msgstr ""
"Rejestracje z tej samej linii zamówień muszą należeć do jednego wydarzenia."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_registration_ids
msgid "SO Lines with reservations"
msgstr "SO Lines z zastrzeżeniami"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Sale Order"
msgstr "Zamówienie sprzedaży"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__sale_order_line_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__sale_order_line_id
msgid "Sale Order Line"
msgstr "Pozycja zamówienia sprzedaży"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order
msgid "Sales Order"
msgstr "Zamówienie sprzedaży"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pozycja zamówienia sprzedaży"

#. module: event_booth_sale
#: model:ir.actions.act_window,name:event_booth_sale.event_booth_configurator_action
msgid "Select an event booth"
msgstr "Wybór stoiska na wydarzenie"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid ""
"The following booths are unavailable, please remove them to continue : "
"%(booth_names)s"
msgstr ""
"Następujące stoiska są niedostępne, usuń je, aby kontynuować:  "
"%(booth_names)s"

#. module: event_booth_sale
#: model:ir.model.constraint,message:event_booth_sale.constraint_event_booth_registration_unique_registration
msgid "There can be only one registration for a booth by sale order line"
msgstr ""
"Może być tylko jedna rejestracja na stoisko według linii zamówienia "
"sprzedaży"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_tree_from_event
msgid "Total"
msgstr "Suma"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Used to create registration when providing the desired event booth."
msgstr ""
"Służy do tworzenia rejestracji podczas udostępniania żądanego stoiska "
"wydarzenia."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth.py:0
#, python-format
msgid ""
"You can't delete the following booths as they are linked to sales orders: "
"%(booths)s"
msgstr ""
"Nie można usunąć następujących stoisk, ponieważ są one powiązane z "
"zamówieniami sprzedaży: %(booths)s"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/wizard/event_booth_configurator.py:0
#, python-format
msgid "You have to select at least one booth."
msgstr "Należy wybrać co najmniej jedno stoisko."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth_registration.py:0
#, python-format
msgid ""
"Your order has been cancelled because the following booths have been "
"reserved"
msgstr ""
"Twoje zamówienie zostało anulowane, ponieważ zarezerwowano następujące "
"stoiska"
