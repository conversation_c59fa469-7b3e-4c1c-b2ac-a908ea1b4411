<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
    
    
    	<record id="hotel_laundry_picking_form" model="ir.ui.view">
            <field name="name">Return lines</field>
            <field name="model">hotel.laundry.picking</field>
            <field name="arch" type="xml">
                <form string="Return lines"  version="7.0">
                    <sheet>
	                    <label for="product_return_moves" string="Provide the quantities of the returned products." colspan="4"/>
	                    <separator string="" colspan="4"/>
	                    <field name="product_return_moves"  nolabel="1" colspan="6"/>
	                    <field name="invoice_state" />
	                    <footer>
	                        <button name="do_method" string="_Cancel" type="object" class="oe_highlight"/>
	                        <button name="create_returns" string="Return" colspan="1" type="object" class="oe_highlight" />
	                    </footer>
                    </sheet>
                </form>
            </field>
        </record>
         
         
        <record id="hotel_laundry_picking_action_form" model="ir.actions.act_window">
            <field name="name">Return Picking</field>
	   		<field name="type">ir.actions.act_window</field>
            <field name="res_model">hotel.laundry.picking</field>
            <field name="view_mode">form</field>
	    	<field name="target">new</field>
        </record>  

        
        
        <record id="hotel_laundry_picking_memory_form" model="ir.ui.view">
            <field name="name">hotel.laundry.picking.memory.from</field>
            <field name="model">hotel.laundry.picking.memory</field>
            <field name="arch" type="xml">
                <form string="Laundry Service">
                    <field name="product_id" />
                    <field name="quantity" />
                 </form>
            </field>
        </record>   
        
        <record id="hotel_laundry_picking_memory_tree" model="ir.ui.view">
            <field name="name">hotel.laundry.picking.memory.tree</field>
            <field name="model">hotel.laundry.picking.memory</field>
            <field name="arch" type="xml">
                <tree editable="bottom" string="Product Moves">
                    <field name="product_id" />
                    <field name="quantity" />
                </tree>
            </field>
        </record>
        
    </data>
</odoo>
