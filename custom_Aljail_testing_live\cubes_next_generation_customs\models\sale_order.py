# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.fields import Command
from odoo.exceptions import UserError, ValidationError


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    global_discount = fields.Float(string='تخفيض كلي')
    partner_balance = fields.Float(string="",  required=False, )

    @api.onchange('partner_id')
    def _onchange_partner_id_balance(self):
        for rec in self:
            rec.partner_balance=rec.partner_id.balance


    @api.onchange('global_discount')
    def make_global_discount(self):
        for elem in self:
            for line in elem.order_line.filtered(lambda l: not l.product_id.is_discount_product):
                line.discount = elem.global_discount

    def _prepare_invoice(self):
        """
        Prepare the dict of values to create the new invoice for a sales order. This method may be
        overridden to implement custom invoice generation (making sure to call super() to establish
        a clean extension chain).
        """
        self.ensure_one()

        return {
            'ref': self.client_order_ref or '',
            'move_type': 'out_invoice',
            'source_so_id': self.id,
            'narration': self.note,
            'currency_id': self.currency_id.id,
            'campaign_id': self.campaign_id.id,
            'medium_id': self.medium_id.id,
            'source_id': self.source_id.id,
            'team_id': self.team_id.id,
            'partner_id': self.partner_invoice_id.id,
            'partner_shipping_id': self.partner_shipping_id.id,
            'fiscal_position_id': (self.fiscal_position_id or self.fiscal_position_id._get_fiscal_position(
                self.partner_invoice_id)).id,
            'invoice_origin': self.name,
            'invoice_payment_term_id': self.payment_term_id.id,
            'invoice_user_id': self.user_id.id,
            'payment_reference': self.reference,
            'transaction_ids': [Command.set(self.transaction_ids.ids)],
            'company_id': self.company_id.id,
            'invoice_line_ids': [],
        }

    @api.onchange('order_line')
    def _onchange_order_line_warn(self):
        for rec in self:
            if rec.order_line.filtered(
                    lambda l: l.price_unit < l.product_template_id.standard_price and not l.is_warn) :
                rec.order_line.filtered(
                    lambda l: l.price_unit < l.product_template_id.standard_price and not l.is_warn).is_warn=True

                return {'warning': {
                    'title': _('Price Warning'),
                    'message': _("Please Check Price of " + ', '.join(line.product_template_id.name or '' for line in
                                                                      rec.order_line.filtered(lambda
                                                                                                  l: l.price_unit < l.product_template_id.standard_price)))

                }}


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    price_unit_after_discount = fields.Float(compute="get_price_unit_after_discount", string='سعر الوحدة بعد الخصم')

    original_total_amount = fields.Float(compute="get_original_total_amount", string='الاجمالي قبل الخصم')
    barcode = fields.Char(string="barcode", related="product_template_id.barcode")
    qty_available = fields.Float(string="",related='product_template_id.qty_available',store=True )
    is_warn = fields.Boolean(string="",  )

    @api.depends('price_unit', 'product_uom_qty')
    def get_original_total_amount(self):
        for elem in self:
            elem.original_total_amount = 0
            if elem.price_unit and elem.product_uom_qty:
                elem.original_total_amount = elem.price_unit * elem.product_uom_qty

    @api.depends('price_unit', 'discount')
    def get_price_unit_after_discount(self):
        for elem in self:
            elem.price_unit_after_discount = 0
            if elem.price_unit:
                elem.price_unit_after_discount = elem.price_unit * (1 - (elem.discount / 100))




class SaleReport(models.Model):
    _inherit = "sale.report"

    qty_available = fields.Float("Qty Available", copy=False, readonly=True)

    def _select_sale(self):
        res = super(SaleReport, self)._select_sale()
        select_str = res + """,CASE WHEN l.product_id IS NOT NULL THEN SUM(l.qty_available / u.factor * u2.factor) ELSE 0 END AS qty_available
"""
        return select_str

