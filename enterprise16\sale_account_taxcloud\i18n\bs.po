# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_account_taxcloud
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:34
#, python-format
msgid "The configuration of TaxCloud is in the Accounting app, Settings menu."
msgstr ""

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:34
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: sale_account_taxcloud
#: model:ir.actions.server,name:sale_account_taxcloud.action_sale_order_update_taxes
msgid "Update taxes with Taxcloud"
msgstr ""
