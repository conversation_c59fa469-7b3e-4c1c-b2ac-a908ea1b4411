# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Сергей <PERSON>е<PERSON>анин <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.disallowed_expenses_main_template
msgid ""
"<span>There are multiple disallowed expenses rates in this period</span>"
msgstr "<span>В этом периоде несколько запрещенных ставок расходов.</span>"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_account
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__account_ids
msgid "Account"
msgstr "Счёт"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Active"
msgstr "Активно"

#. module: account_disallowed_expenses
#: model_terms:ir.actions.act_window,help:account_disallowed_expenses.action_account_disallowed_expenses_category_list
msgid "Add a Disallowed Expenses Category"
msgstr "Добавить категорию запрещенных расходов"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__category_id
msgid "Category"
msgstr "Категория"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Category Name"
msgstr "Название категории"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__code
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Code"
msgstr "Код"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__company_id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "Компания"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "Создал"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__current_rate
msgid "Current Rate"
msgstr "Текущий курс"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__rate
msgid "Disallowed %"
msgstr "Запрещенный %"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_disallowed_amount
msgid "Disallowed Amount"
msgstr "Запрещенная сумма"

#. module: account_disallowed_expenses
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_report_de
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_form
msgid "Disallowed Expenses"
msgstr "Запрещенные расходы"

#. module: account_disallowed_expenses
#: model:ir.actions.act_window,name:account_disallowed_expenses.action_account_disallowed_expenses_category_list
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_disallowed_expenses_category_list
msgid "Disallowed Expenses Categories"
msgstr "Категории запрещенных расходов"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_category
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_account__disallowed_expenses_category_id
msgid "Disallowed Expenses Category"
msgstr "Категория запрещенных расходов"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_report_handler
msgid "Disallowed Expenses Custom Handler"
msgstr "Пользовательский обработчик запрещенных расходов"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_rate
msgid "Disallowed Expenses Rate"
msgstr "Ставка запрещенных расходов"

#. module: account_disallowed_expenses
#: model:account.report,name:account_disallowed_expenses.disallowed_expenses_report
#: model:ir.actions.client,name:account_disallowed_expenses.action_account_report_de
msgid "Disallowed Expenses Report"
msgstr "Отчет по запрещенным расходам"

#. module: account_disallowed_expenses
#: model:ir.model.constraint,message:account_disallowed_expenses.constraint_account_disallowed_expenses_category_unique_code_in_country
msgid "Disallowed expenses category code should be unique in each company."
msgstr ""
"Код категории запрещенных расходов должен быть уникальным в каждой компании."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__display_name
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "General Ledger"
msgstr "Главная книга"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__id
msgid "ID"
msgstr "Идентификатор"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Journal Items"
msgstr "Элементы журнала"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category____last_update
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__name
msgid "Name"
msgstr "Имя"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#, python-format
msgid "No Rate"
msgstr "Без ставки"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_rate
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__rate_ids
msgid "Rate"
msgstr "Ставка"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Rates"
msgstr "Ставки"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Related Account(s)"
msgstr "Связанный счет (счета)"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Set Rates"
msgstr "Установить тарифы"

#. module: account_disallowed_expenses
#: model:ir.model.fields,help:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Set active to false to hide the category without removing it."
msgstr ""
"Установите значение active в false, чтобы скрыть категорию, не удаляя ее."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "Дата начала"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Total"
msgstr "Всего"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_total_amount
msgid "Total Amount"
msgstr "Итоговая сумма"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. 1201"
msgstr "например, 1201"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. Non-Deductible Tax"
msgstr "например, налог, не подлежащий вычету"
