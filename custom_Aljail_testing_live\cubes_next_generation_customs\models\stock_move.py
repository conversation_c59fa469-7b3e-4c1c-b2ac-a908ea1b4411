from odoo import models, fields, api, exceptions
from datetime import datetime


class StockMove(models.Model):
    _inherit = 'stock.move.line'

    notes = fields.Char(string="", required=False, )
    qty_on_hand = fields.Float('Qty On Hand', compute='_compute_func_qty', store=True)
    sales_price = fields.Float(
        string='Sales price',
        required=False,
        related='product_id.list_price')

    @api.depends('product_id', 'location_id')
    def _compute_func_qty(self):
        for rec in self:
            if rec.product_id and rec.location_id:
                rec.qty_on_hand = rec.product_id.with_context(
                    location=rec.location_id.id).qty_available
            else:
                rec.qty_on_hand = 0.0


class StockMove(models.Model):
    _inherit = 'stock.move'

    qty_on_hand = fields.Float('Qty On Hand', compute='_compute_func_qty', store=True)
    notes = fields.Char(string="", required=False, )

    @api.depends('product_id', 'location_id')
    def _compute_func_qty(self):
        for rec in self:
            if rec.product_id and rec.location_id:
                rec.qty_on_hand = rec.product_id.with_context(
                    location=rec.location_id.id).qty_available
            else:
                rec.qty_on_hand = 0.0
