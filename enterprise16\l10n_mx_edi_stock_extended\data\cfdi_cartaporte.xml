<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cfdi_cartaporte_comex" inherit_id="l10n_mx_edi_stock.cfdi_cartaporte">

        <xpath expr="//*[@t-att-Calle='supplier.street']" position="attributes">
            <attribute name="t-att-Colonia">supplier.l10n_mx_edi_colony_code</attribute>
            <attribute name="t-att-Municipio">supplier.partner_id.city_id.l10n_mx_edi_code</attribute>
        </xpath>

        <xpath expr="//*[@t-att-Calle='record.partner_id.street']" position="attributes">
            <attribute name="t-att-Colonia">record.partner_id.l10n_mx_edi_colony_code</attribute>
            <attribute name="t-att-Municipio">record.partner_id.city_id.l10n_mx_edi_code</attribute>
        </xpath>

        <xpath expr="//*[name()='cartaporte20:Mercancia']" position="attributes">
            <attribute name="t-att-FraccionArancelaria">move.product_id.l10n_mx_edi_tariff_fraction_id.code if record.l10n_mx_edi_is_export else None</attribute>
        </xpath>

        <xpath expr="//*[name()='cfdi:Concepto']" position="inside">
            <cfdi:InformacionAduanera
                xmlns:cfdi="http://www.sat.gob.mx/cfd/3"
                t-if="record.l10n_mx_edi_is_export and record.l10n_mx_edi_customs_no"
                t-att-NumeroPedimento="record.l10n_mx_edi_customs_no" />
        </xpath>
    </template>
</odoo>
