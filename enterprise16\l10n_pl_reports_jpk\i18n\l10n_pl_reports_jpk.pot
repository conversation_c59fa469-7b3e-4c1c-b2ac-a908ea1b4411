# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_pl_reports_jpk
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-04 14:58+0000\n"
"PO-Revision-Date: 2023-08-04 14:58+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__540
msgid "15 days"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_quarterly_template
msgid "16"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__58
msgid "180 days"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_monthly_template
msgid "22"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__55
msgid "25 days on VAT account"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__56
msgid "25 days on settlement account"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__560
msgid "40 days"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields.selection,name:l10n_pl_reports_jpk.selection__l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe__57
msgid "60 days"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_future_tax_amount
msgid "Amount to be credited towards future tax obligations"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_amount
msgid "Amount to be reimbursed by the government"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,help:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_birthdate
msgid "As a natural person, the birthdate is needed"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid ""
"As your company is an individual, please put your name and surname separated"
" by a space in the company's contact.\n"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_birthdate
msgid "Birthdate"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.view_account_financial_report_export
msgid "Cancel"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.view_account_financial_report_export
msgid "Choose option(s) before exporting XML"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "Configure your company"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__create_date
msgid "Created on"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_future_tax
msgid "Credit the tax repayment amount towards future tax obligations"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.view_account_financial_report_export
msgid "Export Options"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.view_account_financial_report_export
msgid "Export XML"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__id
msgid "ID"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_is_amendment
msgid "Is an amendment"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "JPK export can only be done for monthly periods."
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_monthly_template
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_quarterly_template
msgid "JPK_VAT"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_monthly_template
msgid "Odoo"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__partner_is_company
msgid "Partner Is Company"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "Please configure the email in the company's contact.\n"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "Please configure the tax office in the Accounting Settings."
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "Please configure the vat number in the company's contact.\n"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model,name:l10n_pl_reports_jpk.model_l10n_pl_reports_jpk_periodic_vat_xml_export
msgid "Polish Periodic VAT Report Export Wizard"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model,name:l10n_pl_reports_jpk.model_l10n_pl_tax_report_handler
msgid "Polish Tax Report Custom Handler"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_reason_amendment
msgid "Reasons for the amendment"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_timeframe
msgid "Repayment Timeframe"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid "Some information is needed to generate the file:\n"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_paid_before_deadline
msgid "Tax liability has been paid in full before deadline"
msgstr ""

#. module: l10n_pl_reports_jpk
#. odoo-python
#: code:addons/l10n_pl_reports_jpk/models/tax_report.py:0
#, python-format
msgid ""
"The company's tax periodicity needs to be quarterly (for JPK_v7k) or monthly"
" (for JPK_v7m).\n"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model:ir.model.fields,field_description:l10n_pl_reports_jpk.field_l10n_pl_reports_jpk_periodic_vat_xml_export__l10n_pl_repayment_future_tax_type
msgid "Type of future tax obligations to be credited"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_monthly_template
msgid "VAT-7"
msgstr ""

#. module: l10n_pl_reports_jpk
#: model_terms:ir.ui.view,arch_db:l10n_pl_reports_jpk.jpk_export_quarterly_template
msgid "VAT-7K"
msgstr ""
