
<templates xml:space="preserve">

    <t t-name="mail.EmojiSearchBarView" owl="1">
        <div class="o_EmojiSearchBarView overflow-auto d-flex flex-shrink-0 p-2 flex-column border-bottom" t-attf-class="{{ className }}" t-ref="root">
            <div class="o_EmojiSearchBarView_inputContainer d-flex flex-grow-1 align-items-center">
                <input class="o_EmojiSearchBarView_searchInput o_input form-control overflow-auto flex-fill" t-att-placeholder="emojiSearchBarView.placeholder" t-on-focusin="emojiSearchBarView.onFocusinInput" t-on-focusout="emojiSearchBarView.onFocusoutInput" t-on-input="emojiSearchBarView.onInput" t-ref="input"/>
                <i class="o_EmojiSearchBarView_icon oi oi-search ps-2" title="Search..." role="img" aria-label="Search..."/>
            </div>
        </div>
    </t>

</templates>
