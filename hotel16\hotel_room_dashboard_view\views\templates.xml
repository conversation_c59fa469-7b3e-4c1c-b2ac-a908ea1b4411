<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="room_dashboard" name="Room Dashboard View">&lt;!DOCTYPE html&gt;
        <html>
            <head>
                <title>Room Dashboard View</title>

                <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
                <meta http-equiv="content-type" content="text/html, charset=utf-8"/>

                <meta name="viewport" content=" width=1024, user-scalable=no"/>
                <meta name="apple-mobile-web-app-capable" content="yes"/>
                <meta name="mobile-web-app-capable" content="yes"/>

                <script type="text/javascript">
                    var odoo = {
                    csrf_token: "<t t-esc="request.csrf_token(None)"/>",
                    session_info:<t t-raw="session_info"/>,
                    };
                </script>

                <t t-call-assets="web.assets_common" t-css="true"/>
                <t t-call-assets="web.assets_backend" t-css="true"/>


                <script type="text/javascript" id="loading-script" t-raw="init">
                    odoo.define('web.web_client', function (require) {
                    var WebClient = require('web.AbstractWebClient');
                    var web_client = new WebClient();

                    web_client._title_changed = function() {};
                    web_client.show_application = function() {
                    return web_client.action_manager.do_action("room.dashboard.ui");
                    };

                    $(function () {
                    web_client.setElement($(document.body));
                    web_client.start();
                    });
                    return web_client;
                    });
                </script>
            </head>
            <body>
                <div class="o_main_content"/>
            </body>
        </html>
    </template>
</odoo>
