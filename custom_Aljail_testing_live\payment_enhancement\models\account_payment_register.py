from odoo import fields, models, api, _


class RegisterPayment(models.TransientModel):
    _inherit = 'account.payment.register'

    writeoff_account_id = fields.Many2one(
        comodel_name='account.account',
        string="Difference Account",
        copy=False,
        domain="[('deprecated', '=', False), ('company_id', '=', company_id), ('show_in_register_pay', '=', True)]",
        # compute='_compute_writeoff_account_id',
        store=True,
        readonly=False,
        default=lambda self: self.env['account.account'].search(
            [('show_in_register_pay', '=', True), ('name', '=', 'خصم مسموح به')])
    )
    #payment_date = fields.Date(string="Payment Date", required=True, default=False)
