<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_attendance_search_view_inherit" model="ir.ui.view">
            <field name="name">hr.attendance.search.view.inherit</field>
            <field name="model">hr.attendance</field>
            <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='department_id']" position="after">
                    <field name="registration_number"/>
                </xpath>
                <xpath expr="//filter[@name='employee']" position="after">
                    <filter string="Registration Number" name="registration" context="{'group_by': 'registration_number'}"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
