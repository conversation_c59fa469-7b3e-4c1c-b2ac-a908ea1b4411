# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr "<b>LinkedIn Post</b>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr "投稿の実態的なLinkedIn ID"

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages data: %r."
msgstr "ページデータの取得中にエラーが発生しました：%r."

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "An error occurred when fetching your pages: %r."
msgstr "ページの取得中にエラーが発生しました：%r."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr "App ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr "App Secret"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "作者イメージ"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr "提供されたアカウントではなく、個人の LinkedIn 開発者アカウントを使用する場合は、このチェックボックスをオンにします。"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr "LinkedInのプレビューを表示"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#, python-format
msgid "Likes"
msgstr "いいね！"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr "LinkedInのアカウントID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr "LinkedInのアカウントURN"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
#, python-format
msgid "LinkedIn Comments"
msgstr "LinkedInのコメント"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr "LinkedIn Likes"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr "LinkedInのプレビュー"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr "LinkedInのバニティ名"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "LinkedInのアクセストークン"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr "LinkedInの作成者のID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr "LinkedInの著者URN"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr "LinkedIn著者の画像URL"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "LinkedIn did not provide a valid access token."
msgstr "LinkedInは、有効なアクセストークンを提供していませんでした。"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr "LinkedInのポストURN"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr "Linkedin開発者アカウント"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr "メディアタイプ"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr "ポストイメージ"

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "投稿"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr "ソーシャルアカウント"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr "ソーシャル・ライブ・ポスト"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "ソーシャルメディア"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "ソーシャル・ポスト"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr "ソーシャルポストテンプレート"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "ソーシャルストリーム"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr "ソーシャルストリームポスト"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr "アクセストークンは、REST APIにリクエストを実行するために使用されます"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr "このサービスが要求したURLがエラーを返しました。アプリの作者に連絡してください。"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
#, python-format
msgid "There is no page linked to this account"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "There was a authentication issue during your request."
msgstr "要求時に認証上の問題がありました。"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "不正な。管理者に連絡してください。"

#. module: social_linkedin
#: code:addons/social_linkedin/models/social_stream_post.py:0
#, python-format
msgid "Unknown"
msgstr "不明"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr "あなた自身のLinkedInのアカウントを使用"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"いくつかの機能を特定のメディア('Facebook','Twitter', ...)に制限する必要がある場合の比較を行うために使用されます。"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr "バニティ名は、著者へのリンクを生成するために使用されます"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr "画像をアップロードできませんでした。サイズを小さくして再度投稿してみて下さい。(エラー: アップロード登録中に失敗しました)"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr "画像をアップロードできませんでした。サイズを小さくして再度投稿してみて下さい。"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#, python-format
msgid "Wrong stream type for \"%s\""
msgstr "%s'に利用するには誤ったストリーム型"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "アクティブなサブスクリプションを持っていません。こちらで購入してください:%s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
#, python-format
msgid "unknown"
msgstr "不明"
