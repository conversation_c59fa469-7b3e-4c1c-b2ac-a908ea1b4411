# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"L'ID des 'téléchargements' playlist fourni par l'API YouTube, cela ne doit "
"jamais être défini manuellement."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr "123 vues •"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Commentaires\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"J'aime\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr "<span class=\"fw-bold\">Votre chaîne YouTube</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""
"<span>Ceux-ci sont stockés jusqu'à 30 jours et souvent actualisés pour vous fournir une représentation précise de la réalité.</span>\n"
"<span>Pour les supprimer d'Odoo, supprimez simplement ce compte.</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr "L'accès à votre compte peut être révoqué à tout moment à partir de"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr ""
"Jeton d'accès fourni par l'API YouTube, il ne doit jamais être défini "
"manuellement."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr "Compte"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "An error occurred."
msgstr "Une erreur s'est produite."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr ""
"Point d'extrémité d'autorisation n'a pas actualisé le jeton. Veuillez "
"réessayer."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "Auteur de l'image"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "By using our Social YouTube Application, you implicitly agree to the:"
msgstr ""
"En utilisant notre application Social YouTube, vous acceptez implicitement :"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr "Chaîne"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Clear"
msgstr "Effacer"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""
"Les commentaires sont marqués comme 'désactivés' pour cette vidéo. Elle "
"aurait pu être défini comme 'privée'."

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Confirmation"
msgstr "Confirmation"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr "Contient l'ID de la vidéo tel qu'il est renvoyé par l'API YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr ""
"Contient l'ID de la catégorie vidéo tel qu'il est renvoyé par l'API YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""
"Impossible de révoquer votre compte.\n"
"Erreur : %s"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr "Créé le"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Do you also want to remove the video from your YouTube account?"
msgstr "Voulez-vous également supprimer la vidéo de votre compte YouTube ?"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Draft Video"
msgstr "Vidéo en brouillon"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""
"Date d'expiration du jeton d'accès fourni par l'API YouTube, cela ne doit "
"jamais être défini manuellement."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr "Jeton d'accès Google"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr "Politique de confidentialité Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr "Jeton d'actualisation Google"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr "ID"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Likes"
msgstr "J'aime"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr "Type de réseau"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "No"
msgstr "Non"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr "ID Client OAuth"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr "Secret Client OAuth"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""
"Odoo perdra l'accès à votre compte YouTube \n"
"et supprimera toutes ses données associées de votre base de données."

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr ""
"Une fois publiée, définissez la vidéo comme publique/privée/non répertoriée"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
#, python-format
msgid "Please select a single YouTube account at a time."
msgstr "Veuillez sélectionner un seul compte YouTube à la fois."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr "Privée"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Processing..."
msgstr "En cours de traitement....."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr "Publique"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Reason:"
msgstr "Motif :"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"Le jeton d'actualisation fourni par l'API YouTube ne doit jamais être défini"
" manuellement."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr "Révoquer"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr "Révoquer & Supprimer"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoke Account"
msgstr "Révoquer le compte"

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr "Révoquer le compte YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
#, python-format
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr ""
"La révocation des jetons d'accès est actuellement limitée aux seuls comptes "
"YouTube."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Select"
msgstr "Sélectionner"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr "Sélectionnez d'autres comptes"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr "Comptes YouTube sélectionnés"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""
"Contient simplement le nom de fichier de la vidéo car la vidéo elle-même est"
" téléchargée directement sur YouTube"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr "Compte social"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr "Post en direct"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr "Réseaux sociaux"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr "Post social"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr "Modèle de post social"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr "Flux social"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr "Post du flux social"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "Le champ 'message' est obligatoire pour l'identifiant du post %s"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr ""
"La vidéo sélectionnée dépasse la taille maximale autorisée qui est de %s."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"L'URL demandée par ce service a renvoyé une erreur. Veuillez contacter "
"l'auteur de l'application."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr "La vidéo que vous essayez de publier a été supprimée de YouTube."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "There is no channel linked with this YouTube account."
msgstr "Aucune chaîne n'est associée à ce compte YouTube."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""
"Pour fournir nos services d'application, notez que nous stockons les données"
" suivantes de votre compte YouTube :"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr "Date d'expiration du jeton"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "Non autorisé. Veuillez contacter votre administrateur."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
#, python-format
msgid "Unknown"
msgstr "Inconnu"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr "Non répertoriée"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#, python-format
msgid "Upload Video"
msgstr "Télécharger la vidéo"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Upload failed. Please try again."
msgstr "Le téléchargement a échoué. Veuillez réessayer."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading %s"
msgstr "En cours de chargement %s"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Uploading... %s%%"
msgstr "En cours de chargement... %s%%"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr "Utiliser votre propre compte YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"Utilisé pour faire des comparaisons lorsque nous devons restreindre "
"certaines fonctionnalités à un réseau spécifique ('facebook', 'twitter', "
"...)."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "Video"
msgstr "Vidéo"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr "Description de la vidéo"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr "Confidentialité vidéo"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr "Titre de la vidéo"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Video Upload"
msgstr "Téléchargement de la vidéo"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
#, python-format
msgid "Video not found. It could have been removed from Youtube."
msgstr "Vidéo non trouvée. Elle a pu être supprimée de Youtube."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
#, python-format
msgid "Views"
msgstr "Vues"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
#, python-format
msgid "Yes, delete it"
msgstr "Oui, supprimer"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Vous n'avez pas d'abonnement actif. Veuillez en acheter un ici : %s"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#, python-format
msgid "You have to upload a video when posting on YouTube."
msgstr "Vous devez télécharger une vidéo lors de la publication sur YouTube."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
msgid "YouTube"
msgstr "YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr "Jeton d'accès YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr "ID de la catégorie YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr "ID de la chaîne YouTube"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"ID de la chaîne YouTube fourni par l'API YouTube, cela ne doit jamais être "
"défini manuellement."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "YouTube Comments"
msgstr "Commentaires YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr "Nombre de commentaires YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr "Compte développeur YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr "Je n'aime pas YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr "J'aime YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_ratio
msgid "YouTube Likes Ratio"
msgstr "Ratio des j'aime YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr "ID Client OAuth YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr "Secret Client OAuth YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr "Options YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placehdoler"
msgstr "Placeholder YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr "Aperçu YouTube"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr "Conditions d'utilisation de YouTube (ToS)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr "Vignette YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr "URL de la vignette YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr "ID téléchargement playlist YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr "Vidéo Youtube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr "Description de la vidéo YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr "ID de la vidéo YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr "Id de la vidéo YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr "Titre de la vidéo YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr "URL de la vidéo YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr "Vues YouTube"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr ""
"YouTube n'a pas fourni de jeton d'accès valide ou celui-ci a peut-être "
"expiré."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
#, python-format
msgid "YouTube did not provide a valid authorization code."
msgstr "YouTube n'a pas fourni de code d'autorisation valide."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr "Le nom et l'image de votre chaîne"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#, python-format
msgid "Your video is missing a correct title or description."
msgstr "Il manque un titre ou une description correcte à votre vidéo."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr ""
"Les métadonnées de vos vidéos, y compris le titre et le nombre de vues (mais"
" jamais la vidéo elle-même)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""
"par ex. Engagez toute votre communauté avec une seule application ! "
"https://www.odoo.com/trial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr "par ex. Tuto social Odoo"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr "le panneau d'accès au compte de l'application tierce Google"
