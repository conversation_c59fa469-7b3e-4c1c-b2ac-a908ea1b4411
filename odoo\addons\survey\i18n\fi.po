# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <heikki.kata<PERSON><PERSON>@myyntivoima.fi>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Top<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# I<PERSON><PERSON>nen <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <svante.suomi<PERSON>@web-veistamo.fi>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# Martin Trigaux, 2022
# Konsta Aavaranta, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Jarmo Kortetjärvi <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "# Kysymykset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "# Kysymystä satunnaisesti poimittuna"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "100€"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "20 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "200€"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "300€"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "50€"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "80€"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% suoritettu"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopio)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s certification passed"
msgstr "%s sertifiointi suoritettu"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certification"
msgstr "%s haastesertifikaatti"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Sertifikaatti - %s' % (object.survey_id.display_name)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 vuotta"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 vuotta"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 vuotta"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Pääsy evätty"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 vuotta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Todistus</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Valmis</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Rekisteröity</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                <span class=\"text-muted\">Average Duration</span>"
msgstr ""
"<br/>\n"
"                                <span class=\"text-muted\">Keskimääräinen kesto</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<br/>by"
msgstr "<br/>tekijä"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"></strong>% !</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Sertifiointi: <t t-out=\"object.survey_id.display_name or ''\">Palautelomake</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Hyvä <span t-out=\"object.partner_id.name or 'participant'\">osallistuja</span></p>\n"
"                <p>\n"
"                    Ohessa on liitteenä\n"
"                       <strong t-out=\"object.survey_id.display_name or ''\">Huonekalujen valmistus</strong>\n"
"                    sertifiointi\n"
"                </p>\n"
"                <p>Onnittelut testin läpäisystä pistemäärällä <strong t-out=\"object.scoring_percentage\"></strong>% !</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br><br>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br><br>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hyvä <t t-out=\"object.partner_id.name or 'participant'\">osallistuja</t><br><br>\n"
"       <t t-if=\"object.survey_id.certification\"> \n"
"            Sinut on kutsuttu uuteen sertifiointiin.\n"
"       </t>\n"
"       <t t-else=\"\"> \n"
"            Teemme parhaillaan kyselyä, ja vastauksesi olisi tervetullut.\n"
"       </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Aloita sertifiointi\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Aloita kyselytutkimus\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"       <t t-if=\"object.deadline\"> \n"
"            Vastaa kyselyyn <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br><br>\n"
"       </t>\n"
"       <t t-if=\"object.survey_id.certification\"> \n"
"            Toivotamme sinulle onnea!\n"
"       </t>\n"
"       <t t-else=\"\"> \n"
"            Kiitos etukäteen osallistumisestanne.\n"
"       </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-bar-chart me-1\"/>Graph"
msgstr "<i class=\"fa fa-bar-chart me-1\"/>Kaavio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Tulokset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>vastaa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>vastaa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Ei valittu\" "
"title=\"Ei valittu\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Sulje"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>vastaa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Valittu\" "
"title=\"Valittu\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Edit Survey"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Muokkaa kyselyä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Lataa sertifikaatti\" title=\"Lataa sertifikaatti\"/>\n"
"                                        Lataa sertifikaatti"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "<i class=\"fa fa-list-alt me-1\"/>All Data"
msgstr "<i class=\"fa fa-list-alt me-1\"/>Kaikki tiedot"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-list-alt me-1\"/>Data"
msgstr "<i class=\"fa fa-list-alt me-1\"/>Tiedot"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "<i class=\"fa fa-list-ol me-1\"/>Most Common"
msgstr "<i class=\"fa fa-list-ol me-1\"/>Yleisin"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>vastaa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<i class=\"fa fa-trash me-1\"/>Remove all filters"
msgstr "<i class=\"fa fa-trash me-1\"/>Poista kaikki suodattimet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_scored_question', '!=', "
"True)]}\">Points</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_scored_question', '!=', "
"True)]}\">Pisteet</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"seconds</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"sekuntia</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">Average "
"</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Keskimääräinen </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">Maximum "
"</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Enimmäismäärä </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">Minimum "
"</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">Vähintään"
" </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span class=\"fa fa-filter me-1\"/>Filters"
msgstr "<span class=\"fa fa-filter me-1\"/>Suodattimet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press Enter</span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> tai paina Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">tai paina Enter</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                        <span id=\"enter-tooltip\">or press Enter</span>\n"
"                    </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                        <span id=\"enter-tooltip\">tai paina Enter</span>\n"
"                    </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Sertifioinnit</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Sertifiointi</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Sertifioinnit</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Sertiointi</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-"
"inline\">tai paina Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Avain</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Completed</span>"
msgstr "<span class=\"text-muted\">Valmistunut</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">Kysymykset</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Registered</span>"
msgstr "<span class=\"text-muted\">Rekisteröity</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr "<span class=\"text-muted\">Onnistuminen</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"Punainen\" ei ole kategoria, tiedän mitä yrität tehdä ;)</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>Kaikki tutkimukset</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>Paras aika tehdä se on oikea aika tehdä se.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span>Click on the filter icon (<span class=\"fa fa-filter text-primary\"/>)\n"
"                                    next to an answer to filter surveys on similar answers only.</span>"
msgstr ""
"<span>Napsauta suodatinkuvaketta (<span class=\"fa fa-filter text-primary\"/>)\n"
"                                    vastauksen vieressä, jos haluat suodattaa kyselyt vain samankaltaisten vastausten perusteella.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>Täytetyt kyselyt</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<span>Date</span>"
msgstr "<span>Päivä</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>Epäonnistui vain</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>Kuinka monta ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeerinen\" title=\"Numeerinen\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeerinen\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Jos et pidä meistä, yritä olla mahdollisimman objektiivinen.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>Nimeä kaikki eläimet</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Useita rivejä\" title=\"Useita rivejä\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>Nimeä yksi eläin</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Yksi rivi\" title=\"Yksi rivi\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>Yritysiä jäljellä</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader !</span>"
msgstr "<span>Kuuluisa johtajamme !</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it !</span>"
msgstr "<span>Myyjillämme on etu, mutta sinä pystyt siihen !</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>Hyväksytty ja hylätty</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>Vain läpäisty</span>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>Kokeile sitä</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Odotetaan osallistujia...</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>Milloin ... alkaa?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>Milloin on joulu?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>Mitkä ovat keltaisia?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>Kumpi on keltainen?</span><br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"Sitrus\" voi antaa sinulle ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr "Merkintä on liitettävä vain yhteen kysymykseen."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Pituuden tulee olla positiivinen!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Hieman ylihinnoiteltu"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Törkeän kallis"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Positiivinen pistemäärä osoittaa oikean valinnan; negatiivinen tai nolla "
"pistemäärä osoittaa väärän vastauksen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "On ilmennyt ongelma"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "A question can either be skipped or answered, not both."
msgstr "Kysymyksen voi joko ohittaa tai vastata, ei molempia."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Tietoja verkkokaupastamme"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Tietoja sinusta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Pääsytila"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Pääsytunniste"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Pääsytunnisteen on oltava yksilöllinen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Aktiivinen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "Toimenpiteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email recipients (separated by commas, semicolons or line "
"breaks). They will not be converted into contacts."
msgstr ""
"Lisää luettelo sähköpostin vastaanottajista (pilkuilla, puolipisteillä tai "
"rivinvaihdoilla erotettuna). Niitä ei muuteta yhteystiedoiksi."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "Lisää kysymys"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "Lisää väliotsikko/osio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Lisää olemassaolevia yhteystietoja..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr "Lisää hauskuutta esityksiin jakamalla kysymyksiä livenä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Lisäsähköpostit"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Ylläpitäjä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Afrikka"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year ?"
msgstr ""
"Kun olet katsonut tämän videon, lupaatko, että et aio viivytellä pensasaidan"
" leikkaamisessa tänä vuonna ?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Samaa mieltä"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Kaikki \"Onko pisteytetty kysymys = True\" ja \"Kysymyksen tyyppi: "
"Päivämäärä\" kysymykset tarvitsevat vastauksen"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Kaikki \"Onko pisteytetty kysymys = True\" ja \"Kysymyksen tyyppi: "
"Päivämäärä ja aika\" tarvitsevat vastauksen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Kaikki kysymykset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "Kaikki tutkimukset"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Pääsykoodin on oltava yksilöllinen!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"Muiden kuin monivalintakysymysten vastauspisteet eivät voi olla "
"negatiivisia!"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "Omena päivässä pitää lääkärin loitolla."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anonyymi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "Vastaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Vastauksen tyyppi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Vastausaika"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_id
msgid "Answer that will trigger the display of the current question."
msgstr "Vastaus, joka käynnistää nykyisen kysymyksen näyttämisen."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "Vastaukset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Vastausten määrä"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Kuka tahansa, jolla on linkki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Esiintyy"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Omenapuut"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Omenat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Taide ja kulttuuri"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Aasia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Liitteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Yritys nro"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "Yritykset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "Yritysten määrä"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr ""
"Osallistujan lempinimi, jota käytetään pääasiassa osallistujan "
"tunnistamiseen kyselyistunnon tulostaululla."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Osallistujat vastaavat kysymykseen..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Osallistujat saavat enemmän pisteitä, jos he vastaavat nopeasti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Tekijä"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr ""
"Käyttäjälle lähetetään automaattinen sähköpostiviesti, joka sisältää hänen "
"sertifiointiasiakirjansa, kun hän onnistuu sertifioinnissa."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Syksy"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Average"
msgstr "Keskinkertainen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
msgid "Average Duration"
msgstr "Keskimääräinen kesto"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Tutkimuksen keskimääräinen kesto (tunteina)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "Keskimääräinen pistemäärä (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Back Button"
msgstr "Takaisin-painike"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Taustakuva"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "Taustan URL"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Ansiomerkki"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Baobab-puut"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Mehiläiset"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years !"
msgstr "Varo karkausvuosia !"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Blue Pen"
msgstr "Sininen kynä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Tiilet"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "Bryssel"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "Bryssel, Belgia"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Purilaistietokilpailu"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "But first, keep listening to the host."
msgstr "Mutta kuuntele ensin isäntää."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Kaappi ovilla"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Kaktus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Voi muokata runkoa"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon ?"
msgstr "Voiko ihminen koskaan nähdä suoraan fotonin?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#, python-format
msgid "Certification"
msgstr "Sertifiointi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Sertifiointimerkki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Sertifiointimerkki "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "Sertifiointimerkkiä ei ole määritetty kyselyä varten %(survey_name)s"

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr "Sertifiointiasiakirja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification Failed"
msgstr "Sertifiointi epäonnistui"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Certification Title"
msgstr "Sertifioinnin otsikko"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification n°"
msgstr "Sertifikaatti nro"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Sertifiointimalli"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Sertifiointi: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Sertifioinnit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Sertifikaattien määrä"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Onnistuneet sertifikaatit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "Sertifioitu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "Sertifioitu sähköpostimalli"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Tuolin lattian suoja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Chart"
msgstr "Kaavio"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Cheating on your neighbors will not help!"
msgstr "Naapurien pettäminen ei auta!"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Merkitse tämä vaihtoehto, jos haluat rajoittaa yrityksien määrää käyttäjää "
"kohden"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "Kiina"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Valinnat"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready ?"
msgstr "Valitse lempiaiheesi ja näytä, miten hyvä olet. Valmiina?"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Klassinen sininen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Klassinen kulta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Klassinen violetti"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Klementiini"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "Sulje"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "Sulje live-istunto"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "Väri"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Väri-indeksi"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "Palaa takaisin, kun olet lisännyt kysymyksiä kyselyihisi."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Kommentti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Palauteviesti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "Kommentti on vastaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Yrityksen sertifikaatit lasketaan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Valmis"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "Täytetyt kyselyt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Luo sähköposti"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Computing score requires a question in arguments."
msgstr "Pisteiden laskeminen edellyttää kysymystä argumenteista."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_conditional
msgid "Conditional Display"
msgstr "Ehdollinen näyttö"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Konferenssin puheenjohtaja"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr ""
"Onnittelut! Olet nyt valmis keräämään palautetta kuin ammattilainen :-)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Onneksi olkoon, olet nyt MyCompanyn virallinen myyjä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Onnittelut, olet läpäissyt kokeen!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Rajoitukset"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "Kontakti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Sisältää ehdollisia kysymyksiä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Sisältö"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Jatka"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Jatka tästä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Evästeet"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#, python-format
msgid "Copied !"
msgstr "Kopioitu !"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Cornaceae"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Kulmapöytä, oikea"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Correct"
msgstr "Oikea"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Oikea vastaus"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Oikea päivämäärä ja kellonaika tähän kysymykseen."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Oikea päivämäärä vastaus"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Oikea vastaus tähän kysymykseen."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Oikea päivä- ja kellonaika -vastaus"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Oikea numerovastaus tähän kysymykseen."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Oikea numeerinen vastaus"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Oikein hinnoiteltu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Kosmiset säteet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "Luo live-istunto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Luotu"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr "Testitunnuksen luominen ei ole sallittua sinulle."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Tunnusten luominen muille kuin työntekijöille ei ole sallittua sisäisissä "
"kyselyissä."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed/archived surveys is not allowed."
msgstr ""
"Merkkien luominen suljettuja/arkistoituja kyselyitä varten ei ole sallittua."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Tunnisteen luominen ulkopuolisille henkilöille ei ole sallittua todennusta "
"pyytävissä kyselyissä."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Nykyinen kysymys"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Nykyisen kysymyksen aloitusaika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Nykyisen istunnon alkamisaika"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "Tällä hetkellä tuetaan vain live-istuntoja."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr ""
"Asiakkaat saavat uuden tunnisteen ja voivat suorittaa kyselyn kokonaan "
"uudelleen."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Asiakkaat saavat saman merkin."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Mukautettava lamppu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Päivämäärä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Päivämäärä vastaus"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Päivämäärä ja aika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Vastaus päivämääränä ja aikana"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Päivämäärä, johon mennessä asiakas voi avata kyselyn ja lähettää vastaukset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Määräaika"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Määrittele haasteen näkyvyys valikoissa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "Poista"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "Kuvaus"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Työpöytäyhdistelmä"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Yksityiskohtaiset vastaukset"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Eri mieltä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "Näytä edistyminen seuraavasti"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Myymmekö akustisia äänenpeittolevyjä?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr "Onko sinulla muita kommentteja, kysymyksiä tai huolenaiheita?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "Puuttuuko kuvastostamme mielestäsi tuotteita? (ei arvioitu)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Koirat"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees ?"
msgstr "Mihin puiden heimoon koiranheinä kuuluu ?"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "Älä ujostele, ole villi!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglaskuusi"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Laatikko"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Dropdown menu"
msgstr "Alasvetovalikko"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "Muokkaa kyselyä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Muokkaa hallinnassa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "Sähköposti"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "Lopeta live-istunto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "Lopeta viesti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Päättymispäivä ja -aika"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Syötä istuntokoodi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Virheilmoitus"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Eurooppa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Pihlaja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "Sulje pois testit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Nykyinen kumppani"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Olemassa olevat sähköpostit"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Islanti)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#, python-format
msgid "Failed"
msgstr "Epäonnistui"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "Epäonnistui vain"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#, python-format
msgid "Feedback Form"
msgstr "Palautelomake"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Fiikus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Filter surveys"
msgstr "Suodata tutkimukset"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#, python-format
msgid "Final Leaderboard"
msgstr "Lopullinen pistetaulukko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Vapaamuotoinen teksti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Vapaamuotoinen vastaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "Alkaa"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris) ?"
msgstr "Mistä maanosasta mänty (pinus sylvestris) on kotoisin?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Hedelmät"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Hedelmät ja vihannekset"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Pelillistämisen ansiomerkki"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Pelillistämishaaste"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "Kerää palautetta työntekijöiltäsi ja asiakkailtasi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Maantiede"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Anna merkki"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Luettelo kaikista myymistämme puulajeista."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Hyvä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Good luck!"
msgstr "Onnea!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Hyvä vastine rahalle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Greipit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Graph"
msgstr "Kaavio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Käsittele nykyisiä"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "Käsittele tietokilpailu &; todistukset"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Vaikea"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Korkeus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "Auta osallistujia tietämään, mitä kirjoittaa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Here, you can overview all the participations."
msgstr "Täällä voit tarkastella kaikkia osallistumisia."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Korkea laatu"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Historia"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr "Kuinka usein ostat tuotteita verkosta?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "How frequently do you use our products?"
msgstr "Kuinka usein käytät tuotteitamme?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "How good of a presenter are you? Let's find out!"
msgstr "Kuinka hyvä juontaja olet? Otetaan selvää!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "How likely are you to recommend the following products to a friend?"
msgstr "Kuinka todennäköisesti suosittelisit seuraavia tuotteita ystävällesi?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Kuinka pitkä Valkoinen Niili on?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Kuinka monta tuolia meidän pitäisi mielestänne pyrkiä myymään vuodessa (ei "
"luokiteltu)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Kuinka monta päivää on rahat takaisin -takuu?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "How many orders did you pass during the last 6 months?"
msgstr "Kuinka monta tilausta annoitte viimeisten 6 kuukauden aikana?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr "Kuinka monta kertaa tilasit tuotteita verkkosivuiltamme ?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Kuinka monta versiota Corner Deskistä meillä on?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last ?"
msgstr "Kuinka monta vuotta 100-vuotinen sota kesti?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Kuinka paljon myymme kaapelinhallintalaatikkoamme?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Kuinka usein kasveja pitäisi kastella"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you ?"
msgstr "Kuinka vanha olet?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"En oikeastaan pidä ajattelusta. Luulen, että ihmiset luulevat, että pidän "
"paljon ajattelusta. Enkä ajattele. En pidä ajattelusta lainkaan."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Ilma kiehtoo minua. Jos taivaalta poistettaisiin ilma, kaikki linnut "
"putoaisivat maahan. Ja kaikki lentokoneet myös."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Olen lisännyt tuotteita toivomuslistalleni"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Minulla ei ole aavistustakaan, olen koira!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young !"
msgstr "Olen huomannut painovoiman jo pienestä pitäen !"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoni joka kertoo poikkeustoiminnosta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Tunnusmerkki"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Jos numero on valittuna, se näyttää vastattujen kysymysten määrän suhteessa "
"vastattavien kysymysten kokonaismäärään."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Jos asiakas ostaa 1 vuoden takuun 6. tammikuuta 2020, milloin takuu päättyy?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Jos asiakas ostaa tuotteen 6. tammikuuta 2020, mikä on myöhäisin päivä, "
"jolloin odotamme sen toimitettavan?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr "Jos valittu, tallentaa käyttäjän vastauksen sähköpostiosoitteeksi."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr "Jos valittu, käyttäjän vastaus tallennetaan käyttäjän lempinimeksi."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_conditional
msgid ""
"If checked, this question will be displayed only\n"
"        if the specified conditional answer have been selected in a previous question"
msgstr ""
"Jos valittu, näytetään vain\n"
"        jos määritelty ehdollinen vastaus on valittu edellisessä kysymyksessä"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Jos valittu, käyttäjät voivat palata edellisille sivuille."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Jos valittu, käyttäjien on kirjauduttava sisään ennen vastaamista, vaikka "
"heillä olisi voimassa oleva tunniste."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "Jos muu, mikä:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Jos satunnaistettu on valittu, lisää satunnaisten kysymysten määrä jakson "
"viereen."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Jos satunnaistettu on valittu, voit määrittää satunnaisten kysymysten määrän"
" osioittain. Tätä tilaa ei oteta huomioon live-istunnossa."

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "Jos kyllä, selitä, mitä mielestäsi puuttuu, ja anna esimerkkejä."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "If you wish, you can"
msgstr "Jos haluat, voit "

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
msgid "Image"
msgstr "Kuva"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "Kuvan tiedostonimi"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
#, python-format
msgid "Image Zoom Dialog"
msgstr "Kuvan zoomausvalintaikkuna"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Epäkäytännöllinen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "Käynnissä"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "Valitse alla olevasta luettelosta kaikki havupuut."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop ?"
msgstr "Missä maassa bonsai-tekniikka kehittyi ?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Sisällytä tämä kysymys osaksi tietokilpailun pisteytystä. Vaatii vastauksen "
"ja vastauspisteytyksen huomioon ottamista."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Incorrect"
msgstr "Virheellinen"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Tehoton"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Syöttötiedon pitää olla sähköpostiosoite"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Kutsun pääsytunniste"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Vain kutsutut henkilöt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "On muokkaaja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "Onko sertifiointi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Onko sivu?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "On istunnossa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "Ei ole sertifiointi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Onko tuo käyttäjän syöttö osa kyselyistuntoa vai ei."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft ?"
msgstr "Onko havupuun puu kovaa vai pehmeää ?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Istanbul"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It does not mean anything specific"
msgstr "Se ei tarkoita mitään erityistä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It helps attendees focus on what you are saying"
msgstr "Se auttaa osallistujia keskittymään siihen, mitä sanot"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It helps attendees remember the content of your presentation"
msgstr "Se auttaa osallistujia muistamaan esityksesi sisällön"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It is a small bit of text, displayed to help participants answer"
msgstr ""
"Se on pieni tekstinpätkä, joka näytetään osallistujien auttamiseksi "
"vastaamaan seuraaviin kysymyksiin"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It is an option that can be different for each Survey"
msgstr "Se on vaihtoehto, joka voi olla erilainen jokaisessa tutkimuksessa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "On helppo löytää haluamani tuote"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It is more engaging for your audience"
msgstr "Se on yleisöäsi kiinnostavampi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "It's a Belgian word for \"Management\""
msgstr "Se on belgialainen sana, joka tarkoittaa \"johtamista\""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"En ole koskaan halunnut mennä Japaniin. Yksinkertaisesti siksi, etten pidä "
"kalan syömisestä. Ja tiedän, että se on hyvin suosittua Afrikassa."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japani"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Liity istuntoon"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "Vain luokittelemaan vastauksiasi, älä huoli."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Etikettien järjestys"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Valintaehdotuksissa käytetyt etiketit: matriisin rivit"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Valintaehdotusten etiketit: yksinkertainen valinta, monivalinta ja matriisin"
" sarakkeet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Kieli"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Suuri työpöytä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question_answer____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Viimeksi näytetty kysymys/sivu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Myöhässä olevat toimenpiteet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "Käynnistä istunto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Layout"
msgstr "Ulkoasu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Tulostaulu"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "jalat"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Sitruunapuut"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Let's get started!"
msgstr "Aloitetaan!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Let's give it a spin!"
msgstr "Kokeillaan sitä!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Let's have a look at your answers!"
msgstr "Katsotaanpa vastauksiasi!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Let's open the survey you just submitted."
msgstr "Avataan juuri lähettämäsi kysely."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Likely"
msgstr "Todennäköisesti"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "Rajaa yritykset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Rajoitettu määrä yrityksiä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#, python-format
msgid "Live Presentation"
msgstr "Suora esitys"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "Suora istunto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Live-istunnot"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr "Lataa <b>näytekysely</b>, jotta pääset nopeasti alkuun."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Sisäänkirjautuminen vaaditaan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Viestipohja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Pakollinen vastaus"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Taulukko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Matriisirivit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Taulukon tyyppi"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Maksimipäivämäärä ei voi olla pienempi kuin minimipäivämäärä!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "Max datetime ei voi olla pienempi kuin min datetime!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Suurin pituus ei voi olla pienempi kuin pienin pituus!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Maksimiarvo ei voi olla pienempi kuin minimiarvo!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Maximum"
msgstr "Enimmäismäärä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Maksimipäivä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Enimmäisaika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Tekstin maksimipituus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Maksimiarvo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Ehkä etsit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Minimum"
msgstr "Vähimmäismäärä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Minimipäivä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Vähimmäispäivämäärä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Tekstin minimipituus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Minimiarvo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Moderni sininen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Moderni kulta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Moderni violetti"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Mooses"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Elbrus-vuori (Venäjä)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Etna-tulivuori (Italia - Sisilia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Teide-vuori (Espanja - Teneriffa)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Vuoristomänty"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Useita rivejä sisältävä tekstikenttä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Monivalintakysymys, jossa on useita vastauksia"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Monivalinta, jossa on yksi vastaus"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Monivalinta: monta vastausta sallittu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Monivalinta: vain yksi vastaus sallittu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Useita valintoja riviä kohden"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "MyCompany Myyjä"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "MyCompany-toimittajan sertifiointi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Neutral"
msgstr "Neutraali"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Never (less than once a month)"
msgstr "Ei koskaan (harvemmin kuin kerran kuukaudessa)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Uusi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "New York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Uusi kutsu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Lempinimi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Ei"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "Ei vielä kysymyksiä!"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "Ei kyselyä löydetty"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "No answers yet!"
msgstr "Ei vielä vastauksia!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr "Yrityksiä ei ole jäljellä."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No one answered this question."
msgstr "Kukaan ei vastannut tähän kysymykseen."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Ei vielä kysymyksiä, palaa myöhemmin."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Ei pisteytystä"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Tutkimusmerkintöjä ei löytynyt"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Käyttäjän syöttörivejä ei löytynyt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's to small for the human eye."
msgstr "Ei, se on liian pieni ihmissilmälle."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Kuusi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Ei hyvä, ei huono"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "Ei vielä aloitettu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Now that you are done, submit your form."
msgstr "Kun olet valmis, lähetä lomake."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Now, use this shortcut to go back to the survey."
msgstr "Käytä tätä pikakuvaketta palataksesi takaisin kyselyyn."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
msgid "Number"
msgstr "Numero"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Yritysten määrä"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Laatikoiden lukumäärä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Numeroarvo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Numeerinen vastaus"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Object-Directed Open Organization"
msgstr "Kohdeohjattu avoin organisaatio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "Esiintyminen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Odoo Certification"
msgstr "Odoo sertifiointi"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Toimistotuoli musta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Often (1-3 times per week)"
msgstr "Usein (1-3 kertaa viikossa)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr ""
"Tutkimuksen kysymyksissä voidaan määritellä \"paikanvaraaja\". Mutta mitä "
"varten ne ovat?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Kerran päivässä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Kerran kuukaudessa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Kerran viikossa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Kerran vuodessa"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Yksi valinta riviä kohti"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "One needs to answer at least half the questions correctly"
msgstr "Vähintään puolet kysymyksistä on vastattava oikein"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "One needs to get 50% of the total score"
msgstr "On saatava 50% of kokonaispistemäärää"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "Yksi sivu kysymystä kohden"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Yksi sivu jaksoa kohti"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Yksi sivu, jossa on kaikki kysymykset"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Only a single question left!"
msgstr "Vain yksi kysymys jäljellä!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Only show survey results having selected this answer"
msgstr "Näytä vain ne tutkimustulokset, jotka ovat valinneet tämän vastauksen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Only survey users can manage sessions."
msgstr "Vain kyselyn käyttäjät voivat hallita istuntoja."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "Hups! Mikään kysely ei vastaa tätä koodia."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "Avaa Session Manager"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
#, python-format
msgid "Open section"
msgstr "Avoin osa"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valinnainen käännöskieli (ISO-koodi), joka valitaan sähköpostia "
"lähetettäessä. Jos sitä ei ole asetettu, käytetään englanninkielistä "
"versiota. Tämän pitäisi yleensä olla sijoitusilmaus, joka antaa sopivan "
"kielen, esim. {{ object.partner_id.lang }}."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "Vaihtoehdot"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Organizational Development for Operation Officers"
msgstr "Organisaation kehittäminen operatiivisille toimihenkilöille"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Other (see comments)"
msgstr "Muu (ks. huomautukset)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Yrityksemme muutama kysymys ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Lähtevän postin palvelin"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance"
msgstr "Yleinen suorituskyky"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Törkeän kallis"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Sivu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Sivut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "Sivutus"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papyrus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Partial"
msgstr "Osittainen"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Partially"
msgstr "Osittain"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "Osallistuja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "Ilmoittautuneita yhteensä"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Osallistu {{ object.survey_id.display_name }} tutkimukseen"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "Osallistumiset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Kumppani"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#, python-format
msgid "Passed"
msgstr "Hyväksytty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "Hyväksytty ja hylätty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "Vain läpäisty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Kiinnitä huomiota isännän näyttöön seuraavaan kysymykseen asti."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "Jäljellä oleva prosenttiosuus"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Performance by Section"
msgstr "Suorituskyky jaksoittain"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Ehkä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "Valitse merkki..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "Valitse tyyli..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "Valitse malli..."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Valitse aihe"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Pie Graph"
msgstr "Piirakkakaavio"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Pinaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "Paikanpitäjä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr ""
"Ole hyvä ja täytä tämä hyvin lyhyt kysely, jotta saamme tietää, kuinka "
"tyytyväinen olet tuotteisiimme."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Syötä vähintään yksi kelvollinen vastaanottaja."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Varmista, että kyselyssäsi on vähintään yksi kysymys. Tarvitset myös "
"vähintään yhden jakson, jos valitsit \"Sivu per jakso\" -asettelun.<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Käytännöt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelot"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Huono laatu"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Practice in front of a mirror"
msgstr "Harjoittele peilin edessä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Ennalta määritellyt kysymykset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "Esikatselu"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Hinnat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Tulosta"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Tuotteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Kysymys"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "Kysymys & sivut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Kysymys (matriisirivinä)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "Kysymys vastauslomake"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Kysymyksen vastausten määrä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "Kysymysten valinta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Time Limit"
msgstr "Kysymyksen aikaraja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Kyselytunnin aikaraja saavutettu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "Kysymyksen tyyppi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_id
msgid ""
"Question containing the triggering answer to display the current question."
msgstr ""
"Kysymys, joka sisältää laukaisevan vastauksen nykyisen kysymyksen "
"näyttämiseksi."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Question type should be empty for these pages: %s"
msgstr "Kysymystyypin tulisi olla tyhjä näillä sivuilla: %s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "Kysymykset"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "Kysymykset & vastaukset (Q&A)"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Tietokilpailu yrityksestämme"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Tietokilpailu läpäisty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Tietokilpailu läpäisty"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "Satunnaistettu jaksoittain"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Rarely (1-3 times per month)"
msgstr "Harvoin (1-3 kertaa kuukaudessa)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Valmis siirrettäväksi"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "Oletko valmis muuttamaan <b>tiedonkeruutapojasi</b>?"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample..."
msgstr "Oletko valmis testaamaan? Valitse näyte..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Vastaanottajat"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Red Pen"
msgstr "Punainen kynä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "Rekisteröitynyt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Renderöintimalli"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "Avaa uudelleen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "Vaadi kirjautuminen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "Vaadittu pistemäärä (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Lähetä kommentti uudelleen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Lähetä kutsu uudelleen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Lähetä kutsu uudelleen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Responded"
msgstr "Vastasi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Results Overview"
msgstr "Tulosten yleiskatsaus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Yritä uudelleen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Palkitse nopeat vastaukset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Palkinnot haasteista"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Rivin vastaus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Rivi1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Rivi2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Rivi3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Rivit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Tallenna käyttäjän sähköpostiosoitteena"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Tallenna käyttäjän lempinimeksi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Tieteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "Pisteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Pisteet (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Pisteet oikeasta vastauksesta tähän kysymykseen."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Pisteet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "Pisteytys"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "Pisteytystyyppi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Pisteytys ja vastaukset lopussa"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr "Pisteytys ilman vastauksia lopussa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Hakuotsikko"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Etsi kysymys"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "Hakukyselyn käyttäjän syötteet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Haku Käyttäjän syöttämät rivit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Osa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Jaksot ja kysymykset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "Katso tulokset"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Valitse kaikki käytettävissä olevat räätälöitävissä olevat räätälöitävät "
"työpöydät"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Valitse kaikki olemassa olevat tuotteet"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Valitse kaikki tuotteet, joiden myyntihinta on vähintään 100 dollaria"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Valitse puita, jotka tekivät yli 20 000 myyntiä tänä vuonna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Lähetä"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "Lähetetään osallistujalle, jos hän on läpäissyt sertifioinnin"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "Lähetetään osallistujalle, kun jaat kyselyn"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "Istunnon koodi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "Istunnon linkki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Istunnon tila"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Istuntokoodin on oltava yksilöllinen"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Shanghai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "Jaa"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Share a Survey"
msgstr "Jaa kysely"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Näytä kommenttikenttä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Näytä istunnon tulostaulu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "Näytä kaikki tietueet joissa on toimenpide myöhässä."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Show them slides with a ton of text they need to read fast"
msgstr ""
"Näytä heille dioja, joissa on paljon tekstiä, joka heidän on luettava "
"nopeasti"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Yksirivinen tekstikenttä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Skipped"
msgstr "Ohitettu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Pehmeä"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr "Osa juuri syöttämistäsi sähköposteista ovat virheellisiä: %s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr ""
"Joitakin yleisiä tietoja sinusta. Niitä käytetään sisäisesti vain "
"tilastointiin."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "Joitakin kysymyksiä yrityksestämme. Tunnetko meidät todella?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Valitettavasti kukaan ei ole vielä vastannut tähän kyselyyn."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Anteeksi, et ole ollut tarpeeksi nopea."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "Etelä-Amerikka"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Etelä-Korea"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Avaruusasemat"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Speak softly so that they need to focus to hear you"
msgstr "Puhu pehmeästi, jotta heidän on keskityttävä kuulemaan sinut"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Speak too fast"
msgstr "Puhu liian nopeasti"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Kevät"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Aloita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Aloita sertifiointi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "Aloita live-istunto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Aloita kysely"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Aloituspäivä ja -aika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Tila"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Mansikat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Aihe"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Aihe..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Tallenna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Onnistuminen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "Onnistumissuhde (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate:"
msgstr "Onnistumisprosentti:"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Ehdotetut arvot"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Ehdotettu vastaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Ehdotettu arvo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Ehdotus"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Kesä"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Kysely"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Kyselyn vastausrivi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "Tutkimuksen tunnukset"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Ohjattu kyselykutsu"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Kyselyn otsikko"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Survey Participant"
msgstr "Kyselyyn osallistuja"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Kyselyn kysymykset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "Kyselyn aikaraja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Kyselyn aikaraja saavutettu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Title"
msgstr "Kyselyn otsikko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "Kyselyn URL"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Käyttäjäsyöte kyselyssä"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Kyselyn käyttäjän syöttörivi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Kyselyn käyttäjien syötteet"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "Kysely: Sertifioinnin onnistuminen"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Tutkimus: Kutsu"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Kyselyt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "Ota uudelleen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "Testi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "Testisyöte"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "Testaa tietämyksesi käytännöistämme."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "Testaa tietosi hinnoistamme."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "Testaa tietämyksesi tuotteista!"

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "Testaa myyjätaitosi!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "Vain testit"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Teksti"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Tekstimuotoinen vastaus"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Thank you for your participation, hope you had a blast!"
msgstr "Kiitos osallistumisestanne, toivottavasti teillä oli hauskaa!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Thank you very much for your feedback. We highly value your opinion !"
msgstr "Kiitos paljon palautteestasi. Arvostamme suuresti mielipidettänne !"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "Kiitoksia!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Thank you. We will contact you soon."
msgstr "Kiitos. Otamme sinuun yhteyttä pian."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "Vastauksen on oltava oikean tyyppinen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#, python-format
msgid "The answer you entered is not valid."
msgstr "Antamasi vastaus ei ole kelvollinen."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Yritysten määrän on oltava positiivinen luku, jos kyselyssä on rajoitettu "
"määrä yrityksiä."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Jokaisen kyselyn tunnuksen on oltava yksilöllinen!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Maksu kassalla on selkeä ja turvallinen prosessi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "Oikea vastaus oli:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Tutkimusistunnon nykyinen kysymys."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr "Valitsemasi päivämäärä on suurempi kuin enimmäispäivämäärä: "

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr "Valitsemasi päivämäärä on alhaisempi kuin minimipäivä: "

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Kuvaus näytetään kyselyn etusivulla. Voit käyttää sitä antaaksesi "
"ehdokkaille tarkoituksen ja ohjeet ennen kuin he aloittavat kyselyn."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr "Seuraavat asiakkaat ovat jo saaneet kutsun"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr "Seuraavat sähköpostit ovat jo saaneet kutsun"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Seuraavilla vastaanottajilla ei ole käyttäjätiliä: %s. Sinun pitäisi luoda "
"heille käyttäjätilit tai sallia ulkoinen kirjautuminen asetuksissa."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Uusi ulkoasu ja muotoilu on raikas ja ajan tasalla"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Sinulla ei ole oikeuksia nähdä hakemaasi sivua."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Onnistumisprosentti on määriteltävä 0:n ja 100:n välille."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Kysymyksellä on aikaraja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "Istunto ei vielä alkanut."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Istunto alkaa automaattisesti, kun isäntä käynnistyy."

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The survey has already started."
msgstr "Tutkimus on jo aloitettu."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Tutkimuksella on aikaraja"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Ajankohta, jolloin nykyinen kysymys on alkanut; käytetään osallistujien "
"ajastimen käsittelyyn."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr "Aikarajan on oltava positiivinen luku, jos kysely on määräaikainen."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr "Tuotteiden vertailutyökalu on hyödyllinen valinnan tekemisessä"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr "Käyttäjä ei ole onnistunut sertifioinnissa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Kyselyn validoinnissa tapahtui virhe."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "They are a default answer, used if the participant skips the question"
msgstr ""
"Ne ovat oletusvastaus, jota käytetään, jos osallistuja ohittaa kysymyksen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr "Ne ovat teknisiä parametreja, jotka takaavat sivun reagointikyvyn"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "Tämän vastauksen on oltava sähköpostiosoite"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "This answer must be an email address."
msgstr "Tämän vastauksen on oltava sähköpostiosoite."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
msgid "This certificate is presented to"
msgstr "Tämä todistus annetaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"This certificate is presented to\n"
"                                <br/>"
msgstr ""
"Tämä todistus annetaan\n"
"                               <br/>"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Osallistujat käyttävät tätä koodia päästäkseen istuntoosi. Voit vapaasti "
"muokata sitä haluamallasi tavalla!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey."
msgstr "Tämä on testikysely."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "Tämä ei ole päiväys"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "Tämä ei ole numero"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""
"Tätä vastaanottajien sähköpostiluetteloa ei muunneta yhteystiedoiksi."
"        Sähköpostit on erotettava toisistaan pilkuilla, puolipisteillä tai "
"rivinvaihdolla."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Tämä viesti tulee näkyviin, kun kysely on valmis"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "This question depends on another question's answer."
msgstr "Tämä kysymys riippuu toisen kysymyksen vastauksesta."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#, python-format
msgid "This question requires an answer."
msgstr "Kysymykseen ei voi jättää vastaamatta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "Tämä kysymys jätettiin väliin"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr ""
"Tässä osiossa on yleisiä tietoja sinusta. Niihin vastaaminen auttaa "
"vastauksiesi pätevöittämisessä."

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr "Tämä osio koskee itse verkkokauppakokemusta."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared !"
msgstr "Tämä pieni tietokilpailu testaa tietosi yrityksestämme. Ole valmis!"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"Tämän kyselyn avulla voit antaa palautetta kokemuksistasi tuotteistamme.\n"
"    Sen täyttäminen auttaa meitä parantamaan kokemustasi."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Tähän kyselyyn eivät voi osallistua ulkopuoliset henkilöt. Sinun tulisi "
"luoda käyttäjätunnukset tai päivittää kyselyyn pääsytila vastaavasti."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr "Tämä kysely on nyt suljettu. Kiitos mielenkiinnostanne !"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr ""
"Tämä kysely on avoin vain rekisteröityneille henkilöille. Ole hyvä ja lähetä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "Aika ja pisteytys"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Aikaraja (minuuttia)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
msgid "Time limit (seconds)"
msgstr "Aikaraja (sekuntia)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "Tämän todistuksen määräaika:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "Tämän kyselyn aikaraja:"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Otsikko"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "To join:"
msgstr "Liittyminen:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Jos haluat vastata tähän kyselyyn, sulje kaikki muut välilehdet osoitteessa "
"<strong class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Tämän päivän toimenpiteet"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokio"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Pisteet"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Täysin samaa mieltä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Täysin eri mieltä"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Puut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_id
msgid "Triggering Answer"
msgstr "Laukaiseva vastaus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_id
msgid "Triggering Question"
msgstr "Käynnistävä kysymys"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tyyppi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Vastausten tyypit"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Viestin lähettäminen ei onnistu, määritä lähettäjän sähköpostiosoite."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Unanswered"
msgstr "Vastaamattomat"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Uncategorized"
msgstr "Luokittelemattomat"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Alihintainen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Valitettavasti olet epäonnistunut testissä."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Ainutlaatuinen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Unlikely"
msgstr "Epätodennäköinen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Tulevat aktiviteetit"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Use a fun visual support, like a live presentation"
msgstr "Käytä hauskaa visuaalista tukea, kuten live-esitystä"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Use humor and make jokes"
msgstr "Käytä huumoria ja vitsaile"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr "Käytä leivänmuruja palataksesi nopeasti takaisin kojelaudalle."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Käytä tätä kenttää lisäselvitysten lisäämiseen kysymyksestäsi tai sen "
"havainnollistamiseen kuvilla tai videolla"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Käytetään satunnaistetuissa osioissa ottamaan X satunnaista kysymystä "
"kaikista kyseisen osion kysymyksistä."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Hyödyllinen"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Käyttäjä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Käyttäjän valinta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Käyttäjän syöte"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Käyttäjän vastaukset"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Käyttäjän syöttämän rivin tiedot"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Käyttäjien vastaukset"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Käyttäjät voivat palata"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Käyttäjät voivat rekisteröityä"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Vahvista syöte"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "Vahvistuksen virheilmoitus"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Vihannekset"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Erittäin halpa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Olemme rekisteröineet vastauksesi! Odota, että isäntä siirtyy seuraavaan "
"kysymykseen."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr "Me sanomme, että omena ei putoa kauas puusta, joten tässä on puita."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "Saatamme olla kiinnostuneita panoksestasi."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr ""
"Tervetuloa tähän Odoo-sertifiointiin. Saat 2 satunnaista kysymystä 3 "
"kysymyksen joukosta."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Minä päivänä ja mihin kellonaikaan asiakkaat todennäköisimmin soittavat "
"asiakaspalveluun (ei luokiteltu)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Mikä päivä on mielestänne paras päivä aloittaa vuosittainen myynti (ei "
"luokiteltu)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce ?"
msgstr "Mitä mieltä olet uudesta verkkokaupastamme ?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Mitä mieltä olet hinnoistamme (ei luokiteltu)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey ?"
msgstr "Mitä mieltä olet tästä tutkimuksesta?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "What does \"ODOO\" stand for?"
msgstr "Mitä tarkoittaa \"ODOO\"?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "Mitä Odoo-kyselyn läpäiseminen edellyttää?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "What is a frequent mistake public speakers do?"
msgstr "Mikä on yleinen virhe, jonka julkiset puhujat tekevät?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "What is the best way to catch the attention of an audience?"
msgstr "Mikä on paras tapa herättää yleisön huomio?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world ?"
msgstr "Mikä on maailman suurin kaupunki ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email ?"
msgstr "Mikä on sähköpostiosoitteesi?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname ?"
msgstr "Mikä on lempinimesi ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239 ?"
msgstr "Mikä on suunnilleen plutonium-239:n kriittinen massa?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die ?"
msgstr "Milloin Tšingis-kaan kuoli?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree ?"
msgstr "Milloin juuri Marc Demo viljeli ensimmäistä omenapuuta?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Milloin keräätte sadon noista hedelmistä"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born ?"
msgstr "Milloin Mitchell Admin on syntynyt?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr "Milloin on syntymäaikasi?"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
#, python-format
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr "Kun valitset vastauksen, Odoo tallentaa sen puolestasi."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from ?"
msgstr "Mistä olet kotoisin?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr "Missä asut?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "Haluammeko näyttää tämän kyselyn osallistujien tulostaulun vai emme."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club ?"
msgstr "Kuka muusikko ei ole 27. klubissa ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "Mihin luokkaan tomaatti kuuluu"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe ?"
msgstr "Mikä on Euroopan korkein tulivuori?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr "Millä seuraavista sanoista kuvailisitte tuotteitamme?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Mitä seuraavista käyttäisit pölyttämiseen"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso ?"
msgstr "Mitä maalausta tai piirustusta Pablo Picasso ei tehnyt?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Mikä sitaatti on Jean-Claude Van Dammen"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you ?"
msgstr "Kuka sinä olet?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza ?"
msgstr "Kuka on Gizan suuren pyramidin arkkitehti?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass ?"
msgstr ""
"Kuka sai fysiikan Nobel-palkinnon neutriino-oskillaatioiden löytämisestä, "
"joka osoittaa, että neutriinoilla on massaa?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr ""
"Miksi sinun pitäisi harkita esityksen tekemistä hauskemmaksi pienellä "
"tietokilpailulla?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Leveys"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Talvi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"VVVV-KK-PP\n"
"                                       <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Kalenteri\" title=\"Kalenteri\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"VVVVV-KK-PP hh:mm:ss\n"
"                                       <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Kalenteri\" title=\"Kalenteri\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid "Yellow Pen"
msgstr "Keltainen kynä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Kyllä"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Kyllä, se on ainoa asia, jonka ihmissilmä voi nähdä."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Voit luoda sertifiointeja vain sellaisille kyselyille, joissa on "
"pisteytysmekanismi."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr ""
"Voit jakaa linkkejäsi eri tavoin: sähköpostitse, kutsua pikakuvakkeen, live-"
"esityksen, ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"Et voi poistaa kysymyksiä kyselytutkimuksista \"%(survey_names)s\" suorien "
"istuntojen ollessa käynnissä."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Et voi lähettää kutsua \"Yksi sivu per osio\" -kyselyyn, jos kyselyssä ei "
"ole osioita."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Et voi lähettää kutsua \"Yksi sivu per osio\" -kyselyyn, jos kyselyssä on "
"vain tyhjiä osioita."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "Et voi lähettää kutsua kyselyyn jossa ei ole kysymyksiä."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "Et voi lähettää kutsuja suljettuihin kyselyihin."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Sait merkin"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Tuloksesi oli"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Tunteesi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#, python-format
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr ""
"Vastauksesi auttavat meitä parantamaan tuotevalikoimaamme, jotta voimme "
"palvella sinua entistä paremmin."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
#, python-format
msgid "Zoom in"
msgstr "Lähennä"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
#, python-format
msgid "Zoom out"
msgstr "Loitonna"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
#, python-format
msgid "Zoomed Image"
msgstr "Suurennettu kuva"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "vastaus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "vastatuista"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "yritykset"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
#, python-format
msgid "close"
msgstr "sulkemista"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "esim. \"Kiitos paljon palautteestasi!\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "esim. \"Seuraava kysely auttaa meitä...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "esim. \"Mikä on...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "esim. 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr ""
"esim. ohjeet, ohjeet, kuva, ..., jotka auttavat osallistujia vastaamaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "esim. Kukaan ei pysty ratkaisemaan haasteita kuten sinä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "esim. ongelmanratkaisija"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "esim. tyytyväisyyskysely"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"for successfully completing\n"
"                                <br/>"
msgstr ""
"menestyksekkäästä suorittamisesta\n"
"                               <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "kirjaudu sisään"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "minuutit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "of"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "of achievement"
msgstr "saavutuksesta"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "or press CTRL+Enter"
msgstr "tai paina CTRL+Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "or press Enter"
msgstr "tai paina Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "or press ⌘+Enter"
msgstr "tai paina ⌘+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "sivuista"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "review your answers"
msgstr "tarkastele vastauksiasi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "kysely on vanhentunut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "kysely on tyhjä"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "tätä sivua"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr " "
