from odoo import api, fields, models
from datetime import datetime

class ReservationPaymentReport(models.TransientModel):
    _name = 'reservation.payment.report.wizard'

    date_from = fields.Date(
        string='Date From',
        required=True
    )
    date_to = fields.Date(
        string='Date To',
        required=True,
        default= datetime.now().date()
    )

    check_in_state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirm'),
        ('done', 'Done'),
        ('cancel', 'Cancel'),
    ], default='done', string='Check In State')


    is_guarantee = fields.Boolean(
        string='Guarantee',
        default=True
    )
    is_auto_payment = fields.Boolean(
        string='Is Auto Payment',
        default=True
    )

    def print_report(self):

        vals = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'check_in_state': self.check_in_state,
            'is_guarantee': self.is_guarantee,
            'is_payment': self.is_auto_payment
        }

        return self.env.ref('banquet_managment.reservation_payment_report_id').report_action(self, data=vals)



class ReservationReportAbstractModel(models.AbstractModel):
    _name = 'report.banquet_managment.reservation_report_temp_id'

    def _get_report_values(self, docids=None, data=None):
        reservation = self.env['hotel.reservation']
        if data.get('check_in_state'):
            reservation = reservation.search([('state', '=', data.get('check_in_state'))])
        else:
            reservation = reservation.search([('state', 'in', ['draft', 'done', 'confirm'])])
        reservation = reservation.reservation_line.filtered(lambda rec: str(rec.checkin.date()) >= data.get('date_from') and str(rec.checkin.date()) <= data.get('date_to')).line_id
        if not data.get('check_in_state'):
            reservation = reservation.filtered(lambda rec: rec.state in ['draft', 'done', 'confirm'])
        if data.get('is_guarantee'):
            reservation = reservation.filtered(lambda rec: rec.is_auto_payment == True)

        if data.get('is_payment'):
            reservation = reservation.filtered(lambda rec: rec.is_guarrantte_amount == True)

        payments = self.env['reservation.parking.payment']
        if data.get('check_in_state'):
            if data.get('check_in_state') != 'draft':
                payments = payments.search([('reservation_id', 'in', reservation.ids)])



        return {
            'record': payments,
            'reservation': reservation
        }
