# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:41+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid "%(project_name)s's Documents"
msgstr "%(project_name)s:n asiakirjat"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Joukko ehtoja ja toimia, jotka ovat kaikkien ehtoja vastaavien "
"liitetiedostojen käytettävissä"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_ask_for_validation
msgid "Ask for Validation"
msgstr "Pyydä varmistusta"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Liite"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Luokittele ja jaa asiakirjoja asiakkaidesi kanssa"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_facet
msgid "Category"
msgstr "Kategoria"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Luo"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Luo tehtävä"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Oletustunnisteet"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_deprecate
msgid "Deprecate"
msgstr "Poista käytöstä"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_deprecated
msgid "Deprecated"
msgstr "Vanhentunut"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
#: model:ir.model.fields,field_description:documents_project.field_ir_attachment__document_ids
msgid "Document"
msgstr "Dokumentti"

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
#, python-format
msgid "Documents"
msgstr "Dokumentit"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Asiakirjat ja analytiikka"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_folder
msgid "Documents Workspace"
msgstr "Asiakirjat-työtila"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_draft
msgid "Draft"
msgstr "Luonnos"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__is_shared
msgid "Is Shared"
msgstr "On jaettu"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_mark_as_draft
msgid "Mark As Draft"
msgstr "Merkitse luonnokseksi"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "Merged Workspace"
msgstr "Yhdistetty työtila"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "New task from Documents"
msgstr "Uusi tehtävä asiakirjoista"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__document_count
msgid "Number of documents in Project"
msgstr "Asiakirjojen määrä projektissa"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_count
msgid "Number of documents in Task"
msgstr "Asiakirjojen määrä tehtävässä"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_documents_folder__project_ids
msgid "Project"
msgstr "Projektit"

#. module: documents_project
#: model:documents.folder,name:documents_project.documents_project_folder
msgid "Projects"
msgstr "Projektit"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_ids
#: model:ir.model.fields,field_description:documents_project.field_project_task__shared_document_ids
msgid "Shared Documents"
msgstr "Jaetut asiakirjat"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__shared_document_count
msgid "Shared Documents Count"
msgstr "Jaettujen asiakirjojen määrä"

#. module: documents_project
#: model:documents.facet,name:documents_project.documents_project_status
msgid "Status"
msgstr "Tila"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Tehtävä"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "Task created from document"
msgstr "Asiakirjasta luotu tehtävä"

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr "Project-sovellus tarvitsee työtilan \"%s\", eikä sitä voi poistaa."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"The \"%s\" workspace should either be in the \"%s\" company like this "
"project or be open to all companies."
msgstr ""
"Työtilan \"%s\" pitäisi olla joko \"%s\" -yrityksessä, kuten tässä "
"projektissa, tai sen pitäisi olla avoin kaikille yrityksille."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the \"%s\" project to "
"which it is linked. Please update the company of the \"%s\" project, or "
"leave the company of this workspace empty."
msgstr ""
"Tämän työtilan tulisi pysyä samassa yrityksessä kuin \"%s\" -projekti, johon"
" se on linkitetty. Päivitä \"%s-projektin yritys tai jätä tämän työtilan "
"yritys tyhjäksi."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid ""
"This workspace should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Tämän työtilan tulisi pysyä samassa yrityksessä kuin seuraavat projektit, joihin se on linkitetty:\n"
"%s\n"
"\n"
"Päivitä näiden projektien yritys tai jätä tämän työtilan yritys tyhjäksi."

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_to_validate
msgid "To Validate"
msgstr "Vahvistettavaksi"

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Lähetä <span class=\"fw-normal\">tiedosto tai</span> vedä <span class=\"fw-"
"normal\">se tänne.</span>"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__project_use_documents
msgid "Use Documents"
msgstr "Käytä asiakirjoja"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule_validate
msgid "Validate"
msgstr "Vahvista"

#. module: documents_project
#: model:documents.tag,name:documents_project.documents_project_status_validated
msgid "Validated"
msgstr "Vahvistettu"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,field_description:documents_project.field_project_task__documents_folder_id
msgid "Workspace"
msgstr "Työtila"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Workspace in which all of the documents of this project will be categorized."
" All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Työtila, johon kaikki tämän projektin asiakirjat luokitellaan. Kaikki "
"tehtävien liitetiedostot lisätään automaattisesti asiakirjoiksi myös tähän "
"työtilaan."

#. module: documents_project
#: code:addons/documents_project/models/project_project.py:0
#, python-format
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%s\" company:\n"
"%s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%s\" workspace blank."
msgstr ""
"Et voi muuttaa tämän projektin yritystä, koska sen työtila on linkitetty muihin seuraaviin projekteihin, jotka ovat edelleen \"%s\" -yrityksessä:\n"
"%s\n"
"\n"
"Päivitä kaikkien projektien yritys niin, että ne pysyvät samassa yrityksessä kuin niiden työtila, tai jätä \"%s\" -työtilan yritys tyhjäksi."

#. module: documents_project
#: code:addons/documents_project/models/folder.py:0
#, python-format
msgid "You cannot set a company on the Projects workspace."
msgstr "Et voi määrittää yritystä Projektit-työtilassa."

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.share_page
msgid "documents shared"
msgstr "jaetut asiakirjat"

#. module: documents_project
#: code:addons/documents_project/models/workflow.py:0
#, python-format
msgid "new %s from %s"
msgstr "uusi %s kohteesta %s"
