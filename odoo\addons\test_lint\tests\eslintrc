{"env": {"browser": true, "es2017": true}, "parserOptions": {"ecmaVersion": 2019, "sourceType": "module"}, "root": true, "rules": {"no-undef": "error", "no-restricted-globals": ["error", "event", "self"], "no-const-assign": ["error"], "no-debugger": ["error"], "no-dupe-class-members": ["error"], "no-unsafe-negation": ["error"], "no-duplicate-imports": ["error"], "valid-typeof": ["error"], "no-unused-vars": ["error", {"vars": "all", "args": "none", "ignoreRestSiblings": false, "caughtErrors": "all", "caughtErrorsIgnorePattern": "^_"}]}, "globals": {"owl": "readonly", "odoo": "readonly", "$": "readonly", "jQuery": "readonly", "_": "readonly", "Chart": "readonly", "fuzzy": "readonly", "QWeb2": "readonly", "Popover": "readonly", "StackTrace": "readonly", "QUnit": "readonly", "luxon": "readonly", "moment": "readonly", "py": "readonly", "FullCalendar": "readonly", "ClipboardJS": "readonly", "Modal": "readonly", "Dropdown": "readonly", "ScrollSpy": "readonly", "Tooltip": "readonly", "Collapse": "readonly", "Alert": "readonly", "globalThis": "readonly", "module": "readonly", "chai": "readonly", "describe": "readonly", "it": "readonly", "afterEach": "readonly", "mocha": "readonly", "DOMPurify": "readonly"}}