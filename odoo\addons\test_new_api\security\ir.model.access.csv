"id","name","model_id:id","group_id:id","perm_read","perm_write","perm_create","perm_unlink"
access_category,test_new_api_category,test_new_api.model_test_new_api_category,,1,1,1,1
access_decimal_precision_test_all,decimal.precision.test,model_decimal_precision_test,,1,1,1,1
access_discussion,test_new_api_discussion,test_new_api.model_test_new_api_discussion,,1,1,1,1
access_message,test_new_api_message,test_new_api.model_test_new_api_message,,1,1,1,1
access_emailmessage,test_new_api_emailmessage,test_new_api.model_test_new_api_emailmessage,,1,1,1,1
access_multi,test_new_api_multi,test_new_api.model_test_new_api_multi,,1,1,1,1
access_multi_line,test_new_api_multi_line,test_new_api.model_test_new_api_multi_line,,1,1,1,1
access_multi_line2,test_new_api_multi_line2,test_new_api.model_test_new_api_multi_line2,,1,1,1,1
access_multi_tag,test_new_api_multi_tag,test_new_api.model_test_new_api_multi_tag,,1,1,1,1
access_creativework_edition,test_new_api_creativework_edition,model_test_new_api_creativework_edition,,1,1,1,1
access_creativework_book,test_new_api_creativework_book,model_test_new_api_creativework_book,,1,1,1,1
access_creativework_movie,test_new_api_creativework_movie,model_test_new_api_creativework_movie,,1,1,1,1
access_mixed,test_new_api_mixed,test_new_api.model_test_new_api_mixed,,1,1,1,1
access_domain_bool,access_domain_bool,model_domain_bool,,1,1,1,1
access_test_new_api_foo,access_test_new_api_foo,model_test_new_api_foo,,1,1,1,1
access_test_new_api_bar,access_test_new_api_bar,model_test_new_api_bar,,1,1,1,1
access_test_new_api_related,access_test_new_api_related,model_test_new_api_related,,1,1,1,1
access_test_new_api_company,access_test_new_api_company,model_test_new_api_company,,1,1,1,1
access_test_new_api_company_attr,access_test_new_api_company_attr,model_test_new_api_company_attr,,1,1,1,1
access_test_new_api_compute_inverse,access_test_new_api_compute_inverse,model_test_new_api_compute_inverse,,1,1,1,1
access_test_new_api_compute_readonly,access_test_new_api_compute_readonly,model_test_new_api_compute_readonly,,1,1,1,1
access_test_new_api_multi_compute_inverse,access_test_new_api_multi_compute_inverse,model_test_new_api_multi_compute_inverse,,1,1,1,1
access_test_new_api_recursive,access_test_new_api_recursive,model_test_new_api_recursive,,1,1,1,1
access_test_new_api_recursive_tree,access_test_new_api_recursive_tree,model_test_new_api_recursive_tree,,1,1,1,1
access_test_new_api_recursive_order,access_test_new_api_recursive_order,model_test_new_api_recursive_order,,1,1,1,1
access_test_new_api_recursive_line,access_test_new_api_recursive_line,model_test_new_api_recursive_line,,1,1,1,1
access_test_new_api_recursive_task,access_test_new_api_recursive_task,model_test_new_api_recursive_task,,1,1,1,1
access_test_new_api_cascade,access_test_new_api_cascade,model_test_new_api_cascade,,1,1,1,1
access_test_new_api_compute_readwrite,access_test_new_api_compute_readwrite,model_test_new_api_compute_readwrite,,1,1,1,1
access_test_new_api_compute_onchange,access_test_new_api_compute_onchange,model_test_new_api_compute_onchange,,1,1,1,1
access_test_new_api_compute_onchange_line,access_test_new_api_compute_onchange_line,model_test_new_api_compute_onchange_line,,1,1,1,1
access_test_new_api_compute_dynamic_depends,access_test_new_api_compute_dynamic_depends,model_test_new_api_compute_dynamic_depends,,1,1,1,1
access_test_new_api_compute_unassigned,access_test_new_api_compute_unassigned,model_test_new_api_compute_unassigned,,1,1,1,1
access_test_new_api_one2many,access_test_new_api_one2many,model_test_new_api_one2many,,1,1,1,1
access_test_new_api_one2many_line,access_test_new_api_one2many_line,model_test_new_api_one2many_line,,1,1,1,1
access_test_new_api_binary_svg,access_test_new_api_binary_svg,model_test_new_api_binary_svg,,1,1,1,1
access_test_new_api_monetary_base,access_test_new_api_monetary_base,model_test_new_api_monetary_base,,1,1,1,1
access_test_new_api_monetary_related,access_test_new_api_monetary_related,model_test_new_api_monetary_related,,1,1,1,1
access_test_new_api_monetary_custom,access_test_new_api_monetary_custom,model_test_new_api_monetary_custom,,1,1,1,1
access_test_new_api_monetary_inherits,access_test_new_api_monetary_inherits,model_test_new_api_monetary_inherits,,1,1,1,1
access_test_new_api_field_with_caps,access_test_new_api_field_with_caps,model_test_new_api_field_with_caps,,1,1,1,1
access_test_new_api_req_m2o,access_test_new_api_req_m2o,model_test_new_api_req_m2o,,1,1,1,1
access_test_new_api_selection,access_test_new_api_selection,model_test_new_api_selection,,1,1,1,1
access_test_new_api_attachment,access_test_new_api_attachment,model_test_new_api_attachment,,1,1,1,1
access_test_new_api_attachment_host,access_test_new_api_attachment_host,model_test_new_api_attachment_host,,1,1,1,1
access_test_new_api_model_image,access_test_new_api_model_image,model_test_new_api_model_image,,1,1,1,1
access_test_new_api_model_a,access_test_new_api_model_a,model_test_new_api_model_a,,1,1,1,1
access_test_new_api_model_b,access_test_new_api_model_b,model_test_new_api_model_b,,1,1,1,1
access_test_new_api_model_parent,access_test_new_api_model_parent,model_test_new_api_model_parent,,1,1,1,1
access_test_new_api_model_child,access_test_new_api_model_child,model_test_new_api_model_child,,1,1,1,1
access_test_new_api_model_child_nocheck,access_test_new_api_model_child_nocheck,model_test_new_api_model_child_nocheck,,1,1,1,1
access_test_new_api_display,access_test_new_api_display,model_test_new_api_display,,1,1,1,1
access_test_new_api_model_active_field,access_test_new_api_model_active_field,model_test_new_api_model_active_field,,1,1,1,1
access_test_new_api_model_many2one_reference,access_test_new_api_model_many2one_reference,model_test_new_api_model_many2one_reference,,1,1,1,1
access_test_new_api_inverse_m2o_ref,access_test_new_api_inverse_m2o_ref,model_test_new_api_inverse_m2o_ref,,1,1,1,1
access_test_new_api_model_binary,access_test_new_api_model_binary,model_test_new_api_model_binary,,1,1,1,1
access_test_new_api_model_child_m2o,access_test_new_api_model_child_m2o,model_test_new_api_model_child_m2o,,1,1,1,1
access_test_new_api_model_parent_m2o,access_test_new_api_model_parent_m2o,model_test_new_api_model_parent_m2o,,1,1,1,1
access_test_new_api_model_private_address_onchange,access_test_new_api_model_private_address_onchange,model_test_new_api_model_private_address_onchange,base.group_user,1,1,1,1
access_test_new_api_partner,access_test_new_api_partner,test_new_api.model_test_new_api_partner,,1,1,1,1
access_test_new_api_req_m2o_transient,access_test_new_api_req_m2o_transient,model_test_new_api_req_m2o_transient,,0,0,0,0
access_test_new_api_country,access_test_new_api_country,model_test_new_api_country,base.group_user,1,1,1,1
access_test_new_api_city,access_test_new_api_city,model_test_new_api_city,base.group_user,1,1,1,1
access_test_new_api_monetary_order,access_test_new_api_monetary_order,model_test_new_api_monetary_order,,1,1,1,1
access_test_new_api_monetary_order_line,access_test_new_api_monetary_order_line,model_test_new_api_monetary_order_line,,1,1,1,1
access_test_new_api_model_selection_base,access_test_new_api_model_selection_base,model_test_new_api_model_selection_base,,1,1,1,1
access_test_new_api_model_selection_required,access_test_new_api_model_selection_required,model_test_new_api_model_selection_required,,1,1,1,1
access_test_new_api_model_selection_non_stored,access_test_new_api_model_selection_non_stored,model_test_new_api_model_selection_non_stored,,1,1,1,1
access_test_new_api_model_selection_required_for_write_override,access_test_new_api_model_selection_required_for_write_override,model_test_new_api_model_selection_required_for_write_override,,1,1,1,1
access_test_new_api_model_selection_related,access_test_new_api_model_selection_related,test_new_api.model_test_new_api_model_selection_related,base.group_user,1,1,1,1
access_test_new_api_model_selection_related_updatable,access_test_new_api_model_selection_related_updatable,test_new_api.model_test_new_api_model_selection_related_updatable,base.group_user,1,1,1,1
access_test_new_api_move,access_test_new_api_move,model_test_new_api_move,,1,1,1,1
access_test_new_api_move_line,access_test_new_api_move_line,model_test_new_api_move_line,,1,1,1,1
access_test_new_api_payment,access_test_new_api_payment,model_test_new_api_payment,,1,1,1,1
access_test_new_api_order,access_test_new_api_order,model_test_new_api_order,,1,1,1,1
access_test_new_api_order_line,access_test_new_api_order_line,model_test_new_api_order_line,,1,1,1,1
access_test_new_api_model_shared_cache_compute_parent,access_test_new_api.model_shared_cache_compute_parent,model_test_new_api_model_shared_cache_compute_parent,,1,1,1,1
access_test_new_api_model_shared_cache_compute_line,access_test_new_api.model_shared_cache_compute_line,model_test_new_api_model_shared_cache_compute_line,,1,1,1,1
access_test_new_api_compute_container,access_test_new_api_compute_container,model_test_new_api_compute_container,,1,1,1,1
access_test_new_api_compute_member,access_test_new_api_compute_member,model_test_new_api_compute_member,,1,1,1,1
access_test_new_api_user,access_test_new_api_user,model_test_new_api_user,base.group_system,1,1,1,1
access_test_new_api_group,access_test_new_api_group,model_test_new_api_group,base.group_system,1,1,1,1
access_test_new_api_compute_editable,access_test_new_api_compute_editable,model_test_new_api_compute_editable,,1,1,1,1
access_test_new_api_compute_editable_line,access_test_new_api_compute_editable_line,model_test_new_api_compute_editable_line,,1,1,1,1
access_test_new_api_model_constrained_unlinks,access_test_new_api_model_constrained_unlinks,model_test_new_api_model_constrained_unlinks,,1,1,1,1
access_test_new_api_trigger_left,access_test_new_api_trigger_left,model_test_new_api_trigger_left,,1,1,1,1
access_test_new_api_trigger_middle,access_test_new_api_trigger_middle,model_test_new_api_trigger_middle,,1,1,1,1
access_test_new_api_trigger_right,access_test_new_api_trigger_right,model_test_new_api_trigger_right,,1,1,1,1
access_test_new_api_crew,access_test_new_api_crew,model_test_new_api_crew,base.group_user,1,1,1,1
access_test_new_api_ship,access_test_new_api_ship,model_test_new_api_ship,base.group_user,1,1,1,1
access_test_new_api_pirate,access_test_new_api_pirate,model_test_new_api_pirate,base.group_user,1,1,1,1
access_test_new_api_prisoner,access_test_new_api_prisoner,model_test_new_api_prisoner,base.group_user,1,1,1,1
access_test_new_api_precompute,access_test_new_api_precompute,model_test_new_api_precompute,,1,0,0,0
access_test_new_api_precompute_line,access_test_new_api_precompute_line,model_test_new_api_precompute_line,,1,0,0,0
access_test_new_api_precompute_combo,access_test_new_api_precompute_combo,model_test_new_api_precompute_combo,,1,0,0,0
access_test_new_api_precompute_editable,access_test_new_api_precompute_editable,model_test_new_api_precompute_editable,,1,0,0,0
access_test_new_api_precompute_readonly,access_test_new_api_precompute_readonly,model_test_new_api_precompute_readonly,,1,0,0,0
access_test_new_api_precompute_required,access_test_new_api_precompute_required,model_test_new_api_precompute_required,,1,0,0,0
access_test_new_api_precompute_monetary,access_test_new_api_precompute_monetary,model_test_new_api_precompute_monetary,,1,0,0,0
access_test_new_api_prefetch,access_test_new_api_prefetch,model_test_new_api_prefetch,,1,0,0,0
access_test_new_api_modified,access_test_new_api_modified,model_test_new_api_modified,base.group_user,1,1,1,1
access_test_new_api_modified_line,access_test_new_api_modified_line,model_test_new_api_modified_line,base.group_user,1,1,1,1
access_test_new_api_related_translation_1,access_test_new_api_related_translation_1,model_test_new_api_related_translation_1,,1,1,1,1
access_test_new_api_related_translation_2,access_test_new_api_related_translation_2,model_test_new_api_related_translation_2,,1,1,1,1
access_test_new_api_related_translation_3,access_test_new_api_related_translation_3,model_test_new_api_related_translation_3,,1,1,1,1
access_test_new_api_indexed_translation,access_test_new_api_indexed_translation,model_test_new_api_indexed_translation,,1,1,1,1
access_test_new_api_empty_char,access_test_new_api_empty_char,model_test_new_api_empty_char,,1,1,1,1
access_test_new_api_unlink_container,access_test_new_api_unlink_container,model_test_new_api_unlink_container,,1,1,1,1
access_test_new_api_unlink_line,access_test_new_api_unlink_line,model_test_new_api_unlink_line,,1,1,1,1
access_test_new_api_team,access_test_new_api_team,model_test_new_api_team,base.group_user,1,0,0,0
access_test_new_api_team_member,access_test_new_api_team_member,model_test_new_api_team_member,base.group_user,1,0,0,0
access_test_new_api_unsearchable_o2m,access_test_new_api_unsearchable_o2m,model_test_new_api_unsearchable_o2m,,1,1,1,1
access_test_new_api_autovacuumed,access_test_new_api_autovacuumed,model_test_new_api_autovacuumed,base.group_user,1,0,0,0
