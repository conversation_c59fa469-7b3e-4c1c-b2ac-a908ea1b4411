# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta


class ResUsers(models.Model):
    _inherit = 'res.users'

    is_hotel_checkin_user = fields.Boolean(
        string='Hotel Check-in User',
        help="If checked, the user will be added to the Hotel Check-in User group"
    )

    @api.onchange('is_hotel_checkin_user')
    def _onchange_is_hotel_checkin_user(self):
        """Update user groups when the checkbox is toggled"""
        hotel_checkin_group = self.env.ref('hotel_checkin.group_hotel_checkin_user')
        if self.is_hotel_checkin_user:
            self.groups_id = [(4, hotel_checkin_group.id)]
        else:
            self.groups_id = [(3, hotel_checkin_group.id)]
    
class HotelReservation(models.Model):
    _inherit = "hotel.reservation"

    # Override the default search to only show relevant reservations
    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Override search to show only confirmed and done reservations for check-in users"""
        if self.env.user.has_group('hotel_checkin.group_hotel_checkin_user'):
            if not any(arg[0] == 'state' for arg in (args or [])):
                args = (args or []) + ['|', ('state', '=', 'confirm'), ('state', '=', 'done')]
        return super().search(args, offset=offset, limit=limit, order=order, count=count)
    
    # Make all fields computed and non-stored to always read from the original reservation
    is_premium = fields.Boolean(
        string='Premium Suite',
        compute='_compute_is_premium',
    )
    
    price = fields.Float(
        string='Price per Night',
        compute='_compute_price',
    )

    checkin_date = fields.Datetime(
        string='Check-in Date',
        compute='_compute_dates',
    )

    checkout_date = fields.Datetime(
        string='Check-out Date',
        compute='_compute_dates',
    )
    
    checkin_date_display = fields.Date(
        string='Check-in Date (Display)',
        compute='_compute_display_dates',
    )

    checkout_date_display = fields.Date(
        string='Check-out Date (Display)',
        compute='_compute_display_dates',
    )
    
    stay_days = fields.Integer(
        string='Days of Stay',
        compute='_compute_stay_days',
    )

    is_late_checkout = fields.Boolean(
        string='Late Checkout',
        compute='_compute_is_late_checkout',
        search='_search_is_late_checkout',
    )

    room_count = fields.Integer(
        string='Number of Rooms',
        compute='_compute_room_count',
    )
    
    room_names = fields.Char(
        string='Room Names',
        compute='_compute_room_names',
    )
    
    folio_id = fields.Many2one(
        'hotel.folio',
        string='Folio',
        compute='_compute_folio_id',
    )
    
    has_folio = fields.Boolean(
        string='Has Folio',
        compute='_compute_has_folio',
    )

    @api.depends('folio_id')
    def _compute_has_folio(self):
        """Compute whether the reservation has an associated folio"""
        for record in self:
            record.has_folio = bool(record.folio_id)
    
    @api.depends('name')
    def _compute_folio_id(self):
        """Get the folio associated with this reservation"""
        for record in self:
            # Look for folio with this reservation
            folio = self.env['hotel.folio'].search([('reservation_id', '=', record.id)], limit=1)
            if not folio:
                # Try to find by other means if the direct relation doesn't work
                record.folio_id = False
            else:
                record.folio_id = folio.id

    @api.depends('reservation_line')
    def _compute_room_count(self):
        """Compute the number of rooms in the reservation"""
        for record in self:
            record.room_count = len(record.reservation_line)
            
    @api.depends('reservation_line.room_number')
    def _compute_room_names(self):
        """Compute a formatted string of room names for display in kanban view"""
        for record in self:
            room_names = []
            for line in record.reservation_line:
                if line.room_number and line.room_number.name:
                    room_names.append(line.room_number.name)
            
            if room_names:
                record.room_names = ", ".join(room_names)
            else:
                record.room_names = "No rooms assigned"

    @api.depends('checkin_date', 'checkout_date')
    def _compute_display_dates(self):
        """Compute date-only versions of the datetime fields for display"""
        for record in self:
            record.checkin_date_display = record.checkin_date.date() if record.checkin_date else False
            record.checkout_date_display = record.checkout_date.date() if record.checkout_date else False
    
    @api.depends('checkin_date', 'checkout_date')
    def _compute_stay_days(self):
        """Compute the number of days of stay"""
        for record in self:
            if record.checkin_date and record.checkout_date:
                # Convert to date objects to ignore time
                checkin = record.checkin_date.date()
                checkout = record.checkout_date.date()
                # Calculate the difference in days
                delta = checkout - checkin
                record.stay_days = delta.days
            else:
                record.stay_days = 0

    @api.depends('reservation_line.room_number.categ_id', 'state')
    def _compute_is_premium(self):
        """Compute whether the room is premium based on its category"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                room = record.reservation_line[0].room_number
                record.is_premium = room and room.categ_id.name.lower().find('premium') >= 0
            else:
                record.is_premium = False
    
    @api.depends('reservation_line.price', 'state')
    def _compute_price(self):
        """Compute the price per night based on the reservation lines"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                record.price = record.reservation_line[0].price
            else:
                record.price = 0.0

    @api.depends('reservation_line.checkin', 'reservation_line.checkout', 'state')
    def _compute_dates(self):
        """Compute check-in and check-out dates from reservation lines"""
        for record in self:
            if record.state in ['confirm', 'done'] and record.reservation_line:
                record.checkin_date = record.reservation_line[0].checkin
                record.checkout_date = record.reservation_line[0].checkout
            else:
                record.checkin_date = False
                record.checkout_date = False

    @api.depends('checkout_date', 'state')
    def _compute_is_late_checkout(self):
        """Compute whether the reservation is past its checkout time"""
        now = fields.Datetime.now()
        for record in self:
            if record.state == 'done' and record.checkout_date:
                record.is_late_checkout = now > record.checkout_date
            else:
                record.is_late_checkout = False

    @api.model
    def _search_is_late_checkout(self, operator, value):
        """
        Search method for is_late_checkout field
        This makes the computed field searchable in views
        """
        now = fields.Datetime.now()
        domain = [
            ('state', '=', 'done'),
            ('reservation_line.checkout', '<', now)
        ]
        reservations = self.search(domain)
        if (operator == '=' and value) or (operator == '!=' and not value):
            return [('id', 'in', reservations.ids)]
        return [('id', 'not in', reservations.ids)]
    
    def action_checkin(self):
        """
        This method is a wrapper around the 'done' method from hotel_management
        It's used to provide a clearer name for the check-in action in our simplified interface
        """
        self.ensure_one()
        if self.state != 'confirm':
            raise UserError(_("Only confirmed reservations can be checked in."))
        return self.done()

    def action_checkout(self):
        """
        Handle the checkout process
        """
        self.ensure_one()
        if self.state != 'done':
            raise UserError(_("Only checked-in reservations can be checked out."))
        # Add your checkout logic here
        return True
        
    def action_view_folio(self):
        """
        Open the folio form view
        """
        self.ensure_one()
        if not self.folio_id:
            raise UserError(_("No folio found for this reservation."))
        
        return {
            'name': _('Folio'),
            'type': 'ir.actions.act_window',
            'res_model': 'hotel.folio',
            'view_mode': 'form',
            'res_id': self.folio_id.id,
            'target': 'current',
        }
    
    def action_add_service(self):
        """
        Open wizard to add a service to the folio
        """
        self.ensure_one()
        if not self.folio_id:
            raise UserError(_("No folio found for this reservation."))
            
        return {
            'name': _('Add Service'),
            'type': 'ir.actions.act_window',
            'res_model': 'hotel.service.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_folio_id': self.folio_id.id,
            },
        }


class HotelCheckin(models.TransientModel):
    """
    This is a transient model that will be used to display a simplified check-in wizard
    It's not actually stored in the database, just used for the UI
    """
    _name = "hotel.checkin.wizard"
    _description = "Hotel Check-in Wizard"
    
    reservation_id = fields.Many2one('hotel.reservation', string='Reservation', required=True,
                                    domain="[('state', '=', 'confirm')]")
    guest_name = fields.Char(related='reservation_id.partner_id.name', string='Guest Name', readonly=True)
    reservation_no = fields.Char(related='reservation_id.reservation_no', string='Reservation No', readonly=True)
    
    def action_checkin(self):
        """
        Process the check-in for the selected reservation
        """
        if not self.reservation_id:
            raise UserError(_("Please select a reservation to check in."))
        
        if self.reservation_id.state != 'confirm':
            raise UserError(_("Only confirmed reservations can be checked in."))
        
        return self.reservation_id.done()


class HotelCheckinTools(models.TransientModel):
    """
    Utility model for hotel check-in administration
    """
    _name = "hotel.checkin.tools"
    _description = "Hotel Check-in Administration Tools"
    
    user_ids = fields.Many2many('res.users', string='Users', 
                               help="Select users to add to the Hotel Check-in User group")
    
    def action_assign_users_to_checkin_group(self):
        """
        Assign selected users to the Hotel Check-in User group
        """
        checkin_group = self.env.ref('hotel_checkin.group_hotel_checkin_user')
        for user in self.user_ids:
            user.write({'groups_id': [(4, checkin_group.id)]})
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s users have been assigned to the Hotel Check-in User group') % len(self.user_ids),
                'sticky': False,
                'type': 'success',
            }
        }


class HotelServiceWizard(models.TransientModel):
    """
    Wizard for adding services to a folio
    """
    _name = 'hotel.service.wizard'
    _description = 'Add Service to Folio'

    folio_id = fields.Many2one('hotel.folio', string='Folio', required=True)
    product_id = fields.Many2one('product.product', string='Product', required=True,
                                domain="[('type', '=', 'service')]")
    product_uom_qty = fields.Float(string='Quantity', default=1.0, required=True)
    product_uom = fields.Many2one('uom.uom', string='Unit of Measure', required=True)
    name = fields.Text(string='Description')
    price_unit = fields.Float(string='Unit Price', required=True)
    discount = fields.Float(string='Discount (%)')
    tax_id = fields.Many2many('account.tax', string='Taxes')

    @api.onchange('product_id')
    def _onchange_product_id(self):
        if self.product_id:
            self.name = self.product_id.display_name
            self.product_uom = self.product_id.uom_id
            self.price_unit = self.product_id.list_price
            self.tax_id = self.product_id.taxes_id

    def action_add_service(self):
        """
        Add the service to the folio's service lines
        """
        self.ensure_one()
        # Create service line
        service_line_vals = {
            'folio_id': self.folio_id.id,
            'order_id': self.folio_id.order_id.id,
            'product_id': self.product_id.id,
            'name': self.name or self.product_id.display_name,
            'product_uom_qty': self.product_uom_qty,
            'product_uom': self.product_uom.id,
            'price_unit': self.price_unit,
            'discount': self.discount,
            'tax_id': [(6, 0, self.tax_id.ids)],
            'order_partner_id': self.folio_id.partner_id.id,
        }
        self.env['hotel_service.line'].create(service_line_vals)
        return {'type': 'ir.actions.act_window_close'} 