# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_extract
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:29+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "%s's Application"
msgstr "Lamaran %s"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Buy credits"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Beli kredit"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Refresh"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Refresh"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Resend For Digitization"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/>\n"
"                        Kirim Ulang untuk Digitalisasi"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"All fields will be automated by Artificial Intelligence, it might take 5 "
"seconds."
msgstr ""
"Semua field akan diotomatiskan oleh Artificial Intelligence, mungkin "
"membutuhkan waktu 5 detik."

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "Terjadi eror"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_applicant
msgid "Applicant"
msgstr "Pelamar"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_ir_attachment
msgid "Attachment"
msgstr "Lampiran"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "CV is being Digitized"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__done
msgid "Completed flow"
msgstr "Alur yang selesai"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Digitalisasi secara otomatis"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Digitalisasi hanya bila diminta"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Jangan digitalisasi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_error_message
msgid "Error message"
msgstr "Pesan Error"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_can_show_resend_button
msgid "Extract Can Show Resend Button"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_can_show_send_button
msgid "Extract Can Show Send Button"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_state
msgid "Extract State"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Generated Applicant"
msgstr ""

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Generated Applicants"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__is_first_stage
msgid "Is First Stage"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__no_extract_requested
msgid "No extract requested"
msgstr "Tidak ada ekstrak yang diminta"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr "Kredit tidak cukup"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_parse_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_ocr_parse
msgid "Recruitment OCR: Parse CV"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_update_ocr_status
msgid "Recruitment OCR: Update All Status"
msgstr "OCR Rekrutmen: Update Semua Status"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_validate_ir_actions_server
#: model:ir.cron,cron_name:hr_recruitment_extract.ir_cron_ocr_validate
msgid "Recruitment OCR: Validate CV"
msgstr "OCR Rekrutmen: Validasi CV"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_config_settings__recruitment_extract_show_ocr_option_selection
msgid "Recruitment processing option"
msgstr "Opsi pemrosesan rekrutmen"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_remote_id
msgid "Request ID to IAP-OCR"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_actions_server_digitize_cv
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "Send For Digitization"
msgstr "Kirim Untuk Digitalisasi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_company__recruitment_extract_show_ocr_option_selection
msgid "Send mode on applicant attachments"
msgstr "Mode kirim pada lampiran pelamar"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr "Server saat ini mengalami maintenance. Mohon coba nanti"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr "Server tidak tersedia. Mohon coba lagi nanti"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__state_processed
msgid "State Processed"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_status_code
msgid "Status code"
msgstr "Status kode"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 10 seconds."
msgstr ""
"Ekstraksi data belum selesai. Ekstraksi biasanya membutuhkan waktu 5 sampai "
"10 detik."

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "The document could not be found"
msgstr "Dokumen tidak dapat ditemukan."

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__to_validate
msgid "To validate"
msgstr "Untuk divalidasi"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "Unsupported image format"
msgstr "Format image tidak didukung"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr "Menunggu ekstraksi"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__extract_not_ready
msgid "Waiting extraction, but not ready"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_upload
msgid "Waiting upload"
msgstr "Menunggu unggahan"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__hr_applicant__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "Menunggu validasi"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "You cannot send a CV for an applicant who's not in first stage!"
msgstr ""
"Anda tidak dapat mengirim CV untuk pelamar yang tidak dalam tahap pertama!"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "You don't have enough credit to extract data from your Resume."
msgstr ""
"Anda tidak memiliki kredit yang mencukupi untuk mengekstrak data dari Resume"
" Anda."
