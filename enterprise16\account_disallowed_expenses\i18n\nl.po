# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses
# 
# Translators:
# <PERSON><PERSON> <gclau<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.disallowed_expenses_main_template
msgid ""
"<span>There are multiple disallowed expenses rates in this period</span>"
msgstr ""
"<span>Er zijn in deze periode meerdere tarieven voor niet toegestane "
"kosten</span>"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_account
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__account_ids
msgid "Account"
msgstr "Rekening"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Active"
msgstr "Actief"

#. module: account_disallowed_expenses
#: model_terms:ir.actions.act_window,help:account_disallowed_expenses.action_account_disallowed_expenses_category_list
msgid "Add a Disallowed Expenses Category"
msgstr "Voeg een niet-toegestane kostencategorie toe"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__category_id
msgid "Category"
msgstr "Categorie"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Category Name"
msgstr "Naam categorie"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__code
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Code"
msgstr "Code"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__company_id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__current_rate
msgid "Current Rate"
msgstr "Percentage"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__rate
msgid "Disallowed %"
msgstr "Niet aftrekbaar %"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_disallowed_amount
msgid "Disallowed Amount"
msgstr "Niet aftrekbaar bedrag"

#. module: account_disallowed_expenses
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_report_de
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_form
msgid "Disallowed Expenses"
msgstr "Verworpen uitgaven"

#. module: account_disallowed_expenses
#: model:ir.actions.act_window,name:account_disallowed_expenses.action_account_disallowed_expenses_category_list
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_disallowed_expenses_category_list
msgid "Disallowed Expenses Categories"
msgstr "Verworpen uitgaven"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_category
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_account__disallowed_expenses_category_id
msgid "Disallowed Expenses Category"
msgstr "Verworpen uitgaven"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_report_handler
msgid "Disallowed Expenses Custom Handler"
msgstr "Aangepaste handler voor niet-toegestane kosten"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_rate
msgid "Disallowed Expenses Rate"
msgstr "Niet aftrekbaar percentage"

#. module: account_disallowed_expenses
#: model:account.report,name:account_disallowed_expenses.disallowed_expenses_report
#: model:ir.actions.client,name:account_disallowed_expenses.action_account_report_de
msgid "Disallowed Expenses Report"
msgstr "Niet-toegestane kostenrapportage"

#. module: account_disallowed_expenses
#: model:ir.model.constraint,message:account_disallowed_expenses.constraint_account_disallowed_expenses_category_unique_code_in_country
msgid "Disallowed expenses category code should be unique in each company."
msgstr "De code verworpen uitgaven moet uniek zijn in elk bedrijf."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__display_name
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "Weergavenaam"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "General Ledger"
msgstr "Grootboek"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__id
msgid "ID"
msgstr "ID"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Journal Items"
msgstr "Boekingsregels"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category____last_update
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "Laatst geupdate op"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__name
msgid "Name"
msgstr "Naam"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#, python-format
msgid "No Rate"
msgstr "Geen percentage"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_rate
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__rate_ids
msgid "Rate"
msgstr "Percentage"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Rates"
msgstr "Percentage"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Related Account(s)"
msgstr "Gerelateerde account(s)"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_tree
msgid "Set Rates"
msgstr "Percentage instellen"

#. module: account_disallowed_expenses
#: model:ir.model.fields,help:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Set active to false to hide the category without removing it."
msgstr ""
"Stel active in op onwaar om de categorie te verbergen zonder deze te "
"verwijderen."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "Startdatum"

#. module: account_disallowed_expenses
#. odoo-python
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Total"
msgstr "Totaal"

#. module: account_disallowed_expenses
#: model:account.report.column,name:account_disallowed_expenses.disallowed_expenses_report_total_amount
msgid "Total Amount"
msgstr "Totaalbedrag"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. 1201"
msgstr "bijv. 1201"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "e.g. Non-Deductible Tax"
msgstr "Bijv. Niet-aftrekbare BTW"
