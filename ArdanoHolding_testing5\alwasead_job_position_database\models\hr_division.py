# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrDivision(models.Model):
    _name = 'hr.division'
    _description = 'Company Division'
    _order = 'sequence, name'

    name = fields.Char(string='Division Name', required=True)
    code = fields.Char(string='Division Code')
    sequence = fields.Integer(string='Sequence', default=10)
    description = fields.Text(string='Description')
    
    # Hierarchy
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    head_id = fields.Many2one(
        'hr.employee',
        string='Division Head',
        help="Head of this division"
    )
    
    # Related fields
    department_ids = fields.One2many(
        'hr.department',
        'division_id',
        string='Departments'
    )
    department_count = fields.Integer(
        string='Number of Departments',
        compute='_compute_department_count'
    )
    employee_count = fields.Integer(
        string='Number of Employees',
        compute='_compute_employee_count'
    )
    job_count = fields.Integer(
        string='Number of Job Positions',
        compute='_compute_job_count'
    )

    active = fields.Boolean(string='Active', default=True)

    _sql_constraints = [
        ('name_company_unique', 'unique(name, company_id)', 'Division name must be unique per company!'),
        ('code_company_unique', 'unique(code, company_id)', 'Division code must be unique per company!')
    ]

    @api.depends('department_ids')
    def _compute_department_count(self):
        for division in self:
            division.department_count = len(division.department_ids)

    @api.depends('department_ids', 'department_ids.member_ids')
    def _compute_employee_count(self):
        for division in self:
            division.employee_count = sum(len(dept.member_ids) for dept in division.department_ids)

    @api.depends('name')
    def _compute_job_count(self):
        for division in self:
            division.job_count = self.env['hr.job'].search_count([
                ('division_id', '=', division.id)
            ])

    def action_view_departments(self):
        """View all departments in this division"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Departments in Division: %s') % self.name,
            'res_model': 'hr.department',
            'view_mode': 'tree,form',
            'domain': [('division_id', '=', self.id)],
            'context': {'default_division_id': self.id}
        }

    def action_view_employees(self):
        """View all employees in this division"""
        employee_ids = []
        for dept in self.department_ids:
            employee_ids.extend(dept.member_ids.ids)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Employees in Division: %s') % self.name,
            'res_model': 'hr.employee',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', employee_ids)],
        }

    def action_view_jobs(self):
        """View all job positions in this division"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Job Positions in Division: %s') % self.name,
            'res_model': 'hr.job',
            'view_mode': 'tree,form',
            'domain': [('division_id', '=', self.id)],
            'context': {'default_division_id': self.id}
        }


class HrDepartment(models.Model):
    _inherit = 'hr.department'

    # Add division field to existing department model
    division_id = fields.Many2one(
        'hr.division',
        string='Division',
        help="Division this department belongs to"
    )

    # Enhance existing fields
    head_id = fields.Many2one(
        'hr.employee',
        string='Department Head',
        help="Head of this department"
    )


class HrUnit(models.Model):
    _name = 'hr.unit'
    _description = 'HR Unit/Team'
    _order = 'sequence, name'

    name = fields.Char(string='Unit/Team Name', required=True)
    code = fields.Char(string='Unit Code')
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    description = fields.Text(string='Description')

    department_id = fields.Many2one('hr.department', string='Department', required=True)
    division_id = fields.Many2one(
        'hr.division',
        string='Division',
        related='department_id.division_id',
        store=True,
        readonly=True
    )
    head_id = fields.Many2one('hr.employee', string='Unit Head')

    # Related fields
    employee_ids = fields.One2many('hr.employee', 'unit_id', string='Employees')
    employee_count = fields.Integer(string='Employee Count', compute='_compute_employee_count')
    job_ids = fields.One2many('hr.job', 'unit_id', string='Job Positions')
    job_count = fields.Integer(string='Job Count', compute='_compute_job_count')

    @api.depends('employee_ids')
    def _compute_employee_count(self):
        for unit in self:
            unit.employee_count = len(unit.employee_ids)

    @api.depends('job_ids')
    def _compute_job_count(self):
        for unit in self:
            unit.job_count = len(unit.job_ids)

    def action_view_employees(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Employees'),
            'res_model': 'hr.employee',
            'view_mode': 'tree,form',
            'domain': [('unit_id', '=', self.id)],
            'context': {'default_unit_id': self.id}
        }

    def action_view_jobs(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Job Positions'),
            'res_model': 'hr.job',
            'view_mode': 'tree,form',
            'domain': [('unit_id', '=', self.id)],
            'context': {'default_unit_id': self.id}
        }

    @api.onchange('division_id')
    def _onchange_division_id(self):
        """Update company when division changes"""
        if self.division_id:
            self.company_id = self.division_id.company_id


class HrUnit(models.Model):
    _inherit = 'hr.unit'

    # Enhance existing unit model
    head_id = fields.Many2one(
        'hr.employee',
        string='Unit Head',
        help="Head of this unit"
    )
    division_id = fields.Many2one(
        'hr.division',
        string='Division',
        related='department_id.division_id',
        store=True,
        readonly=True
    )
    
    # Add employee tracking
    employee_ids = fields.One2many(
        'hr.employee',
        'unit_id',
        string='Employees'
    )
    employee_count = fields.Integer(
        string='Number of Employees',
        compute='_compute_employee_count'
    )

    @api.depends('employee_ids')
    def _compute_employee_count(self):
        for unit in self:
            unit.employee_count = len(unit.employee_ids)
