# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_appraisal
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2019
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:07+0000\n"
"PO-Revision-Date: 2016-08-05 13:29+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_user_input_appraisal
msgid ""
"${object.appraisal_id.employee_id.name} Appraisal: ${object.survey_id.title}"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:175
#, python-format
msgid "%s appraisal: %s"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:128
#, python-format
msgid ""
"<a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">Meeting</a> for <a "
"href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s's</a> appraisal"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_user_input_appraisal
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name or 'participant'}<br/><br/>\n"
"        % if object.appraisal_id.employee_id.name:\n"
"            Please fill out the following survey related to ${object.appraisal_id.employee_id.name}'s appraisal.\n"
"        % else:\n"
"            Please fill out the following appraisal survey.\n"
"        % endif\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"${('%s?answer_token=%s' % (object.survey_id.public_url, object.token)) | safe}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Start Survey\n"
"            </a>\n"
"        </div>\n"
"        % if object.deadline\n"
"            Please answer the appraisal for ${format_date(object.deadline)}.<br/><br/>\n"
"        % endif\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                        Appraisal\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<strong><span>Final Interview: </span></strong>"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_1
msgid "Ability to cope with multidisciplinarity of team"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_6
msgid "Ability to follow and complete work as instructed"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_2_5
msgid "Ability to manage planning resources, risks, budgets and deadlines"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__action_plan
msgid "Action Plan"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_6_1
msgid ""
"Actions by Executive management show genuine interest in the well-being of "
"employees"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.mail_activity_type_action_config_hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_config_activity_type
msgid "Activity Types"
msgstr "Τύποι Δραστηριότητας"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_13
msgid ""
"Adaptability: Ability to adapt oneself to organizational changes while "
"keeping efficiency"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_2_3
#: model:survey.question,title:hr_appraisal.appraisal_2_3
msgid "Additional Comments"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_7
#: model:survey.question,title:hr_appraisal.opinion_2_7
msgid "Additional comments"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
msgid "An appraisal will be automatically created."
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_11
msgid "Analytical and synthetic mind"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__survey_completed_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Answers"
msgstr "Απαντήσεις"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.model.fields,field_description:hr_appraisal.field_survey_invite__appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_survey_user_input__appraisal_id
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.survey_user_input_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
#: selection:survey.survey,category:0
msgid "Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_collaborators_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_collaborators_ids
msgid "Appraisal Collaborators"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_colleagues_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_colleagues_ids
msgid "Appraisal Colleagues"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
msgid "Appraisal Deadline"
msgstr ""

#. module: hr_appraisal
#: model:mail.activity.type,name:hr_appraisal.mail_act_appraisal_form
msgid "Appraisal Form to Fill"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Appraisal Form..."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_manager_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_manager_ids
msgid "Appraisal Manager"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:119
#, python-format
msgid "Appraisal Meeting For %s"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_1
#: model:survey.question,title:hr_appraisal.opinion_1
msgid "Appraisal Process"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_request
msgid "Appraisal Request by Employee"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
msgid "Appraisal Scheduling"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:265
#: selection:hr.appraisal,state:0 selection:hr.appraisal.report,state:0
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#, python-format
msgid "Appraisal Sent"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__survey_template_id
msgid "Appraisal Survey Invite Email"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_appraisal
msgid "Appraisal by Manager"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_1_3
#: model:survey.question,title:hr_appraisal.appraisal_1_3
msgid "Appraisal for Period"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:196
#, python-format
msgid "Appraisal form(s) have been sent"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
msgid "Appraisal(s)"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
#: model:ir.cron,cron_name:hr_appraisal.ir_cron_scheduler_appraisal
#: model:ir.cron,name:hr_appraisal.ir_cron_scheduler_appraisal
msgid "Appraisal: run employee appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_count
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
msgid "Appraisals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_1_5
#: model:survey.question,title:hr_appraisal.appraisal_1_5
msgid "Appraiser"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid "At the conclusion of the appraisal time period"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid "At the outset of the appraisal time period"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid "At the supervisor's appraisal date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_self
msgid "By Employee"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:265
#: selection:hr.appraisal,state:0 selection:hr.appraisal.report,state:0
#, python-format
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_survey_survey__category
msgid "Category"
msgstr "Κατηγορία"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_survey_survey__category
msgid ""
"Category is used to know in which context the survey is used. Various apps "
"may define their own categories when they use survey like jobs recruitment "
"or employee appraisal surveys."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__collaborators_appraisal
msgid "Collaborator"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__collaborators_survey_id
msgid "Collaborator's Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__collaborators_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_by_collaborators
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_by_collaborators
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
msgid "Collaborators"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__colleagues_survey_id
msgid "Colleague's Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__colleagues_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_by_colleagues
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_by_colleagues
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Colleagues"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__colleagues_appraisal
msgid "Colleagues Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_9
msgid ""
"Communication skills (written & verbally): clearness, concision, exactitude"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_6_3
msgid ""
"Compared to similar jobs in other companies where I could work, my total "
"compensation..."
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_3
msgid ""
"Compliance to internal rules and processes (timesheets completion, etc.)"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_4
#: model:survey.question,title:hr_appraisal.opinion_2_4
msgid "Continuous Improvement"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid "Create a new appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_14
msgid "Creativity and forward looking aptitude"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"Critical or key elements of performance and professional development needs "
"(if any), should also be noted at this time"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_8
msgid "Customer commitment"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#: model:survey.question,question:hr_appraisal.opinion_1_2
#: model:survey.question,title:hr_appraisal.opinion_1_2
msgid "Date"
msgstr "Ημερομηνία"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_1_4
#: model:survey.question,title:hr_appraisal.appraisal_1_4
msgid "Date of review"
msgstr "Ημερομηνία της Αναθεώρησης"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
msgid "Deadline"
msgstr "Προθεσμία"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Deadline:"
msgstr "Προθεσμία:"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_7
msgid "Decision making"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_2_2
msgid "Delegation: Ability to efficiently assign tasks to other people"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Delete"
msgstr "Διαγραφή"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_2_1
msgid "Demonstrates genuine concern for me as a person"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
msgid "Department"
msgstr "Τμήμα"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid "Did not meet standards and expectations"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:265
#: selection:hr.appraisal,state:0 selection:hr.appraisal.report,state:0
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#, python-format
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Dropdown menu"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Edit"
msgstr "Επεξεργασία"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_3
#: model:survey.question,title:hr_appraisal.opinion_2_3
msgid "Effectiveness"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_self
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee"
msgstr "Υπάλληλος"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_appraisal
msgid "Employee Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_1
#: model:survey.question,title:hr_appraisal.appraisal_1
#: model:survey.survey,title:hr_appraisal.appraisal_form
msgid "Employee Appraisal Form"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_5
#: model:survey.question,title:hr_appraisal.appraisal_5
msgid "Employee Comments"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_employee
msgid "Employee Name"
msgstr ""

#. module: hr_appraisal
#: model:survey.survey,title:hr_appraisal.opinion_form
msgid "Employee Opinion Form"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_3
#: model:survey.question,title:hr_appraisal.appraisal_3
msgid "Employee Performance in Key Areas"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_colleagues_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_colleagues_survey_id
msgid "Employee's Appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Name"
msgstr "Όνομα Εργαζόμενου"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_1
#: model:survey.question,title:hr_appraisal.opinion_2_1
msgid "Engagement"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_2
msgid "Enthusiasm & implication toward projects/assignments"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2
#: model:survey.question,title:hr_appraisal.opinion_2
msgid "Evaluation"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Event"
msgstr "Συμβάν"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid "Exceeds standards and expectations"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "Εκτεταμένα Φίλτρα..."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:192
#, python-format
msgid ""
"Fill form <a href=\"%s\">%s</a> for <a href=\"#\" data-oe-model=\"%s\" data-"
"oe-id=\"%s\">%s's</a> appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Final Evaluation"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: hr_appraisal
#: selection:survey.survey,category:0
msgid "Generic Survey"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "Ομαδοποίηση κατά..."

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid ""
"His direct reports will be invited through Odoo to express a feedback on "
"their supervisor's leadership and to give their opinion about their own "
"engagement and effectiveness, the continuous improvement and openness in "
"action in thecompany..."
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_1_1
msgid "I am proud to tell others I work here"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_1_5
msgid ""
"I am willing to put in a great deal of effort beyond what is expected to "
"help my workgroup succeed"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_2_4
msgid "I believe the information that I get from the person I report to."
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_2_2
msgid ""
"I consistently acquire new knowledge, skills or understanding through "
"contact with my supervisor. He helps me growing my compete"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_3_2
msgid "I have enough work"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_6_2
msgid ""
"I have the same opportunity to succeed as others with similar experiences, "
"performance and educational background"
msgstr ""
"Έχω την ίδια ευκαιρία να επιτύχω όπως και άλλοι με παρόμοιες εμπειρίες, "
"επιδόσεις και εκπαιδευτικό υπόβαθρο"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_5_1
msgid "I know the company's values and live them"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_3_3
msgid ""
"I mostly work on value-added tasks for the company, the products or the "
"services"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_6_4
msgid "I understand the company strategy and how my workgroup supports it"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_1_3
msgid ""
"I would prefer to remain with this company even if a comparable job were "
"available in another company"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_3_1
msgid "I'm efficient at work and my achievements are successful"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
msgid "ID"
msgstr "Κωδικός"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_4
msgid ""
"Identify professional, performance, or project objectives you recommend for employee’s continued career development\n"
"over the coming year."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_appraisal
#: model:survey.question,comments_message:hr_appraisal.appraisal_1
#: model:survey.question,comments_message:hr_appraisal.appraisal_1_1
#: model:survey.question,comments_message:hr_appraisal.appraisal_1_2
#: model:survey.question,comments_message:hr_appraisal.appraisal_1_3
#: model:survey.question,comments_message:hr_appraisal.appraisal_1_4
#: model:survey.question,comments_message:hr_appraisal.appraisal_1_5
#: model:survey.question,comments_message:hr_appraisal.appraisal_2
#: model:survey.question,comments_message:hr_appraisal.appraisal_2_1
#: model:survey.question,comments_message:hr_appraisal.appraisal_2_2
#: model:survey.question,comments_message:hr_appraisal.appraisal_2_3
#: model:survey.question,comments_message:hr_appraisal.appraisal_3
#: model:survey.question,comments_message:hr_appraisal.appraisal_3_1
#: model:survey.question,comments_message:hr_appraisal.appraisal_3_2
#: model:survey.question,comments_message:hr_appraisal.appraisal_4
#: model:survey.question,comments_message:hr_appraisal.appraisal_4_1
#: model:survey.question,comments_message:hr_appraisal.appraisal_4_2
#: model:survey.question,comments_message:hr_appraisal.appraisal_4_3
#: model:survey.question,comments_message:hr_appraisal.appraisal_5
#: model:survey.question,comments_message:hr_appraisal.appraisal_5_1
#: model:survey.question,comments_message:hr_appraisal.opinion_1
#: model:survey.question,comments_message:hr_appraisal.opinion_1_1
#: model:survey.question,comments_message:hr_appraisal.opinion_1_2
#: model:survey.question,comments_message:hr_appraisal.opinion_2
#: model:survey.question,comments_message:hr_appraisal.opinion_2_1
#: model:survey.question,comments_message:hr_appraisal.opinion_2_2
#: model:survey.question,comments_message:hr_appraisal.opinion_2_3
#: model:survey.question,comments_message:hr_appraisal.opinion_2_4
#: model:survey.question,comments_message:hr_appraisal.opinion_2_5
#: model:survey.question,comments_message:hr_appraisal.opinion_2_6
#: model:survey.question,comments_message:hr_appraisal.opinion_2_7
msgid "If other, please specify:"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__action_plan
msgid ""
"If the evaluation does not meet the expectations, you can propose an action "
"plan"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_5
msgid "Initiative and self autonomy"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Invitations"
msgstr "Προσκλήσεις"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"It is the joint responsibility of the employee and the supervisor (appraiser) to establish a feasible work plan for the\n"
"coming year, including major employee responsibilities and corresponding benchmarks against which results will be\n"
"evaluated."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"It is the primary responsibility of the supervisor to gather the necessary input from the appropriate sources of feedback\n"
"(internal and/or external customers, peers). In case of collaboration with Odoo SA Belgium, the supervisor must\n"
"receive completed evaluation form from the employee's Belgian project manager."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_2
#: model:survey.question,title:hr_appraisal.opinion_2_2
msgid "Leadership"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_2_3
msgid ""
"Leadership: create a challenging and motivating work environment aligned "
"with the company's strategy"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_2_4
msgid "Leadership: sustain subordinates in their professional growth"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_2_3
msgid ""
"Listens and takes into account all ideas and do his best to put in place the"
" best of these"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Manager"
msgstr "Διευθυντής"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_manager_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_manager_survey_id
msgid "Manager's Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_by_manager
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_by_manager
msgid "Managers"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_max_period
msgid "Maximum number of months between appraisals"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid "Meet standards and expectations"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_id
msgid "Meeting"
msgstr "Συνάντηση"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_min_period
msgid "Minimum number of months between appraisals"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_6
#: model:survey.question,title:hr_appraisal.opinion_2_6
msgid "Miscellaneous"
msgstr "Διάφορα"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
msgid "Months"
msgstr "Μήνες"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Activities"
msgstr "Οι Δραστηριότητες μου"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_5_2
msgid ""
"My best achievements have been communicated to the community, internally or "
"to customers"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_1_2
msgid "My job provides me with a sense of personal accomplishment"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_4_1
msgid ""
"My work contributes towards the continuous improvement of the company, our "
"services or products"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
#: model:survey.question,question:hr_appraisal.appraisal_1_1
#: model:survey.question,title:hr_appraisal.appraisal_1_1
msgid "Name"
msgstr "Περιγραφή"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_1_1
#: model:survey.question,title:hr_appraisal.opinion_1_1
msgid "Name of your direct supervisor"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_date
msgid "Next Appraisal Date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__count_completed_survey
msgid "Number of Answers"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__count_sent_survey
msgid "Number of Sent Forms"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_2_1
#: model:survey.question,title:hr_appraisal.appraisal_2_1
msgid "Objectives"
msgstr "Στόχοι"

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "Officer"
msgstr "Προιστάμενος"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.opinion_2_5
#: model:survey.question,title:hr_appraisal.opinion_2_5
msgid "Openness"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_4_4
msgid ""
"Our workgroup identifies and reduces waste of time in our activities and "
"processes"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid "Overall Purpose Of Employee Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_4_3
msgid ""
"Overall, I believe the quality of products and/or services my workgroup "
"delivers is improving"
msgstr ""

#. module: hr_appraisal
#: selection:hr.appraisal,activity_state:0
msgid "Overdue"
msgstr "Εκπρόθεσμο"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__periodic_appraisal_created
msgid "Periodic Appraisal has been created"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_4_2
#: model:survey.question,title:hr_appraisal.appraisal_4_2
msgid "Personal Performance Objectives"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Plan"
msgstr "Σχεδίασε"

#. module: hr_appraisal
#: selection:hr.appraisal,activity_state:0
msgid "Planned"
msgstr "Προγραμματισμένη"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:176
#, python-format
msgid "Please fill out appraisal survey."
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_1_2
#: model:survey.question,title:hr_appraisal.appraisal_1_2
msgid "Position Title"
msgstr "Τίτλος Θέσης"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_4_1
#: model:survey.question,title:hr_appraisal.appraisal_4_1
msgid "Professional Development Objectives"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_4
#: model:survey.question,title:hr_appraisal.appraisal_4
msgid "Professional Development and Performance Plan"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_4_3
#: model:survey.question,title:hr_appraisal.appraisal_4_3
msgid "Project Objectives"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_12
msgid "Promptness and attendance record"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "Σχετικός Συνεργάτης"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "Αναφορές"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_2_2
#: model:survey.question,title:hr_appraisal.appraisal_2_2
msgid "Results"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_2_1
msgid ""
"Results of the bottom-up survey and mitigation actions to face technical, "
"organizational, structural and/or relational issues"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Schedule The Final Interview"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "Αναζήτηση Βαθμολόγησης"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Select Appraisal Reviewer..."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_self_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_self_survey_id
msgid "Self Appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Send Appraisal Form To"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_form_request
msgid "Send Appraisal to"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
msgid "Send Appraisals Form To"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__survey_sent_ids
msgid "Sent Forms"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_form
msgid "Set the minimum number of months between appraisal."
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid "Significantly below standards and expectations"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid ""
"Significantly exceeds standards and expectations required of the position"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Start Appraisal and Send Forms"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "Κατάσταση"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_3_1
#: model:survey.question,title:hr_appraisal.appraisal_3_1
msgid "Subject"
msgstr "Θέμα"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_3_2
#: model:survey.question,title:hr_appraisal.appraisal_3_2
msgid "Supervisors only"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_survey_survey
msgid "Survey"
msgstr "Έρευνα"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_1_4
msgid ""
"Taking everything into account, how satisfied are you with your current job?"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_4
msgid ""
"Team spirit: ability to work efficiently with peers, manage the conflicts "
"with diplomacy"
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_10
msgid "Technical skills regarding to the job requirements"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1_1
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1_2
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1_3
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1_4
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_1_5
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_2
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_2_1
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_2_2
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_2_3
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_3
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_3_1
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_3_2
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_4
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_4_1
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_4_2
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_4_3
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_5
#: model:survey.question,validation_error_msg:hr_appraisal.appraisal_5_1
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_1
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_1_1
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_1_2
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_1
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_2
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_3
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_4
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_5
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_6
#: model:survey.question,validation_error_msg:hr_appraisal.opinion_2_7
msgid "The answer you entered is not valid."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid ""
"The appraiser should rate the employee's major work accomplishments and "
"performance according to the metric provided below"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_3
msgid ""
"The appraiser should rate the employee’s major work accomplishments and performance according to the metric provided\n"
"below:"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_employee.py:73
#, python-format
msgid "The date of the next appraisal cannot be in the past"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"The employee may choose to offer comments or explanation regarding the "
"completed review."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"The employee will be responsible for completing a draft of the Appraisal Form as a tool for self-appraisal and a starting\n"
"point for the supervisor’s evaluation. The employee can add examples of achievements for each criterion.\n"
"Once the form had been filled, the employee send it to his supervisor."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.opinion_1
msgid ""
"The employees will send back their anonymous answers to Odoo. The data will "
"be handled by the HR manager and a brief summary of the data will be sent to"
" the concerned supervisor, to his team and to the supervisor's supervisor."
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"The supervisor send the form to the HR department in India and in Belgium"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"The supervisor synthesizes and integrates all input into the completed appraisal. The motivation of the evaluation\n"
"is explained in the ad hoc fields."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__color
msgid "This color will be used in the kanban view."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__collaborators_appraisal
msgid "This employee will be appraised by his collaborators"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__colleagues_appraisal
msgid "This employee will be appraised by his colleagues"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__manager_appraisal
msgid "This employee will be appraised by his managers"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__employee_appraisal
msgid "This employee will do a self-appraisal"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1_1
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1_2
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1_3
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1_4
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_1_5
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_2
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_2_1
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_2_2
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_2_3
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_3
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_3_1
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_3_2
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_4
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_4_1
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_4_2
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_4_3
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_5
#: model:survey.question,constr_error_msg:hr_appraisal.appraisal_5_1
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_1
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_1_1
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_1_2
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_1
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_2
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_3
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_4
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_5
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_6
#: model:survey.question,constr_error_msg:hr_appraisal.opinion_2_7
msgid "This question requires an answer."
msgstr ""

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.arow_3_1_15
msgid "Time management: projects/tasks are completed on time"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:265
#: selection:hr.appraisal,state:0 selection:hr.appraisal.report,state:0
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#, python-format
msgid "To Start"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid ""
"To assist employees in their professional growth, through the identification of strengths and opportunities for\n"
"development"
msgstr ""

#. module: hr_appraisal
#: model_terms:survey.question,description:hr_appraisal.appraisal_1
msgid "To initiate a clear and open communication of performance expectations"
msgstr ""

#. module: hr_appraisal
#: selection:hr.appraisal,activity_state:0
msgid "Today"
msgstr "Σήμερα"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_5_1
#: model:survey.question,title:hr_appraisal.appraisal_5_1
msgid ""
"Use the following space to make any comments regarding the above performance"
" evaluation."
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
msgid "Users"
msgstr "Χρήστες"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: hr_appraisal
#: model:survey.label,value:hr_appraisal.oprow_2_4_2
msgid ""
"What I did several months ago is still of use to the company, the services "
"or the products today"
msgstr ""

#. module: hr_appraisal
#: model:survey.question,question:hr_appraisal.appraisal_2
#: model:survey.question,title:hr_appraisal.appraisal_2
msgid "Work Plan"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:258
#, python-format
msgid "You cannot delete appraisal which is in %s state"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid ""
"You will be able to choose which forms will be sent (Employee,Managers,Collaborators or Colleagues),\n"
"            to whom and the evaluation deadline. Once you have defined it,\n"
"            change the stage to send it and view the results."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:220
#, python-format
msgid ""
"Your last appraisal was on %s. You will be able to request a new appraisal "
"on %s. If you think it's too late, feel free to have a chat with your "
"manager."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_collaborators_survey_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__appraisal_collaborators_survey_id
msgid "collaborate's Appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"
