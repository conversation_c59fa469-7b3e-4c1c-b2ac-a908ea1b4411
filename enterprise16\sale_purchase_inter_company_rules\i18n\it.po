# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_inter_company_rules
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-07 10:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_generated
msgid "Auto Generated Purchase Order"
msgstr "Ordine di acquisto autogenerato"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_generated
msgid "Auto Generated Sales Order"
msgstr "Ordine di vendita autogenerato"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__auto_validation
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__auto_validation
msgid "Automatic Validation"
msgstr "Convalida automatica"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr "Generato automaticamente da %(origin)s di azienda %(company)s."

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""
"Configurare il magazzino corretto per l'azienda (%s) dal menù: "
"Impostazioni/Utenti/Aziende"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__warehouse_id
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""
"Valore predefinito da impostare negli ordini di acquisto (vendita), i quali "
"vengono creati in base agli ordini di vendita (acquisto) effettuati per "
"questa azienda"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                using "
"warehouse %(warehouse)s when a company confirms a %(event_type)s for "
"%(company)s."
msgstr ""
"Genera un %(generated_object)s %(validation)s,                 utilizzando "
"il magazzino %(warehouse)s, quando un'azienda conferma un %(event_type)s per"
" %(company)s."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a %(validation)s %(generated_object)s                when a company"
" confirms a %(event_type)s for %(company)s."
msgstr ""
"Genera un %(generated_object)s %(validation)s                quando "
"un'azienda conferma un %(event_type)s per %(company)s."

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"Inter company user of company %(name)s doesn't have enough access rights"
msgstr ""
"Diritti di accesso non sufficienti per l'utente interaziendale dell'azienda "
"%(name)s"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""
"Diritti di accesso non sufficienti per l'utente interaziendale dell'azienda "
"%s"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid "Provide at least one user for inter company relation for %(name)s"
msgstr ""
"Fornisci almeno un utente per le relazioni inter-aziendali per %(name)s"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/sale_order.py:0
#, python-format
msgid "Provide one user for intercompany relation for %(name)s "
msgstr "Fornisci un utente per la relazione interaziendale per %(name)s"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Ordine di acquisto"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Rule"
msgstr "Regola"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: sale_purchase_inter_company_rules
#: model:ir.model,name:sale_purchase_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_inter_company_rules.field_res_company__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""
"Selezione della tipologia per l'impostazione delle regole interaziendali "
"nell'azienda selezionata."

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_sale_order__auto_purchase_order_id
msgid "Source Purchase Order"
msgstr "Ordine di acquisto alla fonte"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_purchase_order__auto_sale_order_id
msgid "Source Sales Order"
msgstr "Ordine di vendita alla fonte"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__purchase
msgid "Synchronize Purchase Order"
msgstr "Sincronizzare ordine di acquisto"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale
msgid "Synchronize Sales Order"
msgstr "Sincronizzare ordine di vendita"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields.selection,name:sale_purchase_inter_company_rules.selection__res_company__rule_type__sale_purchase
msgid "Synchronize Sales and Purchase Order"
msgstr "Sincronizzare ordine di acquisto e vendita"

#. module: sale_purchase_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr "Utilizzare magazzino"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_company__warehouse_id
msgid "Warehouse"
msgstr "Magazzino"

#. module: sale_purchase_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_inter_company_rules.field_res_config_settings__warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "Magazzino per ordini di acquisto"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/purchase_order.py:0
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different than purchase price list currency.\n"
"The currency of the SO is obtained from the pricelist of the company partner.\n"
"\n"
"(SO currency: %(so_currency)s, Pricelist: %(pricelist)s, Partner: %(partner)s (ID: %(id)s))"
msgstr ""
"Non puoi creare ordini di vendita a partire da ordini di acquisto perché la valuta del listino prezzi di vendita è diversa rispetto a quella del listino prezzi di acquisto.\n"
"La valuta dell'ordine di vendita è ottenuta dal listino prezzi dell'azienda del partner.\n"
"\n"
"(Valuta ordine di vendita: %(so_currency)s, listino prezzi: %(pricelist)s, partner: %(partner)s (ID: %(id)s))"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "draft"
msgstr "in bozza"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase order"
msgstr "ordine di acquisto"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "purchase/sales order"
msgstr "ordine di acquisto/vendita"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales order"
msgstr "ordine di vendita"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "sales/purchase order"
msgstr "ordine di vendita/acquisto"

#. module: sale_purchase_inter_company_rules
#: code:addons/sale_purchase_inter_company_rules/models/res_company.py:0
#, python-format
msgid "validated"
msgstr "convalidato"
