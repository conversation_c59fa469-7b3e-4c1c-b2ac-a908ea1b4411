# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_referral
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <miu<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>i <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ka<PERSON>.<PERSON><PERSON><PERSON>@emsystems.fi>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>ika <PERSON>ssi <<EMAIL>>, 2022
# Martin Trigaux, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid ""
" You've gained %(gained)s points with this progress.<br/>It makes you a new "
"total of %(total)s points. Visit <a href=\"%(url)s\">this link</a> to pick a"
" gift!"
msgstr ""
" Olet kerännyt %(gained)s pistettä tällä edistyksellä.<br/>Se tekee sinulle "
"yhteensä %(total)s pistettä. Käy <a href=\"%(url)s\">tästä linkistä</a> "
"valitsemassa lahja!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__referral_hired
msgid "# Hired by Referral"
msgstr "# Palkattu suosittelemalla"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<b>Responsible: </b>"
msgstr "<b>Vastuullinen: </b>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-check me-2\" title=\"Hired\"/>"
msgstr "<i class=\"fa fa-check me-2\" title=\"Palkattu\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-square-o\" title=\"Open\"/>"
msgstr "<i class=\"fa fa-square-o\" title=\"Avoin\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-danger fa fa-times\" title=\"Closed\"/>"
msgstr "<i class=\"text-danger fa fa-times\" title=\"Suljettu\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-success fa fa-check\" title=\"Done\"/>"
msgstr "<i class=\"text-success fa fa-check\" title=\"Valmis\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "<span class=\"ml8\">Points</span>"
msgstr "<span class=\"ml8\">Pisteet</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Awarded\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Myönnetty\n"
"                                </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"<span title=\"Buy\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Buy\"/>Buy</span>"
msgstr ""
"<span title=\"Buy\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Buy\"/>Osta</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "<span title=\"More info\">More info</span>"
msgstr "<span title=\"More info\">Lisätietoja</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"My Link to Share\"><i class=\"fa fa-lg fa-link text-"
"secondary\" role=\"img\" aria-label=\"My Link to Share\"/></span>"
msgstr ""
"<span title=\"My Link to Share\"><i class=\"fa fa-lg fa-link text-"
"secondary\" role=\"img\" aria-label=\"Oma linkkini jaettavaksi\"/></span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "<span title=\"Send by Mail\">Refer Friend</span>"
msgstr "<span title=\"Lähetä sähköpostilla\">Suosittele ystävää</span>"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
#, python-format
msgid ""
"A new alert has been added to the Referrals app! Check your <a "
"href=%(url)s>dashboard</a> now!"
msgstr ""
"Referrals-sovellukseen on lisätty uusi hälytys! Tarkista <a "
"href=%(url)s>dashboard</a>!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__active
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__active
msgid "Active"
msgstr "Aktiivinen"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_ids
msgid "Activities"
msgstr "Toimenpiteet"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__name
msgid "Alert"
msgstr "Varoitus"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert
msgid "Alert in Referral App"
msgstr "Hälytys suosittelusovelluksessa"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_alert_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_alert_configuration
msgid "Alerts"
msgstr "Hälytykset"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__points
msgid ""
"Amount of points that the referent will receive when the applicant will "
"reach this stage"
msgstr ""
"Pistemäärä, jonka suosittelija saa, kun hakija saavuttaa tämän vaiheen"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_applicant
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__applicant_id
msgid "Applicant"
msgstr "Hakija"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Applicant must have a company."
msgstr "Hakijalla on oltava yritys."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Archived"
msgstr "Arkistoitu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "Avatar"
msgstr "Avatar"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__awarded_employees
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#, python-format
msgid "Awarded Employees"
msgstr "Palkitut työntekijät"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__back
msgid "Back"
msgstr "Takaisin"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background"
msgstr "Tausta"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background Image"
msgstr "Taustakuva"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__body_html
msgid "Body"
msgstr "Viesti"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__utm_campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__channel
msgid "Channel"
msgstr "Kanava"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Choose an avatar for your new friend!"
msgstr "Valitse avatar uudelle ystävällesi!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Click to level up!"
msgstr "Klikkaa kasvattaaksesi tasoasi!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Close"
msgstr "Sulje"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__company_id
msgid "Company"
msgstr "Yritys"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_configuration
msgid "Configuration"
msgstr "Asetukset"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__cost
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Cost"
msgstr "Kustannushinta"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_friend_configuration
msgid "Create a new friend"
msgstr "Luo uusi ystävä"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_level_configuration
msgid "Create a new level"
msgstr "Luo uusi taso"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid "Create new alerts"
msgstr "Luo uusia hälytyksiä"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_points
msgid "Create new reward points"
msgstr "Luo uusia palkintopisteitä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_date
msgid "Created on"
msgstr "Luotu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: model:ir.actions.client,name:hr_referral.action_hr_referral_welcome_screen
#: model:ir.ui.menu,name:hr_referral.menu_hr_applicant_employee_referral_dashboard
#, python-format
msgid "Dashboard"
msgstr "Työpöytä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image
msgid "Dashboard Image"
msgstr "Kojelaudan kuva"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Date"
msgstr "Päivämäärä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_from
msgid "Date From"
msgstr "Alkupäivä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_to
msgid "Date To"
msgstr "Päättymispäivä"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__position
msgid ""
"Define the position of the friend. If it's a small friend like a dog, you "
"must select Front, it will be placed in the front of the dashboard, above "
"superhero."
msgstr ""
"Määritä ystävän sijainti. Jos kyseessä on pieni ystävä, kuten koira, sinun "
"on valittava Front, jolloin se sijoitetaan kojelaudan etuosaan, "
"supersankarin yläpuolelle."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Department"
msgstr "Osasto"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__description
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Description"
msgstr "Kuvaus"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__direct_clicks
msgid "Direct Clicks"
msgstr "Suorat klikkaukset"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/report/hr_referral_report.py:0
#, python-format
msgid "Direct Referral"
msgstr "Suora lähete"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__dismissed_user_ids
msgid "Dismissed User"
msgstr "Poistettu käyttäjä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid "Do not have access"
msgstr "Ei pääsyä"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"Do you want to confirm this reward? After confirmation an HR will contact "
"you."
msgstr ""
"Haluatko vahvistaa tämän palkkion? Vahvistuksen jälkeen HR ottaa sinuun "
"yhteyttä."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__earned_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__earned_points
msgid "Earned Points"
msgstr "Ansaitut pisteet"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__email_to
msgid "Email"
msgstr "Sähköposti"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Email a friend"
msgstr "Lähetä sähköpostia ystävälle"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Employee"
msgstr "Työntekijä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_hired
msgid "Employee Referral Hired"
msgstr "Työntekijän suositus Palkattu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_refused
msgid "Employee Referral Refused"
msgstr "Työntekijän suositus Hylätty"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_report
msgid "Employee Referral Report"
msgstr "Työntekijöiden suositteluraportti"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Employees"
msgstr "Työntekijät"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Employees Analysis"
msgstr "Työntekijöiden analyysi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.employee_referral_report_action
msgid "Employees Referral Analysis"
msgstr "Työntekijöiden suositusanalyysi"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_alert__url
msgid ""
"External links must start with 'http://www.'. For an internal url, you don't"
" need to put domain name, you can just insert the path."
msgstr ""
"Ulkoisten linkkien on alettava kirjaimella \"http://www.\". Sisäistä URL-"
"osoitetta varten sinun ei tarvitse laittaa verkkotunnusta, vaan voit vain "
"lisätä polun."

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__facebook
msgid "Facebook"
msgstr "Facebook"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__facebook_clicks
msgid "Facebook Clicks"
msgstr "Facebook-klikkaukset"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__friend_id
msgid "Friend"
msgstr "Kaveri"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__name
msgid "Friend Name"
msgstr "Ystävän nimi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_friend_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_friend_configuration
msgid "Friends"
msgstr "Kaverit"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_friend
msgid "Friends for Referrals"
msgstr "Ystävät suosituksia varten"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__front
msgid "Front"
msgstr "Etuosa"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Gather your team"
msgstr "Kokoa tiimisi"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Gather your team!"
msgstr "Kokoa tiimisi!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "Gift Responsible"
msgstr "Lahjasta vastuussa"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Gifts"
msgstr "Lahja"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__all_jobs
msgid "Go to All Jobs"
msgstr "Siirry kaikkiin työpaikkoihin"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Group By"
msgstr "Ryhmittely"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
msgid "HR Referral Alert Search"
msgstr "HR Referral Alert-haku"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__has_referrer
msgid "Has Referrer"
msgstr "On suosittelija"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid ""
"Hello,<br><br>There are some amazing job offers in my company! Have a look, "
"they  can be interesting for you<br><a href=\"%s\">See Job Offers</a>"
msgstr ""
"Hei,<br><br>Yrityksessäni on mahtavia työtarjouksia! Katso, ne voivat olla "
"mielenkiintoisia sinulle<br><a href=\"%s\">Katso työtarjoukset</a>"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
#, python-format
msgid ""
"Hello,<br><br>There is an amazing job offer for %s in my company! It will be"
" a fit for you<br><a href=\"%s\">See Job Offer</a>"
msgstr ""
"Hei,<br><br>Yrityksessäni on mahtava työtarjous %s! Se sopii sinulle<br><a "
"href=\"%s\">Katso työtarjous</a>"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__hired
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__hired
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Hired"
msgstr "Palkattu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_level_id
msgid "Hr Referral Level"
msgstr "HR Suosittelutaso"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_onboarding_page
msgid "Hr Referral Onboarding Page"
msgstr "HR Suosittelun sisäänajosivu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__hr_referral_reward_id
msgid "Hr Referral Reward"
msgstr "HR suosittelupalkkio"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__id
msgid "ID"
msgstr "ID"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoni joka kertoo poikkeustoiminnosta."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image_head
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__image
msgid "Image"
msgstr "Kuva"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__progress
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "In Progress"
msgstr "Käynnissä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__job_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Job"
msgstr "Tehtävä"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_job
msgid "Job Position"
msgstr "Tehtävänimike"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_job_employee_referral
msgid "Job Positions"
msgstr "Työtehtävät"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Job Referral Program"
msgstr "Työpaikkojen suositteluohjelma"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__job_open_date
msgid "Job Start Recruitment Date"
msgstr "Työ alkaa Rekrytointipäivä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward____last_update
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__write_date
msgid "Last Update Date"
msgstr "Viimeisin päivityspäivä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__last_valuable_stage_id
msgid "Last Valuable Stage"
msgstr "Viimeinen arvokas vaihe"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward_configuration
msgid "Let's create Super Rewards to thank your employees."
msgstr "Luodaan superpalkintoja, joilla kiitetään työntekijöitä."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward
msgid "Let's create super Rewards to thank<br>your employees."
msgstr "Luodaan superpalkintoja, joilla kiitetään<br>työntekijöitäsi."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
msgid "Let's share a job position."
msgstr "Jaetaan työpaikka."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Level"
msgstr "Taso"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__name
msgid "Level Name"
msgstr "Tason nimi"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_level
msgid "Level for referrals"
msgstr "Taso suositteluja varten"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_level_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_level_configuration
msgid "Levels"
msgstr "Tasot"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__direct
msgid "Link"
msgstr "Linkki"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_link_to_share_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Link to Share"
msgstr "Jaettava linkki"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__linkedin
msgid "Linkedin"
msgstr "Linkedin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__linkedin_clicks
msgid "Linkedin Clicks"
msgstr "Linkedinin klikkaukset"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__max_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__max_points
msgid "Max Points"
msgstr "Maksimipisteet"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__medium_id
msgid "Medium"
msgstr "Media"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_applicant_employee_referral
msgid "My Referral"
msgstr "Omat suositukseni"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
#, python-format
msgid "New Alert In Referrals App"
msgstr "Uusi hälytys suosittelusovelluksessa"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#, python-format
msgid "New gift awarded for %s"
msgstr "Uusi lahja myönnetään %s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Next"
msgstr "Seuraava"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_job_employee_referral
msgid "No job positions are available to share."
msgstr "Työpaikkoja ei ole jaettavana."

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__no
msgid "Not Clickable"
msgstr "Ei klikattavissa"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__closed
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__closed
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Not Hired"
msgstr "Ei palkattu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__onclick
msgid "On Click"
msgstr "Kun klikataan"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_onboarding_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_onboarding_configuration
msgid "Onboarding"
msgstr "Alkuopastus"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Ongoing"
msgstr "Jatkuva"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open"
msgstr "Avoin"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Point icon"
msgstr "Pisteen kuvake"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__points
#: model:ir.ui.menu,name:hr_referral.menu_hr_points_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points"
msgstr "Pisteet"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__points_not_hired
msgid "Points Given For Not Hired"
msgstr "Palkkaamatta jättämisestä annetut pisteet"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__points_missing
msgid "Points Missing"
msgstr "Puuttuvat pisteet"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Points icon"
msgstr "Pisteiden kuvake"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_points
msgid "Points line for referrals"
msgstr "Pistekohtainen rivi suosituksia varten"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points to buy this"
msgstr "Pisteet tämän ostamiseksi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__position
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Position"
msgstr "Asema"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Positions"
msgstr "Sijoitukset"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Product"
msgstr "Tuote"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__name
msgid "Product Name"
msgstr "Tuotteen nimi"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
msgid "Ready to receive points?"
msgstr "Oletko valmis saamaan pisteitä?"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "Rekrytoinnin analyysiraportti"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Rekrytointivaiheet"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Referer"
msgstr "Viittaaja"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Referral"
msgstr "Lähete"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert_mail_wizard
msgid "Referral Alert Mail Wizard"
msgstr "Suositushälytyksen sähköpostin ohjattu toiminto"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_company__hr_referral_background
#: model:ir.model.fields,field_description:hr_referral.field_res_config_settings__hr_referral_background
msgid "Referral Background"
msgstr "Suosituksen tausta"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_link_to_share
msgid "Referral Link To Share"
msgstr "Suosittelulinkki jakamiseen"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__referral_point_ids
msgid "Referral Point"
msgstr "Suosituspiste"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_points_ids
msgid "Referral Points"
msgstr "Suosituspisteet"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_referral_reward_responsible_user
msgid "Referral Reward Responsible User"
msgstr "Suosittelupalkkion vastuullinen käyttäjä"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_mail
msgid "Referral Send Mail"
msgstr "Suositus Lähetä postia"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_state
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__referral_state
msgid "Referral State"
msgstr "Suositustila"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Referral: %s"
msgstr "Suositus: %s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Referral: %s (%s)"
msgstr "Suositus: %s (%s)"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_root
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
#, python-format
msgid "Referrals"
msgstr "Viittaukset"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_applicant_view_search_bis_inherit_referral
msgid "Referred By"
msgstr "Viitannut"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__ref_user_id
msgid "Referred By User"
msgstr "Käyttäjän suosittelema"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_report_employee_referral_all
msgid "Reporting"
msgstr "Raportointi"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
msgid "Requirements"
msgstr "Vaatimukset"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Restart Onboarding"
msgstr "Käynnistä perehdytys uudelleen"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Restore Default"
msgstr "Palauta oletusarvo"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Reward"
msgstr "Palkinto"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_reward
msgid "Reward for Referrals"
msgstr "Palkinto suositteluista"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reward_configuration
#, python-format
msgid "Rewards"
msgstr "Palkinnot"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Search Points / Gifts"
msgstr "Hakupisteet / Lahjat"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Search Referral"
msgstr "Hae suosituksia"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
msgid "Send"
msgstr "Lähetä"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_mail_action
msgid "Send Job Offer by Mail"
msgstr "Lähetä työtarjous postitse"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Send Job by Mail"
msgstr "Lähetä työ postitse"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_alert_mail_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Send Mail"
msgstr "Lähetä sähköposti"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__sequence
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__sequence_stage
msgid "Sequence of stage"
msgstr "Vaiheen järjestys"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_configuration
#: model:ir.ui.menu,name:hr_referral.hr_referral_menu_configuration
msgid "Settings"
msgstr "Asetukset"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Share"
msgstr "Jaa"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share Now"
msgstr "Jaa nyt"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share on {{ source.name }}"
msgstr "Jaa {{ source.name }}"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share on {{source.name}}"
msgstr "Jaa {{source.name}}"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__shared_item_infos
msgid "Shared Item Infos"
msgstr "Jaettu kohteen tiedot"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid "Show in Referrals"
msgstr "Näytä suosituksissa"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Skip"
msgstr "Ohita"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Skip and Start"
msgstr "Ohita ja aloita"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Sorry, your referral %s has been refused in the recruitment process."
msgstr "Valitettavasti suositus %s on hylätty rekrytointiprosessissa."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__utm_source_id
msgid "Source"
msgstr "Lähde"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__url
msgid "Specify URL"
msgstr "Määritä URL-osoite"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__stage_id
msgid "Stage"
msgstr "Vaihe"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Start"
msgstr "Aloita"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Start Now"
msgstr "Aloita nyt"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__subject
msgid "Subject"
msgstr "Aihe"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Successful"
msgstr "Onnistui"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__text
msgid "Text"
msgstr "Teksti"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image
msgid ""
"This field holds the image used as image for the friend on the dashboard, "
"limited to 1024x1024px."
msgstr ""
"Tähän kenttään tallennetaan kojelaudan ystävän kuvana käytettävä kuva, jonka"
" koko on 1024x1024px."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image_head
msgid ""
"This field holds the image used as image for the head's friend when the user"
" must choose a new friend, limited to 1024x1024px."
msgstr ""
"Tähän kenttään tallennetaan kuva, jota käytetään pään ystävän kuvana, kun "
"käyttäjän on valittava uusi ystävä, rajoitettu 1024x1024px:iin."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"Tämä kenttä sisältää tuotteen kuvan, joka on rajoitettu kokoon 1024x1024px."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid ""
"This link contains a tracker so that people clicking on it will account to a"
" referral for you, even if they apply on a position after a few days."
msgstr ""
"Tämä linkki sisältää nettiseurannan koodin, jotta sitä klikkaavat ihmiset "
"saavat sinulta lähetteen, vaikka he hakisivat paikkaa muutaman päivän "
"kuluttua."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid ""
"This option is used in app 'Referrals'. If checked, the stage is displayed "
"in 'Referrals Dashboard' and points are given to the employee."
msgstr ""
"Tätä vaihtoehtoa käytetään sovelluksessa 'Lähetteet'. Jos se on valittuna, "
"vaihe näytetään 'Referrals Dashboard'-taululla ja työntekijälle annetaan "
"pisteitä."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "This report performs analysis on your employee referral."
msgstr "Tämä raportti analysoi työntekijöiden suosittelua."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "To Spend"
msgstr "Kuluttaa"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "Total"
msgstr "Yhteensä"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__twitter
msgid "Twitter"
msgstr "Twitter"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__twitter_clicks
msgid "Twitter Clicks"
msgstr "Twitter-klikkaukset"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__url
msgid "URL"
msgstr "URL"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM-kampanja"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_source
msgid "UTM Source"
msgstr "UTM-lähde"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__url
msgid "Url"
msgstr "Verkko-osoite"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_users
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__user_ids
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__ref_user_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__ref_user_id
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "User"
msgstr "Käyttäjä"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_recruitment_referral_user
msgid "User : Referral only"
msgstr "Käyttäjä : Vain lähete"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "User responsible of this gift."
msgstr "Käyttäjä vastuussa tästä lahjasta."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "View Jobs"
msgstr "Näytä työpaikkailmoitukset"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
#, python-format
msgid "Visit Webpage"
msgstr "Vieraile verkkosivulla"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_onboarding
msgid "Welcome Onboarding in Referral App"
msgstr "Tervetuloa sisäänkirjautuminen suosittelusovellukseen"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "You are not allowed to access applicant records."
msgstr "Sinulla ei ole oikeutta tutustua hakijoiden tietoihin."

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_campaign.py:0
#, python-format
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following jobs in Referral:\n"
"%(job_names)s"
msgstr ""
"Et voi poistaa näitä UTM-kampanjoita, koska ne on linkitetty seuraaviin Referralin tehtäviin:\n"
"%(job_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following users in Referral:\n"
"%(employee_names)s"
msgstr ""
"Et voi poistaa näitä UTM-lähteitä, koska ne on linkitetty seuraaviin käyttäjiin Referralissa:\n"
"%(employee_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#, python-format
msgid ""
"You do not have enough points in this company to buy this product. In this "
"company, you have %s points."
msgstr ""
"Sinulla ei ole tarpeeksi pisteitä tässä yrityksessä ostaaksesi tämän "
"tuotteen. Sinulla on tässä yrityksessä %s pistettä."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "You earned"
msgstr "Ansaitsit"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "You need another"
msgstr "Tarvitset toisen"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Your referrer got a step further!"
msgstr "Suosittelijasi pääsi askeleen pidemmälle!"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
#, python-format
msgid "Your referrer is hired!"
msgstr "Suosittelijasi on palkattu!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "click(s)"
msgstr "klikkausta"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "has been hired!"
msgstr "on palkattu!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "https://www.google.com"
msgstr "https://www.google.com"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "reward"
msgstr "palkinto"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#, python-format
msgid "share"
msgstr "jaa"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__url
msgid "url"
msgstr "url"
