<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <record id="hr_contract_salary_payroll_resume_gross_cp200" model="hr.contract.salary.resume">
        <field name="name">Gross</field>
        <field name="value_type">contract</field>
        <field name="code">wage_with_holidays</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_salary"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="impacts_monthly_total" eval="False"/>
    </record>

    <record id="hr_contract_salary_resume_net_cp200" model="hr.contract.salary.resume">
        <field name="name">Net</field>
        <field name="value_type">payslip</field>
        <field name="code">NET</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_salary"/>
        <field name="impacts_monthly_total" eval="True"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
    </record>

    <record id="hr_contract_salary_resume_gross_with_commission" model="hr.contract.salary.resume">
        <field name="name">Gross (Incl. Comm)</field>
        <field name="value_type">payslip</field>
        <field name="code">SALARY</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_salary"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
    </record>

    <record id="hr_contract_salary_resume_final_yearly_costs" model="hr.contract.salary.resume">
        <field name="name">Employer Cost</field>
        <field name="value_type">contract</field>
        <field name="code">final_yearly_costs</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_total"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
    </record>

    <record id="hr_contract_salary_resume_monthly_benefit" model="hr.contract.salary.resume">
        <field name="name">Nature</field>
        <field name="value_type">sum</field>
        <field name="code">monthly_benefit</field>
        <field name="advantage_ids" eval="[
            (4, ref('l10n_be_hr_contract_salary.l10n_be_transport_company_car')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_transport_public')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_transport_train')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_transport_private_car')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_internet')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_mobile')),
        ]"/>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_advantages"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="impacts_monthly_total" eval="True"/>
    </record>

    <record id="hr_contract_salary_resume_monthly_cash" model="hr.contract.salary.resume">
        <field name="name">Cash</field>
        <field name="code">monthly_cash</field>
        <field name="value_type">sum</field>
        <field name="advantage_ids" eval="[
            (4, ref('l10n_be_hr_contract_salary.l10n_be_fuel_card')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_yearly_commission')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_meal_vouchers')),
        ]"/>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_advantages"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="impacts_monthly_total" eval="True"/>
    </record>

    <record id="hr_contract_salary_resume_yearly_cash" model="hr.contract.salary.resume">
        <field name="name">Cash</field>
        <field name="code">yearly_cash</field>
        <field name="value_type">sum</field>
        <field name="advantage_ids" eval="[
            (4, ref('l10n_be_hr_contract_salary.l10n_be_eco_voucher')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_thirteen_month')),
            (4, ref('l10n_be_hr_contract_salary.l10n_be_double_holiday')),
        ]"/>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_yearly_advantages"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="impacts_monthly_total" eval="True"/>
    </record>

    <record id="hr_contract_salary_resume_annual_time_off" model="hr.contract.salary.resume">
        <field name="name">Annual Time Off</field>
        <field name="code">annual_time_off</field>
        <field name="value_type">fixed</field>
        <field name="fixed_value">20</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_yearly_advantages"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="uom">days</field>
    </record>

    <record id="hr_contract_salary_resume_extra_time_off" model="hr.contract.salary.resume">
        <field name="name">Extra Time Off</field>
        <field name="value_type">contract</field>
        <field name="code">holidays</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_yearly_advantages"/>
        <field name="structure_type_id" ref="hr_contract.structure_type_employee_cp200"/>
        <field name="uom">days</field>
    </record>
    </data>
</odoo>
