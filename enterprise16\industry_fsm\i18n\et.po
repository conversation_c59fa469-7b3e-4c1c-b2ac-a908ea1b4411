# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> Randmets, 2023
# JanaAvalah, 2023
# Anna, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> Õigus <<EMAIL>>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "Ülesannete arv"

#. module: industry_fsm
#: model:ir.actions.report,print_report_name:industry_fsm.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr "'Tööleht %s - %s' % (object.name, object.partner_id.name)"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""
"<b>Vaadake ülesande</b> <b>aruanne</b> koos kliendiga läbi ja allkirjastage "
"see."

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "<b>Send your task report</b> to your customer."
msgstr "<b>Saatke oma ülesande aruanne</b> kliendile."

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.planned_date_end, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br><br>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br><br>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "<i class=\"fa fa-check me-1\"/>Sign"
msgstr "<i class=\"fa fa-check me-1\"/>Allkirjasta"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_task_report
msgid ""
"<p>\n"
"                Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br><br>\n"
"                Please find attached the worksheet for our onsite operation. <br><br>\n"
"                Feel free to contact us if you have any questions.<br><br>\n"
"                Best regards,<br><br>\n"
"            </p>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<span class=\"o_stat_text\">Worksheet</span>\n"
"                            <span class=\"o_stat_text\">Complete</span>"
msgstr ""
"<span class=\"o_stat_text\">Tööleht</span>\n"
"                            <span class=\"o_stat_text\">Valmis</span>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Allkirjastatud\n"
"                            </span>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong class=\"me-2\">Total</strong>"
msgstr "<strong class=\"me-2\">Kokku</strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>Klient: </strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr "<strong>Planeeritud kuupäev:</strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Worker: </strong>"
msgstr "<strong>Töötaja:</strong>"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__active
msgid "Active"
msgstr "Tegev"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_config_activity_type
msgid "Activity Types"
msgstr "Tegevuste tüübid"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "Administraator"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr "Kõik ülesanded"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid ""
"Analyze the progress of your tasks and the performance of your workers."
msgstr "Analüüsige ülesannete edenemist ja töötajate tulemuslikkust."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__ancestor_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Ancestor Task"
msgstr "Vanem ülesanne"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__user_ids
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Assignees"
msgstr "Kellele määratud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr "Ülesande kuupäev"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_avg
msgid "Average Rating"
msgstr "Keskmine hinne"

#. module: industry_fsm
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
msgid "Blocked"
msgstr "Blokeeritud"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "Projekti järgi"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "Kasutaja järgi"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Cancel"
msgstr "Tühista"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Close"
msgstr "Sulge"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Closed"
msgstr "Lahendatud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Closed Last 30 Days"
msgstr "Viimased 30 päeva suletud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Closed Last 7 Days"
msgstr "Viimased 7 päeva suletud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__is_closed
msgid "Closing Stage"
msgstr "Lõppetapp"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__comment
msgid "Comments"
msgstr "Kommentaarid"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_project_project_filter_inherit_industry_fsm
msgid "Company"
msgstr "Ettevõte"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "Seadistus"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Confirm"
msgstr "Kinnita"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: note that the duration "
"has automatically been rounded to 15 minutes.</i>"
msgstr ""
"Kinnitage ülesandele <b>kulunud aeg</b>. <i>Vihje: Pange tähele, et aeg on "
"automaatselt ümardatud 15 minutini.</i>"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Create"
msgstr "Loo"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Create Date"
msgstr "Loomise kuupäev"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.open_create_project_fsm
msgid "Create a Project"
msgstr "Looge projekt"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr "Loo kohandatud töölehe malle"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr "Loo uued pakkumised otse ülesannetest"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr "Loo uus pakkumine otse ülesandelt"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"Looge oma ülesannete korraldamiseks projekte ja määrake iga projekti jaoks "
"erinev töövoog."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Create reports to be signed off by your customers"
msgstr "Looge aruandeid, mida teie kliendid peaksid allkirjastama"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Custom Worksheets"
msgstr "Kohandatud töölehed"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
#, python-format
msgid "Customer"
msgstr "Klient"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Customer Preview"
msgstr "Kliendi eelvaade"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_customer_ratings
msgid "Customer Ratings"
msgstr "Kliendi hinnangud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Date"
msgstr "Kuupäev"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Days Spent"
msgstr "Kulutatud päevad"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr "Päevi tähtajani"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "Tähtaeg"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Description"
msgstr "Kirjeldus"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Discard"
msgstr "Loobu"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_enabled_conditions_count
msgid "Display Enabled Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_primary
msgid "Display Mark As Done Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_secondary
msgid "Display Mark As Done Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "Kuva nimi"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_satisfied_conditions_count
msgid "Display Satisfied Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_send_report_primary
msgid "Display Send Report Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_send_report_secondary
msgid "Display Send Report Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_sign_report_primary
msgid "Display Sign Report Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_sign_report_secondary
msgid "Display Sign Report Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""
"Kuva ülesandeid Paigalduse moodulis ja lubage planeerida "
"algus/lõpukuupäevadega."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Dissatisfied"
msgstr "Rahulolematu"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Do you want to stop the running timers?"
msgstr "Kas soovite peatada jooksvad taimerid?"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_effective
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Effective Hours"
msgstr "Efektiivsed tunnid"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_task_report
msgid "Email sent when clicking on \"send report\" in a task"
msgstr "Meili saadetakse, kui klõpsate ülesandel \"Saada aruanne\""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_end
msgid "End date"
msgstr "Lõppkuupäev"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "Lõppkuupäev"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "Lisapakkumised"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "FSM Ülesannete analüüs"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_project.py:0
#: code:addons/industry_fsm/models/res_company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#, python-format
msgid "Field Service"
msgstr "Paigaldus"

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Field Service: Intervention Scheduled"
msgstr "Paigaldus: Sekkumine planeeritud"

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_task_report
msgid "Field Service: Task Report"
msgstr "Paigaldus: Ülesande aruanne"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr "Täitke <b>tööleht</b> oma sekkumise üksikasjadega."

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
msgid "Find here your itinerary for today's tasks."
msgstr "Siit leiate oma tänased ülesanded."

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "Find here your upcoming tasks for the next few days."
msgstr "Siit leiate oma eelseisvad ülesanded järgmisteks päevadeks."

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__is_closed
msgid "Folded in Kanban stages are closing stages."
msgstr "Kanbanis volditud etapid on lõpuetapid."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Followed Tasks"
msgstr "Jälgimisel olevad ülesanded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Future"
msgstr "Tulevane"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Give it a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr ""
"Anna sellele <b>pealkiri</b> <i>(nt. Boileri hooldus, Kliimaseadmete "
"paigaldus jne).</i>"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Go back to your Field Service <b>task</b>."
msgstr "Mine tagasi oma Paigalduse <b>ülesande</b> juurde."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Group By"
msgstr "Grupeeri"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Hours Spent"
msgstr "Kulutatud tunnid"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__id
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "In Progress"
msgstr "Töös"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Initially Planned Hours"
msgstr "Algselt planeeritud aeg"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr "Kehtetu ülesanne."

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Valed allkirja andmed."

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr "Kutsu klient ülesande aruannet <b>kinnitama ja allkirjastama</b>."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_reached
msgid "Is Milestone Reached"
msgstr "Kas vahe-eesmärgid on saavutatud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr "On tööleht saadetud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "Kanban State"
msgstr "Kanbani seisund"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr ""
"Jälgi ülesannete täitmiseks kasutatud tooteid ning esita klientidele arveid "
"oma aja ja materjali kohta."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard____last_update
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line____last_update
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud (millal)"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "Viimase etapi värskendus"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Late Tasks"
msgstr "Hilinenud ülesanded"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr "Käivitage taimer, et <b>jälgida ülesandele kulunud aega</b>."

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will be closed.</i>"
msgstr ""
"Märgime <b>oma ülesande tehtuks!</b> <i>Vihje: seda tehes uuendatakse "
"automaatselt teie laoseisu ja teie ülesanne suletakse.</i>"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "Loome Teie esimese <b>ülesande</b>."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__line_ids
msgid "Line"
msgstr "Rida"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr "Hallake ülesandeid Paigalduse moodulis"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr "Kaart"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "Märgi lõpetatuks"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_ir_ui_menu
msgid "Menu"
msgstr "Menüü"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_id
msgid "Milestone"
msgstr "Eesmärk"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_deadline
msgid "Milestone Deadline"
msgstr "Vahe-eesmärkide tähtaeg"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_milestones
msgid "Milestones"
msgstr "Eesmärgid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Department's Projects"
msgstr "Minu osakonna projektid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Department's Tasks"
msgstr "Minu osakonna ülesanded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "My Favorite Projects"
msgstr "Minu lemmikprojektid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Favorites Projects"
msgstr "Minu lemmikud projektid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Projects"
msgstr "Minu projektid"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Tasks"
msgstr "Minu ülesanded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Team's Projects"
msgstr "Minu meeskonna projektid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "My Team's Tasks"
msgstr "Minu meeskonna ülesanded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Navigate To"
msgstr "Navigeeri"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "No Rating"
msgstr "Hinnang puudub"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr "Andmed puuduvad!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr "Projekte ei leitud. Loome ühe!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr "Ühtegi etappi ei leitud. Loome ühe!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid "No tasks found. Let's create one!"
msgstr "Ülesandeid ei leitud. Koostame ühe!"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Not Starred"
msgstr "Pole tähistatud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Okay"
msgstr "Korras"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Open your <b>worksheet</b> in order to fill it in with the details of your "
"intervention."
msgstr "Avage <b>tööleht</b>, et lisada oma sekkumise üksikasjad."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__overtime
msgid "Overtime"
msgstr "Ületunnid"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__parent_id
msgid "Parent Task"
msgstr "Peamine ülesanne"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Past"
msgstr "Varasem"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__personal_stage_type_ids
msgid "Personal Stage"
msgstr "Isiklik etapp"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "Planeeritud"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_planned
msgid "Planned Hours"
msgstr "Planeeritud tunnid"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "Planeerimine"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planning_overlap
msgid "Planning Overlap"
msgstr "Planeerimise kattuvus"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr "Projektipõhine planeerimine"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr "Kasutajapõhine planeerimine"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "Prioriteet"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "Progress"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__project_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Project"
msgstr "Projektid"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "Projektid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Rated tasks"
msgstr "Hinnatud ülesanded"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_text
msgid "Rating Last Text"
msgstr "Viimase teksti hinnang"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Rating Value (/5)"
msgstr "Hindamisväärtus (/5)"

#. module: industry_fsm
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
msgid "Ready"
msgstr "Saadaval"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr ""
"Kas olete valmis haldama oma <b>kohapealseid sekkumisi</b>? <i>Vajutage "
"Paigalduse nupule, et alustada.</i>"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Remaining Hours"
msgstr "Järelejäänud tunnid"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "Aruandlus"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Satisfied"
msgstr "Rahul"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Save time by automatically generating a <b>signature</b>."
msgstr "Säästke aega, genereerides <b>allkiri</b> automaatselt."

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Schedule your tasks and assign them to your workers."
msgstr "Planeerige enda ülesandeid ja määrake need oma töötajatele."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Search planning"
msgstr "Otsi töögraafikut"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Select the <b>customer</b> for your task."
msgstr "Vali oma ülesande jaoks <b>klient</b>."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Send Report"
msgstr "Saada raport"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Send report"
msgstr "Saada aruanne"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_intervention_details
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"Määrake see mall projekti etapis, et automatiseerida meilisõnumid, kui "
"ülesanded jõuavad etappidesse"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "Seaded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign"
msgstr "Allkirjastamine"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Sign Report"
msgstr "Allkirjasta raport"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign Task"
msgstr "Allkirjasta ülesanne"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Signature"
msgstr "Allkiri"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Allkiri on puudu"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr "Allkirjastanud (kelle poolt)"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid ""
"Some employees are still running their timer for this task. Are you sure you"
" want to continue?"
msgstr ""
"Mõnel töötajal jookseb veel antud ülesande jaoks taimer. Kas olete kindel, "
"et soovite jätkata?"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Stage"
msgstr "Etapp"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "Etapid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Starred"
msgstr "Tähistatud"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Start Date"
msgstr "Alguskuupäev"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_begin
msgid "Start date"
msgstr "Alguskuupäev"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Stop the <b>timer</b> when you are done."
msgstr "Peata <b>taimer</b>, kui olete lõpetanud."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_subtasks
msgid "Sub-tasks"
msgstr "Alamülesanded"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr "Efektiivsete tundide summa"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__tag_ids
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "Sildid"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__task_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "Ülesanne"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr "Ülesande sõltuvused"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__fsm_done
msgid "Task Done"
msgstr "Tehtud ülesanne"

#. module: industry_fsm
#: model:ir.actions.report,name:industry_fsm.task_custom_report
msgid "Task Report"
msgstr "Ülesande aruanne"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_type
msgid "Task Stage"
msgstr "Ülesande etapp"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_industry_fsm_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr "Ülesande töölehe kohandatud raportid. "

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard
msgid "Task stop running timers confirmation wizard"
msgstr ""

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard_line
msgid "Task stop running timers confirmation wizard line"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_tasks_action_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Tasks"
msgstr "Ülesanded"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "Ülesannete analüüs"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks Soon in Overtime"
msgstr "Ülesanded on kohe üle aja"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Tasks in Conflict"
msgstr "Ülesanded konfliktis"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "The worksheet has been signed"
msgstr "Tööleht on allkirjastatud"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr "Tööleht ei ole staatuses, mis nõuaks kliendilt allkirja."

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "There are no reports to send."
msgstr "Pole aruandeid, mida saata."

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr "Aeg"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "Kulutatud aeg"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material Invoicing"
msgstr "Aja ja materjali arveldamine"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr "Ülesande jaoks logitud aeg"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "Tööaja ajaarvestusleht"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Timesheets"
msgstr "Tööaja arvestusleht"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "To Do"
msgstr "Tegemiseks"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "To Schedule"
msgstr "Planeerimiseks"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid ""
"To get things done, plan activities and use the task status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Et vajalikud toimingud saaksid tehtud, planeerige tegevusi ja kasutage ülesande staatust.<br>\n"
"              Tehke tõhusat koostööd, vesteldes reaalajas või e-posti teel."

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""
"Asjade tegemiseks kasutage ülesannete puhul tegevusi ja olekut.<br>\n"
"                Tõhusaks koostööks vestelge reaalajas või e-maili teel."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Today"
msgstr "Täna"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "Jälgige ülesande arengut loomisest kuni sulgemiseni."

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Unassigned"
msgstr "Määramata"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr "Kasutage navigeerimisriba, et minna tagasi oma <b>ülesandele</b>."

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "Kasutaja"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Validate the <b>signature</b>."
msgstr "Valideeri <b>allkiri</b>."

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__wizard_id
msgid "Wizard"
msgstr "Nõustaja"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Worker"
msgstr "Tööline"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
msgid "Working Days to Assign"
msgstr "Määratavad tööpäevad"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Working Days to Close"
msgstr "Tööpäevade arv sulgemiseni"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr "Tööaeg määrata"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr "Töötundide arv sulgemiseni"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Worksheet"
msgstr "Tööleht"

#. module: industry_fsm
#: model:mail.template,report_name:industry_fsm.mail_template_data_task_report
msgid ""
"Worksheet {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr "Töölehed"

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.fsm_form_view_comment
msgid "comment"
msgstr "kommentaar"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm_inherited
msgid "e.g. Boiler replacement"
msgstr "nt. Boileri asendamine"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM ?"
msgstr "On paigaldus?"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.fsm_customer_ratings_server_action
msgid "project.project.fsm"
msgstr "project.project.fsm"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "working days to assign"
msgstr "määratavad tööpäevad"

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_task_report
msgid "{{ object.name }} Report"
msgstr "{{ object.name }} Report"
