<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!--
        Specific survey access rules for appraisal
        - The appraisal manager can CRUD survey / questions / question answers for is_appraisal = True
        - The manager can see all the answers
        - The asked-feedback-employee can see their anwers
        - The employee themselves can't see any anwers

         * survey_user: CRUD 1 1 1 1
          * can read survey / questions / question answers: 1 = 1
          * can cr/wr/un survey / questions / question answers
           * survey_id.create_uid = uid (own)
          * can cr/wr/un user_input / user_input_line
           * survey_id.create_uid = uid (own) (survey)
           * & is_appraisal = False, survey_id.create_uid = uid (not private AND own) (appraisal_survey)
         * survey_manager: CRUD 1 1 1 1
          * can CRUD survey / questions / question answers: 1 = 1
          * can CRUD user_input / user_input_line: 1 = 1 (survey), is_appraisal = False (appraisal_survey)
          * can CRUD user_input / user_input_line: 1 = 1 dans appraisal_survey
    -->
        <record id="survey.survey_user_input_rule_survey_manager" model="ir.rule">
            <field name="name">Survey user input: manager: all non private</field>
            <field name="domain_force">[('survey_id.is_appraisal', '=', False)]</field>
        </record>
        <record id="survey.survey_user_input_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey user input: officer: create/write/unlink linked to own non private survey only</field>
            <field name="domain_force">['&amp;', ('survey_id.is_appraisal', '=', False), ('survey_id.create_uid', '=', user.id)]</field>
        </record>

        <record id="survey.survey_user_input_line_rule_survey_manager" model="ir.rule">
            <field name="name">Survey user input line: manager: all non private</field>
            <field name="domain_force">[('survey_id.is_appraisal', '=', False)]</field>
        </record>
        <record id="survey.survey_user_input_line_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey user input line: officer: read all non private</field>
            <field name="domain_force">[('survey_id.is_appraisal', '=', False)]</field>
        </record>
        <record id="survey.survey_user_input_line_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey user input line: officer: create/write/unlink linked to own non private survey only</field>
            <field name="domain_force">['&amp;', ('survey_id.is_appraisal', '=', False), ('user_input_id.survey_id.create_uid', '=', user.id)]</field>
        </record>

        <!--special rights for appraisal manager on appraisal surveys-->
        <record id="survey_user_input_rule_appraisal_manager" model="ir.rule">
            <field name="name">Survey user input: appraisal manager: all</field>
            <field name="model_id" ref="survey.model_survey_user_input"/>
            <field name="domain_force">[('survey_id.is_appraisal', '=', True)]</field>
            <field name="groups" eval="[(4, ref('hr_appraisal.group_hr_appraisal_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_user_input_line_rule_appraisal_manager" model="ir.rule">
            <field name="name">Survey user input line: appraisal manager: all</field>
            <field name="model_id" ref="survey.model_survey_user_input_line"/>
            <field name="domain_force">[('survey_id.is_appraisal', '=', True)]</field>
            <field name="groups" eval="[(4, ref('hr_appraisal.group_hr_appraisal_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_survey_rule_appraisal_manager" model="ir.rule">
            <field name="name">Survey survey: appraisal manager: all</field>
            <field name="model_id" ref="survey.model_survey_survey"/>
            <field name="domain_force">[('is_appraisal', '=', True)]</field>
            <field name="groups" eval="[(4, ref('hr_appraisal.group_hr_appraisal_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_question_rule_appraisal_manager" model="ir.rule">
            <field name="name">Survey question: appraisal manager: all</field>
            <field name="model_id" ref="survey.model_survey_question"/>
            <field name="domain_force">[('survey_id.is_appraisal', '=', True)]</field>
            <field name="groups" eval="[(4, ref('hr_appraisal.group_hr_appraisal_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_question_answer_rule_appraisal_manager" model="ir.rule">
            <field name="name">Survey question answer: appraisal manager: all</field>
            <field name="model_id" ref="survey.model_survey_question_answer"/>
            <field name="domain_force">[('survey_id.is_appraisal', '=', True)]</field>
            <field name="groups" eval="[(4, ref('hr_appraisal.group_hr_appraisal_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
    </data>
</odoo>
